<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $database = 'ClientDatabase'; // Replace with your database name

        // Create lease_term_log table
        Schema::create("{$database}.lease_term_log", function (Blueprint $table) use ($database) {
            $table->id(); // Equivalent to INT IDENTITY(1,1) PRIMARY KEY
            $table->unsignedBigInteger('lease_id'); // Equivalent to INT NOT NULL
            $table->date('commencement_date'); // Lease commencement date
            $table->date('expiry_date'); // Lease expiry date
            $table->string('lease_option', 20)->nullable(); // Lease option, VARCHAR(20), nullable
            $table->timestamps();

            // Foreign key constraint to reference pmle_lease
            $table->foreign('lease_id')
                ->references('pmle_id')
                ->on("{$database}.pmle_lease") // Foreign key references specific database
                ->onDelete('cascade');
        });

        // Insert data into lease_term_log
        DB::statement(
            "
            INSERT INTO {$database}.lease_term_log (lease_id, commencement_date, expiry_date, lease_option)
            SELECT pmle_id, pmle_com_dt, 
                   CASE WHEN pmle_exp_dt IS NULL THEN '2999-12-31' ELSE pmle_exp_dt END, 
                   pmle_option 
            FROM {$database}.pmle_lease 
            WHERE pmle_com_dt IS NOT NULL
        "
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lease_term_log');
    }
};
