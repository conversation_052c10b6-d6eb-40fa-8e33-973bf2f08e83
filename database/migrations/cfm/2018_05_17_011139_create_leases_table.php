<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLeasesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('leases', function (Blueprint $table) {
            $table->string('lease_code')->unique()->index();
            $table->string('client_id');
            $table->string('lease_name')->nullable();
            $table->string('lease_location')->nullable();
            $table->string('tenant_code')->nullable();
            $table->string('tenant_name')->nullable();
            $table->string('tenant_street0')->nullable();
            $table->string('tenant_street1')->nullable();
            $table->string('tenant_city')->nullable();
            $table->string('tenant_state')->nullable();
            $table->string('tenant_postcode')->nullable();
            $table->string('unit_code')->nullable();
            $table->string('unit_description')->nullable();
            $table->string('status')->nullable();
            $table->timestamp('commencement_date');
            $table->timestamp('expiry_date');
            $table->timestamp('occupancy_start_date');
            $table->timestamp('occupancy_end_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('leases');
    }
}
