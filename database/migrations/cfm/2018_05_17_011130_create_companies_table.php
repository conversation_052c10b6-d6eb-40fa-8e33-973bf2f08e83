<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCompaniesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->increments('id');
            $table->string('company_code')->unique()->index();
            $table->string('client_id');
            $table->string('street_address0');
            $table->string('street_address1');
            $table->string('city');
            $table->string('state');
            $table->string('postcode');
            $table->string('country');
            $table->tinyInteger('is_active');
            $table->tinyInteger('is_owner');
            $table->tinyInteger('is_supplier');
            $table->string('taxStatus');
            $table->string('ABN');
            $table->string('email');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('companies');
    }
}
