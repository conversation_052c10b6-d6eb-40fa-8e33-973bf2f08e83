<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateContactsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->string('contact_id')->unique();
            $table->string('client_id');
            $table->string('company_code');
            $table->string('property_code');
            $table->string('lease_code');
            $table->string('contact_name')->nullable();
            $table->string('contact_type')->nullable();
            $table->string('type_description')->nullable();
            $table->timestamps();
        });

        Schema::create('contact_details', function (Blueprint $table) {
            $table->increments('contact_details_id');
            $table->string('contact_id')->index('contact_details_contact_id_foreign');
            $table->string('client_id');
            $table->string('detail_code');
            $table->string('detail_description');
            $table->string('detail_data');
            $table->timestamps();

            $table->foreign('contact_id')->references('contact_id')->on('contacts')->onUpdate('cascade')->onDelete(
                'cascade'
            );
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contact_details');
        Schema::dropIfExists('contacts');
    }
}
