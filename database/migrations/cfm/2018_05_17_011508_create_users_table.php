<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('role_id')->index()->default(2);
            $table->string('name');
            $table->integer('npms_id')->nullable()->index();
            $table->integer('myc8_id')->nullable()->index();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->integer('mobile')->nullable();
            $table->string('email')->unique();
            $table->string('password');
            $table->integer('password_complexity_id');
            $table->integer('password_expiration_id');
            $table->integer('require_password_change');
            $table->integer('read_only')->default(0);
            $table->date('password_change_date')->nullable();
            $table->integer('active')->default(1);
            $table->integer('flagged')->default(0);
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
