<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLinesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lines', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('account_id')->index();
            $table->integer('workorder_id')->index();
            $table->integer('user_id')->index();
            $table->integer('supplier_id')->index();
            $table->integer('asset_id')->nullable()->index();
            $table->string('supplier_code')->nullable();
            $table->string('property_code')->nullable();
            $table->string('account_code')->nullable();
            $table->string('lease_code')->nullable();
            $table->string('title');
            $table->string('description')->nullable();
            $table->string('service_type')->nullable();
            $table->text('images')->nullable();
            $table->integer('total_progress')->default(0);
            $table->integer('total_cost')->default(0);
            $table->integer('approved')->default(0);
            $table->dateTime('order_date')->nullable();
            $table->dateTime('target_date');
            $table->timestamps();
        });

        Schema::create('line_tag', function (Blueprint $table) {
            $table->integer('line_id')->index();
            $table->integer('tag_id')->index();
            $table->integer('workorder_id')->index();
            $table->primary(['line_id', 'tag_id', 'workorder_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lines');
        Schema::dropIfExists('line_tag');
    }
}
