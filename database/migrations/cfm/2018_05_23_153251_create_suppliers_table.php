<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSuppliersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->unique();
            $table->string('email')->unique();
            $table->string('description')->nullable();
            $table->integer('active')->default(1);
            $table->integer('development')->nullable()->default(1);
            $table->string('logo')->nullable();
            $table->integer('npms_id')->nullable()->index();
            $table->integer('myc8_id')->nullable()->index();
            $table->timestamps();
        });

        Schema::create('account_supplier', function (Blueprint $table) {
            $table->integer('account_id')->index();
            $table->integer('supplier_id')->index();
            $table->primary(['account_id', 'supplier_id']);
        });

        Schema::create('supplier_tag', function (Blueprint $table) {
            $table->integer('supplier_id')->index();
            $table->integer('tag_id')->index();
            $table->primary(['tag_id', 'supplier_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('suppliers');
        Schema::dropIfExists('account_supplier');
        Schema::dropIfExists('supplier_tag');
    }
}
