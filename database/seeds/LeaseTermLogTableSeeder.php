<?php

namespace seeds;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeaseTermLogTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Insert data into lease_term_log
        DB::statement(
            "
            INSERT INTO lease_term_log (lease_id, commencement_date, expiry_date, lease_option)
            SELECT pmle_id, pmle_com_dt, 
                   CASE WHEN pmle_exp_dt IS NULL THEN '2999-12-31' ELSE pmle_exp_dt END, 
                   pmle_option 
            FROM pmle_lease 
            WHERE pmle_com_dt IS NOT NULL
        "
        );

        // Insert data into lease_term_log_unapproved
        DB::statement(
            "
            INSERT INTO lease_term_log_unapproved (lease_id, commencement_date, expiry_date, lease_option)
            SELECT pmle_id, pmle_com_dt, 
                   CASE WHEN pmle_exp_dt IS NULL THEN '2999-12-31' ELSE pmle_exp_dt END, 
                   pmle_option 
            FROM temp_pmle_lease 
            WHERE pmle_com_dt IS NOT NULL
        "
        );
    }
}
