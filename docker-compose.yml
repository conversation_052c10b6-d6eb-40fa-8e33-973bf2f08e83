services:
  api-web:
    image: cgr.dev/chainguard/nginx:latest
    restart: unless-stopped
    ports:
      - '8080:80'
    volumes:
      - ./.:/app
      - ./docker/site.conf:/etc/nginx/conf.d/site.conf
    depends_on:
      - api-php
    networks:
      - app_network

  api-php:
    build: .
    volumes:
      - ./.:/app
      - ./docker/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    restart: unless-stopped
    environment:
      - XDEBUG_MODE=debug
      - XDEBUG_CONFIG=client_host=host.docker.internal client_port=9003 idekey=PHPSTORM
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    depends_on:
      - api-redis
      - api-db
    networks:
      - app_network
    expose:
      - 9003
      - 9000
      - 49264

  api-db:
    container_name: db
    image: mcr.microsoft.com/mssql/server:2022-latest
    ports:
      - '1433:1433'
    volumes:
      - ./database/backups:/var/opt/mssql/backups
      - sqlserverdata:/var/opt/mssql/data
    restart: unless-stopped
    environment:
      SA_PASSWORD: M632D9eJR3D
      ACCEPT_EULA: Y
      MSSQL_ENCRYPT: 'false'
    user: '0:0' # Run as root initially to set permissions
    command: >
      bash -c '
        mkdir -p /var/opt/mssql/data &&
        chown -R mssql:mssql /var/opt/mssql/data &&
        /opt/mssql/bin/sqlservr
      '
    networks:
      - app_network
  #
  api-redis:
    image: cgr.dev/chainguard/redis:latest
    ports:
      - '6379:6379'
    restart: unless-stopped
    volumes:
      - redis-data:/data
    networks:
      - app_network

  api-dev-monitor:
    image: ghcr.io/buggregator/server:latest
    restart: unless-stopped
    ports:
      - '127.0.0.1:9000:8000'
      - '127.0.0.1:1025:1025'
    networks:
      - app_network

volumes:
  redis-data:
  sqlserverdata:

networks:
  app_network:
    name: c8_network
    driver: bridge
