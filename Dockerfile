# Base image with PHP 8.3
FROM php:8.3-fpm AS php-base

# Install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gnupg2 \
    unzip \
    git \
    curl \
    wget \
    build-essential \
    libargon2-dev \
    libldap2-dev \
    libcurl4-openssl-dev \
    libedit-dev \
    libsqlite3-dev \
    libssl-dev \
    freetds-bin \
    unixodbc-dev \
    zlib1g-dev \
    iputils-ping \
    libfreetype6-dev libjpeg62-turbo-dev libpng-dev libzip-dev && \
    apt-get clean && rm -rf /var/lib/apt/lists/*


# Enable pcntl for laravel horizon
RUN docker-php-ext-configure pcntl --enable-pcntl \
  && docker-php-ext-install pcntl

# Install and enable required PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg && \
        docker-php-ext-install gd

# Install zip PHP extension (requires libzip-dev)
RUN docker-php-ext-install zip

# Install and enable opcache PHP extension
RUN docker-php-ext-install opcache

# Enable the calendar extension (to fix the error with gregoriantojd())
RUN docker-php-ext-install calendar

# Install Microsoft SQL Server drivers
RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg && \
    curl -fsSL https://packages.microsoft.com/config/debian/11/prod.list | tee /etc/apt/sources.list.d/mssql-release.list && \
    echo "deb [signed-by=/usr/share/keyrings/microsoft-prod.gpg] https://packages.microsoft.com/debian/11/prod bullseye main" > /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y msodbcsql18 mssql-tools18 && \
    ln -sfn /opt/mssql-tools18/bin/sqlcmd /usr/bin/sqlcmd && \
    ln -sfn /opt/mssql-tools18/bin/bcp /usr/bin/bcp && \
    docker-php-ext-install pdo pdo_mysql && \
    pecl install pdo_sqlsrv redis && \
    docker-php-ext-enable pdo_sqlsrv redis

# Download and compile PDFlib from source
RUN wget -O /tmp/PDFlib.tar.gz https://www.pdflib.com/binaries/PDFlib/1003/PDFlib-10.0.3-Linux-aarch64-php.tar.gz && \
    mkdir -p /usr/src/pdflib && tar -xzf /tmp/PDFlib.tar.gz -C /usr/src/pdflib --strip-components=1 && \
    cp /usr/src/pdflib/bind/php/php-830-nts/php_pdflib.so $(php-config --extension-dir) && \
    rm -rf /usr/src/pdflib /tmp/PDFlib.tar.gz

# Enable the PDFlib extension
RUN docker-php-ext-enable php_pdflib

# Install Xdebug
RUN pecl install xdebug && docker-php-ext-enable xdebug

FROM php-base AS php-build

WORKDIR /app

# Install Composer
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer


COPY ./composer.json ./composer.lock ./
# Files for autoload dump
COPY ./database /app/database/
COPY ./phpdocx /app/phpdocx/
COPY ./app/Helper/bcmath.php /app/app/Helper/

RUN composer install -vvv --optimize-autoloader --no-dev --no-scripts

FROM cgr.dev/chainguard/node:latest-dev AS node-build

USER root

WORKDIR /app

ARG MIX_CONFIG_ASSET_DOMAIN
ARG MIX_CONFIG_UPLOADED_ASSET_DOMAIN
ARG MIX_CONFIG_IS_LEASE_TERM_LOG_ENABLED=1
ARG MIX_CONFIG_FOLDERING_SYSTEM_ON=true

ENV MIX_CONFIG_ASSET_DOMAIN=$MIX_CONFIG_ASSET_DOMAIN
ENV MIX_CONFIG_UPLOADED_ASSET_DOMAIN=$MIX_CONFIG_UPLOADED_ASSET_DOMAIN
ENV MIX_CONFIG_IS_LEASE_TERM_LOG_ENABLED=$MIX_CONFIG_IS_LEASE_TERM_LOG_ENABLED
ENV MIX_CONFIG_FOLDERING_SYSTEM_ON=$MIX_CONFIG_FOLDERING_SYSTEM_ON
ENV NODE_ENV="production"
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV PATH="/usr/local/bin:$PATH"
ARG PNPM_VERSION=10

RUN npm install -g pnpm@$PNPM_VERSION

# Install node modules
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY resources/js/vue2/package.json resources/js/vue2/
COPY resources/js/vue3/package.json resources/js/vue3/
RUN pnpm install --frozen-lockfile

# Copy frontend code
COPY ./resources/js /app/resources/js/

## Build frontend - run seperately to reduce memory
RUN cd resources/js/vue3 && pnpm run build
RUN cd resources/js/vue2 && pnpm run build

FROM php-base AS php-app

WORKDIR /app

COPY . .

COPY --from=php-build /app/vendor /app/vendor
COPY --from=node-build /app/public/mix-manifest.json /app/public/mix-manifest.json
COPY --from=node-build /app/public/vuejs /app/public/vuejs


EXPOSE 9000
CMD ["php-fpm"]
