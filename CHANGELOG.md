# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.3.0-alpha.16](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.15...v1.3.0-alpha.16) (2025-09-05)

## [1.3.0-alpha.15](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.14...v1.3.0-alpha.15) (2025-09-05)

### Bug Fixes

- **property-ledger:** fix bank code type validation ([#324](https://github.com/cirrus-8/laravel-api/issues/324)) ([ccc8293](https://github.com/cirrus-8/laravel-api/commit/ccc829319cf4be1feb4a58577177a1a3c06cd669))

## [1.3.0-alpha.14](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.13...v1.3.0-alpha.14) (2025-09-05)

## [1.3.0-alpha.13](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.12...v1.3.0-alpha.13) (2025-09-05)

### Bug Fixes

- **log:** replace DB error logging with Laravel file log storage ([#317](https://github.com/cirrus-8/laravel-api/issues/317)) ([0cade33](https://github.com/cirrus-8/laravel-api/commit/0cade333bc51a654ae2b8b6291bb14003d4942c9))

## [1.3.0-alpha.12](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.11...v1.3.0-alpha.12) (2025-09-02)

## [1.3.0-alpha.11](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.10...v1.3.0-alpha.11) (2025-08-28)

### Bug Fixes

- **workorders:** hot fix dashboard own filter ([#313](https://github.com/cirrus-8/laravel-api/issues/313)) ([84c7bfc](https://github.com/cirrus-8/laravel-api/commit/84c7bfce44a96b764603523c377db8de14947a49))

## [1.3.0-alpha.10](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.9...v1.3.0-alpha.10) (2025-08-26)

## [1.3.0-alpha.9](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.8...v1.3.0-alpha.9) (2025-08-26)

### Bug Fixes

- **emails:** postmark sever list not getting all ([#311](https://github.com/cirrus-8/laravel-api/issues/311)) ([f14ebde](https://github.com/cirrus-8/laravel-api/commit/f14ebde6b8d716cefaf4f472238be3a8b9a2ae42))

## [1.3.0-alpha.8](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.7...v1.3.0-alpha.8) (2025-08-25)

## [1.3.0-alpha.7](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.6...v1.3.0-alpha.7) (2025-08-25)

## [1.3.0-alpha.6](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.5...v1.3.0-alpha.6) (2025-08-22)

### Bug Fixes

- **emails:** added recipient_type where clause + additional test ([#303](https://github.com/cirrus-8/laravel-api/issues/303)) ([d0239bf](https://github.com/cirrus-8/laravel-api/commit/d0239bf4d51b9fe2f4acb49243cdc1641e06caaf))

## [1.3.0-alpha.5](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.4...v1.3.0-alpha.5) (2025-08-21)

### Bug Fixes

- **workorders:** fix bugs found ([#274](https://github.com/cirrus-8/laravel-api/issues/274)) ([89333ef](https://github.com/cirrus-8/laravel-api/commit/89333efb621d7df547187de897034b7f7fda0d71))

## [1.3.0-alpha.4](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.3...v1.3.0-alpha.4) (2025-08-21)

### Features

- **property-ledger:** admin read-only setting ([#301](https://github.com/cirrus-8/laravel-api/issues/301)) ([2967dd8](https://github.com/cirrus-8/laravel-api/commit/2967dd8b04c654586e187885542200cb9b3b01ac))

## [1.3.0-alpha.3](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.2...v1.3.0-alpha.3) (2025-08-20)

### Bug Fixes

- **send-email-letters:** cir-5104 properties are not displaying in th… ([#299](https://github.com/cirrus-8/laravel-api/issues/299)) ([020a627](https://github.com/cirrus-8/laravel-api/commit/020a627f405dfc198f837e596a873f40ed5d96f8))

## [1.3.0-alpha.2](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.1...v1.3.0-alpha.2) (2025-08-20)

## [1.3.0-alpha.1](https://github.com/cirrus-8/laravel-api/compare/v1.3.0-alpha.0...v1.3.0-alpha.1) (2025-08-18)

### Bug Fixes

- **tenant-diary-letter:** cir-4856 diary description placeholder not populating in generated letters ([#285](https://github.com/cirrus-8/laravel-api/issues/285)) ([5c0a94c](https://github.com/cirrus-8/laravel-api/commit/5c0a94c10320a0dd83300d0d692d7d195d1a7c09))

## [1.3.0-alpha.0](https://github.com/cirrus-8/laravel-api/compare/v1.2.1-alpha.4...v1.3.0-alpha.0) (2025-08-18)

## [1.2.1-alpha.4](https://github.com/cirrus-8/laravel-api/compare/v1.2.1-alpha.3...v1.2.1-alpha.4) (2025-08-17)

### Bug Fixes

- **ci:** release changelog path ([#294](https://github.com/cirrus-8/laravel-api/issues/294)) ([cfd8750](https://github.com/cirrus-8/laravel-api/commit/cfd8750e7d79b869588001af665a866a9fa0eac8))

## [1.2.1-alpha.3](https://github.com/cirrus-8/laravel-api/compare/v1.2.1-alpha.2...v1.2.1-alpha.3) (2025-08-17)

### Bug Fixes

- **ci:** release assets ([#293](https://github.com/cirrus-8/laravel-api/issues/293)) ([97587b5](https://github.com/cirrus-8/laravel-api/commit/97587b5f5c55798eef150c2e3b46b6090b0da0f1))

## [1.2.1-alpha.2](https://github.com/cirrus-8/laravel-api/compare/v1.2.1-alpha.1...v1.2.1-alpha.2) (2025-08-15)

### Bug Fixes

- **ci:** move release to separate action ([#291](https://github.com/cirrus-8/laravel-api/issues/291)) ([b38a5e3](https://github.com/cirrus-8/laravel-api/commit/b38a5e3c09aaa9034733ab0005bfb34054263489))

## [1.2.1-alpha.1](https://github.com/cirrus-8/laravel-api/compare/v1.2.1-alpha.0...v1.2.1-alpha.1) (2025-08-15)

### Bug Fixes

- **ci:** allow gh release always on action ([#290](https://github.com/cirrus-8/laravel-api/issues/290)) ([b455ddd](https://github.com/cirrus-8/laravel-api/commit/b455ddd256824cf22497323057a98f727d09ed0d))

## [1.2.1-alpha.0](https://github.com/cirrus-8/laravel-api/compare/v1.2.0...v1.2.1-alpha.0) (2025-08-15)

### Bug Fixes

- add git user to allow release ([#287](https://github.com/cirrus-8/laravel-api/issues/287)) ([7d93038](https://github.com/cirrus-8/laravel-api/commit/7d930388437e22dabf24e066bc1f2f011c45663e))
- **ci:** gh action permission issues ([#288](https://github.com/cirrus-8/laravel-api/issues/288)) ([9acbf79](https://github.com/cirrus-8/laravel-api/commit/9acbf79523c420db9047c13f4f0e7535495b33a9))
- **ci:** use deploy key to bypass actions ([#289](https://github.com/cirrus-8/laravel-api/issues/289)) ([c83fce8](https://github.com/cirrus-8/laravel-api/commit/c83fce859c69c0797c56ada6df0cb43e47c05397))
- utility meter ([#284](https://github.com/cirrus-8/laravel-api/issues/284)) ([a4fbcc3](https://github.com/cirrus-8/laravel-api/commit/a4fbcc36fbb83970bfe0a25e5884db019b75fee2))

## [1.2.0](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.15...v1.2.0) (2025-08-13)

## [1.2.0-alpha.15](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.14...v1.2.0-alpha.15) (2025-08-07)

## [1.2.0-alpha.14](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.13...v1.2.0-alpha.14) (2025-08-06)

### Bug Fixes

- back charge automation ([#275](https://github.com/cirrus-8/laravel-api/issues/275)) ([4b1a6e5](https://github.com/cirrus-8/laravel-api/commit/4b1a6e5daa9afeaf0d23f03c6499d020894dd3e1)), closes [#CIR-5036](https://github.com/cirrus-8/laravel-api/issues/CIR-5036)
- **company-summary:** bsb validation fix for the company summary ([#255](https://github.com/cirrus-8/laravel-api/issues/255)) ([0551aa2](https://github.com/cirrus-8/laravel-api/commit/0551aa274dbae775354d4b4821c68e7f704fb4f6))
- dashboard calendar error ([#269](https://github.com/cirrus-8/laravel-api/issues/269)) ([3724e3a](https://github.com/cirrus-8/laravel-api/commit/3724e3a409db22f8f22fa7afadcc376b8695baf9))
- onS3 type to boolean ([#277](https://github.com/cirrus-8/laravel-api/issues/277)) ([900774f](https://github.com/cirrus-8/laravel-api/commit/900774f49e54cc4a3ab5fcac75abc7056199da33))
- postal code validation update for UK ([#276](https://github.com/cirrus-8/laravel-api/issues/276)) ([ce44b83](https://github.com/cirrus-8/laravel-api/commit/ce44b8393f19fd42888ac044d88cf97f91f6bb9b))

## [1.2.0-alpha.13](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.12...v1.2.0-alpha.13) (2025-08-04)

## [1.2.0-alpha.12](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.11...v1.2.0-alpha.12) (2025-08-04)

### Bug Fixes

- energytec upload ([#267](https://github.com/cirrus-8/laravel-api/issues/267)) ([1f9b0e0](https://github.com/cirrus-8/laravel-api/commit/1f9b0e06de99616b995525524d19e9ddcef87b68))
- optimize query global search ([#259](https://github.com/cirrus-8/laravel-api/issues/259)) ([aba45f1](https://github.com/cirrus-8/laravel-api/commit/aba45f190503b826e65b65c09f94545cc877badf))
- timezone, development and description is nullable ([#266](https://github.com/cirrus-8/laravel-api/issues/266)) ([584e413](https://github.com/cirrus-8/laravel-api/commit/584e413a06d2684e0935b37bdab76828aa470968))
- undefined property ([#261](https://github.com/cirrus-8/laravel-api/issues/261)) ([3db3e3a](https://github.com/cirrus-8/laravel-api/commit/3db3e3a0e8e3794f549db262dbc66379b64b1c14))
- use just joins for tag links to tags ([#262](https://github.com/cirrus-8/laravel-api/issues/262)) ([4fef464](https://github.com/cirrus-8/laravel-api/commit/4fef4646d58082784c97e5e26339a8174cd8dbea))

## [1.2.0-alpha.11](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.10...v1.2.0-alpha.11) (2025-07-29)

### Bug Fixes

- charge email notification ([#254](https://github.com/cirrus-8/laravel-api/issues/254)) ([ebe59c1](https://github.com/cirrus-8/laravel-api/commit/ebe59c1d26235b055862f596df65c46a98883a67)), closes [#CIR-5002](https://github.com/cirrus-8/laravel-api/issues/CIR-5002) [#CIR-5002](https://github.com/cirrus-8/laravel-api/issues/CIR-5002) [#CIR-5002](https://github.com/cirrus-8/laravel-api/issues/CIR-5002) [#CIR-5002](https://github.com/cirrus-8/laravel-api/issues/CIR-5002)
- convert onS3 to number from string ([#258](https://github.com/cirrus-8/laravel-api/issues/258)) ([34ee4bf](https://github.com/cirrus-8/laravel-api/commit/34ee4bf71e241ae6f2794fa74cc9144f3eac4048))
- step 2 endpoint ([#257](https://github.com/cirrus-8/laravel-api/issues/257)) ([15953d1](https://github.com/cirrus-8/laravel-api/commit/15953d1f2a21f9b3776f3403e5d724b3f8241ff6))

## [1.2.0-alpha.10](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.9...v1.2.0-alpha.10) (2025-07-28)

### Bug Fixes

- optimize sum with client id ([#239](https://github.com/cirrus-8/laravel-api/issues/239)) ([e8d64f8](https://github.com/cirrus-8/laravel-api/commit/e8d64f84c07566485b678aea14145462211080fc))
- send letters ([#251](https://github.com/cirrus-8/laravel-api/issues/251)) ([585304a](https://github.com/cirrus-8/laravel-api/commit/585304aae08fa7840198cffd7c65112e1316be30)), closes [#CIR-4986](https://github.com/cirrus-8/laravel-api/issues/CIR-4986) [#CIR-4986](https://github.com/cirrus-8/laravel-api/issues/CIR-4986)
- use fieldKey if field_key is not available ([#253](https://github.com/cirrus-8/laravel-api/issues/253)) ([7687c42](https://github.com/cirrus-8/laravel-api/commit/7687c42ab2d4c495a64941c83c627a21235d9138))

## [1.2.0-alpha.9](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.8...v1.2.0-alpha.9) (2025-07-23)

### Features

- ignore spam, hardbound and manual suppression ([#244](https://github.com/cirrus-8/laravel-api/issues/244)) ([029bd40](https://github.com/cirrus-8/laravel-api/commit/029bd40d93e7fa3c55d73937de357b656cb3b36a))

### Bug Fixes

- remove getDiaryCommentListByDiaryIds ([#250](https://github.com/cirrus-8/laravel-api/issues/250)) ([f41d52b](https://github.com/cirrus-8/laravel-api/commit/f41d52b476ba204e832bb19a0940676c3cd93cea))

## [1.2.0-alpha.8](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.7...v1.2.0-alpha.8) (2025-07-21)

### Bug Fixes

- auth flow methods ([#236](https://github.com/cirrus-8/laravel-api/issues/236)) ([810b54f](https://github.com/cirrus-8/laravel-api/commit/810b54f7dce3b3815b95c70c56d91f8b0d10783e))
- remove return types ([#234](https://github.com/cirrus-8/laravel-api/issues/234)) ([49b105d](https://github.com/cirrus-8/laravel-api/commit/49b105dc2587dfc2de0ed4f0d45081732f3cc412))
- uk display_bsb to true ([#223](https://github.com/cirrus-8/laravel-api/issues/223)) ([4665b66](https://github.com/cirrus-8/laravel-api/commit/4665b668cb073283442e8ec6c9058161fb25e9f7))

## [1.2.0-alpha.7](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.6...v1.2.0-alpha.7) (2025-07-17)

### Bug Fixes

- auto approved of backcharge ([#232](https://github.com/cirrus-8/laravel-api/issues/232)) ([70c91a5](https://github.com/cirrus-8/laravel-api/commit/70c91a5c40afe4c5869924218f35a583cae8cd5b)), closes [#CIR-4939](https://github.com/cirrus-8/laravel-api/issues/CIR-4939)
- back charge email notification ([#226](https://github.com/cirrus-8/laravel-api/issues/226)) ([8960fac](https://github.com/cirrus-8/laravel-api/commit/8960fac96a5fe9cf3982ce93d4b92775403c3b32)), closes [#CIR-4905](https://github.com/cirrus-8/laravel-api/issues/CIR-4905) [#CIR-4905](https://github.com/cirrus-8/laravel-api/issues/CIR-4905) [#CIR-4905](https://github.com/cirrus-8/laravel-api/issues/CIR-4905)

## [1.2.0-alpha.6](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.5...v1.2.0-alpha.6) (2025-07-17)

### Bug Fixes

- **company-documents:** disable submit btn when saving ([#229](https://github.com/cirrus-8/laravel-api/issues/229)) ([eaca99c](https://github.com/cirrus-8/laravel-api/commit/eaca99ce34c8a42d3c10e208cedd90b8a614d44a))

## [1.2.0-alpha.5](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.4...v1.2.0-alpha.5) (2025-07-17)

### Bug Fixes

- change return type ([#227](https://github.com/cirrus-8/laravel-api/issues/227)) ([87870c6](https://github.com/cirrus-8/laravel-api/commit/87870c6ea789df72d6b2e5f80998e9b3568866eb))
- email log id is nullable ([#228](https://github.com/cirrus-8/laravel-api/issues/228)) ([921d1a9](https://github.com/cirrus-8/laravel-api/commit/921d1a9f432f5eabf743464eea012e6463721fbb))

## [1.2.0-alpha.4](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.3...v1.2.0-alpha.4) (2025-07-16)

### Bug Fixes

- filename is nullable ([#224](https://github.com/cirrus-8/laravel-api/issues/224)) ([27ef2d0](https://github.com/cirrus-8/laravel-api/commit/27ef2d0fde70cec68010591e7b41595b2202cbde))
- lease routes ([#221](https://github.com/cirrus-8/laravel-api/issues/221)) ([830211e](https://github.com/cirrus-8/laravel-api/commit/830211e9f74381cadcad09c2ae5fac720b5862ea))

## [1.2.0-alpha.3](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.2...v1.2.0-alpha.3) (2025-07-16)

### Features

- use cache for database list ([#214](https://github.com/cirrus-8/laravel-api/issues/214)) ([3d03397](https://github.com/cirrus-8/laravel-api/commit/3d03397279402faf7006bbe39fcafbea120a6023))

### Bug Fixes

- send on approval ([#216](https://github.com/cirrus-8/laravel-api/issues/216)) ([70398b2](https://github.com/cirrus-8/laravel-api/commit/70398b212939ce910e90ccef4415e9bc83560798)), closes [#CIR-4871](https://github.com/cirrus-8/laravel-api/issues/CIR-4871)
- view email using postmark api ([#222](https://github.com/cirrus-8/laravel-api/issues/222)) ([90cba52](https://github.com/cirrus-8/laravel-api/commit/90cba5223b90046ab56762d2c5051945b3621729))

## [1.2.0-alpha.2](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.1...v1.2.0-alpha.2) (2025-07-15)

### Features

- add ray debugger for dev ([#211](https://github.com/cirrus-8/laravel-api/issues/211)) ([c24bbf7](https://github.com/cirrus-8/laravel-api/commit/c24bbf7ed7cdeefee8db4fa6814145012744f321))

### Bug Fixes

- change bytes multiplier for mb ([#213](https://github.com/cirrus-8/laravel-api/issues/213)) ([2bd2adf](https://github.com/cirrus-8/laravel-api/commit/2bd2adfc1f35dba90b9c54ea17acbd7f4e9e065c))
- clean up stringHelper and add test ([#208](https://github.com/cirrus-8/laravel-api/issues/208)) ([212d026](https://github.com/cirrus-8/laravel-api/commit/212d026858ab757c547b3513012303fd057e5fc2))
- **company-approval:** remove bank name validation ([#210](https://github.com/cirrus-8/laravel-api/issues/210)) ([8ab6cc6](https://github.com/cirrus-8/laravel-api/commit/8ab6cc63f447e03e42049fa6bf41128b50d2c565))
- route method, remove deprecated arg for name ([#215](https://github.com/cirrus-8/laravel-api/issues/215)) ([a97996b](https://github.com/cirrus-8/laravel-api/commit/a97996bde36396a41b90dbeca543dcdc9153b6e0))

## [1.2.0-alpha.1](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.13...v1.2.0-alpha.1) (2025-07-14)

### Features

- cir-4774 fix property manager mobile number ([#199](https://github.com/cirrus-8/laravel-api/issues/199)) ([0edca8b](https://github.com/cirrus-8/laravel-api/commit/0edca8bfb08a7747a7f3292ffccb3c51de1ae867))
- property manager mobile number on email template ([#164](https://github.com/cirrus-8/laravel-api/issues/164)) ([efbd5ef](https://github.com/cirrus-8/laravel-api/commit/efbd5ef3e99a39f7cbadf6129bd97ab0873e8b6f))
- update types and properties on models ([12b7654](https://github.com/cirrus-8/laravel-api/commit/12b7654306759bf50b4a26f6e5935502d3646bcc))

### Bug Fixes

- allow null on s3 bucket parameter ([#206](https://github.com/cirrus-8/laravel-api/issues/206)) ([bb5aca1](https://github.com/cirrus-8/laravel-api/commit/bb5aca1c42680dcf26adcf395bf3b9ec790cc6e4))
- back charge automation ([#165](https://github.com/cirrus-8/laravel-api/issues/165)) ([51d48f2](https://github.com/cirrus-8/laravel-api/commit/51d48f2c726c7ca2f95f09f7512368415f5059e0)), closes [#CIR-4719](https://github.com/cirrus-8/laravel-api/issues/CIR-4719) [#CIR-4719](https://github.com/cirrus-8/laravel-api/issues/CIR-4719) [#CIR-4719](https://github.com/cirrus-8/laravel-api/issues/CIR-4719)
- back charge automation ([#166](https://github.com/cirrus-8/laravel-api/issues/166)) ([18f361b](https://github.com/cirrus-8/laravel-api/commit/18f361bd18548a569fde9ff4f6ea96a1ab0528f7)), closes [#CIR-4719](https://github.com/cirrus-8/laravel-api/issues/CIR-4719)
- back charge automation ([#202](https://github.com/cirrus-8/laravel-api/issues/202)) ([8f5f53d](https://github.com/cirrus-8/laravel-api/commit/8f5f53dd75d4fb6917f2f6f6676678fe3e245081)), closes [#CIR-4817](https://github.com/cirrus-8/laravel-api/issues/CIR-4817)
- change index source ([#175](https://github.com/cirrus-8/laravel-api/issues/175)) ([2689cd4](https://github.com/cirrus-8/laravel-api/commit/2689cd4dd08152a87ce6ececab7f39db1dc60e13))
- **company-approval:** default value for notes ([#201](https://github.com/cirrus-8/laravel-api/issues/201)) ([f0017f5](https://github.com/cirrus-8/laravel-api/commit/f0017f51a2b072ea9497c8aa1e5c9234e33da330))
- **file:** hot fix bucket not setting right ([#204](https://github.com/cirrus-8/laravel-api/issues/204)) ([4588972](https://github.com/cirrus-8/laravel-api/commit/45889724e8420ff2dc86cb54a0f9c0bc567b3e0d))
- fix property manager mobile number on lease charge review ([#205](https://github.com/cirrus-8/laravel-api/issues/205)) ([76698a5](https://github.com/cirrus-8/laravel-api/commit/76698a59d20775652009ec4f533c53ddbc3e16ee))
- **fm contacts:** add contacts raw error ([#207](https://github.com/cirrus-8/laravel-api/issues/207)) ([8ab17f3](https://github.com/cirrus-8/laravel-api/commit/8ab17f349c51250e3b662cfc63b806664d013661))
- **fm repeating:** bug fix for undefined client id ([#172](https://github.com/cirrus-8/laravel-api/issues/172)) ([9ba2d87](https://github.com/cirrus-8/laravel-api/commit/9ba2d87a6393a325339b39a54ac3db9bc9d81135))
- frontend action name ([4c85946](https://github.com/cirrus-8/laravel-api/commit/4c85946c3bb05455b199b09fe394ff3ef2b8e275))
- limit data query for queued emails ([#183](https://github.com/cirrus-8/laravel-api/issues/183)) ([2a9f3d4](https://github.com/cirrus-8/laravel-api/commit/2a9f3d4e4853a40046ff8586d5640adc609c9ecc))
- lint and stan issues ([be65003](https://github.com/cirrus-8/laravel-api/commit/be65003829545901ecd262a1d935885c0879b9f8))
- logging config ([1f8e9de](https://github.com/cirrus-8/laravel-api/commit/1f8e9de9dbd77114ce27758e2d042cfd7950ecea))
- quick charge rent review ([#195](https://github.com/cirrus-8/laravel-api/issues/195)) ([e2873b1](https://github.com/cirrus-8/laravel-api/commit/e2873b11a778a322e3bcff0e0fa9f5175eb46ad7)), closes [#CIR-4817](https://github.com/cirrus-8/laravel-api/issues/CIR-4817) [#CIR-4817](https://github.com/cirrus-8/laravel-api/issues/CIR-4817)
- send sms icon in contacts and company rejection process & email notif ([#194](https://github.com/cirrus-8/laravel-api/issues/194)) ([0e1f7d1](https://github.com/cirrus-8/laravel-api/commit/0e1f7d16c4127d4bcc575f414c7dadcc731b9322))
- use the value of the enum ([#197](https://github.com/cirrus-8/laravel-api/issues/197)) ([f94dee3](https://github.com/cirrus-8/laravel-api/commit/f94dee32cbaa73ec8e060c64190e9dfc423b2fb5))

## [1.1.5](https://github.com/cirrus-8/laravel-api/compare/v1.2.0-alpha.0...v1.1.5) (2025-07-01)

### Bug Fixes

- change index source ([#175](https://github.com/cirrus-8/laravel-api/issues/175)) ([2689cd4](https://github.com/cirrus-8/laravel-api/commit/2689cd4dd08152a87ce6ececab7f39db1dc60e13))
- **fm repeating:** bug fix for undefined client id ([#172](https://github.com/cirrus-8/laravel-api/issues/172)) ([9ba2d87](https://github.com/cirrus-8/laravel-api/commit/9ba2d87a6393a325339b39a54ac3db9bc9d81135))

## [1.2.0-alpha.0](https://github.com/cirrus-8/laravel-api/compare/v1.1.0...v1.2.0-alpha.0) (2025-06-30)

### Features

- property manager mobile number on email template ([#164](https://github.com/cirrus-8/laravel-api/issues/164)) ([efbd5ef](https://github.com/cirrus-8/laravel-api/commit/efbd5ef3e99a39f7cbadf6129bd97ab0873e8b6f))

### Bug Fixes

- back charge automation ([#165](https://github.com/cirrus-8/laravel-api/issues/165)) ([51d48f2](https://github.com/cirrus-8/laravel-api/commit/51d48f2c726c7ca2f95f09f7512368415f5059e0)), closes [#CIR-4719](https://github.com/cirrus-8/laravel-api/issues/CIR-4719) [#CIR-4719](https://github.com/cirrus-8/laravel-api/issues/CIR-4719) [#CIR-4719](https://github.com/cirrus-8/laravel-api/issues/CIR-4719)
- back charge automation ([#166](https://github.com/cirrus-8/laravel-api/issues/166)) ([18f361b](https://github.com/cirrus-8/laravel-api/commit/18f361bd18548a569fde9ff4f6ea96a1ab0528f7)), closes [#CIR-4719](https://github.com/cirrus-8/laravel-api/issues/CIR-4719)

## [1.1.0](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.13...v1.1.0) (2025-06-25)

## [1.1.0-alpha.13](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.12...v1.1.0-alpha.13) (2025-06-25)

### Features

- property manager mobile number on email template ([#162](https://github.com/cirrus-8/laravel-api/issues/162)) ([7028b17](https://github.com/cirrus-8/laravel-api/commit/7028b1771e6530c889e34c25e9cca49fbc0b6ef9))

### Bug Fixes

- **company-documents:** default value for the uploaded file in beta component ([#163](https://github.com/cirrus-8/laravel-api/issues/163)) ([1b9979f](https://github.com/cirrus-8/laravel-api/commit/1b9979f5f6267ddccae9bd18f98fa917d1970269))

## [1.1.0-alpha.12](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.11...v1.1.0-alpha.12) (2025-06-24)

## [1.1.0-alpha.11](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.10...v1.1.0-alpha.11) (2025-06-23)

### Bug Fixes

- back charge automation testing ([#135](https://github.com/cirrus-8/laravel-api/issues/135)) ([0a93f19](https://github.com/cirrus-8/laravel-api/commit/0a93f19ac35831799f6960eee7a46a0d5947b31a)), closes [#CIR-3848](https://github.com/cirrus-8/laravel-api/issues/CIR-3848) [#CIR-3848](https://github.com/cirrus-8/laravel-api/issues/CIR-3848) [#CIR-3848](https://github.com/cirrus-8/laravel-api/issues/CIR-3848) [#CIR-3848](https://github.com/cirrus-8/laravel-api/issues/CIR-3848)
- quick payment controller ([#138](https://github.com/cirrus-8/laravel-api/issues/138)) ([57bd61b](https://github.com/cirrus-8/laravel-api/commit/57bd61b69d713953d3d41e1ae50cbf8f9a11d13c))

## [1.1.0-alpha.10](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.9...v1.1.0-alpha.10) (2025-06-19)

### Features

- add php stan ([#136](https://github.com/cirrus-8/laravel-api/issues/136)) ([a354834](https://github.com/cirrus-8/laravel-api/commit/a354834de303adbff891e6806e08074688f89d19))

### Bug Fixes

- no color in dark mode ([#134](https://github.com/cirrus-8/laravel-api/issues/134)) ([03d411e](https://github.com/cirrus-8/laravel-api/commit/03d411e9beee32825332d3d2ced18392f73c6987))

## [1.1.0-alpha.9](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.8...v1.1.0-alpha.9) (2025-06-19)

### Bug Fixes

- mix env setup ([3b03e28](https://github.com/cirrus-8/laravel-api/commit/3b03e2894bbd2e7de7034d6443d2695cd579a182))

## [1.1.0-alpha.8](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.7...v1.1.0-alpha.8) (2025-06-19)

### Features

- laravel horizon and improvement ([#117](https://github.com/cirrus-8/laravel-api/issues/117)) ([7c4010c](https://github.com/cirrus-8/laravel-api/commit/7c4010c4e5cd517dfec58553bba8d763c74d658d))

### Bug Fixes

- **workorder:** workorder repeating edit details ([#131](https://github.com/cirrus-8/laravel-api/issues/131)) ([01b2726](https://github.com/cirrus-8/laravel-api/commit/01b2726119186160bac8a377e1cd1ee68ac9b56f))

## [1.1.0-alpha.7](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.6...v1.1.0-alpha.7) (2025-06-17)

### Features

- add bugsnag to track errors ([#130](https://github.com/cirrus-8/laravel-api/issues/130)) ([1fb6a5b](https://github.com/cirrus-8/laravel-api/commit/1fb6a5b38a0baa114c6e5348da5162ba3b5fd1d0))

## [1.1.0-alpha.6](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.4...v1.1.0-alpha.6) (2025-06-13)

### Features

- user able to append file one by one ([#126](https://github.com/cirrus-8/laravel-api/issues/126)) ([eeaad3e](https://github.com/cirrus-8/laravel-api/commit/eeaad3e82d5514b97a0659586e89b58783483ab9))

## [1.1.0-alpha.5](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.4...v1.1.0-alpha.5) (2025-06-13)

### Features

- user able to append file one by one ([#126](https://github.com/cirrus-8/laravel-api/issues/126)) ([eeaad3e](https://github.com/cirrus-8/laravel-api/commit/eeaad3e82d5514b97a0659586e89b58783483ab9))

## [1.1.0-alpha.4](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.3...v1.1.0-alpha.4) (2025-06-13)

### Features

- able to delete attached file individually ([#121](https://github.com/cirrus-8/laravel-api/issues/121)) ([8fca6af](https://github.com/cirrus-8/laravel-api/commit/8fca6af1d6846e484dea06c625ad7b84ff3389a0))
- separate archived account on the screen ([#124](https://github.com/cirrus-8/laravel-api/issues/124)) ([39636f1](https://github.com/cirrus-8/laravel-api/commit/39636f1960ced7c37dbe2f8991b915916ad12912))

### Bug Fixes

- conditionally create lease term log updates ([#123](https://github.com/cirrus-8/laravel-api/issues/123)) ([0a4f022](https://github.com/cirrus-8/laravel-api/commit/0a4f022f8144085d31c2222b914d1a5d2c225f2d))
- take numeric into consideration ([#122](https://github.com/cirrus-8/laravel-api/issues/122)) ([5c2abf4](https://github.com/cirrus-8/laravel-api/commit/5c2abf4755ebd827284323ca9c81df5c73544e76))

## [1.1.0-alpha.3](https://github.com/cirrus-8/laravel-api/compare/v1.1.0-alpha.2...v1.1.0-alpha.3) (2025-06-12)

### Features

- change post town to town ([#120](https://github.com/cirrus-8/laravel-api/issues/120)) ([13ce577](https://github.com/cirrus-8/laravel-api/commit/13ce5775b105cf04f0e22dd18876e60d28b1c3ee))

### Bug Fixes

- send email letter(vue js) ([#118](https://github.com/cirrus-8/laravel-api/issues/118)) ([de16e5b](https://github.com/cirrus-8/laravel-api/commit/de16e5bf8dcb0b45d3363c5ddcbc7c790c89a3b5)), closes [#CIR-4609](https://github.com/cirrus-8/laravel-api/issues/CIR-4609)

## 1.1.0-alpha.2 (2025-06-11)

### Features

- cir 3837 lease term log for lease form ([#31](https://github.com/cirrus-8/laravel-api/issues/31)) ([e480d85](https://github.com/cirrus-8/laravel-api/commit/e480d85f0822187f484dd1212596ff6d8f58bf6a))
- **docker:** add docker setup - add missing node packages for webpack ([6b870b3](https://github.com/cirrus-8/laravel-api/commit/6b870b3eeeef3d0015a038a3601085b03389f426))
- hide Ledger and sub-ledger for now ([e810ee3](https://github.com/cirrus-8/laravel-api/commit/e810ee39e8118a60be5a3e77807792a265e37331))
- new attachment component ([#87](https://github.com/cirrus-8/laravel-api/issues/87)) ([ffea0bf](https://github.com/cirrus-8/laravel-api/commit/ffea0bf426c6c6084ced5e5527284f0cb5fe9e85))
- setup versioning scripts ([e6883f7](https://github.com/cirrus-8/laravel-api/commit/e6883f7c528617395f5938ed8bd2ebb7fc6d74a3))
- update date helper ([#106](https://github.com/cirrus-8/laravel-api/issues/106)) ([36f3b20](https://github.com/cirrus-8/laravel-api/commit/36f3b2083a766093cfdb63f87c67a3665ff75fb7))

### Bug Fixes

- backcharge automation approval ([#111](https://github.com/cirrus-8/laravel-api/issues/111)) ([4ef5ab4](https://github.com/cirrus-8/laravel-api/commit/4ef5ab46c74766ce16b612153fd2e7805607de03)), closes [#CIR-3846](https://github.com/cirrus-8/laravel-api/issues/CIR-3846)
- **billing-groups:** main property address on ai list ([#34](https://github.com/cirrus-8/laravel-api/issues/34)) ([7a70501](https://github.com/cirrus-8/laravel-api/commit/7a705013082b9dd27a2978f30550049a6445dbbd))
- change endpoint for temp and missing model ([#89](https://github.com/cirrus-8/laravel-api/issues/89)) ([65e952f](https://github.com/cirrus-8/laravel-api/commit/65e952f3ea1e022e323e1fefbcac738f0d97c9b3))
- **ci:** use ubuntu-latest ([#72](https://github.com/cirrus-8/laravel-api/issues/72)) ([60b5b48](https://github.com/cirrus-8/laravel-api/commit/60b5b485c05fd0ea31c9d1100c99d04a76f75527))
- convert object to array as it is. ([2b9224f](https://github.com/cirrus-8/laravel-api/commit/2b9224fae7fd409867cac8381b038c500bd9ec57))
- data types of list coming from framework is either array or object, make sure the system can handle both. ([5d437f9](https://github.com/cirrus-8/laravel-api/commit/5d437f97c6065f667b9b206b71887d8b8c0d287f))
- disable state for UK in json ([#113](https://github.com/cirrus-8/laravel-api/issues/113)) ([8df0a0a](https://github.com/cirrus-8/laravel-api/commit/8df0a0a5c095d4f3a8edacc3d004a755ccb3a4b6))
- download is not showing ([#114](https://github.com/cirrus-8/laravel-api/issues/114)) ([567c447](https://github.com/cirrus-8/laravel-api/commit/567c44750adc4a97e2f682ffc2ce6e221a5b8d9a))
- fitzroy custom reports ([#108](https://github.com/cirrus-8/laravel-api/issues/108)) ([d64358c](https://github.com/cirrus-8/laravel-api/commit/d64358cd60844dda8ee381b7acaada6f20e5b321)), closes [#CIR-4582](https://github.com/cirrus-8/laravel-api/issues/CIR-4582)
- fix broken note area bg color ([#83](https://github.com/cirrus-8/laravel-api/issues/83)) ([3a0fbc3](https://github.com/cirrus-8/laravel-api/commit/3a0fbc3c129367df3c0981e725e9adc18b01e090))
- fix error due to refactored code ([#92](https://github.com/cirrus-8/laravel-api/issues/92)) ([09dfc73](https://github.com/cirrus-8/laravel-api/commit/09dfc73e974341dd7884b945ec56e3ae2650ef0d))
- fix jasper service ([#107](https://github.com/cirrus-8/laravel-api/issues/107)) ([529463f](https://github.com/cirrus-8/laravel-api/commit/529463ff8e944623eaad7c35470a5e69cdcbe454))
- fix windows environment for vue3 ([#37](https://github.com/cirrus-8/laravel-api/issues/37)) ([2592e5a](https://github.com/cirrus-8/laravel-api/commit/2592e5a62d69623c14fe82b3b661373f05eb01a3))
- **fm:** fixed by removing db raw on select ([#101](https://github.com/cirrus-8/laravel-api/issues/101)) ([96a75eb](https://github.com/cirrus-8/laravel-api/commit/96a75eb80a6a3d3666fe4b2127d168df6c6a26e1))
- get subject from the request and refactor code ([3fb15af](https://github.com/cirrus-8/laravel-api/commit/3fb15afd42eb44e0f31eabe54e1c71bc18d65eeb))
- map object entries to an array ([046c25d](https://github.com/cirrus-8/laravel-api/commit/046c25db9de4b5b8d354bcecff5afcbf48172b93))
- missing Use Principal Owner detail ([#103](https://github.com/cirrus-8/laravel-api/issues/103)) ([52dd1b3](https://github.com/cirrus-8/laravel-api/commit/52dd1b361afdc263d335acf815f82fcdbb4a7819))
- **QuickPayment:** fix payment process ([#71](https://github.com/cirrus-8/laravel-api/issues/71)) ([367522c](https://github.com/cirrus-8/laravel-api/commit/367522c072ac00045d50cd238c792d0bd98f6062))
- remove isset for useDocumentTemp ([#96](https://github.com/cirrus-8/laravel-api/issues/96)) ([0967f19](https://github.com/cirrus-8/laravel-api/commit/0967f1920900dba5fcc7cc655d8233ef7e4fcbe9))
- remove unused vue cli ([#39](https://github.com/cirrus-8/laravel-api/issues/39)) ([dac5be9](https://github.com/cirrus-8/laravel-api/commit/dac5be98edd5e6f6b1b1d89288ac0e2a1a6f89f0))
- rent review custom query ([#50](https://github.com/cirrus-8/laravel-api/issues/50)) ([0027998](https://github.com/cirrus-8/laravel-api/commit/0027998ab5d15c6af840dc7a265ffdca15f2f673)), closes [#CIR-4441](https://github.com/cirrus-8/laravel-api/issues/CIR-4441) [#CIR-4441](https://github.com/cirrus-8/laravel-api/issues/CIR-4441)
- replaced isset as it recognize null as not set. ([6611e4e](https://github.com/cirrus-8/laravel-api/commit/6611e4ecabdbbb53e48089c2b09384b62c610faf))
- undefined name in getAccountCodeManagementvsPriorYear ([#100](https://github.com/cirrus-8/laravel-api/issues/100)) ([c1d5bd5](https://github.com/cirrus-8/laravel-api/commit/c1d5bd589d7dcf7bd2a8d3b49cda7de53af99502))
- update ci to seperate for commits vs php ([#17](https://github.com/cirrus-8/laravel-api/issues/17)) ([a361703](https://github.com/cirrus-8/laravel-api/commit/a36170355f87d1a2334f4824af4aae1bdd1055cb))
- update readme ([#32](https://github.com/cirrus-8/laravel-api/issues/32)) ([9531328](https://github.com/cirrus-8/laravel-api/commit/9531328bfaf67da217318383a103b8259eeff583))
- update redirect url to the letters ([#82](https://github.com/cirrus-8/laravel-api/issues/82)) ([535d7f3](https://github.com/cirrus-8/laravel-api/commit/535d7f3d799d185616d17d0681f9b1ed282b6428))
- use leaseOption instead of leaseExpiry ([#88](https://github.com/cirrus-8/laravel-api/issues/88)) ([479e448](https://github.com/cirrus-8/laravel-api/commit/479e448acd6bbdcc20ee402097e9858cbb482c14))
- useDocxFile treated as string instead of number ([#95](https://github.com/cirrus-8/laravel-api/issues/95)) ([1f46fa1](https://github.com/cirrus-8/laravel-api/commit/1f46fa10502eb62aee498d366e75b6593797a049))
- vend custom report ([#86](https://github.com/cirrus-8/laravel-api/issues/86)) ([17e8bba](https://github.com/cirrus-8/laravel-api/commit/17e8bba7361fb26510cb2bbfa2267c69a192cf23)), closes [#CIR-4407](https://github.com/cirrus-8/laravel-api/issues/CIR-4407)
- wrong merge, fix removed extra = and condition ([a9ec81b](https://github.com/cirrus-8/laravel-api/commit/a9ec81b80e168c0bd5991bab648f68703ce6dc24))
- wrong variable case and parameters ([#97](https://github.com/cirrus-8/laravel-api/issues/97)) ([559b7e8](https://github.com/cirrus-8/laravel-api/commit/559b7e8938567266d14b58f903604fd2e6e3599d))

## 1.1.0-alpha.1 (2025-06-11)

### Features

- cir 3837 lease term log for lease form ([#31](https://github.com/cirrus-8/laravel-api/issues/31)) ([e480d85](https://github.com/cirrus-8/laravel-api/commit/e480d85f0822187f484dd1212596ff6d8f58bf6a))
- **docker:** add docker setup - add missing node packages for webpack ([6b870b3](https://github.com/cirrus-8/laravel-api/commit/6b870b3eeeef3d0015a038a3601085b03389f426))
- hide Ledger and sub-ledger for now ([e810ee3](https://github.com/cirrus-8/laravel-api/commit/e810ee39e8118a60be5a3e77807792a265e37331))
- new attachment component ([#87](https://github.com/cirrus-8/laravel-api/issues/87)) ([ffea0bf](https://github.com/cirrus-8/laravel-api/commit/ffea0bf426c6c6084ced5e5527284f0cb5fe9e85))
- setup versioning scripts ([e6883f7](https://github.com/cirrus-8/laravel-api/commit/e6883f7c528617395f5938ed8bd2ebb7fc6d74a3))
- update date helper ([#106](https://github.com/cirrus-8/laravel-api/issues/106)) ([36f3b20](https://github.com/cirrus-8/laravel-api/commit/36f3b2083a766093cfdb63f87c67a3665ff75fb7))

### Bug Fixes

- backcharge automation approval ([#111](https://github.com/cirrus-8/laravel-api/issues/111)) ([4ef5ab4](https://github.com/cirrus-8/laravel-api/commit/4ef5ab46c74766ce16b612153fd2e7805607de03)), closes [#CIR-3846](https://github.com/cirrus-8/laravel-api/issues/CIR-3846)
- **billing-groups:** main property address on ai list ([#34](https://github.com/cirrus-8/laravel-api/issues/34)) ([7a70501](https://github.com/cirrus-8/laravel-api/commit/7a705013082b9dd27a2978f30550049a6445dbbd))
- change endpoint for temp and missing model ([#89](https://github.com/cirrus-8/laravel-api/issues/89)) ([65e952f](https://github.com/cirrus-8/laravel-api/commit/65e952f3ea1e022e323e1fefbcac738f0d97c9b3))
- **ci:** use ubuntu-latest ([#72](https://github.com/cirrus-8/laravel-api/issues/72)) ([60b5b48](https://github.com/cirrus-8/laravel-api/commit/60b5b485c05fd0ea31c9d1100c99d04a76f75527))
- convert object to array as it is. ([2b9224f](https://github.com/cirrus-8/laravel-api/commit/2b9224fae7fd409867cac8381b038c500bd9ec57))
- data types of list coming from framework is either array or object, make sure the system can handle both. ([5d437f9](https://github.com/cirrus-8/laravel-api/commit/5d437f97c6065f667b9b206b71887d8b8c0d287f))
- disable state for UK in json ([#113](https://github.com/cirrus-8/laravel-api/issues/113)) ([8df0a0a](https://github.com/cirrus-8/laravel-api/commit/8df0a0a5c095d4f3a8edacc3d004a755ccb3a4b6))
- fitzroy custom reports ([#108](https://github.com/cirrus-8/laravel-api/issues/108)) ([d64358c](https://github.com/cirrus-8/laravel-api/commit/d64358cd60844dda8ee381b7acaada6f20e5b321)), closes [#CIR-4582](https://github.com/cirrus-8/laravel-api/issues/CIR-4582)
- fix broken note area bg color ([#83](https://github.com/cirrus-8/laravel-api/issues/83)) ([3a0fbc3](https://github.com/cirrus-8/laravel-api/commit/3a0fbc3c129367df3c0981e725e9adc18b01e090))
- fix error due to refactored code ([#92](https://github.com/cirrus-8/laravel-api/issues/92)) ([09dfc73](https://github.com/cirrus-8/laravel-api/commit/09dfc73e974341dd7884b945ec56e3ae2650ef0d))
- fix jasper service ([#107](https://github.com/cirrus-8/laravel-api/issues/107)) ([529463f](https://github.com/cirrus-8/laravel-api/commit/529463ff8e944623eaad7c35470a5e69cdcbe454))
- fix windows environment for vue3 ([#37](https://github.com/cirrus-8/laravel-api/issues/37)) ([2592e5a](https://github.com/cirrus-8/laravel-api/commit/2592e5a62d69623c14fe82b3b661373f05eb01a3))
- **fm:** fixed by removing db raw on select ([#101](https://github.com/cirrus-8/laravel-api/issues/101)) ([96a75eb](https://github.com/cirrus-8/laravel-api/commit/96a75eb80a6a3d3666fe4b2127d168df6c6a26e1))
- get subject from the request and refactor code ([3fb15af](https://github.com/cirrus-8/laravel-api/commit/3fb15afd42eb44e0f31eabe54e1c71bc18d65eeb))
- map object entries to an array ([046c25d](https://github.com/cirrus-8/laravel-api/commit/046c25db9de4b5b8d354bcecff5afcbf48172b93))
- missing Use Principal Owner detail ([#103](https://github.com/cirrus-8/laravel-api/issues/103)) ([52dd1b3](https://github.com/cirrus-8/laravel-api/commit/52dd1b361afdc263d335acf815f82fcdbb4a7819))
- **QuickPayment:** fix payment process ([#71](https://github.com/cirrus-8/laravel-api/issues/71)) ([367522c](https://github.com/cirrus-8/laravel-api/commit/367522c072ac00045d50cd238c792d0bd98f6062))
- remove isset for useDocumentTemp ([#96](https://github.com/cirrus-8/laravel-api/issues/96)) ([0967f19](https://github.com/cirrus-8/laravel-api/commit/0967f1920900dba5fcc7cc655d8233ef7e4fcbe9))
- remove unused vue cli ([#39](https://github.com/cirrus-8/laravel-api/issues/39)) ([dac5be9](https://github.com/cirrus-8/laravel-api/commit/dac5be98edd5e6f6b1b1d89288ac0e2a1a6f89f0))
- rent review custom query ([#50](https://github.com/cirrus-8/laravel-api/issues/50)) ([0027998](https://github.com/cirrus-8/laravel-api/commit/0027998ab5d15c6af840dc7a265ffdca15f2f673)), closes [#CIR-4441](https://github.com/cirrus-8/laravel-api/issues/CIR-4441) [#CIR-4441](https://github.com/cirrus-8/laravel-api/issues/CIR-4441)
- replaced isset as it recognize null as not set. ([6611e4e](https://github.com/cirrus-8/laravel-api/commit/6611e4ecabdbbb53e48089c2b09384b62c610faf))
- undefined name in getAccountCodeManagementvsPriorYear ([#100](https://github.com/cirrus-8/laravel-api/issues/100)) ([c1d5bd5](https://github.com/cirrus-8/laravel-api/commit/c1d5bd589d7dcf7bd2a8d3b49cda7de53af99502))
- update ci to seperate for commits vs php ([#17](https://github.com/cirrus-8/laravel-api/issues/17)) ([a361703](https://github.com/cirrus-8/laravel-api/commit/a36170355f87d1a2334f4824af4aae1bdd1055cb))
- update readme ([#32](https://github.com/cirrus-8/laravel-api/issues/32)) ([9531328](https://github.com/cirrus-8/laravel-api/commit/9531328bfaf67da217318383a103b8259eeff583))
- update redirect url to the letters ([#82](https://github.com/cirrus-8/laravel-api/issues/82)) ([535d7f3](https://github.com/cirrus-8/laravel-api/commit/535d7f3d799d185616d17d0681f9b1ed282b6428))
- use leaseOption instead of leaseExpiry ([#88](https://github.com/cirrus-8/laravel-api/issues/88)) ([479e448](https://github.com/cirrus-8/laravel-api/commit/479e448acd6bbdcc20ee402097e9858cbb482c14))
- useDocxFile treated as string instead of number ([#95](https://github.com/cirrus-8/laravel-api/issues/95)) ([1f46fa1](https://github.com/cirrus-8/laravel-api/commit/1f46fa10502eb62aee498d366e75b6593797a049))
- vend custom report ([#86](https://github.com/cirrus-8/laravel-api/issues/86)) ([17e8bba](https://github.com/cirrus-8/laravel-api/commit/17e8bba7361fb26510cb2bbfa2267c69a192cf23)), closes [#CIR-4407](https://github.com/cirrus-8/laravel-api/issues/CIR-4407)
- wrong merge, fix removed extra = and condition ([a9ec81b](https://github.com/cirrus-8/laravel-api/commit/a9ec81b80e168c0bd5991bab648f68703ce6dc24))
- wrong variable case and parameters ([#97](https://github.com/cirrus-8/laravel-api/issues/97)) ([559b7e8](https://github.com/cirrus-8/laravel-api/commit/559b7e8938567266d14b58f903604fd2e6e3599d))

## 1.1.0-alpha.0 (2025-06-10)

### Features

- cir 3837 lease term log for lease form ([#31](https://github.com/cirrus-8/laravel-api/issues/31)) ([e480d85](https://github.com/cirrus-8/laravel-api/commit/e480d85f0822187f484dd1212596ff6d8f58bf6a))
- **docker:** add docker setup - add missing node packages for webpack ([6b870b3](https://github.com/cirrus-8/laravel-api/commit/6b870b3eeeef3d0015a038a3601085b03389f426))
- hide Ledger and sub-ledger for now ([e810ee3](https://github.com/cirrus-8/laravel-api/commit/e810ee39e8118a60be5a3e77807792a265e37331))
- new attachment component ([#87](https://github.com/cirrus-8/laravel-api/issues/87)) ([ffea0bf](https://github.com/cirrus-8/laravel-api/commit/ffea0bf426c6c6084ced5e5527284f0cb5fe9e85))
- setup versioning scripts ([e6883f7](https://github.com/cirrus-8/laravel-api/commit/e6883f7c528617395f5938ed8bd2ebb7fc6d74a3))

### Bug Fixes

- **billing-groups:** main property address on ai list ([#34](https://github.com/cirrus-8/laravel-api/issues/34)) ([7a70501](https://github.com/cirrus-8/laravel-api/commit/7a705013082b9dd27a2978f30550049a6445dbbd))
- change endpoint for temp and missing model ([#89](https://github.com/cirrus-8/laravel-api/issues/89)) ([65e952f](https://github.com/cirrus-8/laravel-api/commit/65e952f3ea1e022e323e1fefbcac738f0d97c9b3))
- **ci:** use ubuntu-latest ([#72](https://github.com/cirrus-8/laravel-api/issues/72)) ([60b5b48](https://github.com/cirrus-8/laravel-api/commit/60b5b485c05fd0ea31c9d1100c99d04a76f75527))
- convert object to array as it is. ([2b9224f](https://github.com/cirrus-8/laravel-api/commit/2b9224fae7fd409867cac8381b038c500bd9ec57))
- data types of list coming from framework is either array or object, make sure the system can handle both. ([5d437f9](https://github.com/cirrus-8/laravel-api/commit/5d437f97c6065f667b9b206b71887d8b8c0d287f))
- fitzroy custom reports ([#108](https://github.com/cirrus-8/laravel-api/issues/108)) ([d64358c](https://github.com/cirrus-8/laravel-api/commit/d64358cd60844dda8ee381b7acaada6f20e5b321)), closes [#CIR-4582](https://github.com/cirrus-8/laravel-api/issues/CIR-4582)
- fix broken note area bg color ([#83](https://github.com/cirrus-8/laravel-api/issues/83)) ([3a0fbc3](https://github.com/cirrus-8/laravel-api/commit/3a0fbc3c129367df3c0981e725e9adc18b01e090))
- fix error due to refactored code ([#92](https://github.com/cirrus-8/laravel-api/issues/92)) ([09dfc73](https://github.com/cirrus-8/laravel-api/commit/09dfc73e974341dd7884b945ec56e3ae2650ef0d))
- fix jasper service ([#107](https://github.com/cirrus-8/laravel-api/issues/107)) ([529463f](https://github.com/cirrus-8/laravel-api/commit/529463ff8e944623eaad7c35470a5e69cdcbe454))
- fix windows environment for vue3 ([#37](https://github.com/cirrus-8/laravel-api/issues/37)) ([2592e5a](https://github.com/cirrus-8/laravel-api/commit/2592e5a62d69623c14fe82b3b661373f05eb01a3))
- **fm:** fixed by removing db raw on select ([#101](https://github.com/cirrus-8/laravel-api/issues/101)) ([96a75eb](https://github.com/cirrus-8/laravel-api/commit/96a75eb80a6a3d3666fe4b2127d168df6c6a26e1))
- get subject from the request and refactor code ([3fb15af](https://github.com/cirrus-8/laravel-api/commit/3fb15afd42eb44e0f31eabe54e1c71bc18d65eeb))
- map object entries to an array ([046c25d](https://github.com/cirrus-8/laravel-api/commit/046c25db9de4b5b8d354bcecff5afcbf48172b93))
- missing Use Principal Owner detail ([#103](https://github.com/cirrus-8/laravel-api/issues/103)) ([52dd1b3](https://github.com/cirrus-8/laravel-api/commit/52dd1b361afdc263d335acf815f82fcdbb4a7819))
- **QuickPayment:** fix payment process ([#71](https://github.com/cirrus-8/laravel-api/issues/71)) ([367522c](https://github.com/cirrus-8/laravel-api/commit/367522c072ac00045d50cd238c792d0bd98f6062))
- remove isset for useDocumentTemp ([#96](https://github.com/cirrus-8/laravel-api/issues/96)) ([0967f19](https://github.com/cirrus-8/laravel-api/commit/0967f1920900dba5fcc7cc655d8233ef7e4fcbe9))
- remove unused vue cli ([#39](https://github.com/cirrus-8/laravel-api/issues/39)) ([dac5be9](https://github.com/cirrus-8/laravel-api/commit/dac5be98edd5e6f6b1b1d89288ac0e2a1a6f89f0))
- rent review custom query ([#50](https://github.com/cirrus-8/laravel-api/issues/50)) ([0027998](https://github.com/cirrus-8/laravel-api/commit/0027998ab5d15c6af840dc7a265ffdca15f2f673)), closes [#CIR-4441](https://github.com/cirrus-8/laravel-api/issues/CIR-4441) [#CIR-4441](https://github.com/cirrus-8/laravel-api/issues/CIR-4441)
- replaced isset as it recognize null as not set. ([6611e4e](https://github.com/cirrus-8/laravel-api/commit/6611e4ecabdbbb53e48089c2b09384b62c610faf))
- undefined name in getAccountCodeManagementvsPriorYear ([#100](https://github.com/cirrus-8/laravel-api/issues/100)) ([c1d5bd5](https://github.com/cirrus-8/laravel-api/commit/c1d5bd589d7dcf7bd2a8d3b49cda7de53af99502))
- update ci to seperate for commits vs php ([#17](https://github.com/cirrus-8/laravel-api/issues/17)) ([a361703](https://github.com/cirrus-8/laravel-api/commit/a36170355f87d1a2334f4824af4aae1bdd1055cb))
- update readme ([#32](https://github.com/cirrus-8/laravel-api/issues/32)) ([9531328](https://github.com/cirrus-8/laravel-api/commit/9531328bfaf67da217318383a103b8259eeff583))
- update redirect url to the letters ([#82](https://github.com/cirrus-8/laravel-api/issues/82)) ([535d7f3](https://github.com/cirrus-8/laravel-api/commit/535d7f3d799d185616d17d0681f9b1ed282b6428))
- use leaseOption instead of leaseExpiry ([#88](https://github.com/cirrus-8/laravel-api/issues/88)) ([479e448](https://github.com/cirrus-8/laravel-api/commit/479e448acd6bbdcc20ee402097e9858cbb482c14))
- useDocxFile treated as string instead of number ([#95](https://github.com/cirrus-8/laravel-api/issues/95)) ([1f46fa1](https://github.com/cirrus-8/laravel-api/commit/1f46fa10502eb62aee498d366e75b6593797a049))
- vend custom report ([#86](https://github.com/cirrus-8/laravel-api/issues/86)) ([17e8bba](https://github.com/cirrus-8/laravel-api/commit/17e8bba7361fb26510cb2bbfa2267c69a192cf23)), closes [#CIR-4407](https://github.com/cirrus-8/laravel-api/issues/CIR-4407)
- wrong merge, fix removed extra = and condition ([a9ec81b](https://github.com/cirrus-8/laravel-api/commit/a9ec81b80e168c0bd5991bab648f68703ce6dc24))
- wrong variable case and parameters ([#97](https://github.com/cirrus-8/laravel-api/issues/97)) ([559b7e8](https://github.com/cirrus-8/laravel-api/commit/559b7e8938567266d14b58f903604fd2e6e3599d))
