/.phpunit.cache
.DS_Store
node_modules
/public/build
/public/hot
/public/storage
/public/vuejs
/public/mix-manifest.json
/public/vue2-mix-manifest.json
/public/vue3-mix-manifest.json
/public/phpinfo.php

/vendor

.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode
/.vagrant
queue_email_scheduler.log


#optional files
checkhead.php
info.php
ocr_scheduler.log
ocr_update.bat
web.config
composer.phar
optimization.bat
optimization_local.bat
*.cache

#manually copied files
.env
/public/cacert.pem
config/cors.php
/storage/*.key
/app/Http/Middleware/Cors.php
database/backups
