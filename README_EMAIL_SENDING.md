# EMAIL SENDING PROCESS #

### Command: ###
* php artisan queue:work - this will send the email using postmark
* php artisan command:moveEmailToJob  - this will move the email content to job
### API: ###
* api/administration/email/process/move-email-to-job - this will move the email content to job using API

### Database: NPMS ###
### Tables ###
* queue_email
* queue_email_recipient
* queue_email_cc
* qeuue_email_attachments
* queue_email_activity_logs

### PROCESS 
From Framework, system save the email content and details to npms database then 
send a request to api to dispatch a job where send all pending email to job

Alternatives: use batch file scheduler to move the queue email to job.





