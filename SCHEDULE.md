# Laravel Artisan Commands

This document lists commonly used Artisan commands and their logging outputs for reference.

---

### Commands and Logs

1. **Decode and Clear Files**

    ```bash
    php artisan AiDecodeAndClearEncodedFiles:rundecodeandclear > ai_decodeandclear_scheduler.log
    ```

    - **Description:** Runs the decoding and clearing process for encoded files.
    - **Log File:** `ai_decodeandclear_scheduler.log`

2. **Queue Worker**

    ```bash
    php artisan queue:work
    ```

    - **Description:** Starts processing jobs on the queue workers.
    - **Log File:** None (Real-time worker)

3. **Move Email to Job**

    ```bash
    php artisan command:moveEmailToJob > queue_email_scheduler.log
    ```

    - **Description:** Moves email-related tasks into the queue.
    - **Log File:** `queue_email_scheduler.log`

4. **Email Internal Error Report**

    ```bash
    php artisan report:emailInternalError > sms_email_error_report.log
    ```

    - **Description:** Sends internal error report emails.
    - **Log File:** `sms_email_error_report.log`

5. **FM Repeats Scheduler**

    ```bash
    php artisan FM:Repeats > fm_repeats_scheduler.log
    ```

    - **Description:** Manages the FM repeats scheduler.
    - **Log File:** `fm_repeats_scheduler.log`

6. **FM Update Scheduler**

    ```bash
    php artisan FM:Update > fm_update_scheduler.log
    ```

    - **Description:** Manages updates for FM scheduler tasks.
    - **Log File:** `fm_update_scheduler.log`

7. **NPMS Run Scheduler**

    ```bash
    php artisan NPMS:Run > npms_run_scheduler.log
    ```

    - **Description:** Executes the NPMS process scheduler.
    - **Log File:** `npms_run_scheduler.log`

8. **NPMS Update Scheduler**

    ```bash
    php artisan NPMS:Update > npms_update_scheduler.log
    ```

    - **Description:** Updates the NPMS process scheduler tasks.
    - **Log File:** `npms_update_scheduler.log`

9. **NPMS User Logs Report**
    ```bash
    php artisan NPMS:LogReport > npms_user_logs_report_scheduler.log
    ```
    - **Description:** Generates user logs reports for NPMS.
    - **Log File:** `npms_user_logs_report_scheduler.log`

---

### Notes

- **Log Files:** All commands (except `queue:work`) generate corresponding log files for debugging and tracing purposes.
- Ensure the Laravel environment and file permissions allow for the creation and appending of these log files.
