{"name": "@cirrus8/api-vue3", "version": "1.3.0-alpha.16", "private": true, "scripts": {"dev": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "build": "mix --production", "test": "npm run lint", "lint": "eslint --ext .js,.ts,.vue src", "style": "prettier . --check"}, "dependencies": {"@mdi/font": "^7.4.47", "axios": "^1.7.9", "mitt": "^3.0.1", "roboto-fontface": "^0.10.0", "tailwindcss": "^4.0.0", "vuetify": "^3.7.0-beta.1", "vuex": "^4.0.2"}, "devDependencies": {"@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@cirrus8/eslint-config": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@vitejs/plugin-vue": "^5.2.1", "cross-env": "^7.0.3", "eslint": "~8.57.0", "laravel-mix": "^6.0.49", "node-polyfill-webpack-plugin": "^4.1.0", "sass": "^1.83.1", "sass-loader": "^16.0.4", "ts-loader": "^9.5.2", "vue": "^3.5.13", "vue-loader": "^17.4.2", "vue-template-compiler": "^2.7.16", "webpack": "^5.97.1"}}