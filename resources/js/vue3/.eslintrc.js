const { resolve } = require('path');

module.exports = {
    parserOptions: {
        project: resolve(__dirname, './tsconfig.json'),
    },
    extends: [
        '@cirrus8/eslint-config',
        // See https://eslint.vuejs.org/rules/#available-rules (look for Vuejs 2 ones)
        'plugin:vue/essential',
        'plugin:vue/strongly-recommended',
        'plugin:vue/recommended',
    ],
    ignorePatterns: ['node_modules', 'dist'],
};
