<template>
    <div class="cirrus-page">
        <!-- Tabs -->
        <v-tabs
            class="cirrus-tab-theme"
            v-model="tab"
            bg-color="primary"
            show-arrows
            stacked
            height="49"
        >
            <v-tab
                size="x-small"
                value="1"
            >
                <template v-slot:prepend>
                    <v-icon size="x-small">mdi-view-dashboard</v-icon>
                </template>
                Components
            </v-tab>
            <v-tab
                size="x-small"
                value="3"
            >
                <template v-slot:prepend>
                    <v-icon size="x-small">mdi-format-columns</v-icon>
                </template>
                Forms
            </v-tab>
            <v-tab
                size="x-small"
                value="2"
            >
                <template v-slot:prepend>
                    <v-icon size="x-small">mdi-window-restore</v-icon>
                </template>
                Pop-up/Dialogs
            </v-tab>
            <v-tab
                size="x-small"
                value="4"
            >
                <template v-slot:prepend>
                    <v-icon size="x-small">mdi-palette</v-icon>
                </template>
                Colors
            </v-tab>
        </v-tabs>

        <!-- Tabs Content -->
        <v-tabs-window v-model="tab">
            <v-tabs-window-item value="1">
                <ButtonSection />
                <ToggleSection />
            </v-tabs-window-item>
            <v-tabs-window-item value="2">2</v-tabs-window-item>
            <v-tabs-window-item value="3">3</v-tabs-window-item>
            <v-tabs-window-item value="4">
                <!-- Color Palette -->
                <v-container>
                    <v-row>
                        <!-- Iterate over all theme colors -->
                        <v-col
                            v-for="(color, name) in theme_colors"
                            :key="name"
                            cols="12"
                            md="4"
                            sm="6"
                        >
                            <!-- Display each color block with its name -->
                            <div
                                class="color-block"
                                :style="{ backgroundColor: color }"
                            ></div>
                            <div class="color-name">{{ name }}</div>
                            <div class="color-code">{{ color }}</div>
                        </v-col>
                    </v-row>
                </v-container>
            </v-tabs-window-item>
        </v-tabs-window>
    </div>
</template>

<script>
import { useTheme } from 'vuetify';
import ButtonSection from '../sections/ButtonSection.vue';
import ToggleSection from '../sections/ToggleSection.vue';

export default {
    components: {
        ButtonSection, // Register ButtonSection as a local component
        ToggleSection, // Register ToggleSection as a local component
    },
    data() {
        return {
            tab: null,
            theme_colors: null, // Placeholder for theme colors
        };
    },
    mounted() {
        this.theme_colors = this.getThemeColors();
    },
    methods: {
        getThemeColors() {
            // Access Vuetify's theme colors
            const theme = useTheme();
            const allColors = theme.global.current.value.colors;

            // Filter out keys that start with "on-"
            const filteredColors = Object.fromEntries(
                Object.entries(allColors).filter(([key]) => !key.startsWith('on-')),
            );

            return filteredColors;
        },
    },
};
</script>

<style scoped>
.cirrus-page {
    padding: 16px;
}

.color-block {
    height: 100px;
    border-radius: 8px;
    margin-bottom: 8px;
}

.color-name {
    font-weight: bold;
    margin-bottom: 4px;
}

.color-code {
    font-size: 14px;
    color: gray;
}
</style>
