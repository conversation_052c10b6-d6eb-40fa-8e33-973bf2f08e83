<template>
    <div class="cirrus-section">
        <v-card
            class="section-toolbar rounded-0"
            color="titleHeader"
            variant="flat"
        >
            <v-card-actions class="d-flex align-center">
                <h4 class="title font-weight-black mb-0">Toggles</h4>
                <!-- &nbsp for spacing replaced by <PERSON>uetify's spacer or CSS margin -->
                <div class="spacer"></div>
            </v-card-actions>
        </v-card>

        <v-table class="cirrus-table">
            <thead>
                <tr>
                    <th class="text-left">Type</th>
                    <th class="text-left">UI/EX</th>
                    <th class="text-left">VAR TYPE</th>
                    <th class="text-left">Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Radio</td>
                    <td>
                        <cirrus-btn-group
                            v-model="toggle_btn"
                            :options="btn_group_options_w_value"
                        />
                    </td>
                    <td>STRING:VALUE</td>
                    <td>{{ toggle_btn }}</td>
                </tr>
                <tr>
                    <td>Radio</td>
                    <td>
                        <cirrus-btn-group
                            v-model="toggle_btn_2"
                            :options="btn_group_options_arr"
                        />
                    </td>
                    <td>INT:INDEX</td>
                    <td>{{ toggle_btn_2 }}</td>
                </tr>
            </tbody>
        </v-table>
    </div>
</template>

<script>
export default {
    props: {},
    setup() {
        // Data for button types
        const buttonTypes = [
            { name: 'Default', variant: undefined, label: 'default' },
            { name: 'Outlined', variant: 'outlined', label: 'default' },
            { name: 'Text', variant: 'text', label: 'Text' },
            { name: 'Tonal', variant: 'tonal', label: 'Tonal' },
            { name: 'Plain', variant: 'plain', label: 'Plain' },
        ];

        return {
            buttonTypes, // For table rows
        };
    },
    data() {
        return {
            toggle_btn: 'Ledger',
            toggle_btn_2: 0,
            btn_group_options_w_value: [
                { field_key: 'property', field_value: 'Property' },
                { field_key: 'lease', field_value: 'Lease' },
                { field_key: 'company', field_value: 'Company' },
                { field_key: 'Ledger', field_value: 'Ledger' },
            ],
            btn_group_options_arr: ['Property', 'Lease', 'Company', 'Ledger'],
        };
    },
};
</script>
