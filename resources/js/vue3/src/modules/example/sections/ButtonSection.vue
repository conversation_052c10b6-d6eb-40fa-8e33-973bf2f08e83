<template>
    <div class="cirrus-section">
        <v-card
            class="section-toolbar rounded-0"
            color="titleHeader"
            variant="flat"
        >
            <v-card-actions class="d-flex align-center">
                <h4 class="title font-weight-black mb-0">Buttons</h4>
                <!-- &nbsp for spacing replaced by Vuetify's spacer or CSS margin -->
                <div class="spacer"></div>
            </v-card-actions>
        </v-card>

        <v-table class="cirrus-table">
            <thead>
                <tr>
                    <th class="text-left">Button Type</th>
                    <th class="text-left">Extra-small</th>
                    <th class="text-left">Small</th>
                </tr>
            </thead>
            <tbody>
                <tr
                    v-for="(button, index) in buttonTypes"
                    :key="index"
                >
                    <td>{{ button.name }}</td>
                    <td>
                        <v-btn
                            :size="'x-small'"
                            :variant="button.variant"
                            >{{ button.label }}</v-btn
                        >
                    </td>
                    <td>
                        <v-btn
                            :size="'small'"
                            :variant="button.variant"
                            >{{ button.label }}</v-btn
                        >
                    </td>
                </tr>
            </tbody>
        </v-table>
    </div>
</template>

<script>
export default {
    props: {},
    setup() {
        // Data for button types
        const buttonTypes = [
            { name: 'Default', variant: undefined, label: 'default' },
            { name: 'Outlined', variant: 'outlined', label: 'default' },
            { name: 'Text', variant: 'text', label: 'Text' },
            { name: 'Tonal', variant: 'tonal', label: 'Tonal' },
            { name: 'Plain', variant: 'plain', label: 'Plain' },
        ];

        return {
            buttonTypes, // For table rows
        };
    },
};
</script>
