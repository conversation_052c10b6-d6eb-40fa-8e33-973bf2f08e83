<template>
    <div>
        <p>{{ msg }}</p>
        <button @click="counter++">You clicked me {{ counter }} times</button>
    </div>
</template>

<script>
import { ref } from 'vue'; // Import ref for reactive state

export default {
    props: {
        msg: {
            type: String,
            required: true,
        },
    },
    setup() {
        // Reactive state
        const counter = ref(0);

        // Return data so it can be used in the template
        return {
            counter,
        };
    },
};
</script>

<style scoped>
button {
    background-color: #42b983;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
}
button:hover {
    background-color: #2c8a6d;
}
</style>
