<template>
    <v-btn-toggle
        class="c8-btn-group small"
        v-model="innerModel"
        group
        mandatory
    >
        <v-btn
            v-for="option in parsedOptions"
            :key="option.value"
            :value="option.value"
        >
            {{ option.label }}
        </v-btn>
    </v-btn-toggle>
</template>

<script>
export default {
    name: 'CirrusButtonGroup',
    props: {
        // The toggle button options: Can be an array of strings or objects
        options: {
            type: Array,
            required: true,
            default: () => [],
        },
        // The initial value of the toggle button (v-model)
        modelValue: {
            type: [String, Number],
            required: true,
        },
    },
    data() {
        return {
            innerModel: this.modelValue, // Internal v-model state
        };
    },
    computed: {
        parsedOptions() {
            // Detect the format of the options and normalize them for rendering
            if (
                this.options.length > 0 &&
                typeof this.options[0] === 'object' &&
                'field_key' in this.options[0] &&
                'field_value' in this.options[0]
            ) {
                // Format: [{ field_key, field_value }]
                return this.options.map((option) => ({
                    value: option.field_key, // Use `field_key` as the button's value
                    label: option.field_value, // Use `field_value` as the button's label
                }));
            } else if (typeof this.options[0] === 'string') {
                // Format: ["Property", "Lease", ...]
                return this.options.map((option, index) => ({
                    value: index, // Use the index as the button's value
                    label: option, // Use the string itself as the button's label
                }));
            } else {
                console.warn('Invalid options format passed to CirrusButtonGroup');
                return []; // Fallback if options format is invalid
            }
        },
    },
    watch: {
        // Sync changes from parent (if prop is updated)
        modelValue(newValue) {
            this.innerModel = newValue;
        },
        // Emit changes to the parent
        innerModel(newValue) {
            this.$emit('update:modelValue', newValue);
        },
    },
};
</script>
