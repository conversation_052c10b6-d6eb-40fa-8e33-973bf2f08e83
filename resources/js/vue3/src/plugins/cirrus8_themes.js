// Import required modules
import { createVuetify } from 'vuetify';
import * as components from 'vuetify/components';
import * as directives from 'vuetify/directives';
import '@mdi/font/css/materialdesignicons.css'; // Material Design Icons
import 'vuetify/styles'; // Import global Vuetify styles

// Helper function for light theme colors
const defineLightThemeColors = () => ({
    cirrus_color: '#00BAF2',
    primary: '#00BAF2',
    secondary: '#00BAF2',
    titleHeader: '#3489a1',
    successHeader: '#2AB27B',
    accent: '#8c9eff',
    error: '#b71c1c',
    normal: '#E0E1E2',
    subPrimary: '#ff9f1e',
});

// Helper function for dark theme colors
const defineDarkThemeColors = () => ({
    cirrus_color: '#1E88E5', // Slightly deeper shade for contrast
    primary: '#1E88E5',
    secondary: '#42A5F5',
    titleHeader: '#1A5670',
    successHeader: '#1B5E20',
    accent: '#536DFE',
    error: '#EF5350',
    normal: '#757575',
    subPrimary: '#FFB300',
});

// Create Vuetify instance with themes
const vuetify = createVuetify({
    theme: {
        defaultTheme: 'light', // Default theme
        themes: {
            light: { colors: defineLightThemeColors() }, // Light theme colors
            dark: { colors: defineDarkThemeColors() }, // Dark theme colors
        },
    },
    icons: {
        iconfont: 'mdi', // Material Design Icons
    },
    components, // Register Vuetify components globally
    directives, // Register Vuetify directives globally
});

// Store the original console.warn function
const originalConsoleWarn = console.warn;

// Ignore specific warning messages
const ignoreWarnMessage = (msg) => {
    const ignoredMessages = ['The .native modifier for v-on is only valid on components but it was used on <div>.'];

    // Log the warnings that are not ignored
    if (!ignoredMessages.includes(msg)) {
        originalConsoleWarn(msg); // Use the original console.warn to log valid warnings
    }
};

// Override console.warn while avoiding recursion
console.warn = ignoreWarnMessage;

// Export Vuetify instance
export default vuetify;
