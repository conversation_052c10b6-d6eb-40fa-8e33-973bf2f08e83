// Import required modules
import { createApp } from 'vue';
import vuetify from '../../plugins/cirrus8_themes'; // Import the custom Vuetify theme configuration

// Import the main Vue component for the page
import Example from '../../modules/example/pages/Example.vue';

// Import custom components
import CirrusPageHeader from '../../components/CirrusPageHeader.vue';
import CirrusButtonGroup from '../../components/CirrusButtonGroup.vue';

// Create the Vue app instance
const app = createApp(Example);

// Use Vuetify (with the custom theme) in the app
app.use(vuetify);

// Register custom components globally
app.component('cirrus-page-header', CirrusPageHeader);
app.component('cirrus-btn-group', CirrusButtonGroup);

// Mount the Vue app to the DOM (ensure #app exists in your Blade template or HTML)
app.mount('#app');
