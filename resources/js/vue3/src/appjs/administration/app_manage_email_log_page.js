import { createApp } from 'vue';

// Import Vuetify (for styling and components)
import 'vuetify/styles'; // Global CSS from Vuetify
import { createVuetify } from 'vuetify';
import * as components from 'vuetify/components';
import * as directives from 'vuetify/directives';

// Import the main Vue component for the page
import ManageEmailLogPage from '../../modules/administration/pages/ManageEmailLogPage.vue';

// Create a Vuetify instance
const vuetify = createVuetify({
    components, // Enable Vuetify components globally
    directives, // Enable Vuetify directives globally
});

// Create the Vue app instance
const app = createApp(ManageEmailLogPage); // Mount the specific page component directly

// Add Vuetify to the Vue app
app.use(vuetify);

// Mount the Vue app to the DOM (ensure #app exists in the Blade template)
app.mount('#app');
