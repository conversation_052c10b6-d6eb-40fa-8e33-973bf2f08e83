@layer theme, base, components, utilities;
@import 'tailwindcss/theme.css' layer(theme);
@import 'tailwindcss/utilities.css' layer(utilities);

@import '_variables'; // Import variables (ensure correct path and file exist)

@theme {
    --color-primary: #00baf2;
    --color-secondary: #3489a1;
    --color-error: #b71c1c;
    --color-warning: #ffb812;
    --color-success: #2ab27b;
}

.cirrus-page {
    font-size: $c8-font-size; // Use a font size defined in your variables
    font-family: 'Segoe UI', Helvetica, Arial, Tahoma, sans-serif; // Set a readable default font
    padding: 0; // Reset padding
    .cirrus-tab-theme {
        background-color: #006581 !important;
        color: #ffffff !important;
        .v-tab {
            color: #ffffff !important;
            .v-icon {
                color: #ffffff !important;
            }
        }
    }
    .title {
        font-size: $c8-font-size;
    }
    // Toolbar styles
    .section-toolbar {
        &.v-theme--light {
            &.bg-titleHeader {
                color: white; // Set text color for light theme with titleHeader background
            }
        }
        .v-card-actions {
            min-height: 31px;
            padding-left: 6px;
            padding-right: 6px;
            padding-top: 3px;
            padding-bottom: 3px;
        }
    }

    // Button toggle styles
    .v-btn-toggle {
        border-radius: 0; // Remove border radius (square edges)
    }

    .cirrus-table {
        thead > tr > th {
            height: $c8-field-min-height !important;
            font-size: $c8-font-size + 1px;
            background-color: $c8-table-th-color;
            color: $c8-table-th-text-color !important;
            border-color: $c8-border-color;
            font-weight: 400 !important;
            padding: $c8-table-th-pd;
        }
        tbody > tr > td {
            height: $c8-field-min-height !important;
            font-size: $c8-font-size !important;
            padding: 5px 5px !important;
            border-color: $c8-border-color !important;
        }

        .v-select__slot .v-select__selections input {
            box-shadow: none !important;
            padding: 0;
        }
    }
}

.c8-btn-group {
    &.small {
        height: 30px;
    }
    button {
        color: #4e4e4e !important;
        border: 1px solid #d0d0d0 !important;
        background-color: #e1e1e1 !important;
        font-size: 11px !important;
        font-family: 'Segoe UI', Helvetica, Arial, Tahoma, sans-serif;
        text-transform: none !important;
        letter-spacing: 0px !important;
    }
    button.v-btn--active {
        border: 1px solid #ff9507 !important;
        background-color: #ff9507 !important;
        font-weight: normal !important;
        color: #fff !important;
    }
    button:first-child {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
    }
    button:last-child {
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
    }
}
