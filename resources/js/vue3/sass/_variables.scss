// Body
$body-bg: #f5f8fa;

// Borders
$laravel-border-color: darken($body-bg, 10%);
$list-group-border: $laravel-border-color;
$navbar-default-border: $laravel-border-color;
$panel-default-border: $laravel-border-color;
$panel-inner-border: $laravel-border-color;

// Brands
$brand-primary: #3097d1;
$brand-info: #8eb4cb;
$brand-success: #2ab27b;
$brand-warning: #cbb956;
$brand-danger: #bf5329;

// Typography
$icon-font-path: '';
$font-family-sans-serif: 'Raleway', sans-serif;
$font-size-base: 14px;
$line-height-base: 1.6;
$text-color: #636b6f;

// Navbar
$navbar-default-bg: #fff;

// Buttons
$btn-default-color: $text-color;

// Inputs
$input-border: lighten($text-color, 40%);
$input-border-focus: lighten($brand-primary, 25%);
$input-color-placeholder: lighten($text-color, 30%);

// Panels
$panel-default-heading-bg: #fff;

// CIRRUS8
$c8-font-size: 11px;
$c8-font-color: #666666;
$c8-disabled-color: #cccccc;
$c8-title: #7f8c8d;
$c8-btn-border: #d0d0d0;
$c8-btn-bg: #e2e2e2;
$c8-btn-color: #8d8d8d;

$c8-input-max-width: 300px;
$c8-field-min-height: 30px;

$pdf-color: #ff0000;
$excel-color: #1d6f42;

$c8-border-color: #ececec;
$c8-table-th-color: #eaeaea;
$c8-table-th-text-color: #888888;
$c8-table-th-bg-dark: #666666;
$c8-primary: #00baf2;
$c8-secondary: #3489a1;
$c8-error: #b71c1c;
$c8-warning: #ffb812;
$c8-success: #2ab27b;

$c8-dflt-pd: 0.5rem 1rem;
$c8-dflt-hlf: 0.25rem 0.5rem;
$c8-alert-pd: 1rem;
$c8-alert-min-width: 350px;
$c8-alert-max-width: 500px;
$c8-table-th-pd: 5px 5px;
$c8-table-td-pd: 5px 5px;
$c8-table-td-calendar-pd: 0px 10px;
