let mix = require('laravel-mix');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const webpack = require('webpack'); // Required to use Webpack plugins
const path = require('path'); // Required for resolving absolute paths
const fs = require('fs');
/*
 |--------------------------------------------------------------------------
 | Mix Asset Management for Vue 3
 |--------------------------------------------------------------------------
 */

mix.webpackConfig({
    ignoreWarnings: [/sass-loader/],
    plugins: [
        new NodePolyfillPlugin(),
        // Define Vue feature flags for better production handling
        new webpack.DefinePlugin({
            __VUE_PROD_DEVTOOLS__: false, // Disable Vue devtools in production
            __VUE_OPTIONS_API__: true, // Enable Options API (if you're using it)
            __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false, // Disable hydration mismatch info
        }),
    ],
    resolve: {
        fallback: {
            fs: false,
        },
    },
    watchOptions: {
        aggregateTimeout: 300, // Delay the rebuild after the first change (ms)
        poll: 1000, // Check for changes every second
    },
});

// Grouping all `.js` files
const vue3_js_files = [
    'src/appjs/administration/app_manage_email_log_page.js',
    'src/appjs/example/app_example_page.js',
];

// Grouping all `.scss` files
const sassFiles = ['sass/cirrus-main-style.scss'];

// Compile JavaScript files
vue3_js_files.forEach((file) => {
    mix.ts(file, '../../../public/vuejs/js').vue({ version: 3 }).setPublicPath('../../../public').version();
});

// Compile SCSS files
sassFiles.forEach((file) => {
    mix.sass(file, '../../../public/vuejs/css')
        .setPublicPath('../../../public')
        .options({
            postCss: [require('@tailwindcss/postcss')],
        })
        .version();
});

// Post-processing: Save Manifest to a Separate Location
mix.after(() => {
    const manifestPath = '../../../public/mix-manifest.json'; // Original Manifest
    const vue2ManifestPath = '../../../public/vue2-mix-manifest.json'; // Vue 2 Manifest
    const vue3ManifestPath = '../../../public/vue3-mix-manifest.json'; // Vue 3 Manifest

    // Copy default mix manifest to Vue 2 manifest file
    if (fs.existsSync(manifestPath)) {
        fs.copyFileSync(manifestPath, vue3ManifestPath);
        console.log('Vue 3 Manifest saved to:', vue3ManifestPath);
    }

    // Combine Vue 2 and Vue 3 manifests
    if (fs.existsSync(vue3ManifestPath) && fs.existsSync(vue2ManifestPath)) {
        // Read the contents of both manifest files
        const vue3Manifest = JSON.parse(fs.readFileSync(vue3ManifestPath, 'utf-8'));
        const vue2Manifest = JSON.parse(fs.readFileSync(vue2ManifestPath, 'utf-8'));

        // Merge both manifests into a single object
        const combinedManifest = { ...vue2Manifest, ...vue3Manifest };

        // Write the merged manifest back to `mix-manifest.json`
        fs.writeFileSync(manifestPath, JSON.stringify(combinedManifest, null, 4));
        console.log('Combined manifest saved to:', manifestPath);
    }
});
