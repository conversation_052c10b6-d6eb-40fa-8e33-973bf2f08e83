import BugsnagPerformance from '@bugsnag/browser-performance';
import Vue from 'vue';
import Multiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
import moment from 'moment';
import axios from 'axios';
import VueNoty from 'vuejs-noty'; // https://ned.im/noty/#/themes
import 'vuejs-noty/dist/vuejs-noty.css';
import CirrusPageHeader from '../components/elements/CirrusPageHeader.vue';
import CirrusDatePicker from '../components/elements/CirrusIconDatePicker.vue';
import CirrusSingleSelect from '../components/elements/CirrusSingleSelect.vue';

BugsnagPerformance.start({
    apiKey: process.env.MIX_CONFIG_BUGSNAG_API_KEY,
    releaseStage: process.env.MIX_APP_ENV ?? process.env.NODE_ENV ?? 'testing',
});

/*
 * COMPONENTS
 */
Vue.component('multiselect', Multiselect);
Vue.component('cirrus-page-header', CirrusPageHeader);
Vue.component('cirrus-date-picker', CirrusDatePicker);
Vue.component('cirrus-single-select', CirrusSingleSelect);
/*
 * MIXINS
 */
// ALERT MIXIN
Vue.use(VueNoty, {
    closeWith: ['button', 'click'],
    timeout: 6000,
    theme: 'mint',
    layout: 'topRight',
    progressBar: true,
});

var countryDefaults = { currencySymbol: '' };

var api_url = localStorage.getItem('cirrus8_api_url') + 'admin/country_defaults/load';
var form_data = new FormData();

axios.defaults.headers.common['Content-Type'] = 'application/x-www-form-urlencoded';
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Authorization'] = 'Bearer ' + localStorage.getItem('Authentication');
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

form_data.append('user_type', localStorage.getItem('user_type'));
if (sessionStorage.getItem('sso_key')) form_data.append('app_key', sessionStorage.getItem('sso_key'));
form_data.append('fetch', 'currency_symbol');

if (countryDefaults.currencySymbol == '') {
    axios.post(api_url, form_data).then((response) => {
        if (response.data) {
            countryDefaults.currencySymbol = response.data.currency_symbol;
        } else {
            countryDefaults.currencySymbol = '$';
        }
    });
}

const globalMixins = {
    methods: {
        // BUILDS THE REQUESTS INTO FORM DATA
        req: (obj) => {
            let formData = new FormData();
            if (obj) {
                for (var key in obj) {
                    formData.append(key, obj[key]);
                }
            }
            return formData;
        },
        sort_by: function () {
            var fields = [].slice.call(arguments),
                n_fields = fields.length;
            return function (A, B) {
                var a, b, field, key, primer, reverse, result, format;
                for (var i = 0, l = n_fields; i < l; i++) {
                    result = 0;
                    field = fields[i];

                    key = typeof field === 'string' ? field : field.name;

                    a = A[key];
                    b = B[key];

                    if (typeof field.primer !== 'undefined') {
                        if (field.primer == 'date') {
                            a = moment(a, field.format).valueOf();
                            b = moment(b, field.format).valueOf();
                        } else {
                            a = field.primer(a);
                            b = field.primer(b);
                            if (field.primer == parseFloat) {
                                a = field.primer(a);
                                if (isNaN(a)) {
                                    a = Number.MIN_SAFE_INTEGER;
                                }
                                b = field.primer(b);
                                if (isNaN(b)) {
                                    b = Number.MIN_SAFE_INTEGER;
                                }
                            }
                        }
                    }
                    reverse = field.reverse ? -1 : 1;
                    if (a < b) result = reverse * -1;
                    if (a > b) result = reverse * 1;
                    if (result !== 0) break;
                }
                return result;
            };
        },
        isPropertyFormLive: function () {
            if (
                this.page_form_type === 'client-summary-page' ||
                this.page_form_type === 'admin-manage-page' ||
                this.page_form_type === 'admin-property-print-page'
            ) {
                return true;
            } else {
                return false;
            }
        },
        isLeaseFormLive: function () {
            if (
                this.page_form_type === 'client-summary-page' ||
                this.page_form_type === 'admin-manage-page' ||
                this.page_form_type === 'admin-lease-print-page'
            ) {
                return true;
            } else {
                return false;
            }
        },
        isPropertyInPrintView: function () {
            if (this.page_form_type === 'admin-property-print-page') {
                return true;
            } else {
                return false;
            }
        },
        isLeaseInPrintView: function () {
            if (this.page_form_type === 'admin-lease-print-page') {
                return true;
            } else {
                return false;
            }
        },
        dateToday: function () {
            let today = new Date();
            let dd = today.getDate();
            let mm = today.getMonth() + 1; //January is 0!
            let yyyy = today.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            today = dd + '/' + mm + '/' + yyyy;
            return new Date().toISOString().substr(0, 10);
        },
        matchRegex: function (row, by, word) {
            let regex = new RegExp(word.replace(/\\/g, ''), 'i');
            let found = false;
            for (var i = 0; i < by.length; i++) {
                if (row[by[i]] && row[by[i]].toString().search(regex) != -1) {
                    found = true;
                }
                if (found) break;
            }
            if (word == '') found = true;
            return found;
        },
        printDownload(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;

            let blob = new Blob([this.printS2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            a.download = name + '.' + format;
            a.click();
        },
        printS2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
        fileDL(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;

            let blob = new Blob([this.fileDLS2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            if (format) a.download = name + '.' + format;
            else a.download = name;
            a.click();
        },
        fileDLS2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
        escapeParamValue(code, customCharacter = '`') {
            //For escaping parameter values when passsing through AJAX requests
            //To avoid converting strings to float esp company, ledger, subledger, property, and lease codes
            return customCharacter + code + customCharacter;
        },
        convertToArrayOfObjects(data) {
            let count = Object.keys(data).length;
            let result = [];

            for (let key = 0; key <= count; key++) {
                if (data[key] != null) {
                    result.push(data[key]);
                }
            }

            return result;
        },
        updatePageTitle(title) {
            document.title = title + ' - Cirrus8: Online Commercial Property Management Solution';
        },
        decodeHTMLEntity(text) {
            if (text && typeof text === 'string') {
                var decodedString = document.createElement('div');
                decodedString.innerHTML = text;
                var result = decodedString.innerHTML;
                return result;
            }
            return text;
        },
        ucwords(text) {
            if (text) {
                text = text.toLowerCase().replace(/\b[a-z]/g, function (letter) {
                    return letter.toUpperCase();
                });
            }

            return text;
        },
        prepareDate(dateVal) {
            //DD/MM/YYYY - for generating a valid date (ex. 29-02-2025 is invalid, therefore will be converted to 01-03-2025)
            let convertedDate = '';

            if (dateVal && dateVal.search('/') != -1) {
                let dateArray = dateVal.split('/');
                let newDate = `${dateArray[2]}/${dateArray[1]}/${dateArray[0]}`;
                let generatedDate = new Date(newDate);
                convertedDate = moment(generatedDate).format('DD/MM/YYYY');
            }

            return convertedDate;
        },
        isObjEmpty(objectName) {
            return Object.keys(objectName).length === 0;
        },
    },
};
Vue.mixin(globalMixins);
/*
 * FILTERS
 */
Vue.filter('sql2Date', function (sqlDate) {
    if (sqlDate) return moment(sqlDate, 'YYYY-MM-DD').format('DD/MM/YYYY');
    else return '';
});
Vue.filter('sql2DateTime', function (sqlDate) {
    if (sqlDate) return moment(sqlDate, 'YYYY-MM-DD').format('DD/MM/YYYY hh:mm A');
    else return '';
});
Vue.filter('date2Sql', function (sqlDate) {
    if (sqlDate) return moment(sqlDate, 'DD/MM/YYYY').format('YYYY-MM-DD');
    else return '';
});
Vue.filter('strtoupper', function (text) {
    if (text) return text.toUpperCase();
    else return text;
});
Vue.filter('strtolower', function (text) {
    if (text) return text.toLowerCase();
    else return text;
});
Vue.filter('strtruncate', function (text, max_length) {
    let len = 65;
    if (max_length) len = max_length;
    if (text && text.length > len) return text.substring(0, len) + '...';
    else return text;
});
Vue.filter('strfine', function (text, max_length) {
    let len = 65;
    if (max_length) len = max_length;
    if (text && text.length > len) return false;
    else return true;
});
Vue.filter('ucfirst', function (text) {
    if (text) {
        text = text.toLowerCase();
        return text.charAt(0).toUpperCase() + text.slice(1);
    } else return '';
});
Vue.filter('yesNo', function (thisBool) {
    if (typeof thisBool === 'string') thisBool = !!+thisBool;

    if (thisBool) return 'Yes';
    else return 'No';
});
Vue.filter('num2Amt', function (number, decimalPoint) {
    let num = parseFloat(number);
    let decimal = 2;

    if (decimalPoint) decimal = decimalPoint;

    if (!isNaN(num)) {
        if (num < 0) {
            num *= -1;
            num = num.toFixed(decimal).toString();
            // num = '($'+num+')';
            num = '(' + countryDefaults.currencySymbol + num.replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ')';
        } else {
            num = num.toFixed(decimal).toString();
            // num = '$'+num;
            num = countryDefaults.currencySymbol + num.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
    } else {
        // num = '$'+((0).toFixed(2).toString());
        num = countryDefaults.currencySymbol + (0).toFixed(decimal).toString();
    }

    return num;
});
Vue.filter('numWCurrency', function (number) {
    return countryDefaults.currencySymbol + number;
});
Vue.filter('num2AmtStr', function (number) {
    var num = parseFloat(number);
    if (!isNaN(num)) {
        if (num < 0) {
            num *= -1;
            num = num.toFixed(2).toString();
            num = '(' + num + ')';
        } else {
            num = num.toFixed(2).toString();
            num = '' + num;
        }
    } else num = '';
    return num.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
});
Vue.filter('num2AmtInt', function (number) {
    var num = parseFloat(number);
    if (!isNaN(num)) {
        if (num < 0) {
            num *= -1;
            num = num.toFixed(2);
        } else {
            num = num.toFixed(2);
        }
    } else num = null;
    return num;
});
Vue.filter('numSuffix', function (n) {
    if (!isNaN(n)) {
        var s = ['th', 'st', 'nd', 'rd'],
            v = n % 100;
        return n + (s[(v - 20) % 10] || s[v] || s[0]);
    } else return '';
});
Vue.filter('num2Days', function (days) {
    let days_of_week = [
        { label: 'Monday', value: 1 },
        { label: 'Tuesday', value: 2 },
        { label: 'Wednesday', value: 3 },
        { label: 'Thursday', value: 4 },
        { label: 'Friday', value: 5 },
        { label: 'Saturday', value: 6 },
        { label: 'Sunday', value: 0 },
    ];
    let str = '';
    if (days instanceof Array) {
        let d = days_of_week.filter((row) => {
            return days.indexOf(row.value) !== -1;
        });
        if (d && d.length > 0) {
            for (var i = 0; i < d.length; i++) {
                str += d[i].label + ', ';
            }
        }
        return str.substring(0, str.length - 2);
    } else {
        let d = days_of_week.filter((row) => {
            return row.value == days;
        });
        if (d && d.length > 0) str = d[0].label;
    }
    return str;
});
Vue.filter('num2Months', function (days) {
    let months = [
        { label: 'January', value: 1 },
        { label: 'February', value: 2 },
        { label: 'March', value: 3 },
        { label: 'April', value: 4 },
        { label: 'May', value: 5 },
        { label: 'June', value: 6 },
        { label: 'July', value: 7 },
        { label: 'August', value: 8 },
        { label: 'September', value: 9 },
        { label: 'October', value: 10 },
        { label: 'November', value: 11 },
        { label: 'December', value: 12 },
    ];
    let str = '';
    if (days instanceof Array) {
        let d = months.filter((row) => {
            return days.indexOf(row.value) !== -1;
        });
        if (d && d.length > 0) {
            for (var i = 0; i < d.length; i++) {
                str += d[i].label + ', ';
            }
        }
        return str.substring(0, str.length - 2);
    } else {
        let d = months.filter((row) => {
            return row.value == parseInt(days);
        });
        if (d && d.length > 0) str = d[0].label;
    }
    return str;
});
Vue.filter('highlight', function (words, query) {
    var iQuery = new RegExp(query, 'ig');
    return words.toString().replace(iQuery, function (matchedTxt, a, b) {
        return "<span class='highlight'>" + matchedTxt + '</span>';
    });
});
Vue.filter('appendSymbol', function (label) {
    return label + ' (' + countryDefaults.currencySymbol + ')';
});
Vue.prototype.$filters = Vue.options.filters;

/*
 * Variables for the image paths
 */
Vue.prototype.$folderSysOn = eval(process.env.MIX_CONFIG_FOLDERING_SYSTEM_ON);
Vue.prototype.$assetDomain = process.env.MIX_CONFIG_ASSET_DOMAIN;
Vue.prototype.$uploadedAssetDomain = process.env.MIX_CONFIG_UPLOADED_ASSET_DOMAIN;
Vue.prototype.$tinymceLicenseCode = process.env.MIX_CONFIG_TINYMCE_LICENSE_CODE;
Vue.prototype.$openAILicenseCode = process.env.MIX_CONFIG_OPENAI_LICENSE_CODE;
