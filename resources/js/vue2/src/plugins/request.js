import Vue from 'vue';
import axios from 'axios';
import { bus } from './bus';

let loader_component = document.getElementById('ngLoader-UI');
// CREATE AXIOS
let noty = Vue.prototype.$noty;
let base_url = localStorage.getItem('cirrus8_api_url');
axios.defaults.headers.common['Content-Type'] = 'application/x-www-form-urlencoded';
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Authorization'] = 'Bearer ' + localStorage.getItem('Authentication');
let api = axios.create({ baseURL: base_url + 'api/' });
api.interceptors.request.use(
    (request) => {
        localStorage.setItem('error_code', '');
        if (!request.data) {
            request.data = new FormData();
            request.data.append('user_type', localStorage.getItem('user_type'));
            if (sessionStorage.getItem('sso_key')) request.data.append('app_key', sessionStorage.getItem('sso_key'));
            for (let form_data of request.data.entries())
                request.data.set(form_data[0], cleanAPIRequestData(form_data[1], form_data[0]));

            if (loader_component) loader_component.style.display = 'block';
        } else {
            if (request.data instanceof FormData) {
                request.data.append('user_type', localStorage.getItem('user_type'));
                if (sessionStorage.getItem('sso_key'))
                    request.data.append('app_key', sessionStorage.getItem('sso_key'));
                for (let form_data of request.data.entries())
                    request.data.set(form_data[0], cleanAPIRequestData(form_data[1], form_data[0]));
                if (!request.data.get('no_load')) if (loader_component) loader_component.style.display = 'block';
            } else {
                request.headers['Content-Type'] = 'application/json';
                request.data.user_type = localStorage.getItem('user_type');
                if (sessionStorage.getItem('sso_key')) request.data.app_key = sessionStorage.getItem('sso_key');
                if (!request.data.no_load) if (loader_component) loader_component.style.display = 'block';
            }
        }
        return request;
    },
    (error) => {
        if (loader_component) loader_component.style.display = 'none';
        return Promise.reject(error);
    },
);
api.interceptors.response.use(
    (response) => {
        try {
            response.data = JSON.parse(window.atob(response.data));
        } catch (e) {}
        if (response.data.error) noty.error(response.data.error);
        if (response.data.success) noty.success(response.data.success);
        if (response.data.warning) noty.warning(response.data.warning);
        if (loader_component) loader_component.style.display = 'none';
        return response;
    },
    (error) => {
        if (error.response.hasOwnProperty('status')) {
            let status = error.response.status;
            if (status && (status === 440 || status === 401)) {
                loader_component.style.display = 'block';
                let elem_append = loader_component.querySelector('.loader-ui');
                elem_append.innerHTML = '';
                elem_append.insertAdjacentHTML(
                    'afterbegin',
                    '<div class="desc" style="width:315px;text-align:center;font-size:14px;margin:10px auto;">' +
                        '<span class="title">Your session has expired and you are being logged out. Please login again to keep using Cirrus8.</span>' +
                        '</div>',
                );
                setTimeout(function () {
                    window.location = base_url + 'sso/session/destroy?app=cirrus8';
                }, 3000);
            } else if (status && status === 413) noty.error('File(s) are too large to upload');

            if (loader_component) loader_component.style.display = 'none';
        }
        // let elem_append = loader_component.querySelector(".loader-ui")
        if (error.response.hasOwnProperty('error_code')) {
            let error_code = error.response.data.error_code;
            bus.$emit('setErrorCode', error_code);
            if (error.response.data.error_code) {
                let message =
                    'Please send the error code: ' + error_code + ' to cirrus8 <NAME_EMAIL>';
                noty.error(message);
            }
        }
        return Promise.reject(error);
    },
);
Vue.prototype.$api = api;
export const requestFromApi = api;

let admin = axios.create({ baseURL: base_url + 'admin/' });
admin.interceptors.request.use(
    (request) => {
        localStorage.setItem('error_code', '');
        if (!request.data) {
            request.data = new FormData();
            request.data.append('user_type', localStorage.getItem('user_type'));
            if (sessionStorage.getItem('sso_key')) request.data.append('app_key', sessionStorage.getItem('sso_key'));
            for (let form_data of request.data.entries())
                request.data.set(form_data[0], cleanAPIRequestData(form_data[1], form_data[0]));
        } else {
            if (request.data instanceof FormData) {
                request.data.append('user_type', localStorage.getItem('user_type'));
                if (sessionStorage.getItem('sso_key'))
                    request.data.append('app_key', sessionStorage.getItem('sso_key'));
                for (let form_data of request.data.entries())
                    request.data.set(form_data[0], cleanAPIRequestData(form_data[1], form_data[0]));
                if (!request.data.get('no_load')) if (loader_component) loader_component.style.display = 'block';
            } else {
                request.headers['Content-Type'] = 'application/json';
                request.data.user_type = localStorage.getItem('user_type');
                if (sessionStorage.getItem('sso_key')) request.data.app_key = sessionStorage.getItem('sso_key');
                if (!request.data.no_load) if (loader_component) loader_component.style.display = 'block';
            }
        }
        return request;
    },
    (error) => {
        if (loader_component) loader_component.style.display = 'none';
        return Promise.reject(error);
    },
);
admin.interceptors.response.use(
    (response) => {
        try {
            response.data = JSON.parse(window.atob(response.data));
        } catch (e) {}

        if (response.data.error) {
            noty.error(response.data.error);
        }
        if (response.data.success) {
            noty.success(response.data.success);
        }
        if (response.data.warning) {
            noty.warning(response.data.warning);
        }
        if (loader_component) loader_component.style.display = 'none';
        return response;
    },
    (error) => {
        if (error.response.hasOwnProperty('status')) {
            let status = error.response.status;
            if (status && (status === 440 || status === 401)) {
                loader_component.style.display = 'block';
                let elem_append = loader_component.querySelector('.loader-ui');
                elem_append.innerHTML = '';
                elem_append.insertAdjacentHTML(
                    'afterbegin',
                    '<div class="desc" style="width:315px;text-align:center;font-size:14px;margin:10px auto;">' +
                        '<span class="title">Your session has expired and you are being logged out. Please login again to keep using Cirrus8.</span>' +
                        '</div>',
                );
                setTimeout(function () {
                    window.location = base_url + 'sso/session/destroy?app=cirrus8';
                }, 3000);
            } else if (loader_component) loader_component.style.display = 'none';
        }
        // let elem_append = loader_component.querySelector(".loader-ui")
        if (error.response.hasOwnProperty('error_code')) {
            let error_code = error.response.data.error_code;
            bus.$emit('setErrorCode', error_code);
            if (error.response.data.error_code) {
                let message =
                    'Please send the error code: ' + error_code + ' to cirrus8 <NAME_EMAIL>';
                noty.error(message);
            }
        }
        return Promise.reject(error);
    },
);
Vue.prototype.$admin = admin;

let fm = axios.create({ baseURL: base_url + 'fm/' });
fm.interceptors.request.use(
    (request) => {
        if (!request.data) {
            request.data = new FormData();
            request.data.append('user_type', localStorage.getItem('user_type'));
            request.data.append('app_role', 'Admin');
            if (sessionStorage.getItem('sso_key')) request.data.append('app_key', sessionStorage.getItem('sso_key'));
            if (sessionStorage.getItem('user_name')) request.data.append('user', sessionStorage.getItem('user_name'));
            for (let form_data of request.data.entries())
                request.data.set(form_data[0], cleanAPIRequestData(form_data[1], form_data[0]));
        } else {
            if (request.data instanceof FormData) {
                request.data.append('user_type', localStorage.getItem('user_type'));
                request.data.append('app_role', 'Admin');
                if (sessionStorage.getItem('sso_key'))
                    request.data.append('app_key', sessionStorage.getItem('sso_key'));
                if (sessionStorage.getItem('user_name'))
                    request.data.append('user', sessionStorage.getItem('user_name'));
                for (let form_data of request.data.entries())
                    request.data.set(form_data[0], cleanAPIRequestData(form_data[1], form_data[0]));
                if (!request.data.get('no_load')) if (loader_component) loader_component.style.display = 'block';
            } else {
                request.headers['Content-Type'] = 'application/json';
                request.data.user_type = localStorage.getItem('user_type');
                request.data.append('app_role', 'Admin');
                if (sessionStorage.getItem('sso_key')) request.data.app_key = sessionStorage.getItem('sso_key');
                if (sessionStorage.getItem('user_name'))
                    request.data.append('user', sessionStorage.getItem('user_name'));
                if (!request.data.no_load) if (loader_component) loader_component.style.display = 'block';
            }
        }
        return request;
    },
    (error) => {
        if (loader_component) loader_component.style.display = 'none';
        return Promise.reject(error);
    },
);
fm.interceptors.response.use(
    (response) => {
        try {
            response.data = JSON.parse(window.atob(response.data));
        } catch (e) {}

        if (response.data.error) {
            noty.error(response.data.error);
        }
        if (response.data.success) {
            noty.success(response.data.success);
        }
        if (response.data.warning) {
            noty.warning(response.data.warning);
        }
        if (loader_component) loader_component.style.display = 'none';
        return response;
    },
    (error) => {
        if (loader_component) loader_component.style.display = 'none';
        return Promise.reject(error);
    },
);
Vue.prototype.$fm = fm;

Vue.prototype.$base_url = base_url;

function cleanAPIRequestData(value, key = '') {
    if (key != '') {
        value = escapeReqParamValue(value, key);
    }

    if (value === null) return '';
    if (value === 'null') return '';
    if (typeof value === 'string') {
        //return value.replace(new RegExp("\\bnull\\b"), '""');
        if (isJSONObject(value)) {
            value = JSON.stringify(swapNullValue(JSON.parse(value)));
        }
    }
    return value;
}

function isJSONObject(str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
}

function nullTest(val) {
    return Object.keys(val).reduce(
        (acc, key) => ((acc[key] = val[key] === Object(val[key]) ? nullTest(val[key]) : null), acc),
        {},
    );
}

function swapNullValue(obj) {
    let obj_temp = obj;
    if (Array.isArray(obj_temp)) {
        Object.keys(obj_temp).forEach((key) => {
            if (!obj_temp[key]) obj_temp[key] = '';
            Object.keys(obj_temp[key]).forEach((key2) => {
                if (!obj_temp[key][key2]) obj_temp[key][key2] = '';
                if (typeof obj_temp[key][key2] === 'object') {
                    Object.keys(obj_temp[key][key2]).forEach((key3) => {
                        if (!obj_temp[key][key2][key3]) obj_temp[key][key2][key3] = '';
                    });
                }
            });
        });
    } else {
        Object.keys(obj_temp).forEach((key) => {
            if (!obj_temp[key]) obj_temp[key] = '';
        });
    }
    return obj_temp;
}

function escapeReqParamValue(value, key, customCharacter = '`') {
    //For escaping parameter values when passsing through AJAX requests
    //To avoid converting strings to float esp company, ledger, subledger, property, and lease codes
    switch (key) {
        case 'company_code':
        case 'company_code_old':
        case 'companyCode':
        case 'company_id':
        case 'companyID':
        case 'old_company_code':
        case 'new_company_code':
        case 'reassign_company_code':
        case 'property_owner_code':
        case 'man_principal_owner':
        case 'company_name':
        case 'lease_tenant_name':
        case 'property_owner_name':
        case 'property_name':
        case 'lease_name':
        case 'sub_ledger_name':
        case 'new_lease_code':
        case 'new_property_code':
        case 'lease_code':
        case 'property_code':
        case 'sub_ledger_code':
        case 'owner_code':
        case 'supplier_code':
        case 'supplier_id':
        case 'supplier':
            return customCharacter + value + customCharacter;
            break;
        default:
            return value;
            break;
    }
}
