import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import Vue from 'vue';
import vuetify from './vuetify';
import CirrusPopUpComponent from '../components/elements/CirrusPopUpComponent.vue';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';

export default {
    methods: {
        formSectionReadOnly: function (form_read_only_array, form_type, form_section, is_inactive = 0) {
            if (is_inactive == 1) return true;
            if (Array.isArray(form_read_only_array)) {
                let get_filtered = form_read_only_array.filter(
                    (m) => m.form_type === form_type && m.form_section === form_section,
                );
                if (get_filtered.length <= 0) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return false;
            }
        },
        divisorClean: function (paramValue) {
            if (eval(paramValue) === 0) {
                return 1;
            }
            return paramValue;
        },
        nameWithDash({ field_key, field_value }) {
            if (`${field_key}` === '') return `${field_value}`;
            return `${field_key} — ${field_value}`;
        },
        getValueInList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.field_key === param1);
                if (filtered.length > 0) {
                    return filtered[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        fieldKey: '',
                        fieldValue: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        getValueFromList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.field_key === param1);
                if (filtered.length > 0) {
                    return filtered[0].field_value;
                } else {
                    return param1;
                }
            }
            return param1;
        },
        returnValueFromList: function (param1, paramList) {
            let filtered = (paramList ?? []).find((m) => m.field_key === param1);
            return filtered?.field_value ?? null;
        },
        returnValueInGroupList: function (param1, paramList) {
            return (paramList ?? []).flatMap((m) => m.fieldGroupValues).find((n) => n.fieldKey === param1) ?? {};
        },
        roundTo: function (n, digits) {
            if (digits === undefined) {
                digits = 0;
            }

            let multiplicator = Math.pow(10, digits);
            n = parseFloat((n * multiplicator).toFixed(11));
            let test = Math.round(n) / multiplicator;
            return test.toFixed(digits);
        },
        decimalPlace: function (n, digits) {
            if (digits === undefined) {
                digits = 0;
            }

            return (parseInt(n * 100) / 100).toFixed(digits);
        },
        numberWithCommas: function (n) {
            if (n !== undefined || n) {
                if (Number.isInteger(n)) {
                    n = n + '.00';
                } else {
                    n = n.toString();
                }
                let parts = n.split('.');
                return parts[0].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + (parts[1] ? '.' + parts[1] : '');
            }
            return '';
        },
        accountingAmountFormat: function (amount) {
            if (eval(amount.toString().replace(/,/g, '')) < 0) {
                let remove_nega = eval(amount.toString().replace(/,/g, '')) * -1;
                remove_nega = this.numberWithCommas(this.roundTo(remove_nega, 2));
                return '(' + remove_nega + ')';
            } else {
                return amount;
            }
        },
        isTA: function () {
            if (localStorage.getItem('user_type') === 'A') {
                return true;
            } else {
                return false;
            }
        },
        validateInputNumber: function (event) {
            // let charKeyCode = e.which || e.keyCode;
            // evt = (event) ? event.keyCode : window.event.keyCode;

            event = event ? event : window.event;
            let charCode = event.which ? event.which : event.keyCode;
            //(charCode);

            if (
                $.inArray(charCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
                // Allow: Ctrl/cmd+A
                (charCode == 65 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: Ctrl/cmd+C
                (charCode == 67 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: Ctrl/cmd+X
                (charCode == 88 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: + - ( ) * /
                charCode == 43 ||
                charCode == 45 ||
                charCode == 40 ||
                charCode == 41 ||
                charCode == 47 ||
                charCode == 42 ||
                charCode == 99 ||
                charCode == 118 ||
                // Allow: home, end, left, right
                (charCode >= 38 && charCode <= 39)
            ) {
                // let it happen, don't do anything
                if (charCode == 13) {
                    // //(compo);
                    // compo = eval(compo);
                    // this.budgetExpensesArr[compo].value = eval(this.budgetExpensesArr[compo].value);
                    // $(compo).val(eval(e.target.defaultValue));
                    event.target.value = this.roundTo(eval(event.target.value), 2);
                }
                return;
            }

            if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
                event.preventDefault();
            }
        },
        objectClone: function (object) {
            return JSON.parse(JSON.stringify(object));
        },
        validateEmail: function (email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        padLeftWithZero(n, width, z) {
            z = z || '0';
            n = n + '';
            return n.length >= width ? n : new Array(width - n.length + 1).join(z) + n;
        },
        goToCompanyShortcut: function (company_code, property_code, lease_code) {
            let parameter = '';
            if (property_code) parameter = parameter + '&propertyID=' + property_code;
            if (lease_code) parameter = parameter + '&lease_code=' + lease_code;

            if (localStorage.getItem('user_type') === 'A') {
                window.open(
                    '?module=companies&command=company_v2&companyID=' + company_code + parameter,
                    '_blank', // <- This is what makes it open in a new window.
                );
            } else {
                window.open(
                    '?module=companies&command=company_v2&companyID=' + company_code + parameter,
                    '_blank', // <- This is what makes it open in a new window.
                );
            }
        },
        goToLeaseShortcut: function (property_code, lease_code) {
            if (localStorage.getItem('user_type') === 'A') {
                window.open(
                    '?module=leases&command=lease_page_v2&property_code=' + property_code + '&lease_code=' + lease_code,
                    '_blank', // <- This is what makes it open in a new window.
                );
            } else {
                window.open(
                    '?module=leases&command=lease_summary_page&property_code=' +
                        property_code +
                        '&lease_code=' +
                        lease_code,
                    '_blank', // <- This is what makes it open in a new window.
                );
            }
        },
        linkLocationFormat: function (type, value) {
            let link = value;
            if (link !== '') {
                switch (type) {
                    case 'email':
                        link = "<a href='mailto:" + value + "'>" + value + '</a>';
                        break;
                    case 'mobile':
                        link = "<a href='tel:" + value + "'>" + value + '</a>';
                        break;
                }
            }
            return link;
        },
        getDropdownName(code, options) {
            let label = code ? code : '';
            options.forEach((row) => {
                if (row.field_key) if (row.field_key.trim() === label.trim()) label = row.field_value;
            });
            return label;
        },
        getDateToday: function () {
            let d = new Date();
            let date_today = d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear();
            return date_today;
        },
        findObjectByProperty: function (object_property, object_value, arr_list, object_default) {
            for (let i = 0; i < arr_list.length; i++) {
                if (arr_list[i][object_property] == object_value) {
                    return arr_list[i];
                }
            }
            return object_default;
        },
        properCase(value) {
            if (!value) return '';
            return value.replace(/\w\S*/g, (txt) => {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            });
        },
        ensureParameterIsAnArray(param) {
            if (param === undefined || param === null) {
                // If param is undefined or null, return an empty array
                return [];
            } else if (Array.isArray(param)) {
                // If param is already an array, return it as-is
                return param;
            } else if (typeof param === 'object') {
                // Convert object to array of values
                return Object.values(param);
            } else {
                // Otherwise, wrap the value in an array
                return [param];
            }
        },
        parseDate(dateString) {
            dayjs.extend(customParseFormat);
            return dayjs(dateString, 'DD/MM/YYYY').toDate();
        },
        withFieldProps(items, opts = {}) {
            const {
                keyFrom = 'code',
                valueFrom = 'description',
                keyName = 'field_key',
                valueName = 'field_value',
            } = opts;

            const getKey = typeof keyFrom === 'function' ? keyFrom : (o) => o?.[keyFrom];
            const getVal = typeof valueFrom === 'function' ? valueFrom : (o) => o?.[valueFrom];

            return items.map((item) => ({
                ...item,
                [keyName]: getKey(item) ?? '',
                [valueName]: getVal(item) ?? '',
            }));
        },
        nameWithCodeDash({ code, field_value }) {
            if (`${code}` === '') return `${field_value}`;
            return `${code} — ${field_value}`;
        },
        getFromList(param1, paramList, prop) {
            if (isNil(param1) || !Array.isArray(paramList)) return '';
            const item = paramList.find((m) => m.field_key === param1);
            return item ? item[prop] : '';
        },
        displayCodeAndLabel(key, list) {
            let code = this.getFromList(key, list, 'code');
            let label = this.getFromList(key, list, 'field_value');
            if (isEmpty(label)) return code;
            return code + ' - ' + label;
        },
    },
};

export function objectClone(object) {
    return JSON.parse(JSON.stringify(object));
}

export function cirrusDialog(prop) {
    return new Promise((resolve) => {
        const Wrapper = Vue.extend(CirrusPopUpComponent);
        const dialog = new Wrapper({
            propsData: prop,
            vuetify,
        }).$mount();
        document.body.appendChild(dialog.$el);

        dialog.$on('close', function (value) {
            dialog.$destroy();
            dialog.$el.remove();
            resolve(value);
        });
    });
}
