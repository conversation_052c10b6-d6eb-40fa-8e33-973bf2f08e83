function sortByParam(data, param, order = 'asc') {
    // Check if the parameter is valid
    if (
        !data ||
        !param ||
        ![
            'name',
            'id',
            'sortOrder',
            'folderArchived',
            'documentDescription',
            'documentPeriodDateRaw',
            'documentPeriodFromRaw',
        ].includes(param)
    ) {
        throw new Error('Invalid data or parameter. Valid parameters: name, id, sort_order, folder_archived');
    }

    // Validate order parameter
    if (!['asc', 'desc'].includes(order.toLowerCase())) {
        throw new Error('Invalid order. Valid options: asc, desc');
    }

    // Sort the data using a recursive function
    function sortNested(arr) {
        return arr.sort((a, b) => {
            // Compare values based on the parameter
            const aValue = typeof a[param] === 'string' ? a[param].toLowerCase() : a[param];
            const bValue = typeof b[param] === 'string' ? b[param].toLowerCase() : b[param];

            if (aValue < bValue) return order === 'asc' ? -1 : 1;
            if (aValue > bValue) return order === 'asc' ? 1 : -1;
            return 0;
        });
    }

    // Apply sorting to top level and recursively to children
    sortNested(data);
    data.forEach((item) => sortNested(item.children === undefined ? [] : item.children));

    return data;
}

export default sortByParam;
