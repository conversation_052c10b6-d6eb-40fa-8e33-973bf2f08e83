// import '@mdi/font/css/materialdesignicons.css' // Ensure you are using css-loader
import Vue from 'vue';
import Vuetify from 'vuetify';

const vuetifyOptions = {
    cirrus_color: '#00BAF2',
    primary: '#00BAF2',
    secondary: '#00BAF2',
    titleHeader: '#3489a1',
    successHeader: '#2AB27B',
    accent: '#8c9eff',
    error: '#b71c1c',
    normal: '#E0E1E2',
    subPrimary: '#ff9f1e',
    theme: {
        cirrus_color: '#00BAF2',
        primary: '#00BAF2',
        secondary: '#00BAF2',
        titleHeader: '#3489a1',
        successHeader: '#2AB27B',
        accent: '#8c9eff',
        error: '#b71c1c',
        normal: '#E0E1E2',
        subPrimary: '#ff9f1e',
        themes: {
            light: {
                cirrus_color: '#00BAF2',
                primary: '#00BAF2',
                secondary: '#00BAF2',
                titleHeader: '#3489a1',
                successHeader: '#2AB27B',
                accent: '#8c9eff',
                error: '#b71c1c',
                normal: '#E0E1E2',
                subPrimary: '#ff9f1e',
            },
            dark: {
                cirrus_color: '#00BAF2',
                primary: '#00BAF2',
                secondary: '#00BAF2',
                titleHeader: '#3489a1',
                successHeader: '#2AB27B',
                accent: '#8c9eff',
                error: '#b71c1c',
                normal: '#E0E1E2',
                subPrimary: '#ff9f1e',
            },
        },
    },
    icons: {
        iconfont: 'mdi', // default - only for display purposes
    },
};

Vue.use(Vuetify);
const ignoreWarnMessage = 'The .native modifier for v-on is only valid on components but it was used on <div>.';
Vue.config.warnHandler = function (msg, vm, trace) {
    // `trace` is the component hierarchy trace
    if (msg === ignoreWarnMessage) {
        msg = null;
        vm = null;
        trace = null;
    }
};
export default new Vuetify(vuetifyOptions);
