export default function imageUploadHandler(url) {
    return (blobInfo, progress) =>
        new Promise((resolve, reject) => {
            const formData = new FormData();
            const token = 'Bearer ' + localStorage.getItem('Authentication');
            formData.append('file', blobInfo.blob(), blobInfo.filename());
            formData.append('app_key', sessionStorage.getItem('sso_key'));
            formData.append('user_type', localStorage.getItem('user_type'));

            fetch(localStorage.getItem('cirrus8_api_url') + url, {
                method: 'POST',
                body: formData,
                headers: {
                    Authorization: token,
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    resolve(data.location);
                })
                .catch((error) => {
                    reject('Image upload failed: ' + error.message);
                });
        });
}
