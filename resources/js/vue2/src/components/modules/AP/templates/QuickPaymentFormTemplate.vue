<style>
.payment-list-header {
    box-shadow:
        0 3px 1px -2px rgb(0 0 0 / 20%),
        0 2px 2px 0 rgb(0 0 0 / 14%),
        0 1px 5px 0 rgb(0 0 0 / 12%) !important;
    height: 36px !important;
    padding: 3px 6px;
}

.payment-list-header .v-toolbar__content {
    padding-left: 0px;
    padding-right: 0px;
}

.payment-list-search {
    outline: none;
    max-height: 30px;
    padding: 4px 10px;
    width: 15%;
    margin-left: 4px;
}

.payment-list-search::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.payment-list-table td {
    height: 36px !important;
}

.payment-process-table th,
.payment-process-table td {
    height: 40px !important;
}

.payment-process-table th {
    background-color: #eaeaea;
}

.processed-payment-table th {
    height: 40px !important;
    background-color: #eaeaea;
}

.processed-payment-table td {
    height: 35px !important;
}

.v-dialog .data-grid td {
    padding: 6px 15px;
}

.ui-checkbox input:checked ~ .box:before,
ui.checkbox input:checked ~ label:before {
    background-color: #f2711c;
    border-color: rgba(34, 36, 38, 0.35);
}

.checkbox-round {
    width: 1.3em;
    height: 1.3em;
    background-color: white;
    border-radius: 4px;
    vertical-align: middle;
    border: 1px solid #ddd;
    appearance: none;
    -webkit-appearance: none;
    outline: none;
    cursor: pointer;
}

.checkbox-round:checked {
    background-color: gray;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <div
            class="cirrus8-loader-vue-js"
            v-if="loading_progressBar"
        >
            <div style="padding-top: 15%; text-align: center">
                <v-progress-linear
                    v-model="progressBar"
                    height="30"
                    striped
                >
                    <strong>{{ Math.ceil(progressBar) }}%</strong>
                </v-progress-linear>
            </div>
        </div>
        <div
            class="cirrus8-loader-vue-js"
            v-if="loading_compiledata"
        >
            <div style="padding-top: 15%; text-align: center">
                <span style="color: #7f8c8d; font-size: 1vw">Consolidating the transactions...</span>
                <v-progress-linear
                    color="primary"
                    v-model="progressBar"
                    height="6"
                    indeterminate
                    rounded
                >
                </v-progress-linear>
            </div>
        </div>
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header :title="page_title" />
            </v-toolbar-title>
            <div class="flex-grow-1"></div>
        </v-toolbar>
        <!-- PARAMETERS -->
        <div
            class="page-form"
            v-if="showFormPart"
        >
            <!-- PAYMENT TYPE -->
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Payment Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="PaymentRun"
                    >
                        <v-btn
                            small
                            tile
                            text
                            v-for="(item, index) in PaymentRunItem"
                            :key="item.paymentID"
                            v-bind:id="item.paymentID"
                            v-text="item.name"
                            class="no-text-transform"
                            @click="changePaymentType(item)"
                        ></v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <!-- PAYMENT RUN DATE -->
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Payment Run Date
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-icon-date-picker
                        :size="'40'"
                        :id="'start_date_'"
                        :blurEvent="'quick-payment'"
                        v-model="dateFormatted"
                    ></cirrus-icon-date-picker>
                    <!-- U -->
                </v-col>
            </v-row>
            <!-- WITH INVOICES -->
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >with Invoices Only?
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <input
                        type="checkbox"
                        v-model="checkboxWithInvoice"
                        @change="setWithInvoice"
                    />
                </v-col>
            </v-row>
            <!-- WITH BALANCES -->
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >with Balances Only?
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <!--                    <v-checkbox v-model="checkboxWithBal" @change="setWithBalance" style="width: 50px !important;"></v-checkbox>-->
                    <input
                        type="checkbox"
                        v-model="checkboxWithBal"
                        @change="setWithBalance"
                    />
                    <span style="margin-top: 8; margin-left: 10; position: absolute">{{ this.withBalanceNote }}</span>
                </v-col>
            </v-row>
            <!-- BANK ACCOUNT -->
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Bank Account
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select
                        placeholder="Select a bank account"
                        v-model="BankAccount"
                        :options="BankAccountList"
                        @input="changeBankAccount"
                    />
                </v-col>
            </v-row>
            <!-- SELECT BY BTN TOGGLE -->
            <v-row
                class="form-row"
                v-if="showPropertyParameter"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Select By
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="propertyBy"
                    >
                        <v-btn
                            small
                            v-for="(item, index) in propertyBys"
                            :key="item.value"
                            v-bind:id="item.value"
                            v-text="item.label"
                            class="no-text-transform"
                            @click="changeSelectBy(item.value)"
                        >
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <!-- SELECT BY PM/SUP/PG -->
            <v-row
                class="form-row"
                v-if="showPropertyParameter"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >{{ selectByName }}
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                    style="padding-bottom: 0px"
                >
                    <vue-dual-list-select
                        v-model="selectByPropModel"
                        :options="allPropertyManager"
                        ref="refPropModel"
                        :displayCount="false"
                    >
                    </vue-dual-list-select>
                    <spacer>&nbsp;&nbsp;</spacer>
                    <v-btn
                        class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only"
                        color="primary"
                        depressed
                        elevation="0"
                        small
                        style="width: 200px !important; margin-bottom: 2px; color: #ffffff"
                        @click="updatePropertyList()"
                        v-text="selectByBtnLabel"
                    />
                </v-col>
            </v-row>
            <!-- SELECT BY PROPERTY -->
            <v-row
                class="form-row"
                v-if="showPropertyListParameter"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Property
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                    style="padding-bottom: 0px"
                >
                    <vue-dual-list-select
                        v-model="selectByPropertyModel"
                        :options="propertiesList"
                        ref="refPropertyModel"
                        :displayCount="false"
                    ></vue-dual-list-select>
                </v-col>
                <!-- DISPLAY PAYMENT LIST -->
                <v-row
                    class="form-row"
                    v-if="showPropertyParameter"
                    style="padding-top: 0px"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                        style="padding-top: 0px"
                    >
                        <v-btn
                            class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only"
                            color="primary"
                            depressed
                            elevation="0"
                            small
                            style="width: 210px !important; margin-bottom: 2px; color: #ffffff"
                            @click="reloadPaymentRunDefaults()"
                            v-text="updateBtnLabel"
                        />
                    </v-col>
                </v-row>
            </v-row>
        </div>
        <!-- PAYMENT LIST -->
        <v-card v-if="showPayableDetails">
            <!-- PAYMENT LIST DETAILS -->
            <div class="page-form c8-no-padding">
                <div class="page-list">
                    <v-data-table
                        dense
                        v-model="selectedData"
                        :headers="headers"
                        :items="pdDetails"
                        :single-expand="singleExpand"
                        :multiple="multiple"
                        :expanded.sync="expanded"
                        :search="searchDtl"
                        :loading="loading"
                        loading-text="Loading... Please Wait..."
                        item-key="property_name"
                        show-expand
                        show-select
                        :items-per-page="500"
                        :footer-props="{
                            'items-per-page-options': [100, 250, 500, -1],
                        }"
                        ref="refSelectedData"
                        @pagination="updatePagination"
                        @toggle-select-all="handleToggleAll($event)"
                        class="elevation-1 payment-list-table"
                        style="border-radius: 0px"
                    >
                        <!-- PAYMENT DETAILS HEADERS -->
                        <template
                            class="test"
                            v-slot:top
                        >
                            <v-toolbar
                                flat
                                color="#3489A1"
                                height="30px;"
                                class="payment-list-header"
                            >
                                <v-toolbar-title style="font-size: 12px; color: #ffffff; font-weight: bold"
                                    >Payment Details
                                </v-toolbar-title>
                                <v-spacer></v-spacer>
                                <!--                            <v-switch v-model="singleExpand" class="mt-2"></v-switch>-->
                                <i
                                    aria-hidden="true"
                                    class="v-icon notranslate material-icons theme--dark"
                                    >search</i
                                >
                                <input
                                    v-model="searchDtl"
                                    type="text"
                                    placeholder="Search"
                                    class="payment-list-search"
                                    style="
                                        background-color: rgba(0, 0, 0, 0);
                                        border: 1px solid #ececec !important;
                                        color: #ffffff !important;
                                        border-radius: 2px !important;
                                    "
                                />
                            </v-toolbar>
                        </template>
                        <template v-slot:header.property_name="{ header }">
                            <table width="100%">
                                <tr class="form-row">
                                    <th class="text-left">&nbsp;</th>
                                </tr>
                                <tr class="form-row">
                                    <th class="text-left">{{ header.text }}</th>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:header.availableFunds="{ header }">
                            <table width="100%">
                                <tr class="form-row">
                                    <th class="text-right">&nbsp;</th>
                                </tr>
                                <tr class="form-row">
                                    <th class="text-right">{{ header.text }}</th>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:header.managementFeeSundries="{ header }">
                            <table width="100%">
                                <tr class="form-row">
                                    <th colspan="2">{{ header.text }}</th>
                                </tr>
                                <tr class="form-row">
                                    <th
                                        width=" 50%"
                                        class="text-right"
                                    >
                                        Fees Due
                                    </th>
                                    <th
                                        width=" 50%"
                                        class="text-right"
                                    >
                                        <span style="color: #1ebc30">Pay</span> /
                                        <span style="color: #00baf2">Reserve</span>
                                    </th>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:header.supplierInvoices="{ header }">
                            <table width="100%">
                                <tr class="form-row">
                                    <th colspan="2">{{ header.text }}</th>
                                </tr>
                                <tr class="form-row">
                                    <th
                                        width=" 50%"
                                        class="text-right"
                                    >
                                        Unpaid Invoices
                                    </th>
                                    <th
                                        width=" 50%"
                                        class="text-right"
                                    >
                                        <span style="color: #1ebc30">Pay</span> /
                                        <span style="color: #00baf2">Reserve</span>
                                    </th>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:header.ownerPayment="{ header }">
                            <table width="100%">
                                <tr class="form-row">
                                    <th class="text-right">&nbsp;</th>
                                </tr>
                                <tr class="form-row">
                                    <th class="text-right">{{ header.text }}</th>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:header.closingBal="{ header }">
                            <table width="100%">
                                <tr class="form-row">
                                    <th class="text-right">&nbsp;</th>
                                </tr>
                                <tr class="form-row">
                                    <th class="text-right">{{ header.text }}</th>
                                </tr>
                            </table>
                        </template>
                        <!-- PAYMENT DETAILS LIST -->
                        <template v-slot:item.property_name="{ item }">
                            <table width="100%">
                                <tr class="form-row c8-no-padding">
                                    <td>{{ item.property_name }}</td>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:item.availableFunds="{ item }">
                            <table width="100%">
                                <tr class="form-row c8-no-padding">
                                    <td
                                        @click="
                                            modalOpeningBreakdown(
                                                item.opening_details,
                                                item.opening_holds,
                                                item.partitioned,
                                            )
                                        "
                                        class="text-right"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{ formatAsCurrency(item.availableFunds, 2) }}</a>
                                            </template>
                                            <span>View Available Funds Breakdown</span>
                                        </v-tooltip>
                                    </td>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:item.managementFeeSundries="{ item }">
                            <table width="100%">
                                <tr
                                    v-if="!item.partitioned"
                                    class="form-row c8-no-padding"
                                >
                                    <td
                                        v-if="totalIt(item.managements.outstanding) == 0"
                                        class="text-right"
                                        width="50%"
                                    >
                                        {{ formatAsCurrency(totalIt(item.managements.outstanding), 2) }}
                                    </td>
                                    <td
                                        v-if="totalIt(item.managements.outstanding) != 0"
                                        @click="
                                            modalBreakdown(
                                                '[' + item.property + '] - Management Fee & Sundries (Fees Due)',
                                                item.partitioned,
                                                item.managements.paidrsvd,
                                                'management',
                                                true,
                                                true,
                                                true,
                                            )
                                        "
                                        class="txt-info text-right"
                                        width="50%"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{
                                                    formatAsCurrency(totalIt(item.managements.outstanding), 2)
                                                }}</a>
                                            </template>
                                            <span>View Breakdown</span>
                                        </v-tooltip>
                                    </td>

                                    <td
                                        v-if="totalIfDeep(item.managements.paidrsvd, 'allocated') == 0"
                                        class="text-right"
                                        width="50%"
                                        v-bind:class="setPaidRsvdCellClass(item.managements.paidrsvd, true)"
                                    >
                                        {{ formatAsCurrency(totalIfDeep(item.managements.paidrsvd, 'allocated'), 2) }}
                                    </td>

                                    <td
                                        v-if="totalIfDeep(item.managements.paidrsvd, 'allocated') != 0"
                                        @click="
                                            modalBreakdown(
                                                '[' + item.property + '] - Management Fee & Sundries (Pay/Reserve) ',
                                                item.partitioned,
                                                item.managements.paidrsvd,
                                                'management',
                                                true,
                                                true,
                                            )
                                        "
                                        class="text-right"
                                        width="50%"
                                        v-bind:class="setPaidRsvdCellClass(item.managements.paidrsvd, true)"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{
                                                    formatAsCurrency(
                                                        totalIfDeep(item.managements.paidrsvd, 'allocated'),
                                                        2,
                                                    )
                                                }}</a>
                                            </template>
                                            <span>View Breakdown</span>
                                        </v-tooltip>
                                    </td>
                                </tr>
                                <tr
                                    v-if="item.partitioned"
                                    class="form-row c8-no-padding"
                                >
                                    <td></td>
                                    <td
                                        v-if="!countPaid(item.managements.paidrsvd)"
                                        class="text-right"
                                    >
                                        {{ formatAsCurrency(totalPaidAll(item.managements.paidrsvd), 2) }}
                                    </td>
                                    <td
                                        v-if="countPaid(item.managements.paidrsvd)"
                                        class="text-right"
                                        @click="
                                            modalTotalBreakdown(
                                                '[' + item.property + '] - Management Fee & Sundries',
                                                item.partitioned,
                                                item.managements.paidrsvd,
                                                'management',
                                            )
                                        "
                                        v-bind:class="setPaidRsvdCellClass(item.managements.paidrsvd, true)"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{
                                                    formatAsCurrency(totalPaidAll(item.managements.paidrsvd), 2)
                                                }}</a>
                                            </template>
                                            <span>View Breakdown</span>
                                        </v-tooltip>
                                    </td>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:item.supplierInvoices="{ item }">
                            <table width="100%">
                                <tr
                                    v-if="!item.partitioned"
                                    class="form-row c8-no-padding"
                                >
                                    <td
                                        v-if="totalIt(item.suppliers.outstanding) == 0"
                                        class="text-right"
                                        width="50%"
                                    >
                                        {{ formatAsCurrency(totalIt(item.suppliers.outstanding), 2) }}
                                    </td>
                                    <td
                                        v-if="totalIt(item.suppliers.outstanding) != 0"
                                        @click="
                                            modalBreakdown(
                                                '[' + item.property + '] - Supplier Invoices Outstanding Amounts',
                                                item.partitioned,
                                                item.suppliers.paidrsvd,
                                                'suppliers',
                                                true,
                                                true,
                                                true,
                                            )
                                        "
                                        class="txt-info text-right"
                                        width="50%"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{
                                                    formatAsCurrency(totalIt(item.suppliers.outstanding), 2)
                                                }}</a>
                                            </template>
                                            <span>View Breakdown</span>
                                        </v-tooltip>
                                    </td>

                                    <td
                                        v-if="totalIfDeep(item.suppliers.paidrsvd, 'allocated') == 0"
                                        class="text-right"
                                        width="50%"
                                        v-bind:class="setPaidRsvdCellClass(item.suppliers.paidrsvd, true)"
                                    >
                                        {{ formatAsCurrency(totalIfDeep(item.suppliers.paidrsvd, 'allocated'), 2) }}
                                    </td>

                                    <td
                                        v-if="totalIfDeep(item.suppliers.paidrsvd, 'allocated') != 0"
                                        @click="
                                            modalBreakdown(
                                                '[' + item.property + '] - Supplier Invoices (Pay)',
                                                item.partitioned,
                                                item.suppliers.paidrsvd,
                                                'suppliers',
                                                true,
                                                true,
                                            )
                                        "
                                        class="text-right"
                                        width="50%"
                                        v-bind:class="setPaidRsvdCellClass(item.suppliers.paidrsvd, true)"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{
                                                    formatAsCurrency(
                                                        totalIfDeep(item.suppliers.paidrsvd, 'allocated'),
                                                        2,
                                                    )
                                                }}</a>
                                            </template>
                                            <span>View Breakdown</span>
                                        </v-tooltip>
                                    </td>
                                </tr>
                                <tr
                                    v-if="item.partitioned"
                                    class="form-row c8-no-padding"
                                >
                                    <td></td>
                                    <td
                                        v-if="!countPaid(item.suppliers.paidrsvd)"
                                        class="text-right"
                                        width="50%"
                                    >
                                        {{ formatAsCurrency(totalPaidAll(item.suppliers.paidrsvd), 2) }}
                                    </td>
                                    <td
                                        v-if="countPaid(item.suppliers.paidrsvd)"
                                        @click="
                                            modalTotalBreakdown(
                                                '[' + item.property + '] - Supplier Invoices (Pay)',
                                                item.partitioned,
                                                item.suppliers.paidrsvd,
                                                'suppliers',
                                            )
                                        "
                                        class="text-right"
                                        width="50%"
                                        v-bind:class="setPaidRsvdCellClass(item.suppliers.paidrsvd, true)"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{
                                                    formatAsCurrency(totalPaidAll(item.suppliers.paidrsvd), 2)
                                                }}</a>
                                            </template>
                                            <span>View Breakdown</span>
                                        </v-tooltip>
                                    </td>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:item.ownerPayment="{ item }">
                            <table width="100%">
                                <tr
                                    v-if="!item.partitioned"
                                    class="form-row c8-no-padding"
                                >
                                    <td
                                        v-if="
                                            totalArr(item.owners['O'], 'allocated') == 0 &&
                                            item.allowOwnerPayments &&
                                            !item.showOwnerDetailsDuetoWithhold &&
                                            !item.insufficientFunds &&
                                            !item.noOwnerShareSet
                                        "
                                        class="text-right"
                                    >
                                        {{ formatAsCurrency(totalArr(item.owners['O'], 'allocated'), 2) }}
                                    </td>
                                    <td
                                        v-if="item.showOwnerDetailsDuetoWithhold"
                                        class="text-right"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <span
                                                    v-on="on"
                                                    style="font-weight: bold; color: #aaaaaa !important"
                                                    >{{
                                                        formatAsCurrency(totalArr(item.owners['O'], 'allocated'), 2)
                                                    }}</span
                                                >
                                            </template>
                                            <span>
                                                Amount to withhold from property ${{
                                                    formatAsCurrency(item.withholdAmount, 2)
                                                }}</span
                                            >
                                        </v-tooltip>
                                    </td>
                                    <td
                                        v-if="!item.allowOwnerPayments && !item.showOwnerDetailsDuetoWithhold"
                                        class="text-right"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <span
                                                    v-on="on"
                                                    class="txt-not-paid"
                                                    >{{
                                                        formatAsCurrency(totalArr(item.owners['O'], 'allocated'), 2)
                                                    }}</span
                                                >
                                            </template>
                                            <span>This property is set to not pay the owner</span>
                                        </v-tooltip>
                                    </td>
                                    <td
                                        v-if="item.insufficientFunds || item.noOwnerShareSet"
                                        class="text-right"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <span
                                                    v-on="on"
                                                    class="txt-bold txt-gray"
                                                    >{{
                                                        formatAsCurrency(totalArr(item.owners['O'], 'allocated'), 2)
                                                    }}</span
                                                >
                                            </template>
                                            <span v-if="item.insufficientFunds"
                                                >Insufficient funds to cover fixed payment amounts</span
                                            >
                                            <span v-if="item.noOwnerShareSet">No owner shares setup</span>
                                        </v-tooltip>
                                    </td>
                                    <td
                                        v-if="totalArr(item.owners['O'], 'allocated') != 0"
                                        @click="
                                            modalTotalBreakdown(
                                                '[' + item.property + '] - Total Owners Payments Paid/Reserved ',
                                                item.partitioned,
                                                item.owners,
                                                'owners',
                                                item.withholdAmount,
                                            )
                                        "
                                        class="text-right"
                                        v-bind:class="setPaidRsvdCellClass(item.owners['O'])"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{
                                                    formatAsCurrency(totalArr(item.owners['O'], 'allocated'), 2)
                                                }}</a>
                                            </template>
                                            <span>View Breakdown</span>
                                        </v-tooltip>
                                    </td>
                                </tr>
                                <tr
                                    v-if="item.partitioned"
                                    class="form-row c8-no-padding"
                                >
                                    <td
                                        v-if="totalPaidAll(item.owners) == 0"
                                        class="text-right"
                                    >
                                        {{ formatAsCurrency(totalPaidAll(item.owners), 2) }}
                                    </td>
                                    <td
                                        v-if="totalPaidAll(item.owners) != 0"
                                        @click="
                                            modalTotalBreakdown(
                                                '[' + item.property + '] - Total Owners Payments Paid/Reserved ',
                                                item.partitioned,
                                                item.owners,
                                                'owners',
                                            )
                                        "
                                        class="text-right"
                                        v-bind:class="setPaidRsvdCellClass(item.owners['O'])"
                                    >
                                        <v-tooltip top>
                                            <template v-slot:activator="{ on }">
                                                <a v-on="on">{{ formatAsCurrency(totalPaidAll(item.owners), 2) }}</a>
                                            </template>
                                            <span>View Breakdown</span>
                                        </v-tooltip>
                                    </td>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:item.closingBal="{ item }">
                            <table width="100%">
                                <tr
                                    v-if="!item.partitioned"
                                    class="form-row c8-no-padding"
                                >
                                    <td class="text-right">
                                        {{ formatAsCurrency(item.closingBal, 2) }}
                                    </td>
                                </tr>
                                <tr
                                    v-if="item.partitioned"
                                    class="form-row c8-no-padding"
                                >
                                    <td class="text-right">{{ formatAsCurrency(item.closingBal, 2) }}</td>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:item.data-table-expand="{ item, isExpanded, expand }">
                            <i
                                @click="expand(true)"
                                v-if="item.partitioned && !isExpanded"
                                role="button"
                                class="v-icon notranslate v-data-table__expand-icon v-icon--link mdi mdi-chevron-down theme--light"
                            ></i>
                            <i
                                @click="expand(false)"
                                v-if="item.partitioned && isExpanded"
                                role="button"
                                class="v-icon notranslate v-data-table__expand-icon v-icon--link mdi mdi-chevron-down theme--light v-data-table__expand-icon--active"
                            ></i>
                        </template>

                        <template v-slot:expanded-item="{ headers, item }">
                            <td
                                :colspan="headers.length"
                                width="100%"
                            >
                                <table
                                    width="100%"
                                    style="display: grid !important; padding: 5px; font-size: 11px"
                                >
                                    <tr
                                        v-for="(bal, type) in item.opening_details"
                                        class="form-row c8-no-padding"
                                        style="height: 30px !important; margin-top: 8px"
                                    >
                                        <td
                                            class="text-start"
                                            width="1px"
                                            style="color: #777777"
                                        ></td>
                                        <td
                                            class="text-start"
                                            width="21%"
                                            style="color: #777777"
                                        >
                                            {{ partitions[type] }}
                                        </td>
                                        <!-- Available Funds -->
                                        <td
                                            class="text-start"
                                            width="11%"
                                        >
                                            <table width="100%">
                                                <tr class="form-row">
                                                    <td
                                                        v-if="type == 'O'"
                                                        class="text-right"
                                                    >
                                                        {{
                                                            formatAsCurrency(
                                                                totalIt(bal) -
                                                                    item.opening_holds.unalloc.amount -
                                                                    getUnclearedAmt(item.opening_holds.uncleared, type),
                                                                2,
                                                            )
                                                        }}
                                                    </td>
                                                    <td
                                                        v-else
                                                        class="text-right"
                                                    >
                                                        {{
                                                            formatAsCurrency(
                                                                totalIt(bal) -
                                                                    getUnclearedAmt(item.opening_holds.uncleared, type),
                                                                2,
                                                            )
                                                        }}
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <!-- Management Fees & Sundries-->
                                        <td
                                            class="text-start"
                                            width="22%"
                                        >
                                            <table width="100%">
                                                <tr class="form-row">
                                                    <td
                                                        v-if="item.managements.outstanding[type] == 0"
                                                        class="text-right"
                                                        width="50%"
                                                    >
                                                        {{ formatAsCurrency(item.managements.outstanding[type], 2) }}
                                                    </td>
                                                    <td
                                                        v-if="item.managements.outstanding[type] != 0"
                                                        @click="
                                                            modalBreakdown(
                                                                '[' +
                                                                    item.property +
                                                                    '] - Management Fee & Sundries Outstanding Amounts',
                                                                item.partitioned,
                                                                item.managements.paidrsvd[type],
                                                                'managements',
                                                                false,
                                                                false,
                                                                true,
                                                            )
                                                        "
                                                        class="txt-info text-right"
                                                        width="50%"
                                                    >
                                                        <v-tooltip top>
                                                            <template v-slot:activator="{ on }">
                                                                <a v-on="on">{{
                                                                    formatAsCurrency(
                                                                        item.managements.outstanding[type],
                                                                        2,
                                                                    )
                                                                }}</a>
                                                            </template>
                                                            <span>View Breakdown</span>
                                                        </v-tooltip>
                                                    </td>

                                                    <!--												<td v-if="!countPaid(item.managements.paidrsvd[type])"-->
                                                    <td
                                                        v-if="
                                                            totalPaidperType(
                                                                item.managements.paidrsvd[type],
                                                                'allocated',
                                                            ) == 0
                                                        "
                                                        class="text-right"
                                                        width="50%"
                                                        v-bind:class="
                                                            setPaidRsvdCellClass(item.suppliers.paidrsvd, true)
                                                        "
                                                    >
                                                        {{
                                                            formatAsCurrency(
                                                                totalPaidperType(
                                                                    item.managements.paidrsvd[type],
                                                                    'allocated',
                                                                ),
                                                                2,
                                                            )
                                                        }}
                                                    </td>
                                                    <td
                                                        v-if="
                                                            totalPaidperType(
                                                                item.managements.paidrsvd[type],
                                                                'allocated',
                                                            ) != 0
                                                        "
                                                        @click="
                                                            modalBreakdown(
                                                                '[' +
                                                                    item.property +
                                                                    '] - Management Fee & Sundries Outstanding Amounts',
                                                                item.partitioned,
                                                                item.managements.paidrsvd[type],
                                                                'managements',
                                                                false,
                                                                false,
                                                                true,
                                                            )
                                                        "
                                                        class="text-right"
                                                        width="50%"
                                                        v-bind:class="
                                                            setPaidRsvdCellClass(item.managements.paidrsvd, true)
                                                        "
                                                    >
                                                        <v-tooltip top>
                                                            <template v-slot:activator="{ on }">
                                                                <a v-on="on">{{
                                                                    formatAsCurrency(
                                                                        totalPaidperType(
                                                                            item.managements.paidrsvd[type],
                                                                            'allocated',
                                                                        ),
                                                                        2,
                                                                    )
                                                                }}</a>
                                                            </template>
                                                            <span>View Breakdown</span>
                                                        </v-tooltip>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <!-- Supplier Invoicese -->
                                        <td
                                            class="text-start"
                                            width="22%"
                                        >
                                            <table width="100%">
                                                <tr class="form-row">
                                                    <td
                                                        v-if="item.suppliers.outstanding[type] == 0"
                                                        class="text-right"
                                                        width="50%"
                                                    >
                                                        {{ formatAsCurrency(item.suppliers.outstanding[type], 2) }}
                                                    </td>
                                                    <td
                                                        v-if="item.suppliers.outstanding[type] != 0"
                                                        @click="
                                                            modalBreakdown(
                                                                '[' +
                                                                    item.property +
                                                                    '] - Supplier Invoices Outstanding Amounts',
                                                                item.partitioned,
                                                                item.suppliers.paidrsvd[type],
                                                                'suppliers',
                                                                false,
                                                                false,
                                                                true,
                                                            )
                                                        "
                                                        class="txt-info text-right"
                                                        width="50%"
                                                    >
                                                        <v-tooltip top>
                                                            <template v-slot:activator="{ on }">
                                                                <a v-on="on">{{
                                                                    formatAsCurrency(
                                                                        item.suppliers.outstanding[type],
                                                                        2,
                                                                    )
                                                                }}</a>
                                                            </template>
                                                            <span>View Breakdown</span>
                                                        </v-tooltip>
                                                    </td>

                                                    <td
                                                        v-if="
                                                            totalPaidperType(
                                                                item.suppliers.paidrsvd[type],
                                                                'allocated',
                                                            ) == 0
                                                        "
                                                        class="text-right"
                                                        width="50%"
                                                        v-bind:class="
                                                            setPaidRsvdCellClass(item.suppliers.paidrsvd, true)
                                                        "
                                                    >
                                                        {{
                                                            formatAsCurrency(
                                                                totalPaidperType(
                                                                    item.suppliers.paidrsvd[type],
                                                                    'allocated',
                                                                ),
                                                                2,
                                                            )
                                                        }}
                                                    </td>
                                                    <td
                                                        v-if="
                                                            totalPaidperType(
                                                                item.suppliers.paidrsvd[type],
                                                                'allocated',
                                                            ) != 0
                                                        "
                                                        @click="
                                                            modalBreakdown(
                                                                '[' + item.property + '] - Supplier Invoices Pay',
                                                                item.partitioned,
                                                                item.suppliers.paidrsvd[type],
                                                                'suppliers',
                                                            )
                                                        "
                                                        class="text-right"
                                                        width="50%"
                                                        v-bind:class="
                                                            setPaidRsvdCellClass(item.suppliers.paidrsvd, true)
                                                        "
                                                    >
                                                        <v-tooltip top>
                                                            <template v-slot:activator="{ on }">
                                                                <a v-on="on">{{
                                                                    formatAsCurrency(
                                                                        totalPaidperType(
                                                                            item.suppliers.paidrsvd[type],
                                                                            'allocated',
                                                                        ),
                                                                        2,
                                                                    )
                                                                }}</a>
                                                            </template>
                                                            <span>View Breakdown</span>
                                                        </v-tooltip>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <!-- Owner Payments -->
                                        <td
                                            class="text-start"
                                            width="11%"
                                        >
                                            <table width="100%">
                                                <tr class="form-row">
                                                    <td
                                                        v-if="totalPaidperType(item.owners[type], 'allocated') == 0"
                                                        class="text-right"
                                                        v-bind:class="setPaidRsvdCellClass(item.owners[type])"
                                                    >
                                                        {{
                                                            formatAsCurrency(
                                                                totalPaidperType(item.owners[type], 'allocated'),
                                                                2,
                                                            )
                                                        }}
                                                    </td>
                                                    <td
                                                        v-if="totalPaidperType(item.owners[type], 'allocated') != 0"
                                                        @click="
                                                            modalBreakdown(
                                                                '[' +
                                                                    item.property +
                                                                    '] - Total Owners Payments Paid/Reserved ',
                                                                item.partitioned,
                                                                item.owners[type],
                                                                'owners',
                                                            )
                                                        "
                                                        class="text-right"
                                                        v-bind:class="setPaidRsvdCellClass(item.owners[type])"
                                                    >
                                                        <v-tooltip top>
                                                            <template v-slot:activator="{ on }">
                                                                <a v-on="on">{{
                                                                    formatAsCurrency(
                                                                        totalPaidperType(
                                                                            item.owners[type],
                                                                            'allocated',
                                                                        ),
                                                                        2,
                                                                    )
                                                                }}</a>
                                                            </template>
                                                            <span>View Breakdown</span>
                                                        </v-tooltip>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <!-- Closing Balance -->
                                        <td
                                            class="text-start"
                                            width="11%"
                                        >
                                            <table width="100%">
                                                <tr class="form-row">
                                                    <td class="text-right">
                                                        {{ formatAsCurrency(item.closing[type], 2) }}
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td
                                            class="text-start"
                                            width="2%"
                                        >
                                            &nbsp;
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </template>
                        <template v-slot:body.append>
                            <tr class="form-row c8-no-padding group-total-row">
                                <td
                                    colspan="2"
                                    class="text-right"
                                >
                                    Total
                                </td>
                                <td class="text-right">{{ formatAsCurrency(totalAvailableFunds, 2) }}</td>
                                <td class="text-right">
                                    <table width="100%">
                                        <tr class="form-row c8-no-padding group-total-row">
                                            <td
                                                width=" 50%"
                                                class="text-right"
                                            >
                                                {{ formatAsCurrency(totalMgtFeesDue, 2) }}
                                            </td>
                                            <td
                                                width=" 50%"
                                                class="text-right"
                                            >
                                                {{ formatAsCurrency(totalMgtFeePayReserve, 2) }}
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td class="text-right">
                                    <table width="100%">
                                        <tr class="form-row c8-no-padding group-total-row">
                                            <td
                                                width=" 50%"
                                                class="text-right"
                                            >
                                                {{ formatAsCurrency(totalSupplierUnpaidInvoice, 2) }}
                                            </td>
                                            <td
                                                width=" 50%"
                                                class="text-right"
                                            >
                                                {{ formatAsCurrency(totalSupplierPay, 2) }}
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td class="text-right">{{ formatAsCurrency(totalOwnerPayments, 2) }}</td>
                                <td class="text-right">{{ formatAsCurrency(totalClosingBalance, 2) }}</td>
                                <td class="text-right"></td>
                            </tr>
                        </template>
                    </v-data-table>
                </div>
            </div>
            <!-- PROCESS PAYMENT BTN -->
            <v-card-actions
                class="actionsDetails"
                style="
                    background: rgb(249, 249, 249);
                    border-top-left-radius: 0;
                    border-top-right-radius: 0;
                    box-shadow:
                        0 2px 1px -1px rgba(0, 0, 0, 0.2),
                        0 1px 1px 0 rgba(0, 0, 0, 0.14),
                        0 1px 3px 0 rgba(0, 0, 0, 0.12) !important;
                "
            >
                <v-spacer></v-spacer>
                <v-col
                    class="py-2"
                    align="right"
                >
                    <v-btn
                        color="primary"
                        small
                        v-on:click="processPayment()"
                        :disabled="!processingEnabled"
                        >Process Payment
                    </v-btn>
                </v-col>
            </v-card-actions>
        </v-card>

        <!-- MODAL VIEW OF AVAILABLE FUNDS -->
        <v-dialog
            top
            v-model="dialogOpeningBreakdown"
            max-width="700px"
            max-height="70%"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title
                    class="headline"
                    style="margin-bottom: 0px"
                >
                    <span>{{ dtlOpeningBreakdownData.title }}</span>

                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogOpeningBreakdown = false"
                        ><i
                            aria-hidden="true"
                            class="v-icon notranslate mdi mdi-close theme--light"
                        ></i
                    ></a>
                </v-card-title>
                <v-divider></v-divider>
                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr class="fieldDescription">
                                <td></td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    Total
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                    v-for="(txt, colCode) in dtlOpeningBreakdownData.columns"
                                    v-if="dtlOpeningBreakdownData.partitioned"
                                >
                                    {{ txt }}
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(title, rowCode) in dtlOpeningBreakdownData.rows">
                                <td>{{ title }}</td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    {{ formatAsCurrency(dtlOpeningBreakdownData.totals[rowCode], 2) }}
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                    v-for="(txt, colCode) in dtlOpeningBreakdownData.columns"
                                    v-if="dtlOpeningBreakdownData.partitioned"
                                >
                                    <span v-if="dtlOpeningBreakdownData.blanks.indexOf(colCode + rowCode) == -1">
                                        {{ formatAsCurrency(dtlOpeningBreakdownData.datas[colCode][rowCode], 2) }}
                                    </span>
                                </td>
                            </tr>
                            <tr class="highlight">
                                <td>Closing GL Bank Balance</td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    {{ formatAsCurrency(dtlOpeningBreakdownData.overall, 2) }}
                                </td>
                                <td
                                    align="right"
                                    v-for="(txt, colCode) in dtlOpeningBreakdownData.columns"
                                    class="nums"
                                    v-if="dtlOpeningBreakdownData.partitioned"
                                >
                                    <span>{{ formatAsCurrency(dtlOpeningBreakdownData.closing[colCode], 2) }}</span>
                                </td>
                            </tr>
                            <tr v-for="(title, actCode) in dtlOpeningBreakdownData.actRows">
                                <td>{{ title }}</td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    <span
                                        class="txt-bold txt-hold"
                                        style="font-weight: bold"
                                        v-if="dtlOpeningBreakdownData.actDatas[actCode].hold && actCode == 'uncleared'"
                                        >ON HOLD &nbsp;</span
                                    >
                                    <span
                                        class="txt-bold txt-reserve"
                                        style="font-weight: bold"
                                        v-if="dtlOpeningBreakdownData.actDatas[actCode].hold && actCode == 'unalloc'"
                                        >RESERVED &nbsp;</span
                                    >
                                    <span
                                        class="txt-bold txt-reserve"
                                        style="color: #00baf2; font-weight: bold"
                                        v-if="dtlOpeningBreakdownData.actDatas[actCode].hold && actCode == 'tax'"
                                        >RESERVED &nbsp;</span
                                    >
                                    <span
                                        class="txt-bold txt-hold"
                                        style="font-weight: bold"
                                        v-if="
                                            dtlOpeningBreakdownData.actDatas[actCode].hold &&
                                            actCode == 'futureNetAmount'
                                        "
                                        >ON HOLD &nbsp;</span
                                    >
                                    <span
                                        class="txt-bold"
                                        style="display: inline-block; min-width: 50px; font-weight: bold"
                                        >{{
                                            formatAsCurrency(dtlOpeningBreakdownData.actDatas[actCode].amount, 2)
                                        }}</span
                                    >
                                </td>
                                <td
                                    v-for="(txt, colCode) in dtlOpeningBreakdownData.columns"
                                    class="nums"
                                    align="right"
                                    v-if="dtlOpeningBreakdownData.partitioned"
                                ></td>
                            </tr>
                            <tr class="highlight">
                                <td>Available Funds</td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    {{ formatAsCurrency(dtlOpeningBreakdownData.availableFunds, 2) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </v-card>
        </v-dialog>

        <!-- MODAL VIEW OF MANAGEMENT FEES & SUNDRIES / SUPPLIER INVOICES METHOD -->
        <v-dialog
            top
            v-model="dialogBreakdown"
            max-width="90%"
            max-height="70%"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title
                    class="headline"
                    style="margin-bottom: 0px"
                >
                    <span>{{ dtlBreakdownData.title }}</span>

                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogBreakdown = false"
                        ><i
                            aria-hidden="true"
                            class="v-icon notranslate mdi mdi-close theme--light"
                        ></i
                    ></a>
                </v-card-title>
                <v-divider></v-divider>
                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr class="fieldDescription">
                                <td v-if="dtlBreakdownData.type != 'owners'">Priority</td>
                                <td
                                    class="txts-date"
                                    v-if="dtlBreakdownData.type == 'suppliers'"
                                >
                                    Invoice Date
                                </td>
                                <td
                                    class="txts-date"
                                    v-if="dtlBreakdownData.type == 'suppliers'"
                                >
                                    Due Date
                                </td>
                                <td>Type</td>
                                <td v-if="dtlBreakdownData.type == 'suppliers'">Creditor</td>
                                <td v-if="dtlBreakdownData.type == 'suppliers'">Payment Method</td>
                                <td v-if="dtlBreakdownData.type == 'suppliers'">Invoice Number</td>
                                <td>Account</td>
                                <td>Description</td>
                                <td
                                    width="3%"
                                    v-if="dtlBreakdownData.type == 'suppliers'"
                                >
                                    Hold
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    Outstanding Amount
                                </td>
                                <td align="right">
                                    <span style="color: #1ebc30">Pay</span> /
                                    <span style="color: #00baf2">Reserve</span>
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                    v-if="dtlBreakdownData.totalView && !dtlBreakdownData.outsView"
                                >
                                    Cash Balance
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(data, key) in orderSupplierInvoices">
                                <td
                                    v-if="dtlBreakdownData.type != 'owners'"
                                    style="padding-left: 12px"
                                >
                                    <v-tooltip
                                        top
                                        v-if="data.priority == 1"
                                    >
                                        <template v-slot:activator="{ on }">
                                            <v-icon
                                                v-on="on"
                                                color="blue darken-2"
                                                class="material-icons"
                                                >layers
                                            </v-icon>
                                        </template>
                                        <span>3 - Normal</span>
                                    </v-tooltip>
                                    <v-tooltip
                                        top
                                        v-else-if="data.priority == 2"
                                    >
                                        <template v-slot:activator="{ on }">
                                            <v-icon
                                                v-on="on"
                                                color="yellow darken-2"
                                                class="material-icons"
                                                >layers
                                            </v-icon>
                                        </template>
                                        <span>2 - Medium</span>
                                    </v-tooltip>
                                    <v-tooltip
                                        top
                                        v-else-if="data.priority == 3"
                                    >
                                        <template v-slot:activator="{ on }">
                                            <v-icon
                                                v-on="on"
                                                color="red darken-2"
                                                class="material-icons"
                                                >layers
                                            </v-icon>
                                        </template>
                                        <span>1 - High</span>
                                    </v-tooltip>
                                    <v-tooltip
                                        top
                                        v-else
                                    >
                                        <template v-slot:activator="{ on }">
                                            <v-icon
                                                v-on="on"
                                                color="gray darken-2"
                                                class="material-icons"
                                                >layers
                                            </v-icon>
                                        </template>
                                        <span>0 - None</span>
                                    </v-tooltip>
                                </td>
                                <td
                                    class="txts-date"
                                    v-if="dtlBreakdownData.type == 'suppliers'"
                                >
                                    {{ formatDate(data.trans_date) }}
                                </td>
                                <td
                                    class="txts-date"
                                    v-if="dtlBreakdownData.type == 'suppliers'"
                                >
                                    {{ formatDate(data.due_date) }}
                                </td>
                                <td>
                                    <span v-if="dtlBreakdownData.type != 'suppliers'">{{ data.fee_type }}</span>
                                    <span v-if="dtlBreakdownData.type == 'suppliers'">{{ data.trans_type }}</span>
                                </td>
                                <td v-if="dtlBreakdownData.type == 'suppliers'">
                                    <span>{{ data.creditor + ' - ' + data.creditor_name }}</span>
                                </td>
                                <td v-if="dtlBreakdownData.type == 'suppliers'">{{ payBys[data.pay_by] }}</td>
                                <td v-if="dtlBreakdownData.type == 'suppliers'">{{ data.inv_no }}</td>
                                <td>{{ data.account }} - {{ data.account_name }}</td>
                                <td>
                                    <span
                                        v-if="dtlBreakdownData.type == 'managements' && dtlBreakdownData.partitioned"
                                        >{{ data.desc2 }}</span
                                    >
                                    <span v-if="!dtlBreakdownData.partitioned">{{ data.desc }}</span>
                                    <span
                                        v-if="
                                            dtlBreakdownData.type == 'management' &&
                                            !dtlBreakdownData.partitioned &&
                                            data.adhoc == 0 &&
                                            data.paid_fees_amount != 0
                                        "
                                        style="font-weight: bold"
                                    >
                                        - Fees invoiced to date: ${{ formatAsCurrency(data.paid_fees_amount, 2) }} (${{
                                            formatAsCurrency(data.paid_fees_tax, 2)
                                        }})</span
                                    >
                                    <span
                                        v-if="
                                            dtlBreakdownData.type == 'management' &&
                                            !dtlBreakdownData.partitioned &&
                                            data.adhoc == 1
                                        "
                                    >
                                        (one-off)</span
                                    >
                                    <span
                                        v-if="
                                            dtlBreakdownData.type == 'management' &&
                                            !dtlBreakdownData.partitioned &&
                                            data.alwaysCharge == 1
                                        "
                                    >
                                        (always charged)</span
                                    >
                                    <span v-if="data.fee_type == 'Owner Remittance'"
                                        >(Paid ${{ formatAsCurrency(data.amount_paid, 2) }}) -
                                        {{ data.percentage }}%</span
                                    >
                                    <span v-if="data.fee_type == 'Owner Remittance' && data.fixed_amount != 0"
                                        >Fixed Amount ${{ formatAsCurrency(data.fixed_amount, 2) }}</span
                                    >
                                </td>
                                <td
                                    v-if="dtlBreakdownData.type == 'suppliers'"
                                    align="center"
                                >
                                    <span v-if="data.on_hold == true">
                                        <sui-checkbox
                                            v-model="data.on_hold"
                                            data-inverted=""
                                            :disabled="true"
                                            class="checkbox-round"
                                            style="background: #f2711c"
                                        />
                                    </span>
                                    <span v-else>
                                        <sui-checkbox
                                            v-model="data.on_hold"
                                            data-inverted=""
                                            :disabled="true"
                                        />
                                    </span>
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    {{ formatAsCurrency(data.amount, 2) }}
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                    v-bind:class="'txt-' + data.act"
                                >
                                    <span v-bind:class="{ 'txt-orange': data.allocated < data.amount }">{{
                                        formatAsCurrency(data.allocated, 2)
                                    }}</span>
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                    v-if="dtlBreakdownData.totalView && !dtlBreakdownData.outsView"
                                >
                                    <span
                                        v-if="
                                            data.trans_type == 'INV' || (data.trans_type == 'CRE' && data.act == 'pay')
                                        "
                                        >{{ formatAsCurrency(data.after, 2) }}</span
                                    >
                                </td>
                            </tr>
                            <tr>
                                <td
                                    class="txts-date"
                                    v-if="dtlBreakdownData.type == 'suppliers'"
                                    colspan="8"
                                ></td>
                                <td
                                    class="txts-date"
                                    v-else-if="dtlBreakdownData.type == 'owners'"
                                    colspan="2"
                                >
                                    <span v-if="propertyWithholdAmount > 0"
                                        ><i
                                            >** Amount to withhold from property ${{
                                                formatAsCurrency(this.propertyWithholdAmount, 2)
                                            }}</i
                                        ></span
                                    >
                                </td>
                                <td
                                    v-else
                                    colspan="3"
                                ></td>
                                <td
                                    align="right"
                                    v-if="dtlBreakdownData.type == 'suppliers'"
                                    colspan="2"
                                >
                                    Total Amounts
                                </td>
                                <td
                                    v-else
                                    align="right"
                                >
                                    Total Amounts
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    <b>{{ formatAsCurrency(dtlBreakdownData.totalAmount, 2) }}</b>
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                >
                                    <b>{{ formatAsCurrency(dtlBreakdownData.totalAlloc, 2) }}</b>
                                </td>
                                <td
                                    class="nums"
                                    align="right"
                                    v-if="dtlBreakdownData.totalView && !dtlBreakdownData.outsView"
                                ></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </v-card>
        </v-dialog>

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon>
                    WARNING - Properties selected {{ checkProperties }} of
                    {{ propertyListed }}
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogConfirmation = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">{{ warning_message }}</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="continueProcess()"
                        >Confirm
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogConfirmation = false"
                        >Cancel
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->

        <!-- PROCESS DATA -->
        <div
            class="page-form c8-no-padding success-part"
            style="
                margin-top: 10px;
                box-shadow:
                    0 3px 1px -2px rgb(0 0 0 / 20%),
                    0 2px 2px 0 rgb(0 0 0 / 14%),
                    0 1px 5px 0 rgb(0 0 0 / 12%);
            "
            v-if="showProcess"
        >
            <!--CHECKED DETAIL-->
            <div v-for="(tbl, code) in resultsTbls">
                <!--PAID INVOICES-->
                <template>
                    <v-data-table
                        v-if="code == 'paid_invoices_list'"
                        v-model="paidInvoice"
                        :headers="paidInvoiceHeaders"
                        :items="tbl.datas"
                        :multiple="paidInvoiceMultiple"
                        item-key="batch_no"
                        :hide-default-footer="true"
                        no-data-text="There was no transaction process on the selected properties"
                        class="elevation-1 processed-payment-table"
                        style="font-size: 10px; border-radius: 0px"
                    >
                        <template v-slot:top>
                            <v-toolbar
                                flat
                                color="green"
                                height="30px;"
                                class="payment-list-header"
                            >
                                <v-toolbar-title style="font-size: 12px; color: #ffffff; font-weight: bold"
                                    >{{ tbl.title }}
                                </v-toolbar-title>
                                <v-spacer></v-spacer>
                            </v-toolbar>
                        </template>
                        <template v-slot:item.pay_by_code="{ item }">
                            <table class="processed-payment-table">
                                <tr>
                                    <td
                                        style="
                                            height: 20px;
                                            border-bottom: none;
                                            font-weight: normal;
                                            text-align: left;
                                            font-size: 12px;
                                        "
                                    >
                                        {{ payByCodes[item.pay_by_code] }}
                                    </td>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:item.payment_code="{ item }">
                            <table class="processed-payment-table">
                                <tr>
                                    <td
                                        style="
                                            height: 20px;
                                            border-bottom: none;
                                            font-weight: normal;
                                            text-align: left;
                                            font-size: 12px;
                                        "
                                    >
                                        <a v-on:click="proceedtolinks(item)">{{ payLinks[item.payment_code] }}</a>
                                    </td>
                                </tr>
                            </table>
                        </template>
                    </v-data-table>
                </template>
                <!--CREATED INVOICES-->
                <template>
                    <v-data-table
                        v-if="code == 'created_invoices_total'"
                        v-model="createdInvoice"
                        :headers="createdInvoiceHeaders"
                        :items="tbl.datas"
                        :multiple="createdInvoiceMultiple"
                        item-key="property_id"
                        class="elevation-1 payment-process-table"
                        no-data-text="There was no transaction process on the selected properties"
                        style="font-size: 10px; border-radius: 0px"
                    >
                        <template v-slot:top>
                            <v-toolbar
                                flat
                                color="orange"
                                height="30px;"
                                class="payment-list-header"
                            >
                                <v-toolbar-title style="font-size: 12px; color: #ffffff; font-weight: bold"
                                    >{{ tbl.title }}
                                </v-toolbar-title>
                                <v-spacer></v-spacer>
                            </v-toolbar>
                        </template>
                        <template v-slot:item.inv_ref="{ item }">
                            <table width="100%">
                                <tr class="form-row c8-no-padding">
                                    <td
                                        style="
                                            height: 20px;
                                            border-bottom: none;
                                            font-weight: normal;
                                            text-align: left;
                                            font-size: 12px;
                                        "
                                        v-on:click="downloadAgentInvoice(item)"
                                    >
                                        <a>{{ item.inv_ref }}</a>
                                    </td>
                                </tr>
                            </table>
                        </template>
                        <template v-slot:item.trans_amt="{ item }">
                            <table width="100%">
                                <tr class="form-row c8-no-padding">
                                    <td
                                        class="text-right"
                                        style="font-size: 12px"
                                    >
                                        {{ formatAsCurrency(item.trans_amt, 2) }}
                                    </td>
                                </tr>
                            </table>
                        </template>
                        <template
                            v-slot:body.append
                            v-if="tbl.datas.length > 0"
                        >
                            <tr class="form-row c8-no-padding group-total-row">
                                <td class="text-start">Total</td>
                                <td
                                    class="text-start"
                                    v-on:click="downloadAllAgentInvoice(tbl.datas)"
                                >
                                    <a>Download All Invoice</a>
                                </td>
                                <td class="text-right">{{ formatAsCurrency(totalInvoice, 2) }}</td>
                            </tr>
                        </template>
                    </v-data-table>
                </template>
                <!--ERROR INVOICES-->
                <template>
                    <v-data-table
                        v-if="tbl.datas.length > 0 && code == 'error_invoices'"
                        v-model="errorInvoice"
                        :headers="errorInvoiceHeaders"
                        :items="tbl.datas"
                        :multiple="errorInvoiceMultiple"
                        item-key="property"
                        show-select
                        class="elevation-1 payment-process-table"
                        style="font-size: 10px; border-radius: 0px"
                    >
                        <template v-slot:top>
                            <v-toolbar
                                flat
                                color="red"
                                height="30px;"
                                class="payment-list-header"
                            >
                                <v-toolbar-title style="font-size: 12px; color: #ffffff; font-weight: bold"
                                    >{{ tbl.title }}
                                </v-toolbar-title>
                                <v-spacer></v-spacer>
                            </v-toolbar>
                        </template>
                    </v-data-table>
                </template>
                <!--SKIPPED INVOICES-->
                <template>
                    <v-data-table
                        v-if="tbl.datas.length > 0 && code == 'skipped_invoices'"
                        v-model="skippedInvoice"
                        :headers="skippedInvoiceHeaders"
                        :items="tbl.datas"
                        :multiple="skippedInvoiceMultiple"
                        item-key="property"
                        show-select
                        class="elevation-1 payment-process-table"
                        style="font-size: 10px; border-radius: 0px"
                    >
                        <template v-slot:top>
                            <v-toolbar
                                flat
                                color="grey"
                                height="30px;"
                                class="payment-list-header"
                            >
                                <v-toolbar-title style="font-size: 12px; color: #ffffff; font-weight: bold"
                                    >{{ tbl.title }}
                                </v-toolbar-title>
                                <v-spacer></v-spacer>
                            </v-toolbar>
                        </template>
                    </v-data-table>
                </template>
            </div>
            <!--UNCHECK DETAIL-->
            <template>
                <v-data-table
                    v-if="uncheckeds.datas.length > 0"
                    v-model="unCheckedPaymentModel"
                    :headers="unCheckedHeaders"
                    :items="uncheckeds.datas"
                    :multiple="unCheckedMultiple"
                    item-key="property"
                    show-select
                    class="elevation-1 payment-process-table"
                    style="font-size: 10px; border-radius: 0px"
                >
                    <template v-slot:top>
                        <v-toolbar
                            flat
                            color="#00baf2"
                            height="30px;"
                            class="payment-list-header"
                        >
                            <v-toolbar-title style="font-size: 12px; color: #ffffff; font-weight: bold"
                                >Unchecked Properties
                            </v-toolbar-title>
                            <v-spacer></v-spacer>
                        </v-toolbar>
                    </template>
                </v-data-table>
            </template>
            <!--BUTTONS-->
            <v-card-actions
                style="
                    background: rgb(249, 249, 249);
                    border-top-left-radius: 0;
                    border-top-right-radius: 0;
                    border-bottom-left-radius: 4px;
                    border-bottom-right-radius: 4px;
                    box-shadow:
                        0 2px 1px -1px rgba(0, 0, 0, 0.2),
                        0 1px 1px 0 rgba(0, 0, 0, 0.14),
                        0 1px 3px 0 rgba(0, 0, 0, 0.12) !important;
                "
            >
                <v-spacer></v-spacer>
                <v-col
                    class="py-2"
                    align="right"
                >
                    <v-btn
                        class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only"
                        color="light"
                        depressed
                        elevation="0"
                        small
                        style="color: #8d8d8d"
                        @click="goBack()"
                        v-text="'Back to Quick Payments'"
                    />

                    <v-btn
                        class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only"
                        color="light"
                        depressed
                        elevation="0"
                        small
                        style="color: #8d8d8d"
                        @click="processAgain()"
                        v-text="'Process Again'"
                    />

                    <v-btn
                        class="ui-button ui-widget ui-state-default ui-corner-all ui-button-text-only"
                        color="light"
                        depressed
                        elevation="0"
                        small
                        style="color: #8d8d8d"
                        @click="processToSingle()"
                        v-text="'Process to Single Payment'"
                    />
                </v-col>
            </v-card-actions>
        </div>
        <!-- snackbarError -->
        <template>
            <div class="text-center">
                <v-snackbar
                    top
                    right
                    color="error"
                    v-model="snackbarError"
                    :timeout="snackbarErrorTimeout"
                >
                    {{ snackbarErrorText }}
                </v-snackbar>
            </div>
        </template>
    </div>
</template>

<script>
import Vue from 'vue';
import SuiVue from 'semantic-ui-vue';
import global_mixins from '../../../../plugins/mixins';
import { mapActions, mapMutations, mapState } from 'vuex';
import axios from 'axios';
import _ from 'lodash';

Vue.use(SuiVue);
export default {
    data() {
        return {
            property_manager_label: 'Property Manager',
            dialogConfirmation: false,
            warning_message:
                'The processing might take several minutes during which you cannot do other processing depending on the number of properties selected, click Confirm to proceed or Cancel to go back',
            proceedPayment: false,
            checkProperties: 0,
            propertyListed: 0,

            page_title: 'Quick Payments',
            loading_page_setting: false,
            loading_setting: false,
            loading_progressBar: false,
            loading_compiledata: false,
            progressBar: 0,
            showFormPart: true,
            showPayableDetails: false,
            formData: {},

            /* SNACKBAR ERROR */
            snackbarError: false,
            snackbarErrorText: '',
            snackbarErrorTimeout: 2000,

            /* PARAMETERS */
            /* PAYMENT TYPE DEFAULT */
            PaymentRun: 0,
            PaymentRunItem: [],

            /* PAYMENT RUN DATE DEFAULT */

            runDate: new Date().toISOString().substr(0, 10),
            dateFormatted: this.formatDate(new Date().toISOString().substr(0, 10)),
            paymentRunDate: false,

            /* WITH INVOICES DEFAULT */
            checkboxWithInvoice: false,
            propertiesWithInvoice: [],

            /* WITH BALANCES DEFAULT */
            checkboxWithBal: false,
            withBalanceNote: '* Do not use this option for month end payment runs as management fees may be missed.',

            /* BANK ACCOUNT DEFAULT */
            bankID: '',
            BankAccount: '',
            BankAccountList: [],
            showPropertyParameter: false,
            showPropertyListParameter: false,

            /* SELECT BY DEFAULT */
            propertyBy: 0,
            propertyBys: [
                { label: 'Property Manager', value: 'propertyManagersList' },
                { label: 'Supplier', value: 'suppliersList' },
                { label: 'Property Group', value: 'propertyGroupList' },
                { label: 'Payment Run Group', value: 'paymentRunGroupList' },
            ],

            /* SELECT BY PM/SUP/PG DEFAULT */
            selectBy: 'propertyManagersList',
            selectByName: 'Property Manager',
            selectByBtnLabel: 'Select Property Manager',
            selectByPropModel: [],
            allPropertyManager: [
                {
                    fieldGroupValues: [
                        { fieldKey: '', fieldValue: '', field_key: '', field_value: '', label: '', value: '' },
                    ],
                },
            ],
            availablePropBy: [],
            allPropMgr: [],
            allPropertyGroup: [],
            allPaymentRunGroup: [],
            allSuppliers: [],

            /* SELECT BY PROPERTY DEFAULT */
            selectByPropertyModel: [],
            allProperties: [],
            propertiesList: [
                {
                    fieldGroupValues: [
                        { fieldKey: '', fieldValue: '', field_key: '', field_value: '', label: '', value: '' },
                    ],
                },
            ],

            /* DISPLAY PAYMENT LIST DEFAULT */
            updateBtnLabel: 'Update List',

            /* PAYMENT LIST DEFAULT */
            selectedData: [],
            searchDtl: '',
            headers: [
                { text: 'Property', value: 'property_name', sortable: false, width: '21%' },
                { text: 'Available Funds', sortable: false, value: 'availableFunds', width: '11%', align: 'right' },
                { text: 'Management Fee & Sundries', value: 'managementFeeSundries', sortable: false, width: '22%' },
                { text: 'Supplier Invoices', value: 'supplierInvoices', sortable: false, width: '22%' },
                { text: 'Owner Payments', value: 'ownerPayment', sortable: false, width: '11%', align: 'right' },
                { text: 'Closing Balance', value: 'closingBal', sortable: false, width: '11%', align: 'right' },
                { text: '', value: 'data-table-expand', sortable: false, width: '2%' },
            ],

            pdDetails: [],
            pdProperties: [],
            singleExpand: true,
            multiple: true,
            expanded: [],
            loading: false,

            /* MODAL VIEW OF AVAILABLE FUNDS */
            dialogOpeningBreakdown: false,
            dtlOpeningBreakdownData: [],
            openingBreakdownModal: {
                title: 'Available Funds Details',
                partitioned: false,
                datas: {},
                actDatas: {},
                tax: 0,
                totals: { opening: 0, income: 0, expenses: 0, distributions: 0, tax: 0 },
                columns: { O: 'Owners', V: 'Outgoings', D: 'Recoverables' },
                rows: {
                    opening: 'Opening GL Bank Balance',
                    income: 'Income',
                    expenses: 'Expenses',
                    distributions: 'Owner Remittance and Other Balance Sheet Movements',
                    tax: 'GST',
                },
                closing: { O: 0, V: 0, D: 0 },
                overall: 0,
                blanks: ['Ddistributions', 'Vdistributions', 'Vtax', 'Dtax'],
                actRows: {
                    uncleared: 'Uncleared Funds	',
                    unalloc: 'Unallocated Funds',
                    futureNetAmount: 'Future Dated Receipts',
                    tax: 'Net GST',
                },
            },

            /* MODAL VIEW OF MANAGEMENT FEES & SUNDRIES / SUPPLIER INVOICES METHOD */
            dialogBreakdown: false,
            dtlBreakdownData: [],
            breakdownModal: {
                outsView: false,
                totalView: false,
                partitioned: false,
                title: '',
                type: '',
                datas: [],
                totalAmount: 0,
                totalAlloc: 0,
                curPage: 0,
                perPage: 10,
            },
            payPriosTxts: ['Priority 4', 'Priority 3', 'Priority 2', 'Priority 1'],
            payBys: { 1: 'EFT', 2: 'BPay', 3: 'Cheque' },

            /* PROCESS PAYMENT */
            trustAccountsList: [],
            uncheckeds: { chAll: false, datas: [] },
            resultsTbls: {
                paid_invoices: {
                    title: 'Successful Processed Payments',
                    color: 'green',
                    chAll: false,
                    datas: [],
                    curPage: 0,
                    perPage: 10,
                },
                paid_invoices_list: {
                    title: 'Successful Processed Payments',
                    color: 'green',
                    chAll: false,
                    datas: [],
                    curPage: 0,
                    perPage: 10,
                },
                created_invoices_total: {
                    title: 'New Created Invoices',
                    color: 'orange',
                    chAll: false,
                    datas: [],
                    curPage: 0,
                    perPage: 10,
                },
                error_invoices: {
                    title: 'Failed to Process Payments',
                    color: 'red',
                    chAll: false,
                    datas: [],
                    curPage: 0,
                    perPage: 10,
                },
                skipped_invoices: {
                    title: 'Skipped Properties',
                    color: 'gray',
                    chAll: false,
                    datas: [],
                    curPage: 0,
                    perPage: 10,
                },
            },
            forBatchPayments: [],

            /* PROCESS DATA */
            showProcess: false,
            payByCodes: { E: 'EFT', B: 'BPay', C: 'Cheque' },
            payLinks: { E: 'Generate EFT', B: 'Generate BPay', C: 'Print Cheque' },

            paidInvoice: [],
            paidInvoiceHeaders: [
                { text: 'Type', value: 'pay_by_code', sortable: false, width: '10%' },
                { text: 'Batch No.', value: 'batch_no', sortable: false, width: '20%' },
                { text: 'No. of Transaction', value: 'count', sortable: false, width: '20%' },
                { text: 'Links', value: 'payment_code', sortable: false },
            ],
            paidInvoiceMultiple: true,

            createdInvoice: [],
            createdInvoiceHeaders: [
                { text: 'Property', value: 'property_name', sortable: false, width: '40%' },
                { text: 'Agent Invoice No.', value: 'inv_ref', sortable: false, width: '10%' },
                { text: 'Amount', value: 'trans_amt', sortable: false, align: 'right' },
            ],
            totalInvoice: 0,
            createdInvoiceMultiple: true,

            errorInvoice: [],
            errorInvoiceHeaders: [
                { text: 'Property', value: 'property_name', sortable: false, width: '30%' },
                { text: 'Description', value: 'errors', sortable: false },
            ],
            errorInvoiceMultiple: true,

            skippedInvoice: [],
            skippedInvoiceHeaders: [
                { text: 'Property', value: 'property_name', sortable: false, width: '30%' },
                { text: 'Description', value: 'errors', sortable: false },
            ],
            skippedInvoiceMultiple: true,

            unCheckedPaymentModel: [],
            unCheckedHeaders: [{ text: 'Property', value: 'property_name', sortable: false }],
            unCheckedMultiple: true,

            totalAvailableFunds: 0,
            totalMgtFeesDue: 0,
            totalMgtFeePayReserve: 0,
            totalSupplierUnpaidInvoice: 0,
            totalSupplierPay: 0,
            totalOwnerPayments: 0,
            totalClosingBalance: 0,
            processingEnabled: 0,
            propertyWithholdAmount: 0,
            taxLabel: 'GST',
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.fetchParameters();
        this.loadCountryDefaults();
        this.fetchProperties();
    },
    computed: {
        ...mapState(['cirrus8_api_url', 'currentDB', 'user_type', 'un']),
        orderSupplierInvoices: function () {
            return _.orderBy(
                this.dtlBreakdownData.datas,
                ['priority', 'fee_type', 'due_date', 'inv_no', 'seq_no', 'run_amount'],
                ['desc', 'asc', 'asc', 'asc', 'asc', 'desc'],
            );
        },
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.property_manager_label = this.ucwords(this.country_defaults.property_manager);
                this.propertyBys = [
                    { label: this.property_manager_label, value: 'propertyManagersList' },
                    { label: 'Supplier', value: 'suppliersList' },
                    { label: 'Property Group', value: 'propertyGroupList' },
                    { label: 'Payment Run Group', value: 'paymentRunGroupList' },
                ];
                this.selectByName = this.property_manager_label;
                this.selectByBtnLabel = 'Select ' + this.property_manager_label;
            });
        },
        updatePagination(pagination) {
            this.processingEnabled = pagination.page == pagination.pageCount;
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },

        ...mapActions([]),
        ...mapMutations([]),
        /* INITIAL LOAD */
        fetchParameters: function () {
            this.loading_setting = true;
            this.$api.post('quick-payment/payment/get-parameters', this.formData).then((response) => {
                this.PaymentRunItem = response.data.paymentRuns;
                this.BankAccountList = response.data.bankAccounts;
                this.trustAccountsList = response.data.bankAccounts;
                this.allPropertyGroup = response.data.propertyGroups;
                this.allPaymentRunGroup = response.data.paymentRunGroups;
                this.allSuppliers = response.data.suppliers;
                this.availablePropBy = response.data.propertyManager;
                this.allPropMgr = response.data.propertiesManGrp;
                this.taxLabel = response.data.taxLabel;
                this.openingBreakdownModal.rows['tax'] = this.taxLabel;
                this.openingBreakdownModal.actRows['tax'] = 'Net ' + this.taxLabel;
            });
        },

        fetchProperties: function (mgrID) {
            this.loading_setting = true;
            if (mgrID) this.formData.managerID = mgrID;
            this.formData.bankAccount = this.BankAccount;
            this.$api.post('quick-payment/payment/property-per-portfolio', this.formData).then((response) => {
                this.allProperties = [{ fieldGroupValues: response.data.data[0].fieldGroupValues }];
            });
        },

        fetchPropertyWithInvoice: function () {
            this.formData.bankAccount = this.BankAccount;
            this.formData.runDate = this.dateFormatted;
            this.formData.withInvoice = this.checkboxWithInvoice;
            this.formData.withBalance = this.checkboxWithBal;
            this.formData.selectBy = this.selectBy;
            this.$api.post('quick-payment/payment/property-with-invoice', this.formData).then((response) => {
                this.propertiesWithInvoice = response.data.propWithInvoices;
                this.updatePropertiesList();
            });
        },

        /* PAYMENT RUN METHOD */
        changePaymentType: function (item) {
            this.fetchParameters();
            this.showPayableDetails = false;
            // Refresh Property List
            this.updatePropertiesList();
            this.updatePropertyList();
        },

        /* PAYMENT RUN DATE METHOD */
        formatDate: function (runDate) {
            if (!runDate) return null;
            const [year, month, day] = runDate.split('-');
            return `${day}/${month}/${year}`;
        },
        parseDate: function (runDate) {
            if (!runDate) return null;
            const [day, month, year] = runDate.split('/');
            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        },

        /* WITH INVOICES METHOD */
        setWithInvoice: function () {
            if (this.BankAccount) this.updatePropertyList();
        },

        /* WITH BALANCES METHOD */
        setWithBalance: function () {
            if (this.BankAccount) this.updatePropertyList();
        },

        /* BANK ACCOUNT METHOD */
        changeBankAccount: function (bankValue) {
            if (bankValue) {
                this.showPropertyParameter = true;
                if (this.checkboxWithInvoice || this.checkboxWithBal) {
                    this.fetchPropertyWithInvoice();
                }
                this.propertyBy = 0;
                this.showPropertyListParameter = true;
                this.changeSelectBy('propertyManagersList');
                this.showPayableDetails = false;
            } else {
                /* RESET DUAL LIST */
                this.resetSelectedPropBy();
                this.resetSelectedProperties();

                this.showPropertyParameter = false;
                this.showPropertyListParameter = false;
                this.showPayableDetails = false;
            }
        },

        /* TO RESET DUAL LIST */
        resetSelectedPropBy: function () {
            if (this.$refs.refPropModel && this.$refs.refPropModel.value.length > 0)
                this.$refs.refPropModel.deSelectAllOption();
        },
        resetSelectedProperties: function () {
            if (this.$refs.refPropertyModel && this.$refs.refPropertyModel.value.length > 0)
                this.$refs.refPropertyModel.deSelectAllOption();
        },

        /* SELECT BY METHOD */
        updatePropByList: function (propByData, value, label) {
            let propByLists = [];
            for (var i = 0; i < propByData.length; i++) {
                propByLists[i] = {
                    fieldKey: propByData[i][value],
                    fieldValue: propByData[i][label],
                    field_key: propByData[i][value],
                    field_value: propByData[i][label],
                    value: propByData[i][value],
                    label: propByData[i][label],
                };
            }
            return [{ fieldGroupValues: propByLists }];
        },
        changeSelectBy: function (selectID) {
            this.selectBy = selectID;
            if (this.BankAccount) this.showPropertyListParameter = true;
            this.resetSelectedPropBy();
            if (selectID == 'propertyManagersList') {
                this.selectByName = this.property_manager_label;
                this.selectByBtnLabel = 'Select ' + this.property_manager_label;
                this.allPropertyManager = this.availablePropBy;
                if (this.$refs.refPropModel != undefined) this.$refs.refPropModel.option1 = this.availablePropBy;
            }
            if (selectID == 'suppliersList') {
                this.selectByName = 'Supplier';
                this.selectByBtnLabel = 'Select Supplier/s';
                this.allPropertyManager = this.updatePropByList(this.allSuppliers, 'fieldKey', 'fieldValue');
                if (this.$refs.refPropModel != undefined)
                    this.$refs.refPropModel.option1 = this.updatePropByList(
                        this.allSuppliers,
                        'fieldKey',
                        'fieldValue',
                    );
            }
            if (selectID == 'propertyGroupList') {
                this.selectByName = 'Property Group';
                this.selectByBtnLabel = 'Select Property Group/s';
                this.allPropertyManager = this.updatePropByList(this.allPropertyGroup, 'value', 'label');
                if (this.$refs.refPropModel != undefined)
                    this.$refs.refPropModel.option1 = this.updatePropByList(this.allPropertyGroup, 'value', 'label');
            }
            if (selectID == 'paymentRunGroupList') {
                this.selectByName = 'Payment Run Group';
                this.selectByBtnLabel = 'Select Payment Run Group/s';
                this.allPropertyManager = this.updatePropByList(this.allPaymentRunGroup, 'value', 'label');
                if (this.$refs.refPropModel != undefined)
                    this.$refs.refPropModel.option1 = this.updatePropByList(this.allPaymentRunGroup, 'value', 'label');
            }
            this.updatePropertyList();
        },

        /* UPDATE PROPERTIES LIST BASE ON WITH INVOICES */
        updatePropertiesList: function () {
            let propWithInvoice = $.map(this.propertiesWithInvoice, function (data, index) {
                return data.propertyID;
            });
            let propLists = [];
            for (var i = 0; i < this.allProperties[0].fieldGroupValues.length; i++) {
                let propRow = this.allProperties[0].fieldGroupValues[i];
                if (this.checkboxWithInvoice || this.checkboxWithBal) {
                    if ($.inArray(this.allProperties[0].fieldGroupValues[i]['value'], propWithInvoice) != -1) {
                        propLists.push(propRow);
                    }
                } else {
                    propLists.push(propRow);
                }
            }
            this.propertiesList = [{ fieldGroupValues: propLists }];
            if (this.$refs.refPropertyModel != undefined)
                this.$refs.refPropertyModel.option1 = [{ fieldGroupValues: propLists }];
        },

        /* GET PROPERTIES ON CHANGE OF SELECT BY*/
        updateOnChanegSelectBy: function () {
            let selectedValue = this.selectByPropModel[0].value;
            if (this.selectBy == 'propertyManagersList') {
                this.fetchPortfolio(selectedValue);
            }
            if (this.selectBy == 'suppliersList') {
                this.fetchPortfolio(selectedValue);
            }
            if (this.selectBy == 'propertyGroupList') {
                this.fetchPortfolio(selectedValue);
            }
            if (this.selectBy == 'paymentRunGroupList') {
                this.fetchPortfolio(selectedValue);
            }
        },

        /* UPDATE PROPERTY LIST BASE ON SELECTED BY */
        updatePropertyList: function () {
            let validated = true;
            if (validated) {
                this.resetSelectedProperties();
                if (this.selectByPropModel.length > 0) {
                    let selectPropBy = this.selectByPropModel;
                    if (this.selectBy == 'propertyManagersList') {
                        let propertyWithManGrp = this.allPropMgr;
                        let propertyWithInvoice = this.propertiesWithInvoice;
                        let dataProp = [];
                        for (var i = 0; i < selectPropBy.length; i++) {
                            for (var p = 0; p < propertyWithManGrp.length; p++) {
                                if (propertyWithManGrp[p].propManager == selectPropBy[i].value) {
                                    if (this.checkboxWithInvoice || this.checkboxWithBal) {
                                        for (var v = 0; v < propertyWithInvoice.length; v++) {
                                            if (propertyWithManGrp[p].value == propertyWithInvoice[v].propertyID) {
                                                dataProp.push(propertyWithManGrp[p].value);
                                            }
                                        }
                                    } else {
                                        dataProp.push(propertyWithManGrp[p].value);
                                    }
                                }
                            }
                        }
                        this.fetchFilteredProperty(dataProp);
                    }
                    if (this.selectBy == 'suppliersList') {
                        if (this.checkboxWithInvoice || this.checkboxWithBal) {
                            this.fetchSupplierWithInvoiceProperties();
                        } else {
                            this.fetchSupplierProperties();
                        }
                    }
                    if (this.selectBy == 'propertyGroupList') {
                        let propertyGrp = this.allPropMgr;
                        let dataPropGrp = [];
                        for (var i = 0; i < selectPropBy.length; i++) {
                            for (var p = 0; p < propertyGrp.length; p++) {
                                if (propertyGrp[p].propGroup == selectPropBy[i].value) {
                                    dataPropGrp.push(propertyGrp[p].value);
                                }
                            }
                        }
                        this.fetchFilteredProperty(dataPropGrp);
                    }
                    if (this.selectBy == 'paymentRunGroupList') {
                        let propertyPayGrp = this.allPropMgr;
                        let dataPropPayGrp = [];
                        for (var i = 0; i < selectPropBy.length; i++) {
                            for (var p = 0; p < propertyPayGrp.length; p++) {
                                if (propertyPayGrp[p].payGroup == selectPropBy[i].value) {
                                    dataPropPayGrp.push(propertyPayGrp[p].value);
                                }
                            }
                        }
                        this.fetchFilteredProperty(dataPropPayGrp);
                    }
                } else {
                    if (this.checkboxWithInvoice || this.checkboxWithBal) {
                        this.fetchPropertyWithInvoice();
                    } else {
                        let propLists = [];
                        for (var i = 0; i < this.allProperties[0].fieldGroupValues.length; i++) {
                            if (this.allProperties[0].fieldGroupValues[i].bank == this.BankAccount) {
                                let propRow = this.allProperties[0].fieldGroupValues[i];
                                propLists.push(propRow);
                            }
                        }
                        this.propertiesList = [{ fieldGroupValues: propLists }];
                        if (this.$refs.refPropertyModel)
                            this.$refs.refPropertyModel.option1 = [{ fieldGroupValues: propLists }];
                    }
                }

                if (this.BankAccount) this.showPropertyListParameter = true;
            }
        },

        fetchSupplierWithInvoiceProperties: function () {
            this.formData.bankAccount = this.BankAccount;
            this.formData.runDate = this.dateFormatted;
            this.formData.supplierIDs = this.selectByPropModel;
            this.$api.post('quick-payment/payment/property-supplier-with-invoice', this.formData).then((response) => {
                this.fetchFilteredProperty(response.data.propSupplierWithInvoices);
            });
        },

        fetchSupplierProperties: function () {
            this.formData.bankAccount = this.BankAccount;
            this.formData.runDate = this.dateFormatted;
            this.formData.supplierIDs = this.selectByPropModel;
            this.$api.post('quick-payment/payment/property-supplier', this.formData).then((response) => {
                this.fetchFilteredProperty(response.data.propertiesSupplier);
            });
        },

        fetchFilteredProperty: function (arrayProperty) {
            let propLists = [];
            for (var i = 0; i < this.allProperties[0].fieldGroupValues.length; i++) {
                let propRow = this.allProperties[0].fieldGroupValues[i];

                if ($.inArray(this.allProperties[0].fieldGroupValues[i]['value'], arrayProperty) != -1) {
                    propLists.push(propRow);
                }
            }
            this.propertiesList = [{ fieldGroupValues: propLists }];
            if (this.$refs.refPropertyModel) this.$refs.refPropertyModel.option1 = [{ fieldGroupValues: propLists }];
        },

        reloadPaymentRunDefaults: function () {
            this.$api.post('quick-payment/payment/get-parameters', this.formData).then((response) => {
                this.PaymentRunItem = response.data.paymentRuns;
                this.displayPaymentList();
            });
        },

        /* DISPLAY PAYMENT LIST */
        displayPaymentList: function () {
            /* Reload payment run defaults */
            /* Payment Details */
            this.selectedData = [];
            this.totalAvailableFunds = 0;
            this.totalMgtFeesDue = 0;
            this.totalMgtFeePayReserve = 0;
            this.totalSupplierUnpaidInvoice = 0;
            this.totalSupplierPay = 0;
            this.totalOwnerPayments = 0;
            this.totalClosingBalance = 0;
            let validated = true;
            let errorMessage = '';
            if (this.selectByPropertyModel == '') {
                if (errorMessage) errorMessage = this.selectByName + ' and property was empty.';
                else {
                    errorMessage = 'Property was empty.';
                }
                validated = false;
            }
            if (validated) {
                this.loading = true;
                this.pdDetails = [];
                this.formData.PaymentType = this.PaymentRunItem[this.PaymentRun];
                this.formData.PropMgr = this.selectByPropModel;
                this.formData.Property = this.selectByPropertyModel;
                this.formData.SelectedProp = this.selectByPropertyModel;
                this.formData.bankAccount = this.BankAccount;
                this.formData.run_date = this.dateFormatted;
                this.$api.post('quick-payment/payment/payable-list', this.formData).then((response) => {
                    if (response.data.status == 'success') {
                        this.pdProperties = response.data.propertiesOwners;
                        this.pdDetails = response.data.lists;
                        this.partitions = response.data.partitions;

                        this.showPayableDetails = true;
                    } else {
                        this.snackbarErrorText = response.data.errorMessage;
                        this.snackbarErrorTimeout = 5000;
                        this.snackbarError = true;
                    }
                    this.loading = false;
                });
            } else {
                this.showPayableDetails = false;
                this.snackbarErrorText = errorMessage;
                this.snackbarErrorTimeout = 5000;
                this.snackbarError = true;
            }
        },
        showPayableListReprocess: function () {
            this.$api.post('quick-payment/payment/payable-list', this.formData).then((response) => {
                this.pdProperties = this.pdProperties.concat(response.data.propertiesOwners);
                this.pdDetails = response.data.lists;
            });
            if (this.pdDetails.length > 0) {
                this.showPayableDetails = true;
            }
        },
        /* PAYMENT LIST FUNCTION */
        totalIt: function (obj) {
            let total = 0;
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    total += parseFloat(obj[key]);
                }
            }
            return total;
        },
        totalArr: function (arr, key) {
            let num = 0;
            for (var i = 0; i < arr.length; i++) {
                num += parseFloat(arr[i][key]);
            }
            return num;
        },
        totalIfDeep: function (obj, key) {
            let num = 0;
            for (let ob in obj) {
                if (obj.hasOwnProperty(ob)) {
                    let arr = obj[ob];
                    for (var i = 0; i < arr.length; i++) {
                        num += parseFloat(arr[i][key]);
                    }
                }
            }
            return num;
        },
        totalPaidperType: function (arr, key) {
            let num = 0;
            for (var i = 0; i < arr.length; i++) {
                num += parseFloat(arr[i][key]);
            }
            return num;
        },
        setPaidRsvdCellClass: function (paidrsvd, all) {
            let cellClass = '';
            let hasPay = false;
            let hasReserve = false;
            let hasIgnore = false;
            if (!all) {
                for (var i = 0; i < paidrsvd.length; i++) {
                    if (parseFloat(paidrsvd[i].allocated) > 0 && paidrsvd[i].act == 'pay') {
                        hasPay = true;
                    }
                    if (parseFloat(paidrsvd[i].allocated) > 0 && paidrsvd[i].act == 'reserve') {
                        hasReserve = true;
                    }
                    if (paidrsvd[i].act == 'ignore') {
                        hasIgnore = true;
                    }
                }
            } else {
                for (let typ in paidrsvd) {
                    for (var i = 0; i < paidrsvd[typ].length; i++) {
                        if (parseFloat(paidrsvd[typ][i].allocated) > 0 && paidrsvd[typ][i].act == 'pay') {
                            hasPay = true;
                        }
                        if (parseFloat(paidrsvd[typ][i].allocated) > 0 && paidrsvd[typ][i].act == 'reserve') {
                            hasReserve = true;
                        }
                        if (paidrsvd[typ][i].act == 'ignore') {
                            hasIgnore = true;
                        }
                    }
                }
            }
            if (hasIgnore) {
                cellClass = 'txt-gray';
            }
            if (hasReserve) {
                cellClass = 'txt-blue';
                cellClass += ' txt-bold';
            }
            if (hasPay) {
                cellClass = 'txt-green';
                cellClass += ' txt-bold';
            }
            return cellClass;
        },
        countPaid: function (arr) {
            let show = false;
            if (arr.constructor === Object) {
                for (let typ in arr) {
                    if (arr[typ].length > 0) {
                        for (var i = 0; i < arr[typ].length; i++) {
                            if (arr[typ][i].act != '' && arr[typ][i].act != 'ignore') {
                                show = true;
                                break;
                            }
                        }
                    }
                }
            } else {
                if (arr.length > 0) {
                    for (var i = 0; i < arr.length; i++) {
                        if (arr[i].act != '' && arr[i].act != 'ignore') {
                            show = true;
                            break;
                        }
                    }
                }
            }
            return show;
        },
        formatAsCurrency: function (amt, dec) {
            amt = parseFloat(amt);
            if (amt) {
                dec = dec || 0;
                if (amt < 0) {
                    amt = amt * -1;
                    if (amt.toFixed(2) != 0) {
                        return '(' + amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,') + ')';
                    } else {
                        return '0.00';
                    }
                } else {
                    return '' + amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,');
                }
            } else {
                return '0.00';
            }
        },

        /* PARTITIONED*/
        totalPaidAll: function (lists) {
            let total = 0;
            for (let type in lists) {
                if (lists.hasOwnProperty(type)) {
                    let typeList = lists[type];
                    for (var i = 0; i < typeList.length; i++) {
                        let row = typeList[i];
                        if (row.act == 'pay' || row.act == 'reserve') {
                            total += parseFloat(row.allocated);
                        }
                    }
                }
            }
            return total;
        },

        /* MODAL VIEW OF AVAILABLE FUNDS METHOD */
        modalOpeningBreakdown(details, actDetails, partitioned) {
            this.openingBreakdownModal.datas = details;
            this.openingBreakdownModal.actDatas = actDetails;
            this.openingBreakdownModal.partitioned = partitioned;
            this.openingBreakdownModal.overall = 0;
            // reset values to zero
            for (let code in this.openingBreakdownModal.closing) {
                if (this.openingBreakdownModal.closing.hasOwnProperty(code)) {
                    this.openingBreakdownModal.closing[code] = 0;
                }
            }
            for (let rowCode in this.openingBreakdownModal.totals) {
                if (this.openingBreakdownModal.totals.hasOwnProperty(rowCode)) {
                    this.openingBreakdownModal.totals[rowCode] = 0;
                }
            }
            for (let colCode in details) {
                if (details.hasOwnProperty(colCode)) {
                    let column = details[colCode];
                    let closing = 0;
                    for (let rowCode in column) {
                        if (column.hasOwnProperty(rowCode)) {
                            this.openingBreakdownModal.totals[rowCode] += parseFloat(column[rowCode]);
                            closing += parseFloat(column[rowCode]);
                        }
                    }
                    this.openingBreakdownModal.closing[colCode] = closing;
                }
            }
            let overall = 0;
            for (let rowCode in this.openingBreakdownModal.totals) {
                if (this.openingBreakdownModal.totals.hasOwnProperty(rowCode)) {
                    overall += this.openingBreakdownModal.totals[rowCode];
                }
            }
            let holdAmount = 0;
            for (let rowCode in actDetails) {
                if (actDetails.hasOwnProperty(rowCode)) {
                    if (actDetails[rowCode].hold && actDetails[rowCode].amount > 0) {
                        holdAmount += actDetails[rowCode].amount;
                    }
                }
            }

            this.openingBreakdownModal.overall = overall;
            this.openingBreakdownModal.availableFunds = overall - holdAmount;
            this.dtlOpeningBreakdownData = this.openingBreakdownModal;
            this.dialogOpeningBreakdown = true;
        },

        /* MODAL VIEW OF MANAGEMENT FEES & SUNDRIES / SUPPLIER INVOICES METHOD */
        modalBreakdown(title, partitioned, payArr, type, deep, totalView, outs) {
            this.breakdownModal.partitioned = partitioned;
            this.breakdownModal.title = title;
            this.breakdownModal.type = type;
            this.breakdownModal.totalView = totalView;
            if (outs) this.breakdownModal.outsView = true;
            else this.breakdownModal.outsView = false;
            let datas = [];
            if (deep) {
                for (let key in payArr) {
                    if (payArr.hasOwnProperty(key)) {
                        for (var i = 0; i < payArr[key].length; i++) {
                            datas.push(payArr[key][i]);
                        }
                    }
                }
            } else {
                for (var i = 0; i < payArr.length; i++) {
                    datas.push(payArr[i]);
                }
            }
            let totalAmount = 0;
            let totalAlloc = 0;
            for (var i = 0; i < datas.length; i++) {
                if (datas[i].trans_type != 'CRE') {
                    totalAmount += parseFloat(datas[i].amount);
                    totalAlloc += parseFloat(datas[i].allocated);
                }
            }

            this.breakdownModal.datas = datas;
            this.breakdownModal.totalAmount = totalAmount;
            this.breakdownModal.totalAlloc = totalAlloc;
            this.dtlBreakdownData = this.breakdownModal;
            this.dialogBreakdown = true;
        },

        /* MODAL VIEW OF OWNER PAYMENTS METHOD */
        modalTotalBreakdown(title, partitioned, payArr, type, withholdAmount) {
            let usePayArr = JSON.parse(JSON.stringify(payArr));
            let lists = [];
            let inLists = [];
            for (let key in usePayArr) {
                for (var i = 0; i < usePayArr[key].length; i++) {
                    let row = usePayArr[key][i];
                    if (type == 'suppliers') {
                        if (inLists.indexOf(row.batch_no + row.line_no) == -1) {
                            inLists.push(row.batch_no + row.line_no);
                            if (!row.cre_allocated) row.cre_allocated = 0;
                            row.desc2 = row.desc;
                            lists.push(row);
                        } else {
                            for (var li = 0; li < lists.length; li++) {
                                let listRow = lists[li];
                                if (listRow.batch_no + listRow.line_no == row.batch_no + row.line_no) {
                                    listRow.amount = parseFloat(listRow.amount) + parseFloat(row.amount);
                                    listRow.allocated = parseFloat(listRow.allocated) + parseFloat(row.allocated);
                                    if (row.cre_allocated && row.cre_allocated > 0) {
                                        listRow.cre_allocated =
                                            parseFloat(listRow.cre_allocated) + parseFloat(row.cre_allocated);
                                    }
                                    listRow.after = parseFloat(row.after);
                                    break;
                                }
                            }
                        }
                    } else if (type == 'management') {
                        if (inLists.indexOf(row.account) == -1) {
                            inLists.push(row.account);
                            row.after = parseFloat(row.after2);
                            row.desc2 = row.desc;
                            lists.push(row);
                        } else {
                            for (var li = 0; li < lists.length; li++) {
                                let listRow = lists[li];
                                if (listRow.account == row.account) {
                                    listRow.amount = parseFloat(listRow.amount) + parseFloat(row.amount);
                                    listRow.allocated = parseFloat(listRow.allocated) + parseFloat(row.allocated);
                                    listRow.after = parseFloat(row.after2);
                                    break;
                                }
                            }
                        }
                    } else {
                        lists.push(row);
                    }
                }
            }
            lists.sort(function (a, b) {
                if (a.trans_type == b.trans_type) return parseFloat(b.after) - parseFloat(a.after);
                return a.trans_type > b.trans_type ? 1 : -1;
            });
            if (withholdAmount > 0) {
                this.propertyWithholdAmount = withholdAmount;
            } else {
                this.propertyWithholdAmount = 0;
            }
            this.modalBreakdown(title, partitioned, lists, type, false, true);
        },
        /* PROCESS PAYMENT METHOD */
        continueProcess: function () {
            this.progressBar = 0;
            this.proceedPayment = true;
            this.processPayment();
        },
        processPayment: function () {
            this.totalInvoice = 0;
            let bankDet = this.getListRow(this.trustAccountsList, this.BankAccount);
            let last_recon_date = bankDet.last_recon_date;
            let run_date = this.runDate;
            let selPropData = $.map(this.selectedData, function (value, index) {
                return value.property;
            });
            let go = true;

            if (run_date <= last_recon_date) {
                go = false;
                this.snackbarErrorText =
                    'Payment run date must be after the last bank reconciliation date (' +
                    this.formatDate(last_recon_date) +
                    ').';
                this.snackbarErrorTimeout = 5000;
                this.snackbarError = true;
            }
            if (go) {
                let checkeds = [];
                let uncheckeds = [];
                let processProperty = [];
                for (var i = 0; i < this.pdDetails.length; i++) {
                    let row = this.pdDetails[i];
                    row.chAgain = false;
                    if ($.inArray(row.property, selPropData) !== -1) {
                        let propRow = [];
                        propRow.push(row);
                        checkeds.push(row);
                        processProperty.push(propRow);
                        row.ch = true;
                    } else {
                        uncheckeds.push(row);
                        row.ch = false;
                    }
                }
                this.uncheckeds.datas = uncheckeds;

                let actedProps = [];
                for (let code in this.resultsTbls) {
                    if (this.resultsTbls.hasOwnProperty(code)) {
                        this.resultsTbls[code].datas = [];
                        this.resultsTbls[code].chAll = false;
                    }
                }
                this.forBatchPayments = [];
                if (checkeds.length > 0) {
                    if (!this.proceedPayment) {
                        this.checkProperties = checkeds.length;
                        this.propertyListed = this.pdDetails.length;
                        this.dialogConfirmation = true;
                    } else {
                        this.dialogConfirmation = false;
                        this.loading_setting = true;
                        this.loading_progressBar = true;
                        this.showFormPart = false;
                        this.showPayableDetails = false;
                        this.processPerProperty(processProperty);
                    }
                } else {
                    this.snackbarErrorText = 'Please select properties to process';
                    this.snackbarError = true;
                }
            }
        },

        async processPerProperty(properties) {
            let actedProps = [];
            let payBatchNumber = [];
            for (let code in this.resultsTbls) {
                if (this.resultsTbls.hasOwnProperty(code)) {
                    this.resultsTbls[code].datas = [];
                    this.resultsTbls[code].chAll = false;
                }
            }
            let rowProcessed = 1;
            for (let id of properties) {
                this.formData.lists = JSON.stringify(id);
                this.formData.payCodeBatchNumber = JSON.stringify(payBatchNumber);
                if (rowProcessed > 100) {
                    rowProcessed = 100;
                }
                let progress = (rowProcessed / properties.length) * 100;
                this.progressBar = progress;
                const req = await this.$api
                    .post('quick-payment/payment/process-list', this.formData)
                    .then((response) => {
                        payBatchNumber = response.data.payBatchNumber;
                        //create invoice
                        if (response.data.create_invoice_pdf) {
                            let url = '?module=ap&command=createAgentInvoice';
                            var form_data = new FormData();
                            form_data.append('invoicesList', JSON.stringify(response.data['created_invoices']));
                            form_data.append('runDate', this.runDate);
                            form_data.append('agentInvoiceNumber', JSON.stringify(response.data.invoice_number));
                            axios.post(url, form_data).then((response) => {});
                        }
                        for (let tblName in this.resultsTbls) {
                            if (this.resultsTbls.hasOwnProperty(tblName)) {
                                let tbl = this.resultsTbls[tblName];
                                let tblRows = response.data[tblName];
                                if (tblRows) {
                                    for (var k = 0; k < tblRows.length; k++) {
                                        let row = JSON.parse(JSON.stringify(tblRows[k]));
                                        row.ch = false;
                                        row.trans_month_year = moment(row.trans_date, 'YYYY-MM-DD').format('MM/YYYY');
                                        let use = 'property';
                                        if (tblName == 'paid_invoices') {
                                            use = 'property_id';
                                            row.property = row.property_id;
                                            if (tbl.datas.batch_no == row.batch_no) {
                                                tbl.datas.count += row.count;
                                            } else {
                                                tbl.datas.push(row);
                                            }
                                        } else {
                                            if (tblName == 'created_invoices_total') {
                                                use = 'property_id';
                                                row.property = row.property_id;
                                                this.totalInvoice += row.trans_amt;
                                            } else {
                                                row.property_id = row.property;
                                            }
                                            tbl.datas.push(row);
                                        }

                                        if (actedProps.indexOf(tblRows[k][use]) == -1) {
                                            actedProps.push(tblRows[k][use]);
                                        }
                                    }
                                }
                            }
                        }
                        for (var i = 0; i < response.data.length; i++) {
                            let row = data[i].payables;
                            for (var pd = 0; pd < row.length; pd++) {
                                if (actedProps.indexOf(row[pd].property) == -1) {
                                    let newRow = JSON.parse(JSON.stringify(row[pd]));
                                    newRow.errors = ['Nothing to pay.'];
                                    newRow.ch = false;
                                    this.resultsTbls.skipped_invoices.datas.push(newRow);
                                }
                            }
                        }
                    });
                rowProcessed++;
            }
            let paidInv = this.resultsTbls.paid_invoices.datas;
            let payCode = { B: [], E: [], C: [] };
            for (var p = 0; p < paidInv.length; p++) {
                if (payCode[paidInv[p].pay_by_code].length > 0) {
                    payCode[paidInv[p].pay_by_code][0].count += paidInv[p].count;
                } else {
                    let newPay = {
                        batch_no: paidInv[p].batch_no,
                        count: paidInv[p].count,
                        pay_by_code: paidInv[p].pay_by_code,
                        payment_code: paidInv[p].payment_code,
                        trans_date: paidInv[p].trans_date,
                    };

                    payCode[paidInv[p].pay_by_code].push(newPay);
                }
            }
            if (payCode['C'].length > 0) this.resultsTbls.paid_invoices_list.datas.push(payCode['C'][0]);
            if (payCode['B'].length > 0) this.resultsTbls.paid_invoices_list.datas.push(payCode['B'][0]);
            if (payCode['E'].length > 0) this.resultsTbls.paid_invoices_list.datas.push(payCode['E'][0]);
            this.formData.payCodeBatchNumber = JSON.stringify(payBatchNumber);
            if (sessionStorage.getItem('sso_key')) this.formData.app_key = sessionStorage.getItem('sso_key');
            this.formData.user_type = localStorage.getItem('user_type');
            let is_batched = axios
                .post(this.cirrus8_api_url + 'api/quick-payment/payment/process-batch-payments', this.formData, {
                    headers: { 'Content-Type': 'application/json' },
                })
                .then((response) => {
                    let batchPayCount = response.data.payCount;
                    if (payCode['C'].length > 0) {
                        payCode['C'][0]['count'] = batchPayCount['C']['batch_count'];
                    }
                    if (payCode['B'].length > 0) {
                        payCode['B'][0]['count'] = batchPayCount['B']['batch_count'];
                    }
                    if (payCode['E'].length > 0) {
                        payCode['E'][0]['count'] = batchPayCount['E']['batch_count'];
                    }
                });
            this.progressBar = 100;
            this.loading_progressBar = false;
            this.showProcess = true;
            this.proceedPayment = false;
            this.showPayableDetails = false;
        },

        getListRow: function (arr, value, key) {
            if (!key) key = 'value';
            let row = {};
            for (var i = 0; i < arr.length; i++) {
                if (arr[i][key] == value) {
                    row = arr[i];
                    break;
                }
            }
            return row;
        },

        /* OPEN LINKS */
        proceedtolinks: function (data) {
            let ref = '';
            if (data.payment_code == 'B') {
                let getSource =
                    '&batchID=' +
                    data.batch_no +
                    '&viewDate=' +
                    moment(data.trans_date, 'YYYY-MM-DD').format('MM/YYYY') +
                    '&viewDisplayOption=1&paymentType=2';
                ref = '?command=viewEFT&module=ap' + getSource;
            }
            if (data.payment_code == 'E') {
                let getSource =
                    '&batchID=' +
                    data.batch_no +
                    '&viewDate=' +
                    moment(data.trans_date, 'YYYY-MM-DD').format('MM/YYYY') +
                    '&viewDisplayOption=1&paymentType=1';
                ref = '?command=viewEFT&module=ap' + getSource;
            }
            if (data.payment_code == 'C') {
                ref = '?command=viewCheque&module=ap&batchID=' + data.batch_no;
            }
            let url = window.location.pathname + ref;
            window.open(url, '_blank');
        },

        /* BACK */
        goBack: function () {
            this.displayPaymentList();
            this.showFormPart = true;
            this.showProcess = false;
            this.showPayableDetails = true;
        },
        /* PROCESS AGAIN METHOD */
        processAgain: function () {
            this.newSelectedProperty = [];
            this.unSelectedProperty = [];
            let newProps = [];
            if (this.errorInvoice.length > 0)
                newProps = newProps.concat(
                    $.map(this.errorInvoice, function (value, index) {
                        return value.property;
                    }),
                );
            if (this.unCheckedPaymentModel.length > 0)
                newProps = newProps.concat(
                    $.map(this.unCheckedPaymentModel, function (value, index) {
                        return value.property;
                    }),
                );

            this.newSelectedProperty = $.map(this.selectedProperty, function (data, index) {
                if ($.inArray(data.value, newProps) !== -1) {
                    return data;
                }
            });
            this.unSelectedProperty = $.map(this.selectedProperty, function (data, index) {
                if ($.inArray(data.value, newProps) == -1) {
                    return data.value;
                }
            });

            if (this.unSelectedProperty.length > 0) this.removeProperty(this.unSelectedProperty);

            if (newProps.length > 0) {
                this.formData.PaymentType = this.PaymentRunItem[this.PaymentRun];
                this.formData.PropMgr = this.selectedPropBy;
                this.formData.fundies = this.fundies;
                this.formData.bankAccount = this.BankAccount;
                this.formData.run_date = this.dateFormatted;
                this.formData.Property = this.newSelectedProperty;
                this.formData.SelectedProp = this.newSelectedProperty;
                this.showPayableListReprocess(this.formData);

                this.showFormPart = true;
                this.showProcess = false;
                this.showPayableDetails = true;
            } else {
                this.snackbarErrorText = 'No properties selected';
                this.snackbarError = true;
            }
        },

        /* PROCESS SINGLE METHOD */
        processToSingle: function () {
            let processProps = [];
            if (this.errorInvoice.length > 0)
                processProps = processProps.concat(
                    $.map(this.errorInvoice, function (value, index) {
                        return value.property;
                    }),
                );
            if (this.unCheckedPaymentModel.length > 0)
                processProps = processProps.concat(
                    $.map(this.unCheckedPaymentModel, function (value, index) {
                        return value.property;
                    }),
                );

            if (processProps.length > 0) {
                let getstr =
                    '&bankID=' +
                    this.BankAccount +
                    '&showFunds=0&runType=' +
                    this.PaymentRunItem[this.PaymentRun].paymentID +
                    '&runDate=' +
                    this.dateFormatted +
                    '&selectedList=' +
                    processProps.join('::');
                window.location = window.location.pathname + '?module=ap&command=payments' + getstr;
            } else {
                this.snackbarErrorText = 'No properties selected';
                this.snackbarError = true;
            }
        },

        /* UNCLEARED AMOUNTS */
        getUnclearedAmt: function (uncleared, type) {
            let trans = uncleared.transactions;
            let total = 0;

            for (var i = 0; i < trans.length; i++) {
                if (trans[i].partition == type) {
                    total += parseFloat(trans[i].amount);
                } else {
                    // total += parseFloat(trans[i].tax);
                }
            }

            return total;
        },
        computeTotal() {
            this.totalAvailableFunds = 0;
            this.totalMgtFeesDue = 0;
            this.totalMgtFeePayReserve = 0;
            this.totalSupplierUnpaidInvoice = 0;
            this.totalSupplierPay = 0;
            this.totalOwnerPayments = 0;
            this.totalClosingBalance = 0;

            let selPropRow = $.map(this.selectedData, function (value, index) {
                return value;
            });
            for (var i = 0; i < selPropRow.length; i++) {
                this.totalAvailableFunds += selPropRow[i].availableFunds;
                this.totalMgtFeesDue += selPropRow[i].managementFeesDue;
                this.totalMgtFeePayReserve += selPropRow[i].managementPayReserve;
                this.totalSupplierUnpaidInvoice += selPropRow[i].suppliersUnpaid;
                this.totalSupplierPay += selPropRow[i].suppliersPay;
                this.totalOwnerPayments += selPropRow[i].ownerPayment;
                this.totalClosingBalance += selPropRow[i].closingBal;
            }
        },

        downloadAgentInvoice: function (rawData) {
            this.formData.trans_date = rawData.trans_date;
            this.formData.inv_ref = rawData.inv_ref;
            this.formData.property_id = rawData.property_id;
            this.formData.no_load = true;
            this.$api.post('quick-payment/payment/download-agent-invoice', this.formData).then((response) => {
                if (response.data.status == 'error') {
                    this.$noty.error(response.data.error_message);
                } else {
                    document.location.href = 'download.php?fileID=' + response.data.fileID;
                    this.$noty.success('Invoice downloaded.');
                }
            });
        },

        downloadAllAgentInvoice: function (rawData) {
            this.formData.agent_invoice = JSON.stringify(rawData);
            this.$api.post('quick-payment/payment/download-all-agent-invoice', this.formData).then((response) => {
                if (response.data.status == 'error') {
                    this.$noty.error(response.data.error_message);
                } else {
                    document.location.href = 'download.php?fileID=' + response.data.fileID;
                    this.$noty.success('Invoice downloaded.');
                }
            });
        },

        getParamValue: function (payType, type) {
            let paramvalue = $.map(payType, function (data, index) {
                if (index == type) {
                    return data;
                }
            });

            return paramvalue[0];
        },
        handleToggleAll(event) {
            var toggle = event.value;
            this.$refs.refSelectedData.value = [];
            if (toggle) {
                setTimeout(() => {
                    for (var i = 0; i < this.pdDetails.length; i++) {
                        var item = this.pdDetails[i];
                        this.$refs.refSelectedData.select(item);
                    }
                }, 0);
            }
        },
    },
    watch: {
        runDate(val) {
            this.dateFormatted = this.formatDate(this.runDate);
        },
        selectedData() {
            this.computeTotal();
        },
        PaymentRunItem() {
            this.checkboxWithInvoice = false;
            let cInvoice = this.getParamValue(this.PaymentRunItem[this.PaymentRun], 'invoicesOnly');
            if (cInvoice == 1) this.checkboxWithInvoice = true;
            this.checkboxWithBal = false;
            let cBalance = this.getParamValue(this.PaymentRunItem[this.PaymentRun], 'balancesOnly');
            if (cBalance == 1) this.checkboxWithBal = true;
        },
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
small {
    display: block;
}
</style>
