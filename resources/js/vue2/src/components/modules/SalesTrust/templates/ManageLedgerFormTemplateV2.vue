<style>
#content {
    overflow-x: hidden;
}

.shortcut-div > .v-toolbar__content {
    height: 30px !important;
}

.v-card > .shortcut-div {
    height: 30px !important;
}

.v-autocomplete__content.v-menu__content,
.v-autocomplete__content.v-menu__content .v-card {
    border-radius: 0;
    font-weight: normal !important;
}

.v-menu__content {
    position: fixed !important;
    max-height: 170px !important;
    font-weight: normal !important;
    font-size: 11px !important;
    z-index: 999999 !important;
    color: inherit !important;
}

.v-list--dense .v-list-item .v-list-item__subtitle,
.v-list--dense .v-list-item .v-list-item__title,
.v-list-item--dense .v-list-item__subtitle,
v-list-item--dense .v-list-item__title {
    font-weight: normal !important;
}

.v-list-item .v-list-item__mask {
    color: #ffffff !important;
    background: #00baf2 !important;
    font-weight: bold !important;
}

.v-list-item.v-list-item--highlighted:before {
    opacity: 0 !important;
}

.create-new-ledger-btn {
    margin-top: -1px;
}

td.required {
    min-width: 18px;
}

.c8-page .multiselect .multiselect__tags .multiselect__placeholder {
    line-height: 16px;
}

.property-code-dropdown + div {
    position: relative;
    left: -2px;
}

.property-code-dropdown + div button {
    min-width: 50px;
}

.property-code-dropdown + div button i {
    color: rgba(0, 0, 0, 0.87);
}

.commission-range-amount input {
    min-width: 210px;
    max-width: 210px;
}

.v-btn--fab.v-size--x-small .v-icon,
.v-btn--icon.v-size--x-small .v-icon {
    font-size: 21px;
}

a.no-hover {
    text-decoration: none !important;
}

.line-edit-button {
    font-size: 16px !important;
}

.toolbar-search-holder .v-input__icon {
    height: 30px;
}

.toolbar-search-holder .v-icon {
    margin-top: 2px !important;
    color: #ffffff !important;
}

.toolbar-search-holder .v-icon.primary--text {
    color: #ffffff !important;
}

.toolbar-search-holder input::placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
}

.text-box-alert .v-chip {
    height: 28px !important;
    margin-top: -3px !important;
}

.select-box-alert .v-chip,
.textarea-alert .v-chip {
    margin-left: 0px !important;
    margin-top: 5px !important;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header :title="manage_ledger_title" />
            </v-toolbar-title>

            <div class="flex-grow-1"></div>

            <v-btn
                depressed
                small
                color="primary"
                class="v-step-new-lease-button"
                v-if="form_mode === 0"
                @click="newProperty()"
            >
                <v-icon>add</v-icon>
                New Ledger
            </v-btn>
            <v-btn
                depressed
                small
                color="black"
                class="v-step-cancel-lease-button"
                dark
                v-if="form_mode === 1"
                @click="cancelNewProperty()"
            >
                <v-icon color="red">close</v-icon>
                Cancel Ledger
            </v-btn>
        </v-toolbar>

        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>

        <div class="page-form">
            <v-row
                v-if="form_mode === 0"
                class="form-row"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Ledger:
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <!--O: dropdown START-->
                    <cirrus-single-select-group
                        class="property-code-dropdown"
                        v-model="property_code"
                        :options="dd_property_list"
                        group-values="fieldGroupValues"
                        group-label="fieldGroupNames"
                        track-by="field_key"
                        label="field_value"
                        :selectedValue="selectedPropertyCode"
                        ref="propertyCodeDropDown"
                    />
                    <div style="margin-top: -30px; margin-left: 302px">
                        <sui-button
                            style="height: 30px !important"
                            :loading="ledger_list_loading"
                            class="dropdown-button"
                            icon="caret right icon"
                            @click="forceRerender()"
                        />
                    </div>
                </v-col>
            </v-row>
            <v-row
                v-if="form_mode === 1"
                class="form-row"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Ledger Code:
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        class="v-step-new-property-code"
                        v-model="new_property_code"
                        size="10"
                        :id="'new_property_code'"
                        :edit_form="true"
                        :error_msg="error_msg"
                        :maxlength="10"
                    ></cirrus-input>
                    <v-btn
                        class="form-text-button mb-0 rounded-l-0"
                        color="primary"
                        depressed
                        elevation="0"
                        small
                        @click="checkPropertyCode()"
                        style="margin-top: -1px"
                        >Create New
                    </v-btn>
                </v-col>
            </v-row>
        </div>

        <!--//
        form_mode:
        0 - default, edit mode
        1 - create new ledger
        //-->

        <v-tabs
            class="cirrus-tab-theme"
            icons-and-text
            v-model="template_tab"
            show-arrows
            v-if="property_code !== '' || new_property_code_ok"
        >
            <v-tabs-slider color="white"></v-tabs-slider>

            <v-tab
                href="#tab-1"
                v-if="responsive_show && form_mode === 1"
                class="v-step-form-tab"
            >
                New Ledger
                <v-icon
                    small
                    dense
                    >menu_book
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-1"
                v-if="responsive_show && form_mode === 0"
                class="v-step-form-tab"
            >
                Ledger
                <v-icon
                    small
                    dense
                    >menu_book
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-2"
                v-if="responsive_show"
                v-show="form_mode === 0"
            >
                Sub-ledgers
                <v-icon
                    small
                    dense
                    >payments
                </v-icon>
            </v-tab>

            <v-tab
                href="#tab-1"
                class="primary--text v-step-form-tab"
                v-if="!responsive_show && form_mode === 1"
            >
                <v-icon
                    small
                    dense
                    >menu_book
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-1"
                class="primary--text v-step-form-tab"
                v-if="!responsive_show && form_mode === 0"
            >
                <v-icon
                    small
                    dense
                    >menu_book
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-2"
                class="primary--text"
                v-if="!responsive_show && form_mode === 0"
            >
                <v-icon
                    small
                    dense
                    >payments
                </v-icon>
            </v-tab>

            <v-tab-item :value="'tab-1'">
                <!-- general details section -->
                <div
                    id="general_details_section"
                    @dblclick="doubleClickForm()"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">General Details</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                            <v-btn
                                x-small
                                data-tooltip="Edit"
                                v-if="form_mode !== 1 && !edit_form"
                                class="v-step-edit-button"
                                icon
                                @click="edit_form = true"
                            >
                                <v-icon>edit</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Undo Changes"
                                v-if="edit_form && form_mode !== 1"
                                class="v-step-revert-button"
                                icon
                                @click="resetLedgerForm()"
                            >
                                <v-icon color="red">undo</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Update Details"
                                v-if="edit_form && form_mode !== 1"
                                class="v-step-save-1-button"
                                icon
                                @click="saveForm()"
                            >
                                <v-icon
                                    light
                                    color="green"
                                    >check
                                </v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Refresh"
                                v-if="form_mode !== 1"
                                class="v-step-refresh-button"
                                icon
                                @click="loadLedgerDetails()"
                            >
                                <v-icon>refresh</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                icon
                                @click="openLedgerChangelogModal()"
                                data-tooltip="Changelog"
                                v-if="form_mode !== 1"
                            >
                                <v-icon>history</v-icon>
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                    <div
                        v-if="!loading_setting"
                        class="page-form"
                    >
                        <v-row class="form-row no-gutters">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-input"
                            >
                                <v-row>
                                    <v-col>
                                        <div v-if="isRemove && edit_form">
                                            <v-btn
                                                background
                                                depressed
                                                @click="removeImage"
                                            >
                                                <v-icon
                                                    center
                                                    color="primary"
                                                >
                                                    close
                                                </v-icon>
                                            </v-btn>
                                        </div>
                                        <div v-else>
                                            <v-btn
                                                background
                                                depressed
                                                :loading="isSelecting"
                                                @click="onButtonClick"
                                                v-if="edit_form || form_mode === 1"
                                            >
                                                <v-icon
                                                    center
                                                    color="primary"
                                                >
                                                    add_a_photo
                                                </v-icon>
                                            </v-btn>
                                            <input
                                                ref="uploader"
                                                class="d-none"
                                                type="file"
                                                accept="image/*"
                                                @change="onFileChanged"
                                            />
                                        </div>
                                    </v-col>
                                </v-row>
                                <v-row>
                                    <v-col>
                                        <v-img
                                            max-height="180px"
                                            v-bind:src="property_image"
                                            position="center bottom"
                                            contain
                                            dense
                                        />
                                    </v-col>
                                </v-row>
                            </v-col>
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                                lg="9"
                                xl="9"
                            >
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Name:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td v-if="!edit_form && form_mode !== 1">{{ property_name }}</td>
                                                <td v-if="edit_form || form_mode === 1">
                                                    <v-text-field
                                                        v-model="property_name"
                                                        :maxlength="40"
                                                        dense
                                                    />
                                                    <span class="text-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 && errorData.id === 'property_name'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Address:</strong>
                                                </td>
                                                <td class="required">&nbsp;</td>
                                                <td v-if="!edit_form && form_mode !== 1">{{ property_address }}</td>
                                                <td v-if="edit_form || form_mode === 1">
                                                    <v-text-field
                                                        v-model="property_address"
                                                        :maxlength="80"
                                                        dense
                                                    />
                                                    <v-chip
                                                        v-if="
                                                            error_msg.length > 0 && errorData.id === 'property_address'
                                                        "
                                                        v-for="(errorData, index) in error_msg"
                                                        :key="index"
                                                        outlined
                                                        color="error"
                                                    >
                                                        <v-icon left>error</v-icon>
                                                        {{ errorData.message }}
                                                    </v-chip>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>{{ general_suburb_label }}:</strong>
                                                </td>
                                                <td class="required"></td>
                                                <td v-if="!edit_form && form_mode !== 1">{{ property_suburb }}</td>
                                                <td v-if="edit_form || form_mode === 1">
                                                    <div v-if="this.client_country_code == 'AU'">
                                                        <v-combobox
                                                            v-model="property_suburb"
                                                            :maxlength="40"
                                                            :items="suburb_list_filtered"
                                                            item-value="label"
                                                            item-text="label"
                                                            @change="suburbSelected(property_suburb)"
                                                            auto-select-first
                                                            hide-selected
                                                            persistent-hint
                                                            append-icon
                                                            :search-input.sync="searchSuburb"
                                                            :hide-no-data="!searchSuburb"
                                                            dense
                                                            ref="refSuburb"
                                                            flat
                                                        >
                                                            <template v-slot:no-data>
                                                                <v-list-item>
                                                                    <v-chip
                                                                        v-model="searchSuburb"
                                                                        small
                                                                    >
                                                                        {{ searchSuburb }}
                                                                    </v-chip>
                                                                </v-list-item>
                                                            </template>
                                                        </v-combobox>
                                                    </div>
                                                    <div v-else>
                                                        <v-text-field
                                                            :maxlength="40"
                                                            dense
                                                            v-model="property_suburb"
                                                        />
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Post Code:</strong>
                                                </td>
                                                <td class="required"></td>
                                                <td v-if="!edit_form && form_mode !== 1">{{ property_post_code }}</td>
                                                <td v-if="edit_form || form_mode === 1">
                                                    <v-text-field
                                                        v-model="property_post_code"
                                                        :maxlength="property_postcode_length"
                                                        dense
                                                    />
                                                    <v-chip
                                                        v-if="
                                                            error_msg.length > 0 &&
                                                            errorData.id === 'property_post_code'
                                                        "
                                                        v-for="(errorData, index) in error_msg"
                                                        :key="index"
                                                        outlined
                                                        color="error"
                                                    >
                                                        <v-icon left>error</v-icon>
                                                        {{ errorData.message }}
                                                    </v-chip>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>State:</strong>
                                                </td>
                                                <td class="required"></td>
                                                <td v-if="!edit_form && form_mode !== 1">{{ property_state }}</td>
                                                <td v-if="edit_form || form_mode === 1">
                                                    <cirrus-single-select
                                                        v-model="property_state"
                                                        :options="state_list"
                                                        @input="suburbFilteredList(property_state)"
                                                    />
                                                    <v-chip
                                                        v-if="error_msg.length > 0 && errorData.id === 'property_state'"
                                                        v-for="(errorData, index) in error_msg"
                                                        :key="index"
                                                        outlined
                                                        color="error"
                                                    >
                                                        <v-icon left>error</v-icon>
                                                        {{ errorData.message }}
                                                    </v-chip>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Category:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td v-if="!edit_form && form_mode !== 1">
                                                    {{ getNameFromList(dd_ledger_category_list, property_type, true) }}
                                                </td>
                                                <td v-if="edit_form || form_mode === 1">
                                                    <cirrus-single-select
                                                        class="category-single-select"
                                                        v-model="property_type"
                                                        :options="dd_ledger_category_list"
                                                    />
                                                    <div class="select-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 && errorData.id === 'property_type'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row
                                    class="form-row no-gutters"
                                    v-if="property_type == 'LEASE' || property_type == 'SALE'"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong
                                                        >{{
                                                            property_type == 'LEASE'
                                                                ? 'Leasing Reference'
                                                                : 'Sales Reference'
                                                        }}:</strong
                                                    >
                                                </td>
                                                <td class="required"></td>
                                                <td v-if="!edit_form && form_mode !== 1">{{ sales_reference }}</td>
                                                <td v-if="edit_form || form_mode === 1">
                                                    <v-text-field
                                                        v-model="sales_reference"
                                                        :maxlength="15"
                                                        dense
                                                    />
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row
                                    class="form-row no-gutters"
                                    v-if="property_type == 'LEASE' || property_type == 'SALE'"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Managed Property:</strong>
                                                </td>
                                                <td class="required"></td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-show="form_mode === 1 || edit_form"
                                                        v-model="managed_prop"
                                                        mandatory
                                                    >
                                                        <v-btn
                                                            :value="1"
                                                            small
                                                            text
                                                        >
                                                            Yes
                                                        </v-btn>
                                                        <v-btn
                                                            :value="0"
                                                            small
                                                            text
                                                        >
                                                            No
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <span
                                                        class="form-input-text"
                                                        v-if="managed_prop == 1 && !edit_form && form_mode !== 1"
                                                        >Yes</span
                                                    >
                                                    <span
                                                        class="form-input-text"
                                                        v-if="managed_prop == 0 && !edit_form && form_mode !== 1"
                                                        >No</span
                                                    >
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row
                                    class="form-row no-gutters"
                                    v-if="form_mode !== 1"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Ledger Status:</strong>
                                                </td>
                                                <td class="required"></td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-show="!read_only && edit_form"
                                                        v-model="ledger_status"
                                                        mandatory
                                                    >
                                                        <v-btn
                                                            small
                                                            text
                                                            :disabled="!edit_form"
                                                        >
                                                            Active
                                                        </v-btn>
                                                        <v-btn
                                                            small
                                                            text
                                                            :disabled="!edit_form"
                                                        >
                                                            Inactive
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <span
                                                        class="form-input-text"
                                                        v-if="ledger_status === 0 && !edit_form"
                                                        >Active</span
                                                    >
                                                    <span
                                                        class="form-input-text"
                                                        v-if="ledger_status === 1 && !edit_form"
                                                        >Inactive</span
                                                    >
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row no-gutters"
                                    v-if="edit_form"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <!-- DELETE LEDGER - START -->
                                        <br />
                                        <v-btn
                                            class="v-step-save-1-button"
                                            v-if="form_mode !== 1"
                                            @click="showConfirmationDialog()"
                                            color="error"
                                            dark
                                            absolute
                                            right
                                            small
                                        >
                                            Delete Ledger
                                        </v-btn>
                                        <br /><br /><br />
                                        <!-- DELETE LEDGER - END -->
                                    </v-col>
                                </v-row>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <!-- end of general details section -->

                <!-- agent details section -->
                <div
                    id="management_details_section"
                    @dblclick="doubleClickForm()"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Agent Details</h6>
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                    <div
                        v-if="!loading_setting"
                        class="page-form"
                    >
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Sales or Leasing Agent:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_form && form_mode !== 1">
                                            {{ getNameFromList(dd_sl_agent_list, sl_agent, true) }}
                                        </td>
                                        <td v-if="edit_form || form_mode === 1">
                                            <cirrus-single-select
                                                v-model="sl_agent"
                                                :options="dd_sl_agent_list"
                                            />
                                            <div class="select-box-alert">
                                                <v-chip
                                                    v-if="error_msg.length > 0 && errorData.id === 'sl_agent'"
                                                    v-for="(errorData, index) in error_msg"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Agent Company:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_form && form_mode !== 1">
                                            {{ getNameFromList(dd_agent_list, property_agent, false, true) }}
                                        </td>
                                        <td v-if="edit_form || form_mode === 1">
                                            <cirrus-single-select
                                                v-model="property_agent"
                                                :options="dd_agent_list"
                                            />
                                            <div class="select-box-alert">
                                                <v-chip
                                                    v-if="error_msg.length > 0 && errorData.id === 'property_agent'"
                                                    v-for="(errorData, index) in error_msg"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Remittance Office:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_form && form_mode !== 1">
                                            {{ getNameFromList(dd_office_list, property_remittance_office, false) }}
                                        </td>
                                        <td v-if="edit_form || form_mode === 1">
                                            <cirrus-single-select
                                                v-model="property_remittance_office"
                                                :options="dd_office_list"
                                            />
                                            <div class="select-box-alert">
                                                <v-chip
                                                    v-if="
                                                        error_msg.length > 0 &&
                                                        errorData.id === 'property_remittance_office'
                                                    "
                                                    v-for="(errorData, index) in error_msg"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <!-- end of agent details section -->

                <!-- owner details section -->
                <div
                    id="owner_details_section"
                    @dblclick="doubleClickForm()"
                >
                    <v-card
                        id="owner_details_header"
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Owner Details</h6>
                            &nbsp;&nbsp;
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                    <!-- 1=existing 0=new company -->
                    <div
                        v-if="!loading_setting"
                        class="page-form"
                    >
                        <v-row
                            class="form-row no-gutters"
                            v-if="form_mode === 1"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Company Type:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td>
                                            <v-btn-toggle
                                                v-model="company_type"
                                                mandatory
                                            >
                                                <v-btn
                                                    small
                                                    text
                                                >
                                                    New
                                                </v-btn>
                                                <v-btn
                                                    small
                                                    text
                                                >
                                                    Existing
                                                </v-btn>
                                            </v-btn-toggle>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                                lg="6"
                                xl="6"
                            >
                                <v-row
                                    class="form-row no-gutters"
                                    v-if="company_type == 0"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Owner Code:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-text-field
                                                        v-model="property_owner_code"
                                                        :maxlength="10"
                                                        dense
                                                    />
                                                    <span class="text-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 &&
                                                                errorData.id === 'property_owner_code'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row
                                    class="form-row no-gutters"
                                    v-if="company_type == 1"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Owner:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td v-if="!edit_form && form_mode !== 1">{{ property_owner }}</td>
                                                <td v-if="edit_form || form_mode === 1">
                                                    <cirrus-single-select
                                                        v-model="property_owner"
                                                        :options="dd_owner_list"
                                                    />
                                                    <div class="select-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 &&
                                                                errorData.id === 'property_owner'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Owner Name:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td v-if="company_type == 1">
                                                    {{ property_owner_name }}
                                                </td>
                                                <td v-else>
                                                    <v-text-field
                                                        v-model="property_owner_name"
                                                        :maxlength="240"
                                                        dense
                                                    />
                                                    <span class="text-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 &&
                                                                errorData.id === 'property_owner_name'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Owner Address:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td v-if="company_type == 1">
                                                    {{ property_owner_address1 }}
                                                </td>
                                                <td v-else>
                                                    <cirrus-text-area
                                                        :rows="'2'"
                                                        :cols="'60'"
                                                        :edit_form="1"
                                                        :error_msg="error_msg"
                                                        v-model="property_owner_address1"
                                                        :maxlength="74"
                                                    ></cirrus-text-area>

                                                    <div class="textarea-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 &&
                                                                errorData.id === 'property_owner_address1'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Owner {{ general_suburb_label }}:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td v-if="company_type == 1">
                                                    {{ property_owner_suburb }}
                                                </td>
                                                <td v-else>
                                                    <div v-if="property_owner_country_list == 'AU'">
                                                        <v-combobox
                                                            v-model="property_owner_suburb"
                                                            v-if="property_owner_state_list != ''"
                                                            :items="suburbOwner_list_filtered"
                                                            item-value="label"
                                                            item-text="label"
                                                            @change="suburbOwnerSelected(property_owner_suburb)"
                                                            auto-select-first
                                                            hide-selected
                                                            persistent-hint
                                                            append-icon
                                                            :search-input.sync="searchOwnerSuburb"
                                                            :hide-no-data="!searchOwnerSuburb"
                                                            dense
                                                            flat
                                                            ref="refOwnerSuburb"
                                                            :maxlength="40"
                                                        >
                                                            <template v-slot:no-data>
                                                                <v-list-item>
                                                                    <v-chip
                                                                        v-model="searchOwnerSuburb"
                                                                        small
                                                                    >
                                                                        {{ searchOwnerSuburb }}
                                                                    </v-chip>
                                                                </v-list-item>
                                                            </template>
                                                        </v-combobox>
                                                        <v-text-field
                                                            :maxlength="40"
                                                            dense
                                                            v-model="property_owner_suburb"
                                                            v-else
                                                        />
                                                    </div>
                                                    <div v-else>
                                                        <v-text-field
                                                            :maxlength="40"
                                                            dense
                                                            v-model="property_owner_suburb"
                                                        />
                                                    </div>

                                                    <span class="text-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 &&
                                                                errorData.id === 'property_owner_suburb'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                            </v-col>
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                                lg="6"
                                xl="6"
                            >
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                    v-if="
                                                        (!edit_form && company_type == 1 && form_mode == 1) ||
                                                        (edit_form && company_type == 1 && form_mode == 0)
                                                    "
                                                    style="height: 36px"
                                                >
                                                    <strong>Owner Post Code:</strong>
                                                </td>
                                                <td
                                                    class="title"
                                                    align="right"
                                                    v-else
                                                >
                                                    <strong>Owner Post Code:</strong>
                                                </td>

                                                <td class="required">*</td>
                                                <td v-if="company_type == 1">{{ property_owner_post }}</td>
                                                <td v-else>
                                                    <v-text-field
                                                        v-model="property_owner_post"
                                                        dense
                                                        :maxlength="10"
                                                    />
                                                    <span class="text-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 &&
                                                                errorData.id === 'property_owner_post'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row
                                    class="form-row no-gutters"
                                    v-if="company_country_defaults.display_state"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Owner State:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td v-if="company_type == 1">
                                                    {{ property_owner_state }}
                                                </td>
                                                <td v-else>
                                                    <cirrus-single-select
                                                        v-model="property_owner_state_list"
                                                        :options="stateOwner_list_filtered"
                                                        @input="suburbOwnerFilteredList(property_owner_state_list)"
                                                    />
                                                    <div class="select-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 &&
                                                                errorData.id === 'property_owner_state'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                    v-if="form_mode == 1 && company_type === 0"
                                                    :style="
                                                        company_country_defaults.display_state
                                                            ? company_type == 1
                                                                ? readonly_row_height
                                                                : large_row_height
                                                            : company_type == 1
                                                              ? readonly_row_height
                                                              : regular_row_height
                                                    "
                                                >
                                                    <strong>Owner Country:</strong>
                                                </td>
                                                <td
                                                    class="title"
                                                    align="right"
                                                    v-else
                                                >
                                                    <strong>Owner Country:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td v-if="company_type == 1">
                                                    {{ property_owner_country }}
                                                </td>
                                                <td v-else>
                                                    <cirrus-single-select
                                                        v-model="property_owner_country_list"
                                                        :options="agent_country_list"
                                                        @input="stateOwnerFilteredList(property_owner_country_list)"
                                                    />
                                                    <div class="select-box-alert">
                                                        <v-chip
                                                            v-if="
                                                                error_msg.length > 0 &&
                                                                errorData.id === 'property_owner_country_list'
                                                            "
                                                            v-for="(errorData, index) in error_msg"
                                                            :key="index"
                                                            outlined
                                                            color="error"
                                                        >
                                                            <v-icon left>error</v-icon>
                                                            {{ errorData.message }}
                                                        </v-chip>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                    :style="
                                                        company_country_defaults.display_state
                                                            ? company_type == 1
                                                                ? readonly_row_height
                                                                : regular_row_height
                                                            : company_type == 1
                                                              ? readonly_row_height
                                                              : large_row_height
                                                    "
                                                >
                                                    <strong>Owner E-mail:</strong>
                                                </td>
                                                <td class="required"></td>
                                                <td v-if="company_type == 1">
                                                    <span v-if="property_owner_email != ''">
                                                        <span v-if="property_owner_email.includes(';')">
                                                            <v-item-list
                                                                v-for="(data, index) in property_owner_email.split(';')"
                                                                :key="index"
                                                            >
                                                                <a :href="'mailto:' + data.trim()">{{ data }}</a
                                                                ><span
                                                                    v-if="
                                                                        property_owner_email.split(';').length - 1 !=
                                                                        index
                                                                    "
                                                                    >;
                                                                </span>
                                                            </v-item-list>
                                                        </span>
                                                        <span v-else
                                                            ><a :href="'mailto:' + property_owner_email.trim()">{{
                                                                property_owner_email
                                                            }}</a></span
                                                        >
                                                    </span>
                                                    <span v-else></span>
                                                </td>
                                                <td v-else>
                                                    <v-text-field
                                                        v-model="property_owner_email"
                                                        :maxlength="400"
                                                        dense
                                                    />
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <!-- end of owner section -->

                <!-- Calendar Section -->
                <div
                    id="calendar_section"
                    @dblclick="doubleClickForm()"
                >
                    <v-card
                        id="bank_details_section"
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Calendar Used</h6>
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                    <div
                        class="page-form"
                        v-if="!loading_setting"
                    >
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Property Calendar:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="form_mode == 1">
                                            <cirrus-single-select
                                                v-model="property_calendar_type"
                                                :options="property_calendar_type_list"
                                            />
                                            <div class="select-box-alert">
                                                <v-chip
                                                    v-if="
                                                        error_msg.length > 0 &&
                                                        errorData.id === 'property_calendar_type'
                                                    "
                                                    v-for="(errorData, index) in error_msg"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <!-- End of Calendar Section -->

                <!-- bank details section -->
                <div
                    id="bank_details_section"
                    @dblclick="doubleClickForm()"
                >
                    <v-card
                        id="bank_details_section"
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Bank Account</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                    <div
                        class="page-form"
                        v-if="!loading_setting"
                    >
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Bank Account:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="form_mode == 1">
                                            <cirrus-single-select
                                                v-model="property_bank_account"
                                                :options="dd_bank_account_list"
                                            />
                                            <div class="select-box-alert">
                                                <v-chip
                                                    v-if="
                                                        error_msg.length > 0 && errorData.id === 'property_bank_account'
                                                    "
                                                    v-for="(errorData, index) in error_msg"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                        <td v-if="form_mode == 0">
                                            {{ property_bank_account_name }}
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <!-- end of management details section -->

                <v-row
                    class="form-row no-gutters"
                    v-if="form_mode === 1 || edit_form"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <v-divider></v-divider>
                        <br />
                        <v-btn
                            class="v-step-save-1-button"
                            @click="saveForm()"
                            color="success"
                            dark
                            absolute
                            right
                            small
                        >
                            {{ save_button_label }}
                        </v-btn>
                        <br /><br /><br />
                    </v-col>
                </v-row>

                <commision-component
                    v-if="form_mode == 0 && (property_type == 'LEASE' || property_type == 'SALE')"
                    id="commission-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="property_code"
                    :category="property_type"
                    :agent_state_list="agent_state_list"
                    :agent_country_list="agent_country_list"
                    :agent_suburb_list="agent_suburb_list"
                    :default_country="dd_country"
                    :default_state="dd_state"
                ></commision-component>

                <!-- contacts section -->
                <contacts-component
                    v-if="form_mode == 0"
                    id="contacts-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="property_code"
                ></contacts-component>
                <!-- end of contacts section -->

                <!-- documents section -->
                <documents-component
                    v-if="form_mode == 0"
                    id="documents-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="property_code"
                ></documents-component>
                <!-- end of documents section -->

                <!-- notes section -->
                <notes-component
                    v-if="form_mode == 0"
                    id="notes-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="property_code"
                ></notes-component>
                <!-- end of notes section -->

                <calendar-component
                    v-if="form_mode == 0"
                    id="calendar-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="property_code"
                ></calendar-component>
            </v-tab-item>

            <v-tab-item
                :value="'tab-2'"
                v-show="form_mode === 0"
            >
                <sub-ledger-component
                    v-if="form_mode == 0"
                    id="sub-ledger-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="property_code"
                    :agent_state_list="agent_state_list"
                    :agent_country_list="agent_country_list"
                    :agent_suburb_list="agent_suburb_list"
                    :default_country="dd_country"
                    :default_state="dd_state"
                    :category="property_type"
                ></sub-ledger-component>

                <sundries-component
                    v-if="form_mode == 0"
                    id="sundries-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="property_code"
                ></sundries-component>
            </v-tab-item>
        </v-tabs>

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon>
                    WARNING
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="resetDeleteDialog()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">{{ warning_message }}</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        :loading="button_delete_ledger"
                        @click="deleteLedger()"
                        >Ok
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="resetDeleteDialog()"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->

        <!-- LEDGER CHANGELOG -->
        <v-dialog
            top
            v-model="show_change_log_modal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Activity Logs</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="show_change_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <activity-logs-component
                            v-if="show_change_log_modal"
                            :property_code="property_code"
                            :form_section="'main'"
                        ></activity-logs-component>
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF LEDGER CHANGELOG -->
    </div>
</template>

<script>
import Vue from 'vue';
import SuiVue from 'semantic-ui-vue';
import Multiselect from 'vue-multiselect';
import vSelect from 'vue-select';
import global_mixins from '../../../../plugins/mixins';
import { mapActions, mapMutations, mapState } from 'vuex';
import suburbList from '../../../../plugins/australianSuburb.json';
import axios from 'axios';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

Vue.use(SuiVue);
Vue.component('v-select', vSelect);
Vue.component('multiselect', Multiselect);
Vue.component('sub-ledger-component', require('../forms/SubLedgerFormV2.vue').default);
Vue.component('contacts-component', require('../forms/ContactsFormV2.vue').default);
Vue.component('documents-component', require('../forms/DocumentsFormV2.vue').default);
Vue.component('notes-component', require('../forms/NotesFormV2.vue').default);
Vue.component('calendar-component', require('../forms/CalendarFormV2.vue').default);
Vue.component('sundries-component', require('../forms/SundriesFormV2.vue').default);
Vue.component('commision-component', require('../forms/CommisionFormV2.vue').default);
Vue.component('ledger-update-log-component', require('../forms/LedgerUpdateLog.vue').default);
Vue.component('activity-logs-component', require('../forms/ActivityLogs.vue').default);
Vue.component('subledger-contacts-component', require('../forms/SubLedgerContactsForm.vue').default);
Vue.component('subledger-documents-component', require('../forms/SubLedgerDocumentsForm.vue').default);
Vue.component('subledger-notes-component', require('../forms/SubLedgerNotesForm.vue').default);
Vue.component('cirrus-single-upload-button2', require('../../../elements/CirrusSingleUploadButtonV2.vue').default);

export default {
    props: {
        initialLedgerCode: String,
        initialCountryDefaultSettings: String,
    },
    data() {
        return {
            general_suburb_label: 'Suburb',
            suburb_label: 'Suburb',
            property_postcode_length: 4,
            property_postcode_min_length: 4,
            client_country_code: 'AU',
            manage_ledger_title: 'Manage Ledger',
            loading_page_setting: false,
            loading_setting: false,
            force_load_new_property: 0,
            form_mode: 0,
            new_property_code_ok: 0,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            reloadComponents: 0,
            window_size: {
                x: 0,
                y: 0,
            },

            ledger_list_loading: true,

            property_list: [],
            property_code: '',
            dd_sl_agent_list: [],
            dd_agent_list: [],
            agent_code: {},
            dd_owner_list: [],
            owner_code: {},
            dd_ledger_category_list: [],
            ledger_category_code: {},
            dd_bank_account_list: [],
            bank_account_code: {},
            dd_office_list: [],
            property_remittance_office_code: {},
            sales_reference: '',

            state_list: [
                { value: 'WA', label: 'Western Australia' },
                { value: 'VIC', label: 'Victoria' },
                { value: 'NSW', label: 'New South Wales' },
                { value: 'QLD', label: 'Queensland' },
                { value: 'SA', label: 'South Australia' },
                { value: 'NT', label: 'Northern Territory' },
                { value: 'ACT', label: 'Australian Capital Territory' },
                { value: 'TAS', label: 'Tasmania' },
                { value: 'MALAYSIA', label: 'Malaysia' },
                { value: 'NZ', label: 'New Zealand' },
                { value: 'SNG', label: 'Singapore' },
                { value: 'ENG', label: 'England' },
                { value: 'SCO', label: 'Scotland' },
                { value: 'WAL', label: 'Wales' },
            ],
            state_code: {},
            new_property_code: '',
            property_name: '',
            property_name_old: '',
            property_address: '',
            property_address_old: '',
            property_suburb: '',
            property_suburb_old: '',
            property_suburb_auto: '',
            property_suburb_auto_old: '',
            property_post_code: '',
            property_post_code_old: '',
            property_state: '',
            property_state_old: '',
            sl_agent: '',
            sl_agent_old: '',
            property_agent: '',
            property_agent_old: '',
            property_owner: '',
            property_owner_old: '',
            property_type: '',
            property_type_old: '',
            managed_prop: 0,
            managed_prop_old: 0,
            property_remittance_office: '',
            property_remittance_office_old: '',
            property_bank_account: '',
            property_bank_account_old: '',
            property_bank_account_name: '',
            property_bank_account_name_old: '',
            save_button_label: 'Update Details',
            company_type: 1,
            company_type_old: 1,
            ledger_status: 0,
            ledger_status_old: 0,
            property_total_balance: 0,
            property_total_balance_old: 0,
            property_owner_code: '',
            property_owner_name: '',
            property_owner_address1: '',
            property_owner_address2: '',
            property_owner_suburb: '',
            property_owner_state: '',
            property_owner_post: '',
            property_owner_country: '',
            property_country_code: '',
            property_owner_email: '',
            property_owner_state_list: '',
            property_owner_country_list: '',
            agent_country_list: [],
            agent_state_list: [],
            agent_suburb_list: [],
            property_owner_name_old: '',
            property_owner_address1_old: '',
            property_owner_address2_old: '',
            property_owner_suburb_old: '',
            property_owner_state_old: '',
            property_owner_post_old: '',
            property_owner_country_old: '',
            property_owner_email_old: '',
            property_country_code_old: '',

            suburb_list_filtered: [],
            suburbOwner_list_filtered: [],
            suburbValue: [],
            suburbOwnerValue: [],
            stateOwner_list_filtered: [],
            searchSuburb: '',
            searchOwnerSuburb: '',
            dd_country: '',
            dd_state: '',

            isSelecting: false,
            selectedFile: '',
            property_image: '',
            property_image_file: '',
            default_property_image: this.$assetDomain + 'assets/images/icons/default-property-image.png',
            old_property_image: '',
            isRemove: false,
            selectedPropertyCode: [],
            post_code_length: 4,
            edit_form: false,
            dialogConfirmation: '',
            show_change_log_modal: false,
            button_delete_ledger: false,
            property_calendar_type: '',
            property_calendar_type_list: [],
            company_country_defaults: {
                country_code: 'AU',
                display_state: true,
                post_code_length: '4',
            },
            readonly_row_height: 'height:31px;',
            regular_row_height: 'height:36px;',
            large_row_height: 'height:54px;',
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadCompanyCountryDefaults();
        this.fetchPropertyList();
        this.fetchSuburb();
        this.fetchCountryList();
        this.fetchPropertyDefault();
        this.fetchBankList();
        this.fetchLedgerCategoryList();

        this.property_code = this.initialLedgerCode;
        let country_settings = JSON.parse(atob(this.initialCountryDefaultSettings));
        this.general_suburb_label = this.ucwords(country_settings.suburb);
        this.property_postcode_length = country_settings.post_code_length;
        this.property_postcode_min_length = country_settings.post_code_min_length;
        this.client_country_code = country_settings.country_code;
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'dd_property_list',
            'dd_default_value',
            'dd_country_list',
        ]),
    },
    methods: {
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },

        ...mapActions(['fetchPropertyList', 'fetchCountryList']),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_PUSH_DD_PROPERTY_LIST', 'SET_DD_COUNTRY_LIST']),

        nameWithDash({ value, label }) {
            if (`${value}` === '') return `${label}`;
            return `${label}`;
        },

        doubleClickForm() {
            if (this.form_mode !== 1 && !this.edit_form) {
                this.edit_form = true;
            }
        },

        newProperty() {
            this.resetCompanyDetail();
            this.form_mode = 1;
            this.save_button_label = 'Save Details';
            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_property_code = '';
            this.property_code = '';
            this.property_existed = true;
            this.new_property_code_ok = 0;
            this.company_type = 0;
            this.property_image = this.default_property_image;
            this.updatePageTitle('New Ledger');
        },
        cancelNewProperty() {
            this.form_mode = 0;
            this.save_button_label = 'Update Details';
            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_property_code = '';
            this.property_code = '';
            this.property_existed = true;
            this.new_property_code_ok = 0;
            this.company_type = 1;
            this.updatePageTitle('Manage Ledger');
        },

        initialiseLists() {
            this.fetchPortfolioManagerList();
            this.fetchAgentList();
            this.fetchOwnerList();
            this.fetchOfficeList();
        },

        fetchLedgerCategoryList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/ui/fetch/param-ledger-category-list';

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_ledger_category_list = this.convertToArrayOfObjects(response.data);
            });
        },

        fetchPortfolioManagerList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/ui/fetch/param-agent-list';

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_sl_agent_list = this.convertToArrayOfObjects(response.data);
            });
        },

        fetchAgentList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/agent-dropdown-list';

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_agent_list = this.convertToArrayOfObjects(response.data);
            });
        },

        fetchOwnerList() {
            this.$api.post('owner-dropdown-list', { no_load: true }).then((response) => {
                this.dd_owner_list = this.convertToArrayOfObjects(response.data);
            });
        },

        fetchBankList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/bank-account-dropdown-list';

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_bank_account_list = this.convertToArrayOfObjects(response.data);
            });
        },

        fetchOfficeList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/office-dropdown-list';

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_office_list = this.convertToArrayOfObjects(response.data);
            });
        },

        forceRerender() {
            this.reloadComponents += 1;
            this.loadLedgerDetails();
        },

        fetchPropertyDefault() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/sales-trust/ledger/get-property-default';

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_country = response.data.dd_country;
                this.dd_state = response.data.dd_state;
            });
        },

        checkPropertyCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];

            if (this.new_property_code.length > 10) {
                this.error_server_msg2.push(['Ledger code allows 10 characters only']);
            } else if (this.new_property_code === '') {
                this.error_server_msg2.push(['Please input a valid ledger code']);
            }

            if (this.verifyCode(this.new_property_code)) {
                this.error_server_msg2.push(['Please use only alphanumeric characters for the ledger code.']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;

                var form_data = new FormData();
                form_data.append('user_type', this.user_type);
                form_data.append('property_code', this.new_property_code);
                form_data.append('no_load', true);

                let api_url = this.cirrus8_api_url + 'api/check-for-duplicate-property';

                this.$api.post(api_url, form_data).then((response) => {
                    this.error_server_msg2 = response.data.validation_errors;
                    this.loading_page_setting = false;

                    if (!response.data.property_exists) {
                        this.new_property_code_ok = true;

                        //reset field values
                        this.property_name = '';
                        this.property_address = '';
                        this.property_suburb = '';
                        this.property_post_code = '';
                        this.property_state = this.dd_state;
                        this.sl_agent = '';
                        this.property_agent = '';
                        this.property_owner = '';
                        this.property_type = '';
                        this.managed_prop = 0;
                        this.property_remittance_office = '';
                        this.property_bank_account = '';
                        this.property_bank_account_name = '';
                        this.suburbValue = '';
                        this.suburb_list_filtered = [];
                        this.agent_country_list = this.dd_country_list;
                        this.suburbFilteredList(this.dd_state);
                        this.propertyStateFilteredList(this.dd_country);
                        this.property_owner_state_list = this.dd_state;
                        this.property_owner_country_list = this.dd_country;
                        this.suburbOwnerFilteredList(this.dd_state);
                        this.stateOwnerFilteredList(this.dd_country);
                        this.sales_reference = '';

                        this.initialiseLists();
                    }
                });
            }
        },
        resetLedgerForm: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            this.edit_form = false;
            this.property_name = this.property_name_old;
            this.property_address = this.property_address_old;
            this.property_suburb = this.property_suburb_old;
            this.property_post_code = this.property_post_code_old;
            this.property_state = this.property_state_old;

            this.ledger_status = this.ledger_status_old;
            this.property_total_balance = this.property_total_balance_old;

            this.suburbFilteredList(this.property_state_old);
            this.suburbValue = this.suburbValue_old;

            this.sl_agent = this.sl_agent_old;
            this.property_agent = this.property_agent_old;
            this.property_owner = this.property_owner_old;
            this.property_type = this.property_type_old;
            this.managed_prop = this.managed_prop_old;
            this.property_remittance_office = this.property_remittance_office_old;
            this.property_bank_account = this.property_bank_account_old;
            this.property_bank_account_name = this.property_bank_account_name_old;

            this.property_image = this.propert_image_old;
        },
        saveForm: function () {
            this.edit_form = false;
            this.error_msg = [];
            this.error_server_msg2 = [];
            if (this.property_name == '') {
                this.error_msg.push({ id: 'property_name', message: 'You have not specified a ledger name.' });
                this.$noty.error('You have not specified a ledger name.');
            }
            if (
                this.property_post_code &&
                (this.property_post_code.length < this.property_postcode_min_length ||
                    this.property_post_code.length > this.property_postcode_length) &&
                this.property_post_code != '' &&
                this.property_post_code != null
            ) {
                let prop_length_val = '4';
                if (this.property_postcode_min_length != this.property_postcode_length) {
                    prop_length_val = this.property_postcode_min_length + ' to ' + this.property_postcode_length;
                } else {
                    prop_length_val = this.property_postcode_length;
                }

                this.error_msg.push({
                    id: 'property_post_code',
                    message: 'Please check the entered ledger post code, must be ' + prop_length_val + ' characters.',
                });
                this.$noty.error(
                    'Please check the entered ledger post code, must be ' + prop_length_val + ' characters.',
                );
            }

            if (this.property_type == '' || this.property_type == null) {
                this.error_msg.push({ id: 'property_type', message: 'You have not specified a category.' });
                this.$noty.error('You have not specified a category.');
            }

            if (this.sl_agent == '' || this.sl_agent == null) {
                this.error_msg.push({ id: 'sl_agent', message: 'You have not specified a sales or leasing agent.' });
                this.$noty.error('You have not specified a sales or leasing agent.');
            }

            if (this.property_agent == '' || this.property_agent == null) {
                this.error_msg.push({ id: 'property_agent', message: 'You have not specified an agent company.' });
                this.$noty.error('You have not specified an agent company.');
            }

            if (this.property_remittance_office == '' || this.property_remittance_office == null) {
                this.error_msg.push({
                    id: 'property_remittance_office',
                    message: 'You have not specified a remittance office.',
                });
                this.$noty.error('You have not specified a remittance office.');
            }

            if (this.company_type == 1) {
                if (this.property_owner == '' || this.property_owner == null) {
                    this.error_msg.push({ id: 'property_owner', message: 'You have not specified a principal owner.' });
                    this.$noty.error('You have not specified a principal owner.');
                }
            } else {
                if (this.property_owner_code == '') {
                    this.error_msg.push({
                        id: 'property_owner_code',
                        message: 'You have not specified an owner code.',
                    });
                    this.$noty.error('You have not specified an owner code.');
                }

                if (this.verifyCode(this.property_owner_code)) {
                    this.error_msg.push({
                        id: 'property_owner_code',
                        message: 'Please use only alphanumeric characters for the owner code.',
                    });
                    this.$noty.error('Please use only alphanumeric characters for the owner code.');
                }

                if (this.property_owner_name == '') {
                    this.error_msg.push({
                        id: 'property_owner_name',
                        message: 'You have not specified an owner name.',
                    });
                    this.$noty.error('You have not specified an owner name.');
                }

                if (this.property_owner_address1 == '') {
                    this.error_msg.push({
                        id: 'property_owner_address1',
                        message: 'You have not specified an owner address.',
                    });
                    this.$noty.error('You have not specified an owner address.');
                }

                if (this.property_owner_suburb == '') {
                    this.error_msg.push({
                        id: 'property_owner_suburb',
                        message: 'You have not specified an owner suburb.',
                    });
                    this.$noty.error('You have not specified an owner suburb.');
                }

                if (this.property_owner_post == '') {
                    this.error_msg.push({
                        id: 'property_owner_post',
                        message: 'You have not specified an owner post code.',
                    });
                    this.$noty.error('You have not specified an owner post code.');
                }

                /*if (!$.isNumeric(this.property_owner_post) && this.property_owner_post != '') {
                    this.error_msg.push({
                        id: 'property_owner_post',
                        message: 'Please use only numeric characters for the owner post code.'
                    });
                    this.$noty.error('Please use only numeric characters for the owner post code.');
                }*/

                if (
                    (this.property_owner_post.length < this.company_country_defaults.post_code_min_length ||
                        this.property_owner_post.length > this.company_country_defaults.post_code_length) &&
                    this.property_owner_post != ''
                ) {
                    let comp_length_val = '4';
                    if (
                        this.company_country_defaults.post_code_min_length !=
                        this.company_country_defaults.post_code_length
                    ) {
                        comp_length_val =
                            this.company_country_defaults.post_code_min_length +
                            ' to ' +
                            this.company_country_defaults.post_code_length;
                    } else {
                        comp_length_val = this.company_country_defaults.post_code_length;
                    }

                    this.error_msg.push({
                        id: 'property_owner_post',
                        message:
                            'Please check the entered owner post code, must be ' + comp_length_val + ' characters.',
                    });
                    this.$noty.error(
                        'Please check the entered owner post code, must be ' + comp_length_val + ' characters.',
                    );
                }

                if (this.company_country_defaults.display_state && this.property_owner_state_list == '') {
                    this.error_msg.push({
                        id: 'property_owner_state',
                        message: 'You have not specified an owner state.',
                    });
                    this.$noty.error('You have not specified an owner state.');
                }

                if (this.property_owner_country_list == '') {
                    this.error_msg.push({
                        id: 'property_owner_country_list',
                        message: 'You have not specified an owner country.',
                    });
                    this.$noty.error('You have not specified an owner country.');
                }
            }

            if (this.property_calendar_type == '') {
                this.error_msg.push({
                    id: 'property_calendar_type',
                    message: 'You have not specified a property calendar.',
                });
                this.$noty.error('You have not specified a property calendar.');
            }

            if (this.property_bank_account == '') {
                this.error_msg.push({ id: 'property_bank_account', message: 'You have not specified a bank account.' });
                this.$noty.error('You have not specified a bank account.');
            }

            if (this.ledger_status == 1 && this.property_total_balance != 0) {
                this.error_msg.push({
                    id: 'ledger_status',
                    message:
                        'You are not allowed to make this ledger inactive because it still has a remaining balance',
                });
                this.$noty.error(
                    'You are not allowed to make this ledger inactive because it still has a remaining balance',
                );
            }
            // submit form if no error
            if (this.error_msg.length <= 0) {
                this.loading_setting = true;
                var formData = new FormData();

                formData.append('user_type', this.user_type);
                formData.append('form_mode', this.form_mode);
                formData.append('new_property_code', this.new_property_code);
                formData.append('property_code', this.property_code);
                formData.append('property_name', this.property_name);
                formData.append('property_address', this.property_address);
                formData.append('property_suburb', this.property_suburb);
                formData.append('property_post_code', this.property_post_code);
                formData.append('property_state', this.property_state);

                formData.append('sl_agent', this.sl_agent);
                formData.append('property_agent', this.property_agent);
                formData.append('property_owner', this.escapeParamValue(this.property_owner));
                if (this.company_type == 0) {
                    formData.append('property_owner', this.escapeParamValue(this.property_owner_code));
                }
                formData.append('property_type', this.property_type);
                formData.append('managed_prop', this.managed_prop);
                formData.append('property_remittance_office', this.property_remittance_office);

                formData.append('property_bank_account', this.property_bank_account);

                formData.append('property_owner_name', this.property_owner_name);
                formData.append('property_owner_code', this.property_owner_code);
                formData.append('property_owner_address1', this.property_owner_address1);
                formData.append('property_owner_address2', this.property_owner_address2);
                formData.append('property_owner_state', this.property_owner_state);
                formData.append('property_owner_country', this.property_owner_country);
                formData.append('property_owner_post', this.property_owner_post);
                formData.append('property_owner_email', this.property_owner_email);
                formData.append('property_owner_country_list', this.property_owner_country_list);
                formData.append('property_owner_state_list', this.property_owner_state_list);
                formData.append('property_owner_suburb', this.property_owner_suburb);
                formData.append('company_type', this.company_type);
                formData.append('ledger_status', this.ledger_status);
                formData.append('property_image_file', this.property_image_file);
                formData.append('sales_reference', this.sales_reference);
                formData.append('property_calendar_type', this.property_calendar_type);
                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/with-file-upload/sales-trust/ledger/update-or-create-ledger';
                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        if (this.form_mode == 1) {
                            this.fetchPropertyList();
                            this.property_owner = this.property_owner_code;
                            this.dd_owner_list.push({
                                value: this.property_owner_code,
                                label: this.property_owner_code + ' - ' + this.property_owner_name,
                            });
                            this.fetchOwnerList();
                            this.form_mode = 0; //change to edit and load defaults

                            this.SET_PUSH_DD_PROPERTY_LIST({
                                fieldGroup: 'Active',
                                fieldKey: this.new_property_code,
                                fieldValue: this.property_name,
                                field_group: 'Active',
                                field_key: this.new_property_code,
                                field_value: this.property_name,
                                value: this.new_property_code,
                                label: this.new_property_code + ' - ' + this.property_name,
                            });
                            this.property_code = this.new_property_code;
                            this.selectedPropertyCode = {
                                fieldGroup: 'Active',
                                fieldKey: this.new_property_code,
                                fieldValue: this.property_name,
                                field_group: 'Active',
                                field_key: this.new_property_code,
                                field_value: this.property_name,
                                value: this.new_property_code,
                                label: this.new_property_code + ' - ' + this.property_name,
                            };
                            this.new_property_code_ok = 0;
                            this.$noty.success('Ledger Added.');
                        } else this.$noty.success('Ledger Updated.');
                        this.company_type = 1;
                        this.isRemove = false;
                        this.forceRerender();
                        this.loading_setting = false;
                    } else {
                        this.error_server_msg2 = response.data.validation_errors;
                        this.loading_setting = false;
                    }
                });
            }
        },
        deleteLedger: function () {
            this.button_delete_ledger = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.property_code);
            formData.append('no_load', true);

            //Check for existing transactions first
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/check-ledger-transactions';

            this.$api.post(apiUrl, formData).then((response) => {
                let ledger_data = response.data;

                if (ledger_data.has_transactions > 0) {
                    this.$noty.error('Deleting not allowed. Transactions were found under this ledger.');
                    this.resetDeleteDialog();
                } else {
                    //Allow delete
                    let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/delete-ledger';

                    this.$api.post(apiUrl2, formData).then((response) => {
                        this.$refs.propertyCodeDropDown.select_val = '';
                        this.property_code = '';
                        this.fetchPropertyList();
                        let return_data = response.data;
                        if (return_data.status == 'success') {
                            this.$noty.success('Ledger Deleted.');
                            this.loading_setting = false;
                            this.button_delete_ledger = false;
                        } else {
                            this.$noty.error('Error encountered while trying to delete ledger.');
                            this.loading_setting = false;
                            this.button_delete_ledger = false;
                        }

                        this.resetDeleteDialog();
                    });
                }

                this.updatePageTitle('Manage Ledger');
            });
        },
        showConfirmationDialog: function () {
            this.warning_message =
                'This ledger will be deleted. You will not be able to undo this process. Would you like to proceed?';
            this.dialogConfirmation = true;
        },
        resetDeleteDialog: function () {
            this.dialogConfirmation = false;
        },
        loadLedgerDetails: function () {
            this.initialiseLists();
            if (this.property_code == '') return true;
            this.loading_setting = true;
            var formData = new FormData();
            formData.append('user_type', this.user_type);

            formData.append('property_code', this.property_code);
            formData.append('no_load', true);

            // get ledger details by code
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-ledger';

            this.$api.post(apiUrl, formData).then((response) => {
                let ledger_data = response.data;

                //load to form fields
                this.property_name = ledger_data.property_name;
                this.property_name_old = ledger_data.property_name;
                this.property_address = ledger_data.property_street == null ? '' : ledger_data.property_street;
                this.property_address_old = ledger_data.property_street == null ? '' : ledger_data.property_street;
                this.property_suburb =
                    ledger_data.property_city == null || ledger_data.property_city == 'null'
                        ? ''
                        : ledger_data.property_city;
                this.property_suburb_old =
                    ledger_data.property_city == null || ledger_data.property_city == 'null'
                        ? ''
                        : ledger_data.property_city;
                this.property_post_code = ledger_data.property_postcode == null ? '' : ledger_data.property_postcode;
                this.property_post_code_old =
                    ledger_data.property_postcode == null ? '' : ledger_data.property_postcode;
                this.property_state = ledger_data.property_state;
                this.property_state_old = ledger_data.property_state;
                this.sales_reference = ledger_data.sales_reference;

                this.ledger_status = parseInt(ledger_data.property_inactive_status);
                this.ledger_status_old = parseInt(ledger_data.property_inactive_status);
                this.property_total_balance = parseFloat(ledger_data.property_total_balance);
                this.property_total_balance_old = parseFloat(ledger_data.property_total_balance);

                this.suburbFilteredList(ledger_data.property_state);
                this.suburbValue = { suburb: ledger_data.property_city, pcode: ledger_data.property_postcode };
                this.suburbValue_old = { suburb: ledger_data.property_city, pcode: ledger_data.property_postcode };

                this.sl_agent = ledger_data.sl_agent_id;
                this.sl_agent_old = ledger_data.sl_agent_id;
                this.property_agent = ledger_data.property_agent;
                this.property_agent_old = ledger_data.property_agent;
                this.property_owner = ledger_data.property_owner;
                this.property_owner_old = ledger_data.property_owner;
                this.property_type = ledger_data.property_type;
                this.property_type_old = ledger_data.property_type;
                this.managed_prop = parseInt(ledger_data.managed_prop);
                this.managed_prop_old = parseInt(ledger_data.managed_prop);
                this.property_remittance_office =
                    ledger_data.property_remittance_office == null || ledger_data.property_remittance_office == 'null'
                        ? ''
                        : ledger_data.property_remittance_office;
                this.property_remittance_office_old =
                    ledger_data.property_remittance_office == null || ledger_data.property_remittance_office == 'null'
                        ? ''
                        : ledger_data.property_remittance_office;
                this.property_bank_account = ledger_data.property_bank_account;
                this.property_bank_account_old = ledger_data.property_bank_account;
                this.property_bank_account_name =
                    ledger_data.property_bank_account + ' - ' + ledger_data.property_bank_account_name;
                this.property_bank_account_name_old =
                    ledger_data.property_bank_account + ' - ' + ledger_data.property_bank_account_name;
                if (ledger_data.property_image)
                    this.property_image =
                        this.$uploadedAssetDomain + 'assets/images/property-image/' + ledger_data.property_image;
                else this.property_image = this.default_property_image;
                this.loading_setting = false;
                this.propert_image_old = this.property_image;
                this.property_calendar_type = ledger_data.property_calendar;

                this.updatePageTitle(this.property_code + ' - ' + this.property_name);
            });
        },
        loadCompanyDetails: function () {
            this.loading_setting = true;
            let lease_company_code = this.property_owner;
            if (lease_company_code !== '' && !(lease_company_code == null)) {
                var formData = new FormData();

                formData.append('user_type', this.user_type);
                formData.append('company_code', lease_company_code);
                formData.append('no_load', true);

                this.$api.post('company/load-company-details', formData).then((response) => {
                    this.property_owner_name = response.data.tenant_name;
                    this.property_owner_address1 = response.data.tenant_address_1;
                    this.property_owner_address2 = response.data.tenant_address_2;
                    this.property_owner_suburb = response.data.tenant_suburb;
                    this.property_owner_email = response.data.tenant_email;
                    this.property_owner_post = response.data.tenant_post_code;
                    this.property_owner_state = response.data.tenant_state.field_value;
                    this.property_owner_country = response.data.tenant_country.field_value;
                    this.property_country_code = response.data.tenant_country_code;

                    this.property_owner_name_old = response.data.tenant_name;
                    this.property_owner_address1_old = response.data.tenant_address_1;
                    this.property_owner_address2_old = response.data.tenant_address_2;
                    this.property_owner_suburb_old = response.data.tenant_suburb;
                    this.property_owner_email_old = response.data.tenant_email;
                    this.property_owner_post_old = response.data.tenant_post_code;
                    this.property_owner_state_old = response.data.tenant_state.field_value;
                    this.property_owner_country_old = response.data.tenant_country.field_value;

                    this.agent_state_list = response.data.tenant_state_list;
                    this.agent_country_list = response.data.tenant_country_list;
                    this.loading_setting = false;
                });

                /*axios.post(this.cirrus8_api_url + 'api/company/load-company-details', formData)
                    .then(response => {
                        this.property_owner_name = response.data.tenant_name;
                        this.property_owner_address1 = response.data.tenant_address_1;
                        this.property_owner_address2 = response.data.tenant_address_2;
                        this.property_owner_suburb = response.data.tenant_suburb;
                        this.property_owner_email = response.data.tenant_email;
                        this.property_owner_post = response.data.tenant_post_code;
                        this.property_owner_state = response.data.tenant_state.field_value;
                        this.property_owner_country = response.data.tenant_country.field_value;

                        this.property_owner_name_old = response.data.tenant_name;
                        this.property_owner_address1_old = response.data.tenant_address_1;
                        this.property_owner_address2_old = response.data.tenant_address_2;
                        this.property_owner_suburb_old = response.data.tenant_suburb;
                        this.property_owner_email_old = response.data.tenant_email;
                        this.property_owner_post_old = response.data.tenant_post_code;
                        this.property_owner_state_old = response.data.tenant_state.field_value;
                        this.property_owner_country_old = response.data.tenant_country.field_value;

                        this.agent_state_list = response.data.tenant_state_list;
                        this.agent_country_list = response.data.tenant_country_list;
                        this.loading_setting = false;
                    });*/
            } else {
                this.loading_setting = false;
            }
        },
        resetCompanyDetail: function () {
            if (this.company_type == 1) {
                if (this.property_owner) {
                    this.property_owner_name = this.property_owner_name_old;
                    this.property_owner_address1 = this.property_owner_address1_old;
                    this.property_owner_address2 = this.property_owner_address2_old;
                    this.property_owner_suburb = this.property_owner_suburb_old;
                    this.property_owner_email = this.property_owner_email_old;
                    this.property_owner_post = this.property_owner_post_old;
                    this.property_owner_state = this.property_owner_state_old;
                    this.property_owner_country = this.property_owner_country_old;
                    this.property_country_code = this.property_country_code_old;
                } else {
                    this.property_owner_name = '';
                    this.property_owner_address1 = '';
                    this.property_owner_address2 = '';
                    this.property_owner_suburb = '';
                    this.property_owner_email = '';
                    this.property_owner_post = '';
                    this.property_owner_state = '';
                    this.property_owner_country = '';
                    this.property_country_code = '';
                }
            } else {
                this.property_owner_code = '';
                this.property_owner_name = '';
                this.property_owner_address1 = '';
                this.property_owner_address2 = '';
                this.property_owner_suburb = '';
                this.property_owner_email = '';
                this.property_owner_post = '';
                this.property_owner_state = '';
                this.property_owner_country = '';
                this.property_country_code = '';
                this.property_owner_code = '';
                this.property_owner_state_list = this.dd_state;
                this.property_owner_country_list = this.dd_country;
                this.suburbOwnerValue = '';
                this.stateOwnerFilteredList(this.dd_country);
                this.propertyStateFilteredList(this.dd_country);
                this.suburbOwnerFilteredList(this.dd_state);
            }
        },

        fetchSuburb: function () {
            this.agent_suburb_list = suburbList;
        },

        suburbSelected(data) {
            if (this.$refs.refSuburb.selectedValues.length == 0) {
                if (data) {
                    if (data.suburb !== undefined) {
                        this.property_suburb = data.suburb;
                    }

                    if (data.pcode !== undefined) {
                        this.property_post_code = data.pcode;
                    }
                } else {
                    this.property_suburb = this.property_suburb;
                }
            } else {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_post_code = data.pcode;
                }
            }
        },
        suburbOwnerSelected(data) {
            if (this.$refs.refOwnerSuburb.selectedValues.length == 0) {
                if (data) {
                    this.property_owner_suburb = data.suburb;
                    this.property_owner_post = data.pcode;
                } else {
                    this.property_owner_suburb = this.property_owner_suburb;
                }
            } else {
                if (data) {
                    this.property_owner_suburb = data.suburb;
                    this.property_owner_post = data.pcode;
                }
            }
        },
        suburbFilteredList(stateVal) {
            this.suburb_list_filtered = [];
            let filteredItem = [];

            if (stateVal != '') {
                if (suburbList.length > 0) {
                    $.each(suburbList, function (item, value) {
                        if (value.State === stateVal) {
                            value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                            value['value'] = { suburb: value.suburb, pcode: value.pcode };
                            filteredItem = filteredItem.concat(value);
                        }
                    });
                }
            }

            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }

            this.suburb_list_filtered = filteredItem;
        },

        propertyStateFilteredList(country_code) {
            if (country_code == 'AU') this.post_code_length = 4;
            else if (country_code == 'US') this.post_code_length = 5;
            else if (country_code == 'SG') this.post_code_length = 6;
            else this.post_code_length = 8;

            if (this.property_owner_post.length > this.post_code_length)
                this.property_owner_post = this.property_owner_post.substring(0, this.post_code_length);

            var formData = new FormData();
            formData.append('countryCode', country_code);
            formData.append('no_load', true);

            this.$api.post('loadAPIStatesDropDownList', formData).then((response) => {
                this.state_list = response.data.stateList;
            });
        },

        suburbOwnerFilteredList(stateVal) {
            this.suburbOwner_list_filtered = [];
            let filteredOwnerItem = [];

            if (stateVal != '') {
                if (suburbList.length > 0) {
                    $.each(suburbList, function (item, value) {
                        if (value.State === stateVal) {
                            value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                            value['value'] = { suburb: value.suburb, pcode: value.pcode };
                            filteredOwnerItem = filteredOwnerItem.concat(value);
                        }
                    });
                }
            }

            if (filteredOwnerItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredOwnerItem.push(temp_value);
            }

            this.suburbOwner_list_filtered = filteredOwnerItem;
        },

        stateOwnerFilteredList(country_code) {
            if (country_code == 'AU') this.post_code_length = 4;
            else if (country_code == 'US') this.post_code_length = 5;
            else if (country_code == 'SG') this.post_code_length = 6;
            else this.post_code_length = 8;

            if (this.property_owner_post.length > this.post_code_length)
                this.property_owner_post = this.property_owner_post.substring(0, this.post_code_length);

            var formData = new FormData();
            formData.append('countryCode', country_code);
            formData.append('no_load', true);

            this.$api.post('loadAPIStatesDropDownList', formData).then((response) => {
                this.stateOwner_list_filtered = response.data.stateList;
                this.property_calendar_type_list = response.data.calendar_type_list;
                if (response.data.default_calendar) this.property_calendar_type = response.data.default_calendar;
            });
        },

        verifyCode: function (inputString) {
            var newStr = inputString.replace(/[^a-zA-Z0-9-]/g, '');
            return newStr.length != inputString.length;
        },

        postcodeKeyDown: function () {
            const char = String.fromCharCode(event.keyCode);
            if (!/[0-9]/.test(char)) {
                event.preventDefault();
            }
        },

        onButtonClick() {
            this.isSelecting = true;
            window.addEventListener(
                'focus',
                () => {
                    this.isSelecting = false;
                },
                { once: true },
            );

            this.$refs.uploader.click();
        },
        onFileChanged(e) {
            var files = e.target.files || e.dataTransfer.files;

            if (files[0].type == 'image/png' || files[0].type == 'image/jpg' || files[0].type == 'image/jpeg') {
                if (!files.length) return;

                if (files[0].size <= 5242880) {
                    this.createImage(files[0]);
                } else {
                    alert('Unable to upload file because the size exceeds the maximum allowed file size of 5MB.');
                }
            } else {
                this.$noty.error('Invalid file type. Please upload only image file.');
            }
        },
        createImage(file) {
            var image = new Image();
            var reader = new FileReader();
            this.old_property_image = this.property_image;
            this.property_image_file = file;
            reader.onload = (e) => {
                this.property_image = e.target.result;
            };
            reader.readAsDataURL(file);
            this.isRemove = true;
        },
        removeImage: function (item) {
            this.property_image = this.old_property_image;
            this.property_image_file = '';
            this.isRemove = false;
        },
        getNameFromList: function (arrayVar, value, camelCase, getLabel) {
            if (arrayVar && value) {
                if (camelCase) {
                    var fieldData = arrayVar.find(({ fieldKey }) => fieldKey === value);

                    if (fieldData) {
                        if (getLabel !== undefined) {
                            return fieldData.label;
                        } else {
                            return fieldData.fieldValue;
                        }
                    } else {
                        return '';
                    }
                } else {
                    var fieldData = arrayVar.find(({ field_key }) => field_key === value);

                    if (fieldData) {
                        if (getLabel !== undefined) {
                            return fieldData.label;
                        } else {
                            return fieldData.field_value;
                        }
                    } else {
                        return '';
                    }
                }
            }
        },

        openLedgerChangelogModal: function () {
            this.show_change_log_modal = true;
        },

        loadCompanyCountryDefaults: function (country) {
            var form_data = new FormData();
            form_data.append('country', country);
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.company_country_defaults = response.data.default;
                this.suburb_label = this.ucwords(this.company_country_defaults.suburb);
            });
        },
    },
    watch: {
        dd_property_list: function () {
            if (this.dd_property_list.length > 0 && this.$refs.propertyCodeDropDown && this.property_code != '') {
                this.$refs.propertyCodeDropDown.forceChange(this.property_code, this.dd_property_list);
            }

            this.ledger_list_loading = false;
        },
        property_code: function () {
            if (this.property_code) {
                this.loadLedgerDetails();
            } else {
                this.property_code = '';
            }

            this.edit_form = false;
        },
        new_property_code: function () {
            this.new_property_code = this.new_property_code.toUpperCase().trim();
            if (this.new_property_code.length > 10) this.new_property_code = this.new_property_code.substring(0, 10);
        },
        property_owner: function () {
            this.loadCompanyDetails();
        },
        company_type: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            this.resetCompanyDetail();
        },
        property_owner_code: function () {
            this.property_owner_code = this.property_owner_code.toUpperCase().trim();
            if (this.property_owner_code.length > 10)
                this.property_owner_code = this.property_owner_code.substring(0, 10);
        },
        property_suburb: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchSuburb;
                this.property_suburb = newVal;
            }
        },
        property_owner_suburb: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchOwnerSuburb;
                this.property_owner_suburb = newVal;
            }
        },
        property_owner_country_list: function () {
            this.loadCompanyCountryDefaults(this.property_owner_country_list);
            this.property_owner_state_list = '';
        },
        property_owner_country: function () {
            this.loadCompanyCountryDefaults(this.property_country_code);
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
    },
    mixins: [global_mixins],
};
</script>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
