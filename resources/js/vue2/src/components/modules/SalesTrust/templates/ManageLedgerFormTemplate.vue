<style>
#content {
    overflow-x: hidden !important;
}

.shortcut-div > .v-toolbar__content {
    height: 30px !important;
}

.v-card > .shortcut-div {
    height: 30px !important;
}

.v-autocomplete__content.v-menu__content,
.v-autocomplete__content.v-menu__content .v-card {
    border-radius: 0;
    font-weight: normal !important;
}

.v-menu__content {
    position: fixed !important;
    max-height: 170px !important;
    font-weight: normal !important;
    font-size: 11px !important;
    z-index: 999999 !important;
    color: inherit !important;
}

.v-list--dense .v-list-item .v-list-item__subtitle,
.v-list--dense .v-list-item .v-list-item__title,
.v-list-item--dense .v-list-item__subtitle,
v-list-item--dense .v-list-item__title {
    font-weight: normal !important;
}

.v-list-item .v-list-item__mask {
    color: #ffffff !important;
    background: #00baf2 !important;
    font-weight: bold !important;
}

.v-list-item.v-list-item--highlighted:before {
    opacity: 0 !important;
}

.v-btn--fab.v-size--x-small .v-icon,
.v-btn--icon.v-size--x-small .v-icon {
    font-size: 21px;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <v-toolbar flat>
            <v-toolbar-title>
                <h1
                    class="ui header"
                    style="color: #7f8c8d; font-size: 2vw; font-weight: normal; letter-spacing: -2px"
                >
                    {{ manage_ledger_title }}
                </h1>
            </v-toolbar-title>

            <div class="flex-grow-1"></div>

            <v-btn
                color="primary"
                class="v-step-new-lease-button"
                v-if="form_mode === 0"
                @click="newProperty()"
            >
                <v-icon>add</v-icon>
                New Ledger
            </v-btn>
            <v-btn
                color="black"
                class="v-step-cancel-lease-button"
                dark
                v-if="form_mode === 1"
                @click="cancelNewProperty()"
            >
                <v-icon color="red">close</v-icon>
                Cancel Ledger
            </v-btn>
        </v-toolbar>

        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>

        <div class="page-form">
            <v-row
                v-if="form_mode === 0"
                class="form-row"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Ledger</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <!--O: dropdown START-->
                    <!--O-->
                    <!--                    <cirrus-single-select-group v-model="property_code" :options="dd_property_list"-->
                    <!--                                                group-values="fieldGroupValues" group-label="fieldGroupNames"-->
                    <!--                                                track-by="value" label="label" :selectedValue="selectedPropertyCode"-->
                    <!--                    />-->
                    <!--U-->
                    <cirrus-single-select-group
                        v-model="property_code"
                        :options="dd_property_list"
                        group-values="fieldGroupValues"
                        group-label="fieldGroupNames"
                        track-by="field_key"
                        label="field_value"
                        :selectedValue="selectedPropertyCode"
                        ref="propertyCodeDropDown"
                    />
                    <div style="margin-top: -30px; margin-left: 302px">
                        <sui-button
                            style="height: 30px !important"
                            :loading="dd_property_list.length <= 0"
                            class="dropdown-button"
                            icon="caret right icon"
                            @click="forceRerender()"
                        />
                    </div>
                    <!--U: dropdown END-->
                </v-col>
            </v-row>
            <v-row
                v-if="form_mode === 1"
                class="form-row"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Ledger Code</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-text-field
                        v-model="new_property_code"
                        :maxlength="10"
                        dense
                    />
                    <v-btn
                        color="primary"
                        x-small
                        @click="checkPropertyCode()"
                        >Create New</v-btn
                    >
                    <v-chip
                        v-if="error_msg.length > 0 && errorData.id === 'new_property_code'"
                        v-for="(errorData, index) in error_msg"
                        :key="index"
                        outlined
                        color="error"
                    >
                        <v-icon left>error</v-icon>
                        {{ errorData.message }}
                    </v-chip>
                </v-col>
            </v-row>
        </div>

        <!--o-->
        <div v-if="property_code !== '' || new_property_code_ok">
            <!-- general details section -->
            <div id="general_details_section">
                <v-card
                    id="general_details_header"
                    dark
                    color="titleHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h6 class="title font-weight-black">General Details</h6>
                        <v-spacer></v-spacer>
                        <v-btn
                            x-small
                            icon
                            @click="openLedgerChangelogModal()"
                            v-if="form_mode !== 1"
                        >
                            <v-icon>history</v-icon>
                        </v-btn>
                    </v-card-actions>
                </v-card>
                <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                <div
                    v-if="!loading_setting"
                    class="page-form"
                >
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="3"
                            md="3"
                            class="form-input"
                        >
                            <!--							<div style="max-height: 300px !important;"> -->
                            <v-row>
                                <v-col>
                                    <div v-if="isRemove">
                                        <v-btn
                                            background
                                            depressed
                                            @click="removeImage"
                                        >
                                            <v-icon
                                                center
                                                color="primary"
                                            >
                                                close
                                            </v-icon>
                                        </v-btn>
                                    </div>
                                    <div v-else>
                                        <v-btn
                                            background
                                            depressed
                                            :loading="isSelecting"
                                            @click="onButtonClick"
                                        >
                                            <v-icon
                                                center
                                                color="primary"
                                            >
                                                add_a_photo
                                            </v-icon>
                                        </v-btn>
                                        <input
                                            ref="uploader"
                                            class="d-none"
                                            type="file"
                                            accept="image/*"
                                            @change="onFileChanged"
                                        />
                                    </div>
                                </v-col>
                            </v-row>
                            <v-row>
                                <v-col>
                                    <v-img
                                        max-height="250px"
                                        v-bind:src="property_image"
                                        position="center bottom"
                                        contain
                                        dense
                                    />
                                </v-col>
                            </v-row>
                            <!--</div>-->
                        </v-col>
                        <v-col
                            xs="12"
                            sm="9"
                            md="9"
                            class="form-input"
                        >
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Name</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-text-field
                                        v-model="property_name"
                                        :maxlength="40"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'property_name'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Address</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-text-field
                                        v-model="property_address"
                                        :maxlength="80"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'property_address'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>

                            <!-- <v-row class="form-row">
                              <v-col xs="12" sm="2" md="2" class="form-label">Post Town</v-col>
                              <v-col xs="12" sm="10" md="10" class="form-input">
                                <cirrus-autocomplete-suburb
                                  v-model="property_suburb"
                                  :options="suburb_list_filtered"
                                  :selectedValue="suburbValue"
                                  @input="suburbSelected(property_suburb)"
                                  dense
                                />
                              </v-col>
                            </v-row>	-->

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >{{ suburb_label }}</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-combobox
                                        v-model="property_suburb"
                                        :maxlength="40"
                                        :items="suburb_list_filtered"
                                        item-value="label"
                                        item-text="label"
                                        @change="suburbSelected(property_suburb)"
                                        auto-select-first
                                        hide-selected
                                        persistent-hint
                                        append-icon
                                        :search-input.sync="searchSuburb"
                                        :hide-no-data="!searchSuburb"
                                        dense
                                        ref="refSuburb"
                                        flat
                                    >
                                        <template v-slot:no-data>
                                            <v-list-item>
                                                <v-chip
                                                    v-model="searchSuburb"
                                                    small
                                                >
                                                    {{ searchSuburb }}
                                                </v-chip>
                                            </v-list-item>
                                        </template>
                                    </v-combobox>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Post Code</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-text-field
                                        v-model="property_post_code"
                                        :maxlength="4"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'property_post_code'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >State</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="property_state"
                                        :options="state_list"
                                        @input="suburbFilteredList(property_state)"
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'property_state'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Category</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="property_type"
                                        :options="dd_ledger_category_list"
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'property_type'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>

                            <v-row
                                class="form-row"
                                v-if="property_type == 'LEASE' || property_type == 'SALE'"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >{{ property_type == 'LEASE' ? 'Leasing Reference' : 'Sales Reference' }}</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-text-field
                                        v-model="sales_reference"
                                        :maxlength="15"
                                        dense
                                    />
                                </v-col>
                            </v-row>

                            <!-- MAKE LEDGER INACTIVE - START -->
                            <v-row
                                class="form-row"
                                v-if="form_mode !== 1"
                            >
                                <!-- <v-col xs="12" sm="2" md="2" class="form-label required">Ledger Status</v-col> -->
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Ledger Status</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <!--                            <v-btn-toggle v-model="ledger_status" mandatory :disabled="property_total_balance > 0">-->
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-model="ledger_status"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                        >
                                            Active
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            Inactive
                                        </v-btn>
                                    </v-btn-toggle>
                                    <span style="display: none"
                                        >Selected Value: {{ ledger_status }} ~ {{ property_total_balance }}</span
                                    >
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'ledger_status'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>
                            <!-- MAKE LEDGER INACTIVE - END -->

                            <!-- DELETE LEDGER - START -->
                            <!--                            <v-row class="form-row" v-if="form_mode!==1">-->
                            <!--                              <v-col xs="12" sm="2" md="2" class="form-label">&nbsp;</v-col>-->
                            <!--                              <v-col xs="12" sm="10" md="10" class="form-input">-->
                            <v-divider v-if="form_mode !== 1"></v-divider>
                            <br />
                            <v-btn
                                class="v-step-save-1-button"
                                v-if="form_mode !== 1"
                                @click="deleteLedger()"
                                color="primary"
                                dark
                                absolute
                                right
                                small
                            >
                                Delete Ledger
                            </v-btn>
                            <br /><br /><br />
                            <!--                              </v-col>-->
                            <!--                            </v-row>-->
                            <!-- DELETE LEDGER - END -->
                        </v-col>
                    </v-row>
                </div>
            </div>
            <!-- end of general details section -->

            <!-- management details section -->
            <div id="management_details_section">
                <v-card
                    id="management_details_header"
                    dark
                    color="titleHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h4 class="title font-weight-black required">Agent Details</h4>
                    </v-card-actions>
                </v-card>
                <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                <div
                    v-if="!loading_setting"
                    class="page-form"
                >
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Sales or Leasing Agent</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-single-select
                                v-model="sl_agent"
                                :options="dd_sl_agent_list"
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'sl_agent'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Agent Company</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-single-select
                                v-model="property_agent"
                                :options="dd_agent_list"
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_agent'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Remittance Office</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-single-select
                                v-model="property_remittance_office"
                                :options="dd_office_list"
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_remittance_office'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>
                </div>
            </div>
            <!-- end of management details section -->

            <!-- owner details section -->
            <div id="owner_details_section">
                <v-card
                    id="management_details_header"
                    dark
                    color="titleHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h4 class="title font-weight-black required">Owner Details</h4>
                    </v-card-actions>
                </v-card>
                <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                <div
                    v-if="!loading_setting"
                    class="page-form"
                >
                    <v-row
                        class="form-row"
                        v-if="form_mode === 1"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Company Type</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <v-btn-toggle
                                v-model="company_type"
                                mandatory
                            >
                                <v-btn
                                    small
                                    text
                                >
                                    New
                                </v-btn>
                                <v-btn
                                    small
                                    text
                                >
                                    Existing
                                </v-btn>
                            </v-btn-toggle>
                        </v-col>
                    </v-row>

                    <v-row
                        class="form-row"
                        v-if="company_type != 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-single-select
                                v-model="property_owner"
                                :options="dd_owner_list"
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_owner'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>

                    <v-row
                        class="form-row"
                        v-if="company_type == 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner Code</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <v-text-field
                                v-model="property_owner_code"
                                :maxlength="10"
                                dense
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_owner_code'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner Name</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                            v-if="company_type == 1"
                        >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                >{{ property_owner_name }}</v-col
                            >
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                            v-else
                        >
                            <v-text-field
                                v-model="property_owner_name"
                                :maxlength="240"
                                dense
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_owner_name'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner Address</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                            v-if="company_type == 1"
                        >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                >{{ property_owner_address1 }}</v-col
                            >
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                            v-else
                        >
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-text-field
                                        v-model="property_owner_address1"
                                        :maxlength="37"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'property_owner_address1'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-text-field
                                        v-model="property_owner_address2"
                                        :maxlength="37"
                                        dense
                                    />
                                </v-col>
                            </v-row>
                        </v-col>
                    </v-row>

                    <!-- v-row class="form-row" v-if="company_type == 0" >
                        <v-col xs="12" sm="2" md="2" class="form-label required">Owner Post Town</v-col>
                        <v-col xs="12" sm="10" md="10" class="form-input">
                            <cirrus-autocomplete-suburb
                v-model="property_owner_suburb"
                :options="suburbOwner_list_filtered"
                :selectedValue="suburbOwnerValue"
                @input="suburbOwnerSelected(property_owner_suburb)"
                dense
                            />
              </v-combobox>
                            <v-chip v-if="error_msg.length>0 && errorData.id === 'property_owner_suburb'"
                                    v-for="(errorData,index) in error_msg" :key="index" outlined color="error">
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row> -->

                    <v-row
                        class="form-row"
                        v-if="company_type == 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner {{ suburb_label }}</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <v-combobox
                                v-model="property_owner_suburb"
                                :items="suburbOwner_list_filtered"
                                item-value="label"
                                item-text="label"
                                @change="suburbOwnerSelected(property_owner_suburb)"
                                auto-select-first
                                hide-selected
                                persistent-hint
                                append-icon
                                :search-input.sync="searchOwnerSuburb"
                                :hide-no-data="!searchOwnerSuburb"
                                dense
                                flat
                                ref="refOwnerSuburb"
                                :maxlength="40"
                            >
                                <template v-slot:no-data>
                                    <v-list-item>
                                        <v-chip
                                            v-model="searchOwnerSuburb"
                                            small
                                        >
                                            {{ searchOwnerSuburb }}
                                        </v-chip>
                                    </v-list-item>
                                </template>
                            </v-combobox>
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_owner_suburb'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>

                    <v-row
                        class="form-row"
                        v-if="company_type != 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner {{ suburb_label }}</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                >{{ property_owner_suburb }}</v-col
                            >
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner Post Code</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                            v-if="company_type == 1"
                        >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                >{{ property_owner_post }}</v-col
                            >
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                            v-else
                        >
                            <v-text-field
                                v-model="property_owner_post"
                                dense
                                :maxlength="post_code_length"
                                :readonly="company_type == 1"
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_owner_post'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>

                    <v-row
                        class="form-row"
                        v-if="company_type == 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner State</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-single-select
                                v-model="property_owner_state_list"
                                :options="stateOwner_list_filtered"
                                @input="suburbOwnerFilteredList(property_owner_state_list)"
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_owner_state'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>

                    <v-row
                        class="form-row"
                        v-if="company_type != 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner State</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                >{{ property_owner_state }}</v-col
                            >
                        </v-col>
                    </v-row>

                    <v-row
                        class="form-row"
                        v-if="company_type == 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner Country</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-single-select
                                v-model="property_owner_country_list"
                                :options="agent_country_list"
                                @input="stateOwnerFilteredList(property_owner_country_list)"
                            />
                            <v-chip
                                v-if="error_msg.length > 0 && errorData.id === 'property_owner_country_list'"
                                v-for="(errorData, index) in error_msg"
                                :key="index"
                                outlined
                                color="error"
                            >
                                <v-icon left>error</v-icon>
                                {{ errorData.message }}
                            </v-chip>
                        </v-col>
                    </v-row>

                    <v-row
                        class="form-row"
                        v-if="company_type != 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Owner Country</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                >{{ property_owner_country }}</v-col
                            >
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label"
                            >Owner E-mail</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                            v-if="company_type == 1"
                        >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                v-if="property_owner_email && property_owner_email.includes(';')"
                            >
                                <v-item-list
                                    v-for="(data, index) in property_owner_email.split(';')"
                                    :key="index"
                                >
                                    <a :href="'mailto:' + data.trim()">{{ data.trim() }}</a
                                    ><span v-if="property_owner_email.split(';').length - 1 != index">; </span>
                                </v-item-list>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                v-else
                            >
                                <a
                                    v-if="property_owner_email"
                                    :href="'mailto:' + property_owner_email.trim()"
                                    >{{ property_owner_email }}</a
                                >
                            </v-col>
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                            v-else
                        >
                            <v-text-field
                                v-model="property_owner_email"
                                :maxlength="400"
                                dense
                            />
                        </v-col>
                    </v-row>
                </div>
            </div>
            <!-- end of owner section -->

            <!-- bank details section -->
            <div id="bank_details_section">
                <v-card
                    id="bank_details_header"
                    dark
                    color="titleHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h4 class="title font-weight-black">Bank Account</h4>
                    </v-card-actions>
                </v-card>
                <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                <div
                    class="page-form"
                    v-if="!loading_setting"
                >
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Bank Account</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <div v-if="form_mode == 1">
                                <cirrus-single-select
                                    v-model="property_bank_account"
                                    :options="dd_bank_account_list"
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'property_bank_account'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </div>
                            <div v-if="form_mode == 0">
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    >{{ property_bank_account_name }}</v-col
                                >
                            </div>
                        </v-col>
                    </v-row>
                </div>
            </div>
            <!-- end of management details section -->

            <v-divider></v-divider>
            <br />
            <v-btn
                class="v-step-save-1-button"
                @click="saveForm()"
                color="primary"
                dark
                absolute
                right
                small
            >
                {{ save_button_label }}
            </v-btn>
            <br /><br /><br />

            <commision-component
                v-if="form_mode == 0 && (property_type == 'LEASE' || property_type == 'SALE')"
                id="commission-section"
                :reloadComponents="reloadComponents"
                :selected_property_code="property_code"
                :category="property_type"
                :agent_state_list="agent_state_list"
                :agent_country_list="agent_country_list"
                :agent_suburb_list="agent_suburb_list"
                :default_country="dd_country"
                :default_state="dd_state"
                :form_mode="form_mode"
            ></commision-component>

            <sub-ledger-component
                v-if="form_mode == 0"
                id="sub-ledger-section"
                :reloadComponents="reloadComponents"
                :selected_property_code="property_code"
                :agent_state_list="agent_state_list"
                :agent_country_list="agent_country_list"
                :agent_suburb_list="agent_suburb_list"
                :default_country="dd_country"
                :default_state="dd_state"
                :category="property_type"
            ></sub-ledger-component>
            <br /><br /><br />

            <sundries-component
                v-if="form_mode == 0"
                id="sundries-section"
                :reloadComponents="reloadComponents"
                :selected_property_code="property_code"
            ></sundries-component>

            <br /><br /><br />

            <calendar-component
                v-if="form_mode == 0"
                id="calendar-section"
                :reloadComponents="reloadComponents"
                :selected_property_code="property_code"
            ></calendar-component>

            <!-- LEDGER CHANGELOG -->
            <v-dialog
                top
                v-model="show_change_log_modal"
                width="1200"
                content-class="c8-page"
                style="width: 60%"
            >
                <v-card>
                    <v-card-title class="headline">
                        <span>Ledger Changelog</span>
                        <a
                            href="#"
                            class="dialog-close"
                            @click="show_change_log_modal = false"
                        >
                            <v-icon>mdi-close</v-icon>
                        </a>
                    </v-card-title>

                    <div
                        class="body c8-page"
                        style="height: auto; min-height: initial; padding: 10px"
                    >
                        <v-card-text>
                            <activity-logs-component
                                v-if="show_change_log_modal"
                                :property_code="property_code"
                                :form_section="'main'"
                            ></activity-logs-component>
                            <!--//ledger-update-log-component v-if="show_change_log_modal" :property_code="property_code" :form_section="'main'"></ledger-update-log-component//-->
                        </v-card-text>
                    </div>
                </v-card>
            </v-dialog>
            <!-- END OF LEDGER CHANGELOG -->
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import SuiVue from 'semantic-ui-vue';
import Multiselect from 'vue-multiselect';
import vSelect from 'vue-select';
import global_mixins from '../../../../plugins/mixins';
//import CirrusAutocompleteSuburb from '../../../../components/elements/CirrusAutocompleteSuburb'
import { mapState, mapActions, mapMutations } from 'vuex';
import suburbList from '../../../../plugins/australianSuburb.json';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
// import modalAccountIncome from './components/modules/properties/property_budget/administrator/modalAccountIncome.vue'

Vue.use(SuiVue);
Vue.component('v-select', vSelect);
Vue.component('multiselect', Multiselect);
//Vue.component('cirrus-autocomplete-suburb', CirrusAutocompleteSuburb);

// Vue.component('ItemTemplateAutoPop', ItemTemplateAutoPop);
// import SuiVue from 'semantic-ui-vue'

Vue.component('sub-ledger-component', require('../forms/SubLedgerForm.vue').default);
Vue.component('calendar-component', require('../forms/CalendarForm.vue').default);
Vue.component('sundries-component', require('../forms/SundriesForm.vue').default);
Vue.component('commision-component', require('../forms/CommisionForm.vue').default);
Vue.component('ledger-update-log-component', require('../forms/LedgerUpdateLog.vue').default);
Vue.component('activity-logs-component', require('../forms/ActivityLogs.vue').default);
// Vue.component('lease-diary-component', require('../forms/leaseDiary').default);
// Vue.component('lease-notes-component', require('../forms/leaseNotes').default);
// Vue.component('lease-guarantee-component', require('../forms/leaseGuarantee').default);
// Vue.component('lease-insurance-component', require('../forms/leaseInsurance').default);
// Vue.component('lease-inspection-component', require('../forms/leaseInspection').default);
// Vue.component('lease-rent-review-component', require('../forms/leaseRentReview').default);
//
// Vue.component('lease-unit-details-component', require('../forms/leaseUnitDetails').default);
// Vue.component('lease-man-fees-component', require('../forms/leaseManagementFees').default);
// Vue.component('lease-unit-history-component', require('../forms/leaseUnitHistory').default);
// Vue.component('lease-unit-lease-charges-component', require('../forms/leaseUnitLeaseCharges').default);
//
// Vue.component('lease-bonds-component', require('../forms/leaseBonds').default);
// Vue.component('lease-outstanding-amount-component', require('../forms/leaseOutstandingAmounts').default);
//
// Vue.component('lease-contacts-component', require('../forms/leaseContacts').default);
// Vue.component('lease-documents-component', require('../forms/leaseDocuments').default);
// Vue.component('lease-email-component', require('../forms/leaseEmail').default);
// Vue.component('lease-sms-component', require('../forms/LeaseSMS').default);

// Vue.use(SuiVue);
export default {
    props: {
        initialLedgerCode: String,
    },
    data() {
        return {
            manage_ledger_title: 'Manage Ledger',
            suburb_label: 'Suburb',
            loading_page_setting: false,
            loading_setting: false,
            force_load_new_property: 0,
            form_mode: 0,
            new_property_code_ok: 0,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            reloadComponents: 0,

            property_list: [],
            property_code: '', //O
            ////property_code: {field_key: '', field_value: 'Please a select property'}, //U
            //property_code: 'TLEDGER1',
            dd_sl_agent_list: [],
            dd_agent_list: [],
            agent_code: {},
            dd_owner_list: [],
            owner_code: {},
            dd_ledger_category_list: [],
            ledger_category_code: {},
            dd_bank_account_list: [],
            bank_account_code: {},
            dd_office_list: [],
            property_remittance_office_code: {},

            state_list: [
                { value: 'WA', label: 'Western Australia' },
                { value: 'VIC', label: 'Victoria' },
                { value: 'NSW', label: 'New South Wales' },
                { value: 'QLD', label: 'Queensland' },
                { value: 'SA', label: 'South Australia' },
                { value: 'NT', label: 'Northern Territory' },
                { value: 'ACT', label: 'Australian Capital Territory' },
                { value: 'TAS', label: 'Tasmania' },
                { value: 'MALAYSIA', label: 'Malaysia' },
                { value: 'NZ', label: 'New Zealand' },
                { value: 'SNG', label: 'Singapore' },
                { value: 'ENG', label: 'England' },
                { value: 'SCO', label: 'Scotland' },
                { value: 'WAL', label: 'Wales' },
            ],
            state_code: {},
            new_property_code: '',
            property_name: '',
            property_address: '',
            property_suburb: '',
            property_suburb_auto: '',
            property_post_code: '',
            property_state: '',
            sl_agent: '',
            property_agent: '',
            property_owner: '',
            property_type: '',
            sales_reference: '',
            property_remittance_office: '',
            property_bank_account: '',
            property_bank_account_name: '',
            save_button_label: 'Update Details',
            company_type: 1,
            ledger_status: 0,
            property_total_balance: 0,
            property_owner_code: '',
            property_owner_name: '',
            property_owner_address1: '',
            property_owner_address2: '',
            property_owner_suburb: '',
            property_owner_state: '',
            property_owner_post: '',
            property_owner_country: '',
            property_owner_email: '',
            property_owner_state_list: '',
            property_owner_country_list: '',
            agent_country_list: [],
            agent_state_list: [],
            agent_suburb_list: [],
            property_owner_name_old: '',
            property_owner_address1_old: '',
            property_owner_address2_old: '',
            property_owner_suburb_old: '',
            property_owner_state_old: '',
            property_owner_post_old: '',
            property_owner_country_old: '',
            property_owner_email_old: '',

            suburb_list_filtered: [],
            suburbOwner_list_filtered: [],
            suburbValue: [],
            suburbOwnerValue: [],
            stateOwner_list_filtered: [],
            searchSuburb: '',
            searchOwnerSuburb: '',
            dd_country: '',
            dd_state: '',

            isSelecting: false,
            selectedFile: '',
            property_image: '',
            property_image_file: '',
            default_property_image: this.$assetDomain + 'assets/images/icons/default-property-image.png',
            old_property_image: '',
            isRemove: false,
            selectedPropertyCode: [],
            post_code_length: 4,

            show_change_log_modal: false,
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.fetchPropertyList();
        this.fetchPortfolioManagerList();
        this.fetchAgentList();
        this.fetchOwnerList();
        this.fetchBankList();
        this.fetchOfficeList();
        this.fetchLedgerCategoryList();
        this.fetchSuburb();
        this.fetchCountryList();
        this.fetchPropertyDefault();
        this.loadCountryDefaults();
        this.property_code = this.initialLedgerCode;
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'dd_property_list',
            'dd_default_value',
            'dd_country_list',
        ]),
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.suburb_label = this.ucwords(this.country_defaults.suburb);
            });
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },

        ...mapActions(['fetchPropertyList', 'fetchCountryList']),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_PUSH_DD_PROPERTY_LIST', 'SET_DD_COUNTRY_LIST']),

        nameWithDash({ value, label }) {
            if (`${value}` === '') return `${label}`;
            return `${value} — ${label}`;
        },

        newProperty() {
            this.resetCompanyDetail();
            this.form_mode = 1;
            this.save_button_label = 'Save Details';
            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_property_code = '';
            this.property_code = '';
            this.property_existed = true;
            this.new_property_code_ok = 0;
            this.company_type = 0;
            this.property_image = this.default_property_image;
        },
        cancelNewProperty() {
            this.form_mode = 0;
            this.save_button_label = 'Update Details';
            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_property_code = '';
            this.property_code = '';
            this.property_existed = true;
            this.new_property_code_ok = 0;
            this.company_type = 1;
        },

        fetchLedgerCategoryList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/ui/fetch/param-ledger-category-list';

            /*axios.post(api_url, form_data)
                .then(response => {
                    this.dd_ledger_category_list = response.data
                });*/

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_ledger_category_list = response.data;
            });
        },

        fetchPortfolioManagerList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/ui/fetch/param-agent-list';

            /*axios.post(api_url, form_data)
                .then(response => {
                    this.dd_sl_agent_list = response.data;
                });*/

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_sl_agent_list = response.data;
            });
        },

        fetchAgentList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/agent-dropdown-list';

            /*axios.post(api_url, form_data)
                .then(response => {
                    this.dd_agent_list = response.data
                });*/

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_agent_list = response.data;
            });
        },

        fetchOwnerList() {
            this.$api.post('owner-dropdown-list', {}).then((response) => {
                this.dd_owner_list = response.data;
            });
        },

        fetchBankList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/bank-account-dropdown-list';

            /*axios.post(api_url, form_data)
                .then(response => {
                    this.dd_bank_account_list = response.data
                });*/

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_bank_account_list = response.data;
            });
        },

        fetchOfficeList() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/office-dropdown-list';

            /*axios.post(api_url, form_data)
                .then(response => {
                    this.dd_office_list = response.data
                });*/

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_office_list = response.data;
            });
        },

        forceRerender() {
            this.reloadComponents += 1;
            this.loadLedgerDetails();
        },

        fetchPropertyDefault() {
            var form_data = new FormData();
            form_data.append('user_type', this.user_type);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/sales-trust/ledger/get-property-default';

            /*axios.post(api_url, form_data)
                .then(response => {
                this.dd_country = response.data.dd_country;
                this.dd_state = response.data.dd_state;
            });*/

            this.$api.post(api_url, form_data).then((response) => {
                this.dd_country = response.data.dd_country;
                this.dd_state = response.data.dd_state;
            });
        },

        checkPropertyCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];

            if (this.new_property_code.length > 10) {
                this.error_server_msg2.push(['Ledger code allows 10 characters only']);
            } else if (this.new_property_code === '') {
                this.error_server_msg2.push(['Please input a valid ledger code']);
            }

            if (this.verifyCode(this.new_property_code)) {
                this.error_server_msg2.push(['Please use only alphanumeric characters for the ledger code.']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;

                var form_data = new FormData();
                form_data.append('user_type', this.user_type);
                form_data.append('property_code', this.new_property_code);
                form_data.append('no_load', true);

                let api_url = this.cirrus8_api_url + 'api/check-for-duplicate-property';

                /*axios.post(api_url, form_data)
                .then(response => {
                    this.error_server_msg2 = response.data.validation_errors;
                    this.loading_page_setting = false;

                    if (!response.data.property_exists) {
                        this.new_property_code_ok = true;

                        //reset field values
                        this.property_name = "";
                        this.property_address = "";
                        this.property_suburb = "";
                        this.property_post_code = "";
                        this.property_state = this.dd_state;
                        this.sl_agent = "";
                        this.property_agent = "";
                        this.property_owner = "";
                        this.property_type = "";
                        this.property_remittance_office = "";
                        this.property_bank_account = "";
                        this.property_bank_account_name = "";
                        this.suburbValue = "";
                        this.suburb_list_filtered = [];
                        this.agent_country_list = this.dd_country_list;
                        this.suburbFilteredList(this.dd_state)
                        this.property_owner_state_list = this.dd_state;
                        this.property_owner_country_list = this.dd_country;
                        this.suburbOwnerFilteredList(this.dd_state);
                        this.stateOwnerFilteredList(this.dd_country);
                    }
                });*/

                this.$api.post(api_url, form_data).then((response) => {
                    this.error_server_msg2 = response.data.validation_errors;
                    this.loading_page_setting = false;

                    if (!response.data.property_exists) {
                        this.new_property_code_ok = true;

                        //reset field values
                        this.property_name = '';
                        this.property_address = '';
                        this.property_suburb = '';
                        this.property_post_code = '';
                        this.property_state = this.dd_state;
                        this.sl_agent = '';
                        this.property_agent = '';
                        this.property_owner = '';
                        this.property_type = '';
                        this.sales_reference = '';
                        this.property_remittance_office = '';
                        this.property_bank_account = '';
                        this.property_bank_account_name = '';
                        this.suburbValue = '';
                        this.suburb_list_filtered = [];
                        this.agent_country_list = this.dd_country_list;
                        this.suburbFilteredList(this.dd_state);
                        this.property_owner_state_list = this.dd_state;
                        this.property_owner_country_list = this.dd_country;
                        this.suburbOwnerFilteredList(this.dd_state);
                        this.stateOwnerFilteredList(this.dd_country);
                    }
                });
            }
        },

        saveForm: function () {
            // console.log(this.lease_details);
            // this.edit_form = false;
            this.error_msg = [];
            this.error_server_msg2 = [];
            // if(!this.lease_tenant_country){this.lease_tenant_country_list=this.dropdown_default;}

            if (this.property_name == '') {
                this.error_msg.push({ id: 'property_name', message: 'You have not specified a ledger name.' });
                this.$noty.error('You have not specified a ledger name.');
            }

            if (
                !$.isNumeric(this.property_post_code) &&
                this.property_post_code != '' &&
                this.property_post_code != null
            ) {
                this.error_msg.push({
                    id: 'property_post_code',
                    message: 'Please use only numeric characters for the ledger code.',
                });
                this.$noty.error('Please use only numeric characters for the ledger code.');
            }

            if (
                this.property_post_code.length < 4 &&
                this.property_post_code != '' &&
                this.property_post_code != null
            ) {
                this.error_msg.push({
                    id: 'property_post_code',
                    message: 'Please check the entered owner post code, must be 4 characters.',
                });
                this.$noty.error('Please check the entered owner post code, must be 4 characters.');
            }

            if (this.property_type == '' || this.property_type == null) {
                this.error_msg.push({ id: 'property_type', message: 'You have not specified a category.' });
                this.$noty.error('You have not specified a category.');
            }

            if (this.sl_agent == '' || this.sl_agent == null) {
                this.error_msg.push({ id: 'sl_agent', message: 'You have not specified a sales or leasing agent.' });
                this.$noty.error('You have not specified a sales or leasing agent.');
            }

            if (this.property_agent == '' || this.property_agent == null) {
                this.error_msg.push({ id: 'property_agent', message: 'You have not specified an agent company.' });
                this.$noty.error('You have not specified an agent company.');
            }

            if (this.property_remittance_office == '' || this.property_remittance_office == null) {
                this.error_msg.push({
                    id: 'property_remittance_office',
                    message: 'You have not specified a remittance office.',
                });
                this.$noty.error('You have not specified a remittance office.');
            }

            if (this.company_type == 1) {
                if (this.property_owner == '' || this.property_owner == null) {
                    this.error_msg.push({ id: 'property_owner', message: 'You have not specified a principal owner.' });
                    this.$noty.error('You have not specified a principal owner.');
                }
            } else {
                if (this.property_owner_code == '') {
                    this.error_msg.push({
                        id: 'property_owner_code',
                        message: 'You have not specified an owner code.',
                    });
                    this.$noty.error('You have not specified an owner code.');
                }

                if (this.verifyCode(this.property_owner_code)) {
                    this.error_msg.push({
                        id: 'property_owner_code',
                        message: 'Please use only alphanumeric characters for the owner code.',
                    });
                    this.$noty.error('Please use only alphanumeric characters for the owner code.');
                }

                if (this.property_owner_name == '') {
                    this.error_msg.push({
                        id: 'property_owner_name',
                        message: 'You have not specified an owner name.',
                    });
                    this.$noty.error('You have not specified an owner name.');
                }

                if (this.property_owner_address1 == '') {
                    this.error_msg.push({
                        id: 'property_owner_address1',
                        message: 'You have not specified an owner address.',
                    });
                    this.$noty.error('You have not specified an owner address.');
                }

                if (this.property_owner_suburb == '') {
                    this.error_msg.push({
                        id: 'property_owner_suburb',
                        message: 'You have not specified an owner suburb.',
                    });
                    this.$noty.error('You have not specified an owner suburb.');
                }

                if (this.property_owner_post == '') {
                    this.error_msg.push({
                        id: 'property_owner_post',
                        message: 'You have not specified an owner post code.',
                    });
                    this.$noty.error('You have not specified an owner post code.');
                }

                if (!$.isNumeric(this.property_owner_post) && this.property_owner_post != '') {
                    this.error_msg.push({
                        id: 'property_owner_post',
                        message: 'Please use only numeric characters for the owner post code.',
                    });
                    this.$noty.error('Please use only numeric characters for the owner post code.');
                }

                if (this.property_owner_post.length < this.post_code_length && this.property_owner_post != '') {
                    this.error_msg.push({
                        id: 'property_owner_post',
                        message:
                            'Please check the entered owner post code, must be ' +
                            this.post_code_length +
                            ' characters.',
                    });
                    this.$noty.error(
                        'Please check the entered owner post code, must be ' + this.post_code_length + ' characters.',
                    );
                }

                if (this.property_owner_state_list == '') {
                    this.error_msg.push({
                        id: 'property_owner_state',
                        message: 'You have not specified an owner state.',
                    });
                    this.$noty.error('You have not specified an owner state.');
                }

                if (this.property_owner_country_list == '') {
                    this.error_msg.push({
                        id: 'property_owner_country_list',
                        message: 'You have not specified an owner country.',
                    });
                    this.$noty.error('You have not specified an owner country.');
                }
            }

            if (this.property_bank_account == '') {
                this.error_msg.push({ id: 'property_bank_account', message: 'You have not specified a bank account.' });
                this.$noty.error('You have not specified a bank account.');
            }

            if (this.ledger_status == 1 && this.property_total_balance != 0) {
                this.error_msg.push({
                    id: 'ledger_status',
                    message:
                        'You are not allowed to make this ledger inactive because it still has a remaining balance',
                });
                this.$noty.error(
                    'You are not allowed to make this ledger inactive because it still has a remaining balance',
                );
            }
            // submit form if no error
            if (this.error_msg.length <= 0) {
                this.loading_setting = true;
                var formData = new FormData();

                formData.append('user_type', this.user_type);

                formData.append('form_mode', this.form_mode);
                formData.append('new_property_code', this.new_property_code);
                formData.append('property_code', this.property_code);
                formData.append('property_name', this.property_name);
                formData.append('property_address', this.property_address);
                formData.append('property_suburb', this.property_suburb);
                formData.append('property_post_code', this.property_post_code);
                formData.append('property_state', this.property_state);

                formData.append('sl_agent', this.sl_agent);
                formData.append('property_agent', this.property_agent);
                formData.append('property_owner', this.escapeParamValue(this.property_owner));
                if (this.company_type == 0) {
                    formData.append('property_owner', this.escapeParamValue(this.property_owner_code));
                }
                formData.append('property_type', this.property_type);
                formData.append('sales_reference', this.sales_reference);
                formData.append('property_remittance_office', this.property_remittance_office);

                formData.append('property_bank_account', this.property_bank_account);

                formData.append('property_owner_name', this.property_owner_name);
                formData.append('property_owner_code', this.property_owner_code);
                formData.append('property_owner_address1', this.property_owner_address1);
                formData.append('property_owner_address2', this.property_owner_address2);
                formData.append('property_owner_state', this.property_owner_state);
                formData.append('property_owner_country', this.property_owner_country);
                formData.append('property_owner_post', this.property_owner_post);
                formData.append('property_owner_email', this.property_owner_email);
                formData.append('property_owner_country_list', this.property_owner_country_list);
                formData.append('property_owner_state_list', this.property_owner_state_list);
                formData.append('property_owner_suburb', this.property_owner_suburb);
                formData.append('company_type', this.company_type);
                formData.append('ledger_status', this.ledger_status);
                formData.append('property_image_file', this.property_image_file);

                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/with-file-upload/sales-trust/ledger/update-or-create-ledger';

                /*axios.post(apiUrl, formData)
                .then(response => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        if (this.form_mode == 1) {
                            this.fetchPropertyList();
                            this.property_owner = this.property_owner_code;
                            this.dd_owner_list.push({
                                value: this.property_owner_code,
                                label: this.property_owner_code + ' - ' + this.property_owner_name
                            });
                            this.fetchOwnerList();
                            this.form_mode = 0; //change to edit and load defaults

                            this.SET_PUSH_DD_PROPERTY_LIST(
                                {
                                    fieldGroup: "Active",
                                    fieldKey: this.new_property_code,
                                    fieldValue: this.property_name,
                                    field_group: "Active",
                                    field_key: this.new_property_code,
                                    field_value: this.property_name,
                                    value: this.new_property_code,
                                    label: this.new_property_code + ' - ' + this.property_name
                                }
                            );
                            this.property_code = this.new_property_code;
                            this.selectedPropertyCode =
                                {
                                    fieldGroup: "Active",
                                    fieldKey: this.new_property_code,
                                    fieldValue: this.property_name,
                                    field_group: "Active",
                                    field_key: this.new_property_code,
                                    field_value: this.property_name,
                                    value: this.new_property_code,
                                    label: this.new_property_code + ' - ' + this.property_name
                                }
                            ;
                            this.new_property_code_ok = 0;
                            this.$noty.success('Ledger Added.');
                        } else
                            this.$noty.success('Ledger Updated.');
                        //this.property_bank_account_name = this.property_bank_account.label
                        //if(this.property_suburb){
                        //	this.suburbValue = {"suburb" : this.property_suburb, "pcode" : this.property_post_code };
                        //}

                        //this.suburbOwnerValue = {"suburb" : this.property_owner_suburb, "pcode" : this.property_owner_post };
                        this.company_type = 1;
                        this.isRemove = false;
                        // this.loadLedgerDetails();
                        this.forceRerender();
                        this.loading_setting = false;
                    } else {
                        this.error_server_msg2 = response.data.validation_errors;
                        this.loading_setting = false;
                    }
                });*/

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        if (this.form_mode == 1) {
                            this.fetchPropertyList();
                            this.property_owner = this.property_owner_code;
                            this.dd_owner_list.push({
                                value: this.property_owner_code,
                                label: this.property_owner_code + ' - ' + this.property_owner_name,
                            });
                            this.fetchOwnerList();
                            this.form_mode = 0; //change to edit and load defaults

                            this.SET_PUSH_DD_PROPERTY_LIST({
                                fieldGroup: 'Active',
                                fieldKey: this.new_property_code,
                                fieldValue: this.property_name,
                                field_group: 'Active',
                                field_key: this.new_property_code,
                                field_value: this.property_name,
                                value: this.new_property_code,
                                label: this.new_property_code + ' - ' + this.property_name,
                            });
                            this.property_code = this.new_property_code;
                            this.selectedPropertyCode = {
                                fieldGroup: 'Active',
                                fieldKey: this.new_property_code,
                                fieldValue: this.property_name,
                                field_group: 'Active',
                                field_key: this.new_property_code,
                                field_value: this.property_name,
                                value: this.new_property_code,
                                label: this.new_property_code + ' - ' + this.property_name,
                            };
                            this.new_property_code_ok = 0;
                            this.$noty.success('Ledger Added.');
                        } else this.$noty.success('Ledger Updated.');
                        //this.property_bank_account_name = this.property_bank_account.label
                        //if(this.property_suburb){
                        //	this.suburbValue = {"suburb" : this.property_suburb, "pcode" : this.property_post_code };
                        //}

                        //this.suburbOwnerValue = {"suburb" : this.property_owner_suburb, "pcode" : this.property_owner_post };
                        this.company_type = 1;
                        this.isRemove = false;
                        // this.loadLedgerDetails();
                        this.forceRerender();
                        this.loading_setting = false;
                    } else {
                        this.error_server_msg2 = response.data.validation_errors;
                        this.loading_setting = false;
                    }
                });
            }
        },
        deleteLedger: function () {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            //formData.append('form_mode', this.form_mode);
            formData.append('property_code', this.property_code);
            formData.append('no_load', true);

            //Check for existing transactions first
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/check-ledger-transactions';

            /*axios.post(apiUrl, formData)
                .then(response => {
                    let ledger_data = response.data;
                    //console.log(ledger_data.has_transactions+"====="+ledger_data.transaction_counter);
                    if(ledger_data.has_transactions > 0){
                        this.$noty.error('Deleting not allowed. Transactions were found under this ledger.');
                    }else{
                        //Allow delete
                        let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/delete-ledger';
                        // let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/make-inactive-ledger';

                        this.$api.post(apiUrl2, formData).then(response => {
                            this.$refs.propertyCodeDropDown.select_val = '';
                            this.property_code = '';
                            this.fetchPropertyList();
                            let return_data = response.data;
                            if(return_data.status == 'success'){
                                this.$noty.success('Ledger Deleted.');
                                this.loading_setting = false;
                            }else{
                                this.$noty.error('Error encountered while trying to delete ledger.');
                                this.loading_setting = false;
                            }
                        });
                    }
                });*/

            this.$api.post(apiUrl, formData).then((response) => {
                let ledger_data = response.data;
                //console.log(ledger_data.has_transactions+"====="+ledger_data.transaction_counter);
                if (ledger_data.has_transactions > 0) {
                    this.$noty.error('Deleting not allowed. Transactions were found under this ledger.');
                } else {
                    //Allow delete
                    let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/delete-ledger';
                    // let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/make-inactive-ledger';

                    this.$api.post(apiUrl2, formData).then((response) => {
                        this.$refs.propertyCodeDropDown.select_val = '';
                        this.property_code = '';
                        this.fetchPropertyList();
                        let return_data = response.data;
                        if (return_data.status == 'success') {
                            this.$noty.success('Ledger Deleted.');
                            this.loading_setting = false;
                        } else {
                            this.$noty.error('Error encountered while trying to delete ledger.');
                            this.loading_setting = false;
                        }
                    });
                }
            });
        },
        loadLedgerDetails: function () {
            //console.log('load ledger details:'+this.initialLedgerCode);
            //O
            if (this.property_code == '') return true;
            //U
            // if(this.initialLedgerCode != ''){
            //     //this.property_code = this.initialLedgerCode; //O
            //     //this.property_code = this.getValueInList(this.initialLedgerCode, this.dd_property_list); //o-u
            //     this.property_code = this.getValueInList(this.initialPropertyCode, this.dd_property_list); //u
            // }else if(this.property_code == ''){
            //     return true;
            // }
            this.loading_setting = true;
            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('property_code', this.property_code);
            formData.append('no_load', true);

            // get ledger details by code
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-ledger';

            /*axios.post(apiUrl, formData)
                .then(response => {
                    let ledger_data = response.data;

                    //load to form fields
                    this.property_name = ledger_data.property_name;
                    this.property_address = (ledger_data.property_street == null ? '' : ledger_data.property_street);
                    this.property_suburb = (ledger_data.property_city == null || ledger_data.property_city == 'null' ? '' : ledger_data.property_city);
                    this.property_post_code = (ledger_data.property_postcode == null ? '' : ledger_data.property_postcode);
                    this.property_state = ledger_data.property_state;

                    this.ledger_status = parseInt(ledger_data.property_inactive_status);
                    this.property_total_balance = parseFloat(ledger_data.property_total_balance);

                    this.suburbFilteredList(ledger_data.property_state);
                    this.suburbValue = {"suburb": ledger_data.property_city, "pcode": ledger_data.property_postcode};

                    this.sl_agent = ledger_data.sl_agent_id;
                    this.property_agent = ledger_data.property_agent;
                    this.property_owner = ledger_data.property_owner;
                    this.property_type = ledger_data.property_type;
                    this.property_remittance_office = (ledger_data.property_remittance_office == null || ledger_data.property_remittance_office == 'null' ? '' : ledger_data.property_remittance_office);
                    this.property_bank_account = ledger_data.property_bank_account;
                    this.property_bank_account_name = ledger_data.property_bank_account + ' - ' + ledger_data.property_bank_account_name;
                    if (ledger_data.property_image)
                        this.property_image = "assets/images/property-image/" + ledger_data.property_image;
                    else
                        this.property_image = this.default_property_image;
                    this.loading_setting = false;
                });*/

            this.$api.post(apiUrl, formData).then((response) => {
                let ledger_data = response.data;

                //load to form fields
                this.property_name = ledger_data.property_name;
                this.property_address = ledger_data.property_street == null ? '' : ledger_data.property_street;
                this.property_suburb =
                    ledger_data.property_city == null || ledger_data.property_city == 'null'
                        ? ''
                        : ledger_data.property_city;
                this.property_post_code = ledger_data.property_postcode == null ? '' : ledger_data.property_postcode;
                this.property_state = ledger_data.property_state;

                this.ledger_status = parseInt(ledger_data.property_inactive_status);
                this.property_total_balance = parseFloat(ledger_data.property_total_balance);

                this.suburbFilteredList(ledger_data.property_state);
                this.suburbValue = { suburb: ledger_data.property_city, pcode: ledger_data.property_postcode };

                this.sl_agent = ledger_data.sl_agent_id;
                this.property_agent = ledger_data.property_agent;
                this.property_owner = ledger_data.property_owner;
                this.property_type = ledger_data.property_type;
                this.sales_reference = ledger_data.sales_reference;
                this.property_remittance_office =
                    ledger_data.property_remittance_office == null || ledger_data.property_remittance_office == 'null'
                        ? ''
                        : ledger_data.property_remittance_office;
                this.property_bank_account = ledger_data.property_bank_account;
                this.property_bank_account_name =
                    ledger_data.property_bank_account + ' - ' + ledger_data.property_bank_account_name;
                if (ledger_data.property_image)
                    this.property_image =
                        this.$uploadedAssetDomain + 'assets/images/property-image/' + ledger_data.property_image;
                else this.property_image = this.default_property_image;
                this.loading_setting = false;
            });
        },
        loadCompanyDetails: function () {
            this.loading_setting = true;
            let lease_company_code = this.property_owner;
            if (lease_company_code !== '' && !(lease_company_code == null)) {
                var formData = new FormData();

                formData.append('user_type', this.user_type);
                formData.append('company_code', lease_company_code);
                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/company/load-company-details';

                /*axios.post(apiUrl, formData)
                .then(response => {
                    this.property_owner_name = response.data.tenant_name;
                    this.property_owner_address1 = response.data.tenant_address_1;
                    this.property_owner_address2 = response.data.tenant_address_2;
                    this.property_owner_suburb = response.data.tenant_suburb;
                    this.property_owner_email = response.data.tenant_email;
                    this.property_owner_post = response.data.tenant_post_code;
                    this.property_owner_state = response.data.tenant_state.field_value;
                    this.property_owner_country = response.data.tenant_country.field_value;

                    this.property_owner_name_old = response.data.tenant_name;
                    this.property_owner_address1_old = response.data.tenant_address_1;
                    this.property_owner_address2_old = response.data.tenant_address_2;
                    this.property_owner_suburb_old = response.data.tenant_suburb;
                    this.property_owner_email_old = response.data.tenant_email;
                    this.property_owner_post_old = response.data.tenant_post_code;
                    this.property_owner_state_old = response.data.tenant_state.field_value;
                    this.property_owner_country_old = response.data.tenant_country.field_value;

                    this.agent_state_list = response.data.tenant_state_list;
                    this.agent_country_list = response.data.tenant_country_list;
                    this.loading_setting = false;
                });*/

                this.$api.post('company/load-company-details', formData).then((response) => {
                    this.property_owner_name = response.data.tenant_name;
                    this.property_owner_address1 = response.data.tenant_address_1;
                    this.property_owner_address2 = response.data.tenant_address_2;
                    this.property_owner_suburb = response.data.tenant_suburb;
                    this.property_owner_email = response.data.tenant_email;
                    this.property_owner_post = response.data.tenant_post_code;
                    this.property_owner_state = response.data.tenant_state.field_value;
                    this.property_owner_country = response.data.tenant_country.field_value;

                    this.property_owner_name_old = response.data.tenant_name;
                    this.property_owner_address1_old = response.data.tenant_address_1;
                    this.property_owner_address2_old = response.data.tenant_address_2;
                    this.property_owner_suburb_old = response.data.tenant_suburb;
                    this.property_owner_email_old = response.data.tenant_email;
                    this.property_owner_post_old = response.data.tenant_post_code;
                    this.property_owner_state_old = response.data.tenant_state.field_value;
                    this.property_owner_country_old = response.data.tenant_country.field_value;

                    this.agent_state_list = response.data.tenant_state_list;
                    this.agent_country_list = response.data.tenant_country_list;
                    this.loading_setting = false;
                });
            } else {
                this.loading_setting = false;
            }
        },
        resetCompanyDetail: function () {
            if (this.company_type == 1) {
                if (this.property_owner) {
                    this.property_owner_name = this.property_owner_name_old;
                    this.property_owner_address1 = this.property_owner_address1_old;
                    this.property_owner_address2 = this.property_owner_address2_old;
                    this.property_owner_suburb = this.property_owner_suburb_old;
                    this.property_owner_email = this.property_owner_email_old;
                    this.property_owner_post = this.property_owner_post_old;
                    this.property_owner_state = this.property_owner_state_old;
                    this.property_owner_country = this.property_owner_country_old;
                } else {
                    this.property_owner_name = '';
                    this.property_owner_address1 = '';
                    this.property_owner_address2 = '';
                    this.property_owner_suburb = '';
                    this.property_owner_email = '';
                    this.property_owner_post = '';
                    this.property_owner_state = '';
                    this.property_owner_country = '';
                }
            } else {
                this.property_owner_code = '';
                this.property_owner_name = '';
                this.property_owner_address1 = '';
                this.property_owner_address2 = '';
                this.property_owner_suburb = '';
                this.property_owner_email = '';
                this.property_owner_post = '';
                this.property_owner_state = '';
                this.property_owner_country = '';
                this.property_owner_code = '';
                this.property_owner_state_list = this.dd_state;
                this.property_owner_country_list = this.dd_country;
                this.suburbOwnerValue = '';
                this.stateOwnerFilteredList(this.dd_country);
                this.suburbOwnerFilteredList(this.dd_state);
            }
        },

        fetchSuburb: function () {
            this.agent_suburb_list = suburbList;
        },

        suburbSelected(data) {
            if (this.$refs.refSuburb.selectedValues.length == 0) {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_post_code = data.pcode;
                } else {
                    this.property_suburb = this.property_suburb;
                }
            } else {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_post_code = data.pcode;
                }
            }
        },
        suburbOwnerSelected(data) {
            if (this.$refs.refOwnerSuburb.selectedValues.length == 0) {
                if (data) {
                    this.property_owner_suburb = data.suburb;
                    this.property_owner_post = data.pcode;
                } else {
                    this.property_owner_suburb = this.property_owner_suburb;
                }
            } else {
                if (data) {
                    this.property_owner_suburb = data.suburb;
                    this.property_owner_post = data.pcode;
                }
            }
        },
        suburbFilteredList(stateVal) {
            this.suburb_list_filtered = [];
            let filteredItem = [];

            $.each(suburbList, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });

            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }

            this.suburb_list_filtered = filteredItem;
        },
        suburbOwnerFilteredList(stateVal) {
            this.suburbOwner_list_filtered = [];
            let filteredOwnerItem = [];

            $.each(suburbList, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredOwnerItem = filteredOwnerItem.concat(value);
                }
            });

            if (filteredOwnerItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredOwnerItem.push(temp_value);
            }

            this.suburbOwner_list_filtered = filteredOwnerItem;
        },
        stateOwnerFilteredList(country_code) {
            if (country_code == 'AU') this.post_code_length = 4;
            else if (country_code == 'US') this.post_code_length = 5;
            else if (country_code == 'SG') this.post_code_length = 6;
            else this.post_code_length = 8;

            if (this.property_owner_post.length > this.post_code_length)
                this.property_owner_post = this.property_owner_post.substring(0, this.post_code_length);

            var formData = new FormData();
            /*formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);*/
            formData.append('countryCode', country_code);

            //axios.post(this.cirrus8_api_url + 'api/loadAPIStatesDropDownList', formData)
            this.$api.post('loadAPIStatesDropDownList', formData).then((response) => {
                this.stateOwner_list_filtered = response.data.stateList;
            });
        },

        verifyCode: function (inputString) {
            var newStr = inputString.replace(/[^a-zA-Z0-9-]/g, '');
            return newStr.length != inputString.length;
        },

        postcodeKeyDown: function () {
            const char = String.fromCharCode(event.keyCode);
            if (!/[0-9]/.test(char)) {
                event.preventDefault();
            }
        },

        onButtonClick() {
            this.isSelecting = true;
            window.addEventListener(
                'focus',
                () => {
                    this.isSelecting = false;
                },
                { once: true },
            );

            this.$refs.uploader.click();
        },
        onFileChanged(e) {
            var files = e.target.files || e.dataTransfer.files;

            if (files[0].type == 'image/png' || files[0].type == 'image/jpg' || files[0].type == 'image/jpeg') {
                if (!files.length) return;

                if (files[0].size <= 5242880) {
                    this.createImage(files[0]);
                } else {
                    alert('Unable to upload file because the size exceeds the maximum allowed file size of 5MB.');
                    return;
                }
            } else {
                this.$noty.error('Invalid file type. Please upload only image file.');
            }
        },
        createImage(file) {
            var image = new Image();
            var reader = new FileReader();
            this.old_property_image = this.property_image;
            this.property_image_file = file;
            reader.onload = (e) => {
                this.property_image = e.target.result;
            };
            reader.readAsDataURL(file);
            this.isRemove = true;
        },
        removeImage: function (item) {
            this.property_image = this.old_property_image;
            this.property_image_file = '';
            this.isRemove = false;
        },

        openLedgerChangelogModal: function () {
            this.show_change_log_modal = true;
        },
    },
    watch: {
        dd_property_list: function () {
            if (this.dd_property_list.length > 0 && this.$refs.propertyCodeDropDown && this.property_code != '') {
                this.$refs.propertyCodeDropDown.forceChange(this.property_code, this.dd_property_list);
            }
        },
        property_code: function () {
            if (this.property_code) {
                this.loadLedgerDetails();
            } else {
                this.property_code = '';
            }
        },

        new_property_code: function () {
            this.new_property_code = this.new_property_code.toUpperCase().trim();
            if (this.new_property_code.length > 10) this.new_property_code = this.new_property_code.substring(0, 10);
        },
        property_owner: function () {
            this.loadCompanyDetails();
        },
        company_type: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            this.resetCompanyDetail();
        },
        property_owner_code: function () {
            this.property_owner_code = this.property_owner_code.toUpperCase().trim();
            if (this.property_owner_code.length > 10)
                this.property_owner_code = this.property_owner_code.substring(0, 10);
        },
        property_suburb: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchSuburb;
                this.property_suburb = newVal;
            }
        },
        property_owner_suburb: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchOwnerSuburb;
                this.property_owner_suburb = newVal;
            }
        },
    },
    mixins: [global_mixins],
};
</script>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
