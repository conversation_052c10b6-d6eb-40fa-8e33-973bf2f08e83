<style>
.attachment-links a:hover {
    text-decoration: none !important;
}
.attachment-links a:hover span {
    text-decoration: underline !important;
}
.attachment-links span {
    vertical-align: middle;
}
span.spacer {
    font-size: 11px;
    padding: 0 5px 0 10px;
}
.c8-page-table-row-header td {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.6);
}
.cursor-pointer {
    cursor: pointer;
}
.checkbox-container .checkbox {
    padding-top: 7px;
}
.c8-datatable-custom {
    padding-bottom: 0 !important;
}
.c8-datatable-custom thead.v-data-table-header {
    background-color: #eaeaea;
    color: #888888 !important;
    border-color: #ececec;
}
.c8-datatable-custom th.text-start span {
    font-size: 11px;
    font-weight: 700 !important;
    color: rgba(0, 0, 0, 0.6) !important;
}
.c8-page input.inline-edit-input {
    border: 1px solid #ececec !important;
    padding: 3px 10px;
    width: 300px;
}
.c8-page input.inline-edit-input:focus,
.c8-page input.inline-edit-input:focus-visible {
    outline: none !important;
}
.c8-page input.inline-edit-input.inline-input-150 {
    width: 150px;
}
.error-message-container .error-message-div {
    margin: 0;
}
</style>
<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div id="subledgerDocumentForm">
            <v-card
                id="documents_header"
                class="section-toolbar"
                color="titleHeader"
                text
                tile
                dark
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Documents</h6>
                    &nbsp;&nbsp;
                    <v-spacer></v-spacer>
                    <div style="display: inline-block; padding-right: 1em">
                        <span class="toolbar-search-holder">
                            <v-text-field
                                dense
                                v-model="search"
                                :class="'cirrus-input-form-textbox'"
                                :placeholder="'Search'"
                                prepend-inner-icon="search"
                                :maxlength="'60'"
                            ></v-text-field>
                        </span>
                    </div>
                    <v-btn
                        x-small
                        class="v-step-edit-button"
                        icon
                        @click="showDocumentModal('create')"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="!edit_form"
                        class="v-step-edit-button"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="edit_form && documents.length > 0"
                        class="v-step-save-1-button"
                        icon
                        @click="closeEditing()"
                    >
                        <v-icon
                            light
                            color="red"
                            >undo</v-icon
                        >
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="edit_form && documents.length > 0"
                        icon
                        @click="saveForm()"
                    >
                        <v-icon
                            light
                            color="green"
                            >check</v-icon
                        >
                    </v-btn>
                    <v-btn
                        x-small
                        class="v-step-refresh-button"
                        icon
                        @click="reloadDocuments()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        icon
                        @click="showLogModal()"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div class="error-message-container">
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg3"
                    ></cirrus-server-error>
                </div>
                <div class="page-list">
                    <div class="c8-page-table">
                        <v-data-table
                            class="c8-datatable-custom documents-table"
                            v-show="documents.length > 0"
                            dense
                            item-key="id"
                            :headers="headers"
                            :items="documents"
                            hide-default-footer
                            disable-pagination
                            :search="search"
                        >
                            <template v-slot:[`item.blank`]="{ item }"> &nbsp;&nbsp; </template>
                            <template v-slot:[`item.index`]="{ item }">
                                {{ item.index + 1 }}
                            </template>
                            <template v-slot:[`item.id`]="{ item }">
                                <span>{{ formatID(item.id) }}</span>
                            </template>
                            <template v-slot:[`item.title`]="{ item }">
                                <span v-if="!edit_form">{{ item.title }}</span>
                                <div
                                    v-else
                                    class="inline-edit-container"
                                >
                                    <input
                                        type="text"
                                        max-length="40"
                                        v-model="item.title"
                                        class="inline-edit-input inline-input-150"
                                    />
                                </div>
                            </template>
                            <template v-slot:[`item.description`]="{ item }">
                                <span v-if="!edit_form">{{ item.description }}</span>
                                <div
                                    v-else
                                    class="inline-edit-container"
                                >
                                    <input
                                        type="text"
                                        max-length="60"
                                        v-model="item.description"
                                        class="inline-edit-input"
                                    />
                                </div>
                            </template>
                            <template v-slot:[`item.attachment`]="{ item }">
                                <span
                                    v-if="item.link"
                                    class="attachment-links"
                                >
                                    <a
                                        target="_blank"
                                        :href="item.link"
                                    >
                                        <img
                                            :src="asset_domain + 'assets/images/icons/link_icon_blue.png'"
                                            alt="Link"
                                            class="icon"
                                            style="width: 16px"
                                        />
                                        <span>Download</span>
                                    </a>
                                </span>
                                <span
                                    v-else
                                    class="attachment-links"
                                >
                                    <a
                                        target="_blank"
                                        :href="formatLink(item.file_encode)"
                                    >
                                        <img
                                            :src="asset_domain + 'assets/images/icons/pdf.png'"
                                            alt="pdf"
                                            class="icon"
                                            style="width: 16px"
                                        />
                                        <span>Download</span>
                                    </a>
                                </span>
                            </template>
                            <template v-slot:[`item.action`]="{ item }">
                                <div v-if="edit_form">
                                    <v-icon
                                        color="green"
                                        class="rotate90 option"
                                        @click="setFirstOrLast('first', documents.indexOf(item))"
                                        >fast_rewind</v-icon
                                    >
                                    <v-icon
                                        color="green"
                                        class="rotate90 option"
                                        @click="setOrder('up', documents.indexOf(item))"
                                        >arrow_left</v-icon
                                    >
                                    <v-icon
                                        color="green"
                                        class="rotate90 option"
                                        @click="setOrder('down', documents.indexOf(item))"
                                        >arrow_right</v-icon
                                    >
                                    <v-icon
                                        color="green"
                                        class="rotate90 option"
                                        @click="setFirstOrLast('last', documents.indexOf(item))"
                                        >fast_forward</v-icon
                                    >

                                    <!-- <v-icon x-small class="line-edit-button" @click="updateDocument(item.index)">fas fa-edit</v-icon> -->
                                    <v-btn
                                        x-small
                                        color="red"
                                        icon
                                        @click="deleteDocument(item.id)"
                                    >
                                        <v-icon>close</v-icon>
                                    </v-btn>
                                </div>
                            </template>
                        </v-data-table>
                        <v-col
                            class="text-center"
                            v-if="documents.length === 0"
                        >
                            <v-btn
                                depressed
                                small
                                color="success"
                                @click="showDocumentModal('create')"
                                class="sbldgr-add-btn"
                                >Add Document</v-btn
                            >
                        </v-col>
                    </div>
                </div>
            </div>
            <v-card
                elevation="0"
                v-if="edit_form && documents.length > 0"
            >
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="saveForm()"
                        color="success"
                        dark
                        small
                        depressed
                    >
                        Save Documents details
                    </v-btn>
                </v-card-actions>
            </v-card>
        </div>

        <!-- DOCUMENT MODAL -->
        <v-dialog
            persistent
            top
            v-model="show_document_modal"
            max-width="900"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card id="subledgerDocumentModal">
                <v-card-title class="headline">
                    <span v-if="form_mode === 'create'">Add New Document</span>
                    <span v-else>Update Document</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="hideDocumentModal()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Document Title</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <div style="display: inline-block">
                                        <span>
                                            <v-text-field
                                                dense
                                                v-model="document.title"
                                                :maxlength="40"
                                                :class="'cirrus-input-form-textbox'"
                                            ></v-text-field>
                                        </span>
                                    </div>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Description</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'60'"
                                        v-model="document.description"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >File</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <table id="subledgerDocumentModalFile">
                                        <tr v-if="show_methods">
                                            <td
                                                style="margin: 0; padding: 0"
                                                v-if="!has_attachment"
                                            >
                                                <cirrus-single-upload-button2
                                                    :withLinkUploader="true"
                                                    :id="getUploadButtonID(new Date().getTime() + Math.random())"
                                                    v-model="document.filename"
                                                    :has_saved_file="false"
                                                    :edit_form="true"
                                                    :error_msg="error_msg"
                                                    accept_type="pdf"
                                                    :size_limit="20"
                                                ></cirrus-single-upload-button2>
                                            </td>
                                            <td
                                                style="margin: 0; padding: 0"
                                                v-if="has_attachment"
                                            >
                                                <span class="attachment-links">
                                                    <a
                                                        target="_blank"
                                                        :href="formatLink(document.file_encode)"
                                                    >
                                                        <img
                                                            :src="asset_domain + 'assets/images/icons/pdf.png'"
                                                            alt="pdf"
                                                            class="icon"
                                                            style="width: 16px; vertical-align: middle"
                                                        />
                                                        <span style="font-size: 12px">Download</span>
                                                    </a>
                                                </span>
                                                <label
                                                    class="v-btn v-btn--flat v-btn--icon v-btn--small theme--light"
                                                    style="margin-left: 5px; cursor: pointer"
                                                    @click="removeAttachedFile()"
                                                >
                                                    <v-icon color="red">close</v-icon>
                                                </label>
                                            </td>
                                            <td
                                                style="margin: 0; padding: 0"
                                                v-if="!document.filename"
                                            >
                                                <span class="spacer">or</span>
                                            </td>
                                            <td
                                                style="margin: 0; padding: 0"
                                                v-if="!document.filename"
                                            >
                                                <img
                                                    @click="selectLink()"
                                                    :src="asset_domain + 'assets/images/icons/link_icon_blue.png'"
                                                    alt="Link"
                                                    class="icon cursor-pointer"
                                                    style="width: 24px"
                                                />
                                            </td>
                                        </tr>
                                        <tr v-if="link_selected">
                                            <td
                                                style="margin: 0; padding: 0"
                                                colspan="3"
                                            >
                                                <cirrus-input
                                                    :size="'100'"
                                                    v-model="document.link"
                                                    :edit_form="true"
                                                    :error_msg="error_msg"
                                                ></cirrus-input>
                                                <v-btn
                                                    depressed
                                                    small
                                                    @click="reselectMethod()"
                                                    >Cancel</v-btn
                                                >
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        :loading="submit_btn_loading"
                        @click="submitDocumentData()"
                        color="success"
                        dark
                        depressed
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Submit
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- ACTIVITY LOG -->
        <v-dialog
            top
            v-model="show_change_log_modal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Activity Logs</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="show_change_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <activity-logs-component
                            v-if="show_change_log_modal"
                            :property_code="selected_property_code"
                            :lease_code="selected_lease_code"
                            :form_section="'document'"
                        ></activity-logs-component>
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF ACTIVITY LOG -->

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            persistent
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon> WARNING
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click.prevent="deleteDocumentCancel()"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">Do you really want to delete this document?</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        :loading="delete_btn_loading"
                        @click="deleteDocumentConfirm()"
                        >Yes</v-btn
                    >
                    <v-btn
                        depressed
                        tile
                        small
                        @click="deleteDocumentCancel()"
                        >No</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->
    </v-container>
</template>

<script>
import Vue from 'vue';
import vSelect from 'vue-select';
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import moment from 'moment';

const Swal = require('sweetalert2');
Vue.component('v-select', vSelect);

export default {
    props: {
        selected_property_code: { type: String, default: '' },
        selected_lease_code: { type: String, default: '' },
        reloadComponents: { type: Number, default: 0 },
    },
    data() {
        return {
            loading_setting: false,
            asset_domain: this.$assetDomain,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            error_server_msg3: [],

            show_document_modal: false,

            documents: [],
            document: {
                index: null,
                id: null,
                title: null,
                description: null,
                filename: null,
                file_encode: null,
                link: null,
                is_external: null,
                sequence: null,
            },

            form_mode: 'create',
            delete_id: null,

            dialogConfirmation: '',
            edit_form: false,

            show_methods: true,
            link_selected: false,
            has_attachment: false,

            search: '',
            show_change_log_modal: false,

            headers: [
                { text: '', value: 'blank', sortable: false, width: '5px' },
                { text: '#', value: 'index', sortable: false },
                { text: 'ID', value: 'id', sortable: false },
                { text: 'Document Title', value: 'title', sortable: false },
                { text: 'Description', value: 'description', sortable: false },
                { text: 'File', value: 'attachment', sortable: false },
                { text: '', value: 'action', align: 'end', sortable: false, width: '170px' },
            ],

            submit_btn_loading: false,
            delete_btn_loading: false,
        };
    },
    mounted() {
        this.loadDocumentsList();
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url']),
    },
    methods: {
        ...mapActions([]),
        ...mapMutations([]),
        // ...

        doubleClickForm() {
            if (!this.edit_form) {
                this.edit_form = true;
            }
        },

        loadDocumentsList() {
            this.loading_setting = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);
            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', this.selected_lease_code);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/get-documents';

            this.$api.post(apiUrl, formData).then((response) => {
                this.documents = response.data.documents;
                this.loading_setting = false;
            });
        },
        formatID(id) {
            return String(id).padStart(6, '0');
        },
        formatLink(link) {
            return 'download.php?fileID=' + link;
        },
        closeEditing: function () {
            this.reloadDocuments();
            this.edit_form = false;

            this.error_server_msg3 = [];
        },
        showDocumentModal(mode) {
            this.form_mode = mode;
            this.show_document_modal = true;
        },
        hideDocumentModal() {
            this.show_document_modal = false;
            this.clearDocumentData();
        },
        submitDocumentData() {
            let validate = this.validateDocumentForm();

            if (validate) {
                this.submit_btn_loading = true;

                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('property_code', this.selected_property_code);
                formData.append('lease_code', this.selected_lease_code);
                formData.append('document', this.formatFormData(this.document));

                if (this.document.filename !== this.document.file_encode) {
                    formData.append('file', this.document.filename[0]);
                }

                formData.append('no_load', true);

                let apiUrl =
                    this.cirrus8_api_url + 'api/with-file-upload/sales-trust/subledger/' + this.form_mode + '-document';

                this.$api
                    .post(apiUrl, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        let status = response.data.status;

                        if (status && status == 'success') {
                            setTimeout(this.reloadDocuments(), 5000);
                            this.hideDocumentModal();
                            this.$noty.success('Document successfully ' + this.form_mode + 'd.');
                        } else {
                            if (response.data.error_message) {
                                this.$noty.error(response.data.error_message);
                            }
                        }

                        this.submit_btn_loading = false;
                    });
            }
        },
        validateDocumentForm() {
            let errors = [];

            if (!this.document.title) {
                errors.push(['You have not entered a valid title for the document.']);
            }

            if (!this.document.description) {
                errors.push(['You have not entered a valid description for the document.']);
            }

            if (this.link_selected && !this.document.link) {
                errors.push(['You have not entered a valid link for the document.']);
            }

            if (!this.link_selected && !this.document.filename) {
                errors.push(['You have not entered a valid attachment for the document.']);
            }

            if (errors.length === 0) {
                return true;
            } else {
                this.error_server_msg2 = errors;
                return false;
            }
        },
        validateForm() {
            let errors = [];
            this.error_server_msg3 = [];

            this.documents.forEach(function (line, index) {
                if (!line.title) {
                    errors.push([index + 1 + '. You have not entered a valid title for the document.']);
                }

                if (!line.description) {
                    errors.push([index + 1 + '. You have not entered a valid description for the document.']);
                }
            });

            if (errors.length === 0) {
                return true;
            } else {
                this.error_server_msg3 = errors;
                return false;
            }
        },
        formatFormData() {
            if (this.link_selected) {
                this.document.is_external = 1;
                this.document.filename = null;
            } else {
                this.document.is_external = 0;
                this.document.link = null;
            }

            return JSON.stringify(this.document);
        },
        clearDocumentData() {
            this.document = {
                id: null,
                title: null,
                description: null,
                file_encode: null,
                filename: null,
                is_external: null,
                link: null,
            };

            this.error_server_msg2 = [];

            this.show_methods = true;
            this.link_selected = false;
            this.has_attachment = false;
        },
        reloadDocuments() {
            setTimeout(this.loadDocumentsList(), 5000);
        },
        updateDocument(index) {
            let document = {
                id: this.documents[index].id,
                title: this.documents[index].title,
                description: this.documents[index].description,
                file_encode: this.documents[index].file_encode,
                filename: this.documents[index].file_encode,
                link: this.documents[index].link,
                is_external: this.documents[index].is_external,
            };

            if (document.file_encode) {
                this.has_attachment = true;
            }

            if (document.link) {
                this.link_selected = true;
                this.show_methods = false;
            }

            this.document = document;
            this.showDocumentModal('update');
        },
        deleteDocument(id) {
            this.dialogConfirmation = true;
            this.delete_id = id;
        },
        deleteDocumentConfirm() {
            this.delete_btn_loading = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', this.selected_lease_code);
            formData.append('deleted', JSON.stringify(this.delete_id));
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/delete-document';

            this.$api.post(apiUrl, formData).then((response) => {
                this.delete_id = null;
                let status = response.data.status;
                if (status == 'success') {
                    this.deleteDocumentCancel();
                    setTimeout(this.reloadDocuments(), 5000);
                    this.$noty.success('Document successfully deleted.');
                } else {
                    this.$noty.error(response.data.error_message);
                }

                this.delete_btn_loading = false;
            });
        },
        deleteDocumentCancel() {
            this.dialogConfirmation = false;
            this.delete_id = null;
        },
        getUploadButtonID(id) {
            return 'fileUploadDocs_' + id;
        },
        reselectMethod() {
            this.show_methods = true;
            this.link_selected = false;
        },
        selectLink() {
            this.link_selected = true;
            this.show_methods = false;
        },
        removeAttachedFile() {
            this.has_attachment = false;
            this.document.file_encode = null;
            this.document.filename = null;
        },
        setFirstOrLast(position, index) {
            let id = this.documents[index].id;
            let title = this.documents[index].title;
            let description = this.documents[index].description;
            let file_encode = this.documents[index].file_encode;
            let filename = this.documents[index].filename;
            let link = this.documents[index].link;
            let is_external = this.documents[index].is_external;

            if (position == 'first') {
                if (index == 0) {
                    return false;
                }

                for (let x = this.documents.length - 1; x >= 0; x--) {
                    if (x <= index && x != 0) {
                        this.documents[x].id = this.documents[x - 1].id;
                        this.documents[x].title = this.documents[x - 1].title;
                        this.documents[x].description = this.documents[x - 1].description;
                        this.documents[x].file_encode = this.documents[x - 1].file_encode;
                        this.documents[x].filename = this.documents[x - 1].filename;
                        this.documents[x].link = this.documents[x - 1].link;
                        this.documents[x].is_external = this.documents[x - 1].is_external;
                    } else if (x == 0) {
                        this.documents[0].id = id;
                        this.documents[0].title = title;
                        this.documents[0].description = description;
                        this.documents[0].file_encode = file_encode;
                        this.documents[0].filename = filename;
                        this.documents[0].link = link;
                        this.documents[0].is_external = is_external;
                    }
                }
            } else if (position == 'last') {
                if (this.documents.length == index + 1) {
                    return false;
                }

                let last = this.documents.length - 1;
                for (let x = 0; x < this.documents.length; x++) {
                    if (x >= index && x != last) {
                        this.documents[x].id = this.documents[x + 1].id;
                        this.documents[x].title = this.documents[x + 1].title;
                        this.documents[x].description = this.documents[x + 1].description;
                        this.documents[x].file_encode = this.documents[x + 1].file_encode;
                        this.documents[x].filename = this.documents[x + 1].filename;
                        this.documents[x].link = this.documents[x + 1].link;
                        this.documents[x].is_external = this.documents[x + 1].is_external;
                    } else if (x == last) {
                        this.documents[last].id = id;
                        this.documents[last].title = title;
                        this.documents[last].description = description;
                        this.documents[last].file_encode = file_encode;
                        this.documents[last].filename = filename;
                        this.documents[last].link = link;
                        this.documents[last].is_external = is_external;
                    }
                }
            }
        },
        setOrder(direction, index) {
            let oldIndex = index;
            let newIndex = 0;

            if (direction == 'up') {
                if (index == 0) {
                    return false;
                }

                newIndex = index - 1;
            } else if (direction == 'down') {
                if (this.documents.length == index + 1) {
                    return false;
                }

                newIndex = index + 1;
            }

            let id = this.documents[newIndex].id;
            let title = this.documents[newIndex].title;
            let description = this.documents[newIndex].description;
            let file_encode = this.documents[newIndex].file_encode;
            let filename = this.documents[newIndex].filename;
            let link = this.documents[newIndex].link;
            let is_external = this.documents[newIndex].is_external;

            this.documents[newIndex].id = this.documents[oldIndex].id;
            this.documents[newIndex].title = this.documents[oldIndex].title;
            this.documents[newIndex].description = this.documents[oldIndex].description;
            this.documents[newIndex].file_encode = this.documents[oldIndex].file_encode;
            this.documents[newIndex].filename = this.documents[oldIndex].filename;
            this.documents[newIndex].link = this.documents[oldIndex].link;
            this.documents[newIndex].is_external = this.documents[oldIndex].is_external;

            this.documents[oldIndex].id = id;
            this.documents[oldIndex].title = title;
            this.documents[oldIndex].description = description;
            this.documents[oldIndex].file_encode = file_encode;
            this.documents[oldIndex].filename = filename;
            this.documents[oldIndex].link = link;
            this.documents[oldIndex].is_external = is_external;
        },
        saveForm() {
            let validate = this.validateForm();

            if (validate) {
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('property_code', this.selected_property_code);
                formData.append('lease_code', this.selected_lease_code);
                formData.append('documents', JSON.stringify(this.documents));
                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/reorder-documents';

                this.$api.post(apiUrl, formData).then((response) => {
                    let status = response.data.status;
                    if (status == 'success') {
                        setTimeout(this.reloadDocuments(), 5000);

                        this.$noty.success('Document details successfully updated.');
                        this.edit_form = false;
                    } else {
                        this.$noty.error(response.data.error_message);
                    }
                });
            }
        },
        showLogModal() {
            this.show_change_log_modal = true;
        },

        //
    },

    watch: {
        selected_property_code: function () {
            this.loadDocumentsList();
            this.edit_form = false;
        },
        selected_lease_code: function () {
            this.loadDocumentsList();
            this.edit_form = false;
        },
        reloadComponents: function () {
            this.loadDocumentsList();
        },
    },
    mixins: [global_mixins],
};
</script>
