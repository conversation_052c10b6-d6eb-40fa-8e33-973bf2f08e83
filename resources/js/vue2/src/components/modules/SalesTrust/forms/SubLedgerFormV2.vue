<style>
.update-subledger-edit,
.update-subledger-close {
    color: #fff !important;
}
.update-subledger-edit,
.update-subledger-undo {
    padding-right: 10px;
}
.update-subledger-close i {
    font-size: 24px;
}
.update-subledger-form .form-row {
    height: 40px !important;
}
.show-inactive label,
.show-inactive .v-icon,
#subledgers_details_header .show-inactive .primary--text {
    color: #ffffff !important;
}
.show-inactive label {
    left: 5px !important;
}
</style>
<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>
            <v-card
                id="subledgers_details_header"
                class="section-toolbar"
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6
                        class="title font-weight-black"
                        style="position: relative; top: 1px"
                    >
                        Sub Ledgers
                    </h6>
                    &nbsp&nbsp
                    <v-checkbox
                        v-model="show_inactive_subledgers"
                        color="primary"
                        label="Show Inactive"
                        class="show-inactive"
                        style="margin-left: 25px"
                    />
                    <v-spacer></v-spacer>
                    <div style="display: inline-block; padding-right: 1em">
                        <span class="toolbar-search-holder">
                            <v-text-field
                                dense
                                v-model="searchSubLedger"
                                :class="'cirrus-input-form-textbox'"
                                :placeholder="'Search'"
                                prepend-inner-icon="search"
                                :maxlength="'60'"
                            ></v-text-field>
                        </span>
                    </div>
                    <v-progress-circular
                        indeterminate
                        style="height: 17px; width: 17px; margin-left: 2px; margin-right: 9px"
                        v-if="add_sub_ledger_loading"
                    ></v-progress-circular>
                    <v-btn
                        x-small
                        class="v-step-edit-button"
                        data-tooltip="Add Sub-Ledger"
                        icon
                        @click="showAddSubLedger()"
                        v-else
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <!--//v-btn x-small data-tooltip="Edit"
                        v-show="!edit_form"
                        class="v-step-edit-button" v-if="!edit_form" icon @click="edit_form = true">
                    <v-icon>edit</v-icon>
                    </v-btn//-->
                    <!--//v-btn x-small v-show="edit_form" data-tooltip="Undo Changes"
                        v-if="edit_form" class="v-step-revert-button" icon @click="resetCommissionForm()" title="Undo Changes">
                    <v-icon color="red">undo</v-icon>
                    </v-btn//-->
                    <!--//v-btn x-small v-show="edit_form" data-tooltip="Done"
                        v-if="edit_form" class="v-step-save-1-button" icon @click="edit_form = false">
                    <v-icon light color="green">check</v-icon>
                    </v-btn//-->
                    <v-btn
                        x-small
                        data-tooltip="Refresh"
                        class="v-step-refresh-button"
                        icon
                        @click="initialLoadSubLedgers()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        icon
                        data-tooltip="Changelog"
                        @click="openLedgerChangelogModal()"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
            <!--//v-card id="bank_details_header" dark color="titleHeader" text tile>
                <v-card-actions>
                    <h4 class="title font-weight-black">Sub Ledgers</h4>
                </v-card-actions>
            </v-card//-->

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div class="page-list">
                    <div class="c8-page-table">
                        <table v-show="dd_sub_ledger_list_copy.length > 0">
                            <tbody>
                                <tr class="form-row">
                                    <td colspan="2">
                                        <!--//v-text-field v-model="searchSubLedger" placeholder="Search..." dense//-->
                                    </td>
                                    <td width="20">&nbsp;</td>
                                    <td width="20%">&nbsp;</td>
                                    <td width="15%">&nbsp;</td>
                                    <td width="10%">&nbsp;</td>
                                    <!--//td v-if="edit_form" width="15%" align="right" style="padding-right:1.5px;">
                                        <v-btn class="v-step-save-1-button" @click="showAddSubLedger()"
                                               color="primary"
                                               dark
                                               right
                                               small
                                        >
                                            {{ save_ledger_button_label }}
                                        </v-btn>
                                    </td//-->
                                    <td
                                        width="10%"
                                        align="right"
                                        style="padding-left: 1.5px"
                                    >
                                        <v-btn
                                            class="v-step-save-1-button"
                                            @click="showSubLedgerBalances()"
                                            color="primary"
                                            dark
                                            right
                                            small
                                        >
                                            {{ update_balances_button_label }}
                                        </v-btn>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!--v-data-table
                                :headers="subledgerHeaders"
                                :items="sub_ledgers"
                                :search="searchSubLedger"
                                hide-default-footer
                                dense
                                :old_version="replace_content_of_items"
                        -->
                        <!-- replaced :items content from sub_ledgers to sub_ledger_rows that came from the computed function -->

                        <v-data-table
                            v-show="dd_sub_ledger_list_copy.length > 0"
                            v-model="subledger_list"
                            :headers="subledgerHeaders"
                            :items="sub_ledger_rows"
                            :search="searchSubLedger"
                            hide-default-footer
                            dense
                        >
                            <template v-slot:top="{ pagination, options, updateOptions }">
                                <v-spacer></v-spacer>
                                <v-data-footer
                                    :pagination="pagination"
                                    :options="options"
                                    @update:options="updateOptions"
                                    items-per-page-text="$vuetify.dataTable.itemsPerPageText"
                                />
                            </template>
                            <template v-slot:item.sub_ledger_code="{ item }">
                                <a
                                    title="Click to update"
                                    @click="updateSubLedgerDtlView(item)"
                                >
                                    {{ item.sub_ledger_code }}
                                </a>
                                <!-- <span> {{ item.sub_ledger_code }} </span> -->
                            </template>
                            <template v-slot:item.debtor_code="{ item }">
                                <a
                                    title="Show company details"
                                    @click="showCompanyDetails(item.company_details)"
                                    >{{ item.debtor_code }}</a
                                >
                            </template>
                            <template v-slot:item.sub_ledger_owner="{ item }">
                                <a
                                    title="Show owner details"
                                    @click="showOwnerDetails(item.owner_details)"
                                    >{{ item.sub_ledger_owner }}</a
                                >
                            </template>

                            <template v-slot:item.outstanding_ap="{ item }">
                                <span
                                    v-if="item.outstanding_ap == '-'"
                                    title="Click the Calculate Balances button to show Outstanding AP"
                                    >&nbsp;</span
                                >
                                <span v-else-if="item.outstanding_ap == 'Loading...'">{{ item.outstanding_ap }}</span>
                                <a
                                    v-else
                                    title="Show balance breakdown"
                                    align="right"
                                    @click="showModalAP(item)"
                                    >{{ formatAsCurrency(item.outstanding_ap, 2) }}</a
                                >
                            </template>
                            <template v-slot:item.outstanding_ar="{ item }">
                                <div
                                    style="width: 100%; text-align: center"
                                    v-if="item.outstanding_ar == '-'"
                                >
                                    <v-btn
                                        x-small
                                        @click="showSubLedgerBalances(item.sub_ledger_code)"
                                        >{{ item.update_balances_btn_label }}</v-btn
                                    >
                                </div>
                                <span v-else-if="item.outstanding_ar == 'Loading...'">{{ item.outstanding_ar }}</span>
                                <a
                                    v-else
                                    title="Show balance breakdown"
                                    align="right"
                                    @click="showModalAR(item)"
                                    >{{ formatAsCurrency(item.outstanding_ar, 2) }}</a
                                >
                            </template>
                            <template v-slot:item.balance="{ item }">
                                <span
                                    v-if="item.balance == '-'"
                                    title="Click the Calculate Balances button to show Cash Balance"
                                    >&nbsp;</span
                                >
                                <span v-else-if="item.balance == 'Loading...'">{{ item.balance }}</span>
                                <a
                                    v-else
                                    title="Show balance breakdown"
                                    align="right"
                                    @click="showModalBalance(item)"
                                    >{{ formatAsCurrency(item.balance, 2) }}</a
                                >
                            </template>
                            <template v-slot:item.sub_ledger_status_name="{ item }">{{
                                item.sub_ledger_status_name
                            }}</template>
                            <template v-slot:item.action="{ item }">
                                <v-btn
                                    text
                                    icon
                                    color="warning"
                                    x-small
                                    title="AP Invoice"
                                    data-position="bottom right"
                                    v-if="item.sub_ledger_status == 0"
                                    v-bind:href="
                                        'index.php?module=ap&command=invoice&creditorID=&propertyID=' +
                                        selected_property_code +
                                        '&leaseID=' +
                                        item.sub_ledger_code
                                    "
                                    v-bind:target="'_blank'"
                                >
                                    <v-icon>mdi-file-document-plus</v-icon>
                                </v-btn>
                                <!--								<a style="margin-top: 5px;margin-bottom: 5px;" v-bind:href="'index.php?module=ap&command=invoice&creditorID=&propertyID='+selected_property_code+'&leaseID='+item.sub_ledger_code">AP Invoice</a>-->
                                <!--								<br>-->
                                <!--								<br>-->
                                <v-btn
                                    text
                                    icon
                                    color="success"
                                    x-small
                                    title="AR Invoice"
                                    data-position="bottom right"
                                    v-if="item.sub_ledger_status == 0"
                                    v-bind:href="
                                        'index.php?module=ar&command=invoice&selectionMethod=ledger&propertyID=' +
                                        selected_property_code +
                                        '&leaseID=' +
                                        item.sub_ledger_code
                                    "
                                    v-bind:target="'_blank'"
                                >
                                    <v-icon>mdi-file-document-plus</v-icon>
                                </v-btn>
                                <!--								<a style="margin-top: 5px;margin-bottom: 5px;" v-bind:href="'index.php?module=ar&command=invoice&selectionMethod=ledger&propertyID='+selected_property_code+'&leaseID='+item.sub_ledger_code">AR Invoice</a>-->
                                <!--								<br>-->
                                <!--								<br>-->
                                <v-btn
                                    text
                                    icon
                                    color="primary"
                                    x-small
                                    title="Receipting"
                                    data-position="bottom right"
                                    v-if="item.sub_ledger_status == 0"
                                    v-bind:href="
                                        'index.php?module=ar&command=receipting&method=subLedgerProperties&leaseID=' +
                                        item.sub_ledger_code
                                    "
                                    v-bind:target="'_blank'"
                                >
                                    <v-icon>mdi-file-move</v-icon>
                                </v-btn>
                                <!--								<a style="margin-top: 5px;margin-bottom: 5px;" v-bind:href="'index.php?module=ar&command=receipting'">Receipting</a>-->
                                <v-btn
                                    text
                                    icon
                                    x-small
                                    title="Printing"
                                    data-position="bottom right"
                                    @click="showGenerateModal(item)"
                                >
                                    <v-icon>mdi-printer</v-icon>
                                </v-btn>
                                <!--                                <v-btn v-show="(total_balance == 0)" text icon color="amber" x-small title="Make Sub-Ledger Inactive" data-position="bottom right"-->
                                <!--                                    @click="makeInactiveSubledger(item)" >-->
                                <!--                                    <v-icon>mdi-eye-off</v-icon>-->
                                <!--                                </v-btn>-->
                                <!--                                <v-btn v-show="(total_balance == 0)" text icon color="error" x-small title="Delete Sub-Ledger" data-position="bottom right" @click="deleteSubledger(item)" >-->
                                <!--                                    <v-icon>mdi-delete-forever</v-icon>-->
                                <!--                                </v-btn>-->
                            </template>
                        </v-data-table>
                        <table v-show="dd_sub_ledger_list_copy.length > 0">
                            <tbody>
                                <tr class="form-row">
                                    <td width=" 8%">&nbsp;</td>
                                    <td width=" 6%">&nbsp;</td>
                                    <td width="15%">&nbsp;</td>
                                    <td width="15%">&nbsp;</td>
                                    <td width=" 8%">&nbsp;</td>
                                    <td width=" 8%">&nbsp;</td>
                                    <td width=" 6%">&nbsp;</td>
                                    <td width=" 8%">&nbsp;</td>
                                    <td width=" 8%">&nbsp;</td>
                                    <td
                                        width=" 8%"
                                        align="right"
                                        style="font-weight: bold"
                                    >
                                        {{ formatAsCurrency(total_balance, 2) }}
                                    </td>
                                    <td width="10%">&nbsp;</td>
                                </tr>
                            </tbody>
                        </table>

                        <v-col
                            class="text-center"
                            v-if="dd_sub_ledger_list_copy.length === 0"
                        >
                            <v-btn
                                depressed
                                small
                                color="success"
                                @click="showAddSubLedger()"
                                class="ldgr-add-btn"
                                >Add Sub-Ledger</v-btn
                            >
                        </v-col>
                    </div>
                </div>
            </div>
        </div>

        <!-- PRINT REPORT PER SUB LEDGER -->
        <v-dialog
            top
            v-model="modalGenerateReport"
            width="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>{{ modal_generate_title }}</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="closeGenerateModal()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: 300; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                            >
                                Report</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-btn-toggle
                                    v-model="report_option"
                                    mandatory
                                >
                                    <v-btn
                                        small
                                        text
                                    >
                                        {{ trust_account_label }}
                                    </v-btn>
                                    <v-btn
                                        small
                                        text
                                    >
                                        Seller
                                    </v-btn>
                                    <v-btn
                                        small
                                        text
                                    >
                                        Purchaser
                                    </v-btn>
                                </v-btn-toggle>
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            v-if="report_option != 0"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >{{ report_option == 1 ? 'Purchaser' : 'Seller' }}</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="sub_ledger_ps"
                                    :options="dd_sub_ledger_list"
                                    ref="sub_ledger_ref"
                                />
                            </v-col>
                        </v-row>
                        <!-- PERIOD DATE FROM -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Period From</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="period_from"
                                    :options="period_from_list"
                                    ref="refPeriodFrom"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- PERIOD DATE TO -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Period To</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="period_to"
                                    :options="period_to_list"
                                    ref="refPeriodTo"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- INCLUDE AP INVOICE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                            >
                                Include AP Invoices</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-checkbox
                                    v-model="include_ap_invoices"
                                    color="primary"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        @click="downloadSubLedgerReport()"
                        color="primary"
                        depressed
                        small
                        :loading="button_loading_setting"
                        :disabled="button_loading_setting"
                    >
                        DOWNLOAD
                    </v-btn>
                    <v-btn
                        @click="sendEmail()"
                        color="primary"
                        depressed
                        small
                        :loading="button_loading_setting"
                        :disabled="button_loading_setting"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >email</v-icon
                        >
                        Send Email
                    </v-btn>
                    <!--//v-btn color="primary" text @click="closeGenerateModal()">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF PRINT REPORT PER SUB LEDGER -->
        <!-- BALANCE BREAKDOWN DETAILS -->
        <v-dialog
            top
            v-model="modalBalanceBreakdown"
            max-width="90%"
            max-height="70%"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Balance Breakdown</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="modalBalanceBreakdown = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial; padding: 0px 5px"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr class="fieldDescription">
                                <td>Transaction Date</td>
                                <td>Sub-ledger / Supplier</td>
                                <td>AP Invoice #</td>
                                <td>Debtor / Payee</td>
                                <td>Description</td>
                                <td>Account Code</td>
                                <td>Cheque / EFT #</td>
                                <td>Receipt / Payment #</td>
                                <td align="center">From</td>
                                <td align="center">To</td>
                                <td align="right">Net</td>
                                <td align="right">{{ country_defaults.tax_label }}</td>
                                <td align="right">Received / Paid</td>
                                <td align="right">Balance</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><b>Opening Balance</b></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td align="right">{{ formatAsCurrency((beginingBalance = 0), 2) }}</td>
                            </tr>
                            <tr
                                v-for="(data, index) in breakdownTransaciton"
                                :key="index"
                            >
                                <td>{{ data.transactionDate }}</td>
                                <td>{{ data.leaseSupplierID }}</td>
                                <td>{{ data.invoiceNumber }}</td>
                                <td>{{ data.leaseSupplierName }}</td>
                                <td>{{ data.description }}</td>
                                <td>{{ data.accountID }}</td>
                                <td>{{ data.paymentReference }}</td>
                                <td>
                                    <a
                                        title="Show receipt allocated"
                                        @click="showModalReceiptDetails(data.receiptTran)"
                                        >{{ data.receiptNumber }}</a
                                    >
                                </td>
                                <td align="center">{{ data.fromDate }}</td>
                                <td align="center">{{ data.toDate }}</td>
                                <td align="right">{{ formatAsCurrency(data.netAmount, 2) }}</td>
                                <td align="right">{{ formatAsCurrency(data.gstAmount, 2) }}</td>
                                <td align="right">{{ formatAsCurrency(data.totalAmount, 2) }}</td>
                                <td align="right">
                                    {{
                                        formatAsCurrency(
                                            (beginingBalance = computeBalance(beginingBalance, data.totalAmount)),
                                            2,
                                        )
                                    }}
                                </td>
                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    Total :
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceNet, 2) }}
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceGST, 2) }}
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceAmt, 2) }}
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <!--//v-btn color="primary" text @click="modalBalanceBreakdown = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF RECEIPT DETAILS -->

        <!-- AP BREAKDOWN DETAILS -->
        <v-dialog
            top
            v-model="modalAPBreakdown"
            max-width="90%"
            max-height="70%"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Outstanding AP Breakdown</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="modalAPBreakdown = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial; padding: 0px 5px"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr class="fieldDescription">
                                <td>Supplier Code</td>
                                <td>Supplier Name</td>
                                <td>Transaction Date</td>
                                <td>Due Date</td>
                                <td>Invoice #</td>
                                <td>Description</td>
                                <td>Account Code</td>
                                <td align="right">Net</td>
                                <td align="right">{{ country_defaults.tax_label }}</td>
                                <td align="right">Gross</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(data, index) in breakdownTransaciton"
                                :key="index"
                            >
                                <td>{{ data.leaseSupplierID }}</td>
                                <td>{{ data.leaseSupplierName }}</td>
                                <td>{{ data.transactionDate }}</td>
                                <td>{{ data.dueDate }}</td>
                                <td>{{ data.invoiceNumber }}</td>
                                <td>{{ data.description }}</td>
                                <td>{{ data.accountID }}</td>
                                <td align="right">{{ formatAsCurrency(data.netAmount, 2) }}</td>
                                <td align="right">{{ formatAsCurrency(data.gstAmount, 2) }}</td>
                                <td align="right">{{ formatAsCurrency(data.totalAmount, 2) }}</td>
                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    Total :
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceNet, 2) }}
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceGST, 2) }}
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceAmt, 2) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <!--//v-btn color="primary" text @click="modalAPBreakdown = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF AP BREAKDOWN DETAILS -->

        <!-- AR BREAKDOWN DETAILS -->
        <v-dialog
            top
            v-model="modalARBreakdown"
            max-width="90%"
            max-height="70%"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Outstanding AR Breakdown</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="modalARBreakdown = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial; padding: 0px 5px"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr class="fieldDescription">
                                <td>Transaction Date</td>
                                <td>Due Date</td>
                                <td>Tax Invoice #</td>
                                <td>Description</td>
                                <td>Account Code</td>
                                <td align="center">From Date</td>
                                <td align="center">To Date</td>
                                <td align="right">Net</td>
                                <td align="right">{{ country_defaults.tax_label }}</td>
                                <td align="right">Gross</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(data, index) in breakdownTransaciton"
                                :key="index"
                            >
                                <td>{{ data.transactionDate }}</td>
                                <td>{{ data.dueDate }}</td>
                                <td v-if="data.invoiceNumber != 0">
                                    <a
                                        title="Download Invoice"
                                        @click="downloadInvoice(data)"
                                        >{{ data.invoiceNumber }}</a
                                    >
                                </td>
                                <td v-else>{{ data.invoiceNumber }}</td>
                                <td>{{ data.description }}</td>
                                <td>{{ data.accountID }}</td>
                                <td align="center">{{ data.fromDate }}</td>
                                <td align="center">{{ data.toDate }}</td>
                                <td align="right">{{ formatAsCurrency(data.netAmount, 2) }}</td>
                                <td align="right">{{ formatAsCurrency(data.gstAmount, 2) }}</td>
                                <td align="right">{{ formatAsCurrency(data.totalAmount, 2) }}</td>
                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    Total :
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceNet, 2) }}
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceGST, 2) }}
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalBalanceAmt, 2) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <!--//v-btn color="primary" text @click="modalARBreakdown = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF AR BREAKDOWN DETAILS -->

        <!-- RECEIPT DETAILS -->
        <v-dialog
            top
            v-model="modalReceiptDetails"
            max-width="90%"
            max-height="70%"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Allocated Transactions</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="modalReceiptDetails = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial; padding: 0px 5px"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr class="fieldDescription">
                                <td>Transaction Date</td>
                                <td>Ledger / Property</td>
                                <td>Sub-ledger / Supplier</td>
                                <td>AP Invoice #</td>
                                <td>Debtor / Payee</td>
                                <td>Description</td>
                                <td>Account Code</td>
                                <td>Cheque / EFT #</td>
                                <td>Receipt / Payment #</td>
                                <td align="center">From</td>
                                <td align="center">To</td>
                                <td align="right">Net</td>
                                <td align="right">{{ country_defaults.tax_label }}</td>
                                <td align="right">Received / Paid</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(data, index) in receiptDetails"
                                :key="index"
                            >
                                <td>{{ data.transactionDate }}</td>
                                <td>{{ data.propertyID }}</td>
                                <td>{{ data.leaseSupplierID }}</td>
                                <td>{{ data.invoiceNumber }}</td>
                                <td>{{ data.leaseSupplierName }}</td>
                                <td>{{ data.description }}</td>
                                <td>{{ data.accountID }}</td>
                                <td>{{ data.paymentReference }}</td>
                                <td>{{ data.receiptNumber }}</td>
                                <td align="center">{{ data.fromDate }}</td>
                                <td align="center">{{ data.toDate }}</td>
                                <td align="right">{{ formatAsCurrency(data.netAmount, 2) }}</td>
                                <td align="right">{{ formatAsCurrency(data.gstAmount, 2) }}</td>
                                <td align="right">{{ formatAsCurrency(data.totalAmount, 2) }}</td>
                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    Total :
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalReceiptNet, 2) }}
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalReceiptGST, 2) }}
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalReceiptAmt, 2) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <!--//v-btn color="primary" text @click="modalReceiptDetails = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF RECEIPT DETAILS -->

        <!-- COMPANY DETAILS -->
        <v-dialog
            top
            v-model="modalCompanyDetails"
            width="70%"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Company Details</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="modalCompanyDetails = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="4"
                                md="4"
                            >
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Company Code</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_company_code }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Company Name</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_company_name }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Company Address</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_company_address }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Company {{ suburb_label }}</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_company_suburb }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Company Post Code</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_company_postcode }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="cdf_for_address.display_state"
                                >
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Company State</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_company_state }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Company Country</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_company_country }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Company E-mail</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col v-if="modal_company_email && modal_company_email.includes(';')">
                                            <v-item-list
                                                v-for="(data, index) in modal_company_emails"
                                                :key="index"
                                            >
                                                <a :href="'mailto:' + data">{{ data }}</a
                                                ><br />
                                            </v-item-list>
                                        </v-col>
                                        <v-col v-else>
                                            <a
                                                v-if="modal_company_email"
                                                :href="'mailto:' + modal_company_email"
                                                >{{ modal_company_email }}</a
                                            >
                                        </v-col>
                                    </v-col>
                                </v-row>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="8"
                                md="8"
                            >
                                <v-row
                                    class="form-row"
                                    style="border-bottom: 0 !important"
                                >
                                    <v-item-list
                                        v-for="(data, index) in modal_contact_details"
                                        :key="index"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="12"
                                            md="12"
                                        >
                                            <v-row
                                                class="form-row"
                                                style="border-bottom: 0 !important"
                                            >
                                                <v-col
                                                    xs="12"
                                                    sm="12"
                                                    md="12"
                                                    ><strong>{{ data.contact_name }}</strong></v-col
                                                >
                                            </v-row>
                                            <v-row
                                                class="form-row"
                                                v-if="data.contact_description"
                                                style="border-bottom: 0 !important"
                                            >
                                                <v-col
                                                    xs="12"
                                                    sm="12"
                                                    md="12"
                                                    >{{ data.contact_description }}</v-col
                                                >
                                            </v-row>
                                            <v-row
                                                class="form-row"
                                                v-else
                                                style="border-bottom: 0 !important"
                                            >
                                                <v-col
                                                    xs="12"
                                                    sm="12"
                                                    md="12"
                                                    >&nbsp;</v-col
                                                >
                                            </v-row>
                                            <v-list-item
                                                v-for="details in data.details"
                                                :key="details.s_serial"
                                            >
                                                <v-row>
                                                    <v-col
                                                        xs="12"
                                                        sm="12"
                                                        md="12"
                                                    >
                                                        <strong>{{ details.phone_desc }}</strong
                                                        ><br />

                                                        <span v-if="details.phone_desc == 'E-Mail'"
                                                            ><a :href="'mailto:' + details.phone_no">{{
                                                                details.phone_no
                                                            }}</a></span
                                                        >

                                                        <span
                                                            v-if="
                                                                details.phone_desc == 'Home' ||
                                                                details.phone_desc == 'Mobile Phone' ||
                                                                details.phone_desc == 'Owner Mobile' ||
                                                                details.phone_desc == 'Service Mobile' ||
                                                                details.phone_desc == 'Tenant Mobile' ||
                                                                details.phone_desc == 'Phone'
                                                            "
                                                            ><a
                                                                :href="'tel:' + details.phone_no.replace(/[\s\/]/g, '')"
                                                                >{{ details.phone_no }}</a
                                                            ></span
                                                        >

                                                        <span
                                                            v-if="
                                                                details.phone_desc != 'Home' &&
                                                                details.phone_desc != 'Mobile Phone' &&
                                                                details.phone_desc != 'Owner Mobile' &&
                                                                details.phone_desc != 'Service Mobile' &&
                                                                details.phone_desc != 'Tenant Mobile' &&
                                                                details.phone_desc != 'Phone' &&
                                                                details.phone_desc != 'E-Mail'
                                                            "
                                                            >{{ details.phone_no }}</span
                                                        >
                                                    </v-col>
                                                </v-row>
                                            </v-list-item>
                                        </v-col>
                                    </v-item-list>
                                </v-row>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <!--//v-btn color="primary" text @click="modalCompanyDetails = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF COMPANY DETAILS -->

        <!-- OWNER COMPANY DETAILS -->
        <v-dialog
            top
            v-model="modalOwnerCompanyDetails"
            width="70%"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Owner Details</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="modalOwnerCompanyDetails = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="4"
                                md="4"
                            >
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Owner Code</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_owner_code }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Owner Name</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_owner_name }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Owner Address</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_owner_address }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Owner {{ suburb_label }}</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_owner_suburb }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Owner Post Code</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_owner_postcode }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Owner State</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_owner_state }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Owner Country</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_owner_country }}</v-col>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-label"
                                        >Owner E-mail</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="7"
                                        md="7"
                                        class="form-input"
                                    >
                                        <v-col>{{ modal_owner_email }}</v-col>
                                    </v-col>
                                </v-row>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="8"
                                md="8"
                            >
                                <v-row
                                    class="form-row"
                                    style="border-bottom: 0 !important"
                                >
                                    <v-item-list
                                        v-for="(data, index) in modal_owner_details"
                                        :key="index"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="12"
                                            md="12"
                                        >
                                            <v-row
                                                class="form-row"
                                                style="border-bottom: 0 !important"
                                            >
                                                <v-col
                                                    xs="12"
                                                    sm="12"
                                                    md="12"
                                                    ><strong>{{ data.contact_name }}</strong></v-col
                                                >
                                            </v-row>
                                            <v-row
                                                class="form-row"
                                                v-if="data.contact_description"
                                                style="border-bottom: 0 !important"
                                            >
                                                <v-col
                                                    xs="12"
                                                    sm="12"
                                                    md="12"
                                                    >{{ data.contact_description }}</v-col
                                                >
                                            </v-row>
                                            <v-row
                                                class="form-row"
                                                v-else
                                                style="border-bottom: 0 !important"
                                            >
                                                <v-col
                                                    xs="12"
                                                    sm="12"
                                                    md="12"
                                                    >&nbsp;</v-col
                                                >
                                            </v-row>
                                            <v-list-item
                                                v-for="details in data.details"
                                                :key="details.s_serial"
                                            >
                                                <v-row>
                                                    <v-col
                                                        xs="12"
                                                        sm="12"
                                                        md="12"
                                                    >
                                                        <strong>{{ details.phone_desc }}</strong
                                                        ><br />
                                                        {{ details.phone_no }}
                                                    </v-col>
                                                </v-row>
                                            </v-list-item>
                                        </v-col>
                                    </v-item-list>
                                </v-row>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <!--//v-btn color="primary" text @click="modalOwnerCompanyDetails = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF OWNER COMPANY DETAILS -->

        <!-- ADD NEW COMPANY -->
        <v-dialog
            top
            v-model="modalCompany"
            width="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>New Company</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="hideModalCompany()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company Code</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="property_owner_code"
                                    :maxlength="10"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'property_owner_code'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company Name</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="property_owner_name"
                                    :maxlength="240"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'property_owner_name'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company Address</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        class="form-input"
                                    >
                                        <v-text-field
                                            v-model="property_owner_address1"
                                            :maxlength="37"
                                            dense
                                        />
                                        <v-chip
                                            v-if="error_msg.length > 0 && errorData.id === 'property_owner_address1'"
                                            v-for="(errorData, index) in error_msg"
                                            :key="index"
                                            outlined
                                            color="error"
                                        >
                                            <v-icon left>error</v-icon>
                                            {{ errorData.message }}
                                        </v-chip>
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        class="form-input"
                                    >
                                        <v-text-field
                                            v-model="property_owner_address2"
                                            :maxlength="37"
                                            dense
                                        />
                                    </v-col>
                                </v-row>
                            </v-col>
                        </v-row>

                        <!-- <v-row class="form-row">
                            <v-col xs="12" sm="3" md="3" class="form-label required">Company Post Town</v-col>
                            <v-col xs="12" sm="9" md="9" class="form-input">
                                <cirrus-autocomplete-suburb
                                    v-model="property_owner_suburb"
                                    :options="suburbOwner_list_filtered"
                                    :selectedValue="suburbOwnerValue"
                                    @input="suburbOwnerSelected(property_owner_suburb)"
                                    ref="refSuburb"
                                    dense
                                />
                                <v-chip v-if="error_msg.length>0 && errorData.id === 'property_owner_suburb'"
                                    v-for="(errorData,index) in error_msg" :key="index" outlined color="error">
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                         </v-row> -->

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company {{ suburb_label }}</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <div v-if="this.property_owner_country_list == 'AU'">
                                    <v-combobox
                                        v-model="property_owner_suburb"
                                        v-if="property_owner_state_list != ''"
                                        :items="suburbOwner_list_filtered"
                                        item-value="label"
                                        item-text="label"
                                        @change="suburbOwnerSelected(property_owner_suburb)"
                                        auto-select-first
                                        hide-selected
                                        persistent-hint
                                        append-icon
                                        :search-input.sync="searchOwnerSuburb"
                                        :hide-no-data="!searchOwnerSuburb"
                                        dense
                                        flat
                                        ref="refSuburb"
                                        :maxlength="40"
                                    >
                                        <template v-slot:no-data>
                                            <v-list-item>
                                                <v-chip
                                                    v-model="searchOwnerSuburb"
                                                    small
                                                >
                                                    {{ searchOwnerSuburb }}
                                                </v-chip>
                                            </v-list-item>
                                        </template>
                                    </v-combobox>
                                    <v-text-field
                                        :maxlength="40"
                                        dense
                                        v-model="property_owner_suburb"
                                        v-else
                                    />
                                </div>
                                <div v-else>
                                    <v-text-field
                                        :maxlength="40"
                                        dense
                                        v-model="property_owner_suburb"
                                    />
                                </div>

                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'property_owner_suburb'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company Post Code</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="property_owner_post"
                                    :maxlength="10"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'property_owner_post'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="cdf_for_address.display_state"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company State</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="property_owner_state_list"
                                    :options="stateOwner_list_filtered"
                                    @input="suburbOwnerFilteredList(property_owner_state_list)"
                                    ref="refState"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'property_owner_state'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company Country</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="property_owner_country_list"
                                    :options="agent_country_list"
                                    @input="stateOwnerFilteredList(property_owner_country_list)"
                                    ref="refCompany"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'property_owner_country_list'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Company E-mail</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="property_owner_email"
                                    dense
                                />
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        :loading="save_company_btn_loading"
                        @click="saveNewCompany()"
                        >Save</v-btn
                    >
                    <!--//v-btn color="primary" text @click="hideModalCompany()">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF ADD NEW COMPANY -->

        <!-- UPDATE SUB LEDGER -->
        <v-dialog
            top
            persistent
            v-model="modalUpdateSubLedger"
            width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Update Sub Ledger</span>
                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        v-if="!update_subledger"
                        class="v-step-edit-button update-subledger-edit"
                        icon
                        @click="update_subledger = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="update_subledger"
                        class="v-step-save-1-button .update-subledger-undo"
                        icon
                        @click="update_subledger = false"
                    >
                        <v-icon
                            light
                            color="red"
                            >undo</v-icon
                        >
                    </v-btn>
                    <v-btn
                        x-small
                        class="v-step-save-1-button no-hover update-subledger-close"
                        icon
                        @click="closeUpdateSubledgerDialog()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial"
                >
                    <cirrus-server-error :errorMsg2="error_server_msg3"></cirrus-server-error>

                    <div
                        class="page-form update-subledger-form"
                        @dblclick="doubleClickSubLedger()"
                    >
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Sub Ledger Code</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    >{{ update_sub_ledger_code }}</v-col
                                >
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub Ledger Type</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    v-if="!update_subledger"
                                    >{{ update_data_item.sub_ledger_type }}</v-col
                                >
                                <cirrus-single-select
                                    v-else
                                    v-model="update_sub_ledger_type_code"
                                    :options="dd_sub_ledger_type_list"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub Ledger Name</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    v-if="!update_subledger"
                                    >{{ update_data_item.sub_ledger_name }}</v-col
                                >
                                <v-text-field
                                    v-else
                                    v-model="update_sub_ledger_name"
                                    :maxlength="60"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Location</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    v-if="!update_subledger"
                                    >{{ update_data_item.sub_ledger_location }}</v-col
                                >
                                <v-text-field
                                    v-else
                                    v-model="update_sub_ledger_location"
                                    :maxlength="50"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >CRN</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    v-if="!update_subledger"
                                    >{{ update_data_item.sub_ledger_crn }}</v-col
                                >
                                <v-text-field
                                    v-else
                                    v-model="update_sub_ledger_crn"
                                    :maxlength="8"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'sub_ledger_crn'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                    style="margin-top: -5px"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            v-if="companyReadonly"
                            :style="'min-height: 40px; ' + subledger_owner_row_css"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Sub-Ledger Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                            >
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    style="margin-top: 2px"
                                >
                                    {{ update_debtor }}

                                    <span style="padding-left: 5px">
                                        <v-btn
                                            title="Company Details"
                                            x-small
                                            icon
                                            @click="showCompanyDetails(update_debtor_details)"
                                        >
                                            <v-icon color="primary">info</v-icon>
                                        </v-btn>
                                    </span>
                                </v-col>

                                <!--//div style="margin-top: -25; margin-left: 300;">
                                    <v-btn title='Company Details' x-small icon @click="showCompanyDetails(update_debtor_details)" >
                                        <v-icon color="primary">info</v-icon>
                                    </v-btn>
                                </div//-->
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-else
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub-Ledger Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                style="padding-bottom: 10px"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    v-if="!update_subledger"
                                >
                                    {{ displayCompanyLabel(update_debtor_code) }}

                                    <span style="padding-left: 5px">
                                        <v-btn
                                            title="Company Details"
                                            x-small
                                            icon
                                            @click="showCompanyDetails(update_debtor_details)"
                                        >
                                            <v-icon color="primary">info</v-icon>
                                        </v-btn>
                                    </span>
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    style="padding: 0px"
                                    v-else
                                >
                                    <cirrus-single-select
                                        v-model="update_debtor_code"
                                        :options="dd_company_list"
                                        @input="updateDebtorDetails(update_debtor_code)"
                                    />
                                    <div style="margin-top: -25; margin-left: 300; padding-left: 5px">
                                        <v-btn
                                            title="Company Details"
                                            x-small
                                            icon
                                            @click="showCompanyDetails(update_debtor_details)"
                                        >
                                            <v-icon color="primary">info</v-icon>
                                        </v-btn>
                                    </div>
                                </v-col>

                                <!--//div style="margin-top: -25; margin-left: 300;">
                                    <v-btn title='Company Details' x-small icon @click="showCompanyDetails(update_debtor_details)" >
                                        <v-icon color="primary">info</v-icon>
                                    </v-btn>
                                </div//-->
                            </v-col>
                        </v-row>
                        <!--
                        <v-row class="form-row"  >
                            <v-col xs="12" sm="3" md="3" class="form-label ">Balance</v-col>
                            <v-col xs="12" sm="9" md="9" class="form-input">
                                <v-text-field v-model="update_balance" :readonly="true" dense />
                            </v-col>
                        </v-row>
                        -->
                        <v-row
                            class="form-row"
                            v-if="this.category == 'BOND'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Owner</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    v-if="!update_subledger"
                                    >{{ displayOwnerLabel(update_sub_ledger_owner) }}</v-col
                                >
                                <cirrus-single-select
                                    v-else
                                    v-model="update_sub_ledger_owner"
                                    :options="dd_owner_company_list"
                                    ref="refUpdateSubLedgerOwner"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Status</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    v-if="!update_subledger"
                                    >{{ update_data_item.sub_ledger_status_name }}</v-col
                                >
                                <v-btn-toggle
                                    class="form-toggle"
                                    v-else
                                    v-model="update_sub_ledger_status"
                                    mandatory
                                >
                                    <v-btn
                                        small
                                        text
                                    >
                                        Active
                                    </v-btn>
                                    <v-btn
                                        small
                                        text
                                        :disabled="disableInactive"
                                    >
                                        Inactive
                                    </v-btn>
                                </v-btn-toggle>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions style="min-height: 44px">
                    <v-btn
                        class="v-step-save-1-button"
                        @click="showConfirmationDialog(update_data_item)"
                        color="error"
                        dark
                        right
                        small
                        v-if="update_subledger"
                        v-show="!confirm_crn_assignment"
                    >
                        DELETE SUB LEDGER
                    </v-btn>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        @click="updateSubLedgerDtl()"
                        :loading="update_subledger_loading"
                        color="primary"
                        dark
                        right
                        small
                        v-if="update_subledger"
                        v-show="!confirm_crn_assignment"
                    >
                        UPDATE SUB LEDGER
                    </v-btn>

                    <div v-if="confirm_crn_assignment">
                        <span style="position: relative; top: 2px; margin-right: 5px"
                            ><v-icon
                                color="warning"
                                style="position: relative; top: -2px"
                                >warning</v-icon
                            >
                            <span v-html="confirm_crn_message"></span
                        ></span>
                        <v-btn
                            @click="updateSubLedgerDtl(true)"
                            :loading="crn_update_loading"
                            small
                            color="success"
                            >Yes</v-btn
                        >
                        <v-btn
                            @click="dontAssignCRN()"
                            small
                            color="error"
                            >No</v-btn
                        >
                    </div>
                    <!--//v-btn color="primary" text @click="modalUpdateSubLedger = false">Close</v-btn//-->
                </v-card-actions>

                <!-- CONTACTS & DETAILS -->
                <subledger-contacts-component
                    id="subledger-contacts-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="selected_property_code"
                    :selected_lease_code="update_sub_ledger_code"
                ></subledger-contacts-component>

                <!-- DOCUMENTS -->
                <subledger-documents-component
                    id="subledger-documents-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="selected_property_code"
                    :selected_lease_code="update_sub_ledger_code"
                ></subledger-documents-component>

                <!-- NOTES -->
                <subledger-notes-component
                    id="subledger-notes-section"
                    :reloadComponents="reloadComponents"
                    :selected_property_code="selected_property_code"
                    :selected_lease_code="update_sub_ledger_code"
                ></subledger-notes-component>

                <!-- <br/>
                <br/>
                <br/>
                <br/>
                <br/>
                <br/>
                <br/>
                <br/>
                <br/>
                <br/>
                <br/> -->
            </v-card>
        </v-dialog>
        <!-- END OF UPDATE SUB LEDGER -->

        <!-- ADD SUB LEDGER -->
        <v-dialog
            top
            v-model="modalAddSubLedger"
            width="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Add Sub Ledger</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="modalAddSubLedger = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub Ledger Code</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="sub_ledger_code"
                                    :maxlength="10"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub Ledger Type</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="sub_ledger_type"
                                    :options="dd_sub_ledger_type_list"
                                    ref="refSubLedgerType"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub Ledger Name</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="sub_ledger_name"
                                    :maxlength="60"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Location</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="sub_ledger_location"
                                    :maxlength="50"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            style="min-height: 40px"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub-Ledger Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="company_code"
                                    :options="dd_company_list"
                                    ref="refCompanyCode"
                                />
                                <div style="margin-top: -25; margin-left: 300">
                                    <v-btn
                                        title="Company Details"
                                        x-small
                                        icon
                                        @click="showModalCompany()"
                                    >
                                        <v-icon color="primary">add</v-icon>
                                    </v-btn>
                                </div>
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            v-if="this.category == 'BOND'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Owner</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="sub_ledger_owner"
                                    :options="dd_owner_company_list"
                                    ref="refSubLedgerOwner"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        :loading="save_subledger_btn_loading"
                        @click="saveSubLedger()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        ADD SUB LEDGER
                    </v-btn>
                    <!--//v-btn color="primary" text @click="modalAddSubLedger = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF UPDATE SUB LEDGER -->

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon> WARNING
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click.prevent="resetDeleteDialog()"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">{{ warning_message }}</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        :loading="delete_subledger_btn_loading"
                        @click="deleteSubledger()"
                        >Ok</v-btn
                    >
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="resetDeleteDialog()"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->

        <!-- SUBLEDGER CHANGELOG -->
        <v-dialog
            top
            v-model="show_change_log_modal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Activity Logs</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="show_change_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <activity-logs-component
                            v-if="show_change_log_modal"
                            :property_code="selected_property_code"
                            :lease_code="'-'"
                            :form_section="'subledger'"
                        ></activity-logs-component>
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF SUBLEDGER CHANGELOG -->
    </v-container>
</template>

<script>
import Vue from 'vue';
import vSelect from 'vue-select';
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

const Swal = require('sweetalert2');
Vue.component('v-select', vSelect);

export default {
    props: {
        selected_property_code: { type: String, default: '' },
        reloadComponents: { type: Number, default: 0 },
        agent_country_list: {},
        agent_state_list: {},
        agent_suburb_list: {},
        default_country: {},
        default_state: {},
        refCompany: { default: '' },
        category: '',
    },
    data() {
        return {
            trust_account_label: 'Trust Account',
            suburb_label: 'Suburb',
            hide: true,
            loading_setting: false,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            error_server_msg3: [],

            show_inactive_subledgers: false,
            save_ledger_button_label: 'Add Sub-ledger',
            update_balances_button_label: 'Calculate Balances',
            sub_ledger_code: '',
            sub_ledger_name: '',
            sub_ledger_location: '',
            company_code: '',
            sub_ledger_type: '',
            sub_ledger_owner: '',

            sub_ledgers: [],
            sub_ledger_balances: [],
            total_balance: 0,
            totalBalanceNet: 0,
            totalBalanceGST: 0,
            totalBalanceAmt: 0,

            modalBalanceBreakdown: false,
            breakdownTransaciton: [],
            modalAPBreakdown: false,
            modalARBreakdown: false,

            modalReceiptDetails: false,
            receiptDetails: [],
            totalReceiptNet: 0,
            totalReceiptGST: 0,
            totalReceiptAmt: 0,

            modalCompany: false,

            property_owner_code: '',
            property_owner_name: '',
            property_owner_address1: '',
            property_owner_address2: '',
            property_owner_suburb: '',
            property_owner_state: '',
            property_owner_post: '',
            property_owner_country: '',
            property_owner_email: '',
            property_owner_state_list: '',
            property_owner_country_list: '',

            suburbOwner_list_filtered: [],
            stateOwner_list_filtered: [],
            suburbOwnerValue: [],
            selectedCountryValue: '',

            modalCompanyDetails: false,
            modal_company_code: '',
            modal_company_name: '',
            modal_company_address: '',
            modal_company_suburb: '',
            modal_company_postcode: '',
            modal_company_state: '',
            modal_company_country: '',
            modal_company_email: '',
            modal_company_emails: [],
            modal_contact_details: [],

            modalOwnerCompanyDetails: false,
            modal_owner_code: '',
            modal_owner_name: '',
            modal_owner_address: '',
            modal_owner_suburb: '',
            modal_owner_postcode: '',
            modal_owner_state: '',
            modal_owner_country: '',
            modal_owner_email: '',

            sub_ledger_status: '',
            statusBy: [
                { label: 'Active', value: '0' },
                { label: 'Inactive', value: '1' },
            ],
            statusBys: { 0: 'Active', 1: 'Inactive' },

            add_sub_ledger_loading: false,

            modalUpdateSubLedger: false,
            update_ledger_code: '',
            update_sub_ledger_code: '',
            update_sub_ledger_type_code: '',
            update_sub_ledger_name: '',
            update_sub_ledger_location: '',
            update_debtor: '',
            update_debtor_code: '',
            update_debtor_code_old: '',
            update_debtor_details: [],
            update_balance: '',
            update_sub_ledger_status: 0,
            update_data_item: [],
            update_sub_ledger_crn: '',

            subledgerHeaders: [],
            subledgerHeadersWithOwner: [
                { text: 'Code', value: 'sub_ledger_code', sortable: false, width: '8%' },
                { text: 'Type', value: 'sub_ledger_type', sortable: false, width: '6%' },
                { text: 'Name', value: 'sub_ledger_name', sortable: false, width: '15%' },
                { text: 'Location', value: 'sub_ledger_location', sortable: false, width: '15%' },
                { text: 'Sub-Ledger Company', value: 'debtor_code', sortable: false, width: '8%' },
                { text: 'Owner', value: 'sub_ledger_owner', sortable: false, width: '8%' },
                { text: 'Status', value: 'sub_ledger_status_name', sortable: false, width: '6%' },
                { text: 'Outstanding AP', value: 'outstanding_ap', sortable: false, width: '8%', align: 'end' },
                { text: 'Outstanding AR', value: 'outstanding_ar', sortable: false, width: '8%', align: 'end' },
                { text: 'Cash Balance', value: 'balance', sortable: false, width: '8%', align: 'end' },
                { text: 'Actions', value: 'action', sortable: false, width: '10%', align: 'end' },
            ],
            subledgerHeadersWithoutOwner: [
                { text: 'Code', value: 'sub_ledger_code', sortable: false, width: '8%' },
                { text: 'Type', value: 'sub_ledger_type', sortable: false, width: '6%' },
                { text: 'Name', value: 'sub_ledger_name', sortable: false, width: '15%' },
                { text: 'Location', value: 'sub_ledger_location', sortable: false, width: '15%' },
                { text: 'Sub-Ledger Company', value: 'debtor_code', sortable: false, width: '8%' },
                { text: 'Status', value: 'sub_ledger_status_name', sortable: false, width: '6%' },
                { text: 'Outstanding AP', value: 'outstanding_ap', sortable: false, width: '8%', align: 'end' },
                { text: 'Outstanding AR', value: 'outstanding_ar', sortable: false, width: '8%', align: 'end' },
                { text: 'Cash Balance', value: 'balance', sortable: false, width: '8%', align: 'end' },
                { text: 'Actions', value: 'action', sortable: false, width: '10%', align: 'end' },
            ],
            searchSubLedger: '',

            modalAddSubLedger: false,
            companyReadonly: false,
            disableInactive: false,

            searchOwnerSuburb: '',
            post_code_length: 4,

            //  GENERATE SUB LEDGER REPORT FIELDS
            modalGenerateReport: false,
            modal_generate_title: '',
            include_ap_invoices: false,
            period_from: '',
            period_to: '',
            period_from_list: [],
            period_to_list: [],

            subledger_property_id: '',
            subledger_lease_id: '',
            subledger_company_id: '',
            subledger_filename: '',
            button_loading_setting: false,

            dialogConfirmation: '',
            edit_form: false,
            item_for_deletion: [],

            show_change_log_modal: false,
            sub_ledger_ps: '',
            dd_sub_ledger_list: [],
            dd_sub_ledger_list_copy: [],
            report_option: '',

            update_subledger: false,
            confirm_crn_assignment: false,
            confirm_crn_message: '',
            update_subledger_loading: false,
            subledger_crn_value: '',
            crn_update_loading: false,

            subledger_owner_row_css: '',
            save_company_btn_loading: false,
            save_subledger_btn_loading: false,
            delete_subledger_btn_loading: false,
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
                tax_label: 'GST',
                business_prefix: '',
                currency_symbol: '$',
            },
            cdf_for_address: {
                country_code: 'AU',
                display_state: true,
                post_code_length: '4',
            },
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        // this.$noty.error('test');
        // this.$noty.error('test2');
        //this.checkCategoryType();
        this.fetchCompanyList();
        this.fetchSubLedgerTypeList();
        this.fetchOwnerCompanyList();
        this.initialLoadSubLedgers();
        this.resetCompanyField();
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'dd_company_list',
            'dd_sub_ledger_type_list',
            'dd_owner_company_list',
        ]),
        sub_ledger_rows() {
            let data = [];
            this.total_balance = 0;
            this.dd_sub_ledger_list_copy = [];
            for (var i = 0; i < this.sub_ledgers.length; i++) {
                let row = this.sub_ledgers[i];
                if (this.show_inactive_subledgers == false && parseInt(row.sub_ledger_status) == 1) {
                    continue;
                } else {
                    this.total_balance += row.balance != '-' ? parseFloat(row.balance) : 0;
                    data.push(row);
                }

                this.dd_sub_ledger_list_copy.push({
                    fieldKey: row.sub_ledger_code,
                    field_key: row.sub_ledger_code,
                    value: row.sub_ledger_code,
                    label: row.sub_ledger_code + ' - ' + row.sub_ledger_name,
                    fieldValue: row.sub_ledger_code + ' - ' + row.sub_ledger_name,
                    field_value: row.sub_ledger_code + ' - ' + row.sub_ledger_name,
                    type: row.sub_ledger_type,
                });
            }

            return data;
        },
    },
    methods: {
        ...mapActions(['fetchCompanyList', 'fetchSubLedgerTypeList', 'fetchOwnerCompanyList']),
        ...mapMutations([
            'SET_DD_COMPANY_LIST',
            'SET_DD_OWNER_COMPANY_LIST',
            'SET_SUB_LEDGER_TYPE_CODE',
            'SET_DD_SUB_LEDGER_TYPE_LIST',
        ]),

        doubleClickForm() {
            if (!this.edit_form) {
                this.edit_form = true;
            }
        },

        doubleClickSubLedger() {
            if (!this.update_subledger) {
                this.update_subledger = true;
            }
        },

        checkCategoryType: function () {
            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('property_code', this.selected_property_code);
            formData.append('no_total_balance', true);
            formData.append('no_load', true);

            // get ledger details by code
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-ledger';

            this.$api.post(apiUrl, formData).then((response) => {
                if (response.data.property_type == 'BOND') {
                    this.subledgerHeaders = this.subledgerHeadersWithOwner;
                } else {
                    this.subledgerHeaders = this.subledgerHeadersWithoutOwner;
                }
            });

            /*axios.post(apiUrl, formData).then(response => {
                    if(response.data.property_type == 'BOND'){
                        this.subledgerHeaders = this.subledgerHeadersWithOwner;
                    }else{
                        this.subledgerHeaders = this.subledgerHeadersWithoutOwner;
                    }
                });*/
        },

        saveNewCompany: function () {
            this.error_msg = [];
            if (this.property_owner_code == '') {
                this.error_msg.push({
                    id: 'property_owner_code',
                    message: 'You have not specified a company code.',
                });
            }

            // Validate if property owner code has special character
            if (this.verifyCode(this.property_owner_code)) {
                this.$noty.error('Please use only alphanumeric characters for the company code.');
                validate = false;
            }

            if (this.property_owner_name == '') {
                this.error_msg.push({
                    id: 'property_owner_name',
                    message: 'You have not specified a company name.',
                });
            }

            if (this.property_owner_address1 == '') {
                this.error_msg.push({
                    id: 'property_owner_address1',
                    message: 'You have not specified a company address.',
                });
            }

            if (this.property_owner_suburb == '') {
                this.error_msg.push({
                    id: 'property_owner_suburb',
                    message: 'You have not specified a company suburb.',
                });
            }

            if (this.property_owner_country_list == '') {
                this.error_msg.push({
                    id: 'property_owner_country_list',
                    message: 'You have not specified a company country.',
                });
            }

            if (this.cdf_for_address.display_state && this.property_owner_state_list == '') {
                this.error_msg.push({
                    id: 'property_owner_state',
                    message: 'You have not specified a company state.',
                });
            }

            if (this.property_owner_post == '') {
                this.error_msg.push({
                    id: 'property_owner_post',
                    message: 'You have not specified a company post code.',
                });
            }

            /*if (!$.isNumeric(this.property_owner_post) && this.property_owner_post != '' ){
                    this.error_msg.push({id: 'property_owner_post', message: 'Please use only numeric characters for the company post code.'});
                }*/

            if (
                (this.property_owner_post.length < this.cdf_for_address.post_code_min_length ||
                    this.property_owner_post.length > this.cdf_for_address.post_code_length) &&
                this.property_owner_post != ''
            ) {
                let comp_length_val = '4';
                if (this.cdf_for_address.post_code_min_length != this.cdf_for_address.post_code_length) {
                    comp_length_val =
                        this.cdf_for_address.post_code_min_length + ' to ' + this.cdf_for_address.post_code_length;
                } else {
                    comp_length_val = this.cdf_for_address.post_code_length;
                }

                this.error_msg.push({
                    id: 'property_owner_post',
                    message: 'Please check the entered company post code, must be ' + comp_length_val + ' characters.',
                });
            }

            // submit form if no error
            if (this.error_msg.length <= 0) {
                this.save_company_btn_loading = true;
                this.loading_setting = true;

                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('property_owner_name', this.property_owner_name);
                formData.append('property_owner_code', this.property_owner_code);
                formData.append('property_owner_address1', this.property_owner_address1);
                formData.append('property_owner_address2', this.property_owner_address2);
                formData.append('property_owner_post', this.property_owner_post);
                formData.append('property_owner_email', this.property_owner_email);
                formData.append('property_owner_country_list', this.property_owner_country_list);
                formData.append('property_owner_state_list', this.property_owner_state_list);
                formData.append('property_owner_suburb', this.property_owner_suburb);
                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/company-sub-ledger';

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        this.$noty.success('Company Added.');
                        this.fetchCompanyList(this.property_owner_code);

                        var dis = this;
                        var code = this.property_owner_code;
                        setTimeout(function () {
                            dis.setCompanycode(code);
                            dis.company_code = code;
                        }, 2000);

                        this.resetCompanyField();
                        this.modalCompany = false;
                        this.loading_setting = false;
                        this.save_company_btn_loading = false;
                    } else {
                        this.$noty.error(response.data.validation_errors);
                        this.loading_setting = false;
                        this.save_company_btn_loading = false;
                    }
                });

                /*axios.post(apiUrl, formData).then(response => {
                        this.status = response.data.status;
                        if (this.status == 'success') {

                            this.$noty.success('Company Added.');
                            this.fetchCompanyList( this.property_owner_code );


                            var dis = this;
                            var code = this.property_owner_code;
                            setTimeout(function()
                            {
                                dis.setCompanycode( code );
                            },2000);

                            this.resetCompanyField()
                            this.modalCompany = false;
                            this.loading_setting = false;

                        } else {
                            this.$noty.error(response.data.validation_errors);
                            this.loading_setting = false;
                        }
                    });*/
            }
        },
        saveSubLedger: function () {
            let validate = true;
            if (this.sub_ledger_code == '') {
                this.$noty.error('You have not specified a sub ledger code.');
                validate = false;
            }

            // Validate if Sub Ledger Code has special character
            if (this.verifyCode(this.sub_ledger_code)) {
                this.$noty.error('Please use only alphanumeric characters for the sub ledger code.');
                validate = false;
            }

            if (this.sub_ledger_name == '') {
                this.$noty.error('You have not specified a sub ledger name.');
                validate = false;
            }

            if (this.sub_ledger_type == '' || this.sub_ledger_type == null) {
                this.$noty.error('You have not specified a sub ledger type.');
                validate = false;
            }
            if (this.company_code == '' || this.company_code == null) {
                this.$noty.error('You have not specified a sub ledger company.');
                validate = false;
            }

            /*if (this.category == 'BOND' || this.category == 'OTHER') {
                    if (this.sub_ledger_owner == '' || this.sub_ledger_owner == null) {
                        this.$noty.error('You have not specified a sub ledger owner company.');
                        validate = false;
                    }
                }*/

            // submit form if no error
            if (validate) {
                this.save_subledger_btn_loading = true;
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('form_mode', '1');
                formData.append('property_code', this.selected_property_code);
                formData.append('sub_ledger_code', this.sub_ledger_code.toUpperCase().trim());
                formData.append('sub_ledger_name', this.sub_ledger_name);
                formData.append('sub_ledger_location', this.sub_ledger_location);
                formData.append('sub_ledger_type', this.sub_ledger_type);
                formData.append('company_code', this.company_code);
                formData.append('no_load', true);
                if (this.category == 'BOND' || this.category == 'OTHER') {
                    formData.append('sub_ledger_owner', this.sub_ledger_owner);
                }

                //update or create sub ledger details
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/update-or-create-sub-ledger';

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        // if (this.form_mode == 1) {
                        this.$noty.success('Sub ledger added.');
                        // } else
                        //     this.$noty.success('Sub ledger updated.');

                        // reset fields
                        this.sub_ledger_code = '';
                        this.sub_ledger_name = '';
                        this.sub_ledger_location = '';
                        this.sub_ledger_type_code = '';
                        if (this.company_code) this.$refs.refCompanyCode.select_val = '';
                        this.company_code = '';
                        this.modalAddSubLedger = false;
                        this.loadSubLedgers();
                        this.loading_setting = false;
                        this.save_subledger_btn_loading = false;
                    } else {
                        this.$noty.error('The sub ledger code you entered already exists for this ledger');
                        this.loading_setting = false;
                        this.save_subledger_btn_loading = false;
                    }
                });

                /*axios.post(apiUrl, formData).then(response => {
                        this.status = response.data.status;
                        if (this.status == 'success') {
                            // if (this.form_mode == 1) {
                                    this.$noty.success('Sub ledger added.');
                            // } else
                            //     this.$noty.success('Sub ledger updated.');

                            // reset fields
                            this.sub_ledger_code = '';
                            this.sub_ledger_name = '';
                            this.sub_ledger_location = '';
                            this.sub_ledger_type_code = '';
                            if(this.company_code) this.$refs.refCompanyCode.select_val = '';
                            this.company_code = '';
                            this.modalAddSubLedger = false;
                            this.loadSubLedgers();
                            this.loading_setting = false;
                        } else {
                            this.$noty.error('The sub ledger code you entered already exists for this ledger');
                            this.loading_setting = false;
                        }
                    });*/
            }
        },
        updateSubLedger: function (index) {
            let validate = true;
            if (this.sub_ledgers[index].sub_ledger_code == '') {
                this.$noty.error('You have not specified a sub ledger code.');
                validate = false;
            }

            // Validate if Sub Ledger Code has special character
            if (this.verifyCode(this.sub_ledgers[index].sub_ledger_code)) {
                this.$noty.error('Please use only alphanumeric characters for the sub ledger code.');
                validate = false;
            }

            if (this.sub_ledgers[index].sub_ledger_name == '') {
                this.$noty.error('You have not specified a sub ledger name.');
                validate = false;
            }

            if (
                this.sub_ledgers[index].sub_ledger_type_code == '' ||
                this.sub_ledgers[index].sub_ledger_type_code == null
            ) {
                this.$noty.error('You have not specified a sub ledger type.');
                validate = false;
            }

            if (this.sub_ledgers[index].debtor_code == '' || this.sub_ledgers[index].debtor_code == null) {
                this.$noty.error('You have not specified a sub ledger company.');
                validate = false;
            }

            // submit form if no error
            if (validate) {
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('user_type', this.user_type);

                formData.append('form_mode', '0');
                formData.append('property_code', this.sub_ledgers[index].ledger_code);
                formData.append('sub_ledger_code', this.sub_ledgers[index].sub_ledger_code);

                formData.append('sub_ledger_name', this.sub_ledgers[index].sub_ledger_name);
                formData.append('sub_ledger_location', this.sub_ledgers[index].sub_ledger_location);
                formData.append('sub_ledger_type', this.sub_ledgers[index].sub_ledger_type_code);
                formData.append('company_code', this.sub_ledgers[index].debtor_code);
                formData.append('company_code_old', this.sub_ledgers[index].debtor_code_old);
                formData.append('no_load', true);

                //update or create sub ledger details
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/update-or-create-sub-ledger';

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        if (response.data.with_warning)
                            this.$noty.success(
                                'Sub-Ledger updated. Company has also been updated but Sub-ledger has record already.',
                            );
                        else this.$noty.success('Sub-Ledger updated.');

                        this.loading_setting = false;
                    } else {
                        this.loading_setting = false;
                    }
                });

                /*axios.post(apiUrl, formData).then(response => {
                        this.status = response.data.status;
                        if (this.status == 'success') {
                        
                            if(response.data.warning)
                                this.$noty.success('Sub-Ledger updated. Company has also been updated but Sub-ledger has record already.');
                            else 
                                this.$noty.success('Sub-Ledger updated.');

                            this.loading_setting = false;
                        } else {
                            this.loading_setting = false;
                        }
                    });*/
            }
        },

        verifyCode: function (inputString) {
            var newStr = inputString.replace(/[^a-zA-Z0-9-]/g, '');
            return newStr.length != inputString.length;
        },

        initialLoadSubLedgers: function () {
            this.checkCategoryType();
            this.loadSubLedgers();
            this.loadCountryDefaults();
        },

        loadSubLedgers: function () {
            if (this.selected_property_code == '') return true;

            this.loading_setting = true;

            // reset error
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);
            formData.append('property_code', this.selected_property_code);

            // get ledger details by code
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-sub-ledgers';

            this.$api.post(apiUrl, formData).then((response) => {
                this.sub_ledgers = this.convertToArrayOfObjects(response.data);

                if (typeof response.data[0] != 'undefined') {
                    this.total_balance = response.data[0].total_balance;
                } else {
                    this.total_balance = 0;
                }

                this.loading_setting = false;
            });

            /*axios.post(apiUrl, formData).then(response => {
                    this.sub_ledgers = response.data;

                    if(  typeof response.data[0] != "undefined" ){
                        this.total_balance = response.data[0].total_balance;
                    }
                    else{
                        this.total_balance = 0;
                    }

                    this.loading_setting = false;
                });*/
        },

        showSubLedgerBalances: function (subLedger) {
            var sub_ledger_items = [];

            if (subLedger && subLedger != '') {
                $.each(this.sub_ledgers, function (item, value) {
                    if (subLedger == value.sub_ledger_code) {
                        var sub_ledger_item = {};
                        sub_ledger_item['ledger_code'] = value.ledger_code;
                        sub_ledger_item['sub_ledger_code'] = value.sub_ledger_code;
                        sub_ledger_item['transaction'] = value.transaction;
                        sub_ledger_item['debtor_code'] = value.debtor_code;

                        sub_ledger_items.push(sub_ledger_item);
                    }
                });
            } else {
                $.each(this.sub_ledgers, function (item, value) {
                    var sub_ledger_item = {};
                    sub_ledger_item['ledger_code'] = value.ledger_code;
                    sub_ledger_item['sub_ledger_code'] = value.sub_ledger_code;
                    sub_ledger_item['transaction'] = value.transaction;
                    sub_ledger_item['debtor_code'] = value.debtor_code;

                    sub_ledger_items.push(sub_ledger_item);
                });
            }

            if (subLedger && subLedger != '') {
                this.showLoadingText(subLedger);
            } else {
                this.showLoadingText();
            }

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);
            formData.append('sub_ledger_items', JSON.stringify(sub_ledger_items));

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-sub-ledgers-balances';

            this.$api.post(apiUrl, formData).then((response) => {
                var balances = response.data;
                var sub_ledger_balances = Object.values(balances);
                var updated_sub_ledgers = [];

                if (subLedger && subLedger != '') {
                    $.each(this.sub_ledgers, function (item, value) {
                        if (value.sub_ledger_code == subLedger) {
                            value['balance'] = sub_ledger_balances[0][value.sub_ledger_code]['balance'];
                            value['transaction_ap'] = sub_ledger_balances[0][value.sub_ledger_code]['transaction_ap'];
                            value['outstanding_ap'] = sub_ledger_balances[0][value.sub_ledger_code]['outstanding_ap'];
                            value['transaction_ar'] = sub_ledger_balances[0][value.sub_ledger_code]['transaction_ar'];
                            value['outstanding_ar'] = sub_ledger_balances[0][value.sub_ledger_code]['outstanding_ar'];
                        } else {
                            value['balance'] = value.balance;
                            value['transaction_ap'] = value.transaction_ap;
                            value['outstanding_ap'] = value.outstanding_ap;
                            value['transaction_ar'] = value.transaction_ar;
                            value['outstanding_ar'] = value.outstanding_ar;
                        }

                        updated_sub_ledgers.push(value);
                    });
                } else {
                    $.each(this.sub_ledgers, function (item, value) {
                        value['balance'] = sub_ledger_balances[0][value.sub_ledger_code]['balance'];
                        value['transaction_ap'] = sub_ledger_balances[0][value.sub_ledger_code]['transaction_ap'];
                        value['outstanding_ap'] = sub_ledger_balances[0][value.sub_ledger_code]['outstanding_ap'];
                        value['transaction_ar'] = sub_ledger_balances[0][value.sub_ledger_code]['transaction_ar'];
                        value['outstanding_ar'] = sub_ledger_balances[0][value.sub_ledger_code]['outstanding_ar'];
                        updated_sub_ledgers.push(value);
                    });

                    /*if( typeof sub_ledger_balances[0] != "undefined" ){
                            this.total_balance = sub_ledger_balances[0].total_balance;
                        }
                        else{
                            this.total_balance = 0;
                        }*/
                }

                this.sub_ledgers = updated_sub_ledgers;
            });

            /*axios.post(apiUrl, formData).then(response => {
                    var balances = response.data;
                    var sub_ledger_balances = Object.values(balances);
                    var updated_sub_ledgers = [];

                    $.each(this.sub_ledgers, function(item, value) {
                        console.log(sub_ledger_balances[0]);
                        value['balance'] = sub_ledger_balances[0][value.sub_ledger_code]['balance'];
                        value['transaction_ap'] = sub_ledger_balances[0][value.sub_ledger_code]['transaction_ap'];
                        value['outstanding_ap'] = sub_ledger_balances[0][value.sub_ledger_code]['outstanding_ap'];
                        value['transaction_ar'] = sub_ledger_balances[0][value.sub_ledger_code]['transaction_ar'];
                        value['outstanding_ar'] = sub_ledger_balances[0][value.sub_ledger_code]['outstanding_ar'];
                        updated_sub_ledgers.push(value);
                    });

                    this.sub_ledgers = updated_sub_ledgers;

                    if(  typeof sub_ledger_balances[0] != "undefined" ){
                        this.total_balance = sub_ledger_balances[0].total_balance;
                    }
                    else{
                        this.total_balance = 0;
                    }
                });*/
        },

        showLoadingText: function (subLedger) {
            var updated_sub_ledgers = [];

            if (subLedger && subLedger != '') {
                $.each(this.sub_ledgers, function (item, value) {
                    if (subLedger == value.sub_ledger_code) {
                        value['update_balances_btn_label'] = 'Loading...';
                    } else {
                        value['update_balances_btn_label'] = value.update_balances_btn_label;
                    }

                    updated_sub_ledgers.push(value);
                });
            } else {
                $.each(this.sub_ledgers, function (item, value) {
                    value['balance'] = 'Loading...';
                    value['outstanding_ap'] = 'Loading...';
                    value['outstanding_ar'] = 'Loading...';
                    updated_sub_ledgers.push(value);
                });
            }

            this.sub_ledgers = updated_sub_ledgers;
        },

        showModalBalance: function (subData) {
            this.modalBalanceBreakdown = true;

            let netTotalBalance = 0;
            let gstTotalBalance = 0;
            let amtTotalBalance = 0;
            $.each(subData.transaction, function (item, value) {
                netTotalBalance += parseFloat(value.netAmount);
                gstTotalBalance += parseFloat(value.gstAmount);
                amtTotalBalance += parseFloat(value.totalAmount);
            });
            this.totalBalanceNet = netTotalBalance;
            this.totalBalanceGST = gstTotalBalance;
            this.totalBalanceAmt = amtTotalBalance;

            this.breakdownTransaciton = subData.transaction;
        },

        showModalAP: function (subData) {
            this.modalAPBreakdown = true;

            let netTotalBalance = 0;
            let gstTotalBalance = 0;
            let amtTotalBalance = 0;
            $.each(subData.transaction_ap, function (item, value) {
                netTotalBalance += parseFloat(value.netAmount);
                gstTotalBalance += parseFloat(value.gstAmount);
                amtTotalBalance += parseFloat(value.totalAmount);
            });
            this.totalBalanceNet = netTotalBalance;
            this.totalBalanceGST = gstTotalBalance;
            this.totalBalanceAmt = amtTotalBalance;

            this.breakdownTransaciton = subData.transaction_ap;
        },

        showModalAR: function (subData) {
            this.modalARBreakdown = true;

            let netTotalBalance = 0;
            let gstTotalBalance = 0;
            let amtTotalBalance = 0;
            $.each(subData.transaction_ar, function (item, value) {
                netTotalBalance += parseFloat(value.netAmount);
                gstTotalBalance += parseFloat(value.gstAmount);
                amtTotalBalance += parseFloat(value.totalAmount);
            });
            this.totalBalanceNet = netTotalBalance;
            this.totalBalanceGST = gstTotalBalance;
            this.totalBalanceAmt = amtTotalBalance;

            this.breakdownTransaciton = subData.transaction_ar;
        },

        showModalReceiptDetails: function (subData) {
            this.modalReceiptDetails = true;
            let netTotalReceipt = 0;
            let gstTotalReceipt = 0;
            let amtTotalReceipt = 0;
            $.each(subData, function (item, value) {
                netTotalReceipt += parseFloat(value.netAmount);
                gstTotalReceipt += parseFloat(value.gstAmount);
                amtTotalReceipt += parseFloat(value.totalAmount);
            });
            this.totalReceiptNet = netTotalReceipt;
            this.totalReceiptGST = gstTotalReceipt;
            this.totalReceiptAmt = amtTotalReceipt;
            this.receiptDetails = subData;
        },

        showModalCompany() {
            this.modalCompany = true;
        },
        hideModalCompany() {
            this.resetCompanyField();
            if (this.$refs.refSuburb !== undefined) {
                this.$refs.refSuburb.select_val = null;
            }
            this.modalCompany = false;
        },
        resetCompanyField() {
            this.suburbOwnerFilteredList(this.default_state);
            this.stateOwnerFilteredList(this.default_country);
            this.property_owner_code = '';
            this.property_owner_name = '';
            this.property_owner_address1 = '';
            this.property_owner_address2 = '';
            this.property_owner_suburb = '';
            this.property_owner_state = this.default_state;
            this.property_owner_post = '';
            this.property_owner_country = this.default_country;
            this.property_owner_email = '';
            this.property_owner_state_list = this.default_state;
            this.property_owner_country_list = this.default_country;
            this.suburbOwnerValue = '';
        },
        computeBalance: function (previousBalance, newAmount) {
            let sum = 0;
            sum = parseFloat(previousBalance) + parseFloat(newAmount);
            return sum;
        },
        setCompanycode: function (code) {
            this.company_code = code;
        },
        formatAsCurrency: function (amt, dec) {
            amt = parseFloat(amt);
            if (amt) {
                dec = dec || 0;
                if (amt < 0) {
                    amt = amt * -1;
                    return (
                        this.country_defaults.currency_symbol +
                        ' (' +
                        amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,') +
                        ')'
                    );
                } else {
                    return (
                        this.country_defaults.currency_symbol +
                        ' ' +
                        amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,')
                    );
                }
            } else {
                return this.country_defaults.currency_symbol + ' 0.00';
            }
        },
        // actionLinks: function(amt, dec) {
        //     amt = parseFloat(amt);
        //     if(amt){
        //         dec = dec || 0
        //         if(amt < 0){
        //             amt = amt * -1
        //            return '$ ('+ amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,")+')'
        // 		}else{
        // 			return '$ ' + amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,")
        // 		}
        //     }else{
        //         return '$ 0.00'
        //     }
        // },
        suburbOwnerSelected(data) {
            if (this.$refs.refSuburb.selectedValues.length == 0) {
                if (data) {
                    this.property_owner_suburb = data.suburb;
                    this.property_owner_post = data.pcode;
                } else {
                    this.property_owner_suburb = this.property_owner_suburb;
                }
            } else {
                if (data) {
                    this.property_owner_suburb = data.suburb;
                    this.property_owner_post = data.pcode;
                }
            }
        },
        suburbOwnerFilteredList(stateVal) {
            this.suburbOwner_list_filtered = [];
            let filteredOwnerItem = [];
            $.each(this.agent_suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredOwnerItem = filteredOwnerItem.concat(value);
                }
            });
            this.suburbOwner_list_filtered = filteredOwnerItem;
        },
        stateOwnerFilteredList(country_code) {
            if (country_code == 'AU') this.post_code_length = 4;
            else if (country_code == 'US') this.post_code_length = 5;
            else if (country_code == 'SG') this.post_code_length = 6;
            else this.post_code_length = 8;

            if (this.property_owner_post.length > this.post_code_length)
                this.property_owner_post = this.property_owner_post.substring(0, this.post_code_length);

            var formData = new FormData();
            formData.append('countryCode', country_code);
            formData.append('no_load', true);

            this.$api.post('loadAPIStatesDropDownList', formData).then((response) => {
                this.stateOwner_list_filtered = response.data.stateList;
            });
        },
        showCompanyDetails(data) {
            this.modal_company_code = data.company_code;
            this.modal_company_name = data.company_name;
            this.modal_company_address = data.company_street;
            this.modal_company_suburb = data.company_city;
            this.modal_company_postcode = data.company_postcode;
            this.modal_company_state = data.company_state;
            this.modal_company_country = data.company_country;
            this.modal_company_email = data.company_email;
            this.modal_contact_details = data.contact_details;
            this.modalCompanyDetails = true;

            this.loadCountryDefaults(true, this.modal_company_country);

            if (this.modal_company_email && this.modal_company_email.includes(';')) {
                this.modal_company_emails = this.modal_company_email.split(';');
            }
        },

        showOwnerDetails(data) {
            this.modal_owner_code = data.company_code;
            this.modal_owner_name = data.company_name;
            this.modal_owner_address = data.company_street;
            this.modal_owner_suburb = data.company_city;
            this.modal_owner_postcode = data.company_postcode;
            this.modal_owner_state = data.company_state;
            this.modal_owner_country = data.company_country;
            this.modal_owner_email = data.company_email;
            this.modal_owner_details = data.contact_details;
            this.modalOwnerCompanyDetails = true;
        },

        updateSubLedgerDtlView: function (data) {
            this.update_ledger_code = data.ledger_code;
            this.update_sub_ledger_code = data.sub_ledger_code;
            this.update_sub_ledger_type_code = data.sub_ledger_type_code;
            this.update_sub_ledger_name = data.sub_ledger_name;
            if (data.sub_ledger_location == null) {
                this.update_sub_ledger_location = '';
            } else {
                this.update_sub_ledger_location = data.sub_ledger_location;
            }
            if (data.debtor_code == null) {
                this.update_debtor_code = '';
            } else {
                this.update_debtor_code = data.debtor_code;
            }
            this.update_debtor_code_old = data.debtor_code_old;
            this.update_debtor_details = data.company_details;
            this.update_balance = data.balance;
            this.update_sub_ledger_status = parseInt(data.sub_ledger_status);
            this.update_sub_ledger_owner = '';
            this.update_sub_ledger_crn = data.sub_ledger_crn;

            if (this.category.toLowerCase() == 'bond') {
                if (data.sub_ledger_owner == null || data.sub_ledger_owner == '') {
                    if (this.update_sub_ledger_owner) this.$refs.refUpdateSubLedgerOwner.select_val = '';
                    this.update_sub_ledger_owner = '';
                } else {
                    this.update_sub_ledger_owner = data.sub_ledger_owner;
                }
            }

            if (data.balance > 0) {
                this.disableInactive = true;
            } else {
                this.disableInactive = false;
            }

            if (data.transaction.length > 0) {
                this.companyReadonly = true;
                this.update_debtor = data.company_details.company_code + ' - ' + data.company_details.company_name;
            } else {
                this.companyReadonly = false;
                this.update_debtor = '';
            }

            if (this.update_debtor.length > 163) {
                this.subledger_owner_row_css = 'height: 60px !important;';
            }

            this.update_data_item = data;
            this.modalUpdateSubLedger = true;
            this.update_subledger = false;
            this.error_server_msg3 = [];
        },
        updateSubLedgerDtl: function (confirm) {
            let validate = true;
            this.error_msg = [];

            if (this.update_sub_ledger_name == '') {
                this.$noty.error('You have not specified a sub ledger name.');
                validate = false;
            }

            if (this.update_sub_ledger_type_code == '' || this.update_sub_ledger_type_code == null) {
                this.$noty.error('You have not specified a sub ledger type.');
                validate = false;
            }

            if (this.update_debtor_code == '' || this.update_debtor_code == null) {
                this.$noty.error('You have not specified a sub ledger company.');
                validate = false;
            }

            // If the status is inactive, do not validate CRN
            // Active: 0, Inactive: 1
            if (this.update_sub_ledger_status == 0) {
                if (this.update_sub_ledger_crn == '' || this.update_sub_ledger_crn == null) {
                    this.$noty.error('You have not specified a sub ledger CRN.');
                    this.error_msg.push({ id: 'sub_ledger_crn', message: 'You have not specified a sub ledger CRN.' });
                    validate = false;
                }
            }

            /*if (this.category == 'BOND' || this.category == 'OTHER') {
                    if (this.update_sub_ledger_owner == '' || this.update_sub_ledger_owner == null) {
                        this.$noty.error('You have not specified a sub ledger owner company.');
                        validate = false;
                    }
                }*/
            // submit form if no error
            if (validate) {
                this.loading_setting = true;

                if (confirm !== undefined && confirm == true) {
                    this.crn_update_loading = true;
                } else {
                    this.update_subledger_loading = true;
                }

                this.error_msg = [];
                this.error_server_msg3 = [];

                var formData = new FormData();
                formData.append('user_type', this.user_type);

                formData.append('form_mode', '0');
                formData.append('property_code', this.update_ledger_code);
                formData.append('sub_ledger_code', this.update_sub_ledger_code);

                formData.append('sub_ledger_name', this.update_sub_ledger_name);
                formData.append('sub_ledger_location', this.update_sub_ledger_location);
                formData.append('sub_ledger_type', this.update_sub_ledger_type_code);
                formData.append('company_code', this.update_debtor_code);
                formData.append('company_code_old', this.update_debtor_code_old);
                formData.append('sub_ledger_status', this.update_sub_ledger_status);
                formData.append('sub_ledger_owner', this.update_sub_ledger_owner);
                formData.append('sub_ledger_crn', this.update_sub_ledger_crn);
                formData.append('version', '2.0');
                formData.append('no_load', true);

                if (this.subledger_crn_value == '' || this.update_sub_ledger_crn != this.subledger_crn_value) {
                } else {
                    if (this.confirm_crn_assignment) {
                        formData.append('resubmit_crn', true);
                    }
                }

                //update or create sub ledger details
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/update-or-create-sub-ledger';

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    this.error_msg = [];
                    this.error_server_msg3 = [];

                    if (this.status == 'success') {
                        if (response.data.crn_warning == 1) {
                            this.confirm_crn_assignment = true;
                            this.confirm_crn_message = response.data.crn_warning_message;
                            this.subledger_crn_value = this.update_sub_ledger_crn;
                            this.loading_setting = false;
                        } else {
                            this.confirm_crn_assignment = false;
                            this.confirm_crn_message = '';

                            this.loadSubLedgers();
                            this.modalUpdateSubLedger = false;
                            if (response.data.with_warning)
                                this.$noty.success(
                                    'Sub-Ledger updated. Company has also been updated but Sub-ledger has record already.',
                                );
                            else this.$noty.success('Sub-Ledger updated.');

                            this.loading_setting = false;
                            this.closeEditing();
                        }
                    } else {
                        this.loading_setting = false;
                        //this.error_msg.push({id: 'sub_ledger_crn', message: response.data.validation_errors});
                        this.error_server_msg3 = response.data.validation_errors;
                    }

                    if (confirm !== undefined && confirm == true) {
                        this.crn_update_loading = false;
                    } else {
                        this.update_subledger_loading = false;
                    }
                });

                /*axios.post(apiUrl, formData).then(response => {
                        this.status = response.data.status;
                        if (this.status == 'success') {
                            this.loadSubLedgers();
                            this.modalUpdateSubLedger = false;
                            if(response.data.warning)
                                this.$noty.success('Sub-Ledger updated. Company has also been updated but Sub-ledger has record already.');
                            else
                                this.$noty.success('Sub-Ledger updated.');

                                this.loading_setting = false;
                        } else {
                            this.loading_setting = false;
                        }
                    });*/
            }
        },
        dontAssignCRN: function () {
            this.confirm_crn_assignment = false;
            this.confirm_crn_message = '';
            this.subledger_crn_value = '';
            this.crn_update_loading = false;
            this.update_subledger_loading = false;
        },
        closeUpdateSubledgerDialog: function () {
            this.modalUpdateSubLedger = false;
            this.confirm_crn_assignment = false;
            this.confirm_crn_message = '';
            this.subledger_crn_value = '';
            this.crn_update_loading = false;
            this.update_subledger_loading = false;
        },

        //----- Make Subledger Inactive - START
        makeInactiveSubledger: function (data) {
            let validate = true;
            this.subledger_property_id = data.ledger_code;
            this.subledger_lease_id = data.sub_ledger_code;
            // if (this.update_sub_ledger_name == '') {
            //   this.$noty.error('You have not specified a sub ledger name.');
            //   validate = false;
            // }
            //
            // if (this.update_sub_ledger_type_code == '' || this.update_sub_ledger_type_code == null) {
            //   this.$noty.error('You have not specified a sub ledger type.');
            //   validate = false;
            // }
            //
            // if (this.update_debtor_code == '' || this.update_debtor_code == null) {
            //   this.$noty.error('You have not specified a sub ledger company.');
            //   validate = false;
            // }

            // submit form if no error
            if (validate) {
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('user_type', this.user_type);

                formData.append('form_mode', '0');
                formData.append('property_code', data.ledger_code);
                formData.append('sub_ledger_code', data.sub_ledger_code);

                formData.append('sub_ledger_status', 1);
                formData.append('no_load', true);

                //update or create sub ledger details
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/make-inactive-sub-ledger';

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        this.loadSubLedgers();
                        this.modalUpdateSubLedger = false;
                        if (response.data.with_warning)
                            //this.$noty.success('Sub-Ledger updated. Company has also been updated but Sub-ledger has record already.');
                            this.$noty.success('Sub-Ledger made inactive.');
                        else this.$noty.success('Sub-Ledger made inactive.');

                        this.loading_setting = false;
                    } else {
                        this.loading_setting = false;
                    }
                });

                /*axios.post(apiUrl, formData).then(response => {
                        this.status = response.data.status;
                        if (this.status == 'success') {
                            this.loadSubLedgers();
                            this.modalUpdateSubLedger = false;
                            if(response.data.warning)
                                    //this.$noty.success('Sub-Ledger updated. Company has also been updated but Sub-ledger has record already.');
                                this.$noty.success('Sub-Ledger made inactive.');
                            else
                                this.$noty.success('Sub-Ledger made inactive.');

                            this.loading_setting = false;
                        } else {
                            this.loading_setting = false;
                        }
                    });*/
            }
        },
        //----- Make Subledger Inactive - END

        //----- Delete Subledger - START
        deleteSubledger: function () {
            let data = this.item_for_deletion;
            let validate = true;
            this.subledger_property_id = data.ledger_code;
            this.subledger_lease_id = data.sub_ledger_code;

            // submit form if no error
            if (validate) {
                this.delete_subledger_btn_loading = true;
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('user_type', this.user_type);

                formData.append('form_mode', '0');
                formData.append('property_code', data.ledger_code);
                formData.append('sub_ledger_code', data.sub_ledger_code);
                formData.append('sub_ledger_name', data.sub_ledger_name);

                formData.append('sub_ledger_status', 0);
                formData.append('no_load', true);

                //Check for existing transactions first
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/check-subledger-transactions';

                this.$api.post(apiUrl, formData).then((response) => {
                    let ledger_data = response.data;
                    //console.log(ledger_data.has_transactions+"====="+ledger_data.transaction_counter);

                    if (ledger_data.has_transactions > 0) {
                        this.$noty.error('Deleting not allowed. Transactions were found under this sub-ledger.');
                        this.loading_setting = false;

                        this.resetDeleteDialog();
                    } else {
                        //Allow delete
                        // let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/make-inactive-sub-ledger';
                        let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/delete-sub-ledger';

                        this.$api.post(apiUrl2, formData).then((response) => {
                            let return_data = response.data;
                            if (return_data.status == 'success') {
                                this.modalUpdateSubLedger = false;
                                this.$noty.success('Sub-ledger Deleted.');
                                this.loadSubLedgers();
                                this.loading_setting = false;
                                this.delete_subledger_btn_loading = false;
                                this.resetDeleteDialog();
                            } else {
                                this.$noty.error(response.data.validation_errors);
                                this.loading_setting = false;
                                this.delete_subledger_btn_loading = false;
                                this.resetDeleteDialog();
                            }

                            this.closeEditing();
                        });

                        /*axios.post(apiUrl2, formData).then(response => {
                                let return_data = response.data;
                                if(return_data.status == 'success'){
                                    this.modalUpdateSubLedger = false;
                                    this.$noty.success('Sub-ledger Deleted.');
                                    this.loadSubLedgers();
                                    this.loading_setting = false;
                                    this.resetDeleteDialog();
                                }else{
                                    this.$noty.error(response.data.validation_errors);
                                    this.loading_setting = false;
                                    this.resetDeleteDialog();
                                }
                            });*/
                    }
                });

                /*axios.post(apiUrl, formData).then(response => {
                        let ledger_data = response.data;
                        //console.log(ledger_data.has_transactions+"====="+ledger_data.transaction_counter);

                        if(ledger_data.has_transactions > 0){
                            this.$noty.error('Deleting not allowed. Transactions were found under this sub-ledger.');
                            this.loading_setting = false;

                            this.resetDeleteDialog();
                        }else{
                            //Allow delete
                            // let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/make-inactive-sub-ledger';
                            let apiUrl2 = this.cirrus8_api_url + 'api/sales-trust/ledger/delete-sub-ledger';
                            axios.post(apiUrl2, formData).then(response => {
                                let return_data = response.data;
                                if(return_data.status == 'success'){
                                    this.modalUpdateSubLedger = false;
                                    this.$noty.success('Sub-ledger Deleted.');
                                    this.loadSubLedgers();
                                    this.loading_setting = false;
                                    this.resetDeleteDialog();
                                }else{
                                    this.$noty.error(response.data.validation_errors);
                                    this.loading_setting = false;
                                    this.resetDeleteDialog();
                                }
                            });
                        }
                    });*/
            }
        },
        //----- Delete Subledger - END

        countActiveSubLedger: function () {
            if (this.selected_property_code == '') return true;

            this.add_sub_ledger_loading = true;

            var formData = new FormData();
            formData.append('property_code', this.selected_property_code);
            formData.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/sales-trust/ledger/count-active-sub-ledgers';

            this.$api.post(api_url, formData).then((response) => {
                let count = parseInt(response.data.subledger_count);

                /*Limit: 200 active sub-ledgers; anything beyond will require a new ledger*/
                if (count >= 200) {
                    this.$noty.error('Maximum number of active sub-ledgers has been reached.');
                } else {
                    if (this.sub_ledger_type) this.$refs.refSubLedgerType.select_val = '';
                    if (this.company_code) this.$refs.refCompanyCode.select_val = '';
                    if (this.sub_ledger_owner) this.$refs.refSubLedgerOwner.select_val = '';

                    this.sub_ledger_code = '';
                    this.sub_ledger_name = '';
                    this.sub_ledger_location = '';
                    this.sub_ledger_type = '';
                    this.company_code = '';

                    this.modalAddSubLedger = true;
                }

                this.add_sub_ledger_loading = false;
            });
        },

        showAddSubLedger: function () {
            this.countActiveSubLedger();
            /*if(this.sub_ledger_type) this.$refs.refSubLedgerType.select_val = '';
                if(this.company_code) this.$refs.refCompanyCode.select_val = '';
                if(this.sub_ledger_owner) this.$refs.refSubLedgerOwner.select_val = '';


                this.sub_ledger_code = '';
                this.sub_ledger_name = '';
                this.sub_ledger_location = '';
                this.sub_ledger_type = '';
                this.company_code = '';


                this.modalAddSubLedger = true;*/
        },

        updateDebtorDetails: function (companyCode) {
            var formData = new FormData();
            /*formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);*/

            formData.append('companyCode', companyCode);
            formData.append('no_load', true);

            // get ledger details by code
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-company-detail';
            this.$api.post(apiUrl, formData).then((response) => {
                this.update_debtor_details = response.data;
            });
        },

        fetchSubledgerPeriods: function (data) {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.subledger_property_id);
            formData.append('lease_code', this.subledger_lease_id);
            formData.append('company_id', this.subledger_company_id);
            formData.append('no_load', true);

            // get ledger details by code
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-sub-ledgers-periods';

            this.$api.post(apiUrl, formData).then((response) => {
                this.period_from_list = response.data.period_from_list;
                this.period_to_list = response.data.period_to_list;

                this.period_from = response.data.period_from;
                this.period_to = response.data.period_to;
            });

            /*axios.post(apiUrl, formData).then(response => {
                    this.period_from_list = response.data.period_from_list ;
                    this.period_to_list = response.data.period_to_list ;

                    this.period_from = response.data.period_from;
                    this.period_to = response.data.period_to;

                });*/
        },
        showGenerateModal: function (data) {
            this.report_option = 0;
            this.subledger_property_id = data.ledger_code;
            this.subledger_lease_id = data.sub_ledger_code;
            this.subledger_company_id = data.debtor_code;
            this.fetchSubledgerPeriods(data);
            this.modalGenerateReport = true;
            this.modal_generate_title = data.sub_ledger_code + ' - ' + data.sub_ledger_name;
        },
        clearGenerateModalData: function () {
            this.include_ap_invoices = false;
            if (this.period_from) this.$refs.refPeriodFrom.select_val = '';
            this.period_from = '';
            if (this.period_to) this.$refs.refPeriodTo.select_val = '';
            this.period_to = '';
            this.period_from_list = [];
            this.period_to_list = [];
        },
        closeGenerateModal: function () {
            this.clearGenerateModalData();
            this.modalGenerateReport = false;
        },

        downloadSubLedgerReport: function () {
            let validate = true;
            this.button_loading_setting = true;
            if (this.period_from == '' || this.period_from == null || typeof this.period_from == 'undefined') {
                this.$noty.error('Please select a report period from date.');
                validate = false;
            }
            if (this.period_to == '' || this.period_to == null || typeof this.period_to == 'undefined') {
                this.$noty.error('Please select a report period to date.');
                validate = false;
            }
            if (validate) {
                this.subledger_filename = 'Trust_Account_Statement_' + $.now();
                let url = '?module=managementReports&command=ownerReport&action=generateSubledger';
                var form_data = new FormData();
                form_data.append('property', this.subledger_property_id);
                form_data.append('periodFrom', this.period_from);
                form_data.append('periodTo', this.period_to);
                form_data.append('logo', 1);
                form_data.append('format', 'pdf');
                form_data.append('filename_description', this.subledger_filename);
                form_data.append('description', this.trust_account_label + ' Statement');
                form_data.append('leaseID', this.subledger_lease_id);
                form_data.append('companyID', this.subledger_company_id);
                form_data.append('sub_ledger_ps', this.sub_ledger_ps);
                form_data.append('report_type', this.report_option);

                axios.post(url, form_data).then((response) => {
                    this.button_loading_setting = false;
                    document.location.href = 'download.php?fileID=' + response.data;
                    this.$noty.success('Report successfully downloaded.');
                });
            } else {
                this.button_loading_setting = false;
            }
        },

        sendEmail: function () {
            let validate = true;
            this.button_loading_setting = true;
            if (this.period_from == '' || this.period_from == null || typeof this.period_from == 'undefined') {
                this.$noty.error('Please select a report period from date.');
                validate = false;
            }
            if (this.period_to == '' || this.period_to == null || typeof this.period_to == 'undefined') {
                this.$noty.error('Please select a report period to date.');
                validate = false;
            }
            if (validate) {
                this.subledger_filename = 'Trust_Account_Statement_' + $.now();
                let url = '?module=managementReports&command=ownerReport&action=generateSubledger';
                var form_data = new FormData();
                form_data.append('property', this.subledger_property_id);
                form_data.append('periodFrom', this.period_from);
                form_data.append('periodTo', this.period_to);
                form_data.append('logo', 1);
                form_data.append('format', 'pdf');
                form_data.append('filename_description', this.subledger_filename);
                form_data.append('description', this.trust_account_label + ' Statement');
                form_data.append('leaseID', this.subledger_lease_id);
                form_data.append('companyID', this.subledger_company_id);
                axios.post(url, form_data).then((response) => {
                    if (response.data) {
                        var formData = new FormData();
                        formData.append('user_type', this.user_type);
                        formData.append('property_code', this.subledger_property_id);
                        formData.append('lease_code', this.subledger_lease_id);
                        formData.append('company_id', this.subledger_company_id);
                        formData.append('filename', this.subledger_filename);
                        if (this.include_ap_invoices) formData.append('include_ap', this.include_ap_invoices);
                        formData.append('periodFrom', this.period_from);
                        formData.append('periodTo', this.period_to);
                        // send email
                        let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/send-email';
                        this.$api.post(apiUrl, formData).then((response) => {
                            if (response.data.status == 'error') {
                                this.button_loading_setting = false;
                                this.$noty.error('Email failed. There was no email assigned.');
                            } else {
                                this.button_loading_setting = false;
                                this.$noty.success('Report successfully emailed.');
                            }
                        });
                    } else {
                        this.button_loading_setting = false;
                        this.$noty.error('Report was not send.');
                    }
                });
            } else {
                this.button_loading_setting = false;
            }
        },

        downloadInvoice: function (rawData) {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('invoice_file', rawData.invoiceFile);
            // send email
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/download-invoice';

            this.$api.post(apiUrl, formData).then((response) => {
                if (response.data.status == 'error') {
                    this.$noty.error(response.data.error_message);
                } else {
                    document.location.href = 'download.php?fileID=' + response.data.fileID;
                    this.$noty.success('Invoice downloaded.');
                }
            });

            /*axios.post(apiUrl, formData).then(response => {
                    if(response.data.status == 'error'){
                        this.$noty.error(response.data.error_message);
                    }else{
                        document.location.href = "download.php?fileID=" + response.data.fileID;
                        this.$noty.success('Invoice downloaded.');
                    }

                });*/
        },

        showConfirmationDialog: function (item) {
            this.item_for_deletion = item;
            this.warning_message = 'This sub-ledger will be deleted. Would you like to proceed?';
            this.dialogConfirmation = true;
        },

        resetDeleteDialog: function () {
            this.item_for_deletion = [];
            this.dialogConfirmation = false;
        },

        openLedgerChangelogModal: function () {
            this.show_change_log_modal = true;
        },

        closeEditing: function (data) {
            this.update_subledger = false;
        },
        displayCompanyLabel: function (code) {
            let find = this.dd_company_list.find((x) => x.value === code);
            if (find) {
                return find.label;
            }
        },
        displayOwnerLabel: function (code) {
            let find = this.dd_owner_company_list.find((x) => x.field_key === code);
            if (find) {
                return find.field_value;
            }
        },
        loadCountryDefaults: function (for_company_address = false, country_code = '', no_load = false) {
            let form_data = new FormData();
            let api_url = 'country_defaults/load';

            if (for_company_address) {
                if (country_code) form_data.append('country', country_code);

                if (no_load) {
                    form_data.append('no_load', true);
                }
            }

            this.$admin.post(api_url, form_data).then((response) => {
                if (for_company_address) {
                    this.cdf_for_address = response.data.default;
                } else {
                    this.country_defaults = response.data.default;
                    this.trust_account_label = this.properCase(this.country_defaults.trust_account);
                    this.suburb_label = this.properCase(this.country_defaults.suburb);
                }
            });
        },
    },

    watch: {
        report_option: function () {
            if (this.report_option != 0) {
                let copy = [];
                let default_sp = null;
                let dis = this;
                for (var i = 0; i < this.dd_sub_ledger_list_copy.length; i++) {
                    if (this.dd_sub_ledger_list_copy[i].value != this.subledger_lease_id) {
                        copy.push(this.dd_sub_ledger_list_copy[i]);
                        if (default_sp == null) {
                            if (dis.report_option == 2 && dis.dd_sub_ledger_list_copy[i].type == 'SELLER')
                                default_sp = dis.dd_sub_ledger_list_copy[i].value;
                            else if (dis.report_option == 1 && dis.dd_sub_ledger_list_copy[i].type == 'PURCHASER')
                                default_sp = dis.dd_sub_ledger_list_copy[i].value;
                        }
                    }
                }

                this.dd_sub_ledger_list = copy;
                this.sub_ledger_ps = default_sp;
                if (default_sp == null && typeof this.$refs.sub_ledger_ref != 'undefined')
                    this.$refs.sub_ledger_ref.select_val = '';
            }
        },
        selected_property_code: function () {
            this.initialLoadSubLedgers();
            this.edit_form = false;
            this.update_subledger = false;
        },
        reloadComponents: function () {
            this.initialLoadSubLedgers();
        },
        sub_ledger_code: function () {
            this.sub_ledger_code = this.sub_ledger_code.toUpperCase().trim();
            if (this.sub_ledger_code.length > 10) this.sub_ledger_code = this.sub_ledger_code.substring(0, 10);

            this.update_subledger = false;
        },
        property_owner_code: function () {
            this.property_owner_code = this.property_owner_code.toUpperCase().trim();
            if (this.property_owner_code.length > 10)
                this.property_owner_code = this.property_owner_code.substring(0, 10);
        },
        property_owner_suburb: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchOwnerSuburb;
                this.property_owner_suburb = newVal;
            }
        },
        property_owner_country_list: function () {
            this.loadCountryDefaults(true, this.property_owner_country_list);
            this.property_owner_state_list = '';
        },
    },
    mixins: [global_mixins],
};
</script>
