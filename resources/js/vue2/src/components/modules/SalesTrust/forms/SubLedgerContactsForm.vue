<style>
.no-pad {
    padding: 0 !important;
}
.c8-datatable-custom {
    padding: 0 0 20px;
}
tr.v-data-table-header-row > th {
    background: #eaeaea !important;
}
tr.v-data-table-body > td {
    padding: 5px 16px !important;
}
.details-line ul {
    list-style-type: none;
    padding-left: 0px;
    margin: 0;
}
.details-line ul li span.details-line-index {
    float: left;
    width: 20px;
}
.c8-page #subledgerContactForm .page-form .v-chip.v-size--default {
    height: 32px !important;
}
.c8-page #subledgerContactForm .page-form .v-chip.v-size--default.primary {
    border-radius: 16px !important;
    color: #fff !important;
}
.error-message-div {
    margin: -10px 0 10px;
}
.v-alert__content hr.info {
    opacity: 0 !important;
}
.add-detail-container {
    padding: 10px 5px !important;
}
.details-container {
    padding: 0 5px !important;
}
.c8-page-table-row-header td {
    font-weight: 500;
}
.contact-primary {
    background: #f7ffe6;
}
.to-primary-buttons {
    width: 99px;
    margin: 0 2px;
}
.box-shadow {
    background: #f5f5f5;
    box-shadow:
        0 3px 1px -2px rgb(0 0 0 / 20%),
        0 2px 2px 0 rgb(0 0 0 / 14%),
        0 1px 5px 0 rgb(0 0 0 / 12%);
}
.theme--light.v-btn.v-btn--disabled.box-shadow {
    background: rgba(0, 0, 0, 0.12) !important;
}
</style>
<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div id="subledgerContactForm">
            <v-card
                id="contacts_header"
                class="section-toolbar"
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Contact Details</h6>
                    &nbsp;&nbsp;

                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        class="v-step-edit-button"
                        icon
                        @click="showContactModal('create')"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="!edit_form"
                        class="v-step-edit-button"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="edit_form"
                        class="v-step-save-1-button"
                        icon
                        @click="closeEditing()"
                    >
                        <v-icon
                            light
                            color="red"
                            >undo</v-icon
                        >
                    </v-btn>
                    <v-btn
                        x-small
                        class="v-step-refresh-button"
                        icon
                        @click="reloadContacts()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        icon
                        @click="showLogModal()"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div class="page-list">
                    <div class="c8-page-table">
                        <table
                            border="1"
                            style="border-style: solid"
                        >
                            <tbody>
                                <tr
                                    class="c8-page-table-row-header"
                                    v-if="contacts.length !== 0"
                                >
                                    <td style="width: 5px">&nbsp;&nbsp;</td>
                                    <td>#</td>
                                    <td>ID</td>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Details</td>
                                    <td
                                        class="text-right"
                                        style="width: 170px"
                                    >
                                        &nbsp;&nbsp;
                                    </td>
                                </tr>
                                <tr
                                    v-for="(row, index) in contacts"
                                    :key="index"
                                    v-bind:class="{ 'contact-primary': row.primary == 1 }"
                                    class="subledger-contact"
                                >
                                    <td></td>
                                    <td>
                                        <span>{{ index + 1 }}</span>
                                    </td>
                                    <td>
                                        <span>{{ formatID(row.serial) }}</span>
                                    </td>
                                    <td>
                                        <span>{{ row.name }}</span>
                                    </td>
                                    <td>{{ formatRole(row.role) }}</td>
                                    <td>
                                        <div
                                            v-for="(detail, j) in row.details"
                                            :key="j"
                                            class="details-line"
                                        >
                                            <ul>
                                                <li>
                                                    <span class="details-line-index"> {{ j + 1 }}. </span>
                                                    <span>
                                                        <strong>{{ formatDetail(detail.type) }}</strong>
                                                    </span>
                                                    <span> : </span>
                                                    <span>
                                                        <a
                                                            v-if="input_email.indexOf(formatDetail(detail.type)) !== -1"
                                                            :href="'mailto:' + detail.value"
                                                            >{{ detail.value }}</a
                                                        >
                                                        <a
                                                            v-else-if="input_phone.indexOf(detail.type) !== -1"
                                                            :href="'tel:' + detail.value.replace(/[\s\/]/g, '')"
                                                            >{{ detail.value }}</a
                                                        >
                                                        <span v-else>{{ detail.value }}</span>
                                                    </span>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                    <td class="text-right">
                                        <div v-if="edit_form">
                                            <v-btn
                                                x-small
                                                text
                                                @click="makePrimaryContact(row.id)"
                                                :disabled="row.primary == 1"
                                                class="box-shadow to-primary-buttons"
                                                v-bind:class="{ 'contact-primary-btn': row.primary == 1 }"
                                            >
                                                <span v-if="row.primary != 1">Make &nbsp;</span>Primary
                                            </v-btn>
                                            <v-icon
                                                x-small
                                                class="line-edit-button sbldgr-edit-btn"
                                                @click="updateContact(index)"
                                                >fas fa-edit</v-icon
                                            >
                                            <v-btn
                                                x-small
                                                color="red"
                                                icon
                                                @click="deleteContact(row.id)"
                                            >
                                                <v-icon>close</v-icon>
                                            </v-btn>
                                        </div>
                                    </td>
                                </tr>
                                <tr v-if="contacts.length === 0">
                                    <td
                                        colspan="7"
                                        class="text-center add-btn-container"
                                    >
                                        <v-btn
                                            depressed
                                            small
                                            color="success"
                                            @click="showContactModal('create')"
                                            class="sbldgr-add-btn"
                                            >Add Contact</v-btn
                                        >
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- CONTACT MODAL -->
        <v-dialog
            persistent
            top
            v-model="show_contact_modal"
            max-width="900"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card id="subledgerContactModal">
                <v-card-title class="headline">
                    <span>Contact Information</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="hideContactModal()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Name</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <div style="display: inline-block">
                                        <span>
                                            <v-text-field
                                                dense
                                                v-model="contact.name"
                                                :maxlength="40"
                                                :class="'cirrus-input-form-textbox'"
                                            ></v-text-field>
                                        </span>
                                    </div>
                                    <!-- <cirrus-input :size="'100'" v-model="contact.name" :edit_form="true" :error_msg="error_msg"></cirrus-input> -->
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        v-model="contact.role"
                                        :options="roles"
                                        openDirection="bottom"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-300"
                                        group-label="language"
                                        placeholder="Select a contact type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult">Oops! No elements found.</span>
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <!-- 
                            <v-row v-if="form_mode === 'update'" class="form-row">
                                <v-col xs="12" sm="2" md="2" class="form-label required">Primary</v-col>
                                <v-col xs="12" sm="10" md="10" class="form-input">
                                    <v-switch v-model="contact.primary" inset></v-switch>
                                </v-col>
                            </v-row> 
                            -->
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="text-right add-detail-container"
                                >
                                    <v-btn
                                        x-small
                                        text
                                        @click="addContactDetail()"
                                        class="box-shadow sbldgr-mdl-secondary-btn"
                                    >
                                        <v-icon>add</v-icon>
                                        Add Detail
                                    </v-btn>
                                </v-col>

                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="details-container"
                                >
                                    <v-simple-table
                                        fixed-header
                                        dense
                                        height="300px"
                                        class="c8-datatable-custom"
                                    >
                                        <template v-slot:default>
                                            <thead class="v-data-table-header">
                                                <tr class="v-data-table-header-row">
                                                    <th
                                                        class="text-left"
                                                        style="width: 20px"
                                                    ></th>
                                                    <th class="text-left">Type</th>
                                                    <th class="text-left">Details</th>
                                                    <th
                                                        class="text-left"
                                                        style="width: 62px"
                                                    ></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    v-for="(row, index) in contact.details"
                                                    :key="index"
                                                    class="v-data-table-body"
                                                >
                                                    <td>{{ index + 1 }}</td>
                                                    <td class="text-left">
                                                        <multiselect
                                                            v-model="row.type"
                                                            openDirection="bottom"
                                                            :options="details"
                                                            :allowEmpty="false"
                                                            class="vue-select2 dropdown-left dropdown-300"
                                                            group-label="language"
                                                            placeholder="Select a phone type"
                                                            track-by="field_key"
                                                            label="field_value"
                                                            :show-labels="false"
                                                            ><span slot="noResult">Oops! No elements found.</span>
                                                        </multiselect>
                                                    </td>
                                                    <td class="text-left">
                                                        <cirrus-input
                                                            :size="'100'"
                                                            v-model="row.value"
                                                            :edit_form="true"
                                                            :error_msg="error_msg"
                                                        ></cirrus-input>
                                                    </td>
                                                    <td class="text-left">
                                                        <v-icon
                                                            v-if="contact.details.length > 1"
                                                            color="red"
                                                            size="20"
                                                            @click="removeContactDetail(index)"
                                                            >close</v-icon
                                                        >
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </template>
                                    </v-simple-table>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        :loading="update_btn_loading"
                        @click="submitContactData()"
                        color="success"
                        dark
                        small
                        depressed
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Submit
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- ACTIVITY LOG -->
        <v-dialog
            top
            v-model="show_change_log_modal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Activity Logs</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="show_change_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <activity-logs-component
                            v-if="show_change_log_modal"
                            :property_code="selected_property_code"
                            :lease_code="selected_lease_code"
                            :form_section="'contact'"
                        ></activity-logs-component>
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF ACTIVITY LOG -->

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            persistent
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon> WARNING
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click.prevent="deleteContactCancel()"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">Do you really want to delete this contact detail?</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        :loading="delete_btn_loading"
                        @click="deleteContactConfirm()"
                        >Yes</v-btn
                    >
                    <v-btn
                        depressed
                        tile
                        small
                        @click="deleteContactCancel()"
                        >No</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->
    </v-container>
</template>

<script>
import Vue from 'vue';
import vSelect from 'vue-select';
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import moment from 'moment';

const Swal = require('sweetalert2');
Vue.component('v-select', vSelect);

export default {
    props: {
        selected_property_code: { type: String, default: '' },
        selected_lease_code: { type: String, default: '' },
        reloadComponents: { type: Number, default: 0 },
    },
    data() {
        return {
            loading_setting: false,
            asset_domain: this.$assetDomain,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],

            show_contact_modal: false,

            roles: [],
            details: [],
            contacts: [],
            contact: {
                name: null,
                role: null,
                serial: null,
                details: [
                    {
                        id: null,
                        type: null,
                        value: null,
                    },
                ],
            },

            form_mode: 'create',
            deleted_ids: [],
            delete_id: null,

            dialogConfirmation: '',
            edit_form: false,

            search: '',
            show_change_log_modal: false,

            input_email: ['E-Mail'],
            input_phone: ['MOBILE', 'SERVICEMOB', 'LEASE MOB', 'SUPERMOB', 'SHOP'],
            delete_btn_loading: false,
            update_btn_loading: false,
        };
    },
    mounted() {
        this.loadContactsList();
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url']),
    },
    methods: {
        ...mapActions([]),
        ...mapMutations([]),
        // ...

        doubleClickForm() {
            if (!this.edit_form) {
                this.edit_form = true;
            }
        },

        loadContactsList() {
            this.loading_setting = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);
            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', this.selected_lease_code);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/get-contacts';

            this.$api.post(apiUrl, formData).then((response) => {
                this.roles = response.data.roles;
                this.details = response.data.details;
                this.contacts = response.data.contacts;

                this.loading_setting = false;
            });
        },
        closeEditing: function () {
            this.reloadContacts();
            this.edit_form = false;
        },
        showContactModal(mode) {
            this.form_mode = mode;
            this.show_contact_modal = true;
        },
        hideContactModal() {
            this.show_contact_modal = false;
            this.clearContactData();
        },
        addContactDetail() {
            let detail = {
                id: null,
                type: null,
                value: null,
            };

            this.contact.details.push(detail);
        },
        removeContactDetail(index) {
            let i = 0;
            let size = this.contact.details.length;
            let details = this.contact.details;

            if (size > 1) {
                var id = this.contact.details[index].id;
                if (id) {
                    this.deleted_ids.push(id);
                }

                details.splice(index, 1);

                details.forEach(function (item, x) {
                    i++;
                });

                if (i == size - 1) {
                    details.forEach(function (item, y) {
                        item.render = true;

                        setTimeout(function () {
                            item.render = false;
                        }, 1000);
                    });
                }
            }
        },
        submitContactData() {
            let validate = this.validateContactForm();

            if (validate) {
                this.update_btn_loading = true;

                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('property_code', this.selected_property_code);
                formData.append('lease_code', this.selected_lease_code);
                formData.append('contact', JSON.stringify(this.contact));
                formData.append('deleted', JSON.stringify(this.deleted_ids));
                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/' + this.form_mode + '-contact';

                this.$api.post(apiUrl, formData).then((response) => {
                    let status = response.data.status;
                    if (status == 'success') {
                        setTimeout(this.reloadContacts(), 5000);
                        this.hideContactModal();
                        this.$noty.success('Contact successfully ' + this.form_mode + 'd.');
                    } else {
                        this.$noty.error(response.data.error_message);
                    }

                    this.update_btn_loading = false;
                });
            }
        },
        validateContactForm() {
            let errors = [];

            if (!this.contact.name) {
                errors.push(['You have not entered a valid contact name.']);
            }

            if (!this.contact.role) {
                errors.push(['You have not entered a valid contact type.']);
            }

            this.contact.details.forEach(function (line, index) {
                if (!line.type) {
                    errors.push([index + 1 + '. You have not entered a valid phone type.']);
                }

                if (!line.value) {
                    errors.push([index + 1 + '. You have not entered a valid contact detail.']);
                }

                // if (!line.type && !line.value) {
                //     errors.push(["Complete contact details line " + (index + 1) + "."]);
                // }
            });

            if (errors.length === 0) {
                return true;
            } else {
                this.error_server_msg2 = errors;
                return false;
            }
        },
        clearContactData() {
            this.contact = {
                name: null,
                role: null,
                serial: null,
                details: [
                    {
                        type: null,
                        value: null,
                    },
                ],
            };

            this.deleted_ids = [];
            this.error_server_msg2 = [];
        },
        reloadContacts() {
            setTimeout(this.loadContactsList(), 5000);
        },
        formatID(id) {
            return String(id).padStart(6, '0');
        },
        formatRole(role) {
            let roleFind = this.roles.find((x) => x.fieldKey === role);
            if (roleFind) {
                return roleFind.fieldValue;
            }
        },
        formatDetail(detail) {
            let detailFind = this.details.find((x) => x.fieldKey === detail);
            if (detailFind) {
                return detailFind.fieldValue;
            }
        },
        updateContact(index) {
            let contact = {
                id: this.contacts[index].id,
                serial: this.contacts[index].serial,
                name: this.contacts[index].name,
                role: this.roles.find((x) => x.fieldKey === this.contacts[index].role),
                primary: Boolean(parseInt(this.contacts[index].primary)),
                details: [],
            };

            let pointer = this;
            let delayedLoading = 0;

            this.contacts[index].details.forEach(function (line, index) {
                let detail = {
                    id: line.id,
                    type: pointer.details.find((x) => x.fieldKey === line.type),
                    value: line.value,
                };

                contact.details.push(detail);
                delayedLoading++;
            });

            this.contact = contact;

            if (this.contacts[index].details.length === delayedLoading) {
                this.showContactModal('update');
            }
        },
        makePrimaryContact(id) {
            this.loading_setting = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', this.selected_lease_code);
            formData.append('primary', JSON.stringify(id));
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/primary-contact';

            this.$api.post(apiUrl, formData).then((response) => {
                this.loading_setting = false;
                let status = response.data.status;
                if (status == 'success') {
                    setTimeout(this.reloadContacts(), 5000);
                    this.$noty.success('Contact successfully set to primary');
                } else {
                    this.$noty.error(response.data.error_message);
                }
            });
        },
        deleteContact(id) {
            this.dialogConfirmation = true;
            this.delete_id = id;
        },
        deleteContactConfirm() {
            this.delete_btn_loading = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', this.selected_lease_code);
            formData.append('deleted', JSON.stringify(this.delete_id));
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/delete-contact';

            this.$api.post(apiUrl, formData).then((response) => {
                this.delete_id = null;
                let status = response.data.status;
                if (status == 'success') {
                    this.deleteContactCancel();
                    setTimeout(this.reloadContacts(), 5000);
                    this.$noty.success('Contact successfully deleted.');
                } else {
                    this.$noty.error(response.data.error_message);
                }

                this.delete_btn_loading = false;
            });
        },
        deleteContactCancel() {
            this.dialogConfirmation = false;
            this.delete_id = null;
        },
        showLogModal() {
            this.show_change_log_modal = true;
        },

        //
    },

    watch: {
        selected_property_code: function () {
            this.loadContactsList();
            this.edit_form = false;
        },
        selected_lease_code: function () {
            this.loadContactsList();
            this.edit_form = false;
        },
        reloadComponents: function () {
            this.loadContactsList();
        },
    },
    mixins: [global_mixins],
};
</script>
