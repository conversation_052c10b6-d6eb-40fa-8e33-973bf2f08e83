<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>
            <v-card
                id="commission_details_header"
                class="section-toolbar"
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Commission</h6>
                    &nbsp&nbsp
                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        data-tooltip="Edit"
                        v-show="!edit_form"
                        class="v-step-edit-button"
                        v-if="!edit_form"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <!--//v-btn x-small data-tooltip="Calculate"
                        v-show="edit_form"
                        class="v-step-edit-button" v-if="edit_form" icon @click="calculate_commission();" title="Calculate">
                    <v-icon>mdi-calculator</v-icon>
                    </v-btn//-->
                    <v-btn
                        x-small
                        v-show="edit_form"
                        data-tooltip="Undo Changes"
                        v-if="edit_form"
                        class="v-step-revert-button"
                        icon
                        @click="resetCommissionForm()"
                    >
                        <v-icon color="red">undo</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="edit_form"
                        data-tooltip="Update Details"
                        v-if="edit_form"
                        class="v-step-save-1-button"
                        icon
                        @click="saveAgencyCharges(1)"
                    >
                        <v-icon
                            light
                            color="green"
                            >check</v-icon
                        >
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Refresh"
                        class="v-step-refresh-button"
                        icon
                        @click="loadComission()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        icon
                        data-tooltip="Changelog"
                        @click="openLedgerChangelogModal()"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <!--//v-card id="bank_details_header" dark color="titleHeader" text tile>
                <v-card-actions>
                    <h4 class="title font-weight-black">Commission</h4>
				        </v-card-actions>								
            </v-card//-->

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>{{ category == 'SALE' ? 'Asking Price' : 'Asking Rent' }}:</strong>
                                </td>
                                <td class="required"></td>
                                <td v-if="!edit_form">
                                    {{ asking_price }}
                                </td>
                                <td v-if="edit_form">
                                    <v-text-field
                                        v-model="asking_price"
                                        dense
                                    />
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--//v-row class="form-row">
                <v-col xs="12" sm="2" md="2" class="form-label ">Asking Price</v-col>
                <v-col xs="12" sm="10" md="10" class="form-input">
                  <v-text-field v-model="asking_price" dense />
                </v-col>
              </v-row//-->

                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>{{ category == 'SALE' ? 'Sale Price' : 'Settlement Rent' }}:</strong>
                                </td>
                                <td class="required"></td>
                                <td v-if="!edit_form">
                                    {{ sales_price }}
                                </td>
                                <td v-if="edit_form">
                                    <v-text-field
                                        v-model="sales_price"
                                        dense
                                    />
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Settlement Date:</strong>
                                </td>
                                <td class="required"></td>
                                <td v-if="!edit_form">
                                    {{ sales_date }}
                                </td>
                                <td v-if="edit_form">
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        v-model="sales_date"
                                    ></cirrus-icon-date-picker>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--//v-row class="form-row">
                <v-col xs="12" sm="2" md="2" class="form-label required">Sale Price</v-col>
                <v-col xs="12" sm="10" md="10" class="form-input">
                  <v-text-field v-model="sales_price" dense   />
                </v-col>
              </v-row//-->

                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Commission Type:</strong>
                                </td>
                                <td class="required">*</td>
                                <td v-if="!edit_form">
                                    {{ getNameFromList(commission_type_list, commission_type, true) }}
                                </td>
                                <td v-if="edit_form">
                                    <cirrus-single-select
                                        v-model="commission_type"
                                        :options="commission_type_list"
                                    />
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--//v-row class="form-row">
                <v-col xs="12" sm="2" md="2" class="form-label required">Commission Type</v-col>
                <v-col xs="12" sm="10" md="10" class="form-input">
                  <cirrus-single-select v-model="commission_type" :options="commission_type_list" />
                </v-col>
              </v-row//-->

                <v-row
                    class="form-row no-gutters"
                    v-if="commission_type == 0 && commission_type != ''"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Amount:</strong>
                                </td>
                                <td class="required">*</td>
                                <td v-if="!edit_form">
                                    {{ commission_amount }}
                                </td>
                                <td v-if="edit_form">
                                    <v-text-field
                                        v-model="commission_amount"
                                        dense
                                    />
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--//v-row class="form-row" v-if="commission_type==0 && commission_type!=''">
                <v-col xs="12" sm="2" md="2" class="form-label required">Amount</v-col>
                <v-col xs="12" sm="10" md="10" class="form-input">
                  <v-text-field v-model="commission_amount" dense   />
                </v-col>
              </v-row//-->

                <v-row
                    class="form-row no-gutters"
                    v-if="commission_type == 1"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Percentage (%):</strong>
                                </td>
                                <td class="required">*</td>
                                <td v-if="!edit_form">
                                    {{ commission_percentage }}
                                </td>
                                <td v-if="edit_form">
                                    <v-text-field
                                        v-model="commission_percentage"
                                        dense
                                    />
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--//v-row class="form-row" v-if="commission_type==1">
                <v-col xs="12" sm="2" md="2" class="form-label required">Percentage (%)</v-col>
                <v-col xs="12" sm="10" md="10" class="form-input">
                  <v-text-field v-model="commission_percentage" dense   />
                </v-col>
              </v-row//-->

                <v-row
                    class="form-row no-gutters commission-range"
                    v-for="(ranges, index) in tier_list"
                    :key="index"
                    v-if="commission_type == 2 || commission_type == 3"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="3"
                        md="3"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                    v-if="index == 0"
                                >
                                    <strong>From Range:</strong>
                                </td>
                                <td
                                    class="title"
                                    align="right"
                                    v-else
                                >
                                    <v-btn
                                        title="Delete"
                                        v-if="edit_form"
                                        x-small
                                        icon
                                        @click="delete_tier(index)"
                                    >
                                        <v-icon color="red">delete_forever</v-icon>
                                    </v-btn>
                                </td>
                                <td class="required"></td>
                                <td v-if="!edit_form">
                                    {{ ranges.from }}
                                </td>
                                <td v-if="edit_form">
                                    <v-text-field
                                        v-model="ranges.from"
                                        dense
                                        class="commission-range-amount"
                                    />
                                </td>
                            </tr>
                        </table>
                    </v-col>
                    <v-col
                        cols="12"
                        xs="12"
                        sm="2"
                        md="2"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                    style="width: 70px"
                                >
                                    <strong>To Range:</strong>
                                </td>
                                <td class="required">*</td>
                                <td v-if="!edit_form">
                                    {{ ranges.to }}
                                </td>
                                <td v-if="edit_form">
                                    <v-text-field
                                        v-model="ranges.to"
                                        dense
                                        class="commission-range-amount"
                                    />
                                </td>
                            </tr>
                        </table>
                    </v-col>
                    <v-col
                        cols="12"
                        xs="12"
                        sm="2"
                        md="2"
                        style="margin-left: 5px"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Percentage (%):</strong>
                                </td>
                                <td class="required">*</td>
                                <td v-if="!edit_form">
                                    {{ ranges.percentage }}
                                </td>
                                <td v-if="edit_form">
                                    <v-text-field
                                        v-model="ranges.percentage"
                                        dense
                                    />
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--//v-row class="form-row no-gutters" v-for="(ranges,index) in tier_list" :key="index" v-if="commission_type==2 || commission_type==3" >
                <v-col xs="12" sm="2" md="2" class="form-label required" v-if="index==0" >From Range</v-col>
                <v-col xs="12" sm="2" md="2" class="form-label" v-else >
                  <v-btn title='Delete' x-small icon  @click="delete_tier(index)" >
                    <v-icon color="red">delete_forever</v-icon>
                  </v-btn>
                </v-col>
                <v-col xs="12" sm="1" md="1" class="form-input">
                  <v-text-field v-model="ranges.from" dense   />
                </v-col>
                <v-col xs="12" sm="1" md="1" class="form-label required">To Range</v-col>
                <v-col xs="12" sm="1" md="1" class="form-input">
                  <v-text-field v-model="ranges.to" dense   />
                </v-col>
                <v-col xs="12" sm="1" md="1" class="form-label required">Percentage (%)</v-col>
                <v-col xs="12" sm="1" md="1" class="form-input">
                  <v-text-field v-model="ranges.percentage" dense   />
                </v-col>
              </v-row//-->

                <v-row
                    class="form-row no-gutters"
                    v-if="edit_form && (commission_type == 2 || commission_type == 3)"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    &nbsp;
                                </td>
                                <td class="required"></td>
                                <td>
                                    <v-btn
                                        class="v-step-save-1-button"
                                        @click="add_tier()"
                                        color="grey"
                                        dark
                                        x-small
                                        >Add Tier</v-btn
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--//v-row class="form-row no-gutters"  v-if="(commission_type==2 || commission_type==3)" >
                <v-col xs="12" sm="2" md="2" class="form-label"></v-col>
                <v-col xs="12" sm="1" md="1" class="form-input">

                  <v-btn class="v-step-save-1-button" @click="add_tier();"
                         color="success"
                         dark
                         x-small
                  >Add Tier</v-btn>
                </v-col>
              </v-row//-->

                <v-row
                    class="no-gutters"
                    v-if="edit_form"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input text-right"
                    >
                        <v-btn
                            class="v-step-save-1-button text-right"
                            @click="calculate_commission()"
                            color="primary"
                            dark
                            small
                            right
                            >Calculate</v-btn
                        >

                        <v-btn
                            class="v-step-save-1-button text-right"
                            @click="saveAgencyCharges(1)"
                            color="success"
                            dark
                            small
                            right
                            >Update Details</v-btn
                        >
                        &nbsp;&nbsp;&nbsp;
                        <br /><br />
                    </v-col>
                </v-row>
            </div>
        </div>

        <!-- ADD/UPDATE SUB LEDGER -->
        <v-dialog
            top
            v-model="modalFeesCharge"
            width="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>{{ save_button_label }}</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="modalFeesCharge = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub Ledger Code</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="sub_ledger"
                                    :options="dd_sub_ledger_list"
                                    ref="refSubLedger"
                                />
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Type</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="type"
                                    :options="dd_types"
                                    ref="refType"
                                />
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Description</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="description"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Account</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="account"
                                    :options="dd_account"
                                    ref="refAccount"
                                />
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Net Amount</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="amount"
                                    dense
                                />
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        @click="saveAgencyCharges()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        {{ save_button_label }}
                    </v-btn>
                    <!--//v-btn color="primary" text @click="modalFeesCharge = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF UPDATE SUB LEDGER -->

        <!-- COMMISSION CHANGELOG -->
        <v-dialog
            top
            v-model="show_change_log_modal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Commission Changelog</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="show_change_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <activity-logs-component
                            v-if="show_change_log_modal"
                            :property_code="selected_property_code"
                            :form_section="'commission'"
                        ></activity-logs-component>
                        <!-- <ledger-update-log-component v-if="show_change_log_modal" :property_code="selected_property_code" :form_section="'commission'"></ledger-update-log-component> -->
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF COMMISSION CHANGELOG -->
    </v-container>
</template>

<script>
import Vue from 'vue';
import vSelect from 'vue-select';
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

const Swal = require('sweetalert2');
Vue.component('v-select', vSelect);
Vue.component('ledger-update-log-component', require('./LedgerUpdateLog.vue').default);

export default {
    props: {
        selected_property_code: { type: String, default: '' },
        reloadComponents: { type: Number, default: 0 },
        agent_country_list: {},
        agent_state_list: {},
        agent_suburb_list: {},
        default_country: {},
        default_state: {},
        refCompany: { default: '' },
        form_mode: { type: Number, default: 0 },
        category: { type: String, default: '' },
    },
    data() {
        return {
            loading_setting: false,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],

            primary: '',
            primary_old: '',
            asking_price: '',
            asking_price_old: '',
            sales_price: '',
            sales_date: '',
            sales_price_old: '',
            commission_percentage: '',
            commission_percentage_old: '',
            commission_amount: '',
            commission_amount_old: '',
            tier_list: [{ from: '', to: '', percentage: '' }],
            tier_list_old: [{ from: '', to: '', percentage: '' }],
            commission_type: '',
            commission_type_old: '',
            commission_type_list: [
                {
                    fieldKey: '0',
                    fieldValue: 'Fixed Amount',
                    field_key: '0',
                    field_value: 'Fixed Amount',
                    value: '0',
                    label: 'Fixed Amount',
                },
                {
                    fieldKey: '1',
                    fieldValue: 'Percentage',
                    field_key: '1',
                    field_value: 'Percentage',
                    value: '1',
                    label: 'Percentage',
                },
                {
                    fieldKey: '2',
                    fieldValue: 'Fixed Tier',
                    field_key: '2',
                    field_value: 'Fixed Tier',
                    value: '2',
                    label: 'Fixed Tier',
                },
                {
                    fieldKey: '3',
                    fieldValue: 'Incremental Tier',
                    field_key: '3',
                    field_value: 'Incremental Tier',
                    value: '3',
                    label: 'Incremental Tier',
                },
            ],

            searchSundries: '',
            searchSundries_old: '',
            modalFeesCharge: false,
            modalFeesCharge_old: false,
            //types: 	{'rentreview' : 'Rent Review Fee','inspection' : 'Inspection Fee'},
            dd_types: [],
            dd_sub_ledger_list: [],
            dd_account: [],

            save_button_label: 'ADD AGENCY CHARGES',
            sub_ledger: '',
            sub_ledger_old: '',
            sub_ledger_code: '',
            sub_ledger_code_old: '',
            type: '',
            type_old: '',
            type_name: '',
            type_name_old: '',
            description: '',
            description_old: '',
            account: '',
            account_old: '',
            account_code: '',
            account_code_old: '',
            account_label: '',
            account_label_old: '',
            amount: '',
            amount_old: '',

            formData: {},
            edit_form: false,
            show_change_log_modal: false,
        };
    },
    mounted() {
        this.loadAccountsList();
        this.loadSundriesType();
        this.loadSubLedgerList();
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'dd_company_list',
            'dd_sub_ledger_type_list',
        ]),
    },
    methods: {
        ...mapActions(['fetchCompanyList', 'fetchSubLedgerTypeList']),
        ...mapMutations(['SET_DD_COMPANY_LIST', 'SET_SUB_LEDGER_TYPE_CODE', 'SET_DD_SUB_LEDGER_TYPE_LIST']),

        doubleClickForm() {
            if (!this.edit_form) {
                this.edit_form = true;
            }
        },

        saveAgencyCharges: function (saveOnly) {
            this.error_msg = [];

            if (saveOnly === undefined) {
                if (this.sub_ledger == '' || this.sub_ledger == null) {
                    this.error_msg.push({
                        id: 'sub_ledger_code',
                        message: 'You have not specified a sub-ledger code.',
                    });
                    this.$noty.error('You have not specified a sub-ledger code.');
                }

                if (this.type == '' || this.type == null) {
                    this.error_msg.push({
                        id: 'agency_charge_type',
                        message: 'You have not specified a type.',
                    });
                    this.$noty.error('You have not specified a type.');
                }

                if (this.account == '' || this.account == null) {
                    this.error_msg.push({
                        id: 'agency_charge_account',
                        message: 'You have not specified an account.',
                    });
                    this.$noty.error('You have not specified an account.');
                }

                if (this.description == '' || this.description == null) {
                    this.error_msg.push({
                        id: 'agency_charge_desc',
                        message: 'You have not specified a description.',
                    });
                    this.$noty.error('You have not specified a description.');
                }

                if (this.amount == '' || this.amount == null || this.amount == 0) {
                    this.error_msg.push({
                        id: 'agency_charge_net_amount',
                        message: 'You have not specified a net amount.',
                    });
                    this.$noty.error('You have not specified a net amount.');
                }
            }

            if (this.error_msg.length <= 0) {
                this.loading_setting = true;
                /*this.formData.save_only =  0;
              this.formData.un = this.username;
              this.formData.current_db = this.current_db;
              this.formData.user_type = this.user_type;

              this.formData.property_code = this.selected_property_code;
              this.formData.lease_code = this.sub_ledger;
              this.formData.type =  this.type;
              this.formData.account_code = this.account;
              this.formData.description = this.description;
              this.formData.amount = this.amount;

              this.formData.asking_price =  this.asking_price;
              this.formData.sales_price = this.sales_price;
              this.formData.commission_type =  this.commission_type;
              this.formData.commission_amount =  this.commission_amount;
              this.formData.commission_percentage =  this.commission_percentage;
              this.formData.tier_list =  this.tier_list;
              this.formData.primary =  this.primary;
              if(saveOnly)
                this.formData.save_only =  1;*/

                //update or create sub ledger details
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/create-commission';

                var form_data = new FormData();

                if (saveOnly === undefined) {
                    form_data.append('save_only', 0);
                } else {
                    form_data.append('save_only', 1);
                }

                // form_data.append('un', this.username);
                // form_data.append('current_db', this.current_db);
                form_data.append('user_type', this.user_type);
                form_data.append('property_code', this.selected_property_code);
                form_data.append('lease_code', this.sub_ledger);
                form_data.append('type', this.type);
                form_data.append('account_code', this.account);
                form_data.append('description', this.description);
                form_data.append('amount', this.amount);
                form_data.append('asking_price', this.asking_price);
                form_data.append('sales_price', this.sales_price);
                form_data.append('sales_date', this.sales_date);
                form_data.append('category', this.category);
                form_data.append('commission_type', this.commission_type);
                form_data.append('commission_amount', this.commission_amount);
                form_data.append('commission_percentage', this.commission_percentage);
                form_data.append('tier_list', JSON.stringify(this.tier_list));
                form_data.append('primary', this.primary);
                form_data.append('no_load', true);

                this.$api.post(apiUrl, form_data).then((response) => {
                    this.status = response.data.status;

                    if (this.status == 'success') {
                        if (saveOnly) this.$noty.success('Commission updated.');
                        else {
                            this.$noty.success('Agency charges added.');
                            bus.$emit('call_load');
                        }
                        this.modalFeesCharge = false;
                        this.loading_setting = false;

                        //set new values upon save
                        this.type_old = this.type;
                        this.description_old = this.description;
                        this.amount_old = this.amount;
                        this.asking_price_old = this.asking_price;
                        this.sales_price_old = this.sales_price;
                        this.commission_type_old = this.commission_type;
                        this.commission_amount_old = this.commission_amount;
                        this.commission_percentage_old = this.commission_percentage;
                        this.primary_old = this.primary;
                        this.tier_list_old = this.tier_list;

                        if (!this.commission_type) {
                            this.commission_amount = '';
                            this.commission_percentage = '';
                            this.tier_list = [{ from: '', to: '', percentage: '' }];
                        } else {
                            switch (parseInt(this.commission_type)) {
                                case 0:
                                    this.commission_percentage = '';
                                    this.tier_list = [{ from: '', to: '', percentage: '' }];
                                    break;
                                case 1:
                                    this.commission_amount = '';
                                    this.tier_list = [{ from: '', to: '', percentage: '' }];
                                    break;
                                case 2:
                                case 3:
                                    this.commission_amount = '';
                                    this.commission_percentage = '';
                                    break;
                            }
                        }

                        this.edit_form = false;
                    } else {
                        this.$noty.error(response.data.validation_errors);
                        this.loading_setting = false;
                        //this.loadComission();
                    }
                });

                /*axios.post(apiUrl, this.formData ).then(response => {
                this.status = response.data.status;
                if (this.status == 'success') {
                  if(saveOnly)
                    this.$noty.success('update commission successfully.');
                  else
                  {this.$noty.success('Agency charges added.');
                    bus.$emit('call_load');
                  }
                  this.modalFeesCharge = false;
                  this.loading_setting = false;

                  //set new values upon save
                  this.type_old = this.type;
                  this.description_old = this.description;
                  this.amount_old = this.amount;
                  this.asking_price_old = this.asking_price;
                  this.sales_price_old = this.sales_price;
                  this.commission_type_old = this.commission_type;
                  this.commission_amount_old = this.commission_amount;
                  this.commission_percentage_old = this.commission_percentage;
                  this.primary_old = this.primary;
                  this.tier_list_old = this.tier_list;
                }
                else {
                  this.$noty.error(response.data.validation_errors);
                  this.loading_setting = false;
                }
              });*/
            }
        },
        resetCommissionForm: function () {
            this.edit_form = false;

            this.sub_ledger = this.sub_ledger_old;
            this.type = this.type_old;
            this.account = this.account_old;
            this.description = this.description_old;
            this.amount = this.amount_old;

            this.asking_price = this.asking_price_old;
            this.sales_price = this.sales_price_old;
            this.commission_type = this.commission_type_old;
            this.commission_amount = this.commission_amount_old;
            this.commission_percentage = this.commission_percentage_old;

            this.primary = this.primary_old;

            this.tier_list = this.tier_list_old;
        },
        calculate_commission: function () {
            let selling_price = parseFloat(this.asking_price);
            if (this.sales_price) selling_price = parseFloat(this.sales_price);

            if (selling_price) {
                this.amount = '';
                if (this.commission_type == 0 && this.commission_type != '') this.amount = this.commission_amount;
                else if (this.commission_type == 1)
                    this.amount = ((this.commission_percentage / 100) * selling_price).toFixed(2);
                else if (this.commission_type == 2)
                    for (let x = 0; this.tier_list.length > x; x++) {
                        if (
                            selling_price >= parseFloat(this.tier_list[x].from) &&
                            selling_price <= parseFloat(this.tier_list[x].to)
                        )
                            this.amount = ((parseFloat(this.tier_list[x].percentage) / 100) * selling_price).toFixed(2);
                    }
                else if (this.commission_type == 3) {
                    let sum = 0;
                    let stop = 0;
                    for (let x = 0; this.tier_list.length > x; x++) {
                        if (selling_price <= parseFloat(this.tier_list[x].to) && x == 0) {
                            this.amount = ((parseFloat(this.tier_list[x].percentage) / 100) * selling_price).toFixed(2);
                            stop = 1;
                        } else if (x == 0) {
                            sum =
                                sum +
                                (parseFloat(this.tier_list[x].percentage) / 100) * parseFloat(this.tier_list[x].to);
                        } else if (selling_price > parseFloat(this.tier_list[x].to)) {
                            sum =
                                sum +
                                (parseFloat(this.tier_list[x].to) - parseFloat(this.tier_list[x].from)) *
                                    (parseFloat(this.tier_list[x].percentage) / 100);
                        } else if (selling_price <= parseFloat(this.tier_list[x].to) && stop == 0) {
                            this.amount = (
                                sum +
                                (selling_price - parseFloat(this.tier_list[x].from)) *
                                    (parseFloat(this.tier_list[x].percentage) / 100)
                            ).toFixed(2);
                            stop = 1;
                        }
                    }
                }
            } else this.amount = '';

            this.modalFeesCharge = true;
        },
        add_tier: function () {
            this.tier_list.push({ from: '', to: '', percentage: '' });
        },
        delete_tier: function (index) {
            this.$delete(this.tier_list, index);
        },
        onlyForCurrency($event) {
            let keyCode = $event.keyCode ? $event.keyCode : $event.which;

            if ((keyCode < 48 || keyCode > 57) && (keyCode !== 46 || this.amount.indexOf('.') != -1)) {
                // 46 is dot
                $event.preventDefault();
            }

            if (this.amount != null && this.amount.indexOf('.') > -1 && this.amount.split('.')[1].length > 1) {
                $event.preventDefault();
            }
        },
        resetFields: function () {
            if (this.sub_ledger && typeof this.$refs.refSubLedger !== 'undefined')
                this.$refs.refSubLedger.select_val = '';
            if (this.type && typeof this.$refs.refType !== 'undefined') this.$refs.refType.select_val = '';
            if (this.account && typeof this.$refs.refAccount !== 'undefined') this.$refs.refAccount.select_val = '';
            this.sub_ledger = '';
            this.type = '';
            this.description = '';
            this.account = '';
            this.amount = '';
        },
        loadComission: function () {
            if (this.selected_property_code == '') return true;

            this.loading_setting = true;

            // reset error
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('no_load', true);
            formData.append('property_code', this.selected_property_code);
            this.resetFields();

            // get ledger details by code
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-commission';

            this.$api.post(apiUrl, formData).then((response) => {
                this.sub_ledger = response.data.data['sub_ledger'];
                this.sub_ledger_old = response.data.data['sub_ledger'];
                this.type = response.data.data['type'];
                this.type_old = response.data.data['type'];
                this.account = response.data.data['account'];
                this.account_old = response.data.data['account'];
                this.description = response.data.data['description'];
                this.description_old = response.data.data['description'];
                this.amount = response.data.data['amount'];
                this.amount_old = response.data.data['amount'];

                this.asking_price = response.data.data['asking_price'];
                this.asking_price_old = response.data.data['asking_price'];
                this.sales_price = response.data.data['sales_price'];
                this.sales_price_old = response.data.data['sales_price'];
                this.commission_type = response.data.data['commission_type'];
                this.commission_type_old = response.data.data['commission_type'];
                this.commission_amount = response.data.data['commission_amount'];
                this.commission_amount_old = response.data.data['commission_amount'];
                this.commission_percentage = response.data.data['commission_percentage'];
                this.commission_percentage_old = response.data.data['commission_percentage'];
                this.sales_date = response.data.data['settlement_date'];

                this.primary = response.data.data['primary'];
                this.primary_old = response.data.data['primary'];

                this.tier_list = response.data.tier;
                this.tier_list_old = response.data.tier;
                this.loading_setting = false;
            });

            /*axios.post(apiUrl, formData).then(response => {

                this.sub_ledger = response.data.data['sub_ledger'];
                this.sub_ledger_old = response.data.data['sub_ledger'];
                this.type =  response.data.data['type'];
                this.type_old =  response.data.data['type'];
                this.account = response.data.data['account'];
                this.account_old = response.data.data['account'];
                this.description = response.data.data['description'];
                this.description_old = response.data.data['description'];
                this.amount = response.data.data['amount'];
                this.amount_old = response.data.data['amount'];

                this.asking_price =  response.data.data['asking_price'];
                this.asking_price_old =  response.data.data['asking_price'];
                this.sales_price = response.data.data['sales_price'];
                this.sales_price_old = response.data.data['sales_price'];
                this.commission_type =  response.data.data['commission_type'];
                this.commission_type_old =  response.data.data['commission_type'];
                this.commission_amount =  response.data.data['commission_amount'];
                this.commission_amount_old =  response.data.data['commission_amount'];
                this.commission_percentage =  response.data.data['commission_percentage'];
                this.commission_percentage_old =  response.data.data['commission_percentage'];

                this.primary =  response.data.data['primary'];
                this.primary_old =  response.data.data['primary'];

                this.tier_list =  response.data.tier;
                this.tier_list_old =  response.data.tier;
                this.loading_setting = false;
              });*/
        },
        loadAccountsList: function () {
            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/loadAPIAccountDropDownList';

            this.$api.post(apiUrl, formData).then((response) => {
                let account_data = response.data.data;
                let accDataArray = [];
                $.each(account_data, function (i, v) {
                    accDataArray.push({ value: v.account_code, label: v.account_code + ' - ' + v.account_name });
                });
                this.dd_account = this.convertToArrayOfObjects(accDataArray);
            });

            /*axios.post(this.cirrus8_api_url + 'api/loadAPIAccountDropDownList', formData)
                .then(response => {
                  let account_data = response.data.data;
                  let accDataArray = [];
                  $.each(account_data, function(i, v){
                    accDataArray.push({'value': v.account_code, 'label': v.account_code + ' - ' + v.account_name});
                  });
                  this.dd_account = accDataArray;
                });*/
        },

        loadSundriesType: function () {
            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/ui/fetch/param-sundry-list';

            this.$api.post(apiUrl, formData).then((response) => {
                this.dd_types = this.convertToArrayOfObjects(response.data);
            });

            /*axios.post(this.cirrus8_api_url + 'api/ui/fetch/param-sundry-list', {
              current_db: this.current_db,
              user_type: this.user_type,
              un: this.username,
            })
                .then(response => {
                  this.dd_types = response.data;
                });*/
        },

        loadSubLedgerList: function () {
            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('property_code', this.selected_property_code);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-sundries-sub-ledgers';

            this.$api.post(apiUrl, formData).then((response) => {
                this.loadComission();
                this.dd_sub_ledger_list = this.convertToArrayOfObjects(response.data.data);
            });

            /*axios.post(this.cirrus8_api_url + 'api/sales-trust/ledger/get-sundries-sub-ledgers', formData)
                .then(response => {
                  this.loadComission();
                  this.dd_sub_ledger_list = response.data.data;

                });*/
        },

        getNameFromList: function (arrayVar, value, camelCase, getLabel) {
            if (arrayVar && value) {
                if (camelCase) {
                    var fieldData = arrayVar.find(({ fieldKey }) => fieldKey === value);

                    if (getLabel !== undefined) {
                        return fieldData.label;
                    } else {
                        return fieldData.fieldValue;
                    }
                } else {
                    var fieldData = arrayVar.find(({ field_key }) => field_key === value);

                    if (getLabel !== undefined) {
                        return fieldData.label;
                    } else {
                        return fieldData.field_value;
                    }
                }
            }
        },

        openLedgerChangelogModal: function () {
            this.show_change_log_modal = true;
        },
    },

    watch: {
        selected_property_code: function () {
            this.loadSubLedgerList();
            this.edit_form = false;
        },
        reloadComponents: function () {
            this.loadSubLedgerList();
        },
    },
    mixins: [global_mixins],
};
</script>
