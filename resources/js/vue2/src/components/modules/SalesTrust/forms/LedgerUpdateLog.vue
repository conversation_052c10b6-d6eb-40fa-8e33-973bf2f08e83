<template>
    <div class="ledger-update-log-container">
        <v-skeleton-loader
            class="mx-auto"
            type="table"
            :loading="loading"
            style="position: relative"
        >
            <v-btn
                @click="exportToXLSX()"
                v-if="this.ledger_update_log_list.length > 0"
                depressed
                small
                style="position: absolute; z-index: 99"
            >
                Export to Excel
            </v-btn>
            <v-card elevation="0">
                <v-card-title style="padding-top: 0px">
                    <v-spacer></v-spacer>
                    <v-text-field
                        v-model="search"
                        append-icon="search"
                        label="Search"
                        single-line
                        hide-details
                        style="padding-top: 0px; margin-top: 0px"
                    ></v-text-field>
                </v-card-title>
                <v-data-table
                    item-key="id"
                    :headers="headers"
                    :items="ledger_update_log_list"
                    :search="search"
                >
                    <template v-slot:item.index="{ item }">
                        {{ ledger_update_log_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.field_name="{ item }">
                        <span v-html="item.field_name"></span>
                    </template>
                    <template v-slot:item.old_value="{ item }">
                        <span v-html="item.old_value"></span>
                    </template>
                    <template v-slot:item.new_value="{ item }">
                        <span v-html="item.new_value"></span>
                    </template>
                </v-data-table>
            </v-card>
        </v-skeleton-loader>
    </div>
</template>

<script>
import moment from 'moment';
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
export default {
    props: {
        property_code: { type: String, default: '' },
        form_section: { type: String, default: '' },
    },
    data() {
        return {
            loading: false,
            search: '',
            headers: [
                {
                    text: '#',
                    value: 'index',
                    sortable: false,
                    width: '5%',
                },
                { text: 'Field Name', value: 'field_name', sortable: true, width: '15%' },
                { text: 'Old Value', value: 'old_value', sortable: false, width: '20%' },
                { text: 'New Value', value: 'new_value', sortable: false, width: '20%' },
                { text: 'User', value: 'modified_by', sortable: true, width: '15%' },
                { text: 'Date', value: 'date_modified', sortable: true, width: '15%' },
            ],
            ledger_update_log_list: [],
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadUpdateLogs();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    methods: {
        loadUpdateLogs: function () {
            if (this.property_code !== '' && this.form_section !== '') {
                this.loading = true;

                var form_data = new FormData();
                form_data.append('user_type', this.user_type);
                form_data.append('property_code', this.property_code);
                form_data.append('form_section', this.form_section);
                form_data.append('no_load', true);

                let api_url = this.cirrus8_api_url + 'api/sales-trust/ledger/update-log';

                /*axios.post(api_url, form_data).then(response => {
                        this.ledger_update_log_list = this.transposeData(response.data);
                        this.loading = false;
                    });*/

                this.$api.post(api_url, form_data).then((response) => {
                    this.ledger_update_log_list = this.transposeData(response.data);
                    this.loading = false;
                });
            }
        },
        transposeData: function (data) {
            var transposed_data = [];

            data.forEach(function (item, index) {
                transposed_data.push(item);
            });

            return transposed_data;
        },
        exportToXLSX: function () {
            if (this.property_code !== '' && this.form_section !== '') {
                let params = {
                    property_code: this.property_code,
                    form_section: this.form_section,
                    no_load: true,
                };

                this.$api.post('sales-trust/ledger/export-update-log', this.req(params)).then((response) => {
                    let res = response.data;

                    if (res.file && res.file.name && res.file.type && res.file.data) {
                        this.printDownload(res.file.data, res.file.name + moment().unix(), res.file.type);
                    } else {
                        if (res.error) this.$noty.error(res.error);
                        else this.$noty.error('Generate report failed. Please try again.');
                    }
                });
            }
        },
    },
    created() {
        bus.$on('loadLedgerUpdateLog', (data) => {
            this.loadUpdateLogs();
        });
    },
    mixins: [global_mixins],
};
</script>
