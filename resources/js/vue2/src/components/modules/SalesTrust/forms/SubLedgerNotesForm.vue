<style>
.c8-page-table-row-header td {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.6);
}
.ledger-note-card {
    padding: 6px 0 !important;
}
.ledger-note-card .v-sheet {
    box-shadow: none !important;
}
.ledger-notes-container {
    box-shadow: none !important;
}
#subledgerNoteForm .v-data-iterator > div:not(.row) {
    padding: 5px;
    text-align: center;
    color: rgba(0, 0, 0, 0.38);
}
#subledgerNoteModal .noteTextArea {
    margin: 0;
    padding: 0;
}

#subledgerNoteModal .noteTextArea .v-input__slot {
    background: none !important;
}

#subledgerNoteModal .noteTextArea.v-text-field.v-input--is-focused > .v-input__control > .v-input__slot:after {
    transform: scaleX(0);
}
</style>
<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div id="subledgerNoteForm">
            <v-card
                id="notes_header"
                class="section-toolbar"
                color="titleHeader"
                text
                tile
                dark
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Notes</h6>
                    &nbsp;&nbsp;
                    <v-spacer></v-spacer>
                    <div style="display: inline-block; padding-right: 1em">
                        <span class="toolbar-search-holder">
                            <v-text-field
                                dense
                                v-model="search"
                                :class="'cirrus-input-form-textbox'"
                                :placeholder="'Search'"
                                prepend-inner-icon="search"
                                :maxlength="'60'"
                            ></v-text-field>
                        </span>
                    </div>
                    <v-btn
                        x-small
                        class="v-step-edit-button"
                        icon
                        @click="showNoteModal('create')"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="!edit_form"
                        class="v-step-edit-button"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="edit_form && notes.length > 0"
                        class="v-step-save-1-button"
                        icon
                        @click="closeEditing()"
                    >
                        <v-icon
                            light
                            color="red"
                            >undo</v-icon
                        >
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="edit_form && notes.length > 0"
                        icon
                        @click="saveForm()"
                    >
                        <v-icon
                            light
                            color="green"
                            >check</v-icon
                        >
                    </v-btn>
                    <v-btn
                        x-small
                        class="v-step-refresh-button"
                        icon
                        @click="reloadNotes()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        icon
                        @click="showLogModal()"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <v-col
                class="text-center"
                v-if="notes.length === 0"
            >
                <v-btn
                    depressed
                    small
                    color="success"
                    @click="showNoteModal('create')"
                    class="sbldgr-add-btn"
                    >Add Note</v-btn
                >
            </v-col>
            <v-data-iterator
                :items="notes"
                :search="search"
                hide-default-footer
                disable-pagination
                v-if="notes.length > 0 && !loading_setting"
            >
                <template v-slot:default="props">
                    <v-row class="ma-0">
                        <v-col
                            v-for="(item, index) in props.items"
                            :key="index"
                            cols="12"
                            sm="12"
                            md="12"
                            lg="12"
                            class="ledger-note-card"
                        >
                            <v-card
                                color=""
                                class=""
                            >
                                <v-card-actions class="pa-2">
                                    <strong
                                        >{{ notes.indexOf(item) + 1
                                        }}<span>:({{ formatID(item.id) }}).&nbsp;</span></strong
                                    >
                                    <span>{{ formatHeader(item.header) }}</span>
                                    <span> &nbsp; | &nbsp; </span>
                                    <strong
                                        ><span>{{ item.created_at }}</span></strong
                                    >
                                    <span> &nbsp; by &nbsp; </span>
                                    <strong
                                        ><span>{{ item.created_by }}</span></strong
                                    >
                                    <v-spacer></v-spacer>
                                    <v-icon
                                        color="green"
                                        class="rotate90"
                                        v-if="edit_form"
                                        @click="setFirstOrLast('first', index)"
                                        >fast_rewind
                                    </v-icon>
                                    <v-icon
                                        color="green"
                                        class="rotate90"
                                        v-if="edit_form"
                                        @click="setOrder('up', index)"
                                        >arrow_left
                                    </v-icon>
                                    <v-icon
                                        color="green"
                                        class="rotate90"
                                        v-if="edit_form"
                                        @click="setOrder('down', index)"
                                        >arrow_right
                                    </v-icon>
                                    <v-icon
                                        color="green"
                                        class="rotate90"
                                        v-if="edit_form"
                                        @click="setFirstOrLast('last', index)"
                                        >fast_forward
                                    </v-icon>
                                    <v-icon
                                        small
                                        @click="updateNote(notes.indexOf(item))"
                                        v-if="edit_form"
                                        class="sbldgr-edit-btn"
                                        >fas fa-edit</v-icon
                                    >
                                    <v-icon
                                        color="red"
                                        v-if="edit_form"
                                        @click="deleteNote(item.id)"
                                        >close</v-icon
                                    >
                                </v-card-actions>
                                <v-divider light></v-divider>
                                <v-layout>
                                    <v-flex xs12>
                                        <div
                                            class="noteTextAreaBgColor"
                                            style="white-space: pre-wrap; padding: 12px"
                                            v-text="item.value"
                                        ></div>
                                    </v-flex>
                                </v-layout>
                            </v-card>
                        </v-col>
                    </v-row>
                </template>
            </v-data-iterator>
        </div>

        <!-- NOTE MODAL -->
        <v-dialog
            persistent
            top
            v-model="show_note_modal"
            max-width="900"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card id="subledgerNoteModal">
                <v-card-title class="headline">
                    <span>Note</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="hideNoteModal()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="form-input"
                                >
                                    <v-card
                                        color=""
                                        class="ledger-notes-container"
                                    >
                                        <v-card-actions class="pa-2">
                                            <strong>
                                                <span class="form-input-text">
                                                    {{ note.id ? note.index + 1 + '.' : 'Note Header ' }} </span
                                                >&nbsp;
                                            </strong>
                                            <multiselect
                                                v-model="note.header"
                                                :options="headers"
                                                :allowEmpty="false"
                                                class="vue-select2 dropdown-left dropdown-300"
                                                group-label="language"
                                                placeholder="Select header"
                                                track-by="field_key"
                                                label="field_value"
                                                :show-labels="false"
                                                ><span slot="noResult">Oops! No elements found.</span>
                                            </multiselect>
                                        </v-card-actions>
                                        <v-layout>
                                            <v-flex xs12>
                                                <v-textarea
                                                    v-model="note.value"
                                                    auto-grow
                                                    rows="14"
                                                    full-width
                                                    class="noteTextArea"
                                                ></v-textarea>
                                            </v-flex>
                                        </v-layout>
                                    </v-card>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions class="footer-actions">
                    <v-btn
                        class="v-step-save-2-button"
                        @click="loadPrevious()"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        v-if="form_mode != 'create'"
                        class="v-step-save-2-button"
                        :loading="submit_btn_loading"
                        @click="addNoteData()"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        :loading="submit_btn_loading"
                        @click="submitNoteData()"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Submit
                    </v-btn>
                    <v-btn
                        v-if="form_mode == 'create'"
                        class="v-step-save-2-button"
                        @click="addNoteData()"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        v-if="form_mode != 'create'"
                        class="v-step-save-2-button"
                        @click="deleteNote(note.id)"
                        color="error"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="loadNext()"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                        Next
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            top
            v-model="show_change_log_modal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Activity Logs</span>
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click="show_change_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <activity-logs-component
                            v-if="show_change_log_modal"
                            :property_code="selected_property_code"
                            :lease_code="selected_lease_code"
                            :form_section="'note'"
                        ></activity-logs-component>
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            persistent
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon> WARNING
                    <a
                        href="#"
                        class="dialog-close no-hover"
                        @click.prevent="deleteNoteCancel()"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">Do you really want to delete this note?</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        :loading="delete_btn_loading"
                        @click="deleteNoteConfirm()"
                        >Yes</v-btn
                    >
                    <v-btn
                        depressed
                        tile
                        small
                        @click="deleteNoteCancel()"
                        >No</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->
    </v-container>
</template>

<script>
import Vue from 'vue';
import vSelect from 'vue-select';
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import moment from 'moment';

const Swal = require('sweetalert2');
Vue.component('v-select', vSelect);

export default {
    props: {
        selected_property_code: { type: String, default: '' },
        selected_lease_code: { type: String, default: '' },
        reloadComponents: { type: Number, default: 0 },
    },
    data() {
        return {
            loading_setting: false,
            asset_domain: this.$assetDomain,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],

            show_note_modal: false,

            headers: [],
            notes: [],
            note: {
                id: null,
                header: null,
                value: null,
                created_at: null,
                created_by: null,
                sequence: null,
                index: null,
            },

            form_mode: 'create',
            delete_id: null,

            dialogConfirmation: '',
            edit_form: false,

            search: '',
            show_change_log_modal: false,
            submit_btn_loading: false,
            delete_btn_loading: false,
        };
    },
    mounted() {
        this.loadNotesList();
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url']),
    },
    methods: {
        ...mapActions([]),
        ...mapMutations([]),
        // ...

        doubleClickForm() {
            if (!this.edit_form) {
                this.edit_form = true;
            }
        },

        loadNotesList() {
            this.loading_setting = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);
            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', this.selected_lease_code);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/get-notes';

            this.$api.post(apiUrl, formData).then((response) => {
                this.notes = response.data.notes;
                this.headers = response.data.headers;
                this.loading_setting = false;
            });
        },
        formatID(id) {
            return String(id).padStart(6, '0');
        },
        formatHeader(header) {
            let find = this.headers.find((x) => x.fieldKey === header);
            if (find) {
                return find.fieldValue;
            }
        },
        closeEditing: function () {
            this.reloadNotes();
            this.edit_form = false;
        },
        showNoteModal(mode) {
            this.form_mode = mode;
            this.show_note_modal = true;
        },
        hideNoteModal() {
            this.show_note_modal = false;
            this.clearNoteData();
        },
        addNoteData() {
            this.show_note_modal = true;
            this.clearNoteData();
            this.form_mode = 'create';
        },
        submitNoteData() {
            let validate = this.validateNoteForm();

            if (validate) {
                this.submit_btn_loading = true;

                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('property_code', this.selected_property_code);
                formData.append('lease_code', this.selected_lease_code);
                formData.append('note', this.formatFormData(this.note));
                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/' + this.form_mode + '-note';

                this.$api.post(apiUrl, formData).then((response) => {
                    let status = response.data.status;
                    if (status == 'success') {
                        setTimeout(this.reloadNotes(), 5000);
                        this.hideNoteModal();
                        this.$noty.success('Note successfully ' + this.form_mode + 'd.');
                    } else {
                        this.$noty.error(response.data.error_message);
                    }

                    this.submit_btn_loading = false;
                });
            }
        },
        validateNoteForm() {
            let errors = [];

            if (!this.note.header) {
                errors.push(['You have not entered a valid note heading.']);
            }

            if (!this.note.value) {
                errors.push(['You have not entered a valid description for the note.']);
            }

            if (errors.length === 0) {
                return true;
            } else {
                this.error_server_msg2 = errors;
                return false;
            }
        },
        formatFormData() {
            return JSON.stringify(this.note);
        },
        clearNoteData() {
            let note = {
                id: null,
                header: null,
                value: null,
                created_at: null,
                created_by: null,
                sequence: null,
                index: null,
            };

            this.note = note;
            this.error_server_msg2 = [];
        },
        reloadNotes() {
            setTimeout(this.loadNotesList(), 5000);
        },
        updateNote(index) {
            let note = {
                id: this.notes[index].id,
                header: this.headers.find((x) => x.fieldKey === this.notes[index].header),
                value: this.notes[index].value,
                index: this.notes[index].index,
            };

            this.note = note;
            this.showNoteModal('update');
        },
        deleteNote(id) {
            this.dialogConfirmation = true;
            this.delete_id = id;
        },
        deleteNoteConfirm() {
            this.delete_btn_loading = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', this.selected_lease_code);
            formData.append('deleted', JSON.stringify(this.delete_id));
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/delete-note';

            this.$api.post(apiUrl, formData).then((response) => {
                this.delete_id = null;
                let status = response.data.status;
                if (status == 'success') {
                    this.deleteNoteCancel();
                    setTimeout(this.reloadNotes(), 5000);
                    this.$noty.success('Note successfully deleted.');
                } else {
                    this.$noty.error(response.data.error_message);
                }

                this.delete_btn_loading = false;
                this.hideNoteModal();
            });
        },
        deleteNoteCancel() {
            this.dialogConfirmation = false;
            this.delete_id = null;
        },
        setFirstOrLast(position, index) {
            let id = this.notes[index].id;
            let header = this.notes[index].header;
            let value = this.notes[index].value;
            let created_at = this.notes[index].created_at;
            let created_by = this.notes[index].created_by;
            let sequence = this.notes[index].sequence;
            let pointer = this.notes[index].inder;

            if (position == 'first') {
                if (index == 0) {
                    return false;
                }

                for (let x = this.notes.length - 1; x >= 0; x--) {
                    if (x <= index && x != 0) {
                        this.notes[x].id = this.notes[x - 1].id;
                        this.notes[x].header = this.notes[x - 1].header;
                        this.notes[x].value = this.notes[x - 1].value;
                        this.notes[x].created_at = this.notes[x - 1].created_at;
                        this.notes[x].created_by = this.notes[x - 1].created_by;
                        this.notes[x].sequence = this.notes[x - 1].sequence;
                        this.notes[x].index = this.notes[x - 1].index;
                    } else if (x == 0) {
                        this.notes[0].id = id;
                        this.notes[0].header = header;
                        this.notes[0].value = value;
                        this.notes[0].created_by = created_by;
                        this.notes[0].created_at = created_at;
                        this.notes[0].sequence = sequence;
                        this.notes[0].index = pointer;
                    }
                }
            } else if (position == 'last') {
                if (this.notes.length == index + 1) {
                    return false;
                }

                let last = this.notes.length - 1;
                for (let x = 0; x < this.notes.length; x++) {
                    if (x >= index && x != last) {
                        this.notes[x].id = this.notes[x + 1].id;
                        this.notes[x].header = this.notes[x + 1].header;
                        this.notes[x].value = this.notes[x + 1].value;
                        this.notes[x].created_at = this.notes[x + 1].created_at;
                        this.notes[x].created_by = this.notes[x + 1].created_by;
                        this.notes[x].sequence = this.notes[x + 1].sequence;
                        this.notes[x].index = this.notes[x + 1].index;
                    } else if (x == last) {
                        this.notes[last].id = id;
                        this.notes[last].header = header;
                        this.notes[last].value = value;
                        this.notes[last].created_at = created_at;
                        this.notes[last].created_by = created_by;
                        this.notes[last].sequence = sequence;
                        this.notes[last].index = pointer;
                    }
                }
            }
        },
        setOrder(direction, index) {
            let oldIndex = index;
            let newIndex = 0;

            if (direction == 'up') {
                if (index == 0) {
                    return false;
                }

                newIndex = index - 1;
            } else if (direction == 'down') {
                if (this.notes.length == index + 1) {
                    return false;
                }

                newIndex = index + 1;
            }

            let id = this.notes[newIndex].id;
            let header = this.notes[newIndex].header;
            let value = this.notes[newIndex].value;
            let created_at = this.notes[newIndex].created_at;
            let created_by = this.notes[newIndex].created_by;
            let sequence = this.notes[newIndex].sequence;
            let pointer = this.notes[newIndex].index;

            this.notes[newIndex].id = this.notes[oldIndex].id;
            this.notes[newIndex].header = this.notes[oldIndex].header;
            this.notes[newIndex].value = this.notes[oldIndex].value;
            this.notes[newIndex].created_at = this.notes[oldIndex].created_at;
            this.notes[newIndex].created_by = this.notes[oldIndex].created_by;
            this.notes[newIndex].sequence = this.notes[oldIndex].sequence;
            this.notes[newIndex].index = this.notes[oldIndex].index;

            this.notes[oldIndex].id = id;
            this.notes[oldIndex].header = header;
            this.notes[oldIndex].value = value;
            this.notes[oldIndex].created_at = created_at;
            this.notes[oldIndex].created_by = created_by;
            this.notes[oldIndex].sequence = sequence;
            this.notes[oldIndex].index = pointer;
        },
        saveForm() {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', this.selected_lease_code);
            formData.append('notes', JSON.stringify(this.notes));
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/subledger/reorder-notes';

            this.$api.post(apiUrl, formData).then((response) => {
                let status = response.data.status;
                if (status == 'success') {
                    setTimeout(this.reloadNotes(), 5000);

                    this.$noty.success('Notes successfully updated.');
                } else {
                    this.$noty.error(response.data.error_message);
                }
            });
        },
        loadPrevious() {
            if (this.note.index === null) {
                if (this.notes.length > 0) {
                    var note = {
                        id: this.notes[0].id,
                        header: this.headers.find((x) => x.fieldKey === this.notes[0].header),
                        value: this.notes[0].value,
                        index: this.notes[0].index,
                    };

                    this.note = note;
                    this.error_server_msg2 = [];
                    this.form_mode = 'update';
                }
            } else {
                let index = this.note.index;

                if (this.notes.length > 0) {
                    let last = this.notes.length - 1;

                    if (index != 0) {
                        var note = {
                            id: this.notes[index - 1].id,
                            header: this.headers.find((x) => x.fieldKey === this.notes[index - 1].header),
                            value: this.notes[index - 1].value,
                            index: this.notes[index - 1].index,
                        };
                    } else {
                        var note = {
                            id: this.notes[last].id,
                            header: this.headers.find((x) => x.fieldKey === this.notes[last].header),
                            value: this.notes[last].value,
                            index: this.notes[last].index,
                        };
                    }

                    this.note = note;
                    this.error_server_msg2 = [];
                    this.form_mode = 'update';
                }
            }
        },
        loadNext() {
            if (this.note.index === null) {
                if (this.notes.length > 0) {
                    var note = {
                        id: this.notes[0].id,
                        header: this.headers.find((x) => x.fieldKey === this.notes[0].header),
                        value: this.notes[0].value,
                        index: this.notes[0].index,
                    };

                    this.note = note;
                    this.error_server_msg2 = [];
                    this.form_mode = 'update';
                }
            } else {
                let index = this.note.index;

                if (this.notes.length > 0) {
                    index = index + 1;

                    if (index > this.notes.length - 1) {
                        index = 0;
                    }

                    var note = {
                        id: this.notes[index].id,
                        header: this.headers.find((x) => x.fieldKey === this.notes[index].header),
                        value: this.notes[index].value,
                        index: this.notes[index].index,
                    };

                    this.note = note;
                    this.error_server_msg2 = [];
                    this.form_mode = 'update';
                }
            }
        },
        showLogModal() {
            this.show_change_log_modal = true;
        },

        //
    },

    watch: {
        selected_property_code: function () {
            this.loadNotesList();
            this.edit_form = false;
        },
        selected_lease_code: function () {
            this.loadNotesList();
            this.edit_form = false;
        },
        reloadComponents: function () {
            this.loadNotesList();
        },
    },
    mixins: [global_mixins],
};
</script>
