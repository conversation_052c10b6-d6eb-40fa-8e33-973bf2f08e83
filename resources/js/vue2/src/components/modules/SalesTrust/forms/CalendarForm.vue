<template>
    <v-container
        fluid
        class="c8-page"
    >
        <div>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>

            <!-- panel start -->

            <!--            <v-expansion-panels>-->
            <!--                <v-expansion-panel>-->
            <!--                    <v-expansion-panel-header>Calendar</v-expansion-panel-header>-->
            <!--                    <v-expansion-panel-content>-->
            <!--                        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>-->
            <!--                        <div v-if="!loading_setting" class="page-form">-->
            <!--                            <div class="page-list">-->
            <!--                                <div class="c8-page-table">-->
            <!--                                    <table border="1" style="border-style: solid;">-->
            <!--                                        <tbody>-->
            <!--                                        <tr class="c8-page-table-sub-header">-->
            <!--                                            <td colspan="7">Historical Years</td>-->
            <!--                                        </tr>-->
            <!--                                        <tr class="c8-page-table-row-header">-->
            <!--                                            <td>Year</td>-->
            <!--                                            <td>Period</td>-->
            <!--                                            <td>Start Date</td>-->
            <!--                                            <td>End Date</td>-->
            <!--                                            <td></td>-->
            <!--                                            <td></td>-->
            <!--                                            <td>Year Status</td>-->
            <!--                                        </tr>-->
            <!--                                        <tr class="c8-page-table-sub-header">-->
            <!--                                            <td colspan="7">Current Year</td>-->
            <!--                                        </tr>-->
            <!--                                        <tr class="c8-page-table-row-header">-->
            <!--                                            <td>Year</td>-->
            <!--                                            <td>Period</td>-->
            <!--                                            <td>Start Date</td>-->
            <!--                                            <td>End Date</td>-->
            <!--                                            <td align="center">Update</td>-->
            <!--                                            <td>Period Status</td>-->
            <!--                                            <td>GL Period Status</td>-->
            <!--                                        </tr>-->
            <!--                                        <tr v-for="(ledger_calendar_period, index) in ledger_calendar_periods" :key="index">-->
            <!--                                            <td>{{ ledger_calendar_period.year }}</td>-->
            <!--                                            <td>{{ ledger_calendar_period.period }}</td>-->

            <!--                                            <td v-if="ledger_calendar_period.period_closed == 1">{{ ledger_calendar_period.start_date }}</td>-->
            <!--                                            <td v-else>-->
            <!--                                                <cirrus-icon-date-picker :size="'40'" :id="'start_date'" v-model="ledger_calendar_period.start_date"></cirrus-icon-date-picker>-->
            <!--                                            </td>-->

            <!--                                            <td v-if="ledger_calendar_period.period_closed == 1">{{ ledger_calendar_period.end_date }}</td>-->
            <!--                                            <td v-else>-->
            <!--                                                <cirrus-icon-date-picker :size="'40'" :id="'end_date'" v-model="ledger_calendar_period.end_date"></cirrus-icon-date-picker>-->
            <!--                                            </td>-->

            <!--                                            <td v-if="ledger_calendar_period.period_closed != 1" align="center">-->
            <!--                                                <div>-->
            <!--                                                    <v-btn class="v-step-save-1-button grey&#45;&#45;text text&#45;&#45;darken-2" @click="updateStartAndEndDates(index)"-->
            <!--                                                           color="grey lighten-2"-->
            <!--                                                           dark-->
            <!--                                                           right-->
            <!--                                                           x-small-->
            <!--                                                           font-->
            <!--                                                    >-->
            <!--                                                        save-->
            <!--                                                    </v-btn>-->
            <!--                                                </div>-->
            <!--                                            </td>-->
            <!--                                            <td v-else></td>-->
            <!--                                            <td>-->
            <!--                                                <img v-if="ledger_calendar_period.period_closed == 1" src="assets/images/icons/accept.png" alt="Down" class="icon" />-->
            <!--                                                <img v-else src="assets/images/icons/open.png" alt="Down" class="icon" />-->
            <!--                                                |-->
            <!--                                                <img src="assets/images/icons/up.gif" alt="Up" class="icon" />-->
            <!--                                                <img src="assets/images/icons/down.gif" alt="Down" class="icon" />-->
            <!--                                                |-->
            <!--                                                <a v-if="ledger_calendar_period.period_closed == 1" href="#">Open</a>-->
            <!--                                                <a v-else href="#">Close</a>-->
            <!--                                            </td>-->
            <!--                                            <td>-->
            <!--                                                <img v-if="ledger_calendar_period.gl_period_closed == 1" src="assets/images/icons/accept.png" alt="Down" class="icon" />-->
            <!--                                                <img v-else src="assets/images/icons/open.png" alt="Down" class="icon" />-->
            <!--                                                |-->
            <!--                                                <img src="assets/images/icons/up.gif" alt="Up" class="icon" />-->
            <!--                                                <img src="assets/images/icons/down.gif" alt="Down" class="icon" />-->
            <!--                                                |-->
            <!--                                                <a v-if="ledger_calendar_period.gl_period_closed == 1" href="#">Open</a>-->
            <!--                                                <a v-else href="#">Close</a>-->
            <!--                                            </td>-->
            <!--                                        </tr>-->
            <!--                                        <tr colspan="100%" class="c8-page-table-footer">-->
            <!--                                            <td colspan="6" style="font-weight: bold">-->
            <!--                                                <div style="vertical-align: center;">-->
            <!--                                                    Start from the <div style="display: inline-block; width: 250px; height: 50px" ><cirrus-single-select v-model="master_calendar_type" :options="dd_master_calendar_type_list"/></div>-->
            <!--                                                    year ending <div style="display: inline-block; width: 250px; height: 50px" ><cirrus-single-select v-model="calendar_year" :options="dd_calendar_year_list"/></div>-->
            <!--                                                </div>-->
            <!--                                            </td>-->
            <!--                                            <td>-->
            <!--                                                <v-btn class="v-step-save-1-button" @click="addCalendarPeriods()"-->
            <!--                                                       color="primary"-->
            <!--                                                       dark-->
            <!--                                                       right-->
            <!--                                                       small-->
            <!--                                                >-->
            <!--                                                    Add Periods-->
            <!--                                                </v-btn>-->
            <!--                                            </td>-->
            <!--                                        </tr>-->
            <!--                                        </tbody>-->
            <!--                                    </table>-->
            <!--                                </div>-->
            <!--                            </div>-->
            <!--                        </div>-->
            <!--                    </v-expansion-panel-content>-->
            <!--                </v-expansion-panel>-->
            <!--            </v-expansion-panels>-->

            <!-- panel end -->

            <v-card
                v-on:click="isCalendarHidden = !isCalendarHidden"
                id="bank_details_header"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <!--                    <h4 class="title font-weight-black">Calendar</h4>-->
                    <span class="title font-weight-black">Calendar</span>
                    <v-spacer></v-spacer>
                    <v-icon
                        v-if="!isCalendarHidden"
                        small
                    >
                        mdi-chevron-up
                    </v-icon>
                    <v-icon
                        v-else
                        small
                    >
                        mdi-chevron-down
                    </v-icon>
                </v-card-actions>
            </v-card>
            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div
                    class="page-list"
                    v-if="!isCalendarHidden"
                >
                    <div class="c8-page-table">
                        <table
                            border="1"
                            style="border-style: solid"
                        >
                            <tbody>
                                <tr class="c8-page-table-sub-header">
                                    <td colspan="6">Historical Years</td>
                                    <td style="text-align: right">
                                        <v-btn
                                            x-small
                                            icon
                                            @click="openLedgerChangelogModal()"
                                            style="padding-right: 5px"
                                        >
                                            <v-icon>history</v-icon>
                                        </v-btn>
                                    </td>
                                </tr>
                                <tr class="c8-page-table-row-header">
                                    <td>Year</td>
                                    <td>Period</td>
                                    <td>Start Date</td>
                                    <td>End Date</td>
                                    <td></td>
                                    <td></td>
                                    <td>Year Status</td>
                                </tr>
                                <!--                            <tr v-for="(property_calendar, index) in property_calendars" :key="index">-->
                                <!--                                <td>{{ property_calendar.year }}</td>-->
                                <!--                                <td>{{ property_calendar.period }}</td>-->
                                <!--                                <td>{{ property_calendar.start_date }}</td>-->
                                <!--                                <td>{{ property_calendar.end_date }}</td>-->
                                <!--                                <td>(update)</td>-->
                                <!--                                <td>(status actions)</td>-->
                                <!--                            </tr>-->
                                <tr class="c8-page-table-sub-header">
                                    <td colspan="7">Current Year</td>
                                </tr>
                                <tr class="c8-page-table-row-header">
                                    <td>Year</td>
                                    <td>Period</td>
                                    <td>Start Date</td>
                                    <td>End Date</td>
                                    <td align="center">Update</td>
                                    <td>Period Status</td>
                                    <td>GL Period Status</td>
                                </tr>
                                <tr
                                    class="c8-page-table-calendar-sub-row"
                                    v-for="(ledger_calendar_period, index) in ledger_calendar_periods"
                                    :key="index"
                                >
                                    <td>{{ ledger_calendar_period.year }}</td>
                                    <td>{{ ledger_calendar_period.period }}</td>

                                    <td v-if="ledger_calendar_period.period_closed == 1">
                                        {{ ledger_calendar_period.start_date }}
                                    </td>
                                    <td v-else>
                                        <!--                                    <cirrus-icon-date-picker :size="'40'" :id="'start_date'" v-model="ledger_calendar_period.start_date"></cirrus-icon-date-picker>-->
                                        <!-- O -->
                                        <cirrus-icon-date-picker
                                            :size="'40'"
                                            :id="'start_date_' + index"
                                            v-model="ledger_calendar_period.start_date"
                                        ></cirrus-icon-date-picker>
                                        <!-- U -->
                                    </td>

                                    <td v-if="ledger_calendar_period.period_closed == 1">
                                        {{ ledger_calendar_period.end_date }}
                                    </td>
                                    <td v-else>
                                        <!--                                    <cirrus-icon-date-picker :size="'40'" :id="'end_date'" v-model="ledger_calendar_period.end_date"></cirrus-icon-date-picker>-->
                                        <!-- O -->
                                        <cirrus-icon-date-picker
                                            :size="'40'"
                                            :id="'end_date_' + index"
                                            v-model="ledger_calendar_period.end_date"
                                        ></cirrus-icon-date-picker>
                                        <!-- U -->
                                    </td>

                                    <td
                                        v-if="ledger_calendar_period.period_closed != 1"
                                        align="center"
                                    >
                                        <div>
                                            <v-btn
                                                class="v-step-save-1-button grey--text text--darken-2"
                                                @click="updateStartAndEndDates(index)"
                                                color="grey lighten-2"
                                                dark
                                                right
                                                x-small
                                                font
                                            >
                                                save
                                            </v-btn>
                                        </div>
                                    </td>
                                    <td v-else></td>
                                    <td>
                                        <img
                                            v-if="ledger_calendar_period.period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/accept.png'"
                                            alt="Down"
                                            class="icon"
                                        />
                                        <img
                                            v-else
                                            :src="asset_domain + 'assets/images/icons/open.png'"
                                            alt="Down"
                                            class="icon"
                                        />
                                        |
                                        <img
                                            v-if="ledger_calendar_period.period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/up.gif'"
                                            alt="Up"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'open_up',
                                                    'open',
                                                    'openUp',
                                                )
                                            "
                                        />
                                        <img
                                            v-else
                                            :src="asset_domain + 'assets/images/icons/up.gif'"
                                            alt="Up"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close_up',
                                                    'close',
                                                    'closeUp',
                                                )
                                            "
                                        />
                                        <img
                                            v-if="ledger_calendar_period.period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/down.gif'"
                                            alt="Down"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'open_down',
                                                    'open',
                                                    'openDown',
                                                )
                                            "
                                        />
                                        <img
                                            v-else
                                            :src="asset_domain + 'assets/images/icons/down.gif'"
                                            alt="Down"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close_down',
                                                    'close',
                                                    'closeDown',
                                                )
                                            "
                                        />
                                        |
                                        <a
                                            v-if="ledger_calendar_period.period_closed == 1"
                                            href="#"
                                            @click="
                                                showConfirmationDialog(ledger_calendar_period, 'open', 'open', 'open')
                                            "
                                        >
                                            Open
                                        </a>
                                        <a
                                            v-else
                                            href="#"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close',
                                                    'close',
                                                    'close',
                                                )
                                            "
                                        >
                                            Close
                                        </a>
                                    </td>
                                    <td>
                                        <img
                                            v-if="ledger_calendar_period.gl_period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/accept.png'"
                                            alt="Down"
                                            class="icon"
                                        />
                                        <img
                                            v-else
                                            :src="asset_domain + 'assets/images/icons/open.png'"
                                            alt="Down"
                                            class="icon"
                                        />
                                        |
                                        <img
                                            v-if="ledger_calendar_period.gl_period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/up.gif'"
                                            alt="Up"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'open_up',
                                                    'open',
                                                    'openGLUp',
                                                )
                                            "
                                        />
                                        <img
                                            v-else
                                            :src="asset_domain + 'assets/images/icons/up.gif'"
                                            alt="Up"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close_up',
                                                    'close',
                                                    'closeGLUp',
                                                )
                                            "
                                        />

                                        <img
                                            v-if="ledger_calendar_period.gl_period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/down.gif'"
                                            alt="Down"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'open_down',
                                                    'open',
                                                    'openGLDown',
                                                )
                                            "
                                        />
                                        <img
                                            v-else
                                            :src="asset_domain + 'assets/images/icons/down.gif'"
                                            alt="Down"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close_down',
                                                    'close',
                                                    'closeGLDown',
                                                )
                                            "
                                        />
                                        |
                                        <a
                                            v-if="ledger_calendar_period.gl_period_closed == 1"
                                            href="#"
                                            @click="
                                                showConfirmationDialog(ledger_calendar_period, 'open', 'open', 'openGL')
                                            "
                                        >
                                            Open
                                        </a>
                                        <a
                                            v-else
                                            href="#"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close',
                                                    'close',
                                                    'closeGL',
                                                )
                                            "
                                        >
                                            Close
                                        </a>
                                    </td>
                                </tr>
                                <tr class="c8-page-table-footer">
                                    <!--colspan="100%"-->
                                    <td
                                        colspan="6"
                                        style="font-weight: bold"
                                    >
                                        <!--                                    <div style="vertical-align: center;">-->
                                        <!--                                        Start from the-->
                                        <!--&lt;!&ndash;                                        <div style="display: inline-block; width: 250px; height: 50px" >&ndash;&gt;-->
                                        <!--                                        <div class="d-inline-block" style=" width: 250px; height: 50px;">-->
                                        <!--                                            <cirrus-single-select v-model="master_calendar_type" :options="dd_master_calendar_type_list"/>-->
                                        <!--                                        </div>-->
                                        <!--                                        year ending-->
                                        <!--&lt;!&ndash;                                        <div style="display: inline-block; width: 250px; height: 50px" >&ndash;&gt;-->
                                        <!--                                        <div class="d-inline-block" style=" width: 250px; height: 50px;">-->
                                        <!--                                            <cirrus-single-select v-model="calendar_year" :options="dd_calendar_year_list"/>-->
                                        <!--                                        </div>-->
                                        <!--                                    </div>-->
                                        <div>
                                            <v-row
                                                class="form-row"
                                                style="border-bottom: 0px !important"
                                            >
                                                <v-col
                                                    xs="12"
                                                    sm="2"
                                                    md="2"
                                                    class="form-label"
                                                    >Start from the</v-col
                                                >
                                                <v-col
                                                    xs="12"
                                                    sm="4"
                                                    md="4"
                                                    class="form-input"
                                                >
                                                    <cirrus-single-select
                                                        v-model="master_calendar_type"
                                                        :options="dd_master_calendar_type_list"
                                                    />
                                                </v-col>
                                                <v-col
                                                    xs="12"
                                                    sm="2"
                                                    md="2"
                                                    class="form-label"
                                                    >year ending</v-col
                                                >
                                                <v-col
                                                    xs="12"
                                                    sm="4"
                                                    md="4"
                                                    class="form-input"
                                                >
                                                    <cirrus-single-select
                                                        v-model="calendar_year"
                                                        :options="dd_calendar_year_list"
                                                    />
                                                </v-col>
                                            </v-row>
                                        </div>
                                    </td>
                                    <td>
                                        <v-btn
                                            class="v-step-save-1-button"
                                            @click="addCalendarPeriods()"
                                            color="primary"
                                            dark
                                            right
                                            small
                                        >
                                            Add Periods
                                        </v-btn>
                                    </td>

                                    <!-- try -->
                                    <!--                                <td>-->
                                    <!--                                    <v-card>-->
                                    <!--                                        <v-card-actions>-->
                                    <!--                                            <div>-->
                                    <!--                                                Start from the <div style="display: inline-block; width: 250px; height: 50px" ><cirrus-single-select v-model="master_calendar_type" :options="dd_master_calendar_type_list"/></div>-->
                                    <!--                                                year ending <div style="display: inline-block; width: 250px; height: 50px" ><cirrus-single-select v-model="calendar_year" :options="dd_calendar_year_list"/></div>-->
                                    <!--                                            </div>-->
                                    <!--                                            <v-spacer></v-spacer>-->
                                    <!--                                            <v-btn class="v-step-save-1-button" @click="addCalendarPeriods()"-->
                                    <!--                                                   color="primary"-->
                                    <!--                                                   dark-->
                                    <!--                                                   right-->
                                    <!--                                                   small-->
                                    <!--                                            >-->
                                    <!--                                                Add Periods-->
                                    <!--                                            </v-btn>-->
                                    <!--                                        </v-card-actions>-->
                                    <!--                                    </v-card>-->
                                    <!--                                </td>-->
                                    <!-- try -->
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!--try here-->
                    <!--                    <v-card-title>-->
                    <!--                        <span>Filter via Status:</span>-->
                    <!--                    </v-card-title>-->
                    <!--try here-->
                </div>
            </div>
        </div>

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon> WARNING
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogConfirmation = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">{{ warning_message }}</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="updateCalendar"
                        >Ok</v-btn
                    >
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogConfirmation = false"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->

        <!-- CALENDAR CHANGELOG -->
        <v-dialog
            top
            v-model="show_change_log_modal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Calendar Changelog</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="show_change_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <activity-logs-component
                            v-if="show_change_log_modal"
                            :property_code="selected_property_code"
                            :form_section="'calendar'"
                        ></activity-logs-component>
                        <!--//ledger-update-log-component v-if="show_change_log_modal" :property_code="selected_property_code" :form_section="'calendar'"></ledger-update-log-component//-->
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF CALENDAR CHANGELOG -->
    </v-container>
</template>

<script>
import Vue from 'vue';
import vSelect from 'vue-select';
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import moment from 'moment';

const Swal = require('sweetalert2');
Vue.component('v-select', vSelect);
Vue.component('ledger-update-log-component', require('./LedgerUpdateLog.vue').default);
Vue.component('activity-logs-component', require('./ActivityLogs.vue').default);

export default {
    props: {
        selected_property_code: { type: String, default: '' },
        reloadComponents: { type: Number, default: 0 },
    },
    data() {
        return {
            loading_setting: false,
            isCalendarHidden: true,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            asset_domain: this.$assetDomain,

            ledger_calendar_periods: {},
            master_calendar_type: '',
            dd_master_calendar_type_list: [],
            calendar_year: '',
            dd_calendar_year_list: [],

            start_date: '',
            end_date: '',

            confirmation_label: ' ',
            dialogConfirmation: '',
            period: '',
            year: '',
            action_type: '',
            action_value: '',
            warning_message: '',

            warning_close:
                'The calendar period will be closed. Calendars are normally managed at the global level. Would you like to continue with the change?',
            warning_close_up:
                'The calendar period will be closed and all prior periods for this year. Would you like to continue with the change?',
            warning_close_down:
                'The calendar period will be closed and all succeeding periods for this year. Would you like to continue with the change?',

            warning_open:
                'The calendar period will be opened, this may effect prior period reporting and current transactions being entered. Calendars are normally managed at the global level. Would you like to continue with the change?',
            warning_open_up:
                'The calendar period will be opened and all prior periods, this may effect prior period reporting and current transactions being entered. Would you like to continue with the change?',
            warning_open_down:
                'The calendar period will be opened and all succeeding periods, this may effect prior period reporting and current transactions being entered. Would you like to continue with the change?',

            show_change_log_modal: false,
        };
    },
    mounted() {
        this.loadCalendarPeriods();
        this.generateYearList();
        this.fetchMasterCalendarTypeList();
        // this.calendar_year = '2020';
        this.calendar_year = new Date().getFullYear();
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url']),
    },
    methods: {
        ...mapActions([]),
        ...mapMutations([]),
        generateYearList() {
            // let current_year = new Date().getFullYear();
            // let min_year = -4;
            // let max_year = 20;
            // for (let i = min_year; i < max_year; i++)
            // {
            //     this.dd_calendar_year_list.push({value: current_year+i, label: current_year+i});
            // }

            var form_data = new FormData();
            form_data.append('no_load', true);
            let api_url = 'parameter/fetch/property-calendar';
            this.$api.post(api_url, form_data).then((response) => {
                let calendar_list = response.data.calendar_list;
                let calendar_year_list = [];

                $.each(calendar_list, function (item, value) {
                    calendar_year_list.push({ value: value.field_key, label: value.field_value });
                });

                this.dd_calendar_year_list = calendar_year_list;
            });
        },

        updateStartAndEndDates(index) {
            let validate = true;

            let start_date = moment(this.ledger_calendar_periods[index].start_date, 'DD/MM/YYYY');
            let end_date = moment(this.ledger_calendar_periods[index].end_date, 'DD/MM/YYYY');
            let year = this.ledger_calendar_periods[index].year;
            let period = this.ledger_calendar_periods[index].period;
            let next_start_date = '';
            let next_end_date = '';
            let next_year = '';
            let next_period = '';

            if (typeof this.ledger_calendar_periods[index + 1] !== 'undefined') {
                next_start_date = moment(this.ledger_calendar_periods[index + 1].start_date, 'DD/MM/YYYY');
                next_end_date = moment(this.ledger_calendar_periods[index + 1].end_date, 'DD/MM/YYYY');
                next_year = this.ledger_calendar_periods[index + 1].year;
                next_period = this.ledger_calendar_periods[index + 1].period;
            }

            // validations
            if (start_date.isAfter(end_date)) {
                this.$noty.error('End date cannot be less than its corresponding start date');
                validate = false;
            }

            if (end_date.diff(start_date, 'days') < 3) {
                this.$noty.error('Start date is either too close to, or greater than the end date for the next period');
                validate = false;
            }

            if (
                typeof this.ledger_calendar_periods[index + 1] !== 'undefined' &&
                next_end_date.diff(end_date, 'days') < 3
            ) {
                this.$noty.error('End date is either too close to, or greater than the end date for the next period');
                validate = false;
            }

            if (validate) {
                //update start and end dates
                this.loading_setting = true;
                this.dialogConfirmation = false;
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('property_code', this.selected_property_code);
                formData.append('year', year);
                formData.append('period', period);
                formData.append('start_date', this.ledger_calendar_periods[index].start_date);
                formData.append('end_date', this.ledger_calendar_periods[index].end_date);

                if (index + 1 in this.ledger_calendar_periods) {
                    formData.append('next_start_date', this.ledger_calendar_periods[index + 1].start_date);
                    formData.append('next_end_date', this.ledger_calendar_periods[index + 1].end_date);
                }

                formData.append('next_year', next_year);
                formData.append('next_period', next_period);
                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/update-calendar-start-and-end';

                /*axios.post(apiUrl, formData).then(response => {
                        let status = response.data.status;
                        if (status == 'success') {
                            setTimeout(this.reloadCalendarPeriods(),5000);
                            this.$noty.success('Calendar periods successfully updated.');

                        } else {
                            this.loading_setting = false;
                        }
                    });*/

                this.$api.post(apiUrl, formData).then((response) => {
                    let status = response.data.status;
                    if (status == 'success') {
                        setTimeout(this.reloadCalendarPeriods(), 5000);
                        this.$noty.success('Calendar periods successfully updated.');
                    } else {
                        this.loading_setting = false;
                    }
                });
            }
        },

        reloadCalendarPeriods: function () {
            this.loadCalendarPeriods();
            this.loading_setting = false;
        },

        addCalendarPeriods() {
            let hasErrors = false;
            if (this.master_calendar_type == '') {
                this.$noty.error('You have not specified a calendar type.');
                hasErrors = true;
            }
            if (this.calendar_year == '') {
                this.$noty.error('You have not specified a calendar year.');
                hasErrors = true;
            }

            // submit form if no error
            if (!hasErrors) {
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('master_calendar_type', this.master_calendar_type);
                formData.append('calendar_year', this.calendar_year);
                formData.append('property_code', this.selected_property_code);
                formData.append('no_load', true);

                //update or create sub ledger details
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/add-calendar-periods';

                /*axios.post(apiUrl, formData).then(response => {
                        this.status = response.data.status;
                        if (this.status == 'success') {
                            this.$noty.success('Calendar periods added.');
                            this.loadCalendarPeriods();
                            this.loading_setting = false;
                        } else {
                            this.$noty.error(response.data.error_message);
                            this.loading_setting = false;
                        }
                    });*/

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        let ve = response.data.validation_errors;
                        let noty = this.$noty;

                        if (ve.length > 0) {
                            ve.forEach(function (error) {
                                noty.error(error);
                            });
                        } else {
                            this.$noty.success('Calendar periods added.');
                            this.loadCalendarPeriods();
                        }

                        this.loading_setting = false;
                    } else {
                        this.$noty.error(response.data.error_message);
                        this.loading_setting = false;
                    }
                });
            }
        },

        loadCalendarPeriods() {
            if (this.selected_property_code == '') return true;

            this.loading_setting = true;

            // reset error
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);

            formData.append('property_code', this.selected_property_code);

            // get ledger calendar periods
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-calendar-periods';

            /*axios.post(apiUrl, formData).then(response => {
                    this.ledger_calendar_periods = response.data;
                    this.loading_setting = false;
                });*/

            this.$api.post(apiUrl, formData).then((response) => {
                this.ledger_calendar_periods = response.data;
                this.loading_setting = false;
            });
        },

        fetchMasterCalendarTypeList() {
            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/master-calendar-type-list';

            /*axios.post(apiUrl, formData).then(response => {
                    this.dd_master_calendar_type_list = response.data;
                });*/

            this.$api.post(apiUrl, formData).then((response) => {
                this.dd_master_calendar_type_list = response.data;
            });
        },

        showConfirmationDialog: function (data, warning_type, action_type, action_value) {
            this.period = data.period;
            this.year = data.year;
            this.action_value = action_value;
            this.action_type = action_type;
            switch (warning_type) {
                case 'close':
                    this.warning_message = this.warning_close;
                    break;
                case 'close_up':
                    this.warning_message = this.warning_close_up;
                    break;
                case 'close_down':
                    this.warning_message = this.warning_close_down;
                    break;
                case 'open':
                    this.warning_message = this.warning_open;
                    break;
                case 'open_up':
                    this.warning_message = this.warning_open_up;
                    break;
                case 'open_down':
                    this.warning_message = this.warning_open_down;
                    break;
            }
            this.dialogConfirmation = true;
        },

        updateCalendar: function () {
            if (this.action_type == 'close') {
                this.closeCalendarPeriods();
            } else {
                this.openCalendarPeriods();
            }

            this.loadCalendarPeriods();
        },

        closeCalendarPeriods: function () {
            this.loading_setting = true;
            this.dialogConfirmation = false;
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('period', this.period);
            formData.append('year', this.year);
            formData.append('property_code', this.selected_property_code);
            formData.append('close_type', this.action_value);
            formData.append('no_load', true);
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/close-calendar-periods';

            /*axios.post(apiUrl, formData).then(response => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        this.$noty.success('Calendar periods successfully closed.');
                        this.loadCalendarPeriods();
                        this.loading_setting = false;
                    } else {
                        this.dialogConfirmation = false;
                        this.loading_setting = false;
                    }
                });*/

            this.$api.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                if (this.status == 'success') {
                    this.$noty.success('Calendar periods successfully closed.');
                    this.loadCalendarPeriods();
                    this.loading_setting = false;
                } else {
                    this.dialogConfirmation = false;
                    this.loading_setting = false;
                }
            });
        },

        openCalendarPeriods: function () {
            this.loading_setting = true;
            this.dialogConfirmation = false;
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('period', this.period);
            formData.append('year', this.year);
            formData.append('property_code', this.selected_property_code);
            formData.append('open_type', this.action_value);
            formData.append('no_load', true);
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/open-calendar-periods';

            /*axios.post(apiUrl, formData).then(response => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        this.$noty.success('Calendar periods successfully opened.');
                        this.loadCalendarPeriods();
                        this.loading_setting = false;
                    } else {
                        this.loading_setting = false;
                    }
                });*/

            this.$api.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                if (this.status == 'success') {
                    this.$noty.success('Calendar periods successfully opened.');
                    this.loadCalendarPeriods();
                    this.loading_setting = false;
                } else {
                    this.loading_setting = false;
                }
            });
        },

        openLedgerChangelogModal: function () {
            this.show_change_log_modal = true;
        },
    },

    watch: {
        selected_property_code: function () {
            this.loadCalendarPeriods();
        },
        reloadComponents: function () {
            this.loadCalendarPeriods();
        },
    },
    mixins: [global_mixins],
};
</script>
