<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>
            <v-card
                id="agency_charges_details_header"
                class="section-toolbar"
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6
                        class="title font-weight-black"
                        v-on:click="isCalendarHidden = !isCalendarHidden"
                        style="cursor: pointer"
                    >
                        Calendar
                    </h6>
                    &nbsp&nbsp

                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        data-tooltip="Edit"
                        v-if="!isCalendarHidden && !edit_form"
                        class="v-step-edit-button"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Done"
                        v-if="!isCalendarHidden && edit_form"
                        class="v-step-save-1-button"
                        icon
                        @click="closeEditing()"
                    >
                        <v-icon
                            light
                            color="green"
                            >check
                        </v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Refresh"
                        v-if="!isCalendarHidden"
                        class="v-step-refresh-button"
                        icon
                        @click="loadCalendarPeriods()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        icon
                        data-tooltip="View Changelog"
                        v-if="!isCalendarHidden"
                        @click="openLedgerChangelogModal()"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-if="isCalendarHidden"
                        class="v-step-refresh-button"
                        icon
                        @click="isCalendarHidden = false"
                    >
                        <v-icon>mdi-chevron-down</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-else
                        class="v-step-refresh-button"
                        icon
                        @click="isCalendarHidden = true"
                    >
                        <v-icon>mdi-chevron-up</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div
                    class="page-list"
                    v-if="!isCalendarHidden"
                >
                    <div class="c8-page-table">
                        <table
                            border="1"
                            style="border-style: solid"
                        >
                            <tbody>
                                <tr class="c8-page-table-sub-header">
                                    <td colspan="7">Property Calendar</td>
                                </tr>
                                <tr class="c8-page-table-row-header">
                                    <td>Year</td>
                                    <td>Periods</td>
                                    <td>Start Date</td>
                                    <td>End Date</td>
                                    <td></td>
                                    <td></td>
                                    <td>Year Status</td>
                                </tr>
                                <tr
                                    class="selectRow"
                                    v-for="(historical_year, index) in historical_years"
                                    style="cursor: pointer"
                                    @click="loadPeriods(historical_year.year)"
                                >
                                    <td>{{ historical_year.year }}</td>
                                    <td>{{ historical_year.periods }}</td>
                                    <td>{{ historical_year.start_date }}</td>
                                    <td>{{ historical_year.end_date }}</td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <img
                                            v-if="historical_year.period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/accept.png'"
                                            alt="Closed"
                                            class="icon"
                                        />
                                    </td>
                                </tr>
                                <tr class="c8-page-table-sub-header">
                                    <td colspan="7">Selected Calendar</td>
                                </tr>
                                <tr class="c8-page-table-row-header">
                                    <td>Year</td>
                                    <td>Period</td>
                                    <td>Start Date</td>
                                    <td>End Date</td>
                                    <td align="center"><span v-if="edit_form">Update</span></td>
                                    <td>Period Status</td>
                                    <td>GL Period Status</td>
                                </tr>
                                <tr
                                    class="c8-page-table-calendar-sub-row"
                                    v-for="(ledger_calendar_period, index) in ledger_calendar_periods"
                                    :key="index"
                                >
                                    <td>{{ ledger_calendar_period.year }}</td>
                                    <td>{{ ledger_calendar_period.period }}</td>

                                    <td v-if="ledger_calendar_period.period_closed == 1">
                                        {{ ledger_calendar_period.start_date }}
                                    </td>
                                    <td v-else>
                                        <span v-if="!edit_form">{{ ledger_calendar_period.start_date }}</span>
                                        <cirrus-icon-date-picker
                                            v-else
                                            :size="'40'"
                                            :id="'start_date_' + index"
                                            v-model="ledger_calendar_period.start_date"
                                        ></cirrus-icon-date-picker>
                                    </td>

                                    <td v-if="ledger_calendar_period.period_closed == 1">
                                        {{ ledger_calendar_period.end_date }}
                                    </td>
                                    <td v-else>
                                        <span v-if="!edit_form">{{ ledger_calendar_period.end_date }}</span>
                                        <cirrus-icon-date-picker
                                            v-else
                                            :size="'40'"
                                            :id="'end_date_' + index"
                                            v-model="ledger_calendar_period.end_date"
                                        ></cirrus-icon-date-picker>
                                    </td>

                                    <td
                                        v-if="ledger_calendar_period.period_closed != 1"
                                        align="center"
                                    >
                                        <div>
                                            <v-btn
                                                v-if="edit_form"
                                                class="v-step-save-1-button grey--text text--darken-2"
                                                @click="updateStartAndEndDates(index)"
                                                color="grey lighten-2"
                                                dark
                                                right
                                                x-small
                                                font
                                            >
                                                save
                                            </v-btn>
                                        </div>
                                    </td>
                                    <td v-else></td>
                                    <td>
                                        <img
                                            v-if="ledger_calendar_period.period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/accept.png'"
                                            alt="Closed"
                                            class="icon"
                                        />
                                        <img
                                            v-else
                                            :src="asset_domain + 'assets/images/icons/open.png'"
                                            alt="Open"
                                            class="icon"
                                        />

                                        <span v-if="edit_form">|</span>
                                        <img
                                            v-if="ledger_calendar_period.period_closed == 1"
                                            v-show="edit_form"
                                            :src="asset_domain + 'assets/images/icons/up.gif'"
                                            alt="Up"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'open_up',
                                                    'open',
                                                    'openUp',
                                                )
                                            "
                                        />
                                        <img
                                            v-else
                                            v-show="edit_form"
                                            :src="asset_domain + 'assets/images/icons/up.gif'"
                                            alt="Up"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close_up',
                                                    'close',
                                                    'closeUp',
                                                )
                                            "
                                        />

                                        <img
                                            v-if="ledger_calendar_period.period_closed == 1"
                                            v-show="edit_form"
                                            :src="asset_domain + 'assets/images/icons/down.gif'"
                                            alt="Down"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'open_down',
                                                    'open',
                                                    'openDown',
                                                )
                                            "
                                        />
                                        <img
                                            v-else
                                            v-show="edit_form"
                                            :src="asset_domain + 'assets/images/icons/down.gif'"
                                            alt="Down"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close_down',
                                                    'close',
                                                    'closeDown',
                                                )
                                            "
                                        />
                                        <span v-if="edit_form">|</span>

                                        <a
                                            v-if="ledger_calendar_period.period_closed == 1"
                                            v-show="edit_form"
                                            href="#"
                                            @click="
                                                showConfirmationDialog(ledger_calendar_period, 'open', 'open', 'open')
                                            "
                                        >
                                            Open
                                        </a>
                                        <a
                                            v-else
                                            v-show="edit_form"
                                            href="#"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close',
                                                    'close',
                                                    'close',
                                                )
                                            "
                                        >
                                            Close
                                        </a>
                                    </td>
                                    <td v-if="ledger_calendar_period.current == 1">
                                        <img
                                            v-if="ledger_calendar_period.gl_period_closed == 1"
                                            :src="asset_domain + 'assets/images/icons/accept.png'"
                                            alt="Down"
                                            class="icon"
                                        />
                                        <img
                                            v-else
                                            :src="asset_domain + 'assets/images/icons/open.png'"
                                            alt="Down"
                                            class="icon"
                                        />

                                        <span v-if="edit_form">|</span>
                                        <img
                                            v-if="ledger_calendar_period.gl_period_closed == 1"
                                            v-show="edit_form"
                                            :src="asset_domain + 'assets/images/icons/up.gif'"
                                            alt="Up"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'open_up',
                                                    'open',
                                                    'openGLUp',
                                                )
                                            "
                                        />
                                        <img
                                            v-else
                                            v-show="edit_form"
                                            :src="asset_domain + 'assets/images/icons/up.gif'"
                                            alt="Up"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close_up',
                                                    'close',
                                                    'closeGLUp',
                                                )
                                            "
                                        />

                                        <img
                                            v-if="ledger_calendar_period.gl_period_closed == 1"
                                            v-show="edit_form"
                                            :src="asset_domain + 'assets/images/icons/down.gif'"
                                            alt="Down"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'open_down',
                                                    'open',
                                                    'openGLDown',
                                                )
                                            "
                                        />
                                        <img
                                            v-else
                                            v-show="edit_form"
                                            :src="asset_domain + 'assets/images/icons/down.gif'"
                                            alt="Down"
                                            class="icon"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close_down',
                                                    'close',
                                                    'closeGLDown',
                                                )
                                            "
                                        />
                                        <span v-if="edit_for">|</span>

                                        <a
                                            v-if="ledger_calendar_period.gl_period_closed == 1"
                                            v-show="edit_form"
                                            href="#"
                                            @click="
                                                showConfirmationDialog(ledger_calendar_period, 'open', 'open', 'openGL')
                                            "
                                        >
                                            Open
                                        </a>
                                        <a
                                            v-else
                                            v-show="edit_form"
                                            href="#"
                                            @click="
                                                showConfirmationDialog(
                                                    ledger_calendar_period,
                                                    'close',
                                                    'close',
                                                    'closeGL',
                                                )
                                            "
                                        >
                                            Close
                                        </a>
                                    </td>
                                    <td v-else>&nbsp;</td>
                                </tr>
                                <tr
                                    class="c8-page-table-footer"
                                    v-if="edit_form"
                                >
                                    <!--colspan="100%"-->
                                    <td
                                        colspan="6"
                                        style="font-weight: bold"
                                    >
                                        <div>
                                            <v-row
                                                class="form-row"
                                                style="border-bottom: 0px !important"
                                            >
                                                <v-col
                                                    xs="12"
                                                    sm="10"
                                                    md="10"
                                                    class="form-label"
                                                    style="max-width: fit-content !important"
                                                    >Add Property Calendar: {{ calendar_type['label'] }} for year ending
                                                </v-col>
                                                <v-col
                                                    xs="12"
                                                    sm="2"
                                                    md="2"
                                                    class="form-input"
                                                >
                                                    <cirrus-single-select
                                                        v-model="calendar_year"
                                                        :options="dd_calendar_year_list"
                                                    />
                                                </v-col>
                                            </v-row>
                                        </div>
                                    </td>
                                    <td>
                                        <v-btn
                                            class="v-step-save-1-button"
                                            @click="addCalendarPeriods()"
                                            color="primary"
                                            dark
                                            right
                                            small
                                        >
                                            Add Periods
                                        </v-btn>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon>
                    WARNING
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogConfirmation = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">{{ warning_message }}</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="updateCalendar"
                        >Ok
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogConfirmation = false"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->

        <!-- CALENDAR CHANGELOG -->
        <v-dialog
            top
            v-model="show_change_log_modal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Activity Logs</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="show_change_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <activity-logs-component
                            v-if="show_change_log_modal"
                            :property_code="selected_property_code"
                            :form_section="'calendar'"
                        ></activity-logs-component>
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF CALENDAR CHANGELOG -->
    </v-container>
</template>

<script>
import Vue from 'vue';
import vSelect from 'vue-select';
import { mapActions, mapMutations, mapState } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import moment from 'moment';

const Swal = require('sweetalert2');
Vue.component('v-select', vSelect);

export default {
    props: {
        selected_property_code: { type: String, default: '' },
        reloadComponents: { type: Number, default: 0 },
    },
    data() {
        return {
            loading_setting: false,
            isCalendarHidden: true,
            asset_domain: this.$assetDomain,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],

            ledger_calendar_periods: {},
            ledger_calendar_periods_old: {},
            master_calendar_type: '',
            dd_master_calendar_type_list: [],
            calendar_year: '',
            dd_calendar_year_list: [],

            start_date: '',
            end_date: '',

            confirmation_label: ' ',
            dialogConfirmation: '',
            period: '',
            year: '',
            action_type: '',
            action_value: '',
            warning_message: '',

            warning_close:
                'The calendar period will be closed. Calendars are normally managed at the global level. Would you like to continue with the change?',
            warning_close_up:
                'The calendar period will be closed and all prior periods for this year. Would you like to continue with the change?',
            warning_close_down:
                'The calendar period will be closed and all succeeding periods for this year. Would you like to continue with the change?',

            warning_open:
                'The calendar period will be opened, this may effect prior period reporting and current transactions being entered. Calendars are normally managed at the global level. Would you like to continue with the change?',
            warning_open_up:
                'The calendar period will be opened and all prior periods, this may effect prior period reporting and current transactions being entered. Would you like to continue with the change?',
            warning_open_down:
                'The calendar period will be opened and all succeeding periods, this may effect prior period reporting and current transactions being entered. Would you like to continue with the change?',

            edit_form: false,
            current_year: '',
            counter: 0,
            all_periods: [],
            calendar_periods: {},
            historical_years: [],

            show_change_log_modal: false,
            calendar_type: '',
        };
    },
    mounted() {
        this.loadCalendarPeriods();
        this.generateYearList();
        this.fetchMasterCalendarTypeList();
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url']),
    },
    methods: {
        ...mapActions([]),
        ...mapMutations([]),

        doubleClickForm() {
            if (!this.isCalendarHidden && !this.edit_form) {
                this.edit_form = true;
            }
        },

        generateYearList() {
            const form_data = new FormData();
            form_data.append('property_code', this.selected_property_code);
            form_data.append('no_load', true);
            let api_url = 'parameter/fetch/property-calendar';
            this.$api.post(api_url, form_data).then((response) => {
                let calendar_list = response.data.calendar_list;
                let calendar_year_list = [];
                $.each(calendar_list, function (item, value) {
                    calendar_year_list.push({ value: value.field_key, label: value.field_value });
                });
                this.dd_calendar_year_list = calendar_year_list;
            });
        },

        updateStartAndEndDates(index) {
            let validate = true;

            let start_date = moment(this.ledger_calendar_periods[index].start_date, 'DD/MM/YYYY');
            let end_date = moment(this.ledger_calendar_periods[index].end_date, 'DD/MM/YYYY');
            let year = this.ledger_calendar_periods[index].year;
            let period = this.ledger_calendar_periods[index].period;
            let next_start_date = '';
            let next_end_date = '';
            let next_year = '';
            let next_period = '';

            if (typeof this.ledger_calendar_periods[index + 1] !== 'undefined') {
                next_start_date = moment(this.ledger_calendar_periods[index + 1].start_date, 'DD/MM/YYYY');
                next_end_date = moment(this.ledger_calendar_periods[index + 1].end_date, 'DD/MM/YYYY');
                next_year = this.ledger_calendar_periods[index + 1].year;
                next_period = this.ledger_calendar_periods[index + 1].period;
            }

            // validations
            if (start_date.isAfter(end_date)) {
                this.$noty.error('End date cannot be less than its corresponding start date');
                validate = false;
            }

            if (end_date.diff(start_date, 'days') < 3) {
                this.$noty.error('Start date is either too close to, or greater than the end date for the next period');
                validate = false;
            }

            if (
                typeof this.ledger_calendar_periods[index + 1] !== 'undefined' &&
                next_end_date.diff(end_date, 'days') < 3
            ) {
                this.$noty.error('End date is either too close to, or greater than the end date for the next period');
                validate = false;
            }

            if (validate) {
                //update start and end dates
                this.loading_setting = true;
                this.dialogConfirmation = false;
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('property_code', this.selected_property_code);
                formData.append('year', year);
                formData.append('period', period);
                formData.append('start_date', this.ledger_calendar_periods[index].start_date);
                formData.append('end_date', this.ledger_calendar_periods[index].end_date);

                if (index + 1 in this.ledger_calendar_periods) {
                    formData.append('next_start_date', this.ledger_calendar_periods[index + 1].start_date);
                    formData.append('next_end_date', this.ledger_calendar_periods[index + 1].end_date);
                }

                formData.append('next_year', next_year);
                formData.append('next_period', next_period);
                formData.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/update-calendar-start-and-end';

                this.$api.post(apiUrl, formData).then((response) => {
                    let status = response.data.status;
                    if (status == 'success') {
                        setTimeout(this.reloadCalendarPeriods(), 5000);
                        this.$noty.success('Calendar periods successfully updated.');
                    } else {
                        this.loading_setting = false;
                    }
                });
            }
        },

        reloadCalendarPeriods: function () {
            this.loadCalendarPeriods();
            this.loading_setting = false;
        },

        resetCalendarForm: function () {
            this.ledger_calendar_periods = this.ledger_calendar_periods;
            this.reloadCalendarPeriods();
            this.edit_form = false;
        },

        closeEditing: function () {
            this.reloadCalendarPeriods();
            this.edit_form = false;
        },

        addCalendarPeriods() {
            let hasErrors = false;
            this.master_calendar_type = this.calendar_type.calendarType;
            if (this.master_calendar_type == '') {
                this.$noty.error('You have not specified a calendar type.');
                hasErrors = true;
            }
            if (this.calendar_year == '') {
                this.$noty.error('You have not specified a calendar year.');
                hasErrors = true;
            }

            // submit form if no error
            if (!hasErrors) {
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('master_calendar_type', this.master_calendar_type);
                formData.append('calendar_year', this.calendar_year);
                formData.append('property_code', this.selected_property_code);
                formData.append('no_load', true);

                //update or create sub ledger details
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/add-calendar-periods';

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        let ve = response.data.validation_errors;
                        let noty = this.$noty;

                        if (ve.length > 0) {
                            ve.forEach(function (error) {
                                noty.error(error);
                            });
                        } else {
                            this.$noty.success('Calendar periods added.');
                            this.loadCalendarPeriods();
                        }

                        this.loading_setting = false;
                    } else {
                        this.$noty.error(response.data.error_message);
                        this.loading_setting = false;
                    }
                });
            }
        },

        loadHistoricalYears() {
            let historicalEntryTemp = [];
            let historicalEntryItems = [];
            let itemCounter = 0;
            let arrayCounterTemp = 0;
            let arrayCounter = 0;
            let year = '';
            let totalPeriods = 12;
            let closedPeriods = 0;

            this.all_periods.forEach((period) => {
                itemCounter++;
                if (year != period.year) {
                    year = period.year;
                    historicalEntryTemp[arrayCounterTemp] = [];
                    historicalEntryTemp[arrayCounterTemp]['year'] = year;
                }

                if (itemCounter == 1) {
                    historicalEntryTemp[arrayCounterTemp]['start_date'] = period.start_date;
                }

                closedPeriods++;
                historicalEntryTemp[arrayCounterTemp]['end_date'] = period.end_date;
                historicalEntryTemp[arrayCounterTemp]['periods'] = 12;

                if (closedPeriods == totalPeriods) {
                    historicalEntryTemp[arrayCounterTemp]['period_closed'] = period.period_closed;
                    historicalEntryItems[arrayCounter] = historicalEntryTemp[arrayCounterTemp];

                    arrayCounter++;
                    closedPeriods = 0;
                }

                if (itemCounter == totalPeriods) {
                    itemCounter = 0;
                    arrayCounterTemp++;
                }
            });

            this.historical_years = historicalEntryItems;
        },

        loadPeriods(year) {
            let current_year_periods = {};
            let counter = 0;

            this.all_periods.forEach((period) => {
                if (period.year == year) {
                    current_year_periods[counter] = {};

                    current_year_periods[counter]['start_date'] = period.start_date;
                    current_year_periods[counter]['end_date'] = period.end_date;
                    current_year_periods[counter]['year'] = period.year;
                    current_year_periods[counter]['period'] = period.period;
                    current_year_periods[counter]['period_closed'] = period.period_closed;
                    current_year_periods[counter]['gl_period_closed'] = period.gl_period_closed;
                    current_year_periods[counter]['current'] = period.year == this.current_year ? 1 : 0;

                    counter++;
                }
            });

            this.ledger_calendar_periods = current_year_periods;
        },

        loadOpenPeriods(currentYear) {
            let year = '';
            let openPeriods = [];
            let openPeriodItems = [];
            let totalPeriods = 12;
            let closedPeriods = 0;
            let itemCounter = 0;
            let arrayCounter = 0;

            this.all_periods.forEach((period) => {
                if (period.year == currentYear) {
                    itemCounter++;
                    if (year != period.year) {
                        year = period.year;
                    }

                    if (period.period_closed == 1) {
                        closedPeriods++;
                    }

                    if (itemCounter == totalPeriods) {
                        if (closedPeriods < totalPeriods || period.year == this.current_year) {
                            openPeriods.push(year);
                        }
                        itemCounter = 0;
                        closedPeriods = 0;
                    }
                }
            });

            this.all_periods.forEach((period) => {
                if (openPeriods.includes(period.year)) {
                    openPeriodItems[arrayCounter] = {};

                    openPeriodItems[arrayCounter]['start_date'] = period.start_date;
                    openPeriodItems[arrayCounter]['end_date'] = period.end_date;
                    openPeriodItems[arrayCounter]['year'] = period.year;
                    openPeriodItems[arrayCounter]['period'] = period.period;
                    openPeriodItems[arrayCounter]['period_closed'] = period.period_closed;
                    openPeriodItems[arrayCounter]['gl_period_closed'] = period.gl_period_closed;
                    openPeriodItems[arrayCounter]['current'] = 1; //Since periods are still open

                    arrayCounter++;
                }
            });

            this.ledger_calendar_periods = openPeriodItems;
        },

        loadCalendarPeriods() {
            if (this.selected_property_code == '') return true;

            this.loading_setting = true;

            // reset error
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);

            formData.append('property_code', this.selected_property_code);

            // get all ledger calendar periods
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-all-calendar-periods';

            this.$api.post(apiUrl, formData).then((response) => {
                this.current_year = response.data.current_year.pmcp_year;
                this.calendar_year = response.data.next_year;
                this.all_periods = response.data.property_calendar_list;
                this.calendar_type = response.data.calendar_type;
                this.loadHistoricalYears();
                this.loadOpenPeriods(this.current_year);
                this.loading_setting = false;
            });
        },

        fetchMasterCalendarTypeList() {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/master-calendar-type-list';

            this.$api.post(apiUrl, formData).then((response) => {
                this.dd_master_calendar_type_list = this.convertToArrayOfObjects(response.data);
            });
        },

        showConfirmationDialog: function (data, warning_type, action_type, action_value) {
            this.period = data.period;
            this.year = data.year;
            this.action_value = action_value;
            this.action_type = action_type;
            switch (warning_type) {
                case 'close':
                    this.warning_message = this.warning_close;
                    break;
                case 'close_up':
                    this.warning_message = this.warning_close_up;
                    break;
                case 'close_down':
                    this.warning_message = this.warning_close_down;
                    break;
                case 'open':
                    this.warning_message = this.warning_open;
                    break;
                case 'open_up':
                    this.warning_message = this.warning_open_up;
                    break;
                case 'open_down':
                    this.warning_message = this.warning_open_down;
                    break;
            }
            this.dialogConfirmation = true;
        },

        updateCalendar: function () {
            if (this.action_type == 'close') {
                this.closeCalendarPeriods();
            } else {
                this.openCalendarPeriods();
            }

            this.loadCalendarPeriods();
        },

        closeCalendarPeriods: function () {
            this.loading_setting = true;
            this.dialogConfirmation = false;
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('period', this.period);
            formData.append('year', this.year);
            formData.append('property_code', this.selected_property_code);
            formData.append('close_type', this.action_value);
            formData.append('no_load', true);
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/close-calendar-periods';

            this.$api.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                if (this.status == 'success') {
                    this.$noty.success('Calendar periods successfully closed.');
                    this.loadCalendarPeriods();
                    this.loading_setting = false;
                } else {
                    this.dialogConfirmation = false;
                    this.loading_setting = false;
                }
            });
        },

        openCalendarPeriods: function () {
            this.loading_setting = true;
            this.dialogConfirmation = false;
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('period', this.period);
            formData.append('year', this.year);
            formData.append('property_code', this.selected_property_code);
            formData.append('open_type', this.action_value);
            formData.append('no_load', true);
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/open-calendar-periods';

            this.$api.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                if (this.status == 'success') {
                    this.$noty.success('Calendar periods successfully opened.');
                    this.loadCalendarPeriods();
                    this.loading_setting = false;
                } else {
                    this.loading_setting = false;
                }
            });
        },

        openLedgerChangelogModal: function () {
            this.show_change_log_modal = true;
        },
    },

    watch: {
        selected_property_code: function () {
            this.loadCalendarPeriods();
            this.edit_form = false;
        },
        reloadComponents: function () {
            this.loadCalendarPeriods();
        },
    },
    mixins: [global_mixins],
};
</script>
