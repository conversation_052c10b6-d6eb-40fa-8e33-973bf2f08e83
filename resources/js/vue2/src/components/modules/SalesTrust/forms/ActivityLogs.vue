<style>
.ledger-update-log-container .v-data-table__wrapper tr td {
    font-size: 11px !important;
}
[data-tooltip]:before {
    background: #000 !important;
}
</style>
<template>
    <div class="ledger-update-log-container">
        <v-skeleton-loader
            class="mx-auto"
            type="table"
            :loading="loading"
            style="position: relative"
        >
            <v-btn
                @click="exportToXLSX()"
                v-if="this.ledger_update_log_list.length > 0"
                depressed
                small
                style="position: absolute; z-index: 99"
            >
                Export to Excel
            </v-btn>
            <v-card elevation="0">
                <v-card-title style="padding-top: 0px">
                    <v-spacer></v-spacer>
                    <v-text-field
                        v-model="search"
                        append-icon="search"
                        label="Search"
                        single-line
                        hide-details
                        style="padding-top: 0px; margin-top: 0px"
                    ></v-text-field>
                </v-card-title>
                <v-data-table
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="ledger_update_log_list"
                    :search="search"
                >
                    <template v-slot:item.index="{ item }">
                        {{ ledger_update_log_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.field_name="{ item }">
                        <span v-html="item.field_name"></span>
                    </template>
                    <template v-slot:item.old_value="{ item }">
                        <span :data-tooltip="item.old_value">{{ truncate(item.old_value, 30) }}</span>
                        <!-- <span v-html="item.old_value">{{ item.old_value }}</span> -->
                    </template>
                    <template v-slot:item.new_value="{ item }">
                        <span :data-tooltip="item.new_value">{{ truncate(item.new_value, 30) }}</span>
                        <!-- <span v-html="item.new_value">{{ item.new_value }}</span> -->
                    </template>
                </v-data-table>
            </v-card>
        </v-skeleton-loader>
        <v-dialog
            v-model="overview_loading"
            hide-overlay
            persistent
            width="300"
        >
            <v-card
                color="primary"
                dark
            >
                <v-card-text>
                    {{ overview_loading_msg }}
                    <v-progress-linear
                        indeterminate
                        color="white"
                        class="mb-0"
                    ></v-progress-linear>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
import moment from 'moment';
export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        form_section: { type: String, default: '' },
    },
    data() {
        return {
            loading: false,
            search: '',
            headers: [
                { text: '#', value: 'index', sortable: false, width: '5%' },
                { text: 'Field Name', value: 'field_name', sortable: true, width: '20%' },
                { text: 'Old Value', value: 'old_value', sortable: false, width: '20%' },
                { text: 'New Value', value: 'new_value', sortable: false, width: '20%' },
                { text: 'User', value: 'modified_by', sortable: true, width: '12.5%' },
                { text: 'Date', value: 'date_modified', sortable: true, width: '12.5%' },
                { text: 'Status', value: 'status', sortable: true, width: '10%' },
            ],
            ledger_update_log_list: [],
            overview_loading: false,
            overview_loading_msg: 'Please wait...',
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadUpdateLogs();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    methods: {
        loadUpdateLogs: function () {
            if (this.property_code !== '' && this.form_section !== '') {
                this.loading = true;

                var form_data = new FormData();
                form_data.append('user_type', this.user_type);
                form_data.append('property_code', this.property_code);
                if (this.lease_code !== '') {
                    form_data.append('lease_code', this.lease_code);
                }
                form_data.append('form_section', this.form_section);
                form_data.append('no_load', true);

                let api_url = this.cirrus8_api_url + 'api/sales-trust/ledger/activity-logs';

                this.$api.post(api_url, form_data).then((response) => {
                    this.ledger_update_log_list = this.transposeData(this.convertToArrayOfObjects(response.data));
                    this.loading = false;
                });
            }
        },
        transposeData: function (data) {
            var transposed_data = [];

            data.forEach(function (item, index) {
                transposed_data.push(item);
            });

            return transposed_data;
        },
        truncate: function (string, n) {
            if (string) {
                let str = string.trim();
                return str.length > n ? str.substr(0, n - 1) + '...' : str;
            }
        },
        exportToXLSX: function () {
            if (this.property_code !== '' && this.form_section !== '') {
                let params = {
                    property_code: this.property_code,
                    form_section: this.form_section,
                    no_load: true,
                };

                if (this.lease_code !== '') {
                    params.lease_code = this.lease_code;
                }

                this.overview_loading = true;
                this.overview_loading_msg = 'Generating excel file';

                this.$api.post('sales-trust/ledger/export-activity-log', this.req(params)).then((response) => {
                    let res = response.data;
                    this.overview_loading = false;
                    this.overview_loading_msg = 'Please wait...';

                    if (res.file && res.file.name && res.file.type && res.file.data) {
                        this.printDownload(res.file.data, res.file.name + moment().unix(), res.file.type);
                    } else {
                        if (res.error) this.$noty.error(res.error);
                        else this.$noty.error('Generate report failed. Please try again.');
                    }
                });
            }
        },
    },
    created() {
        bus.$on('loadLedgerUpdateLog', (data) => {
            this.loadUpdateLogs();
        });
    },
    mixins: [global_mixins],
};
</script>
