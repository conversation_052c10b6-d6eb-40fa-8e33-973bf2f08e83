<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>
            <v-card
                id="agency_charges_details_header"
                class="section-toolbar"
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Agency Charges</h6>
                    &nbsp&nbsp
                    <v-spacer></v-spacer>
                    <div style="display: inline-block; padding-right: 1em">
                        <span class="toolbar-search-holder">
                            <v-text-field
                                dense
                                v-model="searchSundries"
                                :class="'cirrus-input-form-textbox'"
                                :placeholder="'Search'"
                                prepend-inner-icon="search"
                                :maxlength="'60'"
                            ></v-text-field>
                        </span>
                    </div>
                    <v-btn
                        x-small
                        class="v-step-edit-button"
                        data-tooltip="Add Agency Charges"
                        icon
                        @click="showAddSundries()"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Edit"
                        v-show="!edit_form"
                        class="v-step-edit-button"
                        v-if="!edit_form"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <!--//v-btn x-small v-show="edit_form" data-tooltip="Undo Changes"
                        v-if="edit_form" class="v-step-revert-button" icon @click="resetCommissionForm()" title="Undo Changes">
                    <v-icon color="red">undo</v-icon>
                    </v-btn//-->
                    <v-btn
                        x-small
                        v-show="edit_form"
                        data-tooltip="Done"
                        v-if="edit_form"
                        class="v-step-save-1-button"
                        icon
                        @click="edit_form = false"
                    >
                        <v-icon
                            light
                            color="green"
                            >check</v-icon
                        >
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Refresh"
                        class="v-step-refresh-button"
                        icon
                        @click="loadSundries()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
            <!--//v-card id="bank_details_header" dark color="titleHeader" text tile>
                <v-card-actions>
                    <h4 class="title font-weight-black">Agency Charges</h4>
				</v-card-actions>								
            </v-card//-->

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div class="page-list">
                    <div class="c8-page-table">
                        <!--//table>
                            <tbody>  								
                            <tr class="form-row">
                                <td colspan="2"><v-text-field v-model="searchSundries" placeholder="Search..." dense/></td>
                                <td width="20">&nbsp;</td>
                                <td width="20%">&nbsp;</td>
                                <td width="15%">&nbsp;</td>
                                <td width="10%">&nbsp;</td>
                                <td width="15%" align="right">
                                    <v-btn v-if="edit_form" class="v-step-save-1-button" @click="showAddSundries()"
                                           color="primary"
                                           dark
                                           right
                                           small
                                    >
                                        ADD AGENCY CHARGES
                                    </v-btn>
                                </td>								
                            </tr>							
                            </tbody>
                        </table//-->
                        <v-data-table
                            v-show="sundries_items.length > 0"
                            :headers="sundriesHeaders"
                            :items="sundries_items"
                            :search="searchSundries"
                            dense
                        >
                            <template v-slot:item.sub_ledger_code="{ item }">
                                <a
                                    v-if="edit_form"
                                    title="Click to update"
                                    @click="showUpdateSundries(item)"
                                >
                                    {{ item.sub_ledger_code }}
                                </a>
                                <span v-else>{{ item.sub_ledger_code }}</span>
                            </template>
                            <template v-slot:item.amount="{ item }">
                                {{ formatAsCurrency(item.amount, 2) }}
                            </template>
                            <template v-slot:item.delete_action="{ item }">
                                <v-btn
                                    v-if="edit_form"
                                    title="Delete"
                                    x-small
                                    icon
                                    @click="showConfirmationDialog(item)"
                                >
                                    <v-icon color="red">delete_forever</v-icon>
                                </v-btn>
                                <v-btn
                                    v-else
                                    title="Delete"
                                    x-small
                                    icon
                                >
                                    <v-icon
                                        color="gray"
                                        style="opacity: 0.5; cursor: not-allowed"
                                        >delete_forever</v-icon
                                    >
                                </v-btn>
                            </template>
                        </v-data-table>
                        <v-col
                            class="text-center"
                            v-if="sundries_items.length === 0"
                        >
                            <v-btn
                                depressed
                                small
                                color="success"
                                :loading="submit_btn_loading"
                                @click="showAddSundries()"
                                class="ldgr-add-btn"
                                >Add Agency Charges</v-btn
                            >
                        </v-col>
                    </div>
                </div>
            </div>
        </div>

        <!-- ADD/UPDATE SUB LEDGER -->
        <v-dialog
            top
            v-model="modalFeesCharge"
            width="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>{{ save_button_label }}</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="modalFeesCharge = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row
                            class="form-row"
                            v-if="form_mode == 1"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Sub Ledger Code</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="sub_ledger"
                                    :options="dd_sub_ledger_list"
                                    ref="refSubLedger"
                                />
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            v-else
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Sub Ledger Code</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                >
                                    {{ sub_ledger_code }}
                                </v-col>
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            v-if="form_mode == 1"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Type</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="type"
                                    :options="dd_types"
                                    ref="refType"
                                />
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            v-else
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Type</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                >
                                    {{ type_name }}
                                </v-col>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Description</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="description"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            v-if="form_mode == 1"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Account</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="account"
                                    :options="dd_account"
                                    ref="refAccount"
                                />
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row"
                            v-else
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Account</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                >
                                    {{ account_label }}
                                </v-col>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Net Amount</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="amount"
                                    dense
                                    @keypress="onlyForCurrency"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        :loading="submit_btn_loading"
                        @click="saveSundries()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        {{ save_button_label }}
                    </v-btn>
                    <!--//v-btn color="primary" text @click="modalFeesCharge = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF UPDATE SUB LEDGER -->

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialogConfirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon> WARNING
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogConfirmation = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">{{ warning_message }}</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        :loading="delete_btn_loading"
                        @click="deleteSundries()"
                        >Ok</v-btn
                    >
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="dialogConfirmation = false"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->
    </v-container>
</template>

<script>
import Vue from 'vue';
import vSelect from 'vue-select';
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

const Swal = require('sweetalert2');
Vue.component('v-select', vSelect);

export default {
    props: {
        selected_property_code: { type: String, default: '' },
        reloadComponents: { type: Number, default: 0 },
    },
    data() {
        return {
            hide: true,
            loading_setting: false,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],

            form_mode: 0,
            sundries_items: [],
            sundriesHeaders: [
                { text: 'Sub Ledger', value: 'sub_ledger_code', sortable: false, width: '15%' },
                { text: 'Type', value: 'type_name', sortable: false, width: '15%' },
                { text: 'Description', value: 'description', sortable: false, width: '15%' },
                { text: 'Account', value: 'account_name', sortable: false, width: '15%' },
                { text: 'Net Amount', value: 'amount', sortable: false, width: '15%', align: 'end' },
                { text: 'Create Date', value: 'create_date', sortable: false, width: '15%', align: 'end' },
                { text: 'Delete', value: 'delete_action', sortable: false, width: '10%', align: 'end' },
            ],

            searchSundries: '',
            modalFeesCharge: false,
            //types: 	{'rentreview' : 'Rent Review Fee','inspection' : 'Inspection Fee'},
            dd_types: [],
            dd_sub_ledger_list: [],
            dd_account: [],

            save_button_label: 'ADD AGENCY CHARGES',
            sub_ledger: '',
            sub_ledger_code: '',
            type: '',
            type_name: '',
            type_code: '',
            description: '',
            account: '',
            account_code: '',
            account_label: '',
            agency_charge_id: 0,
            amount: '',

            confirmation_label: ' ',
            dialogConfirmation: '',
            edit_form: false,
            item_for_deletion: [],
            submit_btn_loading: false,
            delete_btn_loading: false,
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
                tax_label: 'GST',
                business_prefix: '',
                currency_symbol: '$',
            },
        };
    },
    created() {
        bus.$on('call_load', (data) => {
            this.loadSundries();
        });
    },
    mounted() {
        this.loadSundries();
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url', 'dd_company_list', 'dd_sub_ledger_type_list']),
    },
    methods: {
        doubleClickForm() {
            if (!this.edit_form) {
                this.edit_form = true;
            }
        },
        onlyForCurrency($event) {
            let keyCode = $event.keyCode ? $event.keyCode : $event.which;

            if ((keyCode < 48 || keyCode > 57) && (keyCode !== 46 || this.amount.indexOf('.') != -1)) {
                // 46 is dot
                $event.preventDefault();
            }

            if (this.amount != null && this.amount.indexOf('.') > -1 && this.amount.split('.')[1].length > 1) {
                $event.preventDefault();
            }
        },
        formatAsCurrency: function (amt, dec) {
            amt = parseFloat(amt);
            if (amt) {
                dec = dec || 0;
                if (amt < 0) {
                    amt = amt * -1;
                    return (
                        this.country_defaults.currency_symbol +
                        ' (' +
                        amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,') +
                        ')'
                    );
                } else {
                    return (
                        this.country_defaults.currency_symbol +
                        ' ' +
                        amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,')
                    );
                }
            } else {
                return this.country_defaults.currency_symbol + ' 0.00';
            }
        },
        resetFields: function () {
            if (this.sub_ledger) this.$refs.refSubLedger.select_val = '';
            if (this.type) this.$refs.refType.select_val = '';
            if (this.account) this.$refs.refAccount.select_val = '';
            this.sub_ledger = '';
            this.type = '';
            this.description = '';
            this.account = '';
            this.amount = '';
        },
        showAddSundries: function () {
            this.resetFields();
            this.save_button_label = 'ADD AGENCY CHARGES';
            this.form_mode = 1;
            this.modalFeesCharge = true;
        },

        showUpdateSundries: function (data) {
            this.sub_ledger_code = data.sub_ledger_code;
            this.type_code = data.type;
            this.type_name = data.type_name;
            this.account_code = data.account_code;
            this.account_label = data.account_name;
            this.description = data.description;
            this.amount = data.amount;
            this.agency_charge_id = data.agency_charge_id;

            this.save_button_label = 'UPDATE AGENCY CHARGES';

            this.form_mode = 0;
            this.modalFeesCharge = true;
        },

        loadSundries: function () {
            if (this.selected_property_code == '') return true;

            this.loading_setting = true;

            // reset error
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            var formData = new FormData();
            formData.append('user_type', this.user_type);

            formData.append('property_code', this.selected_property_code);
            formData.append('no_load', true);

            // get ledger details by code
            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-sundries';
            this.$api.post(apiUrl, formData).then((response) => {
                this.sundries_items = this.convertToArrayOfObjects(response.data);
                this.loading_setting = false;
                this.loadAccountsList();
                this.loadSundriesType();
                this.loadSubLedgerList();
                this.loadCountryDefaults();
            });
        },
        saveSundries: function () {
            let validate = true;
            if (this.sub_ledger == '' && this.form_mode == 1) {
                this.$noty.error('You have not entered in a valid  sub ledger code.');
                validate = false;
            }
            if (this.type == '' && this.form_mode == 1) {
                this.$noty.error('You have not entered in a valid type.');
                validate = false;
            }
            if (this.description == '') {
                this.$noty.error('You have not entered in a valid description.');
                validate = false;
            }
            if (this.account == '' && this.form_mode == 1) {
                this.$noty.error('You have not entered in a valid account code.');
                validate = false;
            }
            if (this.amount == '' || this.amount == 0) {
                this.$noty.error('You have not entered in a valid amount.');
                validate = false;
            }

            if (validate) {
                this.submit_btn_loading = true;
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('user_type', this.user_type);

                formData.append('form_mode', this.form_mode);
                formData.append('property_code', this.selected_property_code);
                if (this.form_mode == 1) {
                    formData.append('lease_code', this.sub_ledger.toUpperCase().trim());
                    formData.append('type', this.type);
                    formData.append('account_code', this.account);
                } else {
                    formData.append('lease_code', this.sub_ledger_code);
                    formData.append('type_name', this.type_name);
                    formData.append('type', this.type_code);
                    formData.append('account_code', this.account_code);
                    formData.append('agency_charge_id', this.agency_charge_id);
                }
                formData.append('description', this.description);
                formData.append('amount', this.amount);
                formData.append('no_load', true);

                //update or create sub ledger details
                let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/update-or-create-sundries';

                this.$api.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        if (this.form_mode == 1) this.$noty.success('Agency charges added.');
                        else this.$noty.success('Agency charges updated.');
                        this.modalFeesCharge = false;
                        this.resetFields();
                        this.loadSundries();
                        this.loading_setting = false;
                    } else {
                        this.$noty.error('The agency charges you entered already exists for this sub ledger');
                        this.loading_setting = false;
                    }

                    this.submit_btn_loading = false;
                });

                /*axios.post(apiUrl, formData).then(response => {
                        this.status = response.data.status;
                        if (this.status == 'success') {
                            if(this.form_mode == 1)
                                this.$noty.success('Agency charges added.');
                            else
                                this.$noty.success('Agency charges updated.');
                            this.modalFeesCharge = false;
                            this.resetFields();
                            this.loadSundries();
                            this.loading_setting = false;
                        }
                        else {
                            this.$noty.error('The agency charges you entered already exists for this sub ledger');
                            this.loading_setting = false;
                        }
                    });*/
            }
        },
        deleteSundries: function () {
            let item = this.item_for_deletion;

            this.delete_btn_loading = true;
            this.loading_setting = true;
            var formData = new FormData();
            formData.append('user_type', this.user_type);

            formData.append('property_code', this.selected_property_code);
            formData.append('lease_code', item.sub_ledger_code);
            formData.append('type', item.type);
            formData.append('account_code', item.account_code);
            formData.append('agency_charge_id', item.agency_charge_id);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/delete-sundries';

            this.$api.post(apiUrl, formData).then((response) => {
                this.$noty.success('Agency charges deleted.');
                this.loading_setting = false;
                this.delete_btn_loading = false;
                this.dialogConfirmation = false;
                this.loadSundries();
                this.item_for_deletion = [];
            });

            /*axios.post(apiUrl, formData).then(response => {
                    this.$noty.success('Agency charges deleted.');
                    this.loading_setting = false;
                    this.dialogConfirmation = false;
                    this.loadSundries();
                    this.item_for_deletion = [];
                });*/
        },
        loadAccountsList: function () {
            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/loadAPIAccountDropDownList';

            this.$api.post(apiUrl, formData).then((response) => {
                let account_data = response.data.data;
                let accDataArray = [];
                $.each(account_data, function (i, v) {
                    accDataArray.push({ value: v.account_code, label: v.account_code + ' - ' + v.account_name });
                });
                this.dd_account = accDataArray;
            });

            /*axios.post(this.cirrus8_api_url + 'api/loadAPIAccountDropDownList', formData)
                    .then(response => {
                        let account_data = response.data.data;
                        let accDataArray = [];
                        $.each(account_data, function(i, v){
                            accDataArray.push({'value': v.account_code, 'label': v.account_code + ' - ' + v.account_name});
                        });
                        this.dd_account = accDataArray;
                    });*/
        },

        loadSundriesType: function () {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/ui/fetch/param-sundry-list';

            this.$api.post(apiUrl, formData).then((response) => {
                this.dd_types = this.convertToArrayOfObjects(response.data);
            });

            /*axios.post(this.cirrus8_api_url + 'api/ui/fetch/param-sundry-list', {
                    current_db: this.current_db,
                    user_type: this.user_type,
                    un: this.username,
                })
                    .then(response => {
                        this.dd_types = response.data;
                    });*/
        },

        loadSubLedgerList: function () {
            var formData = new FormData();

            formData.append('user_type', this.user_type);
            formData.append('property_code', this.selected_property_code);
            formData.append('no_load', true);

            let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-sundries-sub-ledgers';

            this.$api.post(apiUrl, formData).then((response) => {
                this.dd_sub_ledger_list = response.data.data;
            });

            /*axios.post(this.cirrus8_api_url + 'api/sales-trust/ledger/get-sundries-sub-ledgers', formData)
                    .then(response => {
                        this.dd_sub_ledger_list = response.data.data;

                    });*/
        },

        showConfirmationDialog: function (item) {
            this.item_for_deletion = item;
            this.warning_message = 'This agency charge will be deleted. Would you like to proceed?';
            this.dialogConfirmation = true;
        },
        loadCountryDefaults: function () {
            let form_data = new FormData();
            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
            });
        },
    },

    watch: {
        selected_property_code: function () {
            this.loadSundries();
            this.edit_form = false;
        },
        reloadComponents: function () {
            this.loadSundries();
        },
    },
    mixins: [global_mixins],
};
</script>
