<template>
    <div
        style="float: right; display: contents"
        :key="key"
    >
        <v-dialog
            v-model="show_modal"
            max-width="777"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Send SMS Message
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Mobile Number</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <table>
                                    <tr>
                                        <td style="width: 300px">
                                            <cirrus-single-select-v2
                                                v-model="mobile_number"
                                                :options="mobile_number_list"
                                                ref="refPropertyType"
                                                trackBy="field_key"
                                                label="field_value"
                                                return="field_key"
                                                placeholder="Select a mobile number"
                                            />
                                        </td>
                                        <td>
                                            <v-icon
                                                style="font-size: 21px; position: relative; top: -1px"
                                                title="Mobile number list is from Contact section."
                                                >info</v-icon
                                            >
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Contact Name</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <span class="form-input-text">{{ contact_name }}</span>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Salutation</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <span class="form-input-text">{{ salutation }}</span>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Message</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                style="padding-right: 50px"
                            >
                                <v-textarea
                                    v-model="sms_message"
                                    auto-grow
                                    rows="7"
                                    full-width
                                    maxlength="300"
                                    class="noteTextArea"
                                ></v-textarea>
                                <br />
                                <div class="text-right">
                                    <span>{{ sms_message.length }} / 300</span>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="sendSMS()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-send</v-icon
                        >
                        Send
                    </v-btn>
                    <v-btn
                        depressed
                        small
                        @click="show_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
import { mapState } from 'vuex';
import moment from 'moment';
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';

export default {
    props: {
        key: { type: String, default: '' },
    },
    data() {
        return {
            loading: true,
            overview_loading: false,
            overview_loading_msg: 'Please wait...',
            show_modal: false,
            mobile_number_list: [],
            error_server_msg: {},
            error_server_msg2: [],
            sms_message: '',
            mobile_number: '',
            salutation: '',
            contact_name: '',
        };
    },
    mounted() {
        // this.loadForm()
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    methods: {
        loadForm: function (data) {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.data = data;
            if (
                (data.property_code !== '' ||
                    data.lease_code !== '' ||
                    data.company_code !== '' ||
                    data.contact_detail_id !== '') &&
                data.form_section !== ''
            ) {
                this.loading = true;
                var form_data = new FormData();
                form_data.append('property_code', data.property_code);
                form_data.append('lease_code', data.lease_code);
                form_data.append('company_code', data.company_code);
                form_data.append('contact_detail_id', data.contact_detail_id);
                form_data.append('form_section', data.form_section);
                form_data.append('no_load', true);
                this.$api.post('sms/fetch/sms-contact-list', form_data).then((response) => {
                    this.mobile_number_list = response.data.mobile_number_list;
                    this.mobile_number = response.data.mobile_number;
                    this.salutation = response.data.salutation;
                    this.contact_name = response.data.contact_name;
                    this.sms_message = this.loadDefaultMsg();
                    this.loading = false;
                });
            }
        },
        sendSMS: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let sms_message = this.sms_message.replace(/\n/g, '');
            let error_msg_arr = [];
            if (this.mobile_number === '') error_msg_arr.push(['Please select a mobile number to send to.']);
            if (sms_message === '') error_msg_arr.push(['Please enter a message.']);
            this.error_server_msg2 = error_msg_arr;
            if (error_msg_arr.length > 0) return;
            var form_data = new FormData();
            form_data.append('property_code', this.data.property_code);
            form_data.append('lease_code', this.data.lease_code);
            form_data.append('company_code', this.data.company_code);
            form_data.append('contact_detail_id', this.mobile_number);
            form_data.append('form_section', this.data.form_section);
            form_data.append('sms_message', sms_message);
            form_data.append('no_load', true);
            this.$api.post('sms/process/send-sms', form_data).then((response) => {
                this.error_server_msg2 = response.data.error_server_msg2;
                if (response.data.sent_flag) this.show_modal = false;
            });
        },
        loadDefaultMsg: function () {
            return 'Hi ' + this.salutation + ', ';
        },
        getSalutation(code, options) {
            let label = '';
            options.forEach((row) => {
                if (row.field_key.trim() === code.trim()) label = row.salutation;
            });
            return label;
        },
        getContactName(code, options) {
            let label = '';
            options.forEach((row) => {
                if (row.field_key.trim() === code.trim()) label = row.contact_name;
            });
            return label;
        },
    },
    watch: {
        mobile_number: function () {
            this.salutation = this.getSalutation(this.mobile_number, this.mobile_number_list);
            this.contact_name = this.getContactName(this.mobile_number, this.mobile_number_list);
            this.sms_message = this.loadDefaultMsg();
        },
    },
    created() {
        bus.$on('loadSMSSendingForm', (data) => {
            this.loadForm(data);
        });
        bus.$on('toggleSMSSendingModal', (data) => {
            this.show_modal = data;
            this.loadForm(data);
        });
    },
    mixins: [global_mixins],
};
</script>
