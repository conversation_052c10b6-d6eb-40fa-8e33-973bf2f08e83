<style>
.file-description-textbox {
    min-width: 300px !important;
}

.documents-table {
    border: 1px solid rgba(34, 36, 38, 0.15);
}

td.text-start,
td.text-end {
    padding-top: 0.4em !important;
    padding-bottom: 0.4em !important;
}

.documents-table .option {
    font-size: 21px !important;
}

ul.v-pagination button {
    height: 30px !important;
    min-width: 30px !important;
    min-height: 30px !important;
}
</style>
<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div>
            <v-card
                id="document_details_header"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Documents</h6>
                    &nbsp
                    <v-btn-toggle
                        v-if="sys_ver_control_list.isFolderSystemOn"
                        class="form-toggle"
                        v-model="doc_active_version"
                        mandatory
                    >
                        <v-btn
                            x-small
                            tile
                            text
                            @click="setActiveVersion(0)"
                        >
                            Version 1
                        </v-btn>
                        <v-btn
                            x-small
                            tile
                            text
                            @click="setActiveVersion(1)"
                        >
                            Beta
                        </v-btn>
                    </v-btn-toggle>
                    <v-spacer></v-spacer>
                    <cirrus-input
                        inputFormat="search"
                        v-if="action_parameter != 'review'"
                        v-model="search_datatable"
                        placeholder="Search"
                        :edit_form="true"
                        style="padding-right: 1em"
                    ></cirrus-input>
                    <v-btn
                        x-small
                        data-tooltip="Add Document"
                        v-if="!read_only"
                        icon
                        @click="modalAddData()"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Edit"
                        v-if="form_mode !== 1 && !edit_form && !read_only"
                        class="v-step-edit-button"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Undo Changes"
                        v-if="edit_form && form_mode !== 1"
                        class="v-step-revert-button"
                        icon
                        @click="resetForm()"
                    >
                        <v-icon color="red">undo</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Update Documents"
                        v-if="edit_form && form_mode !== 1"
                        class="v-step-save-1-button"
                        icon
                        @click="saveForm()"
                    >
                        <v-icon
                            light
                            color="green"
                            >check
                        </v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        data-tooltip="Refresh"
                        v-if="form_mode !== 1"
                        class="v-step-refresh-button"
                        icon
                        @click="loadForm()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>

            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div
                    class="form-row"
                    style="border-bottom: none !important"
                >
                    <v-col
                        class="text-center"
                        v-if="company_documents_list.length === 0"
                        v-show="action_parameter != 'view'"
                    >
                        <v-btn
                            v-if="!read_only"
                            depressed
                            small
                            color="success"
                            @click="modalAddData()"
                            >Add Document
                        </v-btn>
                        <div
                            style="margin-top: 10px"
                            v-else
                        >
                            No documents at the moment
                        </div>
                    </v-col>

                    <!--datatable start-->
                    <v-data-table
                        class="c8-datatable-custom documents-table"
                        v-show="company_documents_list.length > 0"
                        dense
                        item-key="id"
                        :headers="headers"
                        :items="company_documents_list"
                        :items-per-page="items_per_page"
                        hide-default-footer
                        :page.sync="page"
                        :total-visible="7"
                        @page-count="page_count = $event"
                        :search="search_datatable"
                    >
                        <template v-slot:item.index="{ item }">
                            {{ company_documents_list.indexOf(item) + 1 }}
                        </template>
                        <template v-slot:item.guaranteeTypeDescription="{ item }">
                            <multiselect
                                v-if="edit_form"
                                data-inverted=""
                                v-model="item.guaranteeType"
                                :options="leaseGuaranteeTypeList"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-200"
                                group-label="language"
                                placeholder="Select a guarantee type"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                            <span v-if="!edit_form">{{ item.guaranteeTypeDescription }}</span>
                        </template>
                        <template v-slot:item.document_title="{ item }">
                            <cirrus-input
                                custom_class="cirrus-input-table-textbox"
                                v-model="item.document_title"
                                size=""
                                data-inverted=""
                                :edit_form="edit_form"
                                :error_msg="error_msg"
                            ></cirrus-input>
                        </template>
                        <template v-slot:item.document_description="{ item }">
                            <cirrus-input
                                custom_class="file-description-textbox"
                                v-model="item.document_description"
                                size=""
                                data-inverted=""
                                :edit_form="edit_form"
                                :error_msg="error_msg"
                            ></cirrus-input>
                        </template>
                        <template v-slot:item.enable_external_link="{ item }">
                            <table v-if="item.is_external != 1 && item.on_s3 != 1">
                                <tbody>
                                    <tr v-if="!item.enable_external_link">
                                        <td style="margin: 0; padding: 0">
                                            <cirrus-single-upload-button2
                                                :withLinkUploader="true"
                                                :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                                v-model="item.filename"
                                                :has_saved_file="
                                                    item.filename_old !== '' &&
                                                    (typeof item.filename_old === 'string' ||
                                                        item.filename_old instanceof String)
                                                        ? true
                                                        : false
                                                "
                                                :edit_form="edit_form"
                                                :error_msg="error_msg"
                                                accept_type="pdf"
                                                :size_limit="20"
                                            ></cirrus-single-upload-button2>
                                        </td>
                                        <td
                                            style="margin: 0; padding: 0"
                                            v-if="edit_form && !item.filename"
                                        >
                                            <span>or</span>
                                        </td>
                                        <td
                                            style="margin: 0; padding: 0"
                                            v-if="edit_form && !item.filename"
                                        >
                                            <a
                                                href="#"
                                                @click="item.enable_external_link = 1"
                                                ><img
                                                    :src="asset_domain + 'assets/images/icons/link_icon_blue.png'"
                                                    class="icon"
                                                    style="width: 19px"
                                            /></a>
                                        </td>
                                    </tr>
                                    <tr v-if="item.enable_external_link == 1">
                                        <td style="margin: 0; padding: 0">
                                            <cirrus-input
                                                custom_class="cirrus-input-table-textbox"
                                                maxlength="255"
                                                v-model="item.external_url"
                                                size=""
                                                :edit_form="edit_form"
                                                :error_msg="error_msg"
                                            ></cirrus-input>
                                            <v-btn
                                                :edit_form="edit_form"
                                                x-small
                                                class=""
                                                @click="
                                                    item.enable_external_link = 0;
                                                    item.external_url = '';
                                                "
                                                >Cancel
                                            </v-btn>
                                            <input
                                                type="hidden"
                                                v-model="item.enable_external_link"
                                                :edit_form="edit_form"
                                                :error_msg="error_msg"
                                            />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <a
                                target="_blank"
                                v-if="item.is_external == 1"
                                :href="item.external_url"
                                ><img
                                    :src="asset_domain + 'assets/images/icons/link_icon_blue.png'"
                                    alt="Adobe Logo"
                                    class="icon"
                                    style="width: 19px"
                                />&nbsp; Download</a
                            >

                            <a
                                target="_blank"
                                v-if="item.on_s3 == 1"
                                v-on:click="downloadS3File(item.s3_filepath, item.s3_filename)"
                                ><img
                                    :src="asset_domain + 'assets/images/icons/pdf.png'"
                                    alt="Adobe Logo"
                                    class="icon"
                                    style="width: 19px"
                                />&nbsp; Download</a
                            >
                        </template>

                        <template v-slot:item.action1="{ item }">
                            <v-icon
                                color="green"
                                class="rotate90 option"
                                v-show="!readonly"
                                v-if="edit_form"
                                @click="setFirst(company_documents_list.indexOf(item))"
                                title="Move to top"
                            >
                                fast_rewind
                            </v-icon>
                            <v-icon
                                color="green"
                                class="rotate90 option"
                                v-show="!readonly"
                                v-if="edit_form"
                                @click="setOrder('updateSeqUp', company_documents_list.indexOf(item))"
                                title="Move up"
                                >arrow_left
                            </v-icon>
                            <v-icon
                                color="green"
                                class="rotate90 option"
                                v-show="!readonly"
                                v-if="edit_form"
                                @click="setOrder('updateSeqDown', company_documents_list.indexOf(item))"
                                title="Move down"
                            >
                                arrow_right
                            </v-icon>
                            <v-icon
                                color="green"
                                class="rotate90 option"
                                v-show="!readonly"
                                v-if="edit_form"
                                @click="setLast(company_documents_list.indexOf(item))"
                                title="Move to bottom"
                            >
                                fast_forward
                            </v-icon>
                            <v-icon
                                color="red"
                                class="option"
                                v-show="!readonly"
                                v-if="edit_form"
                                @click="confirmDeleteDocument(company_documents_list.indexOf(item))"
                                >close
                            </v-icon>
                        </template>
                    </v-data-table>

                    <v-row
                        class="form-row"
                        v-show="company_documents_list.length > 5"
                    >
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                        >
                            <table class="c8-datatable-custom-footer">
                                <tr>
                                    <td class="">Rows per page:</td>
                                    <td>
                                        <multiselect
                                            v-model="items_per_page"
                                            :options="[5, 10, 15]"
                                            :allowEmpty="false"
                                            class="vue-select2 dropdown-left dropdown-200"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </td>
                                    <td></td>
                                    <td>
                                        <v-pagination
                                            v-model="page"
                                            :length="page_count"
                                        ></v-pagination>
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>

                    <v-card
                        elevation="0"
                        v-if="edit_form && company_documents_list.length > 0"
                    >
                        <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn
                                class="v-step-save-2-button"
                                @click="saveForm()"
                                color="success"
                                dark
                                small
                                depressed
                            >
                                Save Documents details
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </div>
            </div>
        </div>

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialog_confirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon>
                    WARNING
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="resetDeleteDialog()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">{{ warning_message }}</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="deleteDocument()"
                        >Ok
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="resetDeleteDialog()"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->

        <!--   SHOW ADD DIALOG      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitData()"
        >
            <v-card>
                <v-card-title class="headline">
                    Add Document
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>

                    <div :key="company_document_add_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        company_document_add_arr.index === 'New'
                                            ? company_document_add_arr.index
                                            : company_document_add_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Document Title
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="company_document_add_arr.document_title"
                                        size=""
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Description
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="file-description-textbox"
                                        v-model="company_document_add_arr.document_description"
                                        size=""
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >File
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <table>
                                        <tbody>
                                            <tr>
                                                <td style="margin: 0; padding: 0">
                                                    <cirrus-single-upload-button2
                                                        :withLinkUploader="true"
                                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                                        v-model="company_document_add_arr.filename"
                                                        :has_saved_file="
                                                            company_document_add_arr.filename_old !== '' &&
                                                            (typeof company_document_add_arr.filename_old ===
                                                                'string' ||
                                                                company_document_add_arr.filename_old instanceof String)
                                                                ? true
                                                                : false
                                                        "
                                                        :edit_form="true"
                                                        :error_msg="error_msg"
                                                        accept_type="pdf"
                                                        :size_limit="20"
                                                    ></cirrus-single-upload-button2>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!company_document_add_arr.filename"
                                                >
                                                    <span>or</span>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!company_document_add_arr.filename"
                                                >
                                                    <a
                                                        href="#"
                                                        @click="company_document_add_arr.enable_external_link = 1"
                                                        ><img
                                                            :src="
                                                                asset_domain + 'assets/images/icons/link_icon_blue.png'
                                                            "
                                                            class="icon"
                                                            style="width: 19px"
                                                    /></a>
                                                </td>
                                            </tr>
                                            <tr v-if="company_document_add_arr.enable_external_link == 1">
                                                <td style="margin: 0; padding: 0">
                                                    <cirrus-input
                                                        custom_class="cirrus-input-table-textbox"
                                                        maxlength="255"
                                                        v-model="company_document_add_arr.external_url"
                                                        size=""
                                                        :edit_form="true"
                                                        :error_msg="error_msg"
                                                    ></cirrus-input>
                                                    <v-btn
                                                        :edit_form="true"
                                                        x-small
                                                        class=""
                                                        @click="
                                                            company_document_add_arr.enable_external_link = 0;
                                                            company_document_add_arr.external_url = '';
                                                        "
                                                    >
                                                        Cancel
                                                    </v-btn>
                                                    <input
                                                        type="hidden"
                                                        v-model="company_document_add_arr.enable_external_link"
                                                    />
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        data-position="left center"
                        color="success"
                        depressed
                        small
                        :loading="submitBtnLoading"
                        :disabled="submitBtnLoading"
                        :dark="isDarkMode()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Submit
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="company_document_add_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        data-position="left center"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   END ADD DIALOG      -->
    </v-container>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import axios from 'axios';
import isEmpty from 'lodash/isEmpty';
import { USER_TYPES } from '../../../../constants';
import { isDarkMode } from '../../../../utils/sharedFunctions';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        reload_components: { type: Boolean, default: false },
        company_code: { type: String, default: '' },
        forceLoad: { type: Boolean, default: false },
        is_temp: { type: Boolean, default: false },
        action_parameter: { type: String, default: '' },
    },
    data() {
        return {
            user_sub_type: localStorage.getItem('user_sub_type'),
            read_only: false,
            asset_domain: this.$assetDomain,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            company_documents_list: [],
            company_documents_list_orig: [],
            readonly: this.read_only,
            show_activity_log_modal: false,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Document Title', value: 'document_title', sortable: false },
                { text: 'Description', value: 'document_description', sortable: false },
                { text: 'File', value: 'enable_external_link', sortable: false },
                { text: '', value: 'action1', align: 'end', sortable: false },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            filename: '',
            warning_message: '',
            dialog_confirmation: false,
            index_for_deletion: 0,
            success_flag: false,
            AED_modal: false,
            company_document_add_arr: [],
            new_array: [],
            submitBtnLoading: false,
        };
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url', 'doc_active_version', 'sys_ver_control_list']),
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loading_setting = false;
        this.loadForm();
        this.checkAccess();
    },
    methods: {
        ...mapMutations(['SET_DOC_ACTIVE_VERSION']),
        isDarkMode,
        checkAccess() {
            if (this.user_sub_type === USER_TYPES.PM_READ_ONLY) {
                this.read_only = true;
            }
        },

        doubleClickForm() {
            if (!this.read_only && this.action_parameter !== 'view') {
                this.edit_form = true;
            }
        },

        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.company_code !== '' || this.forceLoad) {
                this.loadCompanyDocument();
            }
        },

        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.company_documents_list = [];

            this.company_documents_list = JSON.parse(JSON.stringify(this.company_documents_list_orig));

            let current_documents_list = [];

            $.each(this.company_documents_list, function (key, value) {
                if (value.status !== 'new') {
                    current_documents_list.push(value);
                }
            });

            this.company_documents_list = current_documents_list;
        },

        addLine: function () {
            this.edit_form = true;

            this.company_documents_list.push({
                document_title: '',
                document_description: '',
                filename: null,
                filename_old: null,
                document_id: '',
                publish_to_owner: false,
                publish_to_tenant: false,
                status: 'new',
                is_external: 0,
                enable_external_link: 0,
                external_url: '',
            });
        },

        loadCompanyDocument: function () {
            this.loading_setting = true;
            this.edit_form = false;

            var formData = new FormData();
            formData.append('company_code', this.company_code);
            formData.append('no_load', true);

            const dataStatusSlug = this.is_temp ? 'unapproved/' : '';
            const apiUrl = `${this.cirrus8_api_url}api/${dataStatusSlug}company/fetch/documents`;

            this.$api.post(apiUrl, formData).then((response) => {
                this.company_documents_list = response.data.company_docs_list;
                this.company_documents_list_orig = this.company_documents_list;

                this.loading_setting = false;
            });
        },

        getIdOfUploadButton: function (id) {
            return 'fileUploadDocs_' + id;
        },

        shiftIndexValue: function (list, fromIndex, toIndex) {
            const itemToMove = list[fromIndex];
            const updatedList = list.toSpliced(fromIndex, 1);
            return updatedList.toSpliced(toIndex, 0, itemToMove);
        },

        setOrder: function (action, index) {
            let old_index = 0;
            let new_index = 0;

            if (action === 'updateSeqDown') {
                if (this.company_documents_list.length === index + 1) return false;

                old_index = index;
                new_index = index + 1;
            } else if (action === 'updateSeqUp') {
                if (index === 0) return false;

                old_index = index;
                new_index = index - 1;
            }

            this.company_documents_list = this.shiftIndexValue(this.company_documents_list, old_index, new_index);
        },

        setLast: function (indexToMove) {
            if (this.company_documents_list.length === indexToMove + 1) return false;

            const lastIndex = this.company_documents_list.length - 1;
            this.company_documents_list = this.shiftIndexValue(this.company_documents_list, indexToMove, lastIndex);
        },

        setFirst: function (indexToMove) {
            if (0 === indexToMove) return false;

            this.company_documents_list = this.shiftIndexValue(this.company_documents_list, indexToMove, 0);
        },

        saveForm: function () {
            let errorArr = [];

            this.company_documents_list.forEach((document, fileIndex) => {
                if (isEmpty(document.document_title)) {
                    errorArr.push([
                        `You have not entered a valid title for the document on row: ${Number(fileIndex) + 1}`,
                    ]);
                }

                if (isEmpty(document.document_description)) {
                    errorArr.push([
                        `You have not entered a valid description for the document on row: ${Number(fileIndex) + 1}`,
                    ]);
                }

                const filename = document.filename || document.external_url;
                if (isEmpty(filename)) {
                    errorArr.push([
                        `You have not entered a valid attachment for the document on row: ${Number(fileIndex) + 1}`,
                    ]);
                }
            });

            this.error_server_msg2 = errorArr;

            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                let formData = new FormData();
                formData.append('company_code', this.company_code);
                formData.append('no_load', true);
                formData.append('company_documents_list', JSON.stringify(this.company_documents_list));

                const dataStatusSlug = this.is_temp ? 'unapproved/' : '';
                const apiUrl = `${this.cirrus8_api_url}api/with-file-upload/${dataStatusSlug}company/update/document`;

                this.$api
                    .post(apiUrl, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.loadForm();
                        this.loading_setting = false;
                    });
            }
        },

        confirmDeleteDocument: function (index) {
            const status = this.company_documents_list[index].status;

            if (status === 'new') {
                this.company_documents_list.splice(index, 1);
            } else {
                this.warning_message =
                    'This document will be deleted. You will not be able to recover your current changes. Would you like to proceed?';
                this.dialog_confirmation = true;
                this.index_for_deletion = index;
            }
        },

        deleteDocument: function () {
            const documentId = this.company_documents_list[this.index_for_deletion].document_id;

            this.loading_setting = true;
            let formData = new FormData();
            formData.append('company_code', this.company_code);
            formData.append('document_id', documentId);
            formData.append('no_load', true);

            const dataStatusSlug = this.is_temp ? 'unapproved/' : '';
            const apiUrl = `${this.cirrus8_api_url}api/${dataStatusSlug}company/delete/document`;

            this.$api.post(apiUrl, formData).then((response) => {
                this.company_documents_list.splice(this.index_for_deletion, 1);
                this.loading_setting = false;
                this.resetDeleteDialog();

                this.company_documents_list_orig = JSON.parse(JSON.stringify(this.company_documents_list));
            });
        },

        resetDeleteDialog: function () {
            this.dialog_confirmation = false;
            this.warning_message = '';
            this.index_for_deletion = 0;
        },

        modalSubmitData: function () {
            let errorArr = [];
            let document_title = this.company_document_add_arr.document_title;
            let document_description = this.company_document_add_arr.document_description;
            let filename = this.company_document_add_arr.filename || this.company_document_add_arr.external_url;

            if (document_title === '') {
                errorArr.push(['You have not entered a valid title for the document.']);
            }
            if (document_description === '') {
                errorArr.push(['You have not entered a valid description for the document.']);
            }
            if (filename === '') {
                errorArr.push(['You have not entered a valid attachment for the document.']);
            }

            this.error_server_msg2 = errorArr;

            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;

                this.submitBtnLoading = true;

                let companyDocumentDataArray = [];
                companyDocumentDataArray[0] = this.company_document_add_arr;

                let formData = new FormData();
                formData.append('company_code', this.company_code);
                formData.append('no_load', true);
                formData.append('company_documents_list', JSON.stringify(companyDocumentDataArray));

                if (this.company_document_add_arr.filename !== this.company_document_add_arr.filename_old) {
                    formData.append('company_docs_file_0', this.company_document_add_arr.filename[0]);
                }

                const dataStatusSlug = this.is_temp ? 'unapproved/' : '';
                const apiUrl = `${this.cirrus8_api_url}api/with-file-upload/${dataStatusSlug}company/create/document`;

                this.$api
                    .post(apiUrl, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.company_documents_list = [];
                        this.loadForm();
                        this.edit_form = false;
                        this.AED_modal = false;
                        this.loading_setting = false;
                        this.submitBtnLoading = false;
                    });
            }
        },

        modalAddData: function () {
            this.AED_modal = true;
            this.error_server_msg = [];
            this.error_server_msg2 = [];
            this.company_document_add_arr = {
                index: 'New',
                document_title: '',
                document_description: '',
                filename: null,
                filename_old: null,
                document_id: '',
                publish_to_owner: false,
                publish_to_tenant: false,
                status: 'new',
                is_external: 0,
                enable_external_link: 0,
                external_url: '',
            };
        },

        downloadS3File: function (this_file, this_file_name) {
            const params = new FormData();
            params.append('s3path', this_file);

            const api_url = 'documents/download-s3';
            this.$api
                .post(api_url, params)
                .then((response) => {
                    if (response.data.errorMessage) {
                        this.$noty.error(response.data.errorMessage);
                        return;
                    }

                    this.fileDL(response.data.s3File, this_file_name, '');
                })
                .catch((errorResponse) => {
                    this.$noty.error(
                        errorResponse.response.data.message ?? 'Something went wrong with the downloading of S3 file.',
                    );
                });
        },

        setActiveVersion: function (status) {
            this.SET_DOC_ACTIVE_VERSION(status);
        },
    },
    watch: {
        company_code: function () {
            this.loadForm();
        },
    },
    mixins: [global_mixins],
};
</script>
