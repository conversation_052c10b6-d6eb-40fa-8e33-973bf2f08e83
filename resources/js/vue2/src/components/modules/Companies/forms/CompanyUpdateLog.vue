<style>
.v-dialog .company-update-log-container input {
    padding-left: 0px !important;
    padding-right: 0px !important;
}
</style>
<template>
    <div class="company-update-log-container">
        <v-skeleton-loader
            class="mx-auto"
            type="table"
            :loading="loading"
            style="position: relative"
        >
            <v-btn
                @click="exportToXLSX()"
                v-if="this.company_update_log_list.length > 0"
                depressed
                small
                style="position: absolute; z-index: 99"
            >
                Export to Excel
            </v-btn>
            <v-card elevation="0">
                <v-card-title style="padding-top: 0px">
                    <v-spacer></v-spacer>
                    <v-text-field
                        v-model="search"
                        append-icon="search"
                        label="Search"
                        single-line
                        hide-details
                        style="padding-top: 0px; margin-top: 0px"
                    ></v-text-field>
                </v-card-title>
                <v-data-table
                    item-key="id"
                    :headers="headers"
                    :items="company_update_log_list"
                    :search="search"
                >
                    <template v-slot:item.index="{ item }">
                        {{ company_update_log_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.field_name="{ item }">
                        <span v-html="item.field_name"></span>
                    </template>
                    <template v-slot:item.old_value="{ item }">
                        <span>{{ item.old_value }}</span>
                    </template>
                    <template v-slot:item.new_value="{ item }">
                        <span>{{ item.new_value }}</span>
                    </template>
                </v-data-table>
            </v-card>
        </v-skeleton-loader>
        <v-dialog
            v-model="overview_loading"
            hide-overlay
            persistent
            width="300"
        >
            <v-card
                color="primary"
                dark
            >
                <v-card-text>
                    {{ overview_loading_msg }}
                    <v-progress-linear
                        indeterminate
                        color="white"
                        class="mb-0"
                    ></v-progress-linear>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import moment from 'moment';
import { mapState } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
export default {
    props: {
        company_code: { type: String, default: '' },
        action_parameter: { type: String, default: '' },
        supplier_types: { type: String, default: '' },
        company_group: { type: String, default: '' },
    },
    data() {
        return {
            loading: false,
            search: '',
            headers: [
                {
                    text: '#',
                    value: 'index',
                    sortable: false,
                    width: '5%',
                },
                { text: 'Field Name', value: 'field_name', sortable: true, width: '15%' },
                { text: 'Old Value', value: 'old_value', sortable: false, width: '20%' },
                { text: 'New Value', value: 'new_value', sortable: false, width: '20%' },
                { text: 'User', value: 'modified_by', sortable: true, width: '15%' },
                { text: 'Date', value: 'date_modified', sortable: true, width: '15%' },
            ],
            company_update_log_list: [],
            overview_loading: false,
            overview_loading_msg: 'Please wait...',
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadUpdateLogs();
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url']),
    },
    methods: {
        loadUpdateLogs: function () {
            if (this.company_code !== '') {
                this.loading = true;

                var form_data = new FormData();
                form_data.append('user_type', this.user_type);
                form_data.append('company_code', this.company_code);
                form_data.append('action_parameter', this.action_parameter);
                form_data.append('supplier_types', this.supplier_types);
                form_data.append('company_group', this.company_group);
                form_data.append('no_load', true);

                let api_url = this.cirrus8_api_url + 'api/company/fetch/update-log';

                this.$api.post(api_url, form_data).then((response) => {
                    this.company_update_log_list = this.convertToArrayOfObjects(response.data);
                    this.loading = false;
                });
            }
        },
        exportToXLSX: function () {
            if (this.company_code !== '') {
                let params = {
                    company_code: this.company_code,
                    action_parameter: this.action_parameter,
                    supplier_types: this.supplier_types,
                    company_group: this.company_group,
                    no_load: true,
                };

                this.overview_loading = true;
                this.overview_loading_msg = 'Generating excel file';

                this.$api.post('company/export-update-log', this.req(params)).then((response) => {
                    let res = response.data;
                    this.overview_loading = false;
                    this.overview_loading_msg = 'Please wait...';

                    if (res.file && res.file.name && res.file.type && res.file.data) {
                        this.printDownload(res.file.data, res.file.name + moment().unix(), res.file.type);
                    } else {
                        if (res.error) this.$noty.error(res.error);
                        else this.$noty.error('Generate report failed. Please try again.');
                    }
                });
            }
        },
    },
    created() {},
    mixins: [global_mixins],
};
</script>
