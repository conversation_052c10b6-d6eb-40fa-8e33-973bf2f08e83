<style>
.company-contacts tbody td {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.company-contacts thead th,
.company-contacts tbody td {
    padding-left: 16.5px !important;
    padding-right: 16.5px !important;
}

.company-contacts tbody .primary-contact {
    background: #f7ffe6;
}

.v-menu__content {
    position: fixed !important;
    font-weight: normal !important;
    font-size: 11px !important;
    z-index: 999999 !important;
    color: inherit !important;
}

.v-dialog .multiselect__content-wrapper {
    position: fixed;
    max-width: inherit;
}

.v-dialog__content {
    background-color: transparent !important;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    background-color: rgba(0, 0, 0, 1);
    text-decoration: none;
}

.c8-page .c8-datatable-custom .v-data-table-header th {
    background: #eaeaea !important;
}
</style>

<template>
    <v-container
        fluid
        class="c8-page"
        @dblclick="doubleClickForm()"
    >
        <div>
            <v-card
                id="contact_details_header"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Contacts</h6>
                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        data-tooltip="Add Contact"
                        v-if="!read_only"
                        v-show="action_parameter != 'view'"
                        icon
                        @click="modalAddData()"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>

                    <v-btn
                        x-small
                        data-tooltip="Edit"
                        v-if="form_mode !== 1 && !edit_form && !read_only"
                        v-show="action_parameter != 'review' && action_parameter != 'view'"
                        class="v-step-edit-button"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>

                    <v-btn
                        x-small
                        data-tooltip="Done"
                        v-if="edit_form && form_mode !== 1"
                        v-show="action_parameter != 'review' && action_parameter != 'view'"
                        class="v-step-revert-button"
                        icon
                        @click="resetForm()"
                    >
                        <v-icon color="red">undo</v-icon>
                    </v-btn>

                    <v-btn
                        x-small
                        data-tooltip="Refresh"
                        v-if="form_mode !== 1"
                        class="v-step-refresh-button"
                        icon
                        @click="loadContactList()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>

            <div v-if="!loading_setting">
                <div class="c8-page-table">
                    <sui-table
                        class="vue-data-grid company-contacts"
                        stackable
                        width="100%"
                        cellpadding="0"
                        cellspacing="0"
                        border="0"
                        v-show="contact_list.length > 0"
                    >
                        <sui-table-header>
                            <sui-table-row class="company-contact-header">
                                <sui-table-header-cell style="width: 5px">#</sui-table-header-cell>
                                <sui-table-header-cell>Name</sui-table-header-cell>
                                <sui-table-header-cell>Type</sui-table-header-cell>
                                <sui-table-header-cell>Details</sui-table-header-cell>
                                <sui-table-header-cell></sui-table-header-cell>
                            </sui-table-row>
                        </sui-table-header>
                        <sui-table-body class="page-form">
                            <sui-table-row
                                :class="
                                    contact_list_data.contact_primary === '1' ? 'form-row primary-contact' : 'form-row'
                                "
                                v-for="(contact_list_data, index) in contact_list"
                                :key="index"
                            >
                                <sui-table-cell
                                    v-if="!edit_form"
                                    verticalAlign="top"
                                    style="width: 5px"
                                >
                                    <span class="form-input-text">{{ index + 1 }}</span>
                                </sui-table-cell>
                                <sui-table-cell
                                    v-if="edit_form"
                                    verticalAlign="top"
                                    style="width: 5px; padding-top: 0.95em !important"
                                >
                                    <span class="form-input-text">{{ index + 1 }}</span>
                                </sui-table-cell>
                                <sui-table-cell
                                    class="form-input--no-pad-top"
                                    verticalAlign="top"
                                    style="width: 338px"
                                >
                                    <cirrus-input
                                        :size="'45'"
                                        :maxlength="40"
                                        :id="'contact_name'"
                                        v-model="contact_list_data.contact_name"
                                        :edit_form="false"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </sui-table-cell>
                                <sui-table-cell
                                    class="form-input--no-pad-top"
                                    verticalAlign="top"
                                    style="width: 220px"
                                >
                                    <span class="form-input-text">{{ contact_list_data.contact_type.label }}</span>
                                </sui-table-cell>
                                <sui-table-cell
                                    class="form-input--no-pad-top"
                                    verticalAlign="top"
                                    style="padding: 1px 12.5px !important"
                                >
                                    <table class="table-raw data-grid-no-line contact-details">
                                        <tr
                                            v-for="(
                                                contact_details_data, index_detail
                                            ) in contact_list_data.contact_details"
                                            :key="index_detail"
                                        >
                                            <td style="padding: 0px 2px !important">
                                                <span class="form-input-text">{{ index_detail + 1 + '.' }} &nbsp;</span>
                                            </td>
                                            <td style="padding: 0px 2px !important">
                                                <span class="form-input-text"
                                                    ><strong
                                                        >{{ contact_details_data.phone_type.label }}:
                                                    </strong></span
                                                >

                                                <cirrus-input
                                                    v-if="contact_details_data.phone_type.label == 'E-Mail'"
                                                    :inputFormat="'emailClickable'"
                                                    :size="'30'"
                                                    :id="'contact_name'"
                                                    v-model="contact_details_data.contact_detail"
                                                    :edit_form="false"
                                                    :error_msg="error_msg"
                                                ></cirrus-input>

                                                <cirrus-input
                                                    v-if="
                                                        contact_details_data.phone_type.label == 'Mobile Phone' ||
                                                        contact_details_data.phone_type.label == 'Owner Mobile' ||
                                                        contact_details_data.phone_type.label == 'Service Mobile' ||
                                                        contact_details_data.phone_type.label == 'Tenant Mobile'
                                                    "
                                                    :inputFormat="'phoneClickable'"
                                                    :size="'30'"
                                                    :id="'contact_name'"
                                                    v-model="contact_details_data.contact_detail"
                                                    :edit_form="false"
                                                    :error_msg="error_msg"
                                                ></cirrus-input>

                                                <cirrus-input
                                                    v-if="
                                                        contact_details_data.phone_type.label != 'Mobile Phone' &&
                                                        contact_details_data.phone_type.label != 'Owner Mobile' &&
                                                        contact_details_data.phone_type.label != 'Service Mobile' &&
                                                        contact_details_data.phone_type.label != 'Tenant Mobile' &&
                                                        contact_details_data.phone_type.label != 'E-Mail'
                                                    "
                                                    :size="'30'"
                                                    :id="'contact_name'"
                                                    v-model="contact_details_data.contact_detail"
                                                    :edit_form="false"
                                                    :error_msg="error_msg"
                                                ></cirrus-input>
                                                <v-btn
                                                    text
                                                    icon
                                                    color="green"
                                                    x-small
                                                    @click="show_sms_sending_modal(contact_details_data)"
                                                    style="position: absolute; margin-top: -3px"
                                                    v-if="displaySMSIcon(contact_details_data.phone_type.field_key)"
                                                >
                                                    <v-icon x-small>message</v-icon>
                                                </v-btn>
                                            </td>
                                        </tr>
                                    </table>
                                </sui-table-cell>
                                <sui-table-cell
                                    verticalAlign="top"
                                    style="text-align: right"
                                >
                                    <div
                                        v-if="edit_form"
                                        style="padding-top: 0.6em"
                                    >
                                        <v-btn
                                            x-small
                                            v-if="contact_list_data.contact_primary === '1'"
                                            disabled
                                            >Primary
                                        </v-btn>
                                        <v-btn
                                            x-small
                                            class="make-primary-btn"
                                            v-if="
                                                contact_list_data.contact_primary === '0' &&
                                                contact_list_data.status !== 'new'
                                            "
                                            @click="makePrimaryContact(index)"
                                            ><span class="make-primary-btn-label">Make Primary</span>
                                        </v-btn>
                                        <v-icon
                                            small
                                            @click="modalOpenAED(index)"
                                            v-if="edit_form"
                                            >fas fa-edit
                                        </v-icon>
                                        <v-btn
                                            x-small
                                            text
                                            icon
                                            color="red"
                                            @click="confirmDeleteContact(index)"
                                        >
                                            <v-icon color="red">close</v-icon>
                                        </v-btn>
                                    </div>
                                </sui-table-cell>
                            </sui-table-row>
                        </sui-table-body>
                    </sui-table>

                    <v-col
                        class="text-center"
                        v-if="contact_list.length === 0"
                        v-show="action_parameter != 'view'"
                    >
                        <v-btn
                            v-if="!read_only"
                            class="v-step-save-2-button"
                            depressed
                            small
                            color="success"
                            @click="modalAddData()"
                            >Add Contact
                        </v-btn>
                        <div
                            style="margin-top: 10px"
                            v-else
                        >
                            No contact entries at the moment
                        </div>
                    </v-col>
                </div>
            </div>
        </div>

        <!-- SHOW CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialog_confirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon color="warning">warning</v-icon>
                    WARNING
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="resetDeleteDialog()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div style="margin: 10">{{ warning_message }}</div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        v-if="contact_item"
                        @click="deleteContact()"
                        >Ok
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        v-if="contact_detail"
                        @click="deleteContactDetail()"
                        >Ok
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="resetDeleteDialog()"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CONFIRMATION DIALOG -->

        <!--   START OF ADD/EDIT DIALOG      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitData()"
        >
            <v-card>
                <v-card-title class="headline">
                    Contact Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>

                    <div :key="company_contact_add_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Name
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'45'"
                                        :maxlength="'40'"
                                        :id="'contact_name'"
                                        v-model="company_contact_add_arr.contact_name"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Type
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        v-model="company_contact_add_arr.contact_type"
                                        :options="contact_role_list"
                                        openDirection="bottom"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        group-label="language"
                                        placeholder="Select a contact type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="text-right"
                                >
                                    <v-btn
                                        x-small
                                        @click="modalAddLineData()"
                                    >
                                        <v-icon>add</v-icon>
                                        Add detail
                                    </v-btn>
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                >
                                    <v-simple-table
                                        fixed-header
                                        dense
                                        height="200px"
                                        class="c8-datatable-custom"
                                    >
                                        <template v-slot:default>
                                            <thead class="v-data-table-header">
                                                <tr>
                                                    <th
                                                        class="text-left"
                                                        style="width: 40px"
                                                    >
                                                        #
                                                    </th>
                                                    <th class="text-left">Type</th>
                                                    <th class="text-left">Details</th>
                                                    <th class="text-right"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    v-for="(
                                                        contact_details_data, index_detail
                                                    ) in company_contact_add_arr.contact_details"
                                                    :key="index_detail"
                                                >
                                                    <td class="text-left">
                                                        {{ index_detail + 1 }}
                                                    </td>
                                                    <td class="text-left">
                                                        <multiselect
                                                            v-model="contact_details_data.phone_type"
                                                            openDirection="bottom"
                                                            :options="contact_phone_type_list"
                                                            :allowEmpty="false"
                                                            class="vue-select2 dropdown-left dropdown-300"
                                                            group-label="language"
                                                            placeholder="Select a phone type"
                                                            track-by="field_key"
                                                            label="field_value"
                                                            :show-labels="false"
                                                            ><span slot="noResult"
                                                                >Oops! No elements found. Consider changing the search
                                                                query.</span
                                                            >
                                                        </multiselect>
                                                    </td>
                                                    <td class="text-left">
                                                        <cirrus-input
                                                            v-if="
                                                                contact_details_data.phone_type.field_value === 'E-Mail'
                                                            "
                                                            :inputFormat="'emailClickable'"
                                                            :size="'30'"
                                                            :maxlength="'60'"
                                                            :id="'contact_name'"
                                                            v-model="contact_details_data.contact_detail"
                                                            :edit_form="true"
                                                            :error_msg="error_msg"
                                                        ></cirrus-input>

                                                        <cirrus-input
                                                            v-if="
                                                                contact_details_data.phone_type.field_value ===
                                                                    'Mobile Phone' ||
                                                                contact_details_data.phone_type.field_value ===
                                                                    'Owner Mobile' ||
                                                                contact_details_data.phone_type.field_value ===
                                                                    'Service Mobile' ||
                                                                contact_details_data.phone_type.field_value ===
                                                                    'Tenant Mobile'
                                                            "
                                                            :inputFormat="'phoneClickable'"
                                                            :size="'30'"
                                                            :maxlength="'60'"
                                                            :id="'contact_name'"
                                                            v-model="contact_details_data.contact_detail"
                                                            :edit_form="true"
                                                            :error_msg="error_msg"
                                                        ></cirrus-input>

                                                        <cirrus-input
                                                            v-if="
                                                                contact_details_data.phone_type.field_value !==
                                                                    'Mobile Phone' &&
                                                                contact_details_data.phone_type.field_value !==
                                                                    'Owner Mobile' &&
                                                                contact_details_data.phone_type.field_value !==
                                                                    'Service Mobile' &&
                                                                contact_details_data.phone_type.field_value !==
                                                                    'Tenant Mobile' &&
                                                                contact_details_data.phone_type.field_value !== 'E-Mail'
                                                            "
                                                            :size="'30'"
                                                            :maxlength="'60'"
                                                            :id="'contact_name'"
                                                            v-model="contact_details_data.contact_detail"
                                                            :edit_form="true"
                                                            :error_msg="error_msg"
                                                        ></cirrus-input>
                                                        <cirrus-email-centralisation
                                                            v-model="contact_details_data.email_centralisation_setting"
                                                            :contact_table_name="
                                                                is_temp ? 'temp_company_contact' : 'company_contact'
                                                            "
                                                            :contact_table_id="contact_details_data.contact_detail_id"
                                                            v-if="
                                                                email_centralise_setup_config &&
                                                                true &&
                                                                contact_details_data.phone_type.value === 'EMAIL' &&
                                                                company_contact_add_arr.status !== 'new'
                                                            "
                                                            v-show="!contact_details_data.new_detail"
                                                            :key="'con-d-' + contact_details_data.contact_detail_id"
                                                        ></cirrus-email-centralisation>
                                                    </td>
                                                    <td class="text-right">
                                                        <v-icon
                                                            v-if="company_contact_add_arr.contact_details.length > 1"
                                                            color="red"
                                                            size="20"
                                                            @click="deleteContactDetailsV2(index_detail)"
                                                        >
                                                            close
                                                        </v-icon>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </template>
                                    </v-simple-table>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        data-position="left center"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Submit
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="company_contact_add_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        data-position="left center"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--      END OF ADD/EDIT DIALOG        -->
    </v-container>
</template>

<script>
import { mapState } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import isEmpty from 'lodash/isEmpty';
import axios from 'axios';
import { DIALOG_BTN_YES, DIALOG_BUTTONS, USER_TYPES } from '../../../../constants';
import { collectAddressBookSettings } from '../../../../utils/sharedFunctions';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        reload_components: { type: Boolean, default: false },
        company_code: { type: String, default: '' },
        is_debtor: { type: Boolean, default: false },
        is_owner: { type: Boolean, default: false },
        is_supplier: { type: Boolean, default: false },
        is_temp: { type: Boolean, default: false },
        action_parameter: { type: String, default: '' },
    },
    data() {
        return {
            user_sub_type: localStorage.getItem('user_sub_type'),
            read_only: false,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            AED_modal: false,
            success_flag: false,
            contact_list: [],
            company_contact_add_arr: [],
            contact_list_orig: [],
            contact_role_list: [],
            contact_phone_type_list: [],
            email_centralise_list: [],
            email_centralise_list_orig: [],
            email_centralise_send_config: 0,
            email_centralise_setup_config: 0,
            show_progress: true,
            warning_message: '',
            dialog_confirmation: '',
            contact_item: false,
            contact_detail: false,
            contact_id_for_deletion: 0,
            contact_detail_id_for_deletion: 0,
        };
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url', 'sms_sending_setup']),
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadForm();
        this.updateEmailAddressBook();
        this.checkURLParameters();
        this.checkAccess();
    },
    methods: {
        checkAccess() {
            if (this.user_sub_type === USER_TYPES.PM_READ_ONLY) {
                this.read_only = true;
            }
        },

        checkURLParameters: function () {
            if (this.action_parameter === 'review') {
                this.edit_form = false;
            }
        },

        displaySMSIcon: function (contactDetailType) {
            return this.sms_sending_setup && contactDetailType === 'MOBILE' && !this.is_temp;
        },

        doubleClickForm() {
            if (!this.read_only && this.action_parameter !== 'view') {
                this.edit_form = true;
            }
        },

        loadContactList: function (show_loading = true) {
            this.loading_setting = show_loading;
            this.edit_form = false;

            let form_data = new FormData();
            form_data.append('company_code', this.company_code);
            form_data.append('no_load', true);

            let api_url = '';
            if (this.is_temp) {
                api_url = this.cirrus8_api_url + 'api/temp/company/fetch/contact';
            } else {
                api_url = this.cirrus8_api_url + 'api/company/fetch/contact';
            }

            this.$api.post(api_url, form_data).then((response) => {
                this.contact_list = response.data.contact_list;
                this.contact_list_orig = this.contact_list;
                this.contact_role_list = response.data.contact_role_list;
                this.contact_phone_type_list = response.data.contact_phone_type_list;
                this.email_centralise_list = response.data.email_centralise_list['address_book'];
                this.email_centralise_list_orig = this.email_centralise_list;
                this.email_centralise_send_config = response.data.email_cen_send;
                this.email_centralise_setup_config = response.data.email_cen_setup;

                this.updateEmailAddressBook();

                this.loading_setting = false;
            });
        },

        loadForm: function (show_loading = true) {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.company_code !== '' || this.reload_components) {
                this.loadContactList(show_loading);
            } else {
                this.loading_setting = false;
            }

            if (this.action_parameter === 'review') {
                this.edit_form = true;
            }
        },

        resetForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.edit_form = false;
        },

        updateEmailAddressBook: function () {
            const company_types = { debtor: this.is_debtor, owner: this.is_owner, supplier: this.is_supplier };
            this.email_centralise_list = collectAddressBookSettings(this.email_centralise_list_orig, company_types);
            this.filterContactEmailAddressBook();
        },

        filterContactEmailAddressBook: function () {
            let contact_list = this.contact_list;
            const company_types = { debtor: this.is_debtor, owner: this.is_owner, supplier: this.is_supplier };

            this.contact_list_orig.forEach(function callback(contacts, index) {
                let contact_details = contacts['contact_details'];

                contact_details.forEach(function callback(details, detail_index) {
                    contact_list[index]['contact_details'][detail_index]['email_centralisation_setting'] =
                        collectAddressBookSettings(details['email_centralisation_setting'], company_types);
                });
            });
        },

        confirmDeleteContact: function (index) {
            if (this.contact_list[index].status === 'new') {
                this.contact_list.splice(index, 1);
            } else {
                this.warning_message =
                    'This contact will be deleted. You will not be able to undo this process. Would you like to proceed?';
                this.dialog_confirmation = true;
                this.contact_item = true;
                this.contact_id_for_deletion = index;
            }
        },

        deleteContact: function () {
            if (typeof this.contact_id_for_deletion !== 'undefined') {
                let form_data = new FormData();
                let index = this.contact_id_for_deletion;
                form_data.append('company_code', this.company_code);
                form_data.append('contact_list', JSON.stringify(this.contact_list[index]));
                form_data.append('no_load', true);

                let api_url = '';
                if (this.is_temp) {
                    api_url = this.cirrus8_api_url + 'api/temp/company/delete/contact';
                } else {
                    api_url = this.cirrus8_api_url + 'api/company/delete/contact';
                }

                this.$api.post(api_url, form_data).then((response) => {
                    this.contact_list.splice(index, 1);
                    this.resetDeleteDialog();
                });
            }
        },

        deleteContactDetail: function () {
            if (
                typeof this.contact_id_for_deletion !== 'undefined' &&
                typeof this.contact_detail_id_for_deletion !== 'undefined'
            ) {
                let form_data = new FormData();
                let index = this.contact_id_for_deletion;
                let index_detail = this.contact_detail_id_for_deletion;
                form_data.append('company_code', this.company_code);
                form_data.append(
                    'contact_details_list',
                    JSON.stringify(this.contact_list[index].contact_details[index_detail]),
                );
                form_data.append('no_load', true);

                let api_url = '';
                if (this.is_temp) {
                    api_url = this.cirrus8_api_url + 'api/temp/company/delete/contact-detail';
                } else {
                    api_url = this.cirrus8_api_url + 'api/company/delete/contact-detail';
                }

                this.$api.post(api_url, form_data).then((response) => {
                    this.contact_list[index].contact_details.splice(index_detail, 1);
                    this.resetDeleteDialog();
                });
            }
        },
        async deleteContactDetailsV2(index_detail) {
            let contact_detail_id = this.company_contact_add_arr.contact_details[index_detail].contact_detail_id;

            if (contact_detail_id === '0') {
                this.company_contact_add_arr.contact_details.splice(index_detail, 1);
                if (this.company_contact_add_arr.status !== 'new') {
                    this.company_contact_add_arr.status = 'unsaved';
                }
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: DIALOG_BUTTONS,
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === DIALOG_BTN_YES) {
                    this.show_progress = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('no_load', true);

                    form_data.append(
                        'contact_details_list',
                        JSON.stringify(this.company_contact_add_arr.contact_details[index_detail]),
                    );

                    let api_url = '';
                    if (this.is_temp) {
                        api_url = this.cirrus8_api_url + 'api/temp/company/delete/contact-detail';
                    } else {
                        api_url = this.cirrus8_api_url + 'api/company/delete/contact-detail';
                    }
                    this.$api.post(api_url, form_data).then((response) => {
                        this.company_contact_add_arr.contact_details.splice(index_detail, 1);
                        this.company_contact_add_arr.status = 'saved';
                        this.show_progress = false;
                    });
                }
            }
        },

        resetDeleteDialog: function () {
            this.dialog_confirmation = false;
            this.warning_message = '';
            this.contact_item = false;
            this.contact_detail = false;
            this.contact_id_for_deletion = 0;
            this.contact_detail_id_for_deletion = 0;
        },

        makePrimaryContact: function (index) {
            this.loading_setting = true;
            this.show_progress = true;
            let contact_serial = this.contact_list[index].contact_serial;

            var form_data = new FormData();
            form_data.append('company_code', this.company_code);
            form_data.append('no_load', true);
            form_data.append('contact_serial', contact_serial);

            let api_url = '';
            if (this.is_temp) {
                api_url = this.cirrus8_api_url + 'api/temp/company/update/make-primary-contact';
            } else {
                api_url = this.cirrus8_api_url + 'api/company/update/make-primary-contact';
            }

            this.$api.post(api_url, form_data).then((response) => {
                this.loadForm();
                this.loading_setting = false;
                this.show_progress = false;
            });
        },

        modalAddLineData: function () {
            this.AED_modal = true;
            this.updateEmailAddressBook();
            this.company_contact_add_arr.contact_details.push({
                contact_detail_id: '0',
                contact_detail_serial: '0',
                contact_detail_code: '',
                contact_detail_code_desc: '',
                contact_detail: '',
                primary_flag: '0',
                email_centralisation_setting: [],
                phone_type: { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' },
                new_detail: '1',
            });
        },

        modalSubmitData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let errorArr = [];
            let contact_name = this.company_contact_add_arr.contact_name;
            let contact_type = this.company_contact_add_arr.contact_type.value;
            let contact_details = this.company_contact_add_arr.contact_details;

            if (isEmpty(contact_name)) {
                errorArr.push(['You have not entered a valid contact name.']);
            }
            if (isEmpty(contact_type)) {
                errorArr.push(['You have not entered a valid contact type.']);
            }

            contact_details.forEach((contactDetail, detailIndex) => {
                if (isEmpty(contactDetail.phone_type.value)) {
                    errorArr.push([`You have not entered a valid detail type on row: ${Number(detailIndex) + 1}`]);
                }
                if (isEmpty(contactDetail.contact_detail)) {
                    errorArr.push([`You have not entered a valid contact detail on row: ${Number(detailIndex) + 1}`]);
                }
            });

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                this.show_progress = true;
                let company_contact_add_arr_te = [];
                company_contact_add_arr_te[0] = this.company_contact_add_arr;

                var form_data = new FormData();
                form_data.append('company_code', this.company_code);
                form_data.append('no_load', true);
                form_data.append('contact_list', JSON.stringify(company_contact_add_arr_te));

                let api_url = '';
                if (this.is_temp) {
                    api_url = this.cirrus8_api_url + 'api/temp/company/update-or-create/contacts';
                } else {
                    api_url = this.cirrus8_api_url + 'api/company/update-or-create/contacts';
                }
                this.$api.post(api_url, form_data).then((response) => {
                    if (response.data.error_server_msg2) {
                        this.error_server_msg2 = response.data.error_server_msg2;
                    } else {
                        this.edit_form = false;
                        this.AED_modal = false;
                        this.loadForm(false);
                    }
                    this.show_progress = false;

                    this.loading_setting = false;
                });
            }
        },

        modalAddData: function () {
            this.AED_modal = true;
            this.company_contact_add_arr = {
                item_no: 'New',
                contact_id: '0',
                contact_serial: '0',
                contact_name: '',
                contact_create_date: '',
                contact_active: '',
                contact_role_desc: '',
                contact_role: '',
                contact_primary: '0',
                contact_details: [
                    {
                        contact_detail_id: '0',
                        contact_detail_serial: '0',
                        contact_detail_code: '',
                        contact_detail_code_desc: '',
                        contact_detail: '',
                        primary_flag: '0',
                        email_centralisation_setting: [],
                        phone_type: {
                            value: '',
                            label: 'Please select ...',
                            field_key: '',
                            field_value: 'Please select ...',
                        },
                    },
                ],
                contact_type: {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                status: 'new',
            };
        },

        modalOpenAED: function (index) {
            this.edit_form = true;
            this.AED_modal = true;
            this.company_contact_add_arr = this.objectClone(this.contact_list[index]);
        },

        show_sms_sending_modal: function (contact_details_data) {
            bus.$emit('toggleSMSSendingModal', {
                property_code: '',
                lease_code: '',
                company_code: this.company_code,
                contact_detail_id: contact_details_data.contact_detail_id,
                form_section: 'company-contact-section',
            });
        },
    },
    watch: {
        company_code: function () {
            this.loadForm();
        },

        is_debtor: function () {
            this.loadForm();
        },

        is_owner: function () {
            this.loadForm();
        },

        is_supplier: function () {
            this.loadForm();
        },
    },
    mixins: [global_mixins],
};
</script>
