<style>
.property-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    transition: all 0.2s;
}

.property-item {
    flex-basis: 33%;
    min-height: 150px;
    padding: 15px;
    margin: 10px 0px;
}

.property-item .title {
    width: 110px !important;
}

.property-item .image-container {
    padding: 15px 6px;
    min-height: 140px;
    min-width: 100px;
}

@media screen and (max-width: 1280px) {
    .property-item {
        flex-basis: 50%;
    }
}

@media screen and (max-width: 812px) {
    .property-item {
        flex-basis: 100%;
    }
}
</style>
<template>
    <v-container
        fluid
        class="c8-page"
    >
        <div>
            <v-card
                id="contact_details_header"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Property List</h6>
                    <v-spacer></v-spacer>

                    <v-btn
                        v-if="property_count == 0 || current_page == 1"
                        x-small
                        icon
                        disabled
                        data-tooltip="Previous"
                    >
                        <v-icon>arrow_back</v-icon>
                    </v-btn>
                    <v-btn
                        v-else
                        x-small
                        icon
                        data-tooltip="Previous"
                        @click="prevPage()"
                    >
                        <v-icon>arrow_back</v-icon>
                    </v-btn>

                    <span style="padding: 0px 5px">{{ current_range }} &nbsp;of&nbsp; {{ property_count }}</span>

                    <v-btn
                        v-if="property_count == 0 || current_page == last_page"
                        x-small
                        icon
                        disabled
                        data-tooltip="Next"
                    >
                        <v-icon>arrow_forward</v-icon>
                    </v-btn>
                    <v-btn
                        v-else
                        x-small
                        icon
                        data-tooltip="Next"
                        @click="nextPage()"
                    >
                        <v-icon>arrow_forward</v-icon>
                    </v-btn>

                    <v-btn
                        x-small
                        data-tooltip="Refresh"
                        v-if="form_mode !== 1"
                        class="v-step-refresh-button"
                        icon
                        @click="loadPropertyList()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>

            <div v-if="!loading_setting">
                <div class="c8-page-table">
                    <div
                        v-if="property_count > 0"
                        class="property-container"
                    >
                        <div
                            class="property-item"
                            v-for="(property_data, index) in property_list"
                            :key="index"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        rowspan="4"
                                        style="width: 30%"
                                    >
                                        <div class="image-container">
                                            <img
                                                v-if="property_data.file_name && property_data.file_name != ''"
                                                class="profile-pic"
                                                height="100px"
                                                :src="
                                                    uploaded_assets_domain +
                                                    'assets/images/property-image/' +
                                                    property_data.file_name
                                                "
                                            />
                                            <img
                                                v-else
                                                class="profile-pic"
                                                height="100px"
                                                :src="asset_domain + 'assets/images/icons/default-property-image.jpg'"
                                            />
                                        </div>
                                    </td>
                                    <td class="title">Property Code</td>
                                    <td>
                                        <span v-if="property_data.is_ledger == 1">
                                            <a
                                                target="_blank"
                                                v-if="user == 'A'"
                                                :href="
                                                    '?module=salesTrust&command=manage_ledger_v2&ledger_code=' +
                                                    property_data.prop_code
                                                "
                                                >{{ property_data.prop_code }}</a
                                            >
                                            <span v-else>{{ property_data.prop_code }}</span>
                                        </span>
                                        <span v-else>
                                            <a
                                                target="_blank"
                                                :href="
                                                    user == 'A'
                                                        ? '?module=properties&command=v2_manage_property_page&property_code=' +
                                                          property_data.prop_code
                                                        : '?module=properties&command=property_summary_page&property_code=' +
                                                          property_data.prop_code
                                                "
                                                >{{ property_data.prop_code }}</a
                                            >
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Property Name</td>
                                    <td>{{ property_data.prop_name }}</td>
                                </tr>
                                <tr>
                                    <td class="title">% Owned</td>
                                    <td>{{ property_data.share_owner }}</td>
                                </tr>
                                <tr>
                                    <td class="title">{{ property_manager_label }}</td>
                                    <td>{{ property_data.manager }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div
                        v-else
                        style="padding: 15px 6px"
                    >
                        No items to display at the moment.
                    </div>
                </div>
            </div>
        </div>
    </v-container>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        reload_components: { type: Boolean, default: false },
        company_code: { type: String, default: '' },
        user: { type: String, default: '' },
    },
    data() {
        return {
            property_manager_label: 'Property Manager',
            asset_domain: this.$assetDomain,
            uploaded_assets_domain: this.$uploadedAssetDomain,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            property_list: [],
            property_count: 0,
            current_page: 1,
            limit: 12,
            last_page: 0,
            prev_page: 0,
            next_page: 0,
            current_range: '',
        };
    },
    computed: {
        ...mapState(['user_type', 'cirrus8_api_url']),
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadPropertyList();
        this.loadCountryDefaults();
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.property_manager_label = this.ucwords(this.country_defaults.property_manager);
            });
        },

        loadPropertyList: function () {
            this.current_page = 1;
            this.fetchPropertyData(this.current_page);
        },

        fetchPropertyData: function (page) {
            let form_data = new FormData();
            form_data.append('company_code', this.company_code);
            form_data.append('current_page', page);
            form_data.append('limit', this.limit);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'api/company/fetch/properties';
            this.loading_setting = true;

            this.$api.post(api_url, form_data).then((response) => {
                this.property_list = response.data.properties;
                this.property_count = response.data.total_count;
                this.last_page = response.data.last_page;
                this.prev_page = response.data.prev_page;
                this.next_page = response.data.next_page;
                this.current_range = response.data.current_range;

                this.loading_setting = false;
            });
        },

        nextPage: function () {
            this.current_page++;
            this.fetchPropertyData(this.next_page);
        },

        prevPage: function () {
            this.current_page--;
            this.fetchPropertyData(this.prev_page);
        },
    },
    watch: {
        company_code: function () {
            this.loadPropertyList();
        },
    },
    mixins: [global_mixins],
};
</script>
