<style>
.mainDiv .content-ml-sm {
    overflow-x: hidden !important;
}

.shortcut-div > .v-toolbar__content {
    height: 30px !important;
}

.v-card > .shortcut-div {
    height: 30px !important;
}

.v-autocomplete__content.v-menu__content,
.v-autocomplete__content.v-menu__content .v-card {
    border-radius: 0;
    font-weight: normal !important;
}

.v-list--dense .v-list-item .v-list-item__subtitle,
.v-list--dense .v-list-item .v-list-item__title,
.v-list-item--dense .v-list-item__subtitle,
v-list-item--dense .v-list-item__title {
    font-weight: normal !important;
}

.v-list-item .v-list-item__mask {
    color: #ffffff !important;
    background: #00baf2 !important;
    font-weight: bold !important;
}

.v-list-item.v-list-item--highlighted:before {
    opacity: 0 !important;
}

td.required {
    min-width: 18px;
}

.c8-page .multiselect .multiselect__tags .multiselect__placeholder {
    line-height: 16px;
}

.property-code-dropdown + div {
    position: relative;
    left: -2px;
}

.property-code-dropdown + div button {
    min-width: 50px;
}

.property-code-dropdown + div button i {
    color: rgba(0, 0, 0, 0.87);
}

.commission-range-amount input {
    min-width: 210px;
    max-width: 210px;
}

.v-btn--fab.v-size--x-small .v-icon,
.v-btn--icon.v-size--x-small .v-icon {
    font-size: 21px;
}

.business-prefix {
    border: 1px solid #ececec;
    border-radius: 2px;
    padding: 6px 10px 7px;
}

.abn-result-table {
    max-width: 390px;
    width: 100%;
    border: 1px solid #ececec;
}

.abn-result-table tr:nth-child(odd) {
    background-color: #f0f0f0;
}

.abn-result-table th,
.abn-result-table td {
    text-align: left;
}

.abn-message {
    color: crimson;
    font-weight: bold;
    min-height: 30px;
    padding-top: 2px;
}

.abn-detail-container {
    height: 30px;
    margin-top: -30px;
    margin-left: 302px;
}

.loading-info-text {
    position: relative;
    top: 1px;
}

.manage-company-alert .v-chip {
    margin-left: 0px !important;
    margin-top: 5px !important;
}

.feedback_section .feedback-form {
    display: flex;
    align-items: right;
    padding: 3px 0px;
}

.feedback_section .feedback-label {
    vertical-align: middle;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0px 6px;
    flex: 1;
}

.feedback_section .feedback-label div {
    width: 100%;
    text-align: right;
}

.feedback_section .feedback-action {
    border-top: 1px solid #6bc9ff;
    border-radius: 0px;
}

.feedback_section .feedback-container {
    border: 1px solid #ececec;
    min-height: 85px;
    min-width: 350px;
    padding: 6px;
}

.company-status-indicator {
    position: absolute;
    top: 3px;
    right: 3px;
    font-size: 11px;
    font-weight: 700;
    padding: 3px 10px;
    margin-left: 5px;
    border-radius: 4px;
    text-transform: uppercase;
}

.company-approved-status {
    background-color: #4caf50;
    color: #fff;
}

.company-rejected-status {
    background-color: #b71c1c;
    color: #fff;
}

.company-pending-status {
    background-color: #e0e1e2;
    color: rgba(0, 0, 0, 0.6);
}

.company-code-readonly {
    width: 350px;
    height: 30px;
    padding: 6px 10px;
    border: 1px solid #ececec;
    border-radius: 2px;
    margin-bottom: 1px;
    position: relative;
}

.company-approval-info {
    color: #7f8c8d !important;
    position: relative;
    top: -25px;
    font-size: 16px;
}

.go-to-live-company-btn {
    position: absolute !important;
    top: 0px;
    right: -30px;
}

.go-to-live-company-btn i {
    font-size: 20px !important;
}

.approve-company-btn.theme--dark.v-btn.v-btn--disabled.v-btn--has-bg {
    background-color: #4caf50 !important;
    border-color: #4caf50 !important;
    opacity: 0.5;
}

.reject-company-btn.theme--dark.v-btn.v-btn--disabled.v-btn--has-bg {
    background-color: #b71c1c !important;
    border-color: #b71c1c !important;
    opacity: 0.5;
}

.save-company-btn.theme--dark.v-btn.v-btn--disabled.v-btn--has-bg {
    background-color: #4caf50 !important;
    border-color: #4caf50 !important;
    opacity: 0.5;
}

.display-none {
    display: none !important;
}

pre.pre-content {
    font-size: 11px;
    font-family: 'Segoe UI', Helvetica, Arial, Tahoma, sans-serif;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <cirrus-loader v-if="showTopLoadingBar"></cirrus-loader>
        <v-row class="ma-0">
            <v-col
                xs="12"
                sm="12"
                md="9"
                lg="9"
            >
                <v-btn
                    depressed
                    small
                    color="default"
                    v-if="returnToEft"
                    style="margin-right: 5px"
                    @click="returnToEFT()"
                >
                    <v-icon>arrow_left</v-icon>
                    Return to View EFTs <span v-show="this.clientCountry === 'AU'">&nbsp;and BPays</span>
                </v-btn>

                <v-btn
                    depressed
                    small
                    color="default"
                    v-if="returnToLease"
                    style="margin-right: 5px"
                    @click="returnToLeaseSummary()"
                >
                    <v-icon>arrow_left</v-icon>
                    Return to Lease Summary
                </v-btn>
                <v-toolbar flat>
                    <v-toolbar-title>
                        <cirrus-page-header :title="manageCompanyTitle" />
                        <span
                            v-if="company_approver && pageAction == 'view'"
                            class="company-approval-info"
                            >Approved on {{ company_approval_date }} by {{ company_approver }}</span
                        >
                    </v-toolbar-title>
                </v-toolbar>
                <div
                    v-if="isNewCompany() && userIsNotTrustAccountant()"
                    v-show="pageAction != 'view'"
                    style="position: relative; top: -15px; padding: 4px 16px"
                >
                    <b
                        >The new company form is used to advise {{ trustAccountLabel }}s of new supplier/s and owner/s
                        details.</b
                    >
                    <br />The system classifies tenants (debtor), owners and suppliers as companies. The tenant is
                    normally set up as a debtor company when the new lease form is completed. The owner and supplier are
                    set up by completing this company form.
                    <br />
                </div>

                <div class="page-form pt-7">
                    <v-row
                        v-if="isExistingCompany() && pageAction != 'review' && pageAction != 'view'"
                        class="form-row"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Company:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="9"
                            md="8"
                            class="form-input"
                        >
                            <!--O: dropdown START-->
                            <cirrus-single-select-group
                                class="property-code-dropdown dropdown-left dropdown-300"
                                v-model="companyCode"
                                :options="dd_company_list"
                                group-values="fieldGroupValues"
                                group-label="fieldGroupNames"
                                track-by="field_key"
                                label="field_value"
                                :selectedValue="selectedCompanyCode"
                                :displayDelay="true"
                                ref="companyCodeDropDown"
                            />
                            <v-btn
                                :loading="companyListLoadingAnimation"
                                class="form-text-button mb-0 rounded-l-0"
                                color="normal"
                                depressed
                                elevation="0"
                                small
                                @click="forceRerender()"
                                style="margin-top: -1px"
                            >
                                <v-icon>arrow_right</v-icon>
                            </v-btn>
                            <v-tooltip top>
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        depressed
                                        elevation="0"
                                        small
                                        color="primary"
                                        height="30"
                                        class="rounded"
                                        v-if="userIsTrustAccountant() && isExistingCompany()"
                                        v-show="pageAction != 'review'"
                                        @click="newCompany()"
                                        v-bind="attrs"
                                        v-on="on"
                                        style="margin-bottom: 1px"
                                    >
                                        <v-icon>add</v-icon>
                                    </v-btn>
                                </template>
                                <span>New Company</span>
                            </v-tooltip>
                        </v-col>
                    </v-row>
                    <v-row
                        v-if="isNewCompany() || pageAction == 'review' || pageAction == 'view'"
                        class="form-row"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Company Code:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-input
                                v-if="pageAction != 'load' && pageAction != 'view'"
                                class="v-step-new-company-code"
                                v-model="newCompanyCode"
                                size="10"
                                :id="'new_company_code'"
                                :edit_form="true"
                                :error_msg="errorMessage"
                                :maxlength="10"
                            ></cirrus-input>
                            <div
                                v-else
                                class="company-code-readonly"
                            >
                                {{ newCompanyCode }}

                                <span
                                    class="company-status-indicator company-pending-status"
                                    v-if="companyIsPending() && (pageAction == 'view' || pageAction == 'load')"
                                >
                                    Pending Review
                                </span>
                                <span
                                    class="company-status-indicator company-approved-status"
                                    v-else-if="companyIsApproved() && pageAction == 'view'"
                                >
                                    This Company has been approved
                                </span>
                                <span
                                    class="company-status-indicator company-rejected-status"
                                    v-else-if="companyIsRejected() && (pageAction == 'view' || pageAction == 'load')"
                                >
                                    This Company has been rejected
                                </span>

                                <v-btn
                                    v-if="pageAction == 'view' && companyIsApproved()"
                                    class="form-text-button mb-0 rounded-l-0 go-to-live-company-btn"
                                    elevation="0"
                                    icon
                                    small
                                    height="30"
                                    data-tooltip="Go to live company"
                                    @click="goToLiveCompany()"
                                >
                                    <v-icon>business</v-icon>
                                </v-btn>
                            </div>

                            <v-btn
                                v-if="pageAction != 'review' && pageAction != 'load' && pageAction != 'view'"
                                class="form-text-button mb-0 rounded-l-0"
                                color="primary"
                                depressed
                                elevation="0"
                                small
                                @click="checkCompanyCode()"
                                style="margin-top: -1px"
                                >Create New
                            </v-btn>
                            <v-tooltip top>
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        depressed
                                        elevation="0"
                                        small
                                        color="black"
                                        height="30"
                                        class="rounded-a-0"
                                        dark
                                        v-show="pageAction != 'review'"
                                        v-if="userIsTrustAccountant() && isNewCompany()"
                                        @click="cancelNewCompany()"
                                        v-bind="attrs"
                                        v-on="on"
                                        style="margin-bottom: 1px"
                                    >
                                        <v-icon color="red">close</v-icon>
                                    </v-btn>
                                </template>
                                <span>Cancel</span>
                            </v-tooltip>

                            <v-btn
                                v-if="userIsNotTrustAccountant() && pageAction != 'load' && pageAction != 'view'"
                                class="form-text-button mb-0 rounded-l-0 view-details-btn"
                                style="position: relative; top: -1px"
                                text
                                depressed
                                small
                                @click="loadCompanyDetails(true)"
                                :loading="companyDetailsButtonLoading"
                            >
                                <span class="view-details-btn-label">View Details</span>
                            </v-btn>
                        </v-col>
                    </v-row>
                </div>
            </v-col>
            <v-col
                class="pr-2"
                xs="12"
                sm="12"
                md="3"
                lg="3"
                v-if="companyCodeIsNotEmpty() && isExistingCompany() && !isNewCompany() && !companyIsForReview()"
                v-on:dblclick="doubleClickForm()"
            >
                <label
                    v-if="formNotes.length > 0 || editForm"
                    class="font-weight-black"
                    for="main-notes"
                    >Notes:</label
                >
                <textarea
                    id="main-notes"
                    class="noteTextArea font-weight-bold"
                    style="width: 100%"
                    rows="4"
                    v-model="formNotes"
                    v-if="editForm"
                ></textarea>
                <div
                    class="noteTextArea"
                    v-if="!editForm"
                    style="max-height: 115px; overflow: auto"
                >
                    <pre
                        class="pre-content font-weight-bold"
                        v-if="formNotes.length > 0"
                        >{{ formNotes }}</pre
                    >
                    <pre
                        class="pre-content font-weight-bold"
                        style="color: grey"
                        v-if="formNotes.length === 0"
                    >
(double click to add note)</pre
                    >
                </div>
                <v-btn
                    v-if="editForm"
                    class="float-end mt-1"
                    color="success"
                    dense
                    x-small
                    @click="processSaveFormNotes()"
                >
                    Save
                </v-btn>
            </v-col>
        </v-row>
        <br />
        <cirrus-server-error :errorMsg2="errorServerMessage"></cirrus-server-error>

        <v-tabs
            class="cirrus-tab-theme"
            icons-and-text
            v-model="template_tab"
            show-arrows
            v-if="companyCodeIsNotEmpty() || proceedWithNewCompanyCode"
        >
            <v-tabs-slider color="white"></v-tabs-slider>

            <v-tab
                href="#tab-1"
                v-if="!mobileElementDisplay && isNewCompany()"
                class="v-step-form-tab"
            >
                {{ newCompanyTabLabel }}
                <v-icon
                    small
                    dense
                    >business
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-1"
                v-if="!mobileElementDisplay && isExistingCompany()"
                class="v-step-form-tab"
            >
                Company
                <v-icon
                    small
                    dense
                    >business
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-2"
                ref="contactstab"
                v-if="!mobileElementDisplay"
            >
                Contacts and Documents
                <v-icon
                    small
                    dense
                    >import_contacts
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-3"
                v-if="!mobileElementDisplay && pageAction != 'review' && pageAction != 'view' && isOwner"
                v-show="isExistingCompany()"
            >
                Owned Properties
                <v-icon
                    small
                    dense
                    >apartment
                </v-icon>
            </v-tab>

            <v-tab
                href="#tab-1"
                class="primary--text v-step-form-tab"
                v-if="mobileElementDisplay && isNewCompany()"
            >
                <v-icon
                    small
                    dense
                    >business
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-1"
                class="primary--text v-step-form-tab"
                v-if="mobileElementDisplay && isExistingCompany()"
            >
                <v-icon
                    small
                    dense
                    >business
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-2"
                ref="contactstab"
                class="primary--text v-step-form-tab"
                v-if="mobileElementDisplay"
            >
                <v-icon
                    small
                    dense
                    >import_contacts
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-3"
                class="primary--text v-step-form-tab"
                v-if="mobileElementDisplay && pageAction != 'review' && pageAction != 'view' && isOwner"
                v-show="isExistingCompany()"
            >
                <v-icon
                    small
                    dense
                    >apartment
                </v-icon>
            </v-tab>

            <v-col
                class="text-right pt-2"
                v-show="displayShortcutOptions()"
            >
                <v-menu
                    offset-y
                    offset-x
                    v-if="shortcuts.length > 0"
                >
                    <template v-slot:activator="{ on }">
                        <v-btn
                            depressed
                            small
                            color="grey darken-1"
                            dark
                            v-on="on"
                            id="option-button"
                        >
                            <v-icon
                                small
                                left
                                >settings
                            </v-icon>
                            Options
                        </v-btn>
                    </template>
                    <v-list
                        dense
                        subheader
                        class="shortcuts-list"
                        style="left: -120px"
                    >
                        <v-list-item
                            v-for="(shortcuts_data, shortcuts_index) in shortcuts"
                            :key="shortcuts_index"
                            v-if="shortcuts_data.shortcut_code !== 'sms_send' || sms_sending_setup"
                            @click="goToShortcut(shortcuts_data.shortcut_code)"
                        >
                            <v-list-item-avatar>
                                <v-avatar
                                    size="32px"
                                    tile
                                >
                                    <v-icon>{{ shortcuts_data.icon }}</v-icon>
                                </v-avatar>
                            </v-list-item-avatar>
                            <v-list-item-title style="text-align: left">{{ shortcuts_data.title }}</v-list-item-title>
                        </v-list-item>
                    </v-list>
                </v-menu>
            </v-col>

            <v-tab-item
                :value="'tab-1'"
                @dblclick="doubleClickForm()"
            >
                <div
                    class="feedback_section"
                    v-if="pageAction == 'load' || pageAction == 'view'"
                    v-show="companyIsRejected()"
                >
                    <cirrus-content-loader v-if="showLoadingOverlay"></cirrus-content-loader>
                    <div
                        v-if="!showLoadingOverlay"
                        class="page-form"
                    >
                        <v-divider></v-divider>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                                class="feedback-form"
                            >
                                <div class="feedback-label">
                                    <div><strong>Rejected Feedback</strong></div>
                                </div>
                                <div class="feedback-container">
                                    <cirrus-text-area
                                        :rows="'5'"
                                        :cols="'60'"
                                        maxlength="250"
                                        :edit_form="false"
                                        :error_msg="errorMessage"
                                        v-model="feedback"
                                    ></cirrus-text-area>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </div>

                <!-- general details section -->
                <div id="general_details_section">
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">General Details</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                            <v-btn
                                x-small
                                data-tooltip="Edit"
                                v-if="isExistingCompany() && !editForm && !readOnly"
                                v-show="pageAction != 'review' && pageAction != 'view'"
                                class="v-step-edit-button"
                                icon
                                @click="editCompanyForm()"
                            >
                                <v-icon>edit</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Undo Changes"
                                v-if="editForm && isExistingCompany()"
                                v-show="pageAction != 'review' && pageAction != 'view'"
                                class="v-step-revert-button"
                                icon
                                @click="resetCompanyForm()"
                            >
                                <v-icon color="red">undo</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Update Details"
                                v-if="editForm && isExistingCompany()"
                                v-show="pageAction != 'review' && pageAction != 'view'"
                                class="v-step-save-button"
                                icon
                                @click="saveCompany()"
                                :disabled="disableSaveCompanyButton"
                                :id="save - company - icon"
                            >
                                <v-icon
                                    light
                                    color="green"
                                    >check
                                </v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                icon
                                v-if="isExistingCompany()"
                                v-show="pageAction != 'review' && pageAction != 'view'"
                                @click="openCompanyChangelogModal()"
                                data-tooltip="View Changelog"
                            >
                                <v-icon>history</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Refresh"
                                v-if="isExistingCompany() || pageAction == 'review'"
                                class="v-step-refresh-button"
                                icon
                                @click="loadCompanyDetails()"
                            >
                                <v-icon>refresh</v-icon>
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="showLoadingOverlay"></cirrus-content-loader>
                    <div
                        v-if="!showLoadingOverlay"
                        class="page-form"
                    >
                        <v-row
                            class="form-row no-gutters"
                            v-if="isExistingCompany() || pageAction == 'view' || pageAction == 'review'"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                                style="min-height: 31px"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Status</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td>
                                            <strong>Created</strong> <span>{{ create_date }}</span
                                            ><span v-if="update_date && update_date != ''">,</span>
                                            <strong v-if="update_date && update_date != ''">Last updated</strong>
                                            <span v-if="update_date && update_date != ''">{{ update_date }}</span>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Company Type</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ displayCompanyType() }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <sui-checkbox
                                                :label="'Debtor'"
                                                v-model="isDebtor"
                                            />
                                            <sui-checkbox
                                                :label="'Owner'"
                                                style="padding-left: 10px"
                                                v-model="isOwner"
                                            />
                                            <sui-checkbox
                                                :label="'Supplier'"
                                                style="padding-left: 10px"
                                                v-model="isSupplier"
                                            />
                                            <sui-checkbox
                                                v-if="isSupplier"
                                                v-show="pageAction != 'load'"
                                                :label="'Is an Agent?'"
                                                style="padding-left: 10px"
                                                v-model="isSupplierAgent"
                                            />
                                            <sui-checkbox
                                                v-if="displayCirrusFM()"
                                                :label="'Enable on CirrusFM?'"
                                                style="padding-left: 10px"
                                                v-model="cirrusfm"
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row no-gutters"
                            v-if="isSupplier"
                        >
                            <!--//Display this if company type: Supplier is checked//-->
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Supplier Type</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ supplierTypeName() }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <cirrus-single-select
                                                v-model="supplierType"
                                                :options="supplierTypeDropDownListItems"
                                                ref="refCompanySupplierType"
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Company Name</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ company_name }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <v-text-field
                                                :maxlength="240"
                                                dense
                                                v-model="company_name"
                                            />
                                            <div
                                                class="abn-detail-container"
                                                v-if="countryDefaults.country_code == 'AU'"
                                            >
                                                <v-btn
                                                    text
                                                    depressed
                                                    small
                                                    class="company-lookup-btn"
                                                    @click="lookupCompany($event)"
                                                    :loading="companyLookupButtonLoading"
                                                >
                                                    {{ companyLookupButtonLabel }}
                                                </v-btn>
                                                <span
                                                    class="loading-info-text"
                                                    v-if="companyLookupButtonLoading"
                                                    >Looking up Company Name...</span
                                                >

                                                <v-btn
                                                    depressed
                                                    x-small
                                                    class="copy-data-btn"
                                                    v-if="abnDetailDisplay && abnCompanyName != ''"
                                                    data-field-label="company-name"
                                                    :data-field-value="abnCompanyName"
                                                    color="normal"
                                                    @click="copyABNValue($event)"
                                                >
                                                    <v-icon class="copy-btn-content">arrow_left</v-icon>
                                                </v-btn>
                                                <span
                                                    v-if="abnDetailDisplay && abnCompanyName != ''"
                                                    class="abn-details"
                                                >
                                                    {{ abnCompanyName }}
                                                </span>
                                            </div>

                                            <div class="manage-company-alert">
                                                <v-chip
                                                    v-if="errorMessage.length > 0 && errorData.id === 'company_name'"
                                                    v-for="(errorData, index) in errorMessage"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row no-gutters"
                            v-if="companyMatchListDisplay"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Matching Names</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td>
                                            <cirrus-single-select
                                                v-if="companyMatchListCount > 0"
                                                v-model="companyMatchSelectedGst"
                                                :options="companyMatchDropDownListItems"
                                            />
                                            <div
                                                v-else
                                                class="abn-message"
                                            >
                                                {{ companyMatchListMessage }}
                                            </div>
                                            <div class="abn-detail-container">
                                                <v-btn
                                                    text
                                                    depressed
                                                    small
                                                    class="hide-matching-list-btn"
                                                    @click="hideSearchResults()"
                                                    :loading="abnDetailsLoading"
                                                >
                                                    {{ companyLookupHideButtonLabel }}
                                                </v-btn>
                                                <span
                                                    class="loading-info-text"
                                                    v-if="abnDetailsLoading"
                                                    >Loading...</span
                                                >
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Company Group</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ companyGroupName() }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <cirrus-single-select
                                                v-model="companyGroup"
                                                :options="companyGroupDropDownListItems"
                                                ref="refCompanyGroup"
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>{{ countryDefaults.business_label }}</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            <span v-if="company_gst_no !== ''">{{
                                                countryDefaults.business_prefix
                                            }}</span
                                            >{{ company_gst_no }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <span
                                                v-if="countryDefaults.business_prefix"
                                                class="business-prefix-vue"
                                                >{{ countryDefaults.business_prefix }}</span
                                            >
                                            <v-text-field
                                                :maxlength="countryDefaults.business_length"
                                                dense
                                                v-model="company_gst_no"
                                                @change="lookupABN($event)"
                                            />
                                            <div
                                                class="abn-detail-container"
                                                v-if="countryDefaults.country_code == 'AU'"
                                            >
                                                <v-btn
                                                    text
                                                    depressed
                                                    small
                                                    class="abn-lookup-btn"
                                                    @click="lookupABN($event)"
                                                    :loading="abnLookupButtonLoading"
                                                >
                                                    {{ abnLookupButtonLabel }}
                                                </v-btn>
                                                <span
                                                    class="loading-info-text"
                                                    v-if="abnLookupButtonLoading"
                                                    >Looking up ABN...</span
                                                >
                                            </div>
                                            <v-chip
                                                v-if="errorMessage.length > 0 && errorData.id === 'company_abn'"
                                                v-for="(errorData, index) in errorMessage"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row no-gutters"
                            v-if="abnDetails || abnMessage != ''"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong v-if="abnDetails"
                                                >ABN Details
                                                <v-icon
                                                    style="font-size: 21px; position: relative; top: -1px"
                                                    title="Values retrieved from ABN and Company Name Lookup"
                                                    >info
                                                </v-icon>
                                            </strong>
                                        </td>
                                        <td class="required"></td>
                                        <td>
                                            <table
                                                v-if="abnDetails"
                                                class="abn-result-table"
                                            >
                                                <thead v-if="abnMessage != ''">
                                                    <tr>
                                                        <th
                                                            colspan="2"
                                                            style="border-bottom: 1px solid #ececec !important"
                                                            class="abn-message"
                                                        >
                                                            {{ abnMessage }}
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>Company Name</td>
                                                        <td>{{ abnCompanyName }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>ABN</td>
                                                        <td>
                                                            {{ abnValue }}
                                                            <span v-if="abnStatus == 'Cancelled'"
                                                                >({{ abnStatus }})</span
                                                            >
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>ACN</td>
                                                        <td>{{ abn_acn }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Company Type</td>
                                                        <td>{{ abnCompanyType }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>{{ taxLabel }} Status</td>
                                                        <td>{{ abnGstStatus }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>State Code</td>
                                                        <td>{{ abnStateCode }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Post Code</td>
                                                        <td>{{ abnPostCode }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <div
                                                v-else
                                                class="abn-message"
                                            >
                                                {{ abnMessage }}
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <!-- end of general details section -->

                <div id="company_details_section">
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Company Details</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="showLoadingOverlay"></cirrus-content-loader>
                    <div
                        v-if="!showLoadingOverlay"
                        class="page-form"
                    >
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Country</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ company_country_display }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <cirrus-single-select
                                                v-model="company_country"
                                                :options="dd_country_list"
                                                ref="refCompanyCountry"
                                            />

                                            <div class="manage-company-alert">
                                                <v-chip
                                                    v-if="errorMessage.length > 0 && errorData.id === 'company_country'"
                                                    v-for="(errorData, index) in errorMessage"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>

                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Address</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ company_street }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <cirrus-text-area
                                                :rows="'2'"
                                                :cols="'60'"
                                                :edit_form="true"
                                                :error_msg="errorMessage"
                                                v-model="company_street"
                                                :maxlength="74"
                                            ></cirrus-text-area>

                                            <div class="manage-company-alert">
                                                <v-chip
                                                    v-if="errorMessage.length > 0 && errorData.id === 'company_street'"
                                                    v-for="(errorData, index) in errorMessage"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>{{ suburbLabel }}</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ company_suburb }}
                                        </td>
                                        <td v-if="(editForm || isNewCompany()) && company_country == 'AU'">
                                            <v-combobox
                                                v-model="company_suburb"
                                                v-if="company_state != ''"
                                                :maxlength="40"
                                                :items="suburbFilteredListItems"
                                                item-value="label"
                                                item-text="label"
                                                @change="suburbSelected(company_suburb)"
                                                auto-select-first
                                                hide-selected
                                                persistent-hint
                                                append-icon
                                                :search-input.sync="searchSuburb"
                                                :hide-no-data="!searchSuburb"
                                                dense
                                                ref="refSuburb"
                                                flat
                                            >
                                                <template v-slot:no-data>
                                                    <v-list-item>
                                                        <v-chip
                                                            v-model="searchSuburb"
                                                            small
                                                        >
                                                            {{ searchSuburb }}
                                                        </v-chip>
                                                    </v-list-item>
                                                </template>
                                            </v-combobox>
                                            <v-text-field
                                                :maxlength="40"
                                                dense
                                                v-model="company_suburb"
                                                v-else
                                            />

                                            <div class="manage-company-alert">
                                                <v-chip
                                                    v-if="errorMessage.length > 0 && errorData.id === 'company_city'"
                                                    v-for="(errorData, index) in errorMessage"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>
                                        </td>
                                        <td v-if="(editForm || isNewCompany()) && company_country != 'AU'">
                                            <v-text-field
                                                :maxlength="40"
                                                dense
                                                v-model="company_suburb"
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            class="form-row no-gutters"
                            v-if="countryDefaultsForAddress.display_state"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>State:</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ company_state_display }}
                                            <input
                                                v-if="pageAction == 'view'"
                                                type="hidden"
                                                ref="refCompanyState"
                                            />
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <cirrus-single-select
                                                v-model="company_state"
                                                :options="stateList"
                                                ref="refCompanyState"
                                                @input="suburbFilteredList(company_state)"
                                            />

                                            <div class="manage-company-alert">
                                                <v-chip
                                                    v-if="errorMessage.length > 0 && errorData.id === 'company_state'"
                                                    v-for="(errorData, index) in errorMessage"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>

                                            <div class="abn-detail-container">
                                                <v-btn
                                                    depressed
                                                    x-small
                                                    class="copy-data-btn"
                                                    v-if="abnDetailDisplay && abnStateCode != ''"
                                                    data-field-label="company-state"
                                                    :data-field-value="abnStateCode"
                                                    color="normal"
                                                    @click="copyABNValue($event)"
                                                >
                                                    <v-icon class="copy-btn-content">arrow_left</v-icon>
                                                </v-btn>
                                                <span
                                                    v-if="abnDetailDisplay && abnStateCode != ''"
                                                    class="abn-details"
                                                >
                                                    {{ abnStateCode }}
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Post Code</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ company_post_code }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <v-text-field
                                                :maxlength="10"
                                                dense
                                                v-model="company_post_code"
                                            />

                                            <div class="manage-company-alert">
                                                <v-chip
                                                    v-if="
                                                        errorMessage.length > 0 && errorData.id === 'company_post_code'
                                                    "
                                                    v-for="(errorData, index) in errorMessage"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>

                                            <div class="abn-detail-container">
                                                <v-btn
                                                    depressed
                                                    x-small
                                                    class="copy-data-btn"
                                                    v-if="abnDetailDisplay && abnPostCode != ''"
                                                    data-field-label="company-post-code"
                                                    :data-field-value="abnPostCode"
                                                    color="normal"
                                                    @click="copyABNValue($event)"
                                                >
                                                    <v-icon class="copy-btn-content">arrow_left</v-icon>
                                                </v-btn>
                                                <span
                                                    v-if="abnDetailDisplay && abnPostCode != ''"
                                                    class="abn-details"
                                                >
                                                    {{ abnPostCode }}
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Email Address</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            <span>
                                                <span v-if="company_email.includes(';')">
                                                    <v-item-list
                                                        v-for="(data, index) in company_email.split(';')"
                                                        :key="index"
                                                    >
                                                        <a :href="'mailto:' + data.trim()">{{ data }}</a
                                                        ><span v-if="company_email.split(';').length - 1 != index"
                                                            >;
                                                        </span>
                                                    </v-item-list>
                                                </span>
                                                <span v-else
                                                    ><a :href="'mailto:' + company_email.trim()">{{
                                                        company_email
                                                    }}</a></span
                                                >
                                            </span>
                                        </td>
                                        <td v-else>
                                            <v-text-field
                                                v-model="company_email"
                                                dense
                                            />
                                            <cirrus-email-centralisation
                                                v-model="emailCentralisationSetting"
                                                :contact_table_name="emailCentralisationReferenceTable()"
                                                :contact_table_id="companyId"
                                                v-if="emailCentralisationIconDisplay()"
                                            ></cirrus-email-centralisation>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </div>
                </div>

                <div id="banking_details_section">
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Banking Details</h6>
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="showLoadingOverlay"></cirrus-content-loader>
                    <div
                        v-if="!showLoadingOverlay"
                        class="page-form"
                    >
                        <v-row
                            v-if="pageAction != 'view'"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Preferred Payment Method</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="isExistingCompany()">
                                            {{ paymentMethodName() }}
                                        </td>
                                        <td v-else>
                                            <div v-if="!defaultNewCompanyAsCheque">
                                                <v-btn-toggle
                                                    class="v-step-search-type form-toggle"
                                                    mandatory
                                                    v-model="company_payment_method"
                                                >
                                                    <v-btn
                                                        small
                                                        tile
                                                        text
                                                        :key="'PAY_EFT'"
                                                        v-bind:id="1"
                                                        v-text="'Direct Deposit (EFT)'"
                                                        class="no-text-transform"
                                                    ></v-btn>
                                                    <v-btn
                                                        v-bind:class="{
                                                            'display-none hidden': countryDefaults.country_code != 'AU',
                                                        }"
                                                        small
                                                        tile
                                                        text
                                                        :key="'PAY_BPAY'"
                                                        v-bind:id="2"
                                                        v-text="'BPAY'"
                                                        class="no-text-transform"
                                                    ></v-btn>
                                                    <v-btn
                                                        small
                                                        tile
                                                        text
                                                        :key="'PAY_CHQ'"
                                                        v-bind:id="3"
                                                        v-text="'Cheque'"
                                                        class="no-text-transform"
                                                    ></v-btn>
                                                </v-btn-toggle>
                                            </div>
                                            <div v-else>
                                                {{ paymentMethodName() }}
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="pageAction == 'view'"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Direct Banking</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td>
                                            {{ directBanking() }}
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="paymentIsEFT() && isDebtor == true"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Direct Debit</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="isExistingCompany()">
                                            {{ directDebit() }}
                                        </td>
                                        <td v-else>
                                            <v-btn-toggle
                                                class="v-step-search-type form-toggle"
                                                mandatory
                                                v-model="company_direct_debit"
                                            >
                                                <v-btn
                                                    small
                                                    tile
                                                    text
                                                    :key="1"
                                                    v-bind:id="1"
                                                    v-text="'Yes'"
                                                    class="no-text-transform"
                                                ></v-btn>
                                                <v-btn
                                                    small
                                                    tile
                                                    text
                                                    :key="0"
                                                    v-bind:id="0"
                                                    v-text="'No'"
                                                    class="no-text-transform"
                                                ></v-btn>
                                            </v-btn-toggle>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="paymentIsEFT() && countryDefaults.display_bsb"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>{{ bsbLabel }}</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="isExistingCompany()">
                                            {{ formattedBsb }}
                                        </td>
                                        <td v-else>
                                            <v-text-field
                                                v-model="company_bsb"
                                                :maxlength="bsbLength"
                                                dense
                                            />
                                            <v-chip
                                                v-if="errorMessage.length > 0 && errorData.id === 'company_bsb'"
                                                v-for="(errorData, index) in errorMessage"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="paymentIsEFT()"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Bank Account Number</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="isExistingCompany()">{{ company_acct_no }}</td>
                                        <td v-else>
                                            <v-text-field
                                                v-model="company_acct_no"
                                                :maxlength="countryDefaults.bank_account_length"
                                                dense
                                            />
                                            <v-chip
                                                v-if="errorMessage.length > 0 && errorData.id === 'company_acct_no'"
                                                v-for="(errorData, index) in errorMessage"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="paymentIsEFT()"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Account Name</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="isExistingCompany()">{{ company_acc_name }}</td>
                                        <td v-else>
                                            <v-text-field
                                                v-model="company_acc_name"
                                                maxlength="40"
                                                dense
                                            />
                                            <v-chip
                                                v-if="errorMessage.length > 0 && errorData.id === 'company_acc_name'"
                                                v-for="(errorData, index) in errorMessage"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="paymentIsEFT()"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Bank Name</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="isExistingCompany()">{{ company_bank_name }}</td>
                                        <td v-else>
                                            <v-text-field
                                                v-model="company_bank_name"
                                                maxlength="40"
                                                dense
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>

                        <!--//For BPAY//-->
                        <v-row
                            v-if="paymentIsBPAY()"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>BPAY Biller Code</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td v-if="isExistingCompany()">{{ company_biller_code }}</td>
                                        <td v-else>
                                            <v-text-field
                                                v-model="company_biller_code"
                                                maxlength="10"
                                                dense
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>

                        <!--//For Cheque//-->
                        <v-row
                            v-if="paymentIsCheque()"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Cheque Clearance</strong>
                                        </td>
                                        <td class="required"><span v-show="pageAction != 'view'">*</span></td>
                                        <td>{{ company_cheque_days }}</td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </div>
                </div>

                <div id="account_details_section">
                    <v-card
                        v-if="pageAction != 'load'"
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Account Details</h6>
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <v-card
                        v-if="pageAction == 'load' && (!isDebtor || (isDebtor && (isOwner || isSupplier)))"
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Account Details</h6>
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="showLoadingOverlay"></cirrus-content-loader>
                    <div
                        v-if="!showLoadingOverlay"
                        class="page-form"
                    >
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Tax Status</strong>
                                        </td>
                                        <td class="required">
                                            <span
                                                v-if="isOwner || isSupplier || (!isDebtor && !isOwner && !isSupplier)"
                                                v-show="pageAction != 'view'"
                                                >*</span
                                            >
                                        </td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ taxStatus() }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <cirrus-single-select
                                                v-model="company_gst_code"
                                                :options="taxStatusDropDownListItems"
                                                ref="refCompanyTaxStatus"
                                            />

                                            <div class="manage-company-alert">
                                                <v-chip
                                                    v-if="
                                                        errorMessage.length > 0 && errorData.id === 'company_tax_status'
                                                    "
                                                    v-for="(errorData, index) in errorMessage"
                                                    :key="index"
                                                    outlined
                                                    color="error"
                                                >
                                                    <v-icon left>error</v-icon>
                                                    {{ errorData.message }}
                                                </v-chip>
                                            </div>

                                            <div class="abn-detail-container">
                                                <v-btn
                                                    depressed
                                                    x-small
                                                    class="copy-data-btn"
                                                    v-if="abnDetailDisplay && abnGstStatus !== ''"
                                                    data-field-label="company-gst-status"
                                                    :data-field-value="abnGstCode"
                                                    color="normal"
                                                    @click="copyABNValue($event)"
                                                >
                                                    <v-icon class="copy-btn-content">arrow_left</v-icon>
                                                </v-btn>
                                                <span
                                                    v-if="abnDetailDisplay && abnGstStatus !== ''"
                                                    class="abn-details"
                                                >
                                                    {{ abnGstStatus }}
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr v-if="pageAction != 'load'">
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Company is Active?</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ companyStatus() }}
                                        </td>
                                        <td v-else>
                                            <v-btn-toggle
                                                class="v-step-search-type form-toggle"
                                                mandatory
                                                v-model="company_status"
                                            >
                                                <v-btn
                                                    small
                                                    tile
                                                    text
                                                    :key="1"
                                                    v-bind:id="1"
                                                    v-text="'Yes'"
                                                    class="no-text-transform"
                                                ></v-btn>
                                                <v-btn
                                                    small
                                                    tile
                                                    text
                                                    :key="0"
                                                    v-bind:id="0"
                                                    v-text="'No'"
                                                    class="no-text-transform"
                                                ></v-btn>
                                            </v-btn-toggle>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="isSupplier && isSupplierAgent"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Invoice Address</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ company_invoice_add }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <cirrus-text-area
                                                :rows="'2'"
                                                :cols="'60'"
                                                :edit_form="true"
                                                :error_msg="errorMessage"
                                                v-model="company_invoice_add"
                                            ></cirrus-text-area>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="isSupplier && isSupplierAgent"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Don't Show From/To Dates on Invoice</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ invoiceDatesStatus() }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <sui-checkbox
                                                v-model="disable_invoice_dates"
                                                :label="''"
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="isSupplier && isSupplierAgent"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Don't show EFT Details on Agent Invoice</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ eftDetailStatus() }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <sui-checkbox
                                                v-model="disable_eft_details"
                                                :label="''"
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row
                            v-if="isSupplier"
                            class="form-row no-gutters"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table
                                    v-if="pageAction != 'load'"
                                    class="data-grid data-grid-dense data-grid-no-line tableHive"
                                >
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Require valid CirrusFM work order for all invoices</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ orderNumberStatus() }}
                                        </td>
                                        <td v-if="editForm || isNewCompany()">
                                            <sui-checkbox
                                                v-model="require_order_no"
                                                :label="''"
                                            />
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr v-if="pageAction != 'load'">
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Send Remittance</strong>
                                        </td>
                                        <td class="required"></td>
                                        <td v-if="!editForm && isExistingCompany()">
                                            {{ sendRemittance() }}
                                        </td>
                                        <td v-else>
                                            <v-btn-toggle
                                                class="v-step-search-type form-toggle"
                                                mandatory
                                                v-model="send_remittance"
                                            >
                                                <v-btn
                                                    small
                                                    tile
                                                    text
                                                    :key="1"
                                                    v-bind:id="1"
                                                    v-text="'Yes'"
                                                    class="no-text-transform"
                                                ></v-btn>
                                                <v-btn
                                                    small
                                                    tile
                                                    text
                                                    :key="0"
                                                    v-bind:id="0"
                                                    v-text="'No'"
                                                    class="no-text-transform"
                                                ></v-btn>
                                            </v-btn-toggle>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </div>
                </div>

                <v-row
                    class="form-row no-gutters"
                    v-show="pageAction != 'review' && pageAction != 'view'"
                    v-if="isNewCompany() || editForm"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <v-divider></v-divider>
                        <br />
                        <v-btn
                            :class="'save-company-btn v-step-save-1-button'"
                            @click="saveCompany()"
                            color="success"
                            dark
                            absolute
                            right
                            small
                            :disabled="disableSaveCompanyButton"
                        >
                            {{ saveButtonLabel }}
                        </v-btn>
                        <br /><br /><br />
                    </v-col>
                </v-row>

                <div
                    class="feedback_section"
                    v-if="pageAction == 'review'"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Your Feedback</h6>
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="showLoadingOverlay"></cirrus-content-loader>
                    <div
                        v-if="!showLoadingOverlay"
                        class="page-form"
                    >
                        <v-divider></v-divider>
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                                class="feedback-form"
                            >
                                <div class="feedback-label">
                                    <div>
                                        This response is sent to the client when either approving or rejecting a form.
                                    </div>
                                </div>

                                <cirrus-text-area
                                    :rows="'5'"
                                    :cols="'60'"
                                    :edit_form="true"
                                    :error_msg="errorMessage"
                                    maxlength="250"
                                    v-model="feedback"
                                ></cirrus-text-area>
                            </v-col>
                        </v-row>
                    </div>

                    <v-card
                        elevation="0"
                        class="feedback-action"
                    >
                        <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn
                                color="error"
                                dark
                                small
                                depressed
                                @click="rejectCompany()"
                                :disabled="disableApprovalButtons"
                                :class="'reject-company-btn'"
                            >
                                Reject
                            </v-btn>
                            <v-btn
                                color="success"
                                dark
                                small
                                depressed
                                @click="approveCompany()"
                                :disabled="disableApprovalButtons"
                                :class="'approve-company-btn'"
                            >
                                Approve
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </div>
            </v-tab-item>

            <v-tab-item :value="'tab-2'">
                <contacts-component
                    id="contacts_section"
                    :reload_components="reloadComponents"
                    :company_code="companyCodeForFetching()"
                    :is_debtor="isDebtor"
                    :is_owner="isOwner"
                    :is_supplier="isSupplier"
                    :is_temp="saveToTemp()"
                    :action_parameter="pageAction"
                ></contacts-component>
                <br />
                <documents-v2-component
                    v-if="docComponentIsVersion2()"
                    :company_code="companyCodeForFetching()"
                    id="documents_section"
                    :reloadComponents="reloadComponents"
                    :is_temp="saveToTemp()"
                ></documents-v2-component>
                <documents-component
                    v-if="docComponentIsVersion1()"
                    :company_code="companyCodeForFetching()"
                    id="documents_section"
                    :reloadComponents="reloadComponents"
                    :is_temp="saveToTemp()"
                    :action_parameter="pageAction"
                ></documents-component>
            </v-tab-item>

            <v-tab-item :value="'tab-3'">
                <properties-component
                    v-if="isExistingCompany()"
                    :company_code="companyCode"
                    :user="user_type"
                    id="contacts_section"
                    :reloadComponents="reloadComponents"
                ></properties-component>
            </v-tab-item>
        </v-tabs>

        <!-- COMPANY CHANGELOG -->
        <v-dialog
            top
            v-model="showChangeLogModal"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Company Changelog</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="showChangeLogModal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <company-update-log-component
                            v-if="showChangeLogModal"
                            :company_code="companyCode"
                            :action_parameter="pageAction"
                            :supplier_types="JSON.stringify(supplierTypeDropDownListItems)"
                            :company_group="JSON.stringify(companyGroupDropDownListItems)"
                        ></company-update-log-component>
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF COMPANY CHANGELOG -->
        <sms-sending-modal-component key="shortcuts"></sms-sending-modal-component>
    </div>
</template>

<script>
import Vue from 'vue';
import SuiVue from 'semantic-ui-vue';
import Multiselect from 'vue-multiselect';
import vSelect from 'vue-select';
import global_mixins from '../../../../plugins/mixins';
import { mapActions, mapMutations, mapState } from 'vuex';
import suburbList from '../../../../plugins/australianSuburb.json';
import axios from 'axios';
import { bus } from '../../../../plugins/bus';
import isEmpty from 'lodash/isEmpty';
import startCase from 'lodash/startCase';
import isEqual from 'lodash/isEqual';
import { USER_TYPES } from '../../../../constants';
import {
    ACTIVE_COMPANY,
    APPROVAL_STATUS,
    DATA_STATUS,
    DEFAULT_CHEQUE_DAYS,
    DIRECT_BANKING_ENABLED,
    DIRECT_DEBIT_ENABLED,
    FORM_MODE,
    PAYMENT_METHODS,
    SEND_REMITTANCE_ENABLED,
} from '../company_summary_constants';
import { COMPONENT_VERSION_1, COMPONENT_VERSION_2 } from '../documents_component_constants';
import {
    collectAddressBookSettings,
    formatValidationResponseData,
    formatWithDelimiter,
} from '../../../../utils/sharedFunctions';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

Vue.use(SuiVue);
Vue.component('v-select', vSelect);
Vue.component('multiselect', Multiselect);

Vue.component('contacts-component', require('../forms/ContactsForm.vue').default);
Vue.component('documents-component', require('../forms/DocumentsForm.vue').default);
Vue.component(
    'documents-v2-component',
    require('../../../../DocumentDirectory/sections/CompanyDocumentFormV2.vue').default,
);
Vue.component('properties-component', require('../forms/PropertiesForm.vue').default);
Vue.component('company-update-log-component', require('../forms/CompanyUpdateLog.vue').default);
Vue.component('sms-sending-modal-component', require('../../Generic/Forms/SMSSendingModal.vue').default);

export default {
    props: {
        companyCodeParameter: String,
        pageActionParameter: String,
        returnPageParameter: String,
        batchId: String,
        paymentType: String,
        viewDate: String,
        date: String,
        viewDisplayOption: String,
        propertyId: String,
        leaseId: String,
        clientCountry: String,
        initialCountryDefaultSettings: String,
    },
    data() {
        return {
            trustAccountLabel: 'Trust Account',
            suburbLabel: 'Suburb',
            taxLabel: 'GST',
            bsbLabel: 'BSB',
            bsbLength: 6,
            bsbDelimiter: '-',
            bsbFrequency: 3,
            manageCompanyTitle: 'Company Summary',
            saveButtonLabel: 'Update Details',
            newCompanyTabLabel: 'New Company',
            pageTitleList: [
                'Company Details for Approval',
                'Approved Company Details',
                'Company Summary',
                'Rejected Company Details',
            ],
            paymentMethodList: ['Direct Deposit (EFT)', 'BPAY', 'Cheque'],
            supplierTypeDropDownListItems: [],
            taxStatusDropDownListItems: [],
            companyMatchDropDownListItems: [],
            companyGroupDropDownListItems: [],
            shortcuts: [],
            errorMessage: [],
            errorServerMessage: [],
            companyCodeExists: false,

            userSubType: localStorage.getItem('user_sub_type'),
            formMode: 0,
            readOnly: false,
            showTopLoadingBar: false,
            showLoadingOverlay: false,
            companyListLoadingAnimation: true,
            proceedWithNewCompanyCode: false,
            reloadComponents: 0,
            mobileElementDisplay: true,
            windowSize: {
                x: 0,
                y: 0,
            },
            editForm: false,
            showChangeLogModal: false,
            companyDetailsButtonLoading: false,
            disableApprovalButtons: false,
            disableSaveCompanyButton: false,
            defaultNewCompanyAsCheque: true,

            companyDataTempBackup: {},
            stateList: [],
            newCompanyCode: '',
            cancelledCompanyCode: '',
            suburbFilteredListItems: [],
            searchSuburb: '',

            originalCompanyCode: '',
            selectedCompanyCode: [],
            pageAction: '',
            companyPayMethod: 0,
            formNotes: '',

            companyLookupButtonLoading: false,
            companyLookupButtonLabel: 'Look up Company Name',
            companyLookupHideButtonLabel: 'Hide Search Results',
            companyMatchListDisplay: false,
            companyMatchListCount: 0,
            companyMatchListMessage: '',
            companyMatchSelectedGst: '',

            abnDetails: false,
            abnLookupButtonLoading: false,
            abnLookupButtonLabel: 'Look up ABN',
            abnMessage: '',
            abnCompanyName: '',
            abnValue: '',
            abnCompanyType: '',
            abnGstCode: '',
            abnGstStatus: '',
            abnStateCode: '',
            abnPostCode: '',
            abnStatus: '',
            abnDetailDisplay: true,
            abnDetailsLoading: false,

            returnToEft: false,
            returnToLease: false,
            returnLink: '',
            returnToEftData: {
                batchId: '',
                paymentType: '',
                viewDate: '',
                date: '',
                viewDisplayOption: '',
            },

            //Company Data from database
            companyId: '',
            companyCode: '',
            create_date: '',
            update_date: '',
            isDebtor: false,
            isOwner: false,
            isSupplier: false,
            isSupplierAgent: false,
            cirrusfm: false,
            supplierType: '',
            companyGroup: '',
            company_status: 0,
            company_name: '',
            company_suburb: '',
            company_country: '',
            company_country_display: '',
            company_state: '',
            company_state_display: '',
            company_post_code: '',
            company_email: '',
            company_street: '',
            company_direct_banking: '',
            company_gst_no: '',
            company_gst_code: '',
            company_payment_method: 0,
            company_direct_debit: '',
            company_bsb: '',
            company_acct_no: '',
            company_acc_name: '',
            company_bank_name: '',
            company_biller_code: '',
            company_cheque_days: 4,
            require_order_no: false,
            payment_acct_code: '',
            use_owner_chart: '',
            income_acct_code: '',
            emailCentralisationSetting: [],
            email_centralisation_setup_config: 0,
            companyFormDataStatus: 1,
            company_invoice_add: '',
            disable_invoice_dates: false,
            disable_eft_details: false,
            send_remittance: 0,
            company_approval_status: 0,
            feedback: '',
            company_approver: '',
            company_approval_date: '',

            countryDefaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                bank_account_length: '9',
            },

            countryDefaultsForAddress: {
                display_state: true,
                country_code: 'AU',
            },

            emailCentralisationDataForSaving: [],
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

        this.fetchFormVersionControl();
        this.fetchCompanyList();
        this.fetchParamSMSSendingSetup();
        this.fetchCountryList();
        this.fetchSupplierTypeList();
        this.fetchCompanyGroupList();
        this.fetchTaxStatusList();
        this.getActionParameter();
        this.setFormFieldsDisplay();
        this.checkIfReadOnly();

        let countrySettings = JSON.parse(decodeURIComponent(this.initialCountryDefaultSettings));
        this.taxLabel = countrySettings.tax_label;
        this.trustAccountLabel = startCase(countrySettings.trust_account);
        this.suburbLabel = startCase(countrySettings.suburb);
        this.bsbLabel = countrySettings.bsb_label;
        this.bsbLength = countrySettings.bsb_length;
        this.bsbDelimiter = countrySettings.bsb_format.delimiter;
        this.bsbFrequency = countrySettings.bsb_format.delimiter_frequency;
    },
    computed: {
        ...mapState([
            'user_type',
            'cirrus8_api_url',
            'dd_default_value',
            'dd_company_list',
            'dd_country_list',
            'sms_sending_setup',
            'sys_ver_control_list',
            'doc_active_version',
        ]),
        formattedBsb() {
            return formatWithDelimiter(this.company_bsb, this.bsbDelimiter, this.bsbFrequency);
        },
    },
    methods: {
        ...mapActions(['fetchCountryList', 'fetchCompanyList', 'fetchParamSMSSendingSetup', 'fetchFormVersionControl']),
        ...mapMutations(['SET_COMPANY_CODE', 'SET_PUSH_DD_COMPANY_LIST', 'SET_DD_COMPANY_LIST', 'SET_DD_COUNTRY_LIST']),
        formatWithDelimiter,

        onResize() {
            this.windowSize = { x: window.innerWidth, y: window.innerHeight };
        },

        isNewCompany() {
            return this.formMode === FORM_MODE.NEW;
        },

        isExistingCompany() {
            return this.formMode === FORM_MODE.EXISTING;
        },

        userIsTrustAccountant() {
            return this.user_type === USER_TYPES.TRUST_ACCOUNTANT;
        },

        userIsNotTrustAccountant() {
            return this.user_type !== USER_TYPES.TRUST_ACCOUNTANT;
        },

        userSubTypeIsPMReadOnly() {
            return this.userSubType === USER_TYPES.PM_READ_ONLY;
        },

        companyIsPending() {
            return this.company_approval_status === APPROVAL_STATUS.PENDING;
        },

        companyIsApproved() {
            return this.company_approval_status === APPROVAL_STATUS.APPROVED;
        },

        companyIsRejected() {
            return this.company_approval_status === APPROVAL_STATUS.REJECTED;
        },

        paymentIsEFT() {
            return this.companyPayMethod === PAYMENT_METHODS.EFT;
        },

        paymentIsBPAY() {
            return this.companyPayMethod === PAYMENT_METHODS.BPAY;
        },

        paymentIsCheque() {
            return this.companyPayMethod === PAYMENT_METHODS.CHEQUE;
        },

        docComponentIsVersion1() {
            return this.doc_active_version === COMPONENT_VERSION_1;
        },

        docComponentIsVersion2() {
            return this.doc_active_version === COMPONENT_VERSION_2;
        },

        companyCodeIsNotEmpty() {
            return !isEmpty(this.companyCode.trim());
        },

        companyIsForReview() {
            return this.pageAction === 'view' || this.pageAction === 'review';
        },

        saveToTemp() {
            return (
                (this.isNewCompany() && this.userIsNotTrustAccountant()) ||
                this.pageAction === 'review' ||
                this.pageAction === 'view'
            );
        },

        emailCentralisationReferenceTable() {
            if (
                (this.isNewCompany() && this.userIsNotTrustAccountant()) ||
                this.pageAction === 'review' ||
                this.pageAction === 'view'
            ) {
                return 'temp_company';
            }

            return 'company';
        },

        companyCodeForFetching() {
            if (this.pageAction === 'review') {
                return this.originalCompanyCode;
            }

            if (this.pageAction !== 'review' && this.isExistingCompany()) {
                return this.companyCode;
            } else {
                return this.newCompanyCode;
            }
        },

        displayShortcutOptions() {
            return (
                this.companyCodeIsNotEmpty() &&
                this.isExistingCompany() &&
                this.pageAction !== 'view' &&
                this.pageAction !== 'review'
            );
        },

        toggleBoolValue(value) {
            return (value ^= 1);
        },

        incrementValueForSaving(value) {
            return (value += 1);
        },

        goToShortcut: function (parameter) {
            switch (parameter) {
                case 'supp_recon':
                    window.open(
                        '?module=accountingReports&command=supplierReconciliation&supplierID=' +
                            this.companyCode +
                            '&propSelection=all',
                        '_blank',
                    );
                    break;
                case 'bank_detail':
                    window.open('?module=configuration&command=bankDetail&compID=' + this.companyCode, '_blank');
                    break;
                case 'sms_send':
                    bus.$emit('toggleSMSSendingModal', {
                        property_code: '',
                        lease_code: '',
                        company_code: this.companyCode,
                        contact_detail_id: '',
                        form_section: 'company-form-template',
                    });
                    break;
            }
        },

        checkIfReadOnly() {
            if (this.userSubTypeIsPMReadOnly()) {
                this.readOnly = true;
            }
        },

        resetErrorMessages() {
            this.errorMessage = [];
            this.errorServerMessage = [];
        },

        resetCompanyCode() {
            this.newCompanyCode = '';
            this.companyCode = '';
            this.proceedWithNewCompanyCode = false;
        },

        proceedWithSaving() {
            return (
                (this.companyCodeExists && this.isExistingCompany()) ||
                (this.companyCodeExists && this.userIsTrustAccountant()) ||
                (this.companyCodeExists && this.isNewCompany() && this.companyIsRejected()) ||
                !this.companyCodeExists
            );
        },

        newCompany() {
            if (this.readOnly) {
                return false;
            }

            this.formMode = FORM_MODE.NEW;

            if (this.userIsTrustAccountant()) {
                this.saveButtonLabel = 'Save Details';
            } else {
                this.saveButtonLabel = 'Submit';
            }

            this.manageCompanyTitle = 'New Company';
            this.errorMessage = [];
            this.errorServerMessage = [];
            this.companyId = '';
            this.newCompanyCode = '';
            this.companyCode = '';
            this.company_existed = true;
            this.proceedWithNewCompanyCode = false;
            this.companyFormDataStatus = DATA_STATUS.NEW;
            this.emailCentralisationDataForSaving = [];

            this.updatePageTitle(this.manageCompanyTitle);
            this.loadEmailCentralisationConfig();
            this.loadCountryDefaults(false);
        },

        cancelNewCompany() {
            this.formMode = FORM_MODE.EXISTING;
            this.saveButtonLabel = 'Update Details';
            this.manageCompanyTitle = 'Company Summary';
            this.errorMessage = [];
            this.errorServerMessage = [];
            this.companyId = '';
            this.cancelledCompanyCode = this.newCompanyCode;
            this.newCompanyCode = '';
            this.companyCode = '';
            this.company_existed = true;
            this.proceedWithNewCompanyCode = false;
            this.companyFormDataStatus = DATA_STATUS.EXISTING;

            this.updatePageTitle(this.manageCompanyTitle);

            this.clearContactsList();
            this.resetCompanyTypeVariables();
            this.resetCompanyLookupVariables(true);
            this.resetABNLookupVariables();

            this.company_state = '';
            this.company_suburb = '';
        },

        editCompanyForm() {
            if (!this.readOnly) {
                this.editForm = true;
                this.suburbFilteredList(this.company_state);
            }
        },

        doubleClickForm() {
            if (!this.readOnly && this.pageAction !== 'view' && this.pageAction !== 'review') {
                this.editForm = true;
            }
        },

        fetchSupplierTypeList() {
            let formData = new FormData();
            const apiUrl = 'dropDowns/supplierTypes';
            this.$api.post(apiUrl, formData).then((response) => {
                this.supplierTypeDropDownListItems = this.convertToArrayOfObjects(response.data);
            });
        },

        fetchCompanyGroupList() {
            let formData = new FormData();
            const apiUrl = 'dropDowns/companyGroup';
            this.$api.post(apiUrl, formData).then((response) => {
                this.companyGroupDropDownListItems = this.convertToArrayOfObjects(response.data);
            });
        },

        fetchTaxStatusList() {
            let formData = new FormData();
            const apiUrl = 'dropDowns/tax-rates';
            this.$api.post(apiUrl, formData).then((response) => {
                this.taxStatusDropDownListItems = this.convertToArrayOfObjects(response.data).map((status) => {
                    status.label = status.label.replace('GST', this.taxLabel);
                    return status;
                });
            });
        },

        fetchStateList: function () {
            let formData = new FormData();
            formData.append('countryCode', this.company_country);
            this.stateList = [];
            const apiUrl = 'loadAPIStatesDropDownList';
            this.$api.post(apiUrl, formData).then((response) => {
                this.stateList = response.data.stateList;

                if (this.company_country !== this.companyDataTempBackup.company_country) {
                    this.company_state = null;
                    this.company_state_display = '';

                    if (this.$refs.refCompanyState !== undefined) {
                        this.$refs.refCompanyState.select_val = [];
                    }

                    if (this.$refs.refSuburb !== undefined) {
                        this.$refs.refSuburb.select_val = [];
                    }
                } else {
                    const self = this;
                    setTimeout(function () {
                        self.company_state = self.companyDataTempBackup.company_state ?? '';
                        self.company_state_display = self.companyDataTempBackup.company_state_display ?? '';
                    }, 200);
                }
            });
        },

        fetchStateDisplayName() {
            const stateData = this.stateList.filter((state) => state.value === this.company_state);
            this.company_state_display = !isEmpty(stateData) ? stateData[0].label : '';
            this.companyDataTempBackup.company_state_display = this.company_state_display;
        },

        backupCompanyData() {
            this.companyDataTempBackup = {
                isDebtor: this.isDebtor,
                isOwner: this.isOwner,
                isSupplier: this.isSupplier,
                supplierType: this.supplierType,
                companyGroup: this.companyGroup,
                isSupplierAgent: this.isSupplierAgent,
                cirrufm: this.cirrusfm,
                company_name: this.company_name,
                company_gst_no: this.company_gst_no,

                company_country: this.company_country,
                company_country_display: this.company_country_display,
                company_street: this.company_street,
                company_suburb: this.company_suburb,
                company_state: this.company_state,
                company_state_display: this.company_state_display,
                company_post_code: this.company_post_code,
                company_email: this.company_email,

                company_payment_method: this.company_payment_method,
                company_direct_debit: this.company_direct_debit,

                company_gst_code: this.company_gst_code,
                company_status: this.company_status,
                send_remittance: this.send_remittance,

                company_invoice_add: this.company_invoice_add,
                disable_invoice_dates: this.disable_invoice_dates,
                disable_eft_details: this.disable_eft_details,
                require_order_no: this.require_order_no,
                payment_acct_code: this.payment_acct_code,
                use_owner_chart: this.use_owner_chart,
                income_acct_code: this.income_acct_code,

                emailCentralisationSetting: this.emailCentralisationSetting,
            };
        },

        updateOldValues() {
            const countryData = this.dd_country_list.filter((country) => country.value === this.company_country);
            this.company_country_display = !isEmpty(countryData) ? countryData[0].label : '';
            this.backupCompanyData();
        },

        suburbSelected(data) {
            if (isEmpty(data)) return false;
            this.company_suburb = data.suburb ?? '';
            this.company_post_code = data.pcode ?? '';
        },

        suburbFilteredList(state_value) {
            this.suburbFilteredListItems = [];
            let filtered_list = [];

            if (isEmpty(state_value)) return false;

            if (suburbList.length > 0) {
                filtered_list = suburbList
                    .filter((value) => value.State === state_value)
                    .map((value) => {
                        value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                        value['value'] = { suburb: value.suburb, pcode: value.pcode };
                        return value;
                    });
            }

            if (filtered_list.length === 0) {
                const temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filtered_list.push(temp_value);
            }

            this.suburbFilteredListItems = filtered_list;
        },

        setTaxStatus() {
            let find_val = this.taxStatusDropDownListItems.filter((row) => {
                return row.value === this.company_gst_code;
            });

            if (typeof this.$refs == 'object' && !isEmpty(this.$refs.refCompanyTaxStatus)) {
                let refs = this.$refs;
                refs.refCompanyTaxStatus.select_val = find_val;
            }
        },

        setSupplierType() {
            if (isEmpty(this.supplierType)) return false;

            let find_val = this.supplierTypeDropDownListItems.filter((row) => {
                return row.value === this.supplierType;
            });

            if (typeof this.$refs == 'object' && !isEmpty(this.$refs.refCompanySupplierType)) {
                let refs = this.$refs;
                refs.refCompanySupplierType.select_val = find_val;
            }
        },

        setCountry() {
            let find_val = this.dd_country_list.filter((row) => {
                return row.value === this.company_country;
            });

            if (typeof this.$refs == 'object' && !isEmpty(this.$refs.refCompanyCountry)) {
                let refs = this.$refs;
                refs.refCompanyCountry.select_val = find_val;
            }
        },

        getActionParameter() {
            this.pageAction = this.pageActionParameter ?? 'manage';
        },

        setDefaultFormDisplayVariables() {
            this.companyCode = this.companyCodeParameter;
            this.pageAction = 'manage';
            this.formMode = FORM_MODE.EXISTING;
            this.editForm = false;
        },

        setCompanyCodeVariables() {
            this.companyCode = this.companyCodeParameter;
            this.newCompanyCode = this.companyCode;
        },

        setViewEFTReturnVariables() {
            this.returnToEft = true;
            this.returnToEftData.batchId = this.batchId ?? '';
            this.returnToEftData.paymentType = this.paymentType ?? '';
            this.returnToEftData.viewDate = this.viewDate ?? '';
            this.returnToEftData.date = this.date ?? '';
            this.returnToEftData.viewDisplayOption = this.viewDisplayOption ?? '';
        },

        setFormFieldsDisplay() {
            if (!this.pageAction || this.pageAction === 'manage') {
                this.setDefaultFormDisplayVariables();
            } else if (this.pageAction === 'new') {
                this.newCompany();
            } else if (this.pageAction === 'review' && this.userIsTrustAccountant()) {
                this.setCompanyCodeVariables();
                this.formMode = FORM_MODE.NEW;
                this.editForm = true;
                this.saveButtonLabel = 'Submit';
            } else if (this.pageAction === 'load' && this.userIsNotTrustAccountant()) {
                this.setCompanyCodeVariables();
                this.formMode = FORM_MODE.NEW;
                this.saveButtonLabel = 'Submit';
            } else if (this.pageAction === 'view') {
                this.setCompanyCodeVariables();
                this.formMode = FORM_MODE.EXISTING;
                this.editForm = false;
                this.saveButtonLabel = 'Submit';
                this.manageCompanyTitle = 'Loading Details';
                this.newCompanyTabLabel = 'Company';
            } else if (this.pageAction === 'contact') {
                this.setCompanyCodeVariables();
            }

            if (this.returnPageParameter && this.returnPageParameter === 'viewEFT') this.setViewEFTReturnVariables();

            if (this.leaseId) {
                this.returnToLease = true;
            }

            this.loadEmailCentralisationConfig();
            this.updateEmailAddressBook();
        },

        returnToEFT() {
            this.returnLink =
                '?module=ap&command=viewEFTs&returnPage=' +
                this.returnPageParameter +
                '&paymentType=' +
                this.returnToEftData.paymentType +
                '&batchID=' +
                this.returnToEftData.batchId +
                '&status=' +
                this.returnToEftData.viewDisplayOption +
                '&date=' +
                this.returnToEftData.date +
                '&viewDate=' +
                this.returnToEftData.viewDate;
            window.location.href = this.returnLink;
        },

        returnToLeaseSummary() {
            if (this.userIsTrustAccountant()) {
                this.returnLink =
                    '?module=leases&command=lease_page_v2&property_code=' +
                    this.propertyId +
                    '&lease_code=' +
                    this.leaseId;
            } else {
                this.returnLink =
                    '?module=leases&command=lease_summary_page&property_code=' +
                    this.propertyId +
                    '&lease_code=' +
                    this.leaseId;
            }

            window.location.href = this.returnLink;
        },

        goToLiveCompany() {
            this.returnLink = '?module=companies&command=company_v2&companyID=' + this.companyCode;
            window.location.href = this.returnLink;
        },

        forceRerender() {
            this.loadCompanyDetails();
            this.updateEmailAddressBook();
        },

        loadCompanyDetails(temporary_data = false) {
            if (this.isExistingCompany() && isEmpty(this.companyCode.trim())) return true;

            if (this.isNewCompany() && isEmpty(this.newCompanyCode.trim())) return true;

            this.showLoadingOverlay = false;
            this.errorServerMessage = [];
            this.resetCompanyTypeVariables();
            this.company_approval_status = APPROVAL_STATUS.PENDING;
            this.feedback = '';
            this.editForm = false;
            this.disableSaveCompanyButton = false;

            let formData = new FormData();
            if (this.isNewCompany()) {
                formData.append('company_code', this.newCompanyCode);
            } else {
                formData.append('company_code', this.companyCode);
            }

            if (this.companyCodeParameter) {
                formData.append('action', this.pageAction);
            } else {
                formData.append('action', 'manage');
            }

            formData.append('temporary_data', temporary_data);
            formData.append('no_load', true);

            if (this.userIsNotTrustAccountant() && this.isNewCompany()) {
                this.companyDetailsButtonLoading = true;
            }

            // get company details by code
            const apiUrl = 'company/load-all-company-details';
            this.$api.post(apiUrl, formData).then((response) => {
                let company_data = response.data;

                this.loadCountryDefaults(false);

                if (!isEmpty(company_data)) {
                    if (company_data.company_id !== undefined) {
                        this.updatePageTitle(company_data.company_code + ' - ' + company_data.tenant_name);
                    } else {
                        this.updatePageTitle('Company Not Found');
                    }

                    this.companyId = company_data.company_id;
                    this.companyCode = company_data.company_code;
                    this.originalCompanyCode = this.companyCode;
                    this.create_date = company_data.create_date;
                    this.update_date = company_data.update_date;

                    this.isDebtor = company_data.is_debtor;
                    this.isOwner = company_data.is_owner;
                    this.isSupplier = company_data.is_supplier;
                    this.supplierType = company_data.supplier_type;
                    this.companyGroup = company_data.company_group;
                    this.isSupplierAgent = company_data.is_supplier_agent;
                    this.cirrusfm = company_data.cirrusfm;

                    if (this.isOwner === true || this.isSupplier === true) {
                        if (!this.readOnly) {
                            this.shortcuts = [
                                { icon: 'summarize', title: 'Supplier Reconciliation', shortcut_code: 'supp_recon' },
                                {
                                    icon: 'account_balance',
                                    title: 'Change Banking Details',
                                    shortcut_code: 'bank_detail',
                                },
                                { icon: 'message', title: 'Send SMS', shortcut_code: 'sms_send' },
                            ];
                        } else {
                            this.shortcuts = [
                                { icon: 'summarize', title: 'Supplier Reconciliation', shortcut_code: 'supp_recon' },
                                { icon: 'message', title: 'Send SMS', shortcut_code: 'sms_send' },
                            ];
                        }
                    } else {
                        if (!this.readOnly) {
                            this.shortcuts = [
                                {
                                    icon: 'account_balance',
                                    title: 'Change Banking Details',
                                    shortcut_code: 'bank_detail',
                                },
                                { icon: 'message', title: 'Send SMS', shortcut_code: 'sms_send' },
                            ];
                        }
                    }

                    this.company_name = company_data.tenant_name;
                    this.company_gst_no = company_data.tenant_gst_no;

                    const tenant_state = company_data.tenant_state ?? {};
                    this.company_state = !isEmpty(tenant_state) ? tenant_state.fieldKey : '';
                    this.company_state_display = !isEmpty(tenant_state) ? tenant_state.fieldValue : '';
                    const tenant_country = company_data.tenant_country ?? {};
                    this.company_country = !isEmpty(tenant_country) ? tenant_country.fieldKey : '';
                    this.company_country_display = !isEmpty(tenant_country) ? tenant_country.fieldValue : '';
                    this.company_street = company_data.tenant_street;
                    this.company_suburb = company_data.tenant_suburb;
                    this.company_post_code = company_data.tenant_post_code;
                    this.company_email = company_data.tenant_email;

                    this.company_payment_method = parseInt(company_data.payment_method);
                    this.companyPayMethod = this.company_payment_method;
                    this.company_direct_debit = company_data.banking_details.direct_debit;
                    this.company_bsb = company_data.banking_details.bsb;
                    this.company_acct_no = company_data.banking_details.acc_no;
                    this.company_acc_name = company_data.banking_details.acc_name;
                    this.company_bank_name = company_data.banking_details.bank_name;
                    this.company_biller_code = company_data.banking_details.biller_code;
                    this.company_cheque_days = company_data.banking_details.cheque_days;
                    this.company_direct_banking = company_data.direct_banking;
                    this.company_direct_banking = company_data.tenant_dir_bank;
                    this.company_gst_code = company_data.tenant_gst_code;
                    this.company_status = this.toggleBoolValue(Number(company_data.is_active));
                    this.send_remittance = this.toggleBoolValue(Number(company_data.send_remittance));
                    this.company_invoice_add = company_data.invoice_address;
                    this.disable_invoice_dates = company_data.disable_invoice_dates;
                    this.disable_eft_details = company_data.disable_eft_details;
                    this.require_order_no = company_data.require_order_no;
                    this.payment_acct_code = company_data.payment_acct_code;
                    this.use_owner_chart = company_data.use_owner_chart;
                    this.income_acct_code = company_data.income_acct_code;

                    this.resetABNLookupVariables();
                    this.resetCompanyLookupVariables();

                    this.companyLookupButtonLoading = false;
                    this.abnLookupButtonLoading = false;
                    this.abnDetailsLoading = false;

                    this.companyLookupButtonLabel = 'Look up Company Name';
                    this.companyLookupHideButtonLabel = 'Hide Search Results';
                    this.abnLookupButtonLabel = 'Look up ABN';

                    this.emailCentralisationSetting = company_data.email_centralisation_setting;
                    this.email_centralisation_setup_config = Number(company_data.email_cen_setup);

                    this.backupCompanyData();
                    this.updateEmailAddressBook();

                    this.companyFormDataStatus = company_data.company_status;

                    if (this.pageAction === 'review' || this.pageAction === 'load' || this.pageAction === 'view') {
                        this.company_approval_status = parseInt(company_data.approval_status);
                        this.feedback = company_data.company_feedback;

                        if (this.pageAction === 'view') {
                            this.manageCompanyTitle =
                                this.pageTitleList[this.company_approval_status] ?? 'Company Summary';
                        }
                    }

                    if (this.pageAction === 'view') {
                        this.company_approver = company_data.approver;
                        this.company_approval_date = company_data.approval_date;
                    }

                    this.suburbFilteredList(this.company_state);

                    if (this.pageAction === 'review' || this.pageAction === 'load') {
                        this.defaultNewCompanyAsCheque = Boolean(Number(company_data.cheque_by_default));
                    }

                    setTimeout(() => this.setCountry(), 50);
                    setTimeout(() => this.setTaxStatus(), 50);
                    setTimeout(() => this.setSupplierType(), 50);

                    if (this.pageAction === 'contact') {
                        const contactsTab = this.$refs.contactstab;
                        contactsTab.isActive = true;
                        contactsTab.click();
                    }

                    this.errorServerMessage = [];
                    this.companyCodeExists = true;
                } else {
                    this.errorServerMessage.push(['No company data found.']);
                    this.companyCode = '';
                }

                this.showLoadingOverlay = false;

                if (this.userIsNotTrustAccountant() && this.isNewCompany()) {
                    this.companyDetailsButtonLoading = false;
                }
            });
        },

        setInitialCompanyCountry() {
            this.company_country = this.countryDefaults.country_code;
        },

        loadEmailAddressBookList() {
            let formData = new FormData();
            const apiUrl = 'company/fetch/email-address-book';

            this.$api.post(apiUrl, formData).then((response) => {
                this.emailCentralisationSetting = response.data.email_centralisation_list;
                this.companyDataTempBackup.emailCentralisationSetting = this.emailCentralisationSetting;
            });
        },

        loadEmailCentralisationConfig() {
            let formData = new FormData();
            formData.append('no_load', true);
            const apiUrl = 'company/fetch/email-centralisation-config';
            this.$api.post(apiUrl, formData).then((response) => {
                let email_cen_data = response.data;
                this.email_centralisation_setup_config = Number(email_cen_data.email_cen_setup);
            });
        },

        saveCompany: function () {
            this.errorMessage = [];
            this.errorServerMessage = [];
            this.disableSaveCompanyButton = true;

            if (!this.proceedWithSaving()) {
                this.$noty.error(
                    'To save another company enter a new Company Code and click on the "Create New" button.',
                );
                this.disableSaveCompanyButton = false;
                return false;
            }

            if (this.isOwner || this.isSupplier) {
                if (isEmpty(this.company_gst_code)) {
                    this.errorMessage.push({
                        id: 'company_tax_status',
                        message: 'You have not specified a tax status.',
                    });
                    this.$noty.error('You have not specified a tax status.');
                }
            }

            // VALIDATE COUNTRY DEFAULT RELATED FIELDS
            // BUSINESS LENGTH
            if (this.company_gst_no.length > this.countryDefaults.business_length) {
                let _message =
                    this.countryDefaults.business_label +
                    ' should not exceed ' +
                    this.countryDefaults.business_length +
                    ' characters';
                this.errorMessage.push({ id: 'company_abn', message: _message });
                this.$noty.error(_message);
            }

            // BANK ACCOUNT LENGTH
            if (this.company_payment_method === PAYMENT_METHODS.EFT) {
                if (isEmpty(this.company_acct_no.trim())) {
                    let _message = 'You have not specified a Bank Account Number';
                    this.errorMessage.push({ id: 'company_acct_no', message: _message });
                    this.$noty.error(_message);
                } else if (this.company_acct_no.length > this.countryDefaults.bank_account_length) {
                    let _message =
                        'Bank Account Number should not exceed ' +
                        this.countryDefaults.bank_account_length +
                        ' characters';
                    this.errorMessage.push({ id: 'company_acct_no', message: _message });
                    this.$noty.error(_message);
                }

                if (isEmpty(this.company_acc_name.trim())) {
                    let _message = 'You have not specified an Account Name';
                    this.errorMessage.push({ id: 'company_acc_name', message: _message });
                    this.$noty.error(_message);
                }
            }

            // IF BSB IS DISPLAYED
            if (
                this.company_payment_method === PAYMENT_METHODS.EFT &&
                this.countryDefaults.display_bsb &&
                isEmpty(this.company_bsb.trim())
            ) {
                this.errorMessage.push({ id: 'company_bsb', message: `You have not specified a ${this.bsbLabel}.` });
                this.$noty.error(`You have not specified a ${this.bsbLabel}.`);
            }

            if (isEmpty(this.company_post_code.trim())) {
                this.errorMessage.push({ id: 'company_post_code', message: 'You have not specified a post code.' });
                this.$noty.error('You have not specified a post code.');
            }

            // IF STATE IS DISPLAYED
            if (isEmpty(this.company_state) && this.countryDefaultsForAddress.display_state) {
                this.errorMessage.push({ id: 'company_state', message: 'You have not specified a state.' });
                this.$noty.error('You have not specified a state.');
            }

            if (isEmpty(this.company_suburb.trim())) {
                this.errorMessage.push({
                    id: 'company_city',
                    message: 'You have not specified a ' + this.suburbLabel + '.',
                });
                this.$noty.error('You have not specified a ' + this.suburbLabel + '.');
            }

            if (isEmpty(this.company_street.trim())) {
                this.errorMessage.push({ id: 'company_street', message: 'You have not specified an address.' });
                this.$noty.error('You have not specified an address.');
            }

            if (isEmpty(this.company_country)) {
                this.errorMessage.push({ id: 'company_country', message: 'You have not specified a country.' });
                this.$noty.error('You have not specified a country.');
            }

            if (isEmpty(this.company_name.trim())) {
                this.errorMessage.push({ id: 'company_name', message: 'You have not specified a company name.' });
                this.$noty.error('You have not specified a company name.');
            }

            if (this.errorMessage.length > 0) {
                this.disableSaveCompanyButton = false;
                return false;
            }

            this.showLoadingOverlay = true;

            let formData = new FormData();
            formData.append('form_mode', this.formMode);
            formData.append('company_code', this.companyCode);
            formData.append('new_company_code', this.newCompanyCode);

            formData.append('is_debtor', this.isDebtor);
            formData.append('is_owner', this.isOwner);
            formData.append('is_supplier', this.isSupplier);
            formData.append('supplier_type', this.supplierType);
            formData.append('company_group', this.companyGroup);
            formData.append('is_supplier_agent', this.isSupplierAgent);
            formData.append('cirrusfm', this.cirrusfm);

            formData.append('company_name', this.company_name);
            formData.append('company_gst_no', this.company_gst_no);
            formData.append('company_country', this.company_country);
            formData.append('company_street', this.company_street);
            formData.append('company_suburb', this.company_suburb);
            formData.append('company_state', this.company_state);
            formData.append('company_post_code', this.company_post_code);
            formData.append('company_email', this.company_email);
            formData.append(
                'company_payment_method',
                this.incrementValueForSaving(parseInt(this.company_payment_method)),
            );

            formData.append('company_direct_debit', this.toggleBoolValue(Number(this.company_direct_debit)));
            formData.append('company_bsb', this.company_bsb);
            formData.append('company_acct_no', this.company_acct_no);
            formData.append('company_acc_name', this.company_acc_name);
            formData.append('company_bank_name', this.company_bank_name);

            formData.append('company_biller_code', this.company_biller_code);
            formData.append('company_cheque_days', this.company_cheque_days);

            formData.append('company_gst_code', this.company_gst_code);
            formData.append('company_status', this.toggleBoolValue(Number(this.company_status))); //UI 0 is active; DB 1 is active
            formData.append('send_remittance', this.toggleBoolValue(Number(this.send_remittance)));
            formData.append('company_invoice_add', this.company_invoice_add);
            formData.append('disable_invoice_dates', Number(this.disable_invoice_dates));
            formData.append('disable_eft_details', Number(this.disable_eft_details));
            formData.append('require_order_no', Number(this.require_order_no));
            formData.append('email_centralisation_setting', JSON.stringify(this.emailCentralisationSetting));

            if (this.pageAction === 'load' && this.userIsNotTrustAccountant()) {
                formData.append('company_form_status', this.company_approval_status);
            } else {
                formData.append('company_form_status', this.companyFormDataStatus);
            }

            formData.append('action_parameter', this.pageAction);
            formData.append('no_load', true);
            const apiUrl = 'company/save-company';

            this.$api.post(apiUrl, formData).then((response) => {
                let processStatus = response.data.status;
                let reloadData = false;
                let redirectToView = false;

                if (processStatus === 'failed') {
                    this.errorServerMessage = response.data.validation_errors;
                    this.showLoadingOverlay = false;
                }

                if (processStatus === 'success' && this.isNewCompany()) {
                    this.emailCentralisationDataForSaving = this.emailCentralisationSetting;
                    this.saveEmailCentralisationData(response.data.company_id);
                }

                if (processStatus === 'success' && this.isNewCompany() && this.userIsTrustAccountant()) {
                    this.formMode = FORM_MODE.EXISTING;
                    this.editForm = false;

                    this.fetchCompanyList();
                    this.SET_COMPANY_CODE(this.newCompanyCode);
                    this.companyCode = this.newCompanyCode;

                    this.$noty.success(response.data.message);

                    this.manageCompanyTitle = 'Company Summary';

                    this.forceRerender();
                    this.showLoadingOverlay = false;
                    reloadData = true;
                } else if (processStatus === 'success' && this.isNewCompany() && this.userIsNotTrustAccountant()) {
                    this.showLoadingOverlay = false;
                    this.$noty.success(response.data.message);
                    redirectToView = true;
                } else if (processStatus === 'success' && this.isExistingCompany()) {
                    this.showLoadingOverlay = false;
                    this.editForm = false;

                    let message = response.data.message;
                    this.$noty.success(message);
                    reloadData = true;
                }

                if (processStatus === 'success') {
                    this.resetCompanyLookupVariables(true);
                    this.resetABNLookupVariables();
                    this.companyCodeExists = true;
                }

                if (reloadData) {
                    this.loadCompanyDetails();
                    this.updateEmailAddressBook();
                    this.fetchStateDisplayName();
                    this.updateOldValues();
                }

                if (redirectToView) {
                    const origin = window.location.origin;
                    const path = window.location.pathname;
                    const redirectUrl =
                        origin +
                        path +
                        '?command=company_v2&module=companies&companyID=' +
                        this.newCompanyCode +
                        '&action=view';
                    setTimeout(() => (window.location.href = redirectUrl), 2000);
                }

                this.disableSaveCompanyButton = false;
            });
        },

        saveEmailCentralisationData: function (contactTableId) {
            if (isEmpty(this.company_email)) {
                return;
            }

            let formData = new FormData();
            formData.append('contact_table_name', this.emailCentralisationReferenceTable());
            formData.append('contact_table_id', contactTableId);
            formData.append('no_load', true);
            formData.append('email_centralisation_values', JSON.stringify(this.emailCentralisationDataForSaving));

            this.$api
                .post('email/process/save-email-centralisation', formData)
                .then((response) => {
                    if (response.data.status !== 'success') {
                        this.emailCentralisationDataForSaving = [];
                        return;
                    }

                    this.emailCentralisationSetting = this.emailCentralisationDataForSaving;
                })
                .catch((errorResponse) => {
                    this.$noty.error(errorResponse.message);
                });
        },

        displayCompanyType: function () {
            let companyTypes = [];

            if (this.isDebtor) companyTypes.push('Debtor');
            if (this.isOwner) companyTypes.push('Owner');
            if (this.isSupplier) companyTypes.push('Supplier');
            if (this.isSupplierAgent) companyTypes.push('Agent');
            if (this.cirrusfm) companyTypes.push('Enabled on CirrusFM');

            return companyTypes.join(', ');
        },

        displayCirrusFM: function () {
            let checkCount = 0;
            let supplierCheck = 0;

            if (this.isSupplier) supplierCheck++;
            if (this.isDebtor) checkCount++;
            if (this.isOwner) checkCount++;
            if (this.isSupplierAgent) checkCount++;

            return checkCount === 0 && supplierCheck > 0;
        },

        supplierTypeName: function () {
            const typeData = this.supplierTypeDropDownListItems.filter((type) => type.field_key === this.supplierType);
            const typeName = !isEmpty(typeData) ? typeData[0].field_value : '';
            return typeName;
        },

        companyGroupName: function () {
            const groupData = this.companyGroupDropDownListItems.filter(
                (group) => group.field_key === this.companyGroup,
            );
            const groupName = !isEmpty(groupData) ? groupData[0].field_value : '';
            return groupName;
        },

        paymentMethodName: function () {
            const paymentMethod = this.paymentMethodList[this.companyPayMethod] ?? '';
            return paymentMethod;
        },

        companyStatus: function () {
            return this.company_status === ACTIVE_COMPANY ? 'Yes' : 'No';
        },

        sendRemittance: function () {
            return this.send_remittance === SEND_REMITTANCE_ENABLED ? 'Yes' : 'No';
        },

        directDebit: function () {
            return this.company_direct_debit === DIRECT_DEBIT_ENABLED ? 'Yes' : 'No';
        },

        directBanking: function () {
            return this.company_direct_banking === DIRECT_BANKING_ENABLED ? 'Yes' : 'No';
        },

        taxStatus: function () {
            const taxStatusData = this.taxStatusDropDownListItems.filter(
                (status) => status.value == this.company_gst_code,
            );
            const taxStatus = !isEmpty(taxStatusData) ? taxStatusData[0].label.replace('GST', this.taxLabel) : '';
            return taxStatus;
        },

        invoiceDatesStatus: function () {
            return this.disable_invoice_dates ? 'Dates are hidden' : 'Dates are shown';
        },

        eftDetailStatus: function () {
            return this.disable_eft_details ? 'EFT Details are hidden' : 'EFT Details are shown';
        },

        orderNumberStatus: function () {
            return this.require_order_no ? 'Order Number required' : 'Order Number not required';
        },

        clearContactsList: function () {
            this.errorServerMessage = [];

            if (isEmpty(this.cancelledCompanyCode)) {
                return;
            }

            let formData = new FormData();
            formData.append('company_code', this.cancelledCompanyCode);

            const apiUrl = 'company/clear-contacts-list';
            this.$api
                .post(apiUrl, formData)
                .then((response) => {
                    const deleteStatus = response.data.status;
                    const message = [response.data.message];

                    if (!deleteStatus) {
                        this.errorServerMessage.push(message);
                    }
                })
                .catch((errorResponse) => {
                    this.$noty.error(errorResponse.response.data.message);
                    this.errorServerMessage = Object.entries(errorResponse.response.data.errors)
                        .map((errorDetail) => errorDetail[1])
                        .flat()
                        .map((errorItem) => [errorItem]);
                });
        },

        resetCompanyTypeVariables: function () {
            this.isDebtor = false;
            this.isOwner = false;
            this.isSupplier = false;
            this.isSupplierAgent = false;
            this.cirrusfm = false;
        },

        resetCompanyLookupVariables: function (exclude_selected = false) {
            this.companyMatchListDisplay = false;
            this.companyMatchListCount = 0;
            this.companyMatchListMessage = '';
            this.companyMatchDropDownListItems = [];
            if (!exclude_selected) this.companyMatchSelectedGst = '';
        },

        resetABNLookupVariables: function () {
            this.abnMessage = '';
            this.abnDetails = false;
            this.abnCompanyName = '';
            this.abnValue = '';
            this.abnCompanyType = '';
            this.abnGstStatus = '';
            this.abnGstCode = '';
            this.abnStateCode = '';
            this.abnPostCode = '';
            this.abnDetailDisplay = false;
        },

        resetCompanyForm: function () {
            const temp_data = this.companyDataTempBackup;

            this.isDebtor = temp_data.isDebtor;
            this.isOwner = temp_data.isOwner;
            this.isSupplier = temp_data.isSupplier;
            this.supplierType = temp_data.supplierType;
            this.companyGroup = temp_data.companyGroup;
            this.isSupplierAgent = temp_data.isSupplierAgent;
            this.cirrusfm = temp_data.cirrufm;

            this.company_name = temp_data.company_name;
            this.company_gst_no = temp_data.company_gst_no;

            this.company_country = temp_data.company_country;
            this.company_country_display = temp_data.company_country_display;
            this.company_street = temp_data.company_street;
            this.company_suburb = temp_data.company_suburb;
            this.company_state = temp_data.company_state;
            this.company_state_display = temp_data.company_state_display;
            this.company_post_code = temp_data.company_post_code;
            this.company_email = temp_data.company_email;

            this.company_payment_method = temp_data.company_payment_method;
            this.company_direct_debit = temp_data.company_direct_debit;

            this.company_gst_code = temp_data.company_gst_code;
            this.company_status = temp_data.company_status;
            this.send_remittance = temp_data.send_remittance;

            this.company_invoice_add = temp_data.company_invoice_add;
            this.disable_invoice_dates = temp_data.disable_invoice_dates;
            this.disable_eft_details = temp_data.disable_eft_details;

            this.resetABNLookupVariables();
            this.resetCompanyLookupVariables();

            this.companyLookupButtonLoading = false;
            this.abnLookupButtonLoading = false;
            this.abnDetailsLoading = false;

            this.companyLookupButtonLabel = 'Look up Company Name';
            this.companyLookupHideButtonLabel = 'Hide Search Results';
            this.abnLookupButtonLabel = 'Look up ABN';

            this.updateEmailAddressBook();

            this.editForm = false;
            this.disableSaveCompanyButton = false;
        },

        lookupABN: function (event) {
            // LOCK TO AU
            if (this.countryDefaults.country_code === 'AU') {
                let button = $(event.currentTarget);
                button.attr('disabled', 'disabled');
                this.abnLookupButtonLabel = '';
                this.abnLookupButtonLoading = true;

                let formData = new FormData();
                formData.append('company_code', this.companyCode);
                formData.append('company_gst_no', this.company_gst_no);
                formData.append('company_country', this.company_country);
                formData.append('no_load', true);

                const apiUrl = 'company/lookup-abn';
                this.abnDetailDisplay = true;

                //Reset first the ABN Result
                this.resetABNLookupVariables();

                this.$api.post(apiUrl, formData).then((response) => {
                    let abnData = response.data;

                    //Reset Company Name lookup variables
                    this.resetCompanyLookupVariables(true);

                    this.abnMessage = abnData.hasOwnProperty('message') ? abnData.message : '';

                    if (abnData.hasOwnProperty('data') && Object.keys(abnData.data).length != 0) {
                        this.abnDetails = true;
                        this.abnCompanyName = abnData.data.name;
                        this.abnValue = abnData.data.abn;
                        this.abn_acn = abnData.data.acn;
                        this.abnCompanyType = abnData.data.company_type;
                        this.abnGstStatus = abnData.data.taxStatus;
                        this.abnGstCode = abnData.data.gst;
                        this.abnStateCode = abnData.data.state;
                        this.abnPostCode = abnData.data.postal;
                        this.abnStatus = abnData.data.abnStatus;

                        if (this.abnStatus === 'Cancelled') {
                            this.abnMessage = 'Selected ABN is cancelled';
                        }
                    }

                    button.removeAttr('disabled');
                    this.abnLookupButtonLabel = 'Look up ABN';
                    this.abnLookupButtonLoading = false;
                });
            }
        },

        copyABNValue: function (event) {
            let button = $(event.currentTarget);
            let label = button.data('field-label');
            let abnValue = button.data('field-value');

            switch (label) {
                case 'company-name':
                    this.company_name = abnValue;
                    break;
                case 'company-state':
                    this.company_state = abnValue;
                    this.suburbFilteredList(this.company_state);
                    break;
                case 'company-post-code':
                    this.company_post_code = abnValue;
                    break;
                case 'company-gst-status':
                    this.company_gst_code = abnValue;
                    break;
            }
        },

        lookupCompany: function (event) {
            let button = $(event.currentTarget);
            button.attr('disabled', 'disabled');
            this.companyLookupButtonLabel = '';
            this.companyLookupButtonLoading = true;

            let formData = new FormData();
            formData.append('company_name', this.company_name);
            formData.append('company_country', this.company_country);
            formData.append('no_load', true);

            const apiUrl = 'company/lookup-company';
            this.abnDetailDisplay = true;

            //Reset Company Name lookup variables
            this.resetCompanyLookupVariables();

            this.$api.post(apiUrl, formData).then((response) => {
                let listData = response.data;

                //Reset ABN lookup variables
                this.resetABNLookupVariables();

                if (listData) {
                    if (Object.keys(listData.data).length > 0) {
                        this.companyMatchListCount = Object.keys(listData.data).length;
                        this.companyMatchDropDownListItems = listData.data;
                    } else {
                        this.companyMatchListCount = 0;
                        this.companyMatchListMessage = listData.message;
                    }

                    this.companyMatchListDisplay = true;
                } else {
                    this.companyMatchListCount = 0;
                    this.companyMatchListMessage = listData.message;
                    this.companyMatchListDisplay = true;
                }

                button.removeAttr('disabled');
                this.companyLookupButtonLabel = 'Look up Company Name';
                this.companyLookupButtonLoading = false;
                this.abnDetailsLoading = false;
            });
        },

        hideSearchResults: function () {
            this.resetCompanyLookupVariables();
        },

        selectCompanyName: function () {
            let button = $('.hide-matching-list-btn');
            button.attr('disabled', 'disabled');
            this.companyLookupHideButtonLabel = '';
            this.abnDetailsLoading = true;

            if (!isEmpty(this.companyMatchSelectedGst)) {
                let formData = new FormData();
                formData.append('company_code', this.companyCode);
                formData.append('company_gst_no', this.companyMatchSelectedGst);
                formData.append('no_load', true);

                const apiUrl = 'company/select-abn';
                this.abnDetailDisplay = false;

                //Reset first the ABN Result
                this.resetABNLookupVariables();

                this.$api.post(apiUrl, formData).then((response) => {
                    let abnData = response.data;

                    //Reset Company Name lookup variables
                    this.resetCompanyLookupVariables();

                    this.abnMessage = abnData.hasOwnProperty('message') ? abnData.message : '';

                    if (abnData.hasOwnProperty('data') && Object.keys(abnData.data).length != 0) {
                        this.abnDetails = true;
                        this.abnCompanyName = abnData.data.name;
                        this.abnValue = abnData.data.abn;
                        this.abn_acn = abnData.data.acn;
                        this.abnCompanyType = abnData.data.company_type;
                        this.abnGstStatus = abnData.data.taxStatus;
                        this.abnGstCode = abnData.data.gst;
                        this.abnStateCode = abnData.data.state;
                        this.abnPostCode = abnData.data.postal;
                        this.abnStatus = abnData.data.abnStatus;

                        if (this.abnStatus === 'Cancelled') {
                            this.abnMessage = 'Selected ABN is cancelled';
                        }

                        if (!this.abnDetailDisplay) {
                            this.company_name = this.abnCompanyName;
                            this.company_gst_no = this.abnValue;
                            this.company_state = this.abnStateCode;
                            this.company_post_code = this.abnPostCode;
                            this.company_gst_code = this.abnGstCode;

                            this.suburbFilteredList(this.company_state);
                        }
                    }

                    button.removeAttr('disabled');
                    this.companyLookupHideButtonLabel = 'Hide Search Results';
                    this.abnDetailsLoading = false;
                });
            } else {
                this.companyMatchSelectedGst = '';
                button.removeAttr('disabled');
                this.companyLookupHideButtonLabel = 'Hide Search Results';
                this.abnDetailsLoading = false;
            }
        },

        updateEmailAddressBook: function () {
            const company_types = { debtor: this.isDebtor, owner: this.isOwner, supplier: this.isSupplier };
            this.emailCentralisationSetting = collectAddressBookSettings(
                this.companyDataTempBackup.emailCentralisationSetting,
                company_types,
            );
        },

        emailCentralisationIconDisplay: function () {
            let company_type_count = 0;

            if (Boolean(this.email_centralisation_setup_config)) {
                if (this.isDebtor) company_type_count++;
                if (this.isOwner) company_type_count++;
                if (this.isSupplier) company_type_count++;

                return company_type_count > 0;
            } else {
                return false;
            }
        },

        openCompanyChangelogModal: function () {
            this.showChangeLogModal = true;
        },

        checkCompanyCode: function (checkCountOnly = false) {
            this.errorMessage = [];
            this.errorServerMessage = [];

            if (this.newCompanyCode.length > 10) {
                this.errorServerMessage.push(['Company code allows 10 characters only']);
            } else if (isEmpty(this.newCompanyCode.trim())) {
                this.errorServerMessage.push(['Please input a valid Company Code']);
            }

            if (this.verifyCode(this.newCompanyCode)) {
                this.errorServerMessage.push(['Please use only alphanumeric characters for the Company Code.']);
            }

            if (this.errorServerMessage.length <= 0) {
                this.showTopLoadingBar = !checkCountOnly;
                this.errorServerMessage = [];
                this.proceedWithNewCompanyCode = false;
                this.companyCodeExists = false;

                let formData = new FormData();

                formData.append('company_code', this.newCompanyCode);
                formData.append('no_load', true);

                const apiUrl = 'check-for-duplicate-company';
                this.$api.post(apiUrl, formData).then((response) => {
                    this.errorServerMessage = response.data.validation_errors;
                    this.showTopLoadingBar = false;

                    if (checkCountOnly) {
                        this.companyCodeExists = response.data.company_exists;
                    }

                    if (!checkCountOnly && !response.data.company_exists) {
                        this.defaultNewCompanyAsCheque = Boolean(Number(response.data.set_cheque_as_default));
                        this.proceedWithNewCompanyCode = true;
                        this.companyCodeExists = false;

                        //reset field values
                        this.resetCompanyTypeVariables();

                        this.supplierType = '';
                        this.companyGroup = '';
                        this.company_name = '';
                        this.company_gst_no = '';
                        this.company_country = '';
                        this.company_country_display = '';
                        this.company_street = '';
                        this.company_state = '';
                        this.company_state_display = '';
                        this.company_suburb = '';
                        this.company_post_code = '';
                        this.company_email = '';
                        this.company_payment_method = PAYMENT_METHODS.CHEQUE;
                        this.company_direct_debit = '';
                        this.company_bsb = '';
                        this.company_acct_no = '';
                        this.company_acc_name = '';
                        this.company_bank_name = '';
                        this.company_biller_code = '';

                        this.company_cheque_days = DEFAULT_CHEQUE_DAYS;

                        this.company_gst_code = '';
                        this.company_status = ACTIVE_COMPANY;
                        this.send_remittance = SEND_REMITTANCE_ENABLED;
                        this.company_invoice_add = '';
                        this.disable_invoice_dates = false;
                        this.disable_eft_details = false;
                        this.companyFormDataStatus = DATA_STATUS.NEW;

                        this.loadEmailAddressBookList();
                        this.setInitialCompanyCountry();
                    }
                });
            }
        },

        verifyCode: function (inputString) {
            const newStr = inputString.replace(/[^a-zA-Z0-9-]/g, '');
            return newStr.length !== inputString.length;
        },

        rejectCompany: function () {
            this.errorMessage = [];
            this.errorServerMessage = [];
            this.disableApprovalButtons = true;

            if (isEmpty(this.feedback.trim())) {
                this.errorMessage.push({ id: 'company_feedback', message: 'You need to enter your feedback.' });
                this.$noty.error('You need to enter your feedback.');
                this.disableApprovalButtons = false;
            }

            if (this.errorMessage.length <= 0) {
                this.showLoadingOverlay = true;

                let formData = new FormData();

                formData.append('company_code', this.companyCode);
                formData.append('new_company_code', this.newCompanyCode);
                formData.append('comments', this.feedback);

                const apiUrl = 'company/reject';
                this.$api.post(apiUrl, formData).then((response) => {
                    this.showLoadingOverlay = false;

                    const processStatus = response.data.status;

                    if (processStatus === 'success') {
                        const origin = window.location.origin;
                        const path = window.location.pathname;
                        const redirectUrl = origin + path + '?command=companyTasks&module=companies';

                        window.location.href = redirectUrl;
                    } else {
                        this.$noty.error('Company rejection unsuccessful.');
                        this.disableApprovalButtons = false;
                    }
                });
            }
        },

        approveCompany: function () {
            this.errorMessage = [];
            this.errorServerMessage = [];
            this.disableApprovalButtons = true;

            if (this.isOwner || this.isSupplier) {
                if (isEmpty(this.company_gst_code)) {
                    this.errorMessage.push({
                        id: 'company_tax_status',
                        message: 'You have not specified a tax status.',
                    });
                    this.$noty.error('You have not specified a tax status.');
                }
            }

            if (isEmpty(this.company_post_code.trim())) {
                this.errorMessage.push({ id: 'company_post_code', message: 'You have not specified a post code.' });
                this.$noty.error('You have not specified a post code.');
            }

            if (this.countryDefaultsForAddress.display_state) {
                if (isEmpty(this.company_state)) {
                    this.errorMessage.push({ id: 'company_state', message: 'You have not specified a state.' });
                    this.$noty.error('You have not specified a state.');
                }
            }

            if (isEmpty(this.company_suburb.trim())) {
                this.errorMessage.push({
                    id: 'company_city',
                    message: 'You have not specified a ' + this.suburbLabel + '.',
                });
                this.$noty.error('You have not specified a ' + this.suburbLabel + '.');
            }

            if (isEmpty(this.company_street.trim())) {
                this.errorMessage.push({ id: 'company_street', message: 'You have not specified an address.' });
                this.$noty.error('You have not specified an address.');
            }

            if (isEmpty(this.company_country)) {
                this.errorMessage.push({ id: 'company_country', message: 'You have not specified a country.' });
                this.$noty.error('You have not specified a country.');
            }

            if (isEmpty(this.company_name.trim())) {
                this.errorMessage.push({ id: 'company_name', message: 'You have not specified a company name.' });
                this.$noty.error('You have not specified a company name.');
            }

            // submit form if no error
            if (this.errorMessage.length <= 0) {
                this.showLoadingOverlay = true;

                let formData = new FormData();
                formData.append('form_mode', this.formMode);
                formData.append('old_company_code', this.companyCode);
                formData.append('company_code', this.newCompanyCode);

                formData.append('is_debtor', this.isDebtor);
                formData.append('is_owner', this.isOwner);
                formData.append('is_supplier', this.isSupplier);
                formData.append('supplier_type', this.supplierType);
                formData.append('company_group', this.companyGroup);
                formData.append('is_supplier_agent', this.isSupplierAgent);
                formData.append('cirrusfm', this.cirrusfm);

                formData.append('company_name', this.company_name);
                formData.append('company_gst_no', this.company_gst_no);
                formData.append('company_country', this.company_country);
                formData.append('company_street', this.company_street);
                formData.append('company_suburb', this.company_suburb);
                formData.append('company_state', this.company_state);
                formData.append('company_post_code', this.company_post_code);
                formData.append('company_email', this.company_email);
                formData.append(
                    'company_payment_method',
                    this.incrementValueForSaving(parseInt(this.company_payment_method)),
                );

                formData.append('company_direct_debit', this.company_direct_debit);
                formData.append('company_bsb', this.company_bsb);
                formData.append('company_acct_no', this.company_acct_no);
                formData.append('company_acc_name', this.company_acc_name);
                formData.append('company_bank_name', this.company_bank_name);
                formData.append('company_biller_code', this.company_biller_code);
                formData.append('company_cheque_days', this.company_cheque_days);

                formData.append('company_gst_code', this.company_gst_code);
                formData.append('company_status', this.toggleBoolValue(Number(this.company_status))); //UI 0 is active; DB 1 is active
                formData.append('send_remittance', this.toggleBoolValue(Number(this.send_remittance)));
                formData.append('company_invoice_add', this.company_invoice_add);
                formData.append('disable_invoice_dates', this.disable_invoice_dates);
                formData.append('disable_eft_details', this.disable_eft_details);
                formData.append('require_order_no', Number(this.require_order_no));
                formData.append('company_form_status', this.companyFormDataStatus);

                const apiUrl = 'company/approve';
                this.$api
                    .post(apiUrl, formData)
                    .then((response) => {
                        this.showLoadingOverlay = false;
                        let processStatus = response.data.status;

                        if (!isEmpty(processStatus) && processStatus !== 'success') {
                            this.$noty.error('Company approval unsuccessful.');
                            this.disableApprovalButtons = false;
                            this.errorServerMessage = formatValidationResponseData(response.data);
                            return;
                        }

                        this.errorServerMessage = [];

                        this.formMode = FORM_MODE.EXISTING;
                        this.editForm = false;

                        this.dd_company_list.push({
                            value: this.newCompanyCode,
                            label: this.newCompanyCode + ' - ' + this.company_name,
                        });

                        this.SET_PUSH_DD_COMPANY_LIST({
                            fieldGroup: 'Active',
                            fieldKey: this.newCompanyCode,
                            fieldValue: this.company_name,
                            field_group: 'Active',
                            field_key: this.newCompanyCode,
                            field_value: this.company_name,
                            value: this.newCompanyCode,
                            label: this.newCompanyCode + ' - ' + this.company_name,
                        });

                        this.companyCode = this.newCompanyCode;
                        this.saveButtonLabel = 'Update Details';
                        this.manageCompanyTitle = 'Company Summary';
                        this.pageAction = '';
                        this.loadCompanyDetails();
                        this.forceRerender();

                        this.$noty.success(response.data.message);
                    })
                    .catch((errorResponse) => {
                        this.showLoadingOverlay = false;
                        this.$noty.error(errorResponse.response.data.message);
                        this.errorServerMessage = formatValidationResponseData(errorResponse.response.data);
                        this.disableApprovalButtons = false;
                    });
            } else {
                this.disableApprovalButtons = false;
            }
        },

        loadCountryDefaults: function (forCompanyAddress = false) {
            let formData = new FormData();
            if (forCompanyAddress) formData.append('country', this.company_country);

            const apiUrl = 'country_defaults/load';
            this.$admin.post(apiUrl, formData).then((response) => {
                this.showLoadingOverlay = false;

                if (!forCompanyAddress) {
                    this.countryDefaults = response.data.default;
                    this.resetABNLookupVariables();
                } else {
                    this.countryDefaultsForAddress = response.data.default;
                }
            });
        },

        processSaveFormNotes: function () {
            let text_area_data = this.formNotes.trim(); // Remove unnecessary white spaces

            let formData = new FormData();
            formData.append('company_code', this.companyCode);
            formData.append('company_notes', text_area_data);
            this.$api.post('company/update/main-notes', formData).then((response) => {
                this.loadFormNotes();
                this.loadCompanyDetails();
            });
        },

        loadFormNotes: function () {
            this.formNotes = '';
            if (this.companyCodeIsNotEmpty()) {
                let formData = new FormData();
                formData.append('company_code', this.companyCode);
                formData.append('no_load', true);
                this.$api.post('company/fetch/main-notes', formData).then((response) => {
                    this.formNotes = response.data.form_notes;
                });
            }
        },
    },
    watch: {
        dd_company_list: function () {
            if (this.dd_company_list.length > 0 && this.$refs.companyCodeDropDown && this.companyCode != '') {
                this.$refs.companyCodeDropDown.forceChange(this.companyCode, this.dd_company_list);
            }

            this.companyListLoadingAnimation = false;
        },

        isDebtor: function () {
            this.updateEmailAddressBook();
        },

        isOwner: function () {
            this.updateEmailAddressBook();
        },

        isSupplier: function () {
            this.updateEmailAddressBook();
        },

        companyMatchSelectedGst: function () {
            this.selectCompanyName();
        },

        companyCode: function () {
            if (this.companyCode) {
                const load_temp_data = this.isNewCompany() && this.userIsNotTrustAccountant();
                this.loadCompanyDetails(load_temp_data);
                this.loadFormNotes();
            } else {
                this.companyCode = '';
            }
        },

        newCompanyCode: function () {
            this.newCompanyCode = this.newCompanyCode.toUpperCase().trim();

            if (this.newCompanyCode.length > 10) {
                this.newCompanyCode = this.newCompanyCode.substring(0, 10);
            }
        },

        company_country: function () {
            if (this.isNewCompany() && this.pageAction !== 'review' && this.pageAction !== 'load') {
                this.company_suburb = '';
            }
            this.company_state = '';

            this.fetchStateList();
            this.loadCountryDefaults(true);
        },

        company_suburb: function (newVal, oldVal) {
            if (newVal === undefined && oldVal !== this.searchSuburb) {
                newVal = this.searchSuburb;
                this.company_suburb = newVal;
            }
        },

        company_payment_method: function () {
            let payment_method = parseInt(this.company_payment_method);

            if (this.pageAction !== 'view') {
                this.companyPayMethod = this.company_payment_method;
            }

            this.company_cheque_days = payment_method === PAYMENT_METHODS.CHEQUE ? DEFAULT_CHEQUE_DAYS : 0;
        },

        emailCentralisationSetting: function () {
            if (
                !isEmpty(this.emailCentralisationDataForSaving) &&
                isEqual(this.emailCentralisationSetting, this.emailCentralisationDataForSaving)
            ) {
                return;
            }

            if (!isEmpty(this.emailCentralisationDataForSaving)) {
                this.emailCentralisationSetting = this.emailCentralisationDataForSaving;
                this.emailCentralisationDataForSaving = [];
            }
        },

        windowSize: function () {
            if (this.windowSize.x <= 882) {
                this.mobileElementDisplay = true;
            } else {
                this.mobileElementDisplay = false;
            }
        },
    },
    mixins: [global_mixins],
};
</script>
