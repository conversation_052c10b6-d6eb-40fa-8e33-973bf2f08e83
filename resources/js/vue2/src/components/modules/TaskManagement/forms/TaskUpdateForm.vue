<style>
.task-edit-modal #taskEditModalBody {
    width: 100%;
    transition-property: width;
    transition-timing-function: ease-out;
    transition-duration: 0s;
    transition-delay: 0.1s;
}

.task-edit-modal:hover #taskEditModalBody {
    width: 99.99%;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <v-dialog
            top
            v-model="modalUpdateTaskManagement"
            hide-overlay
            scrollable
            content-class="c8-page"
            width="60%"
        >
            <v-card height="600">
                <v-card-title class="headline">
                    Update Task Management
                    <a
                        href="#"
                        class="dialog-close"
                        @click="showActivityModal(task_id)"
                        ><v-icon>history</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <!-- TASK CATEGORY -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Task Category</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-"
                            >
                                <v-col>{{ task_category_label }}</v-col>
                            </v-col>
                        </v-row>

                        <!-- TYPE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Type</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_type"
                                    :options="type_list"
                                    ref="refType"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_type'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col>{{ task_type_desc }}</v-col>
                            </v-col>
                        </v-row>

                        <!-- STEP -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Step</v-col
                            >
                            <!--              <v-col xs="12" sm="9" md="9" class="form-input" v-if="allow_edit">-->
                            <!--                <cirrus-single-select-->
                            <!--                    v-model="task_step"-->
                            <!--                    :options="steps_list"-->
                            <!--                    ref="refStep"-->
                            <!--                    dense-->
                            <!--                />-->
                            <!--                <v-chip v-if="error_msg.length>0 && errorData.id === 'task_step'"-->
                            <!--                        v-for="(errorData,index) in error_msg" :key="index" outlined color="error">-->
                            <!--                  <v-icon left>error</v-icon>-->
                            <!--                  {{ errorData.message }}-->
                            <!--                </v-chip>-->
                            <!--              </v-col>-->
                            <!--              <v-col xs="12" sm="9" md="9" class="form-input" v-else>-->
                            <!--                <v-col>{{ task_step_desc }}</v-col>-->
                            <!--              </v-col>-->
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-if="step_previous_data.step_id && disable_take_over"
                                        @click="changeStep(step_previous_data)"
                                        data-tooltip="Go to previous step"
                                    >
                                        <v-icon>mdi-step-backward</v-icon>
                                    </v-btn>
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-else
                                        disabled
                                        data-tooltip="Go to previous step"
                                    >
                                        <v-icon>mdi-step-backward</v-icon>
                                    </v-btn>
                                    &nbsp;
                                    {{ task_step_desc }}
                                    &nbsp;
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-if="step_next_data.step_id && disable_take_over"
                                        @click="changeStep(step_next_data)"
                                        data-tooltip="Go to next step"
                                    >
                                        <v-icon>mdi-step-forward</v-icon>
                                    </v-btn>
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-else
                                        disabled
                                        data-tooltip="Go to next step"
                                    >
                                        <v-icon>mdi-step-forward</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>

                        <!-- STATUS -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Status</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_status"
                                    :options="status_list"
                                    ref="refStatus"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_status'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col>{{ task_status_desc }}</v-col>
                            </v-col>
                        </v-row>

                        <!-- BASE ON CATEGORY TASK MANDATORY FIELDS -->
                        <!-- COMPANY -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'CO'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_company"
                                    :options="company_list"
                                    ref="refCompany"
                                    dense
                                />
                                <div style="margin-top: -25; margin-left: 300">
                                    <v-btn
                                        v-if="task_company !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon>mdi-account-card-details-outline</v-icon>
                                    </v-btn>
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="showLookup()"
                                        data-tooltip="Look up"
                                    >
                                        <v-icon>mdi-feature-search-outline</v-icon>
                                    </v-btn>
                                </div>
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_company'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col
                                    >{{ task_company_desc }}
                                    <v-btn
                                        v-if="task_company !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon>mdi-account-card-details-outline</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- PROPERTY -->
                        <v-row
                            class="form-row"
                            v-if="
                                this.task_category_code == 'PR' ||
                                this.task_category_code == 'LE' ||
                                this.task_category_code == 'DI'
                            "
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Property</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_property"
                                    :options="property_list"
                                    @input="updateRelatedList()"
                                    dense
                                />
                                <div style="margin-top: -25; margin-left: 300">
                                    <v-btn
                                        v-if="task_property !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('property')"
                                        data-tooltip="Go to property"
                                    >
                                        <v-icon>business</v-icon>
                                    </v-btn>
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="showLookup()"
                                        data-tooltip="Look up"
                                    >
                                        <v-icon>mdi-feature-search-outline</v-icon>
                                    </v-btn>
                                </div>
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_property'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col
                                    >{{ task_property_desc }}
                                    <v-btn
                                        v-if="task_property !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('property')"
                                        data-tooltip="Go to property"
                                    >
                                        <v-icon>business</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- LEASE -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'LE'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Lease</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_lease"
                                    :options="lease_list"
                                    ref="refLease"
                                    dense
                                />
                                <div style="margin-top: -25; margin-left: 300">
                                    <v-btn
                                        v-if="task_lease !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('lease')"
                                        data-tooltip="Go to lease"
                                    >
                                        <v-icon>mdi-home-floor-l</v-icon>
                                    </v-btn>
                                </div>
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_lease'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col
                                    >{{ task_lease_desc }}
                                    <v-btn
                                        v-if="task_lease !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('lease')"
                                        data-tooltip="Go to lease"
                                    >
                                        <v-icon>mdi-home-floor-l</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- DIARY ITEM -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'DI'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Diary Item</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_diary"
                                    :options="diary_list"
                                    ref="refDiary"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_diary'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col>{{ task_diary_desc }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- END -->
                        <v-card-actions v-if="this.task_category_code && this.task_category_code != 'GE'">
                            <v-spacer></v-spacer>
                            <v-btn
                                v-if="hide_optional_parameters"
                                color="primary"
                                text
                                x-small
                                @click="showOptionalParameters()"
                                >SHOW OPTIONAL PARAMETERS</v-btn
                            >
                            <v-btn
                                v-if="show_optional_parameters"
                                color="primary"
                                text
                                x-small
                                @click="hideOptionalParameters()"
                                >HIDE OPTIONAL PARAMETERS</v-btn
                            >
                        </v-card-actions>
                        <v-divider v-if="this.task_category_code && this.task_category_code != 'GE'"></v-divider>

                        <!-- SHOW OPTIONAL PARAMETERS -->
                        <!-- COMPANY -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_company"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_company"
                                    :options="company_list"
                                    ref="refCompany"
                                    dense
                                />
                                <div style="margin-top: -25; margin-left: 300">
                                    <v-btn
                                        v-if="task_company !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon>mdi-account-card-details-outline</v-icon>
                                    </v-btn>
                                </div>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col
                                    >{{ task_company_desc }}
                                    <v-btn
                                        v-if="task_company !== '' && task_company != null"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon>mdi-account-card-details-outline</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- PROPERTY -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_property"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Property</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_property"
                                    :options="property_list"
                                    @input="updateRelatedList()"
                                    dense
                                />
                                <div style="margin-top: -25; margin-left: 300">
                                    <v-btn
                                        v-if="task_property !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('property')"
                                        data-tooltip="Go to property"
                                    >
                                        <v-icon>business</v-icon>
                                    </v-btn>
                                </div>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col
                                    >{{ task_property_desc }}
                                    <v-btn
                                        v-if="task_property !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('property')"
                                        data-tooltip="Go to property"
                                    >
                                        <v-icon>business</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- LEASE -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_lease"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Lease</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_lease"
                                    :options="lease_list"
                                    ref="refLease"
                                    dense
                                />
                                <div style="margin-top: -25; margin-left: 300">
                                    <v-btn
                                        v-if="task_lease !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('lease')"
                                        data-tooltip="Go to lease"
                                    >
                                        <v-icon>mdi-home-floor-l</v-icon>
                                    </v-btn>
                                </div>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col
                                    >{{ task_lease_desc }}
                                    <v-btn
                                        v-if="task_lease !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('lease')"
                                        data-tooltip="Go to lease"
                                    >
                                        <v-icon>mdi-home-floor-l</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- DIARY ITEM -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_diary"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Diary Item</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_diary"
                                    :options="diary_list"
                                    ref="refDiary"
                                    dense
                                />
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col>{{ task_diary_desc }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- END -->

                        <!-- SUBJECT -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Subject</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <v-text-field
                                    v-model="task_subject"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_subject'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col>{{ task_subject }}</v-col>
                            </v-col>
                        </v-row>

                        <!-- DESCRIPTION -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Description</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col><span v-html="task_description"></span></v-col>
                            </v-col>
                        </v-row>

                        <!-- ASSIGNEE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Assignee</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="allow_edit"
                            >
                                <cirrus-single-select
                                    v-model="task_assignee"
                                    :options="assignee_list"
                                    ref="refAssignee"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_assignee'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col
                                    >{{ task_assignee_name }}
                                    <v-btn
                                        v-if="!disable_take_over"
                                        elevation="0"
                                        icon
                                        x-small
                                        @click="takeOverTask()"
                                        data-tooltip="Take over"
                                    >
                                        <v-icon>mdi-account-convert</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>

                        <!-- ATTACHMENTS -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Attachments</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <div v-if="isRemove">
                                    <v-btn
                                        text
                                        color="primary"
                                        v-if="task_filename"
                                        @click="downloadFile(task_filename_url)"
                                        >{{ task_filename }}
                                        <v-icon
                                            center
                                            color="primary"
                                        >
                                            attach_file
                                        </v-icon>
                                    </v-btn>
                                    <v-btn
                                        text
                                        @click="removeFile"
                                    >
                                        <v-icon
                                            center
                                            color="error"
                                        >
                                            close
                                        </v-icon>
                                    </v-btn>
                                </div>
                                <div v-else>
                                    <v-btn
                                        text
                                        small
                                        :loading="isSelecting"
                                        @click="onButtonClick"
                                    >
                                        <v-icon color="primary"> attach_file </v-icon>
                                    </v-btn>
                                    <input
                                        ref="uploader"
                                        class="d-none"
                                        type="file"
                                        @change="onFileChanged"
                                    />
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-divider></v-divider>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="updateTask()"
                        >Update</v-btn
                    >
                    <v-btn
                        color="primary"
                        text
                        @click="modalUpdateTaskManagement = false"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- ACTIVITY LOG -->
        <v-dialog
            v-model="dialogActivity"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Task Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogActivity = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <task-activity-log-component
                        v-if="dialogActivity"
                        :task_id="activity_task_id"
                    ></task-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogActivity = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--  LOOK UP BY PARAMETER  -->
        <v-dialog
            v-model="dialogLookUp"
            max-width="90%"
            heigth="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Search Options
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogLookUp = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div>
                        <v-row>
                            <v-col
                                cols="12"
                                md="3"
                            >
                                <v-list
                                    flat
                                    dense
                                >
                                    <v-subheader><h5>Filter By</h5></v-subheader>
                                    <v-list-item-group
                                        v-model="settings"
                                        multiple
                                        active-class=""
                                    >
                                        <v-list-item>
                                            <template v-slot:default="{ active }">
                                                <v-list-item-action>
                                                    <v-checkbox
                                                        v-model="checkPropertyManager"
                                                        :input-value="active"
                                                    ></v-checkbox>
                                                </v-list-item-action>
                                                <v-list-item-content>
                                                    <v-list-item-title>{{ property_manager_label }}</v-list-item-title>
                                                    <v-list-item-subtitle
                                                        >Choose {{ property_manager_label }} to
                                                        filter</v-list-item-subtitle
                                                    >
                                                </v-list-item-content>
                                            </template>
                                        </v-list-item>

                                        <v-list-item>
                                            <template v-slot:default="{ active }">
                                                <v-list-item-action>
                                                    <v-checkbox
                                                        v-model="checkOwner"
                                                        :input-value="active"
                                                    ></v-checkbox>
                                                </v-list-item-action>
                                                <v-list-item-content>
                                                    <v-list-item-title>Owner</v-list-item-title>
                                                    <v-list-item-subtitle>Choose owner to filter</v-list-item-subtitle>
                                                </v-list-item-content>
                                            </template>
                                        </v-list-item>
                                        <v-list-item>
                                            <template v-slot:default="{ active }">
                                                <v-list-item-action>
                                                    <v-checkbox
                                                        v-model="checkLease"
                                                        :input-value="active"
                                                    ></v-checkbox>
                                                </v-list-item-action>
                                                <v-list-item-content>
                                                    <v-list-item-title>Lease</v-list-item-title>
                                                    <v-list-item-subtitle>Choose lease to filter</v-list-item-subtitle>
                                                </v-list-item-content>
                                            </template>
                                        </v-list-item>
                                        <v-list-item>
                                            <template v-slot:default="{ active }">
                                                <v-list-item-action>
                                                    <v-checkbox
                                                        v-model="checkAddress"
                                                        :input-value="active"
                                                    ></v-checkbox>
                                                </v-list-item-action>
                                                <v-list-item-content>
                                                    <v-list-item-title>Address</v-list-item-title>
                                                    <v-list-item-subtitle
                                                        >Entered search text will base on the property
                                                        address</v-list-item-subtitle
                                                    >
                                                </v-list-item-content>
                                            </template>
                                        </v-list-item>
                                    </v-list-item-group>
                                </v-list>
                                <v-divider
                                    class="vertical-divider"
                                    vertical
                                ></v-divider>
                            </v-col>
                            <v-col
                                cols="12"
                                md="9"
                            >
                                <v-row v-if="showPropertyManager && checkPropertyManager">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        >{{ property_manager_label }}</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="6"
                                        md="6"
                                        class="form-input"
                                    >
                                        <cirrus-single-select
                                            v-model="modelPropertyManager"
                                            :options="propertyManagerList"
                                            @input="changePropertyManager()"
                                            dense
                                        ></cirrus-single-select>
                                    </v-col>
                                </v-row>
                                <v-row v-if="showOwner && checkOwner">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        >Owner</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="6"
                                        md="6"
                                        class="form-input"
                                    >
                                        <cirrus-single-select
                                            v-model="modelOwner"
                                            :options="ownerList"
                                            @input="changePropertyOwner()"
                                            dense
                                        ></cirrus-single-select>
                                    </v-col>
                                </v-row>

                                <v-row v-if="showLease && checkLease">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        >Lease</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="6"
                                        md="6"
                                        class="form-input"
                                    >
                                        <cirrus-single-select
                                            v-model="modelLease"
                                            :options="lease_list"
                                            @input="changeLease()"
                                            dense
                                        ></cirrus-single-select>
                                    </v-col>
                                </v-row>

                                <div class="c8-page-table">
                                    <table>
                                        <tbody>
                                            <tr class="form-row">
                                                <td>
                                                    <v-text-field
                                                        v-model="searchTask"
                                                        placeholder="Search..."
                                                        dense
                                                    />
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <v-data-table
                                        :headers="searchHeaders"
                                        :items="searchItems"
                                        :search="searchTask"
                                        @click:row="selectedSearch"
                                        dense
                                    >
                                    </v-data-table>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <br /><br />
                <br /><br />
                <br /><br />
                <br /><br />
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogLookUp = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

import Vue from 'vue';

export default {
    props: {
        visible: false,
        task_id: 0,
        task_category: '',
    },
    data() {
        return {
            property_manager_label: 'Property Manager',
            loading: true,
            error_msg: [],

            editor: ClassicEditor,
            editorConfig: {
                toolbar: {
                    items: [
                        'bold',
                        'italic',
                        '|',
                        'bulletedList',
                        'numberedList',
                        '|',
                        'indent',
                        'outdent',
                        '|',
                        'undo',
                        'redo',
                    ],
                },
                language: 'en',
            },

            type_list: [],
            steps_list: [],
            status_list: [],
            lease_list: [],
            company_list: [],
            property_list: [],
            assignee_list: [],

            task_category_code: '',
            task_category_label: '',
            task_type: '',
            task_type_desc: '',
            task_step: '',
            task_step_desc: '',
            task_status: '',
            task_status_desc: '',
            task_company: '',
            task_company_desc: '',
            task_property: '',
            task_property_desc: '',
            task_lease: '',
            task_lease_desc: '',
            task_diary: '',
            task_diary_desc: '',
            task_subject: '',
            task_description: '',
            task_assignee: '',
            task_assignee_name: '',
            task_filename: '',
            task_filename_url: '',
            old_task_type: '',
            old_task_step: '',
            old_task_status: '',
            old_task_company: '',
            old_task_property: '',
            old_task_lease: '',
            old_task_diary: '',
            old_task_subject: '',
            old_task_description: '',
            old_task_assignee: '',
            old_task_filename: '',

            //OPTIONAL PARAMETERS
            optional_parameters: 'show optional parameters',
            show_optional_parameters: false,
            hide_optional_parameters: true,
            show_optional_company: false,
            show_optional_property: false,
            show_optional_lease: false,
            show_optional_diary: false,

            isSelecting: false,
            selectedFile: '',
            file_name: '',
            file_attached: '',
            isRemove: false,

            dialogActivity: false,
            activities: [],
            activity_task_id: 0,

            allow_edit: true,
            disable_take_over: false,

            step_previous_data: [],
            step_next_data: [],

            dialogLookUp: false,
            checkPropertyManager: '',
            checkOwner: '',
            checkLease: '',
            checkAddress: '',

            showPropertyManager: false,
            propertyManagerList: [],
            modelPropertyManager: '',

            showOwner: false,
            ownerList: [],
            modelOwner: '',

            showLease: false,
            leaseList: [],
            modelLease: '',

            searchTask: '',
            defaultSearchHeaders: [
                { text: 'Property', value: 'property_name', sortable: false },
                { text: 'Lease', value: 'lease_name', sortable: false },
                { text: 'Company', value: 'company_name', sortable: false },
                // {text: 'Address', value: 'property_address', sortable: false, selected: false },
            ],
            searchHeaders: [
                { text: 'Property', value: 'property_name', sortable: false },
                { text: 'Lease', value: 'lease_name', sortable: false },
                { text: 'Company', value: 'company_name', sortable: false },
                // {text: 'Address', value: 'property_address', sortable: false, selected: false },
            ],
            searchItems: [],
        };
    },
    mounted() {
        this.fetchAssigneeList();
        this.fetchPropertyList();
        this.fetchCompanyList();
        this.fetchLeaseList();
        this.fetchDiaryList();
        this.loadCountryDefaults();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
        modalUpdateTaskManagement: {
            get() {
                if (this.visible) {
                    this.loadTaskOptions();
                }
                return this.visible;
            },
            set(value) {
                if (!value) {
                    this.$emit('close');
                }
            },
        },
    },
    methods: {
        ...mapActions([]),
        ...mapMutations([]),
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },

        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.property_manager_label = this.ucwords(this.country_defaults.property_manager);
            });
        },
        // For Company
        fetchPropertyList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('active_only', 1);
            let apiUrlProperty = 'loadAPIPropertyDropDownList';
            axios.post(apiUrlProperty, formData).then((response) => {
                this.property_list = response.data.data;
            });
        },
        // For Company
        fetchCompanyList: function () {
            var formData = new FormData();
            formData.append('show_active', 1);
            let apiUrlCompany = 'company-dropdown-list';
            axios.post(apiUrlCompany, formData).then((response) => {
                this.company_list = response.data;
            });
        },
        // For Lease
        fetchLeaseList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('propertyID', this.task_property);
            let apiUrlLease = this.cirrus8_api_url + 'api/task-management/get-lease-option';
            axios.post(apiUrlLease, formData).then((response) => {
                this.lease_list = response.data;
            });
        },
        // For Diary
        fetchDiaryList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('propertyID', this.task_property);
            let apiUrlDiary = this.cirrus8_api_url + 'api/ui/fetch/param-diary-property-list';
            axios.post(apiUrlDiary, formData).then((response) => {
                this.diary_list = response.data;
            });
        },
        // For Assignee
        fetchAssigneeList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            let apiUrlAssignee = this.cirrus8_api_url + 'api/task-management/get-assignee';
            axios.post(apiUrlAssignee, formData).then((response) => {
                this.assignee_list = response.data;
            });
        },
        // For Property Manager
        fetchPropertyManagerList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            let apiUrlProperty = this.cirrus8_api_url + 'api/loadAPIPortfolioManagersList';
            axios.post(apiUrlProperty, formData).then((response) => {
                this.propertyManagerList = response.data.data;
            });
        },
        // For Owner
        fetchOwnerList: function () {
            var formData = new FormData();
            formData.append('show_active', 1);
            let apiUrlCompany = 'company-dropdown-list';
            axios.post(apiUrlCompany, formData).then((response) => {
                this.ownerList = response.data;
            });
        },
        // For Search Iterms
        fetchSearchItems: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            if (this.modelPropertyManager) {
                formData.append('property_manager', this.modelPropertyManager);
            }
            if (this.modelOwner) {
                formData.append('property_owner', this.modelOwner);
            }
            if (this.modelLease) {
                formData.append('lease_code', this.modelLease);
            }
            let apiUrlSearch = this.cirrus8_api_url + 'api/task-management/search-list';
            axios.post(apiUrlSearch, formData).then((response) => {
                this.searchItems = response.data;
            });
        },

        updateRelatedList: function () {
            if (this.task_category == 'CO') {
                this.fetchPropertyList();
            } else {
                this.fetchCompanyList();
                this.fetchLeaseList();
                this.fetchDiaryList();
            }
        },

        // OPTIONAL PARAMETERS
        showOptionalParameters: function () {
            this.show_optional_parameters = true;
            this.hide_optional_parameters = false;

            switch (this.task_category_code) {
                case 'CO':
                    this.show_optional_company = false;
                    this.show_optional_property = true;
                    this.show_optional_lease = true;
                    this.show_optional_diary = true;
                    break;
                case 'PR':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = true;
                    this.show_optional_diary = true;
                    break;
                case 'LE':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = false;
                    this.show_optional_diary = true;
                    break;
                case 'DI':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = true;
                    this.show_optional_diary = false;
                    break;
            }
        },
        hideOptionalParameters: function () {
            this.show_optional_parameters = false;
            this.hide_optional_parameters = true;
            this.show_optional_company = false;
            this.show_optional_property = false;
            this.show_optional_lease = false;
            this.show_optional_diary = false;
        },
        loadTaskOptions: function () {
            this.updateRelatedList();
            this.loadTaskDetails();
        },
        loadTaskDetails: function () {
            if (this.task_id != 0) {
                var formData = new FormData();
                formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);
                formData.append('task_id', this.task_id);
                let apiUrlLease = this.cirrus8_api_url + 'api/task-management/get-task';
                document.getElementById('ngLoader-UI').style.display = 'block';
                axios.post(apiUrlLease, formData).then((response) => {
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    this.allow_edit = response.data.allow_edit;
                    this.disable_take_over = response.data.allow_edit;
                    this.step_previous_data = response.data.previous_step;
                    this.step_next_data = response.data.next_step;

                    this.type_list = response.data.type_list;
                    this.steps_list = response.data.steps_list;
                    this.status_list = response.data.status_list;
                    this.diary_list = response.data.diary_list;
                    //
                    let task_detail = response.data.task_detail;
                    this.task_type_desc = task_detail.task_type_desc;
                    this.task_step_desc = task_detail.task_step_desc;
                    this.task_status_desc = task_detail.task_status_desc;
                    this.task_company_desc = task_detail.task_company_label;
                    this.task_property_desc = task_detail.task_property_label;
                    this.task_lease_desc = task_detail.task_lease_label;
                    this.task_diary_desc = task_detail.task_diary;
                    this.task_assignee_name = task_detail.task_assignee_name;
                    //
                    this.task_type = task_detail.task_type;
                    this.task_status = task_detail.task_status;
                    this.task_step = task_detail.task_step;
                    this.task_category_code = task_detail.task_category_code;
                    this.task_category_label = task_detail.task_category_label;
                    this.task_subject = task_detail.task_subject;
                    if (task_detail.task_description != null) this.task_description = task_detail.task_description;
                    this.task_assignee = task_detail.task_assignee;
                    this.task_company = task_detail.task_company;
                    // this.task_property = task_detail.task_property;
                    this.task_lease = task_detail.task_lease;
                    this.task_diary = task_detail.task_diary;
                    if (task_detail.task_filename) {
                        this.task_filename_url = task_detail.task_filename_url;
                        this.task_filename = task_detail.task_filename;
                        this.file_name = task_detail.task_filename;
                        this.isRemove = true;
                    } else {
                        this.removeFile();
                    }

                    this.old_task_type = task_detail.task_type;
                    this.old_task_step = task_detail.task_step;
                    this.old_task_status = task_detail.task_status;
                    this.old_task_company = task_detail.task_company;
                    this.old_task_property = task_detail.task_property;
                    this.old_task_lease = task_detail.task_lease;
                    this.old_task_diary = task_detail.task_diary;
                    this.old_task_subject = task_detail.task_subject;
                    this.old_task_description = task_detail.task_description;
                    this.old_task_assignee = task_detail.task_assignee;
                    this.old_task_filename = task_detail.task_filename;
                    this.hideOptionalParameters();
                });
            }
        },

        updateTask: function () {
            let validate = true;

            if (this.task_category_code == 'CO') {
                if (this.task_company == '') {
                    this.$noty.error('You have not specified a company.');
                    validate = false;
                }
            }
            if (this.task_category_code == 'PR') {
                if (this.task_property == '') {
                    this.$noty.error('You have not specified a property.');
                    validate = false;
                }
            }
            if (this.task_category_code == 'LE') {
                if (this.task_lease == '') {
                    this.$noty.error('You have not specified a lease.');
                    validate = false;
                }
            }
            if (this.task_category_code == 'DI') {
                if (this.task_diary == '') {
                    this.$noty.error('You have not specified a diary item.');
                    validate = false;
                }
            }

            if (this.task_type == '' || this.task_type == null) {
                this.$noty.error('You have not specified a type.');
                validate = false;
            }

            if (this.task_step == '' || this.task_step == null) {
                this.$noty.error('You have not specified a step.');
                validate = false;
            }

            if (this.task_subject == '') {
                this.$noty.error('You have not specified a subject.');
                validate = false;
            }

            if (this.task_assignee == '' || this.task_assignee == null) {
                this.$noty.error('You have not specified an assignee.');
                validate = false;
            }
            // submit form if no error
            if (validate) {
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);

                formData.append('form_mode', 'update');
                formData.append('task_row_id', this.task_id);
                formData.append('task_code', this.task_category_code);
                formData.append('task_type', this.task_type);
                formData.append('task_step', this.task_step);
                formData.append('task_status', this.task_status);
                formData.append('task_subject', this.task_subject);
                formData.append('task_description', this.task_description);
                formData.append('task_assignee', this.task_assignee);
                formData.append('task_company', this.task_company);
                formData.append('task_property', this.task_property);
                formData.append('task_lease', this.task_lease);
                formData.append('task_diary', this.task_diary);
                formData.append('file_attached', this.file_attached);
                // changes on attachment
                if (this.task_filename != this.file_name) {
                    formData.append('task_filename', this.file_name);
                } else {
                    formData.append('task_filename', this.task_filename);
                }

                formData.append('old_task_type', this.old_task_type);
                formData.append('old_task_step', this.old_task_step);
                formData.append('old_task_status', this.old_task_status);
                formData.append('old_task_company', this.old_task_company);
                formData.append('old_task_property', this.old_task_property);
                formData.append('old_task_lease', this.old_task_lease);
                formData.append('old_task_diary', this.old_task_diary);
                formData.append('old_task_subject', this.old_task_subject);
                formData.append('old_task_description', this.old_task_description);
                formData.append('old_task_assignee', this.old_task_assignee);
                formData.append('old_task_filename', this.old_task_filename);

                // UPdate Task details
                let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
                document.getElementById('ngLoader-UI').style.display = 'block';
                axios.post(apiUrl, formData).then((response) => {
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        // this.removeFile();
                        // this.loadTaskList();
                        this.modalUpdateTaskManagement = false;
                        this.$noty.success('Task updated.');
                        this.loading_setting = false;
                        this.$emit('reload-list');
                        this.$emit('close');
                    } else {
                        this.$noty.error('error message');
                        this.loading_setting = false;
                    }
                });
            }
        },

        onButtonClick: function () {
            this.isSelecting = true;
            window.addEventListener(
                'focus',
                () => {
                    this.isSelecting = false;
                },
                { once: true },
            );

            this.$refs.uploader.click();
        },
        onFileChanged: function (e) {
            var files = e.target.files || e.dataTransfer.files;
            if (files.length && files[0].type == 'application/pdf') {
                this.file_attached = files[0];
                this.file_name = files[0].name;
                this.isRemove = true;
            } else {
                this.$noty.error('Invalid file type. Please upload only pdf file.');
            }
        },
        removeFile: function (item) {
            this.file_name = '';
            this.isRemove = false;
            this.file_attached = '';
        },

        downloadFile: function (encoded_url) {
            document.location.href = 'download.php?fileID=' + encoded_url;
        },

        showActivityModal: function (task_id) {
            this.activity_task_id = task_id;
            this.dialogActivity = true;
        },
        goToShortcut: function (parameter) {
            let property_code = '';
            let lease_code = '';
            let company_code = '';
            switch (parameter) {
                case 'property':
                    property_code = this.task_property;
                    window.open(
                        '?module=properties&command=v2_manage_property_page&property_code=' + property_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'lease':
                    lease_code = this.task_lease;
                    property_code = this.task_property;
                    window.open(
                        '?module=leases&command=home&propertyID=' + property_code + '&leaseID=' + lease_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'company':
                    company_code = this.task_company;
                    window.open(
                        '?module=companies&command=company&companyID=' + company_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
            }
        },

        takeOverTask: function () {
            var formData = new FormData();
            formData.append('form_mode', 'take_over');
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('task_id', this.task_id);
            formData.append('old_task_assignee', this.old_task_assignee);
            // UPDATE STEPS STATUS AND ASSIGNEE
            document.getElementById('ngLoader-UI').style.display = 'block';
            let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
            axios.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                document.getElementById('ngLoader-UI').style.display = 'none';
                if (this.status == 'success') {
                    this.modalCommentTaskManagement = false;
                    this.loadTaskList();
                    this.loading_setting = false;
                } else {
                    this.$noty.error('error message');
                    this.loading_setting = false;
                }
            });
        },

        changeStep: function (stepData) {
            var formData = new FormData();
            formData.append('form_mode', 'add_comment');
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('step_id', stepData.step_id);
            formData.append('step_assignee', stepData.step_assignee);
            formData.append('task_assignee_email', stepData.task_assignee_email);
            formData.append('task_id', this.task_id);
            formData.append('old_task_step', this.old_task_step);
            formData.append('old_task_assignee', this.old_task_assignee);
            formData.append('task_subject', this.task_subject);
            formData.append('task_description', this.task_description);

            // CHANGE TASK STEP
            document.getElementById('ngLoader-UI').style.display = 'block';
            let apiUrl = this.cirrus8_api_url + 'api/task-management/change-step';
            axios.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                document.getElementById('ngLoader-UI').style.display = 'none';
                if (this.status == 'success') {
                    this.modalCommentTaskManagement = false;
                    this.$noty.success('Step successfully updated.');
                    this.loadTaskList();
                    this.loading_setting = false;
                } else {
                    this.$noty.error('error message');
                    this.loading_setting = false;
                }
            });
        },

        showLookup: function () {
            this.fetchSearchItems();
            this.dialogLookUp = true;
        },
        getHeaderIndex: function (value) {
            let arr = this.searchHeaders;
            let indexValue = 0;
            for (var i = 0; i < arr.length; i++) {
                if (arr[i]['value'] == value) {
                    indexValue = i;
                    break;
                }
            }
            return indexValue;
        },
        selectedSearch: function (row) {
            this.populateDropdownOptions(row);
            this.dialogLookUp = false;
        },

        populateDropdownOptions: function (row) {
            this.task_lease = row.lease_code;
            this.task_company = row.company_code;
            this.task_property = row.property_code;
            this.updateRelatedList();
        },

        changePropertyManager: function () {
            this.fetchSearchItems();
        },
        changePropertyOwner: function () {
            this.fetchSearchItems();
        },
        changeLease: function () {
            this.fetchSearchItems();
        },
    },
    watch: {
        checkPropertyManager: function () {
            if (this.checkPropertyManager) {
                this.fetchPropertyManagerList();
                this.showPropertyManager = true;
                // this.searchHeaders = this.searchHeaders.concat({text: 'Property Manager', value: 'property_manager', sortable: false, selected: false });
            } else {
                // this.$delete(this.searchHeaders, this.getHeaderIndex('property_manager'))
                this.showPropertyManager = false;
            }
        },
        checkOwner: function () {
            if (this.checkOwner) {
                this.fetchOwnerList();
                // this.searchHeaders = this.searchHeaders.concat({text: 'Owner', value: 'property_owner', sortable: false, selected: false });
                this.showOwner = true;
            } else {
                // this.$delete(this.searchHeaders, this.getHeaderIndex('property_owner'));
                this.showOwner = false;
            }
        },
        checkLease: function () {
            if (this.checkLease) {
                this.fetchLeaseList();
                // this.searchHeaders = this.searchHeaders.concat({text: 'Owner', value: 'property_owner', sortable: false, selected: false });
                this.showLease = true;
            } else {
                // this.$delete(this.searchHeaders, this.getHeaderIndex('property_owner'));
                this.showLease = false;
            }
        },
        checkAddress: function () {
            if (this.checkAddress) {
                this.searchHeaders = this.searchHeaders.concat({
                    text: 'Address',
                    value: 'property_address',
                    sortable: false,
                    selected: false,
                });
            } else {
                this.$delete(this.searchHeaders, this.getHeaderIndex('property_address'));
            }
        },
    },
    mixins: [global_mixins],
};
</script>

<style scoped></style>
