<template>
    <div>
        <v-skeleton-loader
            class="mx-auto"
            type="table"
            :loading="loading"
        >
            <v-card elevation="0">
                <v-card-title>
                    <v-spacer></v-spacer>
                    <v-text-field
                        v-model="search"
                        append-icon="search"
                        label="Search"
                        single-line
                        hide-details
                    ></v-text-field>
                </v-card-title>
                <v-data-table
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="task_activity_log_list"
                    :search="search"
                >
                    <template v-slot:item.index="{ item }">
                        {{ task_activity_log_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.task_log_old_value="{ item }">
                        <div v-if="item.task_log_field_name == 'Attachment'">
                            <v-btn
                                left
                                text
                                x-small
                                color="primary"
                                v-if="item.task_log_old_value"
                                @click="downloadFile(item.task_filename_url_old)"
                            >
                                {{ item.file_name_old }}
                                <v-icon
                                    center
                                    color="primary"
                                    x-small
                                >
                                    attach_file
                                </v-icon>
                            </v-btn>
                        </div>
                        <div v-else>
                            <span
                                v-if="item.task_log_old_value_x"
                                class="d-inline-block text-truncate"
                                style="max-width: 150px"
                                v-html="item.task_log_old_value"
                                @click="showCompleteText(item, 'old')"
                            >
                            </span>
                            <span
                                v-else
                                v-html="item.task_log_old_value"
                            ></span>
                        </div>
                    </template>
                    <template v-slot:item.task_log_new_value="{ item }">
                        <div v-if="item.task_log_field_name == 'Attachment'">
                            <v-btn
                                left
                                text
                                x-small
                                color="primary"
                                v-if="item.task_log_new_value"
                                @click="downloadFile(item.task_filename_url)"
                            >
                                {{ item.file_name }}
                                <v-icon
                                    center
                                    color="primary"
                                    x-small
                                >
                                    attach_file
                                </v-icon>
                            </v-btn>
                        </div>
                        <div v-else>
                            <span
                                v-if="item.task_log_new_value_x"
                                class="d-inline-block text-truncate"
                                style="max-width: 150px"
                                v-html="item.task_log_new_value"
                                @click="showCompleteText(item, 'new')"
                            >
                            </span>
                            <span
                                v-else
                                v-html="item.task_log_new_value"
                            ></span>
                        </div>
                    </template>
                </v-data-table>
            </v-card>
        </v-skeleton-loader>

        <!-- SHOW COMPLETE TEXT -->
        <v-dialog
            v-model="dialogShowCompleteText"
            max-width="500px"
        >
            <v-card>
                <v-card-title class="headline">
                    {{ field_name }}
                </v-card-title>
                <v-card-text><span v-html="complete_text"></span></v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="dialogShowCompleteText = false"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF SHOW COMPLETE TEXT -->
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
export default {
    props: {
        task_id: { type: String, default: '' },
    },
    data() {
        return {
            loading: true,
            search: '',
            headers: [
                { text: '#', value: 'index', sortable: false },
                { text: 'Field Name', value: 'task_log_field_name', sortable: false, width: '150' },
                { text: 'Old Value', value: 'task_log_old_value', sortable: false, width: '190' },
                { text: 'New Value', value: 'task_log_new_value', sortable: false, width: '190' },
                { text: 'User', value: 'task_log_by', sortable: false, width: '100' },
                { text: 'Date', value: 'task_log_date', sortable: false, width: '160' },
                { text: 'Task Type', value: 'task_log_type', sortable: false, width: '100' },
                { text: 'Status', value: 'task_log_status', sortable: false, width: '100' },
            ],
            task_activity_log_list: [],

            dialogShowCompleteText: false,
            field_name: '',
            complete_text: '',
        };
    },
    mounted() {
        this.loadTaskActivityLogs();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    methods: {
        loadTaskActivityLogs: function () {
            if (this.task_id !== '') {
                this.loading = true;
                var form_data = new FormData();
                form_data.append('un', this.username);
                form_data.append('current_db', this.current_db);
                form_data.append('user_type', this.user_type);
                form_data.append('task_id', this.task_id);
                form_data.append('no_load', true);
                axios
                    .post(this.cirrus8_api_url + 'api/task-management/get-activity-log', form_data)
                    .then((response) => {
                        this.task_activity_log_list = response.data;
                        this.loading = false;
                    });
            }
        },
        showCompleteText: function (data, type) {
            if (type == 'new') {
                this.complete_text = data.task_log_new_value_x;
            } else {
                this.complete_text = data.task_log_old_value_x;
            }
            this.dialogShowCompleteText = true;
            this.field_name = data.task_log_field_name;
        },
        downloadFile: function (encoded_url) {
            document.location.href = 'download.php?fileID=' + encoded_url;
        },
    },
    created() {
        bus.$on('loadTaskActivityLogs', (data) => {
            this.loadActivityLogs();
        });
    },
    mixins: [global_mixins],
};
</script>

<style scoped></style>
