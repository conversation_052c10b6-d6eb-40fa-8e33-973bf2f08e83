<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <!-- COMMENT TASK MANAGEMENT -->
        <v-dialog
            top
            v-model="modalCommentTaskManagement"
            hide-overlay
            scrollable
            content-class="c8-page"
            width="600"
        >
            <v-card height="600">
                <v-card-title class="headline">
                    {{ modal_title }}
                    <a
                        href="#"
                        class="dialog-close"
                        @click="showActivityModal(task_id)"
                    >
                        <v-icon>history</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <!-- TYPE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Type
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_type_desc }}</v-col>
                            </v-col>
                        </v-row>

                        <!-- STEP -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Step
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-if="step_previous_data.step_id && disable_take_over"
                                        @click="changeStep(step_previous_data)"
                                        data-tooltip="Go to previous step"
                                    >
                                        <v-icon>mdi-step-backward</v-icon>
                                    </v-btn>
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-else
                                        disabled
                                        data-tooltip="Go to previous step"
                                    >
                                        <v-icon>mdi-step-backward</v-icon>
                                    </v-btn>
                                    &nbsp;
                                    {{ task_step_desc }}
                                    &nbsp;
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-if="step_next_data.step_id && disable_take_over"
                                        @click="changeStep(step_next_data)"
                                        data-tooltip="Go to next step"
                                    >
                                        <v-icon>mdi-step-forward</v-icon>
                                    </v-btn>
                                    <v-btn
                                        elevation="0"
                                        icon
                                        x-small
                                        v-else
                                        disabled
                                        data-tooltip="Go to next step"
                                    >
                                        <v-icon>mdi-step-forward</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>

                        <!-- STATUS -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Status
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-if="!showAddComment"
                            >
                                <cirrus-single-select
                                    v-model="task_status"
                                    :options="status_list"
                                    ref="refStatus"
                                    dense
                                />
                                <v-chip
                                    v-for="(errorData, index) in taskStatusErrors"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                v-else
                            >
                                <v-col>{{ task_status_desc }}</v-col>
                            </v-col>
                        </v-row>

                        <!-- BASE ON CATEGORY TASK MANDATORY FIELDS -->
                        <!-- COMPANY -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'CO'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Company
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    >{{ task_company_label }}
                                    <v-btn
                                        v-if="task_company !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon>mdi-account-card-details-outline</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- PROPERTY -->
                        <v-row
                            class="form-row"
                            v-if="
                                this.task_category_code == 'PR' ||
                                this.task_category_code == 'LE' ||
                                this.task_category_code == 'DI'
                            "
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Property
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    >{{ task_property_label }}
                                    <v-btn
                                        v-if="task_property !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('property')"
                                        data-tooltip="Go to property"
                                    >
                                        <v-icon>business</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- LEASE -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'LE'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Lease
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    >{{ task_lease_label }}
                                    <v-btn
                                        v-if="task_lease !== ''"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('lease')"
                                        data-tooltip="Go to lease"
                                    >
                                        <v-icon>mdi-home-floor-l</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- DIARY ITEM -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'DI'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Diary Item
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_diary }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- END -->
                        <v-card-actions v-if="this.task_category_code && this.task_category_code != 'GE'">
                            <v-spacer></v-spacer>
                            <v-btn
                                v-if="hide_optional_parameters"
                                color="primary"
                                text
                                x-small
                                @click="showOptionalParameters()"
                                >SHOW OPTIONAL PARAMETERS
                            </v-btn>
                            <v-btn
                                v-if="show_optional_parameters"
                                color="primary"
                                text
                                x-small
                                @click="hideOptionalParameters()"
                                >HIDE OPTIONAL PARAMETERS
                            </v-btn>
                        </v-card-actions>
                        <v-divider v-if="this.task_category_code && this.task_category_code != 'GE'"></v-divider>

                        <!-- SHOW OPTIONAL PARAMETERS -->
                        <!-- COMPANY -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_company"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Company
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    >{{ task_company_label }}
                                    <v-btn
                                        v-if="task_company != '' && task_company != null"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon>mdi-account-card-details-outline</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- PROPERTY -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_property"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Property
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    >{{ task_property_label }}
                                    <v-btn
                                        v-if="task_property != '' && task_property != null"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('property')"
                                        data-tooltip="Go to property"
                                    >
                                        <v-icon>business</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- LEASE -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_lease"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Lease
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    >{{ task_lease_label }}
                                    <v-btn
                                        v-if="task_lease != '' && task_lease != null"
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('lease')"
                                        data-tooltip="Go to lease"
                                    >
                                        <v-icon>mdi-home-floor-l</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>
                        <!-- DIARY ITEM -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_diary"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Diary Item
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_diary_label }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- END -->

                        <!-- DESCRIPTION -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Description
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col><span v-html="task_description"></span></v-col>
                            </v-col>
                        </v-row>

                        <!-- ASSIGNEE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Assignee
                            </v-col>
                            <!--              <v-col xs="12" sm="9" md="9" class="form-input" v-if="!showAddComment">-->
                            <!--                <cirrus-single-select-->
                            <!--                    v-model="task_assignee"-->
                            <!--                    :options="assignee_list"-->
                            <!--                    ref="refAssignee"-->
                            <!--                    dense-->
                            <!--                />-->
                            <!--                <v-chip v-if="error_msg.length>0 && errorData.id === 'task_assignee'"-->
                            <!--                        v-for="(errorData,index) in error_msg" :key="index" outlined color="error">-->
                            <!--                  <v-icon left>error</v-icon>-->
                            <!--                  {{ errorData.message }}-->
                            <!--                </v-chip>-->
                            <!--              </v-col>-->
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col
                                    >{{ task_assignee_name }}
                                    <v-btn
                                        v-if="!disable_take_over"
                                        elevation="0"
                                        icon
                                        x-small
                                        @click="takeOverTask()"
                                        data-tooltip="Take over"
                                    >
                                        <v-icon>mdi-account-convert</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-col>
                        </v-row>

                        <!-- ATTACHMENTS -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Attachments
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-btn
                                    text
                                    color="primary"
                                    v-if="task_filename"
                                    @click="downloadFile(task_filename_url)"
                                    >{{ task_filename }}
                                    <v-icon
                                        center
                                        color="primary"
                                    >
                                        attach_file
                                    </v-icon>
                                </v-btn>
                            </v-col>
                        </v-row>
                    </div>
                    <v-divider></v-divider>
                    <v-card-actions>
                        <h3 class="title font-weight-black">COMMENTS</h3>
                    </v-card-actions>
                    <v-divider></v-divider>
                    <div v-if="comment_items && comment_items.length != 0">
                        <v-list>
                            <v-list-item-group
                                v-model="comment_section"
                                color="warning"
                            >
                                <template v-for="item in comment_items">
                                    <v-list-item :key="item.title">
                                        <template>
                                            <v-list-item-content>
                                                <v-list-item-title>
                                                    <h6>
                                                        {{ item.comment_by }}
                                                        <span
                                                            x-small
                                                            v-if="item.modified_date"
                                                            >&nbsp;&nbsp;<v-icon
                                                                x-small
                                                                color="grey lighten-1"
                                                                >mdi-pencil-outline</v-icon
                                                            ></span
                                                        >
                                                        <br />
                                                        {{ item.comment_date }}
                                                    </h6>
                                                </v-list-item-title>
                                                <v-list-item-subtitle class="text--primary">
                                                    <div
                                                        style="padding: 5px; font-size: medium"
                                                        v-html="item.task_comment"
                                                    ></div>
                                                </v-list-item-subtitle>
                                                <div v-if="item.has_attached_file == 1">
                                                    <v-spacer></v-spacer>
                                                    <v-btn
                                                        text
                                                        x-small
                                                        color="primary lighten-1"
                                                        @click="downloadFile(item.task_filename_url)"
                                                    >
                                                        {{ item.task_comment_filename }}
                                                        <v-icon
                                                            x-small
                                                            color="primary lighten-1"
                                                        >
                                                            attach_file
                                                        </v-icon>
                                                    </v-btn>
                                                </div>
                                            </v-list-item-content>
                                            <v-list-item-action v-if="item.is_creator == 1">
                                                <v-card-actions>
                                                    <v-btn
                                                        text
                                                        icon
                                                        color="warning"
                                                        x-small
                                                        title="Edit"
                                                        data-position="top right"
                                                        @click="showEditComment(item)"
                                                    >
                                                        <v-icon>mdi-square-edit-outline</v-icon>
                                                    </v-btn>
                                                    <v-btn
                                                        text
                                                        icon
                                                        color="error"
                                                        x-small
                                                        data-position="top right"
                                                        @click="showDeleteComment(item)"
                                                    >
                                                        <v-icon>mdi-comment-remove-outline</v-icon>
                                                    </v-btn>
                                                </v-card-actions>
                                                <v-spacer></v-spacer>
                                            </v-list-item-action>
                                        </template>
                                    </v-list-item>
                                    <v-divider :key="item.title" />
                                </template>
                            </v-list-item-group>
                        </v-list>
                    </div>
                </v-card-text>
                <v-divider></v-divider>
                <div v-if="showAddComment"></div>
                <v-divider></v-divider>
                <v-card-actions>
                    <v-btn
                        color="primary"
                        @click="addComment()"
                        >{{ label_comment }}
                    </v-btn>
                    <div v-if="showAddComment">
                        <div v-if="isRemove">
                            <v-btn
                                background
                                depressed
                                @click="removeFile"
                                >{{ file_name }}
                                <v-icon
                                    center
                                    color="primary"
                                >
                                    close
                                </v-icon>
                            </v-btn>
                        </div>
                        <div v-else>
                            <v-btn
                                text
                                small
                                :loading="isSelecting"
                                @click="onButtonClick"
                            >
                                <v-icon color="primary"> attach_file</v-icon>
                            </v-btn>
                            <input
                                ref="uploader"
                                class="d-none"
                                type="file"
                                @change="onFileChanged"
                            />
                        </div>
                    </div>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="commentTask()"
                        >Save
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        @click="modalCommentTaskManagement = false"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF COMMENT TASK MANAGEMENT -->

        <!-- EDIT COMMENT -->
        <v-dialog
            v-model="dialogEditComment"
            max-width="650px"
            id="dialogEditComment"
            v-bind:content-class="`c8-page task-edit-comment-modal display-${dialogEditComment}`"
        >
            <v-card>
                <v-card-title class="headline"> Update Comment</v-card-title>
                <div
                    padding="10px"
                    margin="10"
                    id="taskEditCommentModalBody"
                ></div>
                <v-card-actions>
                    <div v-if="isRemove">
                        <v-btn
                            background
                            depressed
                            @click="removeFile"
                            >{{ comment_file_name }}
                            <v-icon
                                center
                                color="primary"
                            >
                                close
                            </v-icon>
                        </v-btn>
                    </div>
                    <div v-else>
                        <v-btn
                            text
                            small
                            :loading="isSelecting"
                            @click="onButtonClick"
                        >
                            <v-icon color="primary"> attach_file</v-icon>
                        </v-btn>
                        <input
                            ref="uploader"
                            class="d-none"
                            type="file"
                            @change="onFileChanged"
                        />
                    </div>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="editComment()"
                        >Update
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        @click="dialogEditComment = false"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF EDIT COMMENT -->

        <!-- DELETE COMMENT -->
        <v-dialog
            v-model="dialogDeleteComment"
            max-width="500px"
        >
            <v-card>
                <v-card-title>Remove this comment?</v-card-title>
                <v-card-text><span v-html="delete_comment"></span></v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="deleteComment()"
                        >Delete
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        @click="dialogDeleteComment = false"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF DELETE COMMENT -->
        <!-- ACTIVITY LOG -->
        <v-dialog
            v-model="dialogActivity"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Task Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogActivity = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <task-activity-log-component
                        v-if="dialogActivity"
                        :task_id="activity_task_id"
                    ></task-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogActivity = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapActions, mapMutations, mapState } from 'vuex';
import global_mixins from '../../../../plugins/mixins';

import Vue from 'vue';

Vue.component('task-activity-log-component', require('./TaskActivityLog.vue').default);
export default {
    props: {
        visible: false,
        task_id: 0,
    },
    data() {
        return {
            loading: true,
            error_msg: [],

            editor: ClassicEditor,
            editorConfig: {
                toolbar: {
                    items: [
                        'bold',
                        'italic',
                        '|',
                        'bulletedList',
                        'numberedList',
                        '|',
                        'indent',
                        'outdent',
                        '|',
                        'undo',
                        'redo',
                    ],
                },
                language: 'en',
            },

            type_list: [],
            steps_list: [],
            status_list: [],
            assignee_list: [],

            task_category_code: '',
            task_category_label: '',
            task_type: '',
            task_type_desc: '',
            task_step: '',
            task_step_desc: '',
            task_status: '',
            task_status_desc: '',
            task_company: '',
            task_company_label: '',
            task_property: '',
            task_property_label: '',
            task_lease: '',
            task_lease_label: '',
            task_diary: '',
            task_diary_label: '',
            task_subject: '',
            task_description: '',
            task_assignee: '',
            task_assignee_name: '',
            task_filename: '',
            task_filename_url: '',

            dialogDeleteComment: false,
            delete_comment: '',
            delete_comment_id: 0,
            delete_task_id: '',

            dialogEditComment: false,
            edit_comment: '',
            edit_comment_id: '',
            edit_task_id: '',
            task_comment_filename: '',
            old_edit_comment: '',
            old_has_attached_file: 0,
            old_task_comment_filename: '',

            modal_title: '',
            comment_section: '',
            comment_items: [],

            showAddComment: false,
            label_comment: 'Add Comment',
            new_comment: '',

            //OPTIONAL PARAMETERS
            optional_parameters: 'show optional parameters',
            show_optional_parameters: false,
            hide_optional_parameters: true,
            show_optional_company: false,
            show_optional_property: false,
            show_optional_lease: false,
            show_optional_diary: false,

            isSelecting: false,
            selectedFile: '',
            file_name: '',
            comment_file_name: '',
            file_attached: '',
            isRemove: false,

            dialogActivity: false,
            activities: [],
            activity_task_id: 0,
            disable_take_over: false,

            step_previous_data: [],
            step_next_data: [],
        };
    },
    mounted() {
        this.fetchAssigneeList();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
        modalCommentTaskManagement: {
            get() {
                if (this.visible) {
                    this.loadTaskDetails();
                }
                return this.visible;
            },
            set(value) {
                if (!value) {
                    this.$emit('close');
                }
            },
        },
        taskStatusErrors() {
            return this.error_msg.filter((item) => item.id === 'task_status');
        },
    },
    methods: {
        ...mapActions([]),
        ...mapMutations([]),
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        // For Assignee
        fetchAssigneeList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            let apiUrlAssignee = this.cirrus8_api_url + 'api/task-management/get-assignee';
            axios.post(apiUrlAssignee, formData).then((response) => {
                this.assignee_list = response.data;
            });
        },

        // OPTIONAL PARAMETERS
        showOptionalParameters: function () {
            this.show_optional_parameters = true;
            this.hide_optional_parameters = false;

            switch (this.task_category_code) {
                case 'CO':
                    this.show_optional_company = false;
                    this.show_optional_property = true;
                    this.show_optional_lease = true;
                    this.show_optional_diary = true;
                    break;
                case 'PR':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = true;
                    this.show_optional_diary = true;
                    break;
                case 'LE':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = false;
                    this.show_optional_diary = true;
                    break;
                case 'DI':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = true;
                    this.show_optional_diary = false;
                    break;
            }
        },
        hideOptionalParameters: function () {
            this.show_optional_parameters = false;
            this.hide_optional_parameters = true;
            this.show_optional_company = false;
            this.show_optional_property = false;
            this.show_optional_lease = false;
            this.show_optional_diary = false;
        },
        loadTaskDetails: function () {
            if (this.task_id != 0) {
                var formData = new FormData();
                formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);
                formData.append('task_id', this.task_id);
                let apiUrlLease = this.cirrus8_api_url + 'api/task-management/get-task';
                document.getElementById('ngLoader-UI').style.display = 'block';
                axios.post(apiUrlLease, formData).then((response) => {
                    document.getElementById('ngLoader-UI').style.display = 'none';

                    this.type_list = response.data.type_list;
                    this.steps_list = response.data.steps_list;
                    this.status_list = response.data.status_list;
                    this.disable_take_over = response.data.allow_edit;
                    this.step_previous_data = response.data.previous_step;
                    this.step_next_data = response.data.next_step;

                    let task_detail = response.data.task_detail;
                    this.comment_items = task_detail.task_comments;

                    this.modal_title = task_detail.task_ctlr_no + ' - ' + task_detail.task_subject;
                    this.task_type = task_detail.task_type;
                    this.task_type_desc = task_detail.task_type_desc;
                    this.task_status = task_detail.task_status;
                    this.task_status_desc = task_detail.task_status_desc;
                    this.task_step = task_detail.task_step;
                    this.task_step_desc = task_detail.task_step_desc;
                    this.task_category_code = task_detail.task_category_code;
                    this.task_category_label = task_detail.task_category_label;
                    this.task_subject = task_detail.task_subject;
                    this.task_description = task_detail.task_description;
                    this.task_assignee = task_detail.task_assignee;
                    this.task_assignee_name = task_detail.task_assignee_name;
                    this.task_company_label = task_detail.task_company_label;
                    this.task_company = task_detail.task_company;
                    this.task_property_label = task_detail.task_property_label;
                    this.task_property = task_detail.task_property;
                    this.task_lease_label = task_detail.task_lease_label;
                    this.task_lease = task_detail.task_lease;
                    this.task_diary = task_detail.task_diary;
                    this.task_diary_label = task_detail.task_diary_label;
                    if (task_detail.task_filename) {
                        this.task_filename_url = task_detail.task_filename_url;
                        this.task_filename = task_detail.task_filename;
                        this.file_name = task_detail.task_filename;
                        this.isRemove = true;
                    } else {
                        this.removeFile();
                    }

                    this.old_task_assignee = task_detail.task_assignee;
                    this.old_task_step = task_detail.task_step;

                    this.showAddComment = false;
                    this.label_comment = 'Add Comment';
                    this.new_comment = '';

                    this.fetchAssigneeList();
                    this.hideOptionalParameters();
                    this.removeFile();
                });
            }
        },

        commentTask: function () {
            let validate = true;
            if (this.showAddComment) {
                if (this.new_comment == '') {
                    this.$noty.error('You have not specified any comment.');
                    validate = false;
                }
                if (validate) {
                    var formData = new FormData();
                    formData.append('form_mode', 'add_comment');
                    formData.append('un', this.username);
                    formData.append('current_db', this.current_db);
                    formData.append('user_type', this.user_type);
                    formData.append('task_id', this.task_id);
                    formData.append('task_code', this.task_category_code);
                    formData.append('task_assignee', this.task_assignee);
                    formData.append('task_subject', this.task_subject);
                    formData.append('task_comment', this.new_comment);
                    formData.append('file_attached', this.file_attached);
                    formData.append('task_filename', this.file_name);

                    // INSERT NEW COMMENT
                    document.getElementById('ngLoader-UI').style.display = 'block';
                    let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
                    axios.post(apiUrl, formData).then((response) => {
                        this.status = response.data.status;
                        document.getElementById('ngLoader-UI').style.display = 'none';
                        if (this.status == 'success') {
                            this.modalCommentTaskManagement = false;
                            if (this.new_comment) {
                                this.$noty.success('Comment added.');
                            }
                            // if (this.old_task_step != this.task_step) {
                            //   this.$noty.success('Task Step Updated.');
                            // }
                            // if (this.old_task_status != this.task_status) {
                            //   this.$noty.success('Task Status Updated.');
                            // }
                            // if (this.old_task_assignee != this.task_assignee) {
                            //   this.$noty.success('Task Assignee Updated.');
                            // }
                            // reset fields
                            this.loadTaskList();
                            this.loading_setting = false;
                        } else {
                            this.$noty.error('error message');
                            this.loading_setting = false;
                        }
                    });
                }
            } else {
                if (this.task_step == '' || this.task_step == null) {
                    this.$noty.error('You have not specified a step.');
                    validate = false;
                }
                if (this.task_status == '' || this.task_status == null) {
                    this.$noty.error('You have not specified a status.');
                    validate = false;
                }
                if (this.task_assignee == '' || this.task_assignee == null) {
                    this.$noty.error('You have not specified an assignee.');
                    validate = false;
                }
                if (validate) {
                    var formData = new FormData();
                    formData.append('form_mode', 'update_task');
                    formData.append('un', this.username);
                    formData.append('current_db', this.current_db);
                    formData.append('user_type', this.user_type);
                    formData.append('task_code', this.task_category_code);
                    formData.append('task_id', this.task_id);
                    formData.append('task_step', this.task_step);
                    formData.append('task_status', this.task_status);
                    formData.append('task_assignee', this.task_assignee);
                    formData.append('old_task_step', this.old_task_step);
                    formData.append('old_task_status', this.old_task_status);
                    formData.append('old_task_assignee', this.old_task_assignee);

                    // UPDATE STEPS STATUS AND ASSIGNEE
                    document.getElementById('ngLoader-UI').style.display = 'block';
                    let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
                    axios.post(apiUrl, formData).then((response) => {
                        this.status = response.data.status;
                        document.getElementById('ngLoader-UI').style.display = 'none';
                        if (this.status == 'success') {
                            this.modalCommentTaskManagement = false;
                            if (this.old_task_step != this.task_step) {
                                this.$noty.success('Task Step Updated.');
                            }
                            if (this.old_task_status != this.task_status) {
                                this.$noty.success('Task Status Updated.');
                            }
                            if (this.old_task_assignee != this.task_assignee) {
                                this.$noty.success('Task Assignee Updated.');
                            }
                            // reset fields
                            this.loadTaskList();
                            this.loading_setting = false;
                        } else {
                            this.$noty.error('error message');
                            this.loading_setting = false;
                        }
                    });
                }
            }
        },
        addComment: function () {
            if (this.showAddComment) {
                this.showAddComment = false;
                this.label_comment = 'Add Comment';
                this.removeFile();
            } else {
                this.showAddComment = true;
                this.label_comment = 'Cancel Comment';
                this.removeFile();
            }
        },
        showDeleteComment: function (data) {
            if (this.showAddComment) {
                this.showAddComment = false;
                this.label_comment = 'Add Comment';
                this.removeFile();
            }
            this.dialogDeleteComment = true;
            this.delete_comment = data.task_comment;
            this.delete_comment_id = data.row_id;
            this.delete_task_id = data.task_id;
        },
        deleteComment: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('comment_id', this.delete_comment_id);
            formData.append('task_id', this.delete_task_id);

            // DELETE NEW COMMENT
            document.getElementById('ngLoader-UI').style.display = 'block';
            let apiUrl = this.cirrus8_api_url + 'api/task-management/delete-comment';
            axios.post(apiUrl, formData).then((response) => {
                document.getElementById('ngLoader-UI').style.display = 'none';
                this.status = response.data.status;
                if (this.status == 'success') {
                    this.dialogDeleteComment = false;
                    this.modalCommentTaskManagement = false;
                    this.$noty.success('Comment successfully removed.');
                    // reset fields
                    this.loadTaskList();
                    this.loading_setting = false;
                } else {
                    this.$noty.error('error message');
                    this.loading_setting = false;
                }
            });
        },
        showEditComment: function (data) {
            if (this.showAddComment) {
                this.showAddComment = false;
                this.label_comment = 'Add Comment';
                this.removeFile();
            }
            this.dialogEditComment = true;
            this.edit_comment = data.task_comment;
            if (data.task_comment_filename) {
                this.task_comment_filename = data.task_comment_filename;
                this.comment_file_name = data.task_comment_filename;
                this.isRemove = true;
            } else {
                this.removeFile();
            }
            this.old_edit_comment = data.task_comment;
            this.old_task_comment_filename = data.task_comment_filename;
            this.old_has_attached_file = data.has_attached_file;
            this.edit_comment_id = data.row_id;
            this.edit_task_id = data.task_id;
        },
        editComment: function () {
            var formData = new FormData();
            formData.append('form_mode', 'edit_comment');
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('task_code', this.task_category_code);
            formData.append('comment_text', this.edit_comment);
            formData.append('comment_id', this.edit_comment_id);
            formData.append('task_id', this.edit_task_id);
            formData.append('file_attached', this.file_attached);
            // changes on attachment
            if (this.task_comment_filename != this.comment_file_name) {
                formData.append('task_filename', this.comment_file_name);
            } else {
                formData.append('task_filename', this.task_comment_filename);
            }
            formData.append('old_comment_text', this.old_edit_comment);
            formData.append('old_task_comment_filename', this.old_task_comment_filename);
            formData.append('old_has_attached_file', this.old_has_attached_file);
            // EDIT COMMENT
            document.getElementById('ngLoader-UI').style.display = 'block';
            let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
            axios.post(apiUrl, formData).then((response) => {
                document.getElementById('ngLoader-UI').style.display = 'none';
                this.status = response.data.status;
                if (this.status == 'success') {
                    this.dialogEditComment = false;
                    this.modalCommentTaskManagement = false;
                    this.$noty.success('Comment update.');
                    // reset fields
                    // this.loadTaskList();
                    this.loading_setting = false;
                } else {
                    this.$noty.error('error message');
                    this.loading_setting = false;
                }
            });
        },

        onButtonClick: function () {
            this.isSelecting = true;
            window.addEventListener(
                'focus',
                () => {
                    this.isSelecting = false;
                },
                { once: true },
            );

            this.$refs.uploader.click();
        },
        onFileChanged: function (e) {
            var files = e.target.files || e.dataTransfer.files;
            if (files.length && files[0].type == 'application/pdf') {
                this.file_attached = files[0];
                this.file_name = files[0].name;
                this.isRemove = true;
            } else {
                this.$noty.error('Invalid file type. Please upload only pdf file.');
            }
        },
        removeFile: function (item) {
            this.file_name = '';
            this.isRemove = false;
            this.file_attached = '';
        },

        downloadFile: function (encoded_url) {
            document.location.href = 'download.php?fileID=' + encoded_url;
        },

        showActivityModal: function (task_id) {
            this.activity_task_id = task_id;
            this.dialogActivity = true;
        },

        goToShortcut: function (parameter) {
            let property_code = '';
            let lease_code = '';
            let company_code = '';
            switch (parameter) {
                case 'property':
                    property_code = this.task_property;
                    window.open(
                        '?module=properties&command=v2_manage_property_page&property_code=' + property_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'lease':
                    lease_code = this.task_lease;
                    property_code = this.task_property;
                    window.open(
                        '?module=leases&command=home&propertyID=' + property_code + '&leaseID=' + lease_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'company':
                    company_code = this.task_company;
                    window.open(
                        '?module=companies&command=company&companyID=' + company_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
            }
        },

        takeOverTask: function () {
            var formData = new FormData();
            formData.append('form_mode', 'take_over');
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('task_id', this.task_id);
            formData.append('task_subject', this.task_subject);
            formData.append('task_assignee', this.task_assignee);
            formData.append('task_description', this.task_description);
            formData.append('old_task_assignee', this.old_task_assignee);
            // UPDATE STEPS STATUS AND ASSIGNEE
            document.getElementById('ngLoader-UI').style.display = 'block';
            let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
            axios.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                document.getElementById('ngLoader-UI').style.display = 'none';
                if (this.status == 'success') {
                    this.modalCommentTaskManagement = false;
                    this.loadTaskList();
                    this.loading_setting = false;
                } else {
                    this.$noty.error('error message');
                    this.loading_setting = false;
                }
            });
        },

        changeStep: function (stepData) {
            var formData = new FormData();
            formData.append('form_mode', 'add_comment');
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('step_id', stepData.step_id);
            formData.append('step_assignee', stepData.step_assignee);
            formData.append('task_assignee_email', stepData.task_assignee_email);
            formData.append('task_id', this.task_id);
            formData.append('old_task_step', this.old_task_step);
            formData.append('old_task_assignee', this.old_task_assignee);
            formData.append('task_subject', this.task_subject);
            formData.append('task_description', this.task_description);

            // CHANGE TASK STEP
            document.getElementById('ngLoader-UI').style.display = 'block';
            let apiUrl = this.cirrus8_api_url + 'api/task-management/change-step';
            axios.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                document.getElementById('ngLoader-UI').style.display = 'none';
                if (this.status == 'success') {
                    this.modalCommentTaskManagement = false;
                    this.$noty.success('Step successfully updated.');
                    this.loadTaskList();
                    this.loading_setting = false;
                } else {
                    this.$noty.error('error message');
                    this.loading_setting = false;
                }
            });
        },
    },
    mixins: [global_mixins],
};
</script>

<style scoped>
div >>> p,
ul,
li {
    font-size: medium !important;
    font-color: #000000 !important;
}
</style>
