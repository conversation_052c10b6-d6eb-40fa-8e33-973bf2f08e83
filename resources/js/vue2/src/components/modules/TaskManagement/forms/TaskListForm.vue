<style>
.ck-editor__editable_inline {
    min-height: 100px;
}
</style>
<template>
    <v-container
        fluid
        class="c8-page"
    >
        <div v-if="showListForm">
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>
            <v-card
                id="task_list_header"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h4 class="title font-weight-black">Task List - {{ headerTitle }}</h4>
                </v-card-actions>
            </v-card>

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div class="page-list">
                    <div class="c8-page-table">
                        <table>
                            <tbody>
                                <tr class="form-row">
                                    <td>
                                        <v-text-field
                                            v-model="searchTask"
                                            placeholder="Search..."
                                            dense
                                        />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- TASK LIST DATA TABLE -->
                        <v-data-table
                            :headers="listHeaders"
                            :items="options"
                            :search="searchTask"
                            dense
                        >
                            <template v-slot:item.action="{ item }">
                                <v-btn
                                    text
                                    icon
                                    color="warning"
                                    x-small
                                    title="Edit"
                                    data-position="bottom right"
                                    @click="showEditModal(item)"
                                >
                                    <v-icon>mdi-square-edit-outline</v-icon>
                                </v-btn>
                                <v-btn
                                    text
                                    icon
                                    color="primary"
                                    x-small
                                    title="Comment"
                                    data-position="bottom right"
                                    @click="showCommentModal(item)"
                                >
                                    <v-icon>mdi-comment-plus-outline</v-icon>
                                </v-btn>
                            </template>
                        </v-data-table>
                    </div>
                </div>
            </div>
        </div>

        <!-- UPDATE TASK MANAGEMENT -->
        <v-dialog
            top
            v-model="modalUpdateTaskManagement"
            width="600"
        >
            <v-card>
                <v-card
                    id="update_task_header"
                    dark
                    color="primary"
                    text
                    tile
                >
                    <v-card-actions>
                        <h4 class="title font-weight-black">Update Task Management</h4>
                    </v-card-actions>
                </v-card>
                <v-divider></v-divider>
                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <!-- TASK CATEGORY -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Task Category</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                                >{{ task_category_label }}
                            </v-col>
                        </v-row>

                        <!-- TYPE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Type</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_type"
                                    :options="type_list"
                                    ref="refType"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_type'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <!-- STEP -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Step</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_step"
                                    :options="step_list"
                                    ref="refStep"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_assignee'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <!-- STATUS -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Status</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_status"
                                    :options="dd_status_list"
                                    ref="refStatus"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_assignee'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <!-- BASE ON CATEGORY TASK MANDATORY FIELDS -->
                        <!-- COMPANY -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'CO'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_company"
                                    :options="company_list"
                                    ref="refCompany"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_company'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>
                        <!-- PROPERTY -->
                        <v-row
                            class="form-row"
                            v-if="
                                this.task_category_code == 'PR' ||
                                this.task_category_code == 'LE' ||
                                this.task_category_code == 'DI'
                            "
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Property</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_property"
                                    :options="dd_property_list"
                                    ref="refProperty"
                                    @input="updateRelatedList()"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_property'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>
                        <!-- LEASE -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'LE'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Lease</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_lease"
                                    :options="lease_list"
                                    ref="refLease"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_lease'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>
                        <!-- DIARY ITEM -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'DI'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Diary Item</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_diary"
                                    :options="diary_list"
                                    ref="refDiary"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_diary'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>
                        <!-- END -->
                        <v-card-actions v-if="this.task_category_code && this.task_category_code != 'GE'">
                            <v-spacer></v-spacer>
                            <v-btn
                                v-if="hide_optional_parameters"
                                color="primary"
                                text
                                x-small
                                @click="showOptionalParameters()"
                                >SHOW OPTIONAL PARAMETERS</v-btn
                            >
                            <v-btn
                                v-if="show_optional_parameters"
                                color="primary"
                                text
                                x-small
                                @click="hideOptionalParameters()"
                                >HIDE OPTIONAL PARAMETERS</v-btn
                            >
                        </v-card-actions>
                        <v-divider v-if="this.task_category_code && this.task_category_code != 'GE'"></v-divider>

                        <!-- SHOW OPTIONAL PARAMETERS -->
                        <!-- COMPANY -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_company"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_company"
                                    :options="company_list"
                                    ref="refCompany"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- PROPERTY -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_property"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Property</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_property"
                                    :options="dd_property_list"
                                    ref="refProperty"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- LEASE -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_lease"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Lease</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_lease"
                                    :options="lease_list"
                                    ref="refLease"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- DIARY ITEM -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_diary"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Diary Item</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_diary"
                                    :options="diary_list"
                                    ref="refDiary"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- END -->

                        <!-- SUBJECT -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Subject</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="task_subject"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_subject'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <!-- DESCRIPTION -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Description</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                            </v-col>
                        </v-row>

                        <!-- ASSIGNEE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Assignee</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_assignee"
                                    :options="dd_assignee_list"
                                    ref="refAssignee"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_assignee'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <!-- ATTACHMENTS -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Attachments</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-btn
                                    background
                                    depressed
                                >
                                    <v-icon
                                        center
                                        color="primary"
                                    >
                                        attach_file
                                    </v-icon>
                                </v-btn>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="updateTask()"
                        >Update</v-btn
                    >
                    <v-btn
                        color="primary"
                        text
                        @click="modalUpdateTaskManagement = false"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF UPDATE TASK MANAGEMENT -->

        <!-- COMMENT TASK MANAGEMENT -->
        <v-dialog
            top
            v-model="modalCommentTaskManagement"
            hide-overlay
            transition="dialog-bottom-transition"
            scrollable
            content-class="c8-page"
            width="600"
        >
            <v-card height="600">
                <v-card
                    id="comment_task_header"
                    dark
                    color="primary"
                    text
                    tile
                >
                    <v-card-actions>
                        <h4 class="title font-weight-black">{{ modal_title }}</h4>
                    </v-card-actions>
                </v-card>
                <v-card-text>
                    <div class="page-form">
                        <!-- TASK CATEGORY -->
                        <!--            <v-row class="form-row">-->
                        <!--              <v-col xs="12" sm="3" md="3" class="form-label">Task Category</v-col>-->
                        <!--              <v-col xs="12" sm="9" md="9" class="form-input">-->
                        <!--                <v-col>{{ task_category_label }}</v-col>-->
                        <!--              </v-col>-->
                        <!--            </v-row>-->

                        <!-- TYPE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Type</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_type }}</v-col>
                            </v-col>
                        </v-row>

                        <!-- STEP -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Step</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_step"
                                    :options="step_list"
                                    ref="refStep"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_assignee'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <!-- STATUS -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Status</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_status"
                                    :options="dd_status_list"
                                    ref="refStatus"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_assignee'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <!-- BASE ON CATEGORY TASK MANDATORY FIELDS -->
                        <!-- COMPANY -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'CO'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_company }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- PROPERTY -->
                        <v-row
                            class="form-row"
                            v-if="
                                this.task_category_code == 'PR' ||
                                this.task_category_code == 'LE' ||
                                this.task_category_code == 'DI'
                            "
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Property</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_property }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- LEASE -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'LE'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Lease</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_lease }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- DIARY ITEM -->
                        <v-row
                            class="form-row"
                            v-if="this.task_category_code == 'DI'"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Diary Item</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_diary }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- END -->
                        <v-card-actions v-if="this.task_category_code && this.task_category_code != 'GE'">
                            <v-spacer></v-spacer>
                            <v-btn
                                v-if="hide_optional_parameters"
                                color="primary"
                                text
                                x-small
                                @click="showOptionalParameters()"
                                >SHOW OPTIONAL PARAMETERS</v-btn
                            >
                            <v-btn
                                v-if="show_optional_parameters"
                                color="primary"
                                text
                                x-small
                                @click="hideOptionalParameters()"
                                >HIDE OPTIONAL PARAMETERS</v-btn
                            >
                        </v-card-actions>
                        <v-divider v-if="this.task_category_code && this.task_category_code != 'GE'"></v-divider>

                        <!-- SHOW OPTIONAL PARAMETERS -->
                        <!-- COMPANY -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_company"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Company</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_company }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- PROPERTY -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_property"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Property</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_property }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- LEASE -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_lease"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Lease</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_lease }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- DIARY ITEM -->
                        <v-row
                            class="form-row"
                            v-if="show_optional_diary"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Diary Item</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col>{{ task_diary }}</v-col>
                            </v-col>
                        </v-row>
                        <!-- END -->

                        <!-- SUBJECT -->
                        <!--            <v-row class="form-row">-->
                        <!--              <v-col xs="12" sm="3" md="3" class="form-label">Subject</v-col>-->
                        <!--              <v-col xs="12" sm="9" md="9" class="form-input">-->
                        <!--                <v-col>{{ task_subject }}</v-col>-->
                        <!--              </v-col>-->
                        <!--            </v-row>-->

                        <!-- DESCRIPTION -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Description</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-col><span v-html="task_description"></span></v-col>
                            </v-col>
                        </v-row>

                        <!-- ASSIGNEE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Assignee</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="task_assignee"
                                    :options="dd_assignee_list"
                                    ref="refAssignee"
                                    dense
                                />
                                <v-chip
                                    v-if="error_msg.length > 0 && errorData.id === 'task_assignee'"
                                    v-for="(errorData, index) in error_msg"
                                    :key="index"
                                    outlined
                                    color="error"
                                >
                                    <v-icon left>error</v-icon>
                                    {{ errorData.message }}
                                </v-chip>
                            </v-col>
                        </v-row>

                        <!-- ATTACHMENTS -->
                        <!--            <v-row class="form-row">-->
                        <!--              <v-col xs="12" sm="3" md="3" class="form-label">Attachments</v-col>-->
                        <!--              <v-col xs="12" sm="9" md="9" class="form-input">-->
                        <!--                <v-btn background depressed >-->
                        <!--                  <v-icon center color="primary">-->
                        <!--                    attach_file-->
                        <!--                  </v-icon>-->
                        <!--                </v-btn>-->
                        <!--              </v-col>-->
                        <!--            </v-row>-->
                    </div>
                    <v-divider></v-divider>
                    <v-card-actions>
                        <h3 class="title font-weight-black">COMMENTS</h3>
                    </v-card-actions>
                    <v-divider></v-divider>
                    <v-list v-if="comment_items">
                        <v-list-item-group v-model="comment_section">
                            <template v-for="(item, index) in comment_items">
                                <v-list-item :key="item.title">
                                    <template v-slot:default="{ active }">
                                        <v-list-item-content>
                                            <v-list-item-title v-text="item.comment_by">
                                                <span v-if="item.modified_date">Edited</span>
                                            </v-list-item-title>
                                            <v-list-item-subtitle
                                                class="text--primary"
                                                v-text="item.comment_date"
                                            ></v-list-item-subtitle>
                                            <v-list-item-subtitle>
                                                <span v-html="item.task_comment"></span>
                                            </v-list-item-subtitle>
                                            <div v-if="item.has_attached_file == 1">
                                                <span>Attachment : </span>
                                                <v-btn
                                                    text
                                                    icon
                                                    color="error"
                                                    x-small
                                                    data-position="bottom right"
                                                >
                                                    <v-icon
                                                        x-small
                                                        color="grey lighten-1"
                                                    >
                                                        attach_file
                                                    </v-icon>
                                                </v-btn>
                                            </div>
                                        </v-list-item-content>

                                        <v-list-item-action v-if="item.is_creator == 1">
                                            <v-card-actions>
                                                <v-btn
                                                    text
                                                    icon
                                                    color="warning"
                                                    x-small
                                                    title="Edit"
                                                    data-position="top right"
                                                    @click="editComment(item)"
                                                >
                                                    <v-icon>mdi-square-edit-outline</v-icon>
                                                </v-btn>
                                                <v-btn
                                                    text
                                                    icon
                                                    color="error"
                                                    x-small
                                                    data-position="top right"
                                                    @click="showDeleteComment(item)"
                                                >
                                                    <v-icon>mdi-comment-remove-outline</v-icon>
                                                </v-btn>
                                            </v-card-actions>
                                            <v-spacer></v-spacer>
                                        </v-list-item-action>
                                    </template>
                                    <v-divider
                                        v-if="index < comment_items.length - 1"
                                        :key="index"
                                    ></v-divider>
                                </v-list-item>
                            </template>
                        </v-list-item-group>
                    </v-list>
                </v-card-text>
                <v-divider></v-divider>
                <div v-if="showAddComment"></div>
                <v-divider></v-divider>
                <v-card-actions>
                    <v-btn
                        color="primary"
                        @click="addComment()"
                        >{{ label_comment }}</v-btn
                    >
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="commentTask()"
                        >Save</v-btn
                    >
                    <v-btn
                        color="primary"
                        text
                        @click="modalCommentTaskManagement = false"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF UPDATE TASK MANAGEMENT -->

        <!-- DELETE COMMENT -->
        <v-dialog
            v-model="dialogDelete"
            max-width="500px"
        >
            <v-card>
                <v-card-title>Remove this comment?</v-card-title>
                <v-card-text><span v-html="delete_comment"></span></v-card-text>
                <v-card-actions>
                    <v-btn
                        color="primary"
                        text
                        @click="dialogDelete = false"
                        >Close</v-btn
                    >
                    <v-btn
                        color="primary"
                        text
                        @click="deleteComment()"
                        >Delete</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-container>
</template>

<script>
import Vue from 'vue';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import { mapActions, mapMutations, mapState } from 'vuex';

export default {
    name: 'TaskListForm',
    props: ['visible', 'title', 'options', 'type_list'],

    data() {
        return {
            // Default Values
            loading_setting: false,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            editor: ClassicEditor,
            editorConfig: {
                toolbar: {
                    items: [
                        'bold',
                        'italic',
                        '|',
                        'bulletedList',
                        'numberedList',
                        '|',
                        'indent',
                        'outdent',
                        '|',
                        'undo',
                        'redo',
                    ],
                },
                language: 'en',
            },
            // OPTIONS
            step_list: [],
            company_list: [],
            property_list: [],
            lease_list: [],
            diary_list: [],

            searchTask: '',

            listHeaders: [
                { text: 'Task ID', value: 'task_id', sortable: false, width: '10%' },
                { text: 'Subject', value: 'task_subject', sortable: false, width: '29%' },
                { text: 'Type', value: 'task_type', sortable: false, width: '18%' },
                { text: 'Step Name', value: 'task_step_name', sortable: false, width: '14%' },
                { text: 'Assignee', value: 'task_assignee', sortable: false, width: '14%' },
                { text: 'Status', value: 'task_status', sortable: false, width: '10%' },
                { text: 'Actions', value: 'action', sortable: false, width: '5%' },
            ],

            // UPDATE TASK
            modalUpdateTaskManagement: false,
            task_row_id: 0,
            task_category_code: '',
            task_category_label: '',
            task_type: '',
            task_step: '',
            task_status: '',
            task_company: '',
            task_property: '',
            task_lease: '',
            task_diary: '',

            task_subject: '',
            task_description: '',
            task_assignee: '',

            //OPTIONAL PARAMETERS
            optional_parameters: 'show optional parameters',
            show_optional_parameters: false,
            hide_optional_parameters: true,
            show_optional_company: false,
            show_optional_property: false,
            show_optional_lease: false,
            show_optional_diary: false,

            // COMMENT
            modalCommentTaskManagement: false,
            modal_title: '',
            comment_section: '',
            comment_items: [],

            showAddComment: false,
            label_comment: 'Add Comment',
            new_comment: '',

            dialogDelete: false,
            delete_comment: '',
            delete_comment_id: 0,
        };
    },
    mounted() {
        this.fetchAssigneeList();
        this.fetchPropertyList();
        this.fetchStatusList();
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'dd_assignee_list',
            'dd_property_list',
            'dd_status_list',
        ]),
        showListForm: {
            get() {
                return this.visible;
            },
            set(value) {
                if (!value) {
                    this.$emit('close');
                }
            },
        },
        headerTitle: {
            get() {
                return this.title;
            },
        },
    },
    methods: {
        ...mapActions(['fetchAssigneeList', 'fetchPropertyList', 'fetchStatusList']),
        ...mapMutations(['SET_DD_ASSIGNEE_LIST', 'SET_DD_PROPERTY_LIST', 'SET_DD_STATUS_LIST']),
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        // LOAD LIST OPTIONS
        fetchStepsList: function (typeID) {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('task_type', typeID);
            let apiUrl = this.cirrus8_api_url + 'api/task-management/get-steps-option';
            axios.post(apiUrl, formData).then((response) => {
                this.step_list = response.data;
            });
        },
        // OPTIONAL PARAMETERS
        showOptionalParameters: function () {
            this.show_optional_parameters = true;
            this.hide_optional_parameters = false;

            switch (this.task_category_code) {
                case 'CO':
                    this.show_optional_company = false;
                    this.show_optional_property = true;
                    this.show_optional_lease = true;
                    this.show_optional_diary = true;
                    break;
                case 'PR':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = true;
                    this.show_optional_diary = true;
                    break;
                case 'LE':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = false;
                    this.show_optional_diary = true;
                    break;
                case 'DI':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = true;
                    this.show_optional_diary = false;
                    break;
            }
        },
        hideOptionalParameters: function () {
            this.show_optional_parameters = false;
            this.hide_optional_parameters = true;
            this.show_optional_company = false;
            this.show_optional_property = false;
            this.show_optional_lease = false;
            this.show_optional_diary = false;
        },

        reloadTypeOption: function () {
            var formData = new FormData();
            formData.append('category', this.task_category_code);
            formData.append('show_active', 1);
            switch (this.task_category_code) {
                case 'CO':
                    let apiUrlCompany = 'company-dropdown-list';
                    axios.post(apiUrlCompany, formData).then((response) => {
                        this.company_list = response.data;
                    });
                    break;
                case 'PR':
                case 'LE':
                case 'DI':
                    formData.append('active_only', 1);
                    let apiUrlProperty = 'loadAPIPropertyDropDownList';
                    axios.post(apiUrlProperty, formData).then((response) => {
                        this.property_list = response.data.data;
                    });
                    break;
            }

            // this.resetDropDown();
            // this.hideOptionalParameters();
        },

        showEditModal: function (data) {
            this.reloadTypeOption();
            this.modalUpdateTaskManagement = true;
            this.task_type = data.task_type_id;
            this.task_status = data.task_status_code;
            this.task_category_code = data.task_category_code;
            this.task_row_id = data.task_row_id;
            this.task_category_code = data.task_category_code;
            this.task_category_label = data.task_category_label;
            this.task_subject = data.task_subject;
            this.task_description = data.task_description;
            this.task_assignee = data.task_assignee_id;
            this.task_company = data.task_company;
            this.task_property = data.task_property;
            this.task_lease = data.task_lease;
            this.task_diary = data.task_diary;
        },
        showCommentModal: function (data) {
            this.fetchStepsList(data.task_type_id);
            this.comment_items = data.task_comments;
            this.reloadTypeOption();
            this.modalCommentTaskManagement = true;
            this.modal_title = data.task_id + ' - ' + data.task_subject;
            this.task_type = data.task_type;
            this.task_status = data.task_status_code;
            this.task_step = data.task_step_name;
            this.task_category_code = data.task_category_code;
            this.task_row_id = data.task_row_id;
            this.task_category_code = data.task_category_code;
            this.task_category_label = data.task_category_label;
            this.task_subject = data.task_subject;
            this.task_description = data.task_description;
            this.task_assignee = data.task_assignee_id;
            this.task_company = data.task_company;
            this.task_property = data.task_property;
            this.task_lease = data.task_lease;
            this.task_diary = data.task_diary;
        },
        hideModal: function (data) {},
        updateTask: function () {
            let validate = true;

            if (this.task_category_code == 'CO') {
                if (this.task_company == '') {
                    this.$noty.error('You have not specified a company.');
                    validate = false;
                }
            }
            if (this.task_category_code == 'PR') {
                if (this.task_property == '') {
                    this.$noty.error('You have not specified a property.');
                    validate = false;
                }
            }
            if (this.task_category_code == 'LE') {
                if (this.task_lease == '') {
                    this.$noty.error('You have not specified a lease.');
                    validate = false;
                }
            }
            if (this.task_category_code == 'DI') {
                if (this.task_diary == '') {
                    this.$noty.error('You have not specified a diary item.');
                    validate = false;
                }
            }

            if (this.task_type == '' || this.task_type == null) {
                this.$noty.error('You have not specified a type.');
                validate = false;
            }

            if (this.task_subject == '') {
                this.$noty.error('You have not specified a subject.');
                validate = false;
            }

            if (this.task_assignee == '' || this.task_assignee == null) {
                this.$noty.error('You have not specified an assignee.');
                validate = false;
            }
            // submit form if no error
            if (validate) {
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);

                formData.append('form_mode', 'update');
                formData.append('task_row_id', this.task_row_id);
                formData.append('task_code', this.task_category_code);
                formData.append('task_type', this.task_type);
                formData.append('task_subject', this.task_subject);
                formData.append('task_description', this.task_description);
                formData.append('task_assignee', this.task_assignee);
                formData.append('task_company', this.task_company);
                formData.append('task_property', this.task_property);
                formData.append('task_lease', this.task_lease);
                formData.append('task_diary', this.task_diary);
                // UPdate Task details
                let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';

                axios.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        // this.resetAllField();
                        this.modalUpdateTaskManagement = false;
                        this.$noty.success('Task updated.');
                        this.loading_setting = false;
                    } else {
                        this.$noty.error('error message');
                        this.loading_setting = false;
                    }
                });
            }
        },
        commentTask: function () {
            if (this.showAddComment) {
                var formData = new FormData();
                formData.append('form_mode', 'add_comment');
                formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);
                formData.append('task_id', this.task_row_id);
                formData.append('task_comment', this.new_comment);

                // INSERT NEW COMMENT
                let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
                axios.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        this.$noty.success('Comment added.');
                        // reset fields
                        this.loading_setting = false;
                    } else {
                        this.$noty.error('error message');
                        this.loading_setting = false;
                    }
                });
            } else {
            }
        },

        addComment: function () {
            if (this.showAddComment) {
                this.showAddComment = false;
                this.label_comment = 'Add Comment';
            } else {
                this.showAddComment = true;
                this.label_comment = 'Cancel Comment';
            }
        },

        showDeleteComment: function (data) {
            this.dialogDelete = true;
            this.delete_comment = data.task_comment;
            this.delete_comment_id = data.row_id;
        },
        deleteComment: function () {
            var formData = new FormData();
            formData.append('form_mode', 'add_comment');
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('comment_id', this.delete_comment_id);

            // INSERT NEW COMMENT
            let apiUrl = this.cirrus8_api_url + 'api/task-management/delete-comment';
            axios.post(apiUrl, formData).then((response) => {
                this.status = response.data.status;
                if (this.status == 'success') {
                    this.$noty.success('Comment successfully removed.');
                    // reset fields
                    this.loading_setting = false;
                } else {
                    this.$noty.error('error message');
                    this.loading_setting = false;
                }
            });
        },
    },
    watch: {},
    mixins: [global_mixins],
};
</script>

<style scoped></style>
