<style>
.task-create-modal #taskCreateModalBody {
    width: 100%;
    transition-property: width;
    transition-timing-function: ease-out;
    transition-duration: 0s;
    transition-delay: 0.1s;
}

.task-create-modal:hover #taskCreateModalBody {
    width: 99.99% !important;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <v-dialog
            v-model="showCreateForm"
            width="60%"
            scrollable
            v-bind:content-class="'task-create-modal display-' + showCreateForm"
            content-class="c8-page"
        >
            <v-card height="600">
                <v-card-title class="headline">
                    {{ page_title }}
                </v-card-title>
                <v-card-text>
                    <div
                        id="taskCreateModalBody"
                        class="body c8-page"
                        style="height: auto; min-height: initial; padding: 10px"
                    >
                        <div class="page-form">
                            <!-- TASK CATEGORY -->
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Task Category</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_category"
                                        :options="dd_category_list"
                                        @input="reloadTypeOption(new_category)"
                                        ref="refCategory"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'new_category'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>

                            <!-- TYPE -->
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_type"
                                        :options="type_list"
                                        @input="changeTaskType()"
                                        ref="refType"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'new_type'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>
                            <!-- BASE ON CATEGORY TASK MANDATORY FIELDS -->
                            <!-- COMPANY -->
                            <v-row
                                class="form-row"
                                v-if="this.new_category == 'CO'"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Company</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_company"
                                        :options="company_list"
                                        ref="refCompany"
                                        @input="updateRelatedList()"
                                        dense
                                    />
                                    <div style="margin-top: -25; margin-left: 300">
                                        <v-btn
                                            elevation="0"
                                            icon
                                            x-small
                                            v-on:click="showLookup()"
                                            data-tooltip="Look up"
                                        >
                                            <v-icon>mdi-feature-search-outline</v-icon>
                                        </v-btn>
                                    </div>
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'new_company'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>
                            <!-- PROPERTY -->
                            <v-row
                                class="form-row"
                                v-if="
                                    this.new_category == 'PR' || this.new_category == 'LE' || this.new_category == 'DI'
                                "
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Property</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_property"
                                        :options="property_list"
                                        ref="refProperty"
                                        @input="updateRelatedList()"
                                        dense
                                    />
                                    <div style="margin-top: -25; margin-left: 300">
                                        <v-btn
                                            elevation="0"
                                            icon
                                            x-small
                                            v-on:click="showLookup()"
                                            data-tooltip="Look up"
                                        >
                                            <v-icon>mdi-feature-search-outline</v-icon>
                                        </v-btn>
                                    </div>
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'new_property'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>
                            <!-- LEASE -->
                            <v-row
                                class="form-row"
                                v-if="this.new_category == 'LE'"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Lease</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_lease"
                                        :options="lease_list"
                                        ref="refLease"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'new_lease'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>
                            <!-- DIARY ITEM -->
                            <v-row
                                class="form-row"
                                v-if="this.new_category == 'DI'"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Diary Item</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_diary"
                                        :options="diary_list"
                                        ref="refDiary"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'new_diary'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>
                            <!-- END -->
                            <v-card-actions v-if="this.new_category && this.new_category != 'GE'">
                                <v-spacer></v-spacer>
                                <v-btn
                                    v-if="hide_optional_parameters"
                                    color="primary"
                                    text
                                    small
                                    @click="showOptionalParameters()"
                                    >SHOW OPTIONAL PARAMETERS</v-btn
                                >
                                <v-btn
                                    v-if="show_optional_parameters"
                                    color="primary"
                                    text
                                    small
                                    @click="hideOptionalParameters()"
                                    >HIDE OPTIONAL PARAMETERS</v-btn
                                >
                            </v-card-actions>
                            <v-divider v-if="this.new_category && this.new_category != 'GE'"></v-divider>

                            <!-- SHOW OPTIONAL PARAMETERS -->
                            <!-- COMPANY -->
                            <v-row
                                class="form-row"
                                v-if="show_optional_company"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Company</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_company"
                                        :options="company_list"
                                        ref="refCompany"
                                        dense
                                    />
                                </v-col>
                            </v-row>
                            <!-- PROPERTY -->
                            <v-row
                                class="form-row"
                                v-if="show_optional_property"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Property</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_property"
                                        :options="property_list"
                                        ref="refProperty"
                                        @input="updateOptionalRelatedList()"
                                        dense
                                    />
                                </v-col>
                            </v-row>
                            <!-- LEASE -->
                            <v-row
                                class="form-row"
                                v-if="show_optional_lease"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Lease</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_lease"
                                        :options="lease_list"
                                        ref="refLease"
                                        dense
                                    />
                                </v-col>
                            </v-row>
                            <!-- DIARY ITEM -->
                            <v-row
                                class="form-row"
                                v-if="show_optional_diary"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Diary Item</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_diary"
                                        :options="diary_list"
                                        ref="refDiary"
                                        dense
                                    />
                                </v-col>
                            </v-row>
                            <!-- END -->

                            <!-- SUBJECT -->
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Subject</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-text-field
                                        v-model="new_subject"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'new_subject'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>

                            <!-- DESCRIPTION -->
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Description</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>

                            <!-- ASSIGNEE -->
                            <v-row
                                class="form-row"
                                v-if="step_assignee"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Assignee</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-col>{{ step_assignee_name }}</v-col>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-else
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Assignee</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select
                                        v-model="new_assignee"
                                        :options="dd_assignee_list"
                                        ref="refAssignee"
                                        dense
                                    />
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'new_assignee'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </v-col>
                            </v-row>

                            <!-- ATTACHMENTS -->
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Attachments</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <div v-if="isRemove">
                                        <v-btn
                                            background
                                            depressed
                                            @click="removeFile"
                                            >{{ file_name }}
                                            <v-icon
                                                center
                                                color="primary"
                                            >
                                                close
                                            </v-icon>
                                        </v-btn>
                                    </div>
                                    <div v-else>
                                        <v-btn
                                            text
                                            small
                                            :loading="isSelecting"
                                            @click="onButtonClick"
                                        >
                                            <v-icon color="primary"> attach_file </v-icon>
                                        </v-btn>
                                        <input
                                            ref="uploader"
                                            class="d-none"
                                            type="file"
                                            @change="onFileChanged"
                                        />
                                    </div>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="saveNewTask()"
                        >Save</v-btn
                    >
                    <v-btn
                        color="primary"
                        text
                        @click="closeCreateForm()"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--  LOOK UP BY PARAMETER  -->
        <v-dialog
            v-model="dialogLookUp"
            max-width="90%"
            heigth="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Search Options
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogLookUp = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div>
                        <v-row>
                            <v-col
                                cols="12"
                                md="3"
                            >
                                <v-list
                                    flat
                                    dense
                                >
                                    <v-subheader><h5>Filter By</h5></v-subheader>
                                    <v-list-item-group
                                        v-model="settings"
                                        multiple
                                        active-class=""
                                    >
                                        <v-list-item>
                                            <template v-slot:default="{ active }">
                                                <v-list-item-action>
                                                    <v-checkbox
                                                        v-model="checkPropertyManager"
                                                        :input-value="active"
                                                    ></v-checkbox>
                                                </v-list-item-action>
                                                <v-list-item-content>
                                                    <v-list-item-title>{{ property_manager_label }}</v-list-item-title>
                                                    <v-list-item-subtitle
                                                        >Choose {{ property_manager_label }} to
                                                        filter</v-list-item-subtitle
                                                    >
                                                </v-list-item-content>
                                            </template>
                                        </v-list-item>

                                        <v-list-item>
                                            <template v-slot:default="{ active }">
                                                <v-list-item-action>
                                                    <v-checkbox
                                                        v-model="checkOwner"
                                                        :input-value="active"
                                                    ></v-checkbox>
                                                </v-list-item-action>
                                                <v-list-item-content>
                                                    <v-list-item-title>Owner</v-list-item-title>
                                                    <v-list-item-subtitle>Choose owner to filter</v-list-item-subtitle>
                                                </v-list-item-content>
                                            </template>
                                        </v-list-item>
                                        <v-list-item>
                                            <template v-slot:default="{ active }">
                                                <v-list-item-action>
                                                    <v-checkbox
                                                        v-model="checkLease"
                                                        :input-value="active"
                                                    ></v-checkbox>
                                                </v-list-item-action>
                                                <v-list-item-content>
                                                    <v-list-item-title>Lease</v-list-item-title>
                                                    <v-list-item-subtitle>Choose lease to filter</v-list-item-subtitle>
                                                </v-list-item-content>
                                            </template>
                                        </v-list-item>
                                        <v-list-item>
                                            <template v-slot:default="{ active }">
                                                <v-list-item-action>
                                                    <v-checkbox
                                                        v-model="checkAddress"
                                                        :input-value="active"
                                                    ></v-checkbox>
                                                </v-list-item-action>
                                                <v-list-item-content>
                                                    <v-list-item-title>Address</v-list-item-title>
                                                    <v-list-item-subtitle
                                                        >Entered search text will base on the property
                                                        address</v-list-item-subtitle
                                                    >
                                                </v-list-item-content>
                                            </template>
                                        </v-list-item>
                                    </v-list-item-group>
                                </v-list>
                                <v-divider
                                    class="vertical-divider"
                                    vertical
                                ></v-divider>
                            </v-col>
                            <v-col
                                cols="12"
                                md="9"
                            >
                                <v-row v-if="showPropertyManager && checkPropertyManager">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        >{{ property_manager_label }}</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="6"
                                        md="6"
                                        class="form-input"
                                    >
                                        <cirrus-single-select
                                            v-model="modelPropertyManager"
                                            :options="propertyManagerList"
                                            @input="changePropertyManager()"
                                            dense
                                        ></cirrus-single-select>
                                    </v-col>
                                </v-row>
                                <v-row v-if="showOwner && checkOwner">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        >Owner</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="6"
                                        md="6"
                                        class="form-input"
                                    >
                                        <cirrus-single-select
                                            v-model="modelOwner"
                                            :options="ownerList"
                                            @input="changePropertyOwner()"
                                            dense
                                        ></cirrus-single-select>
                                    </v-col>
                                </v-row>

                                <v-row v-if="showLease && checkLease">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        >Lease</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="6"
                                        md="6"
                                        class="form-input"
                                    >
                                        <cirrus-single-select
                                            v-model="modelLease"
                                            :options="lease_list"
                                            @input="changeLease()"
                                            dense
                                        ></cirrus-single-select>
                                    </v-col>
                                </v-row>

                                <div class="c8-page-table">
                                    <table>
                                        <tbody>
                                            <tr class="form-row">
                                                <td>
                                                    <v-text-field
                                                        v-model="searchTask"
                                                        placeholder="Search..."
                                                        dense
                                                    />
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <v-data-table
                                        :headers="searchHeaders"
                                        :items="searchItems"
                                        :search="searchTask"
                                        @click:row="selectedSearch"
                                        dense
                                    >
                                    </v-data-table>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <br /><br />
                <br /><br />
                <br /><br />
                <br /><br />
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogLookUp = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import Vue from 'vue';
import global_mixins from '../../../../plugins/mixins';
import { mapActions, mapMutations, mapState } from 'vuex';
import { bus } from '../../../../plugins/bus';

export default {
    name: 'TaskCreateForm',
    props: ['visible'],
    data() {
        return {
            // DEFAULT
            property_manager_label: 'Property Manager',
            page_title: 'Create Task',
            loading_page_setting: false,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            editor: ClassicEditor,
            editorConfig: {
                fontSize: {},
                toolbar: {
                    items: [
                        'bold',
                        'italic',
                        '|',
                        'bulletedList',
                        'numberedList',
                        '|',
                        'indent',
                        'outdent',
                        '|',
                        'undo',
                        'redo',
                    ],
                },
                language: 'en',
            },
            // OPTIONS
            type_list: [],
            company_list: [],
            property_list: [],
            lease_list: [],
            diary_list: [],

            // FIELD INPUT FOR CREATING NEW
            new_category: '',
            new_subject: '',
            new_type: '',
            new_description: '',
            new_assignee: '',
            // new_attachments: "",
            file_attached: '',

            new_company: '',
            new_property: '',
            new_lease: '',
            new_diary: '',

            //OPTIONAL PARAMETERS
            optional_parameters: 'show optional parameters',
            show_optional_parameters: false,
            hide_optional_parameters: true,
            show_optional_company: false,
            show_optional_property: false,
            show_optional_lease: false,
            show_optional_diary: false,

            isSelecting: false,
            selectedFile: '',
            file_name: '',
            isRemove: false,

            step_assignee: false,
            step_assignee_name: '',

            dialogLookUp: false,
            checkPropertyManager: '',
            checkOwner: '',
            checkLease: '',
            checkAddress: '',

            showPropertyManager: false,
            propertyManagerList: [],
            modelPropertyManager: '',

            showOwner: false,
            ownerList: [],
            modelOwner: '',

            showLease: false,
            leaseList: [],
            modelLease: '',

            searchTask: '',
            defaultSearchHeaders: [
                { text: 'Property', value: 'property_name', sortable: false },
                { text: 'Lease', value: 'lease_name', sortable: false },
                { text: 'Company', value: 'company_name', sortable: false },
                // {text: 'Address', value: 'property_address', sortable: false, selected: false },
            ],
            searchHeaders: [
                { text: 'Property', value: 'property_name', sortable: false },
                { text: 'Lease', value: 'lease_name', sortable: false },
                { text: 'Company', value: 'company_name', sortable: false },
                // {text: 'Address', value: 'property_address', sortable: false, selected: false },
            ],
            searchItems: [],
        };
    },
    mounted() {
        this.fetchCategoryList();
        this.fetchAssigneeList();
        this.loadCountryDefaults();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'dd_category_list', 'dd_assignee_list']),
        showCreateForm: {
            get() {
                return this.visible;
            },
            set(value) {
                if (!value) {
                    this.resetAllField();
                    this.hideOptionalParameters();
                    this.$emit('close');
                }
            },
        },
        form_type: {
            get() {
                return this.options;
            },
        },
    },
    methods: {
        ...mapActions(['fetchCategoryList', 'fetchAssigneeList']),
        ...mapMutations(['SET_DD_CATEGORY_LIST', 'SET_DD_ASSIGNEE_LIST']),
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.property_manager_label = this.ucwords(this.country_defaults.property_manager);
            });
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        // LOAD OPTION
        // For Type
        fetchTypeList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('task_category', this.new_category);
            let apiUrl = this.cirrus8_api_url + 'api/task-management/get-type-option';
            axios.post(apiUrl, formData).then((response) => {
                this.type_list = response.data;
            });
        },
        // For Company
        fetchCompanyList: function () {
            var formData = new FormData();
            formData.append('show_active', 1);
            let apiUrlCompany = 'company-dropdown-list';
            axios.post(apiUrlCompany, formData).then((response) => {
                this.company_list = response.data;
            });
        },
        // For Property
        fetchPropertyList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('active_only', 1);
            let apiUrlProperty = 'loadAPIPropertyDropDownList';
            axios.post(apiUrlProperty, formData).then((response) => {
                this.property_list = response.data.data;
            });
        },
        // For Lease
        fetchLeaseList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('propertyID', this.new_property);
            let apiUrlLease = this.cirrus8_api_url + 'api/task-management/get-lease-option';
            axios.post(apiUrlLease, formData).then((response) => {
                this.lease_list = response.data;
            });
        },
        // For Diary
        fetchDiaryList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('propertyID', this.new_property);
            let apiUrlDiary = this.cirrus8_api_url + 'api/ui/fetch/param-diary-property-list';
            axios.post(apiUrlDiary, formData).then((response) => {
                this.diary_list = response.data;
            });
        },
        // For Property Manager
        fetchPropertyManagerList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            let apiUrlProperty = this.cirrus8_api_url + 'api/loadAPIPortfolioManagersList';
            axios.post(apiUrlProperty, formData).then((response) => {
                this.propertyManagerList = response.data.data;
            });
        },
        // For Owner
        fetchOwnerList: function () {
            var formData = new FormData();
            formData.append('show_active', 1);
            let apiUrlCompany = 'company-dropdown-list';
            axios.post(apiUrlCompany, formData).then((response) => {
                this.ownerList = response.data;
            });
        },
        // For Search Iterms
        fetchSearchItems: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            if (this.modelPropertyManager) {
                formData.append('property_manager', this.modelPropertyManager);
            }
            if (this.modelOwner) {
                formData.append('property_owner', this.modelOwner);
            }
            if (this.modelLease) {
                formData.append('lease_code', this.modelLease);
            }
            let apiUrlSearch = this.cirrus8_api_url + 'api/task-management/search-list';
            axios.post(apiUrlSearch, formData).then((response) => {
                this.searchItems = response.data;
            });
        },

        reloadTypeOption: function () {
            this.company_list = [];
            this.property_list = [];
            this.lease_list = [];
            this.diary_list = [];

            this.fetchTypeList();
            switch (this.new_category) {
                case 'CO':
                    this.fetchCompanyList();
                    break;
                case 'PR':
                case 'LE':
                case 'DI':
                    this.fetchPropertyList();
                    break;
            }
            this.resetDropDown();
            this.hideOptionalParameters();
        },
        closeCreateForm: function () {
            this.showCreateForm = false;
            this.new_category = '';
            this.resetAllField();
            this.$emit('close');
        },
        saveNewTask: function () {
            let validate = true;
            if (this.new_category == '') {
                this.$noty.error('You have not specified a task category.');
                validate = false;
            }

            if (this.new_category == 'CO') {
                if (this.new_company == '') {
                    this.$noty.error('You have not specified a company.');
                    validate = false;
                }
            }
            if (this.new_category == 'PR' || this.new_category == 'LE' || this.new_category == 'DI') {
                if (this.new_property == '') {
                    this.$noty.error('You have not specified a property.');
                    validate = false;
                }
            }
            if (this.new_category == 'LE') {
                if (this.new_lease == '') {
                    this.$noty.error('You have not specified a lease.');
                    validate = false;
                }
            }
            if (this.new_category == 'DI') {
                if (this.new_diary == '') {
                    this.$noty.error('You have not specified a diary item.');
                    validate = false;
                }
            }

            if (this.new_type == '' || this.new_type == null) {
                this.$noty.error('You have not specified a type.');
                validate = false;
            }

            if (this.new_subject == '') {
                this.$noty.error('You have not specified a subject.');
                validate = false;
            }

            if (this.new_assignee == '' || this.new_assignee == null) {
                this.$noty.error('You have not specified an assignee.');
                validate = false;
            }
            // submit form if no error
            if (validate) {
                this.loading_setting = true;
                var formData = new FormData();
                formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);

                formData.append('form_mode', 'create');
                formData.append('task_code', this.new_category);
                formData.append('task_type', this.new_type);
                formData.append('task_subject', this.new_subject);
                formData.append('task_description', this.new_description);
                formData.append('task_assignee', this.new_assignee);
                formData.append('task_company', this.new_company);
                formData.append('task_property', this.new_property);
                formData.append('task_lease', this.new_lease);
                formData.append('task_diary', this.new_diary);
                formData.append('task_filename', this.file_name);
                formData.append('file_attached', this.file_attached);
                // Create Task details
                let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
                document.getElementById('ngLoader-UI').style.display = 'block';
                axios.post(apiUrl, formData).then((response) => {
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    this.status = response.data.status;
                    if (this.status == 'success') {
                        this.resetAllField();
                        // this.loadTaskList();
                        this.$noty.success('Task added.');
                        // reset fields
                        // this.$emit('close');
                        this.$emit('reload-list');
                        this.$emit('close');
                        this.loading_setting = false;
                    } else {
                        this.$noty.error('error message');
                        this.loading_setting = false;
                    }
                });
            }
        },
        // RESET FIELD
        resetDropDown: function () {
            if (this.new_type) this.$refs.refType.select_val = '';
            this.new_type = '';
            if (this.new_company) this.$refs.refCompany.select_val = '';
            this.new_company = '';
            if (this.new_property) this.$refs.refProperty.select_val = '';
            this.new_property = '';
            if (this.new_lease) this.$refs.refLease.select_val = '';
            this.new_lease = '';
            if (this.new_diary) this.$refs.refDiary.select_val = '';
            this.new_diary = '';
        },
        resetAllField: function () {
            this.resetDropDown();
            if (this.new_category) this.$refs.refCategory.select_val = '';
            this.new_category = '';
            if (this.new_assignee && !this.step_assignee) this.$refs.refAssignee.select_val = '';
            this.new_assignee = '';
            this.new_subject = '';
            this.new_description = '';
            this.file_name = '';
            this.isRemove = false;
            this.step_assignee = false;
            this.step_assignee_name = '';
        },

        // OPTIONAL PARAMETERS
        showOptionalParameters: function () {
            this.show_optional_parameters = true;
            this.hide_optional_parameters = false;

            switch (this.new_category) {
                case 'CO':
                    this.show_optional_company = false;
                    this.show_optional_property = true;
                    this.show_optional_lease = true;
                    this.show_optional_diary = true;
                    break;
                case 'PR':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = true;
                    this.show_optional_diary = true;
                    break;
                case 'LE':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = false;
                    this.show_optional_diary = true;
                    break;
                case 'DI':
                    this.show_optional_company = true;
                    this.show_optional_property = false;
                    this.show_optional_lease = true;
                    this.show_optional_diary = false;
                    break;
            }
        },
        hideOptionalParameters: function () {
            this.show_optional_parameters = false;
            this.hide_optional_parameters = true;
            this.show_optional_company = false;
            this.show_optional_property = false;
            this.show_optional_lease = false;
            this.show_optional_diary = false;
        },

        updateRelatedList: function () {
            if (this.new_category == 'CO') {
                this.fetchPropertyList();
            } else {
                this.fetchCompanyList();
                this.fetchLeaseList();
                this.fetchDiaryList();
            }
        },

        updateOptionalRelatedList: function () {
            this.fetchLeaseList();
            this.fetchDiaryList();
        },
        // getIdOfUploadButton: function(id) {
        //   return 'fileUploadDocs_' + id;
        // },

        onButtonClick() {
            this.isSelecting = true;
            window.addEventListener(
                'focus',
                () => {
                    this.isSelecting = false;
                },
                { once: true },
            );

            this.$refs.uploader.click();
        },
        onFileChanged(e) {
            var files = e.target.files || e.dataTransfer.files;
            if (files.length && files[0].type == 'application/pdf') {
                this.file_attached = files[0];
                this.file_name = files[0].name;
                this.isRemove = true;
            } else {
                this.$noty.error('Invalid file type. Please upload only pdf file.');
            }
        },
        removeFile: function (item) {
            this.file_name = '';
            this.isRemove = false;
        },

        changeTaskType: function () {
            if (this.new_type) {
                var formData = new FormData();
                formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);
                formData.append('typeID', this.new_type);
                formData.append('stepID', 0);
                let apiUrlDiary = this.cirrus8_api_url + 'api/task-management/get-task-assignee';
                axios.post(apiUrlDiary, formData).then((response) => {
                    if (response.data.task_assignee) {
                        this.step_assignee = true;
                        this.step_assignee_name = response.data.task_assignee_name;
                        this.new_assignee = response.data.task_assignee;
                    } else {
                        this.step_assignee = false;
                        this.step_assignee_name = '';
                        this.new_assignee = '';
                    }
                });
            }
        },

        showLookup: function () {
            this.fetchSearchItems();
            this.dialogLookUp = true;
        },
        getHeaderIndex: function (value) {
            let arr = this.searchHeaders;
            let indexValue = 0;
            for (var i = 0; i < arr.length; i++) {
                if (arr[i]['value'] == value) {
                    indexValue = i;
                    break;
                }
            }
            return indexValue;
        },
        selectedSearch: function (row) {
            this.populateDropdownOptions(row);
            this.dialogLookUp = false;
        },

        populateDropdownOptions: function (row) {
            this.new_lease = row.lease_code;
            this.new_company = row.company_code;
            this.new_property = row.property_code;
            this.updateRelatedList();
        },

        changePropertyManager: function () {
            this.fetchSearchItems();
        },
        changePropertyOwner: function () {
            this.fetchSearchItems();
        },
        changeLease: function () {
            this.fetchSearchItems();
        },
    },
    watch: {
        options: function () {},
        checkPropertyManager: function () {
            if (this.checkPropertyManager) {
                this.fetchPropertyManagerList();
                this.showPropertyManager = true;
                // this.searchHeaders = this.searchHeaders.concat({text: 'Property Manager', value: 'property_manager', sortable: false, selected: false });
            } else {
                // this.$delete(this.searchHeaders, this.getHeaderIndex('property_manager'))
                this.showPropertyManager = false;
            }
        },
        checkOwner: function () {
            if (this.checkOwner) {
                this.fetchOwnerList();
                // this.searchHeaders = this.searchHeaders.concat({text: 'Owner', value: 'property_owner', sortable: false, selected: false });
                this.showOwner = true;
            } else {
                // this.$delete(this.searchHeaders, this.getHeaderIndex('property_owner'));
                this.showOwner = false;
            }
        },
        checkLease: function () {
            if (this.checkLease) {
                this.fetchLeaseList();
                // this.searchHeaders = this.searchHeaders.concat({text: 'Owner', value: 'property_owner', sortable: false, selected: false });
                this.showLease = true;
            } else {
                // this.$delete(this.searchHeaders, this.getHeaderIndex('property_owner'));
                this.showLease = false;
            }
        },
        checkAddress: function () {
            if (this.checkAddress) {
                this.searchHeaders = this.searchHeaders.concat({
                    text: 'Address',
                    value: 'property_address',
                    sortable: false,
                    selected: false,
                });
            } else {
                this.$delete(this.searchHeaders, this.getHeaderIndex('property_address'));
            }
        },
    },
    mixins: [global_mixins],
};
</script>

<style scoped></style>
