<template>
    <div>
        <v-skeleton-loader
            class="mx-auto"
            type="table"
            :loading="loading"
        >
            <v-card elevation="0">
                <v-card-title>
                    <v-btn
                        tile
                        color="primary"
                        @click="showAllTask()"
                    >
                        View All Task
                    </v-btn>
                    <v-spacer></v-spacer>
                    <v-text-field
                        v-model="search"
                        append-icon="search"
                        label="Search"
                        single-line
                        hide-details
                    ></v-text-field>
                </v-card-title>
                <v-data-table
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="task_list"
                    :search="search"
                >
                    <template v-slot:item.task_subject="{ item }">
                        <div>
                            <span
                                v-if="item.task_subject_x"
                                class="d-inline-block text-truncate"
                                style="max-width: 150px"
                                v-html="item.task_subject"
                                @click="showCompleteText(item, 'subject')"
                            >
                            </span>
                            <span
                                v-else
                                v-html="item.task_subject"
                            ></span>
                        </div>
                    </template>
                    <template v-slot:item.task_description="{ item }">
                        <div>
                            <span
                                v-if="item.task_description_x"
                                class="d-inline-block text-truncate"
                                style="max-width: 150px"
                                v-html="item.task_description"
                                @click="showCompleteText(item, 'description')"
                            >
                            </span>
                            <span
                                v-else
                                v-html="item.task_description"
                            ></span>
                        </div>
                    </template>
                    <template v-slot:item.task_step="{ item }">
                        <div>
                            <span
                                v-if="item.task_step_x"
                                class="d-inline-block text-truncate"
                                style="max-width: 150px"
                                v-html="item.task_step"
                                @click="showCompleteText(item, 'step')"
                            >
                            </span>
                            <span
                                v-else
                                v-html="item.task_step"
                            ></span>
                        </div>
                    </template>
                </v-data-table>
            </v-card>
        </v-skeleton-loader>

        <!-- SHOW COMPLETE TEXT -->
        <v-dialog
            v-model="dialogShowCompleteText"
            max-width="500px"
        >
            <v-card>
                <v-card-title class="headline">
                    {{ field_name }}
                </v-card-title>
                <v-card-text><span v-html="complete_text"></span></v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        @click="dialogShowCompleteText = false"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF SHOW COMPLETE TEXT -->
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
export default {
    props: {
        property_code: { type: Object, default: { field_key: '', field_value: '' } },
        lease_code: { type: Object, default: { field_key: '', field_value: '' } },
    },
    data() {
        return {
            loading: true,
            search: '',
            headers: [
                { text: 'Task ID', value: 'task_id', sortable: false, width: '80' },
                { text: 'Task Subject', value: 'task_subject', sortable: false, width: '150' },
                { text: 'Task Description', value: 'task_description', sortable: false, width: '150' },
                { text: 'Task Type', value: 'task_type', sortable: false },
                { text: 'Task Step', value: 'task_step', sortable: false },
                { text: 'Task Status', value: 'task_status', sortable: false },
                { text: 'Task Assignee', value: 'task_assignee', sortable: false },
            ],
            task_list: [],

            dialogShowCompleteText: false,
            field_name: '',
            complete_text: '',
            status: "'NOTSTARTED','PROGRESS'",
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadTaskList();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    methods: {
        loadTaskList: function () {
            if (this.property_code.field_key && this.lease_code.field_key) {
                this.loading = true;
                var form_data = new FormData();
                form_data.append('un', this.username);
                form_data.append('current_db', this.current_db);
                form_data.append('user_type', this.user_type);
                form_data.append('task_id', this.task_id);
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('lease_code', this.lease_code.field_key);
                form_data.append('status', this.status);
                axios
                    .post(this.cirrus8_api_url + 'api/task-management/get-task-list-view', form_data)
                    .then((response) => {
                        this.loading = false;
                        this.task_list = response.data.task_list;
                    });
            }
        },
        showCompleteText: function (data, type) {
            if (type == 'subject') {
                this.complete_text = data.task_subject_x;
                this.field_name = 'Task Subject';
            } else if (type == 'step') {
                this.complete_text = data.task_step_x;
                this.field_name = 'Task Step';
            } else {
                this.complete_text = data.task_description_x;
                this.field_name = 'Task Description';
            }
            this.dialogShowCompleteText = true;
        },
        showAllTask: function () {
            this.status = '';
            this.loadTaskList();
        },
    },
    created() {
        bus.$on('loadTaskList', (data) => {
            this.loadTaskList();
        });
    },
    mixins: [global_mixins],
};
</script>

<style scoped></style>
