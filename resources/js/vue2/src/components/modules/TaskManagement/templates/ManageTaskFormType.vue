<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <v-toolbar flat>
            <v-toolbar-title>
                <h1
                    class="ui header"
                    style="color: #7f8c8d; font-size: 2vw; font-weight: normal; letter-spacing: -2px"
                >
                    {{ page_title }}
                </h1>
            </v-toolbar-title>

            <div class="flex-grow-1"></div>
            <!-- ADDING NEW TASK -->
            <v-btn
                color="primary"
                class="v-step-new-lease-button"
                @click="clear_type()"
            >
                <v-icon>add</v-icon>
                New Type
            </v-btn>
        </v-toolbar>

        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>

        <!-- TASK LIST -->
        <div class="page-list">
            <div class="c8-page-table">
                <table>
                    <tbody>
                        <tr class="form-row">
                            <td>
                                <v-text-field
                                    v-model="searchTask"
                                    placeholder="Search..."
                                    dense
                                />
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- TASK LIST DATA TABLE -->
                <v-data-table
                    :headers="listHeaders"
                    :items="listItems"
                    :search="searchTask"
                    dense
                >
                    <template v-slot:item.task_steps="{ item }">
                        <div class="form-row no-border-line">
                            <v-btn
                                @click="
                                    stepsPrimary = item.task_primary;
                                    stepsRelated = item.task_related;
                                    stepsTask = item.task_description;
                                    loadSteps();
                                "
                                depressed
                                elevation="0"
                                tile
                                small
                                color="normal"
                                ><v-icon>list</v-icon></v-btn
                            >
                        </div>
                    </template>

                    <template v-slot:item.action="{ item }">
                        <div class="form-row no-border-line">
                            <v-btn
                                depressed
                                elevation="0"
                                tile
                                small
                                color="normal"
                                @click="
                                    edit_type(
                                        item.status_field,
                                        item.task_description,
                                        item.task_primary,
                                        item.task_related_code,
                                        item.task_related,
                                    )
                                "
                                ><v-icon>edit</v-icon></v-btn
                            >
                            <v-btn
                                depressed
                                elevation="0"
                                tile
                                small
                                color="normal"
                                @click="delete_type(item.task_primary)"
                                ><v-icon>delete</v-icon></v-btn
                            >
                        </div>
                    </template>
                </v-data-table>
            </div>
        </div>
        <!-- END OF TASK LIST -->

        <!-- ADDING NEW TASK -->
        <v-dialog
            v-model="task_Type"
            hide-overlay
            transition="dialog-bottom-transition"
            scrollable
            content-class="c8-page"
            max-width="500"
        >
            <v-card height="330">
                <v-card-title class="headline">
                    Task Type
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="task_Type = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>

                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="3"
                                sm="3"
                                md="3"
                                class=""
                                >Task</v-col
                            >
                            <v-col
                                xs="5"
                                sm="5"
                                md="5"
                                class=""
                            >
                                <cirrus-single-select
                                    v-model="model_related_task"
                                    :options="dd_category_list"
                                    ref="refCategory"
                                    dense
                                />
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="3"
                                sm="3"
                                md="3"
                                class=""
                                >Description</v-col
                            >
                            <v-col
                                xs="5"
                                sm="5"
                                md="5"
                                class=""
                            >
                                <cirrus-input
                                    v-model="task_description"
                                    :edit_form="true"
                                ></cirrus-input>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="3"
                                sm="3"
                                md="3"
                                class=""
                                >Status</v-col
                            >
                            <v-col
                                xs="5"
                                sm="5"
                                md="5"
                                class=""
                            >
                                <v-btn-toggle
                                    class="v-step-search-type"
                                    v-model="task_status"
                                >
                                    <v-btn
                                        small
                                        tile
                                        text
                                    >
                                        Active
                                    </v-btn>
                                    <v-btn
                                        small
                                        tile
                                        text
                                    >
                                        Inactive
                                    </v-btn>
                                </v-btn-toggle>
                            </v-col>
                        </v-row>
                    </div>

                    <v-card-actions>
                        <v-spacer />
                        <v-btn
                            class="v-step-save-2-button"
                            color="primary"
                            dark
                            depressed
                            tile
                            small
                            @click="saveType()"
                        >
                            save
                        </v-btn>
                        <v-btn
                            class="v-step-save-2-button"
                            color="primary"
                            dark
                            depressed
                            tile
                            small
                            @click="task_Type = false"
                        >
                            close
                        </v-btn>
                    </v-card-actions>
                </v-card-text>
            </v-card>
        </v-dialog>
        <!-- END OF ADDING NEW TASK -->

        <v-dialog
            v-model="task_steps"
            hide-overlay
            transition="dialog-bottom-transition"
            scrollable
            content-class="c8-page"
            max-width="800"
        >
            <v-card height="500">
                <v-card-title class="headline">
                    Steps ( {{ stepsRelated }} - {{ stepsTask }} )
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="task_steps = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>

                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="4"
                                sm="4"
                                md="4"
                                class="text-center"
                            >
                                <span class="form-label">Description</span>
                            </v-col>
                            <v-col
                                xs="5"
                                sm="5"
                                md="5"
                                class="text-center"
                            >
                                <span class="form-label">Assignee</span>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-for="(item, index) in stepsItem"
                            :key="index"
                        >
                            <v-col
                                xs="4"
                                sm="4"
                                md="4"
                                class=""
                            >
                                <cirrus-input
                                    :edit_form="true"
                                    placeholder="Input Description"
                                    v-model="item.steps"
                                ></cirrus-input>
                            </v-col>
                            <v-col
                                xs="5"
                                sm="5"
                                md="5"
                                class=""
                            >
                                <cirrus-single-select
                                    v-model="item.assignee"
                                    :options="dd_assignee_list"
                                    dense
                                    :ref="index + '-assignee'"
                                />
                            </v-col>
                            <v-col
                                xs="3"
                                sm="3"
                                md="23"
                                class=""
                            >
                                <v-btn
                                    @click="move_up_steps(index)"
                                    depressed
                                    elevation="0"
                                    tile
                                    small
                                    color="normal"
                                    ><v-icon>arrow_drop_up</v-icon></v-btn
                                >
                                <v-btn
                                    @click="move_down_steps(index)"
                                    depressed
                                    elevation="0"
                                    tile
                                    small
                                    color="normal"
                                    ><v-icon>arrow_drop_down</v-icon></v-btn
                                >
                                <v-btn
                                    :disabled="item.delete == 0 ? false : true"
                                    @click="delete_steps(index)"
                                    depressed
                                    elevation="0"
                                    tile
                                    small
                                    color="normal"
                                    ><v-icon>delete</v-icon></v-btn
                                >
                            </v-col>
                        </v-row>
                    </div>

                    <v-card-actions>
                        <v-spacer />
                        <v-btn
                            color="primary"
                            class="v-step-save-2-button"
                            dark
                            depressed
                            tile
                            small
                            @click.stop="add_steps()"
                        >
                            <v-icon>add</v-icon>
                        </v-btn>

                        <v-btn
                            class="v-step-save-2-button"
                            color="primary"
                            dark
                            depressed
                            tile
                            small
                            @click="saveSteps()"
                        >
                            save
                        </v-btn>
                        <v-btn
                            class="v-step-save-2-button"
                            color="primary"
                            dark
                            depressed
                            tile
                            small
                            @click="task_steps = false"
                        >
                            close
                        </v-btn>
                    </v-card-actions>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import Vue from 'vue';

import { mapActions, mapMutations, mapState } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

const Swal = require('sweetalert2');

export default {
    name: 'ManageTaskFormTemplate',
    data() {
        return {
            // Default Values
            task_status: 0,
            page_title: 'Task Settings',
            loading_page_setting: false,
            error_server_msg: {},
            error_server_msg2: [],
            form_mode: 'listing',

            // RELATED TO
            task_primary: '',
            task_description: '',
            model_related_task: '',
            type_list: [],

            task_Type: false,
            task_steps: false,

            searchTask: '',
            listHeaders: [
                { text: 'Task', value: 'task_related', sortable: false, width: '10%' },
                { text: 'Description', value: 'task_description', sortable: false, width: '15%' },
                { text: 'Status', value: 'status', sortable: false, width: '10%' },
                { text: 'Steps', value: 'task_steps', sortable: false, width: '15%' },
                { text: '', value: 'action', sortable: false, width: '10%' },
            ],
            listItems: [],

            stepsRelated: '',
            stepsTask: '',
            stepsItem: [],
            stepsPrimary: '',

            formData: {
                currentDB: localStorage.getItem('currentDB'),
                user_type: localStorage.getItem('user_type'),
                un: localStorage.getItem('un'),
            },
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.fetchCategoryList();
        this.fetchAssigneeList();
        this.loadType();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'dd_category_list', 'dd_assignee_list']),
    },
    methods: {
        ...mapActions(['fetchCategoryList', 'fetchAssigneeList']),
        ...mapMutations(['SET_DD_CATEGORY_LIST', 'SET_DD_ASSIGNEE_LIST']),
        delete_type: function (primary) {
            let dis = this;
            Swal.fire({
                title: 'Are you sure?',
                text: 'This will delete task management type?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ok',
                cancelButtonText: 'Cancel',
            }).then((result) => {
                if (result.value) {
                    dis.formData.primary = primary;
                    document.getElementById('ngLoader-UI').style.display = 'block';
                    axios
                        .post(dis.cirrus8_api_url + 'api/task-management/delete-task-type', dis.formData)
                        .then((response) => {
                            if (response.data.status == 'success') {
                                dis.listItems = response.data.task_list;
                                this.$noty.success('Successfully deleted');
                            } else {
                                this.$noty.error(response.data.msg);
                            }
                            document.getElementById('ngLoader-UI').style.display = 'none';
                        });
                }
            });
        },
        clear_type: function () {
            this.task_Type = true;
            this.task_primary = '';
            this.task_description = '';
            this.model_related_task = '';
            if (typeof this.$refs.refCategory !== 'undefined') this.$refs.refCategory.select_val = '';
        },
        add_steps: function (index) {
            this.stepsItem.push({ steps: '', defaultSteps: 0, assignee: '', id: 0, delete: 0 });
        },
        move_up_steps: function (index) {
            if (index == 0) return;
            let old_desc = this.stepsItem[index - 1].steps;
            let old_assignee = this.stepsItem[index - 1].assignee;
            let old_assignee_name = (this.$refs[index - 1 + '-assignee'][0].select_val = '');
            this.stepsItem[index - 1].steps = this.stepsItem[index].steps;
            this.stepsItem[index].steps = old_desc;
            this.stepsItem[index - 1].assignee = this.stepsItem[index].assignee;
            this.stepsItem[index].assignee = old_assignee;
            this.$refs[index - 1 + '-assignee'][0].select_val = this.$refs[index + '-assignee'][0].select_val;
            this.$refs[index + '-assignee'][0].select_val = old_assignee_name;
            let old_id = this.stepsItem[index - 1].id;
            this.stepsItem[index - 1].id = this.stepsItem[index].id;
            this.stepsItem[index].id = old_id;
            let old_delete = this.stepsItem[index - 1].delete;
            this.stepsItem[index - 1].delete = this.stepsItem[index].delete;
            this.stepsItem[index].delete = old_delete;
        },
        move_down_steps: function (index) {
            if (typeof this.stepsItem[index + 1] == 'undefined') return;
            let old_desc = this.stepsItem[index + 1].steps;
            let old_assignee = this.stepsItem[index + 1].assignee;
            let old_assignee_name = (this.$refs[index + 1 + '-assignee'][0].select_val = '');
            this.stepsItem[index + 1].steps = this.stepsItem[index].steps;
            this.stepsItem[index].steps = old_desc;
            this.stepsItem[index + 1].assignee = this.stepsItem[index].assignee;
            this.stepsItem[index].assignee = old_assignee;
            this.$refs[index + 1 + '-assignee'][0].select_val = this.$refs[index + '-assignee'][0].select_val;
            this.$refs[index + '-assignee'][0].select_val = old_assignee_name;
            let old_id = this.stepsItem[index + 1].id;
            this.stepsItem[index + 1].id = this.stepsItem[index].id;
            this.stepsItem[index].id = old_id;
            let old_delete = this.stepsItem[index + 1].delete;
            this.stepsItem[index + 1].delete = this.stepsItem[index].delete;
            this.stepsItem[index].delete = old_delete;
        },
        delete_steps: function (index) {
            this.$refs[index + '-assignee'][0].select_val = '';
            this.stepsItem[index].assignee = '';
            this.$delete(this.stepsItem, index);
        },
        remove_steps_default: function (index) {
            for (let x = 0; x < this.stepsItem.length; x++) if (index != x) this.stepsItem[x].defaultSteps = 0;
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        loadType: function () {
            let form_data = new FormData();
            form_data.append('current_db', this.current_db);
            form_data.append('un', this.username);
            form_data.append('user_type', this.user_type);
            document.getElementById('ngLoader-UI').style.display = 'block';
            axios.post(this.cirrus8_api_url + 'api/task-management/get-task-type', form_data).then((response) => {
                document.getElementById('ngLoader-UI').style.display = 'none';
                this.listItems = response.data.task_list;
            });
        },
        loadSteps: function () {
            let form_data = new FormData();
            form_data.append('current_db', this.current_db);
            form_data.append('un', this.username);
            form_data.append('user_type', this.user_type);
            form_data.append('task_primary', this.stepsPrimary);
            document.getElementById('ngLoader-UI').style.display = 'block';
            axios.post(this.cirrus8_api_url + 'api/task-management/get-task-steps', form_data).then((response) => {
                document.getElementById('ngLoader-UI').style.display = 'none';
                this.stepsItem = response.data.task_list;
                this.task_steps = true;
            });
        },
        saveSteps: function () {
            this.formData.task_primary = this.stepsPrimary;
            this.formData.steps = this.stepsItem;
            document.getElementById('ngLoader-UI').style.display = 'block';
            axios.post(this.cirrus8_api_url + 'api/task-management/save-task-steps', this.formData).then((response) => {
                document.getElementById('ngLoader-UI').style.display = 'none';
                if (response.data.status == 'success') {
                    this.task_steps = false;
                    this.$noty.success('Successfully added');
                } else {
                    this.$noty.error(response.data.msg);
                }
            });
        },
        saveType: function () {
            let form_data = new FormData();
            form_data.append('current_db', this.current_db);
            form_data.append('un', this.username);
            form_data.append('user_type', this.user_type);
            form_data.append('task_primary', this.task_primary);
            form_data.append('task_description', this.task_description);
            form_data.append('task_related', this.model_related_task);
            form_data.append('task_status', this.task_status);
            document.getElementById('ngLoader-UI').style.display = 'block';
            axios.post(this.cirrus8_api_url + 'api/task-management/save-task-type', form_data).then((response) => {
                document.getElementById('ngLoader-UI').style.display = 'none';
                if (response.data.status == 'success') {
                    this.listItems = response.data.task_list;
                    this.task_Type = false;
                    this.$noty.success('Successfully added');
                } else {
                    this.$noty.error(response.data.msg);
                }
            });
        },
        edit_type: function (stats, desc, primary, code, related) {
            this.task_status = stats;
            this.task_primary = primary;
            this.task_description = desc;
            this.model_related_task = code;
            this.task_Type = true;
        },
    },
    watch: {},
    mixins: [global_mixins],
};
</script>
<style scoped></style>
