<style>
.ck-editor__editable_inline {
    min-height: 100px;
}

.task-edit-modal #taskEditModalBody {
    width: 100%;
    transition-property: width;
    transition-timing-function: ease-out;
    transition-duration: 0s;
    transition-delay: 0.1s;
}

.task-edit-modal:hover #taskEditModalBody {
    width: 99.99%;
}

.task-edit-comment-modal #taskEditCommentModalBody {
    width: 100%;
    transition-property: width;
    transition-timing-function: ease-out;
    transition-duration: 0s;
    transition-delay: 0.1s;
}

.task-edit-comment-modal:hover #taskEditCommentModalBody {
    width: 99.99%;
}
</style>

<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <v-toolbar flat>
            <v-toolbar-title>
                <h1
                    class="ui header"
                    style="color: #7f8c8d; font-size: 2vw; font-weight: normal; letter-spacing: -2px"
                >
                    {{ page_title }}
                </h1>
            </v-toolbar-title>

            <div class="flex-grow-1"></div>
            <!-- ADDING NEW TASK -->
            <v-btn
                color="primary"
                class="v-step-new-lease-button"
                v-if="form_mode === 'listing'"
                @click="showCreateModal()"
            >
                <v-icon>add</v-icon>
                New Task
            </v-btn>
        </v-toolbar>

        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <!-- PARAMETERS -->
        <div class="page-form">
            <v-row
                v-if="form_mode === 'listing'"
                class="form-row"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Task Category</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select
                        v-model="model_task_category"
                        :options="category_list"
                    />
                    <div style="margin-top: -30px; margin-left: 302px">
                        <sui-button
                            style="height: 30px !important"
                            :loading="category_list.length <= 0"
                            class="dropdown-button"
                            icon="caret right icon"
                            @click="forceRerender()"
                        />
                    </div>
                </v-col>
            </v-row>
        </div>
        <!-- END OF PARAMETERS -->

        <!-- TASK LIST -->
        <!--    <task-list-component :visible="showListForm" :title="selectedTask" :options="taskList" :type_list="typeList"></task-list-component>-->
        <div v-if="showListForm">
            <div class="page-form">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Task Assignee</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-single-select
                            v-model="model_task_assignee"
                            :options="task_assignee_list"
                        />
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Task Status</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            v-model="model_task_status"
                            multiple
                            mandatory
                        >
                            <v-btn small>NOT STARTED </v-btn>
                            <v-btn small>WORK IN PROGRESS </v-btn>
                            <v-btn small>ON HOLD </v-btn>
                            <v-btn small>CLOSED </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
            </div>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>
            <v-card
                id="task_list_header"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h4 class="title font-weight-black">Task List - {{ selectedTask }}</h4>
                </v-card-actions>
            </v-card>

            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                v-if="!loading_setting"
                class="page-form"
            >
                <div class="page-list">
                    <div class="c8-page-table">
                        <table>
                            <tbody>
                                <tr class="form-row">
                                    <td>
                                        <v-text-field
                                            v-model="searchTask"
                                            placeholder="Search..."
                                            dense
                                        />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- TASK LIST DATA TABLE -->
                        <v-data-table
                            :headers="listHeaders"
                            :items="taskList"
                            :search="searchTask"
                            dense
                        >
                            <template v-slot:item.action="{ item }">
                                <v-btn
                                    text
                                    icon
                                    color="info"
                                    x-small
                                    title="Activities"
                                    data-position="bottom right"
                                    @click="showActivityModal(item.task_row_id)"
                                >
                                    <v-icon>history</v-icon>
                                </v-btn>
                                <v-btn
                                    text
                                    icon
                                    color="warning"
                                    x-small
                                    title="Edit"
                                    data-position="bottom right"
                                    @click="showEditModal(item)"
                                >
                                    <v-icon>mdi-square-edit-outline</v-icon>
                                </v-btn>
                                <v-btn
                                    text
                                    icon
                                    color="primary"
                                    x-small
                                    title="Comment"
                                    data-position="bottom right"
                                    @click="showCommentModal(item)"
                                >
                                    <v-icon>mdi-comment-plus-outline</v-icon>
                                </v-btn>
                                <v-btn
                                    v-if="item.modified_by == null"
                                    text
                                    icon
                                    color="error"
                                    x-small
                                    data-position="top right"
                                    @click="deleteTask(item.task_row_id)"
                                >
                                    <v-icon>delete_forever</v-icon>
                                </v-btn>
                            </template>
                        </v-data-table>
                    </div>
                </div>
            </div>
        </div>

        <!-- END OF TASK LIST -->

        <!-- ADDING NEW TASK -->
        <v-dialog
            v-model="showCreateForm"
            content-class="c8-page"
        >
            <!--    <task-create-component :visible="showCreateForm"  @close="showCreateForm=false" ></task-create-component>-->
            <task-create-component
                :visible="showCreateForm"
                @reload-list="loadTaskList()"
                @close="showCreateForm = false"
            ></task-create-component>
        </v-dialog>
        <!-- END OF ADDING NEW TASK -->

        <!-- UPDATING EXISTING TASK -->
        <v-dialog
            v-model="showUpdateForm"
            content-class="c8-page"
        >
            <!--      <task-update-component :visible="showUpdateForm" :task_id="task_row_id" @close="showUpdateForm=false" ></task-update-component>-->
            <task-update-component
                :visible="showUpdateForm"
                :task_id="task_row_id"
                :task_category="task_category_code"
                @reload-list="loadTaskList()"
                @close="showUpdateForm = false"
            ></task-update-component>
        </v-dialog>
        <!-- END OF ADDING NEW TASK -->

        <!-- COMMENT SECTION -->
        <v-dialog
            v-model="showCommentForm"
            content-class="c8-page"
        >
            <task-comment-component
                :visible="showCommentForm"
                :task_id="task_row_id"
                @close="showCommentForm = false"
            ></task-comment-component>
        </v-dialog>
        <!-- END OF COMMENT SECTION -->

        <!-- ACTIVITY LOG -->
        <v-dialog
            v-model="dialogActivity"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Task Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogActivity = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <task-activity-log-component
                        v-if="dialogActivity"
                        :task_id="activity_task_id"
                    ></task-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogActivity = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import Vue from 'vue';
import SuiVue from 'semantic-ui-vue';

import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import { mapActions, mapMutations, mapState } from 'vuex';

Vue.use(SuiVue);

// DISPLAY LIST
// Vue.component('task-list-component', require('../forms/TaskListForm').default);
Vue.component('task-create-component', require('../forms/TaskCreateForm.vue').default);
Vue.component('task-activity-log-component', require('../forms/TaskActivityLog.vue').default);
Vue.component('task-update-component', require('../forms/TaskUpdateForm.vue').default);
Vue.component('task-comment-component', require('../forms/TaskCommentForm.vue').default);
export default {
    name: 'ManageTaskFormTemplate',
    data() {
        return {
            // Default Values
            page_title: 'Task Management',
            loading_setting: false,
            loading_page_setting: false,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            editor: ClassicEditor,
            editorConfig: {
                toolbar: {
                    items: [
                        'bold',
                        'italic',
                        '|',
                        'bulletedList',
                        'numberedList',
                        '|',
                        'indent',
                        'outdent',
                        '|',
                        'undo',
                        'redo',
                    ],
                },
                language: 'en',
            },
            form_mode: 'listing',

            // TASK CATEGORY
            model_task_category: '',

            // LIST FORM
            showListForm: false,
            selectedTask: '',
            taskList: [],
            typeList: [],
            // OPTIONS
            category_list: [],
            step_list: [],
            company_list: [],
            property_list: [],
            lease_list: [],
            diary_list: [],

            searchTask: '',
            listHeaders: [
                { text: 'Task ID', value: 'task_row_id', sortable: false, width: '8%' },
                { text: 'Subject', value: 'task_subject', sortable: false, width: '29%' },
                { text: 'Type', value: 'task_type', sortable: false, width: '15%' },
                { text: 'Step Name', value: 'task_steps_name', sortable: false, width: '14%' },
                { text: 'Assignee', value: 'task_assignee', sortable: false, width: '14%' },
                { text: 'Status', value: 'task_status', sortable: false, width: '10%' },
                { text: 'Actions', value: 'action', sortable: false, width: '10%' },
            ],

            // CREATE FORM
            showCreateForm: false,

            // UPDATE FORM
            showUpdateForm: false,
            task_data: {},

            // EDIT
            modalUpdateTaskManagement: false,

            // COMMENT
            showCommentForm: false,
            task_row_id: 0,
            task_category_code: '',
            task_category_label: '',
            task_type: '',
            task_step: '',
            task_step_label: '',
            task_status: '',
            task_status_label: '',
            task_company: '',
            task_property: '',
            task_lease: '',
            task_diary: '',
            task_subject: '',
            task_description: '',
            task_assignee: '',
            task_assignee_name: '',
            task_filename: '',
            task_filename_url: '',
            old_task_type: '',
            old_task_step: '',
            old_task_status: '',
            old_task_company: '',
            old_task_property: '',
            old_task_lease: '',
            old_task_diary: '',
            old_task_subject: '',
            old_task_description: '',
            old_task_assignee: '',
            old_task_filename: '',

            modalCommentTaskManagement: false,
            modal_title: '',

            showAddComment: false,
            label_comment: 'Add Comment',
            new_comment: '',

            dialogActivity: false,
            activities: [],
            activity_task_id: 0,

            //OPTIONAL PARAMETERS
            optional_parameters: 'show optional parameters',
            show_optional_parameters: false,
            hide_optional_parameters: true,
            show_optional_company: false,
            show_optional_property: false,
            show_optional_lease: false,
            show_optional_diary: false,

            isSelecting: false,
            selectedFile: '',
            file_name: '',
            file_attached: '',
            isRemove: false,

            model_task_assignee: '',
            model_task_status: [0, 1],

            selectedStatus: [],
            status_toggle_list: [
                { id: 0, parameterID: 'NOTSTARTED', parameterDescription: 'Not started' },
                { id: 1, parameterID: 'PROGRESS', parameterDescription: 'Work in Progress' },
                { id: 2, parameterID: 'HOLD', parameterDescription: 'On Hold' },
                { id: 3, parameterID: 'CLOSED', parameterDescription: 'Closed' },
            ],
        };
    },
    mounted() {
        this.fetchCategoryList();
        this.fetchAssigneeList();
        this.fetchPropertyList();
        this.fetchStatusList();
        this.fetchCompanyList();
        this.fetchStepsList();
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            // 'dd_category_list',
            'dd_assignee_list',
            'dd_property_list',
            'dd_status_list',
            'dd_company_list',
            'dd_steps_list',
        ]),
    },
    methods: {
        ...mapActions([
            // 'fetchCategoryList',
            'fetchAssigneeList',
            'fetchPropertyList',
            'fetchStatusList',
            'fetchCompanyList',
            'fetchStepsList',
        ]),
        ...mapMutations([
            'SET_DD_CATEGORY_LIST',
            'SET_DD_ASSIGNEE_LIST',
            'SET_DD_PROPERTY_LIST',
            'SET_DD_STATUS_LIST',
            'SET_DD_COMPANY_LIST',
            'SET_DD_STEPS_LIST',
        ]),
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        fetchCategoryList: function () {
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            let apiUrlCompany = this.cirrus8_api_url + 'api/ui/fetch/param-task-category-list';
            axios.post(apiUrlCompany, formData).then((response) => {
                let addAll = [
                    {
                        fieldKey: 'All',
                        fieldValue: 'All',
                        field_key: 'All',
                        field_value: 'All',
                        label: 'All',
                        value: 'All',
                    },
                ];
                this.category_list = addAll.concat(response.data);
            });
        },
        loadTaskList: function () {
            // DISPLAY TASK LIST
            this.showListForm = true;
            this.selectedTask = this.model_task_category;
            this.selectedStatus = this.model_task_status;
            let statusIDS = this.getStatus(this.model_task_status);
            // load list
            var formData = new FormData();
            formData.append('un', this.username);
            formData.append('current_db', this.current_db);
            formData.append('user_type', this.user_type);
            formData.append('task_category', this.model_task_category);
            formData.append('task_assignee', this.model_task_assignee);
            formData.append('task_status', statusIDS);

            // Create Task details
            let apiUrl = this.cirrus8_api_url + 'api/task-management/get-task-list';
            document.getElementById('ngLoader-UI').style.display = 'block';
            axios.post(apiUrl, formData).then((response) => {
                document.getElementById('ngLoader-UI').style.display = 'none';
                this.taskList = response.data.task_list;
                this.typeList = response.data.type_list.original;
                this.model_task_assignee = response.data.assignee;
                this.task_assignee_list = response.data.task_assignee_list.original;
            });
        },
        showCommentModal: function (data) {
            this.showCommentForm = true;
            this.task_row_id = data.task_row_id;
        },
        showCreateModal: function () {
            this.showCreateForm = true;
        },
        showEditModal: function (data) {
            this.showUpdateForm = true;
            this.task_category_code = data.task_category_code;
            this.task_row_id = data.task_row_id;
        },
        showActivityModal: function (task_id) {
            this.activity_task_id = task_id;
            this.dialogActivity = true;
        },
        forceRerender: function () {
            this.loadTaskList();
        },
        deleteTask: function (taskID) {
            if (taskID) {
                var formData = new FormData();
                formData.append('form_mode', 'delete_task');
                formData.append('un', this.username);
                formData.append('current_db', this.current_db);
                formData.append('user_type', this.user_type);
                formData.append('task_id', taskID);
                // UPDATE STEPS STATUS AND ASSIGNEE
                document.getElementById('ngLoader-UI').style.display = 'block';
                let apiUrl = this.cirrus8_api_url + 'api/task-management/save-task';
                axios.post(apiUrl, formData).then((response) => {
                    this.status = response.data.status;
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    if (this.status == 'success') {
                        this.loadTaskList();
                        this.$noty.success('Task deleted.');
                        this.loading_setting = false;
                    } else {
                        this.$noty.error('error message');
                        this.loading_setting = false;
                    }
                });
            }
        },

        closeUpdateForm: function () {
            this.showUpdateForm = false;
            this.loadTaskList();
        },
        getStatus: function (arrayStatus) {
            var statusData = [];
            if (arrayStatus) {
                for (var i = 0; i < arrayStatus.length; i++) {
                    statusData.push("'" + this.status_toggle_list[arrayStatus[i]].parameterID + "'");
                }
            }
            return statusData;
        },
    },
    watch: {
        model_task_category: function () {
            if (this.model_task_category) {
                this.loadTaskList();
            } else {
                this.model_task_category = '';
                // HIDE LIST IF NO TASK CATEGORY SELECTED
                this.showListForm = false;
            }
        },
        model_task_assignee: function () {
            if (this.model_task_assignee) {
                this.loadTaskList();
            }
        },
        model_task_status: function () {
            this.loadTaskList();
        },
    },
    mixins: [global_mixins],
};
</script>
<style scoped></style>
