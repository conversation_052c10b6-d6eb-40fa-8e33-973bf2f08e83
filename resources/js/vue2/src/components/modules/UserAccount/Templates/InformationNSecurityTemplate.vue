<template>
    <div class="c8-page">
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header
                    title="Account Information"
                    subtitle="Info about you and your account login logs"
                />
            </v-toolbar-title>
            <div class="flex-grow-1"></div>
        </v-toolbar>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <v-card>
            <v-card-title>
                <v-icon
                    large
                    left
                >
                    mdi-information
                </v-icon>
                <span class="text-h6 font-weight-light">Basic Info</span>
            </v-card-title>

            <v-card-text class="text-h5">
                <v-list dense>
                    <v-list-item-group color="primary">
                        <v-list-item>
                            <v-list-item-content>
                                <v-list-item-title>Name</v-list-item-title>
                                <v-list-item-subtitle>{{ full_name }}</v-list-item-subtitle>
                            </v-list-item-content>
                        </v-list-item>
                        <v-list-item>
                            <v-list-item-content>
                                <v-list-item-title>Email</v-list-item-title>
                                <v-list-item-subtitle>{{ email }}</v-list-item-subtitle>
                            </v-list-item-content>
                        </v-list-item>
                        <v-list-item>
                            <v-list-item-content>
                                <v-list-item-title>License Start</v-list-item-title>
                                <v-list-item-subtitle>{{ license_start }}</v-list-item-subtitle>
                            </v-list-item-content>
                        </v-list-item>
                    </v-list-item-group>
                </v-list>
            </v-card-text>
        </v-card>

        <account-login-activity-section></account-login-activity-section>
    </div>
</template>

<script>
import AccountLoginLog from '../Forms/AccountLoginLog.vue';
export default {
    components: {
        'account-login-activity-section': AccountLoginLog,
    },
    data() {
        return {
            error_server_msg: {},
            error_server_msg2: [],
            loading_page_setting: false,
            license_start: '',
            email: '',
            full_name: '',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
    },
    methods: {
        loadForm: function () {
            this.loadLoginDetails();
        },
        loadLoginDetails: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('no_load', true);
            let apiUrl = 'user-account/fetch/login-details';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.email = response.data.email;
                this.full_name = response.data.full_name;
                this.license_start = response.data.license_start;
                this.loading_setting = false;
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
