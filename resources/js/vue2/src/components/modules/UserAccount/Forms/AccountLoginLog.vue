<template>
    <div>
        <div class="page-form">
            <div class="form-row">
                <v-row class="form-row no-gutters">
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <span>Login Log</span>
                        <!--datatable start-->
                        <v-data-table
                            class="c8-datatable-custom"
                            dense
                            item-key="id"
                            :headers="success_headers"
                            :items="success_login_log"
                            :items-per-page="success_items_per_page"
                            hide-default-footer
                            :page.sync="success_page"
                            :total-visible="7"
                            @page-count="success_page_count = $event"
                            :search="success_search_datatable"
                            :calculate-widths="true"
                        >
                            <template v-slot:item.index="{ item }">
                                {{ success_login_log.indexOf(item) + 1 }}
                            </template>
                            <template v-slot:item.login_at_raw="{ item }">
                                <div class="form-row no-border-line">
                                    <span class="form-input-text">{{ item.login_at }}</span>
                                </div>
                            </template>
                            <template v-slot:item.logout_at_raw="{ item }">
                                <div class="form-row no-border-line">
                                    <span class="form-input-text">{{ item.logout_at }}</span>
                                </div>
                            </template>
                        </v-data-table>
                        <v-row
                            class="form-row"
                            v-show="success_login_log.length > 5"
                        >
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="c8-datatable-custom-footer">
                                    <tr>
                                        <td class="">Rows per page:</td>
                                        <td>
                                            <multiselect
                                                v-model="success_items_per_page"
                                                :options="[5, 10, 15]"
                                                :allowEmpty="false"
                                                class="vue-select2 dropdown-left dropdown-200"
                                                :show-labels="false"
                                                ><span slot="noResult"
                                                    >Oops! No elements found. Consider changing the search query.</span
                                                ></multiselect
                                            >
                                        </td>
                                        <td></td>
                                        <td>
                                            <v-pagination
                                                v-model="success_page"
                                                :length="success_page_count"
                                                :total-visible="7"
                                            ></v-pagination>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </v-col>
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        Login Failed Log
                        <!--datatable start-->
                        <v-data-table
                            class="c8-datatable-custom"
                            dense
                            item-key="id"
                            :headers="attempt_headers"
                            :items="attempt_login_log"
                            :items-per-page="attempt_items_per_page"
                            hide-default-footer
                            :page.sync="attempt_page"
                            :total-visible="7"
                            @page-count="attempt_page_count = $event"
                            :search="attempt_search_datatable"
                            :calculate-widths="true"
                        >
                            <template v-slot:item.index="{ item }">
                                {{ attempt_login_log.indexOf(item) + 1 }}
                            </template>
                            <template v-slot:item.attempt_at_raw="{ item }">
                                <div class="form-row no-border-line">
                                    <span class="form-input-text">{{ item.attempt_at }}</span>
                                </div>
                            </template>
                        </v-data-table>
                        <v-row
                            class="form-row"
                            v-show="attempt_login_log.length > 5"
                        >
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="c8-datatable-custom-footer">
                                    <tr>
                                        <td class="">Rows per page:</td>
                                        <td>
                                            <multiselect
                                                v-model="attempt_items_per_page"
                                                :options="[5, 10, 15]"
                                                :allowEmpty="false"
                                                class="vue-select2 dropdown-left dropdown-200"
                                                :show-labels="false"
                                                ><span slot="noResult"
                                                    >Oops! No elements found. Consider changing the search query.</span
                                                ></multiselect
                                            >
                                        </td>
                                        <td></td>
                                        <td>
                                            <v-pagination
                                                v-model="attempt_page"
                                                :length="attempt_page_count"
                                                :total-visible="7"
                                            ></v-pagination>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </v-col>
                </v-row>
            </div>
        </div>
    </div>
</template>

<script>
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
export default {
    components: {},
    data() {
        return {
            error_server_msg: {},
            error_server_msg2: [],
            loading_page_setting: false,
            success_login_log: [],
            attempt_login_log: [],
            success_headers: [
                { text: '#', value: 'index', sortable: true },
                { text: 'OS', value: 'client_os', sortable: true },
                { text: 'Device', value: 'client_device', sortable: true },
                { text: 'Browser', value: 'login_browser', sortable: true },
                { text: 'Login At', value: 'login_at_raw', sortable: true },
                { text: 'Logout At', value: 'logout_at_raw', sortable: true },
                { text: 'Location', value: 'login_loc', sortable: true },
                { text: 'IP Address', value: 'ip_address', sortable: true },
            ],
            success_page: 1,
            success_page_count: 0,
            success_items_per_page: 5,
            success_search_datatable: '',
            attempt_headers: [
                { text: '#', value: 'index', sortable: true },
                { text: 'OS', value: 'client_os', sortable: true },
                { text: 'Device', value: 'client_device', sortable: true },
                { text: 'Browser', value: 'client_browser', sortable: true },
                { text: 'Attempted At', value: 'attempt_at_raw', sortable: true },
                { text: 'Location', value: 'client_location', sortable: true },
                { text: 'IP Address', value: 'client_ip', sortable: true },
                { text: 'No. of Attempt', value: 'attempt_counter', sortable: true },
            ],
            attempt_page: 1,
            attempt_page_count: 0,
            attempt_items_per_page: 5,
            attempt_search_datatable: '',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
    },
    methods: {
        loadForm: function () {
            this.loadLoginLog();
            this.loadLoginAttemptLog();
        },
        loadLoginLog: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('no_load', true);
            let apiUrl = 'user-account/fetch/login-log';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.success_login_log = response.data.success_login_log_list;
                this.loading_setting = false;
            });
        },
        loadLoginAttemptLog: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('no_load', true);
            let apiUrl = 'user-account/fetch/login-attempt-log';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.attempt_login_log = response.data.attempt_login_log_list;
                this.loading_setting = false;
            });
        },
    },
    created() {
        bus.$on('loadUserLoginLogs', (data) => {
            this.loadForm();
        });
    },
    mixins: [global_mixins],
};
</script>
