<template>
    <div class="page-form">
        <v-card
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Property Bank Balances</h6>
                <v-spacer></v-spacer>
                <h6
                    class="title font-weight-black"
                    style="padding-right: 10px"
                >
                    Total: {{ currency_symbol
                    }}{{ accountingAmountFormat(numberWithCommas(roundTo(trial_balance_closing_balance, 2))) }}
                </h6>
                <v-btn
                    x-small
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    color="white"
                    icon
                    x-small
                    class="v-step-gen-final-message"
                    @click="expand = !expand"
                >
                    <v-icon v-if="!expand">expand_more</v-icon>
                    <v-icon v-if="expand">expand_less</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-expand-transition
            class="mx-auto"
            v-if="expand"
        >
            <v-simple-table dense>
                <template v-slot:default>
                    <thead>
                        <tr>
                            <th class="text-left"></th>
                            <th class="text-right">Total</th>
                            <th
                                class="text-right"
                                v-if="getLengthOfObjectOrArray(fund) > 0"
                            >
                                Total Main Property
                            </th>
                            <th class="text-right">Owners</th>
                            <th class="text-right">Outgoings</th>
                            <th class="text-right">Recoverables</th>
                            <th
                                class="text-right"
                                v-for="(fund_data, fund_index) in fund"
                                :key="fund_index"
                            >
                                {{ fund_data.owner_name }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Bank Balances</strong></td>
                            <td class="text-right">
                                {{ currency_symbol
                                }}{{
                                    accountingAmountFormat(numberWithCommas(roundTo(trial_balance_closing_balance, 2)))
                                }}
                            </td>
                            <td
                                class="text-right"
                                v-if="getLengthOfObjectOrArray(fund) > 0"
                            >
                                {{ currency_symbol
                                }}{{
                                    accountingAmountFormat(
                                        numberWithCommas(roundTo(trial_balance_closing_balance_main, 2)),
                                    )
                                }}
                            </td>
                            <td class="text-right">
                                {{ currency_symbol
                                }}{{ accountingAmountFormat(numberWithCommas(roundTo(owners_income_closing, 2))) }}
                            </td>
                            <td class="text-right">
                                {{ currency_symbol
                                }}{{ accountingAmountFormat(numberWithCommas(roundTo(vo_closing, 2))) }}
                            </td>
                            <td class="text-right">
                                {{ currency_symbol
                                }}{{
                                    accountingAmountFormat(numberWithCommas(roundTo(direct_recoverables_closing, 2)))
                                }}
                            </td>
                            <td
                                class="text-right"
                                v-for="(fund_data2, fund_index2) in fund"
                                :key="fund_index2"
                            >
                                {{ currency_symbol
                                }}{{ accountingAmountFormat(numberWithCommas(roundTo(fund_data2.closing_balance, 2))) }}
                            </td>
                        </tr>
                    </tbody>
                </template>
            </v-simple-table>
        </v-expand-transition>

        <br />
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
import { bus } from '../../../../plugins/bus';
import Vue from 'vue';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        country_default_settings: { type: String, default: '' },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'BANK_BALANCES',
            expand: false,
            total_amount: 0.0,
            loading_setting: false,
            owners_income_closing: '0.00',
            vo_closing: '0.00',
            direct_recoverables_closing: '0.00',
            trial_balance_closing_balance: '0.00',
            trial_balance_closing_balance_main: '0.00',
            fund: [],
            currency_symbol: '$',
        };
    },
    computed: {},
    mounted() {
        this.loadForm();

        let country_settings = JSON.parse(atob(this.country_default_settings));
        this.currency_symbol = country_settings.currency_symbol;
    },
    methods: {
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.email_errors = [];
            this.loadBankBalance();
        },
        loadBankBalance: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            this.$api.post('property/fetch/bank-balances', form_data).then((response) => {
                this.expand = response.data.expand_section;
                this.owners_income_closing = response.data.owners_income_closing;
                this.vo_closing = response.data.vo_closing;
                this.direct_recoverables_closing = response.data.direct_recoverables_closing;
                this.trial_balance_closing_balance = response.data.trial_balance_closing_balance;
                this.trial_balance_closing_balance_main = response.data.trial_balance_closing_balance_main;
                this.fund = response.data.fund;
            });
        },
        getLengthOfObjectOrArray: function (variable) {
            if (Array.isArray(variable)) {
                // handle as array
                return variable.length;
            } else if (typeof variable === 'object') {
                // handle as object
                return Object.keys(variable).length;
            }
            return 0;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadPropertyBankBalances', (data) => {
            this.loadForm();
        });
    },
    mixins: [global_mixins],
};
</script>
<style>
.v-expansion-panel-content > .v-expansion-panel-content__wrap {
    padding: 0px;
}
</style>
