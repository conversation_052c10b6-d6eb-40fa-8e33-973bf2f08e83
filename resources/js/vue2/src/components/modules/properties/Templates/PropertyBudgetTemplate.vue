<template>
    <div class="c8-page">
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header title="Property Budget" />
            </v-toolbar-title>
            <v-btn-toggle
                class="form-toggle"
                v-model="version"
                mandatory
            >
                <v-btn
                    x-small
                    depressed
                    text
                >
                    Overview
                </v-btn>
                <v-btn
                    x-small
                    depressed
                    text
                >
                    Version 2
                </v-btn>
                <v-btn
                    x-small
                    depressed
                    text
                >
                    Version 1
                </v-btn>
            </v-btn-toggle>
            <div class="flex-grow-1"></div>
            <v-btn
                depressed
                small
                color="primary"
                class="v-step-new-lease-button"
                @click="show_bulk_copy_modal = true"
            >
                <v-icon>dynamic_feed</v-icon>
                Bulk Copy
            </v-btn>
        </v-toolbar>
        <span class="caption">This form allows you to manage property budgets input for a financial year.</span>
        <property-budget-overview-component
            v-if="version === 0"
            :currency_symbol="currency_symbol"
            :country_default_settings="initialCountryDefaultSettings"
        ></property-budget-overview-component>
        <property-budget-main-component
            v-if="version === 1"
            :currency_symbol="currency_symbol"
            :area_unit="area_unit"
            :country_default_settings="initialCountryDefaultSettings"
        ></property-budget-main-component>
        <property-budget-v1-component
            v-if="version === 2"
            :currency_symbol="currency_symbol"
            :area_unit="area_unit"
            :country_default_settings="initialCountryDefaultSettings"
        ></property-budget-v1-component>

        <v-dialog
            v-model="show_bulk_copy_modal"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Bulk Budget Copy
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_bulk_copy_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error :errorMsg2="bulk_copy_error"></cirrus-server-error>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Template</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <cirrus-single-upload-button
                                    :edit_form="true"
                                    :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                    v-model="budget_excel_file"
                                    accept_type="excel"
                                ></cirrus-single-upload-button>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >From Year</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="budget_bulk_from_year"
                                    :options="property_calendar_years"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-300"
                                    group-label="language"
                                    placeholder="Select a financial year"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >To Year</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="budget_bulk_to_year"
                                    :options="property_calendar_years"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-300"
                                    group-label="language"
                                    placeholder="Select a financial year"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </v-col>
                        </v-row>
                    </div>
                    <v-progress-linear
                        v-if="loading_bulk_setting"
                        :buffer-value="bulk_loader_buffer"
                        :value="bulk_loader_value"
                        stream
                        color="primary"
                    ></v-progress-linear>
                    <span v-if="loading_bulk_setting"
                        >processing ({{ bulk_curr_procc }}) {{ bulk_curr_procc_no }}/{{ bulk_total_procc }}</span
                    >
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        :disabled="loading_bulk_setting"
                        color="success"
                        depressed
                        small
                        @click="processBulkCopy()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Submit
                    </v-btn>
                    <v-btn
                        :disabled="loading_bulk_setting"
                        color="normal"
                        depressed
                        small
                        @click="show_bulk_copy_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';
import PropertyOverview from '../PropertyBudget/PropertyOverview.vue';
import PropertyBudget from '../PropertyBudget/PropertyBudget.vue';
import PropertyBudgetV1 from '../PropertyBudget/PropertyBudgetV1.vue';

// Vue.use(SuiVue);
export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialVersionId: String,
        initialCountryDefaultSettings: String,
    },
    components: {
        'property-budget-overview-component': PropertyOverview,
        'property-budget-main-component': PropertyBudget,
        'property-budget-v1-component': PropertyBudgetV1,
    },
    data() {
        return {
            budget_bulk_process_type: 0,
            budget_bulk_from_year: { label: '', value: '', field_value: '', field_key: '' },
            budget_bulk_to_year: { label: '', value: '', field_value: '', field_key: '' },
            bulk_curr_procc: '',
            budget_excel_file: null,
            bulk_total_procc: 0,
            bulk_curr_procc_no: 0,
            bulk_loader_value: 0,
            bulk_loader_buffer: 0,
            version: 1,
            latest_name: 'Main',
            version_flag: false,
            loading_bulk_setting: false,
            bulk_copy_error: [],
            property_calendar_years: [],
            show_bulk_copy_modal: false,
            currency_symbol: '$',
            area_unit: 'm&sup2;',
        };
    },
    mounted() {
        this.initialPageLoad();
        this.loadPropertyYears();
    },
    methods: {
        initialPageLoad: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('property/budget/loadBudgetParam', form_data).then((response) => {
                //       this.version = response.data.version;
                //       this.latest_name = response.data.latest_name;
                //       this.version_flag = !response.data.ver_flag;
                this.currency_symbol = response.data.currency_symbol;
                this.area_unit = response.data.area_unit;
            });
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadBudget_' + id;
        },
        loadPropertyYears: function () {
            this.budgetSettingDimmer = true;
            let form_data = new FormData();
            form_data.append('budget_version', 'V2');
            form_data.append('no_load', true);
            this.$api.post('ui/fetch/all-property-calendar-years', form_data).then((response) => {
                this.property_calendar_years = response.data.property_calendar_years;
                this.budgetSettingDimmer = false;
            });
        },
        processBulkCopy: function () {
            //
            this.bulk_curr_procc_no = 0;
            this.bulk_copy_error = [];
            let financial_year_from = this.budget_bulk_from_year.field_key;
            let financial_year_to = this.budget_bulk_to_year.field_key;
            let uploaded_file = null;
            let uploaded_filetype = null;
            if (this.budget_excel_file !== null) {
                uploaded_file = this.budget_excel_file[0];
                uploaded_filetype = uploaded_file['type'];
            }

            if (uploaded_file === undefined || uploaded_file === null) {
                this.bulk_copy_error.push(['You have not chosen a valid file']);
            }
            if (uploaded_file !== null) {
                if (
                    uploaded_filetype != 'application/vnd.ms-excel' &&
                    uploaded_filetype != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                ) {
                    this.bulk_copy_error.push(['Invalid file format. Please upload valid excel file.']);
                }
            }
            if (financial_year_from === '') {
                this.bulk_copy_error.push(['You have not entered a valid from year']);
            }
            if (financial_year_to === '') {
                this.bulk_copy_error.push(['You have not entered a valid to year']);
            }
            if (this.bulk_copy_error.length === 0) {
                // this.loading_bulk_setting = true;
                var formData = new FormData();
                formData.append('budget_excel_file', uploaded_file);
                formData.append('financial_year_from', financial_year_from);
                formData.append('financial_year_to', financial_year_to);
                formData.append('no_load', true);

                this.$api
                    .post('with-file-upload/vue/property/budget/load-bulk-copy-details', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        let total_properties = parseInt(response.data.total_properties);
                        let property_list = response.data.property_list;

                        this.bulk_total_procc = total_properties;
                        this.processBulkCopyPerProperty(property_list);
                        // this.loading_bulk_setting = false;
                    });
            }
        },
        async processBulkCopyPerProperty(property_list) {
            try {
                let financial_year_from = this.budget_bulk_from_year.field_key;
                let financial_year_to = this.budget_bulk_to_year.field_key;
                let uploaded_file = null;
                if (this.budget_excel_file !== null) {
                    uploaded_file = this.budget_excel_file[0];
                }
                let curr_ctr = 0;
                for (let x = 0; x <= property_list.length - 1; x++) {
                    this.bulk_curr_procc = property_list[x];
                    // this.bulk_curr_procc_no = curr_ctr;
                    this.loading_bulk_setting = true;
                    this.bulk_loader_buffer = ((curr_ctr + 1) / this.bulk_total_procc) * 100;
                    var formData = new FormData();
                    formData.append('budget_excel_file', uploaded_file);
                    formData.append('financial_year_from', financial_year_from);
                    formData.append('financial_year_to', financial_year_to);
                    formData.append('property_code', property_list[x]);
                    formData.append('no_load', true);
                    const response = await this.$api
                        .post('with-file-upload/vue/property/budget/process-bulk-copy', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data',
                            },
                        })
                        .then((response) => {
                            this.bulk_curr_procc_no++;

                            this.bulk_loader_value = (this.bulk_curr_procc_no / this.bulk_total_procc) * 100;
                            this.loading_bulk_setting = false;
                        });
                }
            } catch (e) {}
        },
    },
    watch: {
        property_code: function () {
            // this.property_code = {fieldKey: ''};
            // this.property_list = [];
            // this.loadTempProperty();
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
