<template>
    <div class="c8-page">
        <div>
            <div>
                <div class="page-header">
                    <div class="page-title mb-1">{{ property_page_title }}</div>
                    <div class="subtitle-2">
                        The new property form is used to advise {{ trust_account_label }}s of new property details.
                    </div>
                    <div class="caption">
                        The form is done in 3 steps and will be saved as a draft after step 1 has been completed and can
                        be retrieved later if all details are not known.
                    </div>
                    <div class="subtitle-2">
                        Important: First complete a
                        <a
                            href="?module=companies&command=company"
                            target="_blank"
                            >new company form</a
                        >
                        to create the owner then proceed to completing the new property form.
                    </div>
                </div>
                <div></div>
                <cirrus-server-error
                    :error_msg="error_server_msg"
                    :errorMsg2="error_server_msg2"
                ></cirrus-server-error>
                <div class="page-form">
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Enter A Property Code:</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <input
                                type="hidden"
                                id="property_code"
                                :value="new_property_code"
                            />
                            <div v-if="!check_property_flag && !show_new_main_form">
                                <cirrus-input
                                    class="v-step-property-enter"
                                    v-model="new_property_code"
                                    size="10"
                                    :id="'new_property_code'"
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                                <v-btn
                                    class="form-text-button mb-0 rounded-l-0"
                                    color="primary"
                                    depressed
                                    elevation="0"
                                    small
                                    @click="checkPropertyCode()"
                                >
                                    Create New
                                </v-btn>
                            </div>
                            <div v-if="check_property_flag && !show_new_main_form">
                                <span class="form-input-text">{{ new_property_code }}</span>
                            </div>
                            <div v-if="check_property_flag && show_new_main_form">
                                <span class="form-input-text">{{ new_property_code }}</span>
                            </div>
                        </v-col>
                    </v-row>
                </div>

                <property-stepper-form-template-component
                    v-if="show_new_main_form"
                    :new_property_flag="new_property_flag"
                    :property_code="new_property_code"
                    :version_id="new_version_id"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                >
                </property-stepper-form-template-component>
            </div>
        </div>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex';
import global_mixins from '../../../../plugins/mixins';

// Vue.use(SuiVue);
export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialVersionId: String,
        initialCountryDefaultSettings: String,
    },
    components: {},
    data() {
        return {
            trust_account_label: 'Trust Account',
            property_code: '',
            new_property_code: '',
            version_id: null,
            property_list: [],
            property_main_list: [],
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            new_version_id: '1',
            read_only: this.initialReadOnly,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            template_tab: null,
            loading_setting: false,
            loading_page_setting: false,
            step: 0,
            force_load: false,
            property_page_title: 'New Property',
            error_server_msg: {},
            error_server_msg2: [],
            property_list_temp: [],
            property_existed: true,
            force_load_new_property: 0,
            show_new_main_form: false,
            check_property_flag: false,
            new_property_flag: true,
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'tour_steps',
            'auto_diarise',
            'doc_active_version',
            'sys_ver_control_list',
        ]),
    },
    mounted() {
        // console.log(localStorage.getItem('cirrus8_api_url'));
        this.fetchFormVersionControl();
        this.fetchCountryList();
        this.fetchPMAutoDiarise();
        if (this.initialPropertyCode) {
            this.new_property_code = this.initialPropertyCode;
            this.checkPropertyCode();
        }
        this.loadCountryDefaults();
    },
    methods: {
        ...mapActions(['fetchCountryList', 'fetchPMAutoDiarise', 'fetchFormVersionControl']),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_LEASE_CODE', 'SET_TOUR_STEPS']),
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.trust_account_label = this.ucwords(this.country_defaults.trust_account);
            });
        },
        getValueInList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.field_key === param1);
                if (filtered.length > 0) {
                    return filtered[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        fieldKey: '',
                        fieldValue: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        loadPropertyList: function () {
            let form_data = new FormData();
            form_data.append('page_source', 'propertyFormTemplate');
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-dropdown-list', form_data).then((response) => {
                this.property_list = response.data.data;
                if (this.initialPropertyCode && this.initialVersionId) {
                    this.new_property_code = this.initialPropertyCode;
                    this.version_id = this.initialVersionId;
                    this.selectProperty(this.new_property_code, this.version_id);
                }
            });
        },
        selectProperty: function (property_code, version_id) {
            this.new_property_flag = true;
            this.new_property_code = property_code;
            this.new_version_id = version_id;
            this.check_property_flag = true;
            this.show_new_main_form = true;
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        goToHref: function (param1, param2) {
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        goToShortcut: function (parameter) {
            switch (parameter) {
                case 'property':
                    window.open(
                        '?module=properties&command=v2_manage_property_page&property_code=' +
                            this.property_code.field_key,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'company':
                    //module=companies&command=company&companyID=7ELEVEN
                    window.open(
                        '?module=companies&command=company&companyID=' + this.$company_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'lease_abstract':
                    break;
                case 'tenant_activity_current_month':
                    break;
                case 'tenant_activity_all_date':
                    break;
                case 'view_tax_invoice':
                    break;
                case 'print':
                    break;
            }
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        showNewProperty: function () {
            this.form_mode = 1;
            this.property_page_title = 'New Property';
            this.new_property_code = '';
        },
        cancelNewProperty: function () {
            this.property_page_title = 'Manage Property';
            this.form_mode = 0;

            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_property_code = '';
            this.property_existed = true;
        },
        showPropertyListTable: function () {
            this.show_new_main_form = false;
            this.check_property_flag = false;
            this.property_list_temp = [];
            this.new_property_code = '';
            this.loadTempProperty();

            this.error_server_msg2 = [];
            this.error_msg = [];
        },
        checkPropertyCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            let new_property_code = this.new_property_code.toUpperCase().trim();

            this.new_property_code = new_property_code;
            if (new_property_code.length > 10) {
                // this.error_msg.push({id:'new_property_code', message: 'Property code allows 10 characters only'});
                this.error_server_msg2.push(['Property code allows 10 characters only']);
            } else if (new_property_code === '') {
                // this.error_msg.push({id:'new_property_code', message: 'Pproperty input a valid property code'});
                this.error_server_msg2.push(['Please input a valid property code']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('new_property_code', new_property_code);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/property/fetch/check-code-if-exist', form_data)
                    .then((response) => {
                        this.property_list_temp = response.data.property_list_temp;
                        this.property_existed = response.data.property_existed;
                        this.new_version_id = response.data.new_version_id.toString();
                        this.error_server_msg2 = response.data.validation_errors;
                        this.loading_page_setting = false;
                        if (!this.property_existed && this.error_server_msg2.length === 0) {
                            this.check_property_flag = true;
                            this.show_new_main_form = true;
                            this.property_code = new_property_code;
                        }
                    });
            }
        },
        createNewPropertyVersion: function () {
            this.show_new_main_form = true;
        },
        loadTempProperty: function () {
            let form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('no_load', true);
            this.$api
                .post(this.cirrus8_api_url + 'api/temp/property/fetch/property-list', form_data)
                .then((response) => {
                    let property_list_temp = response.data.property_list_temp;
                    let filtered_list = property_list_temp.filter((m) => m.form_type === '2');
                    this.property_list_temp = filtered_list;
                });
        },
        getPropertyCode: function (value) {
            let property_code = value.property_code;
            let property_name = value.property_name;
            let label = property_code + ' - ' + property_name;
            this.cancelNewProperty();
            this.property_code = {
                fieldKey: property_code,
                field_key: property_code,
                fieldValue: label,
                field_value: label,
                value: property_code,
                label: label,
            };
        },
    },
    watch: {
        property_code: function () {
            // this.property_code = {fieldKey: ''};
            // this.property_list = [];
            // this.loadTempProperty();
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
