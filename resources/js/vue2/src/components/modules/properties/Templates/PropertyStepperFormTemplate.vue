<style>
.shortcut-div > .v-toolbar__content {
    height: 30px;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <div>
            <div>
                <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
                <v-stepper v-model="step">
                    <v-stepper-header>
                        <v-stepper-step
                            :complete="step > 1"
                            step="1"
                            >Step 1
                        </v-stepper-step>

                        <v-divider></v-divider>

                        <v-stepper-step
                            :complete="step > 2"
                            step="2"
                            >Step 2
                        </v-stepper-step>

                        <v-divider></v-divider>

                        <v-stepper-step
                            :complete="step > 3"
                            step="3"
                            >Step 3
                        </v-stepper-step>

                        <v-divider></v-divider>

                        <v-stepper-step step="4">Submitting</v-stepper-step>
                    </v-stepper-header>

                    <v-stepper-items>
                        <v-stepper-content step="1">
                            <v-alert
                                type="info"
                                dense
                                dark
                                color="primary"
                            >
                                <span>
                                    A property (draft) will be automatically created after this step.
                                    <br />
                                    <strong>Property Name</strong> is the name chosen to identify the Property, and
                                    usually corresponds to the tenant name.<br />
                                    <strong>Tenancy location</strong> describes the physical location (unit / building)
                                    of the Property.
                                </span>
                            </v-alert>
                            <property-main-form-component
                                ref="main_form_new_property"
                                save_button_default_label="Continue to step 2"
                                :property_code="property_code"
                                :new_property="true"
                                :version_id="version_id"
                                v-on:returnPropertyIsExisted="getPropertyCode"
                                v-on:returnPropertyFormSuccess="nextStepper"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :edit_flag="true"
                            ></property-main-form-component>
                        </v-stepper-content>

                        <v-stepper-content step="2">
                            <cirrus-server-error
                                :error_msg="error_server_msg"
                                :errorMsg2="error_server_msg2"
                            ></cirrus-server-error>
                            <property-man-detail-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-man-detail-form-component>
                            <property-man-agreement-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-man-agreement-form-component>
                            <property-man-fee-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-man-fee-form-component>
                            <property-fees-charges-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-fees-charges-form-component>
                            <property-fund-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-fund-form-component>
                            <property-keys-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-keys-component>

                            <br />
                            <br />
                            <v-btn
                                color="success"
                                dark
                                small
                                @click="changeStep(1)"
                                >Back
                            </v-btn>
                            <v-btn
                                absolute
                                right
                                color="success"
                                dark
                                small
                                @click="changeStep(3)"
                                :loading="btn_loading_setting"
                            >
                                Continue to step 3
                            </v-btn>
                            <br />
                            <br />
                        </v-stepper-content>

                        <v-stepper-content step="3">
                            <property-diary-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-diary-form-component>
                            <property-floor-n-unit-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-floor-n-unit-form-component>
                            <property-recoverable-split-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-recoverable-split-component>
                            <property-parking-bays-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-parking-bays-form-component>
                            <property-note-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-note-form-component>
                            <property-inspection-form-component
                                :property_code="property_code"
                                :read_only="read_only"
                                :new_property="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-inspection-form-component>
                            <property-insurance-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-insurance-form-component>
                            <property-contact-form-component
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-contact-form-component>
                            <property-document-v2-form-component
                                v-if="doc_active_version === 1"
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-document-v2-form-component>
                            <property-document-form-component
                                v-else
                                :property_code="property_code"
                                :version_id="version_id"
                                :new_property="true"
                                :edit_flag="true"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                            ></property-document-form-component>

                            <br />
                            <br />
                            <v-btn
                                absolute
                                right
                                color="success"
                                dark
                                small
                                @click="changeStep(4)"
                            >
                                Complete form
                            </v-btn>

                            <v-btn
                                color="success"
                                dark
                                small
                                @click="changeStep(2)"
                                >Back
                            </v-btn>

                            <br />
                            <br />
                        </v-stepper-content>
                        <v-stepper-content step="4">
                            <v-alert
                                type="info"
                                dense
                                dark
                                color="primary"
                            >
                                <v-row align="center">
                                    <v-col class="grow">
                                        <span>
                                            Your property form has been saved as draft.<br />
                                            Select <strong v-if="process_type === 0">'PROCESS'</strong
                                            ><strong v-if="process_type === 1">'RE-ASSIGN'</strong> for processing or
                                            <strong>'NO, KEEP AS DRAFT'</strong> to retain the form in draft format.<br />
                                        </span>
                                    </v-col>
                                    <v-col class="shrink">
                                        <v-btn
                                            color="primary"
                                            small
                                            right
                                            @click="printLease()"
                                        >
                                            <v-icon>print</v-icon>
                                            PRINT
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </v-alert>
                            <v-container fluid>
                                <v-row>
                                    <v-col
                                        cols="12"
                                        md="6"
                                    >
                                        <cirrus-content-loader
                                            v-if="false"
                                            type="article"
                                        ></cirrus-content-loader>
                                        <v-card
                                            class="mx-auto"
                                            color="#1F7087"
                                            shaped
                                            dark
                                        >
                                            <v-card-text>
                                                <div class="overline mb-4">
                                                    Property Code:
                                                    <span class="v-step-property-code">{{ property_code }}</span>
                                                </div>
                                                <table class="data-grid data-grid-dense tableHive">
                                                    <tr>
                                                        <td
                                                            class="title"
                                                            align="right"
                                                            style="color: white"
                                                        >
                                                            Submit to:
                                                        </td>
                                                        <td class="required"></td>
                                                        <td>
                                                            <v-btn-toggle
                                                                class="v-step-search-type form-toggle"
                                                                v-model="process_type"
                                                                mandatory
                                                            >
                                                                <v-btn
                                                                    small
                                                                    text
                                                                >
                                                                    {{ trust_accountant_label }}
                                                                </v-btn>
                                                                <v-btn
                                                                    small
                                                                    text
                                                                >
                                                                    {{ property_manager_label }}
                                                                </v-btn>
                                                            </v-btn-toggle>
                                                        </td>
                                                    </tr>
                                                    <tr v-if="process_type === 0">
                                                        <td
                                                            class="title"
                                                            align="right"
                                                            style="color: white"
                                                        >
                                                            {{ trust_accountant_label }}:
                                                        </td>
                                                        <td class="required">*</td>
                                                        <td>
                                                            <span style="background-color: white">
                                                                <multiselect
                                                                    v-model="complete_trust_accountant"
                                                                    :options="dd_param_trust_accountant_list"
                                                                    :allowEmpty="false"
                                                                    class="vue-select2 dropdown-left dropdown-400"
                                                                    :custom-label="nameWithDash"
                                                                    group-label="language"
                                                                    placeholder="Please select..."
                                                                    track-by="field_key"
                                                                    label="field_value"
                                                                    :show-labels="false"
                                                                    ><span slot="noResult"
                                                                        >Oops! No elements found. Consider changing the
                                                                        search query.</span
                                                                    ></multiselect
                                                                >
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr v-if="process_type === 1">
                                                        <td
                                                            class="title"
                                                            align="right"
                                                            style="color: white"
                                                        >
                                                            {{ property_manager_label }}:
                                                        </td>
                                                        <td class="required">*</td>
                                                        <td>
                                                            <div>
                                                                <multiselect
                                                                    v-model="complete_property_manager"
                                                                    :options="dd_param_property_manager_list"
                                                                    :allowEmpty="false"
                                                                    class="vue-select2 dropdown-left dropdown-400"
                                                                    :custom-label="nameWithDash"
                                                                    group-label="language"
                                                                    placeholder="Please select..."
                                                                    track-by="field_key"
                                                                    label="field_value"
                                                                    :show-labels="false"
                                                                    ><span slot="noResult"
                                                                        >Oops! No elements found. Consider changing the
                                                                        search query.</span
                                                                    >
                                                                </multiselect>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </v-card-text>

                                            <v-card-actions>
                                                <v-btn
                                                    small
                                                    color="primary"
                                                    class="v-step-modify-lease-code-btn"
                                                    @click="reloadPage"
                                                    >No, keep as draft
                                                </v-btn>
                                                <v-btn
                                                    small
                                                    color="primary"
                                                    class="v-step-modify-lease-code-btn"
                                                    v-if="process_type === 0"
                                                    @click="processNewProperty()"
                                                    >Process
                                                </v-btn>
                                                <v-btn
                                                    small
                                                    color="primary"
                                                    class="v-step-modify-lease-code-btn"
                                                    v-if="process_type === 1"
                                                    @click="processNewProperty()"
                                                    >Re-assign
                                                </v-btn>
                                            </v-card-actions>
                                        </v-card>
                                    </v-col>
                                    <v-col
                                        cols="12"
                                        md="6"
                                    >
                                        <v-textarea
                                            class="vuetifyText v-step-feedback"
                                            outlined
                                            label="If you have any final comments, add them here."
                                            v-model="final_comment"
                                        ></v-textarea>
                                    </v-col>
                                </v-row>
                            </v-container>

                            <v-btn
                                color="success"
                                dark
                                small
                                @click="changeStep(3)"
                                >Back
                            </v-btn>
                        </v-stepper-content>
                    </v-stepper-items>
                </v-stepper>
            </div>
        </div>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import { mapActions, mapMutations, mapState } from 'vuex';
import PropertyMainForm from '../Forms/PropertyMainForm.vue';
import PropertyDiaryForm from '../Forms/PropertyDiaryForm.vue';
import PropertyCalendarForm from '../Forms/PropertyCalendarForm.vue';
import PropertyCommunicationHistoryForm from '../Forms/PropertyCommunicationHistoryForm.vue';
import PropertyContactForm from '../Forms/PropertyContactForm.vue';
import PropertyDocumentForm from '../Forms/PropertyDocumentForm.vue';
import PropertyDocumentFormV2 from '../../../../DocumentDirectory/sections/PropertyDocumentFormV2.vue';
import PropertyFloorNUnitForm from '../Forms/PropertyFloorNUnitForm.vue';
import PropertyInspectionForm from '../Forms/PropertyInspectionForm.vue';
import PropertyInsuranceForm from '../Forms/PropertyInsuranceForm.vue';
import PropertyNoteForm from '../Forms/PropertyNoteForm.vue';
import PropertyOwnerSharesForm from '../Forms/PropertyOwnerSharesForm.vue';
import PropertyFeesNChargesForm from '../Forms/PropertyFeesNChargesForm.vue';
import PropertyTakeOnBalanceForm from '../Forms/PropertyTakeOnBalanceForm.vue';
import PropertyManagementFeeForm from '../Forms/PropertyManagementFeeForm.vue';
import PropertyManagementAgreementForm from '../Forms/PropertyManagementAgreementForm.vue';
import PropertyManagementDetailForm from '../Forms/PropertyManagementDetailForm.vue';
import PropertyBankAccountForm from '../Forms/PropertyBankAccountForm.vue';
import PropertyParkingBaysForm from '../Forms/PropertyParkingBaysForm.vue';
import PropertyFundForm from '../Forms/PropertyFundForm.vue';
import PropertyKeysForm from '../Forms/PropertyKeysForm.vue';
import PropertyRecoverableSplitForm from '../Forms/PropertyRecoverableSplitForm.vue';
import { cirrusDialog } from '../../../../plugins/mixins';

// Vue.use(SuiVue);
export default {
    props: {
        initialReadOnly: Boolean,
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_property_flag: { type: Boolean, default: false },
        force_load: { type: Number, default: 0 },
        page_form_type: { type: String, default: '' },
        country_default_settings: { type: String, default: '' },
    },
    components: {
        'property-main-form-component': PropertyMainForm,
        'property-diary-form-component': PropertyDiaryForm,
        'property-calendar-form-component': PropertyCalendarForm,
        'property-communication-history-form-component': PropertyCommunicationHistoryForm,
        'property-contact-form-component': PropertyContactForm,
        'property-document-form-component': PropertyDocumentForm,
        'property-document-v2-form-component': PropertyDocumentFormV2,
        'property-floor-n-unit-form-component': PropertyFloorNUnitForm,
        'property-inspection-form-component': PropertyInspectionForm,
        'property-insurance-form-component': PropertyInsuranceForm,
        'property-note-form-component': PropertyNoteForm,
        'property-owner-shares-form-component': PropertyOwnerSharesForm,
        'property-fees-charges-form-component': PropertyFeesNChargesForm,
        'property-take-on-balances-form-component': PropertyTakeOnBalanceForm,
        'property-man-fee-form-component': PropertyManagementFeeForm,
        'property-man-agreement-form-component': PropertyManagementAgreementForm,
        'property-man-detail-form-component': PropertyManagementDetailForm,
        'property-bank-account-form-component': PropertyBankAccountForm,
        'property-parking-bays-form-component': PropertyParkingBaysForm,
        'property-fund-form-component': PropertyFundForm,
        'property-keys-component': PropertyKeysForm,
        'property-recoverable-split-component': PropertyRecoverableSplitForm,
    },
    data() {
        return {
            property_manager_label: 'Property Manager',
            trust_accountant_label: 'Trust Accountant',
            loading_page_setting: false,
            step: 1,
            show_step_2_button: false,
            show_step_3_button: false,
            lease_commencement_date: '31/12/2999',
            error_server_msg: {},
            error_server_msg2: [],
            process_type: 0,
            complete_trust_accountant: { field_key: '' },
            complete_trust_accountant_list: [],
            complete_property_manager: { field_key: '' },
            complete_property_manager_list: [],
            final_comment: '',
            btn_loading_setting: false,
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'tour_steps',
            'lease_details',
            'lease_details_old',
            'dd_param_trust_accountant_list',
            'dd_param_property_manager_list',
            'dd_param_trust_accountant_default',
            'auto_diarise',
            'doc_active_version',
            'sys_ver_control_list',
        ]),
    },
    mounted() {
        let country_default_settings = JSON.parse(atob(this.country_default_settings));
        this.property_manager_label = this.ucwords(country_default_settings.property_manager);
        this.trust_accountant_label = this.ucwords(country_default_settings.trust_accountant);
        this.fetchFormVersionControl();
        this.fetchParamTAList();
        this.fetchParamPMList();
        this.fetchPMAutoDiarise();
        this.fetchParamEmailCenSetup();
        // console.log(localStorage.getItem('cirrus8_api_url'));
    },
    methods: {
        ...mapActions([
            'fetchCountryList',
            'fetchAccountIncomeGroupedList',
            'fetchAccountExpenseGroupedList',
            'fetchBondPropertyList',
            'fetchParamDivisionList',
            'fetchParamLeaseTypeList',
            'fetchParamTenantTypeList',
            'fetchRetailCategoryList',
            'fetchRetailSubCategoryList',
            'fetchRetailFineCategoryList',
            'fetchParamTAList',
            'fetchParamPMList',
            'fetchPMAutoDiarise',
            'fetchParamEmailCenSetup',
            'fetchFormVersionControl',
        ]),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_TOUR_STEPS']),
        nameWithDash({ field_key, field_value }) {
            if (!field_value) {
                return 'Please select...';
            }
            return `${field_key} — ${field_value}`;
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        reloadPage() {
            window.location.reload();
        },
        getPropertyCode: function (value) {
            this.lease_commencement_date = value.lease_commencement_date;
            this.show_step_2_button = true;
        },
        nextStepper: function (value) {
            console.log('step 2');
            this.step = 2;
        },
        changeStep: function (step) {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            switch (step) {
                case 1:
                    if (this.error_server_msg2.length <= 0) {
                        bus.$emit('loadLeaseMainFormSection', '');
                        this.step = step;
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                    }

                    break;
                case 2:
                    if (this.error_server_msg2.length <= 0) {
                        this.step = step;
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                        bus.$emit('loadPropertyManagementDetailFormSection', '');
                        bus.$emit('loadPropertyManagementAgreementFormSection', '');
                        bus.$emit('loadPropertyParkingBaysFormSection', '');
                        bus.$emit('loadPropertyManagementFeeFormSection', '');
                        bus.$emit('loadPropertySundryChargesFormSection', '');
                        bus.$emit('loadPropertyFundFormSection', '');
                        bus.$emit('loadPropertyKeysFormSection', '');
                    }

                    break;
                case 3:
                    //
                    this.btn_loading_setting = true;
                    let api_url = 'temp/property/fetch/pm-step-2-is-valid';
                    let form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('no_load', true);
                    this.$api.post(api_url, form_data).then((response) => {
                        this.btn_loading_setting = false;
                        this.error_server_msg2 = response.data.validation_errors;

                        if (this.error_server_msg2.length <= 0) {
                            this.step = step;
                            bus.$emit('loadPropertyDiaryFormSection', '');
                            bus.$emit('loadPropertyFloorNUnitsFormSection', '');
                            bus.$emit('loadPropertyParkingBaysFormSection', '');
                            bus.$emit('loadPropertyNoteSection', '');
                            bus.$emit('loadPropertyInspectionSection', '');
                            bus.$emit('loadPropertyInsuranceSection', '');
                            bus.$emit('loadPropertyContactSection', '');
                            bus.$emit('loadPropertyDocumentSection', '');

                            this.error_server_msg = {};
                            this.error_server_msg2 = [];
                        }
                    });
                    break;
                case 4:
                    if (this.error_server_msg2.length <= 0) {
                        this.step = step;
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                    }
                    break;
                default:
                    break;
            }
        },
        processNewProperty: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            if (this.property_code === '') {
                this.error_server_msg2.push(['There is no property found']);
            }

            if (this.process_type === 0) {
                if (this.complete_trust_accountant.field_key === '') {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Please select a ' + this.trust_accountant_label + '.',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    cirrusDialog(dialog_prop);
                    this.error_server_msg2.push(['Please select a ' + trust_accountant_label + '.']);
                }
            } else {
                if (this.complete_property_manager.field_key === '') {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Please select a ' + this.property_manager_label + '.',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    cirrusDialog(dialog_prop);
                    this.error_server_msg2.push(['Please select a ' + this.property_manager_label + '.']);
                }
            }
            if (this.error_server_msg2.length === 0) {
                let api_url = '';
                if (this.process_type === 0) {
                    api_url = this.cirrus8_api_url + 'api/property/process/submit-new-property-form';
                } else {
                    api_url = this.cirrus8_api_url + 'api/property/process/reassign-new-property-form';
                }
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('process_type', this.process_type);
                form_data.append('trust_accountant', this.complete_trust_accountant.field_value);
                form_data.append('property_manager', this.complete_property_manager.field_value);
                form_data.append('final_comment', this.final_comment);
                form_data.append('no_load', true);
                this.$api.post(api_url, form_data).then((response) => {
                    this.error_server_msg2 = response.data.validation_errors;
                    this.loading_page_setting = false;
                    this.form_no_btn = false;
                    window.open(
                        '?module=properties&command=property_approval_page&property_code=' + this.property_code,
                        '_parent', // <- This is what makes it open in a new window.
                    );
                });
            }
        },
        printLease: function () {
            window.open(
                '?module=leases&command=lease_print_page&property_code=' + this.property_code,
                '_blank', // <- This is what makes it open in a new window.
            );
        },
    },
    watch: {
        dd_param_trust_accountant_default: function () {
            this.complete_trust_accountant = this.dd_param_trust_accountant_default;
        },
    },
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
