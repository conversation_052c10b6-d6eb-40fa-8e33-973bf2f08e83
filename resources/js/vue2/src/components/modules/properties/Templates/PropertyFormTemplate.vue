<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <v-row class="ma-0">
            <v-col
                xs="12"
                sm="12"
                md="9"
                lg="9"
            >
                <v-toolbar flat>
                    <v-toolbar-title>
                        <cirrus-page-header :title="property_page_title" />
                    </v-toolbar-title>
                </v-toolbar>

                <div class="page-form pt-7">
                    <v-row
                        class="form-row"
                        v-if="form_mode === 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Property:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <multiselect
                                v-model="property_code"
                                :options="property_list"
                                group-values="field_group_values"
                                :groupSelect="false"
                                group-label="field_group_names"
                                :group-select="true"
                                class="vue-select2 dropdown-left dropdown-400"
                                :custom-label="nameWithDash"
                                placeholder="Select a property"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                            <v-btn
                                depressed
                                elevation="0"
                                small
                                color="normal"
                                height="30"
                                class="rounded-l-0"
                                v-on:click="reloadPage()"
                            >
                                <v-icon>arrow_right</v-icon>
                            </v-btn>
                            <v-tooltip top>
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        depressed
                                        elevation="0"
                                        small
                                        color="primary"
                                        height="30"
                                        v-if="form_mode === 0"
                                        @click="showNewProperty()"
                                        v-bind="attrs"
                                        v-on="on"
                                    >
                                        <v-icon>add</v-icon>
                                    </v-btn>
                                </template>
                                <span>New Property</span>
                            </v-tooltip>
                        </v-col>
                    </v-row>
                    <v-row
                        class="form-row"
                        v-if="form_mode === 1"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Enter A Property Code:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-input
                                class=""
                                v-model="new_property_code"
                                size="10"
                                :id="'new_property_code'"
                                :edit_form="true"
                                :error_msg="error_msg"
                            ></cirrus-input>
                            <v-btn
                                class="form-text-button ma-auto rounded-l-0"
                                color="primary"
                                depressed
                                elevation="0"
                                small
                                @click="checkPropertyCode()"
                                style="margin-top: -1px !important"
                            >
                                Create New
                            </v-btn>
                            <v-tooltip top>
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        depressed
                                        elevation="0"
                                        small
                                        color="black"
                                        height="30"
                                        class="rounded-a-0"
                                        dark
                                        v-if="form_mode === 1"
                                        @click="cancelNewProperty()"
                                        v-bind="attrs"
                                        v-on="on"
                                    >
                                        <v-icon color="red">close</v-icon>
                                    </v-btn>
                                </template>
                                <span>Cancel</span>
                            </v-tooltip>
                        </v-col>
                    </v-row>
                </div>
            </v-col>
            <v-col
                class="pr-2"
                xs="12"
                sm="12"
                md="3"
                lg="3"
                v-if="property_code.field_key !== '' && form_mode === 0"
                v-on:dblclick="doubleClickForm()"
            >
                <label
                    v-if="
                        (!formSectionReadOnly(pm_property_form_read_only, 'PROPERTY', 'PROPERTY_MAIN_DETAILS') &&
                            form_notes.length > 0) ||
                        edit_form
                    "
                    class="font-weight-black"
                    for="main-notes"
                    >Notes:</label
                >
                <textarea
                    id="main-notes"
                    class="noteTextArea font-weight-bold"
                    style="width: 100%"
                    rows="4"
                    v-model="form_notes"
                    v-if="edit_form"
                ></textarea>
                <div
                    class="noteTextArea"
                    v-if="!edit_form"
                    style="max-height: 115px; overflow: auto"
                >
                    <pre
                        class="pre-content font-weight-bold"
                        v-if="form_notes.length > 0"
                        >{{ form_notes }}</pre
                    >
                    <pre
                        class="pre-content font-weight-bold"
                        style="color: grey"
                        v-if="form_notes.length === 0 && is_inactive == 0"
                    >
(double click to add note)</pre
                    >
                </div>
                <v-btn
                    v-if="edit_form"
                    class="float-end mt-1"
                    color="success"
                    dense
                    x-small
                    @click="processSaveFormNotes()"
                    >Save
                </v-btn>
            </v-col>
        </v-row>
        <br />
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <v-card
            color="grey lighten-4"
            flat
            tile
            v-show="property_code.field_key !== '' && form_mode === 0"
        >
            <v-toolbar
                v-show="false"
                dense
                flat
                class="shortcut-div"
                height="30"
                style="z-index: 9999"
            >
                <v-spacer></v-spacer>
                <v-toolbar-items>
                    <v-menu
                        offset-y
                        offset-x
                    >
                        <template v-slot:activator="{ on }">
                            <v-btn
                                outlined
                                color="grey darken-1"
                                dark
                                v-on="on"
                                id="option-button"
                                class="v-step-option"
                            >
                                <v-icon left>settings</v-icon>
                                Options
                            </v-btn>
                        </template>
                        <v-list
                            dense
                            subheader
                            class="menu-option"
                            style="left: -120px"
                        >
                            <v-list-item
                                v-if="is_inactive == 0"
                                @click="processPropertyInactiveStatus(1)"
                            >
                                <v-list-item-avatar>
                                    <v-avatar
                                        size="32px"
                                        tile
                                    >
                                        <v-icon>mdi-alert</v-icon>
                                    </v-avatar>
                                </v-list-item-avatar>
                                <v-list-item-title style="text-align: left">Set as Inactive</v-list-item-title>
                            </v-list-item>
                            <v-list-item
                                v-for="(shortcuts_data, shortcuts_index) in shortcuts"
                                :key="shortcuts_index"
                                v-if="
                                    shortcuts_data.shortcut_code !== 'sms_send' ||
                                    (sms_sending_setup && is_inactive != 1)
                                "
                                @click="goToShortcut(shortcuts_data.shortcut_code)"
                            >
                                <v-list-item-avatar>
                                    <v-avatar
                                        size="32px"
                                        tile
                                    >
                                        <v-icon>{{ shortcuts_data.icon }}</v-icon>
                                    </v-avatar>
                                </v-list-item-avatar>
                                <v-list-item-title style="text-align: left"
                                    >{{ shortcuts_data.title }}
                                </v-list-item-title>
                            </v-list-item>
                        </v-list>
                    </v-menu>
                </v-toolbar-items>
            </v-toolbar>
        </v-card>
        <div
            class="form-row"
            v-if="property_code.field_key !== '' && is_inactive == 1"
        >
            <v-card
                class="section-toolbar"
                :class="is_inactive == 0 ? 'subHeader' : 'subHeader-inactive'"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black pr-2">Property Status</h6>
                    <v-chip
                        small
                        label
                        color="primary"
                        v-if="is_inactive == 0"
                        >Active
                    </v-chip>
                    <v-chip
                        small
                        label
                        color="subPrimary"
                        v-if="is_inactive == 1"
                        >Inactive
                    </v-chip>
                    <span
                        class="pl-2 pr-2"
                        v-if="is_inactive == 1"
                    >
                        <cirrus-icon-date-picker
                            :id="'is_inactive_date' + String(Math.random()).replace('.', '')"
                            :index="is_inactive_date_index"
                            :size="'40'"
                            v-model="is_inactive_date"
                            :edit_form="true"
                        ></cirrus-icon-date-picker>
                    </span>
                    <v-btn
                        x-small
                        color="success"
                        v-if="is_inactive_date !== is_inactive_date_old"
                        @click="processPropertyInactiveStatus(1)"
                        :loading="is_inactive_loading"
                        >Update Date
                    </v-btn>
                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        color="subPrimary"
                        v-if="is_inactive == 1"
                        @click="processPropertyInactiveStatus(0)"
                        :loading="is_inactive_loading"
                        >Set as Active
                    </v-btn>
                    <v-btn
                        x-small
                        color="subPrimary"
                        v-if="is_inactive == 0"
                        @click="processPropertyInactiveStatus(1)"
                        :loading="is_inactive_loading"
                        >Set as Inactive
                    </v-btn>
                </v-card-actions>
            </v-card>
        </div>
        <v-tabs
            color="primary"
            icons-and-text
            class="cirrus-tab-theme"
            v-model="currentTab"
            v-show="property_code.field_key !== '' && form_mode === 0"
            show-arrows
        >
            <v-tabs-slider color="white"></v-tabs-slider>
            <v-tab
                v-for="(tab, index) in visibleTabs"
                :key="'tab-' + index"
            >
                {{ tab.name }}
                <v-icon
                    small
                    dense
                >
                    {{ tab.icon }}
                </v-icon>
            </v-tab>
            <v-col
                class="text-right pt-2"
                v-show="property_code.field_key !== '' && form_mode === 0"
            >
                <v-menu
                    offset-y
                    offset-x
                >
                    <template v-slot:activator="{ on }">
                        <v-btn
                            depressed
                            small
                            color="grey darken-1"
                            dark
                            v-on="on"
                            id="option-button"
                            v-show="property_code.field_key !== '' && form_mode === 0"
                        >
                            <v-icon
                                small
                                left
                                >settings
                            </v-icon>
                            Options
                        </v-btn>
                    </template>
                    <v-list
                        dense
                        subheader
                        class="menu-option"
                        style="left: -120px"
                    >
                        <v-list-item
                            v-if="is_inactive == 0"
                            @click="processPropertyInactiveStatus(1)"
                        >
                            <v-list-item-avatar>
                                <v-avatar
                                    size="32px"
                                    tile
                                >
                                    <v-icon>mdi-alert</v-icon>
                                </v-avatar>
                            </v-list-item-avatar>
                            <v-list-item-title style="text-align: left">Set as Inactive</v-list-item-title>
                        </v-list-item>
                        <v-list-item
                            v-for="(shortcuts_data, shortcuts_index) in shortcuts"
                            :key="shortcuts_index"
                            v-if="
                                shortcuts_data.shortcut_code !== 'sms_send' || (sms_sending_setup && is_inactive != 1)
                            "
                            @click="goToShortcut(shortcuts_data.shortcut_code)"
                        >
                            <v-list-item-avatar>
                                <v-avatar
                                    size="32px"
                                    tile
                                >
                                    <v-icon>{{ shortcuts_data.icon }}</v-icon>
                                </v-avatar>
                            </v-list-item-avatar>
                            <v-list-item-title style="text-align: left">{{ shortcuts_data.title }}</v-list-item-title>
                        </v-list-item>
                    </v-list>
                </v-menu>
            </v-col>
        </v-tabs>
        <v-tabs-items
            v-model="currentTab"
            v-show="property_code.field_key !== '' && form_mode === 0"
        >
            <v-tab-item
                v-for="(tab, index) in visibleTabs"
                :key="'tab-item-' + index"
            >
                <template v-for="(component, compIndex) in getComponents(tab)">
                    <component
                        :key="'comp-' + compIndex"
                        :is="component"
                        v-if="component"
                        :property_code="property_code.field_key"
                        :propertyCode="property_code.field_key"
                        :version_id="version_id"
                        :page_form_type="page_form_type"
                        :country_default_settings="initialCountryDefaultSettings"
                        :is_inactive="is_inactive"
                        :isInactive="is_inactive"
                        :new_property="new_property"
                    >
                    </component>
                </template>
            </v-tab-item>
        </v-tabs-items>

        <v-dialog
            v-model="show_balance_modal"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Unpaid Invoices and Property Balances
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_balance_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div
                        id="show_balance_html"
                        v-html="show_balance_html"
                    ></div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_cash_book_activity"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Cash Book
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_cash_book_activity = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div
                        id="show_cash_book_html"
                        v-html="show_cash_book_html"
                    ></div>
                </v-card-text>
            </v-card>
        </v-dialog>
        <sms-sending-modal-component
            key="shortcuts"
            v-if="isPropertyFormLive()"
        ></sms-sending-modal-component>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import { mapActions, mapMutations, mapState } from 'vuex';
import PropertyMainForm from '../Forms/PropertyMainForm.vue';

import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import Vue from 'vue';
import axios from 'axios';
import { PROPERTY_SUMMARY_TABS, PROPERTY_TAB_KEYS } from '../../../../modules/Property/constants';

Vue.component('sms-sending-modal-component', require('../../Generic/Forms/SMSSendingModal.vue').default);
export default {
    props: {
        initialPageFormType: String,
        initialPropertyCode: String,
        initialVersionId: String,
        initialCountryDefaultSettings: String,
    },
    components: {
        'property-main-form-component': PropertyMainForm,
    },
    data() {
        return {
            property_code: { field_key: '', field_value: 'Please select a property' },
            version_id: null,
            property_list: [],
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            read_only: this.initialReadOnly,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            template_tab: null,
            loading_content_setting: false,
            loading_page_setting: false,
            property_existed: true,
            tab: null,
            shortcuts: [
                { icon: 'table_chart', title: 'Property Abstract', shortcut_code: 'property_abstract' },
                { icon: 'account_balance', title: 'Tenancy Schedule', shortcut_code: 'tenant_schedule' },
                {
                    icon: 'account_balance',
                    title: 'Cash Book (current month)',
                    shortcut_code: 'cash_book_current_month',
                },
                { icon: 'account_balance', title: 'Cash Book (all dates)', shortcut_code: 'cash_book_all_date' },
                {
                    icon: 'receipt',
                    title: 'Unpaid Invoices and Property Balances',
                    shortcut_code: 'unpaid_invoice_n_balances',
                },
                { icon: 'email', title: 'Email Address Book', shortcut_code: 'email_address_book' },
                { icon: 'message', title: 'Send SMS', shortcut_code: 'sms_send' },
                { icon: 'print', title: 'Print', shortcut_code: 'print' },
            ],
            force_load: false,
            property_page_title: 'Property Summary',
            error_server_msg: {},
            error_server_msg2: [],
            show_activity_log_modal: true,
            new_property_code: '',
            pdfLoading: false,
            show_balance_modal: false,
            show_balance_html: '',
            files: [],
            show_inactive_property: true,
            is_inactive: 0,
            is_inactive_old: null,
            is_inactive_loading: false,
            is_inactive_date: this.getDateToday(),
            is_inactive_date_old: null,
            is_inactive_date_index: 0,
            form_notes: '',
            form_notes_old: '',
            edit_form: false,
            show_cash_book_activity: false,
            show_cash_book_html: '',
            currency_symbol: '$',
            currentTab: null,
            tabs: PROPERTY_SUMMARY_TABS,
            new_property: false,
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'auto_diarise',
            'sms_sending_setup',
            'doc_active_version',
            'sys_ver_control_list',
            'pm_property_form_read_only',
        ]),
        isMultiplePropertyLedger() {
            return !!this.sys_ver_control_list?.isMultiplePropertyLedger;
        },
        visibleTabs() {
            const tabs = this.tabs || [];
            return tabs.filter((t) => this.shouldShowTab(t));
        },
    },
    mounted() {
        this.fetchFormVersionControl();
        this.loadPropertyList();
        this.fetchCountryList();
        this.fetchTAAutoDiarise();
        this.fetchParamEmailCenSetup();
        this.fetchParamSMSSendingSetup();
        if (this.initialPropertyCode) {
            this.version_id = this.initialVersionId;
        }

        let country_settings = JSON.parse(atob(this.initialCountryDefaultSettings));
        this.currency_symbol = country_settings.currency_symbol;
    },
    methods: {
        ...mapActions([
            'fetchCountryList',
            'fetchAccountList',
            'fetchTAAutoDiarise',
            'fetchParamEmailCenSetup',
            'fetchParamSMSSendingSetup',
            'fetchFormVersionControl',
        ]),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_LEASE_CODE', 'SET_TOUR_STEPS']),
        shouldShowTab(tab) {
            if (tab.value === PROPERTY_TAB_KEYS.LEDGER && !this.isMultiplePropertyLedger) {
                return false;
            }
            return true;
        },
        loadPropertyList: function () {
            let active_only = '1';
            if (this.show_inactive_property) active_only = '0';
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyFormTemplate');
            form_data.append('active_only', active_only);
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-dropdown-list', form_data).then((response) => {
                this.property_list = response.data.group;
                let property_list_ungroup = response.data.data;
                if (this.initialPropertyCode) {
                    this.property_code = this.getValueInList(this.initialPropertyCode, property_list_ungroup);
                }
                this.property_index = Math.random();
            });
        },
        loadPropertyStatus: function () {
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyFormTemplate');
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('no_load', true);
            this.$api.post('property/fetch/property-status-detail', form_data).then((response) => {
                this.is_inactive = response.data.is_inactive;
                this.is_inactive_date = response.data.is_inactive_date;
                this.is_inactive_date_old = response.data.is_inactive_date;
                bus.$emit('setInactiveStatus', this.is_inactive);
            });
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        goToHref: function (param1, param2) {
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        goToShortcut: function (parameter) {
            var today = new Date();
            var dd = today.getDate();
            var mm = today.getMonth() + 1;
            var yyyy = today.getFullYear();
            var single_date = yyyy + '-' + mm + '-' + dd;
            let url = '';
            let from_date = '';
            let to_date = '';
            let run_date = '';
            switch (parameter) {
                case 'property_abstract':
                    this.loading_page_setting = true;
                    var form_data = new FormData();
                    form_data.append('properties', this.property_code.field_key);
                    form_data.append('no_load', true);
                    form_data.append('report_ids', '61');
                    form_data.append('singleDate', single_date);
                    this.$api.post('reports/download/pdf', form_data, { responseType: 'blob' }).then((response) => {
                        this.loading_page_setting = false;
                        let blob = new Blob([response.data], { type: 'application/pdf' });
                        let a = document.createElement('a');
                        a.style = 'display: none';
                        document.body.appendChild(a);
                        url = window.URL.createObjectURL(blob);
                        a.href = url;
                        var fileName = 'Property_Abstract';
                        a.download = fileName + '.pdf';
                        a.click();
                    });
                    break;
                case 'print':
                    window.open(
                        '?module=properties&command=property_print_page&is_live=1&property_code=' +
                            this.property_code.field_key,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'sms_send':
                    bus.$emit('toggleSMSSendingModal', {
                        property_code: this.property_code.field_key,
                        lease_code: '',
                        company_code: '',
                        contact_detail_id: '',
                        form_section: this.page_form_type,
                    });
                    break;
                case 'tenant_schedule':
                    this.loading_page_setting = true;
                    var form_data = new FormData();
                    form_data.append('properties', this.property_code.field_key);
                    form_data.append('leases', '');
                    form_data.append('no_load', true);
                    form_data.append('user_type', localStorage.getItem('user_type'));
                    if (sessionStorage.getItem('sso_key'))
                        form_data.append('app_key', sessionStorage.getItem('sso_key'));
                    form_data.append('report_ids', '4');
                    form_data.append('singleDate', single_date);
                    form_data.append('format', 'pdf');
                    axios
                        .post(this.cirrus8_api_url + 'api/reports/download/pdf', form_data, { responseType: 'blob' })
                        .then((response) => {
                            this.loading_page_setting = false;
                            let blob = new Blob([response.data], { type: 'application/pdf' });
                            let a = document.createElement('a');
                            a.style = 'display: none';
                            document.body.appendChild(a);
                            url = window.URL.createObjectURL(blob);
                            a.href = url;
                            var fileName = 'Tenancy_Schedule';
                            a.download = fileName + '.pdf';
                            a.click();
                        });
                    break;
                case 'unpaid_invoice_n_balances':
                    this.show_balance_modal = true;
                    this.loading_page_setting = true;
                    this.show_balance_html = '';
                    var cmm = today.getMonth() + 1;
                    mm = today.getMonth() + 2;
                    var yyyy2 = today.getFullYear();
                    if (mm > 12) {
                        mm = 1;
                        yyyy2++;
                    }
                    cmm = cmm < 10 ? '0' + cmm : cmm;
                    mm = mm < 10 ? '0' + mm : mm;
                    run_date = dd + '/' + cmm + '/' + yyyy;
                    url = '?module=ap&command=balances';
                    var form_data = new FormData();
                    form_data.append('property', this.property_code.field_key);
                    form_data.append('screen', 'propertySummary');
                    form_data.append('format', 'screen');
                    form_data.append('action', 'fetch');
                    form_data.append('from', 'propertySummary');
                    form_data.append('reportType', '1');
                    form_data.append('includeOutstanding', '1');
                    form_data.append('runDate', run_date);
                    form_data.append('app_origin', 'property_page');
                    form_data.append('no_load', true);
                    axios.post(url, form_data).then((response) => {
                        this.loading_page_setting = false;
                        this.show_balance_html = response.data;
                    });
                    break;
                    break;
                case 'email_address_book':
                    window.open(
                        '?module=administration&command=emailAddressBook&search_type=property_or_lease&propertyID=' +
                            this.property_code.field_key,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'cash_book_current_month':
                    this.show_cash_book_activity = true;
                    this.loading_page_setting = true;
                    this.show_cash_book_html = '';
                    var cmm = today.getMonth() + 1;
                    mm = today.getMonth() + 2;
                    var yyyy2 = today.getFullYear();
                    if (mm > 12) {
                        mm = 1;
                        yyyy2++;
                    }
                    cmm = cmm < 10 ? '0' + cmm : cmm;
                    mm = mm < 10 ? '0' + mm : mm;
                    from_date = '01' + '/' + cmm + '/' + yyyy;
                    to_date = '01' + '/' + mm + '/' + yyyy2;
                    url = '?module=accountingReports&command=cashBook&action=finalise';
                    var form_data = new FormData();
                    form_data.append('propertyID', this.property_code.field_key);
                    form_data.append('reportType', 'property');
                    form_data.append('transactionOption', 'allTransactions');
                    form_data.append('accountOption', 'allAccountCodes');
                    form_data.append('format', 'screen');
                    form_data.append('sortOption', 'sortDate');
                    form_data.append('fromDate', from_date);
                    form_data.append('toDate', to_date);
                    form_data.append('action', 'finalise');
                    form_data.append('from', 'propertySummary');

                    form_data.append('app_origin', 'property_page');
                    form_data.append('no_load', true);
                    axios.post(url, form_data).then((response) => {
                        this.loading_page_setting = false;
                        this.show_cash_book_html = response.data;
                    });
                    break;
                case 'cash_book_all_date':
                    this.show_cash_book_activity = true;
                    this.loading_page_setting = true;
                    this.show_cash_book_html = '';
                    url = '?module=accountingReports&command=cashBook&action=finalise';
                    var form_data = new FormData();
                    form_data.append('propertyID', this.property_code.field_key);
                    form_data.append('reportType', 'property');
                    form_data.append('transactionOption', 'allTransactions');
                    form_data.append('accountOption', 'allAccountCodes');
                    form_data.append('format', 'screen');
                    form_data.append('sortOption', 'sortDate');
                    form_data.append('fromDate', '01/01/1900');
                    form_data.append('toDate', '31/12/2999');
                    form_data.append('action', 'finalise');
                    form_data.append('from', 'propertySummary');

                    form_data.append('app_origin', 'property_page');
                    form_data.append('no_load', true);
                    axios.post(url, form_data).then((response) => {
                        this.loading_page_setting = false;
                        this.show_cash_book_html = response.data;
                    });
                    break;
            }
        },
        getPropertyCode: function (value) {
            let property_code = value.property_code;
            let property_name = value.property_name;
            this.cancelNewProperty(false);
            // this.loadLeaseList();
            this.property_code = { field_key: property_code, field_value: property_name };
        },
        showNewProperty: function () {
            this.loadPropertyList();

            this.form_mode = 1;
            this.property_page_title = 'New Property';
            this.new_property_code = '';

            this.updatePageTitle(this.property_page_title);
        },
        cancelNewProperty: function () {
            this.reset_filter();

            this.property_page_title = 'Property Summary';
            this.form_mode = 0;

            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_property_code = '';
            this.property_existed = true;

            this.updatePageTitle(this.property_page_title);
        },
        checkPropertyCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            let new_property_code = this.new_property_code.toUpperCase().trim();

            this.new_property_code = new_property_code;
            if (new_property_code.length > 10) {
                this.error_server_msg2.push(['Property code allows 10 characters only']);
            } else if (new_property_code === '') {
                this.error_server_msg2.push(['Please input a valid property code']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('page_source', 'PropertyFormTemplate');
                form_data.append('new_property_code', new_property_code);
                form_data.append('no_load', true);
                this.$api.post('property/fetch/check-code-if-exist', form_data).then((response) => {
                    this.error_server_msg2 = response.data.validation_errors;
                    this.property_existed = response.data.property_existed;
                    this.loading_page_setting = false;
                    if (!this.property_existed) {
                        let form_data = new FormData();
                        form_data.append('propertyCode', new_property_code);
                        form_data.append('no_load', true);
                        this.$api.post('property/store/main-form-details', form_data).then((response) => {
                            this.property_code = {
                                field_key: new_property_code,
                                field_value: 'New property',
                            };
                            this.property_existed = false;
                            this.form_mode = 0;
                            this.property_page_title = 'New Property';
                            this.new_property_code = '';
                            this.new_property = true;
                            this.updatePageTitle(this.property_page_title);
                            this.loadPropertyList();
                        });
                    }
                });
            }
        },
        reset_filter: function () {
            this.property_code = { field_key: '', field_value: 'Please select...' };
            this.property_list = [];
            this.SET_PROPERTY_CODE('');
            this.loadPropertyList();
        },
        handlePdfUpload: function () {
            let files = this.$refs.file.files;

            if (files.length > 0) {
                this.pdfLoading = true;

                let counter = 0;
                for (var i = 0; i < files.length; i++) {
                    let name = files[i].name;
                    let fsize = Math.round(files.item(i).size / 1024);

                    if (fsize > 5120) {
                        this.pdfLoading = false;
                        this.showAlert('error', 'File size should not exceed 5MB', 'top');
                        // alert('File size should not exceed 5MB');
                        break;
                    } else {
                        let params = new FormData();
                        params.append('un', this.formData.un);
                        params.append('currentDB', this.formData.currentDB);
                        params.append('user_type', this.formData.user_type);
                        params.append('input_file', files[i]);

                        // axios.post(this.cirrus8ApiUrl + 'api/ai/store-temporary-file', params)
                        //     .then(response => {
                        //       let base = (window.location.href).split('?')[0];
                        //       base = base.replace('index.php', '');
                        //
                        //       let file = {
                        //         name: name,
                        //         new: response.data.new_file_name,
                        //         urlAbsolute: response.data.new_file,
                        //         urlRelative: base + 'getFile.php?fileID=' + response.data.new_path_file,
                        //       };
                        //       this.files.push(file);
                        //
                        //       counter++;
                        //       if (counter === files.length) {
                        //         this.displayPdf();
                        //       }
                        //     });
                    }
                }
            }
        },
        async processPropertyInactiveStatus(status) {
            if (this.is_inactive_date === null) this.is_inactive_date = this.getDateToday();
            if (status === 1) {
                // considering date is in dd/mm/yyyy format
                let parts = this.is_inactive_date.split('/');
                let inputDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));

                // create current date upto midnight
                let currentDate = new Date();
                currentDate.setHours(0, 0, 0, 0);

                // compare if input date is a future date
                if (inputDate > currentDate) {
                    let dialog_prop = {
                        title: 'warning',
                        message: "Inactive date can not be after today's date",
                        icon_show: true,
                    };
                    cirrusDialog(dialog_prop);
                    this.is_inactive_date = this.is_inactive_date_old;
                    this.is_inactive_loading = false;
                    return;
                }
            }
            this.$api
                .post('property/fetch/bank-balances', {
                    property_code: this.property_code.field_key,
                })
                .then((response) => {
                    (async () => {
                        let total_amount_number = Number(response.data.trial_balance_closing_balance);
                        let total_amount = Number(response.data.trial_balance_closing_balance).toFixed(2);

                        if (total_amount_number !== 0 && status === 1 && this.is_inactive == 0) {
                            let dialog_prop = {
                                title: 'Property Balance Alert',
                                message: `The property currently has a balance of ${this.currency_symbol}${total_amount}\nAre you sure you want to make it inactive?`,
                                icon_show: true,
                                buttons_right: [
                                    { label: 'Yes, set as Inactive', value: 1, color: 'primary' },
                                    { label: 'Cancel', value: 2 },
                                ],
                            };

                            const result = await cirrusDialog(dialog_prop);
                            if (result !== 1) {
                                return;
                            }
                        }

                        this.setPropertyInactiveStatus(status);
                    })();
                });
        },
        setPropertyInactiveStatus: function (status) {
            this.is_inactive_loading = true;
            if (status === 1 && this.is_inactive_date === null) this.is_inactive_date = this.getDateToday();
            let form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('status', status);
            form_data.append('inactive_date', this.is_inactive_date);
            form_data.append('no_load', true);
            this.$api.post('property/process/change-property-inactive-status', form_data).then(async (response) => {
                this.is_inactive_loading = false;
                this.loadPropertyList();
                bus.$emit('setInactiveStatus', status);
                this.is_inactive = status;
                this.loadPropertyStatus();
            });
        },
        processSaveFormNotes: function () {
            let text_area_data = this.form_notes.trim(); // Remove unnecessary white spaces

            var form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('notes', text_area_data);
            this.$api.post('property/update/main-notes', form_data).then((response) => {
                this.loadFormNotes();
                bus.$emit('loadPropertyMainFormSection', this.property_code);
            });
        },
        loadFormNotes: function () {
            this.form_notes = '';
            if (this.property_code.field_key !== '') {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('no_load', true);
                this.$api.post('property/fetch/main-notes', form_data).then((response) => {
                    this.form_notes = response.data.form_notes;
                    this.form_notes_old = response.data.form_notes;
                });
            }
        },
        doubleClickForm: function () {
            bus.$emit('triggerDoubleClick');
        },
        reloadPage: function () {
            if (this.property_code.field_key !== '') {
                bus.$emit('loadPropertyBankAccountFormSection', '');
                bus.$emit('loadPropertyBudgetSection', '');
                bus.$emit('loadPropertyCalendarFormSection', '');
                bus.$emit('loadPropertyEmailSection', this.property_code);
                bus.$emit('loadPropertySMSSection', this.property_code);
                bus.$emit('loadPropertyContactSection', this.property_code);
                bus.$emit('loadPropertyDiaryFormSection', this.property_code);
                bus.$emit('loadPropertyDocumentSection', this.property_code);
                bus.$emit('loadPropertySundryChargesFormSection', this.property_code);
                bus.$emit('loadPropertyFloorNUnitsFormSection', this.property_code);
                bus.$emit('loadPropertyFundFormSection', this.property_code);
                bus.$emit('loadPropertyInspectionSection', this.property_code);
                bus.$emit('loadPropertyInsuranceSection', this.property_code);
                bus.$emit('loadPropertyKeysFormSection', this.property_code);
                bus.$emit('loadPropertyMainFormSection', this.property_code);
                bus.$emit('loadPropertyManagementAgreementFormSection', this.property_code);
                bus.$emit('loadPropertyManagementDetailFormSection', this.property_code);
                bus.$emit('loadPropertyManagementFeeFormSection', this.property_code);
                bus.$emit('loadPropertyNoteSection', this.property_code);
                bus.$emit('loadPropertyOwnerSharesFormSection', this.property_code);
                bus.$emit('loadPropertyParkingBaysFormSection', this.property_code);
                bus.$emit('loadPropertyRecoverableSplitFormSection', this.property_code);
                bus.$emit('loadPropertyTakeOnBalancesFormSection', this.property_code);
            }
        },
        getComponents(tab) {
            if (tab.components) {
                return Object.values(tab.components);
            }
            return [];
        },
    },
    watch: {
        property_code: function () {
            // this.changeSearchType('property_code');
            if (this.property_code.field_key !== '') {
                this.loadPropertyStatus();
                this.loadFormNotes();
            }
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
        show_inactive_property: function () {
            this.loadPropertyList();
        },
        nameWithDash({ field_key, field_value, lease_property, is_inactive }) {
            if (`${field_key}` === '') return `${field_value}`;
            let inactive_char = '';
            switch (this.search_type) {
                case 1:
                    if (!field_value) {
                        return 'Please select...';
                    }
                    if (lease_property) return `${inactive_char}${field_key} — ${field_value} (${lease_property})`;
                    return `${inactive_char}${field_key} — ${field_value}`;
                    break;
                default:
                    if (!field_value) {
                        return 'Please select...';
                    }
                    return `${inactive_char}${field_key} — ${field_value}`;
                    break;
            }
        },
    },
    created() {
        bus.$on('clearPropertyDropdown', (data) => {
            this.reset_filter();
        });
        bus.$on('changeEditMode', (data) => {
            this.edit_form = data;
            this.loadFormNotes();
        });
        bus.$on('processSaveFormNotes', (data) => {
            this.processSaveFormNotes();
        });
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
