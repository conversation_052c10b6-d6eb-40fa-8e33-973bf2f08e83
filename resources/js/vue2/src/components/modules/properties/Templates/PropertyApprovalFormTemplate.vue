<style>
.shortcut-div > .v-toolbar__content {
    height: 30px;
}

.v-application .overline {
    font-size: 0.625rem;
    font-weight: 400;
    letter-spacing: 0.1666666667em;
    line-height: 1rem;
    text-transform: uppercase;
    font-family: 'Raleway', sans-serif;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <div class="page-form">
            <div class="form-row">
                <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
                <v-toolbar
                    flat
                    v-if="property_name !== ''"
                >
                    <v-toolbar-title>
                        <cirrus-page-header :title="property_code + ' - ' + property_name" />
                    </v-toolbar-title>
                    <div class="flex-grow-1"></div>
                    <!--            <v-btn x-small @click="startTour()" v-if="created_by_name!==''" class="v-step-final-message" color="secondary">Tour</v-btn>-->
                </v-toolbar>
                <v-subheader v-if="property_form_type === '3'">
                    These details are as per the original new property form and may have been updated since submission.
                    Go to &nbsp
                    <a
                        v-if="user_type === 'A'"
                        target="_blank"
                        :href="'?module=properties&command=v2_manage_property_page&property_code=' + property_code + ''"
                    >
                        Property Summary
                    </a>
                    <a
                        v-else
                        target="_blank"
                        :href="'?module=properties&command=property_summary_page&property_code=' + property_code + ''"
                    >
                        Property Summary
                    </a>
                    &nbsp for up-to date information.
                </v-subheader>
                <cirrus-server-error
                    :error_msg="error_server_msg"
                    :errorMsg2="error_server_msg2"
                ></cirrus-server-error>

                <v-container fluid>
                    <v-row>
                        <v-col
                            cols="12"
                            md="6"
                        >
                            <!--              <cirrus-content-loader type="article" v-if="created_by_name===''"></cirrus-content-loader>-->
                            <v-card
                                class="mx-auto"
                                color="#1F7087"
                                shaped
                                dark
                                v-if="created_by_name !== ''"
                            >
                                <v-list-item three-line>
                                    <v-list-item-content>
                                        <div class="overline mb-4">
                                            Property Code: <span class="v-step-property-code">{{ property_code }}</span>
                                        </div>
                                        <v-list-item-title class="headline mb-1"
                                            ><span class="v-step-created-by">{{ created_by_email }}</span> -
                                            <span class="v-step-created-date">{{ created_date }}</span>
                                        </v-list-item-title>
                                        <v-list-item-subtitle class="v-step-comment">
                                            <pre style="text-wrap: pretty">{{ comments }}</pre>
                                        </v-list-item-subtitle>
                                    </v-list-item-content>
                                </v-list-item>

                                <v-card-actions>
                                    <v-btn
                                        color="primary"
                                        v-if="property_form_type === '1'"
                                        class="v-step-assign-company-btn"
                                        @click="showReassignPropertyModal()"
                                        >Re-assign Property Code
                                    </v-btn>
                                    <v-spacer></v-spacer>
                                    <v-btn
                                        text
                                        color="#fff"
                                        small
                                        right
                                        @click="printProperty()"
                                    >
                                        <v-icon>print</v-icon>
                                        PRINT
                                    </v-btn>
                                </v-card-actions>
                            </v-card>
                        </v-col>
                        <v-col
                            cols="12"
                            md="6"
                        >
                            <div v-if="property_form_type === '1'">
                                <v-textarea
                                    class="vuetifyText v-step-feedback"
                                    outlined
                                    label="Your feedback (This response is sent to the client when either approving or rejecting a form)"
                                    v-model="feedback_comment"
                                    value=""
                                    full-width
                                ></v-textarea>

                                <div style="float: right; margin-left: 10px">
                                    <span>
                                        <v-btn
                                            color="error"
                                            dense
                                            small
                                            class="v-step-form-no-btn"
                                            @click="processRejectForm()"
                                            :disabled="form_no_btn"
                                            ><v-icon>clear</v-icon>Reject Property</v-btn
                                        >
                                    </span>
                                </div>
                                <div style="float: right; margin-left: 10px">
                                    <span>
                                        <v-btn
                                            color="success"
                                            dense
                                            small
                                            class="v-step-form-yes-btn"
                                            @click="processApproveForm"
                                            :disabled="form_yes_btn"
                                            ><v-icon>done</v-icon>Approve Property</v-btn
                                        >
                                    </span>
                                </div>
                                <div style="float: right; margin-top: 7px">
                                    <span><strong>Would you like to process this Property?</strong></span>
                                    <!--                                    <span><strong>Would you like to mark this Property as processed?</strong></span>-->
                                </div>
                                <div style="float: left !important">
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'feedback_comment'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </div>
                            </div>
                            <div v-if="feedback_comment && property_form_type !== '1'">
                                <v-textarea
                                    class="vuetifyText v-step-feedback"
                                    outlined
                                    v-model="feedback_comment"
                                    value=""
                                    full-width
                                    readonly
                                ></v-textarea>
                            </div>
                        </v-col>
                    </v-row>
                </v-container>
                <v-tabs
                    color="primary"
                    icons-and-text
                    class="cirrus-tab-theme"
                    v-show="property_code !== '' && form_mode === 0"
                    v-model="template_tab"
                    show-arrows
                >
                    <v-tabs-slider color="orange"></v-tabs-slider>

                    <v-tab
                        href="#tab-1"
                        v-if="responsive_show"
                        class="v-step-form-tab"
                    >
                        Overview
                        <v-icon>business</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-2"
                        v-if="responsive_show"
                    >
                        Management
                        <v-icon>monetization_on</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-3"
                        v-if="responsive_show"
                    >
                        floor(s) and Unit(s)
                        <v-icon>business</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-4"
                        v-if="responsive_show"
                    >
                        Diary
                        <v-icon>date_range</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-6"
                        v-if="responsive_show"
                    >
                        Notes and Additional Details
                        <v-icon>notes</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-7"
                        v-if="responsive_show"
                    >
                        Communication and Documents
                        <v-icon>attachment</v-icon>
                    </v-tab>

                    <v-tab
                        href="#tab-1"
                        class="primary--text v-step-form-tab"
                        v-if="!responsive_show"
                    >
                        <v-icon>business</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-2"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon>monetization_on</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-3"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon>business</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-4"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon>date_range</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-6"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon>notes</v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-7"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon>attachment</v-icon>
                    </v-tab>
                    <v-tab-item :value="'tab-1'">
                        <property-main-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-main-form-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-2'">
                        <property-man-detail-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-man-detail-form-component>
                        <property-man-agreement-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-man-agreement-form-component>
                        <property-man-fee-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-man-fee-form-component>
                        <property-fees-charges-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-fees-charges-form-component>
                        <property-recoverable-split-component
                            :property_code="property_code"
                            :version_id="version_id"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-recoverable-split-component>
                        <property-fund-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-fund-form-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-3'">
                        <property-floor-n-unit-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-floor-n-unit-form-component>
                        <property-parking-bays-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-parking-bays-form-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-4'">
                        <property-diary-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-diary-form-component>
                    </v-tab-item>

                    <v-tab-item :value="'tab-6'">
                        <property-keys-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-keys-component>
                        <property-note-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-note-form-component>

                        <property-inspection-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-inspection-form-component>
                        <property-insurance-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-insurance-form-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-7'">
                        <property-contact-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-contact-form-component>
                        <property-document-form-component
                            :property_code="property_code"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></property-document-form-component>
                    </v-tab-item>
                </v-tabs>

                <sui-modal
                    v-model="modify_lease_code_modal"
                    size="small"
                >
                    <sui-modal-header>Modify Lease Code</sui-modal-header>
                    <sui-modal-content
                        scrolling
                        image
                    >
                        <sui-modal-description>
                            <div>
                                <cirrus-server-error
                                    :errorMsg2="error_server_msg_modify_lease_modal"
                                ></cirrus-server-error>
                            </div>
                            <div>
                                <table
                                    class="data-grid data-grid-dense-version-2"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="row">
                                            <td class="v-step-gen-lease-name">
                                                <b>Lease Code</b>
                                            </td>
                                            <td class="required">*</td>
                                            <td>
                                                <cirrus-input
                                                    :id="'new_lease_code'"
                                                    v-model="new_lease_code"
                                                    :edit_form="true"
                                                    size="10"
                                                    :error_msg="error_msg"
                                                ></cirrus-input>
                                                <v-btn
                                                    color="primary"
                                                    x-small
                                                    @click="modifyLeaseCode()"
                                                    :disabled="modify_lease_code_btn"
                                                >
                                                    Modify
                                                </v-btn>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </sui-modal-description>
                    </sui-modal-content>
                    <sui-modal-actions>
                        <v-btn
                            x-small
                            text
                            @click="modify_lease_code_modal = false"
                            >Close
                        </v-btn>
                    </sui-modal-actions>
                </sui-modal>

                <sui-modal
                    v-model="assign_propety_code_modal"
                    size="small"
                >
                    <sui-modal-header>Re-assign Property Code</sui-modal-header>
                    <sui-modal-content
                        scrolling
                        image
                    >
                        <sui-modal-description>
                            <div>
                                <cirrus-server-error
                                    :errorMsg2="error_server_msg_assign_propety_code_modal"
                                ></cirrus-server-error>
                            </div>
                            <div>
                                <table
                                    class="data-grid data-grid-dense-version-2"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="row">
                                            <td class="title">
                                                <b>New Property Code</b>
                                            </td>
                                            <td class="required"></td>
                                            <td>
                                                <cirrus-input
                                                    :id="'reassign_property_code'"
                                                    v-model="reassign_property_code"
                                                    :edit_form="true"
                                                    size="10"
                                                    :error_msg="error_msg"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </sui-modal-description>
                    </sui-modal-content>
                    <sui-modal-actions>
                        <v-btn
                            x-small
                            color="primary"
                            @click="assignOrUpdateProperty()"
                            :disabled="assign_company_btn"
                            >Re-assign Property Code
                        </v-btn>
                        <v-btn
                            x-small
                            text
                            @click="assign_propety_code_modal = false"
                            >Close
                        </v-btn>
                    </sui-modal-actions>
                </sui-modal>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex';
import PropertyMainForm from '../Forms/PropertyMainForm.vue';
import PropertyDiaryForm from '../Forms/PropertyDiaryForm.vue';
import PropertyCalendarForm from '../Forms/PropertyCalendarForm.vue';
import PropertyCommunicationHistoryForm from '../Forms/PropertyCommunicationHistoryForm.vue';
import PropertyContactForm from '../Forms/PropertyContactForm.vue';
import PropertyDocumentForm from '../../../../DocumentDirectory/sections/PropertyDocumentFormV2.vue';
import PropertyFloorNUnitForm from '../Forms/PropertyFloorNUnitForm.vue';
import PropertyInspectionForm from '../Forms/PropertyInspectionForm.vue';
import PropertyInsuranceForm from '../Forms/PropertyInsuranceForm.vue';
import PropertyNoteForm from '../Forms/PropertyNoteForm.vue';
import PropertyOwnerSharesForm from '../Forms/PropertyOwnerSharesForm.vue';
import PropertyFeesNChargesForm from '../Forms/PropertyFeesNChargesForm.vue';
import PropertyTakeOnBalanceForm from '../Forms/PropertyTakeOnBalanceForm.vue';
import PropertyManagementFeeForm from '../Forms/PropertyManagementFeeForm.vue';
import PropertyManagementAgreementForm from '../Forms/PropertyManagementAgreementForm.vue';
import PropertyManagementDetailForm from '../Forms/PropertyManagementDetailForm.vue';
import PropertyBankAccountForm from '../Forms/PropertyBankAccountForm.vue';
import PropertyParkingBaysForm from '../Forms/PropertyParkingBaysForm.vue';
import PropertyFundForm from '../Forms/PropertyFundForm.vue';
import PropertyKeysForm from '../Forms/PropertyKeysForm.vue';
import PropertyRecoverableSplitForm from '../Forms/PropertyRecoverableSplitForm.vue';
import global_mixins from '../../../../plugins/mixins';
// Vue.use(SuiVue);
export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialCountryDefaultSettings: String,
    },
    components: {
        'property-main-form-component': PropertyMainForm,
        'property-diary-form-component': PropertyDiaryForm,
        'property-calendar-form-component': PropertyCalendarForm,
        'property-communication-history-form-component': PropertyCommunicationHistoryForm,
        'property-contact-form-component': PropertyContactForm,
        'property-document-form-component': PropertyDocumentForm,
        'property-floor-n-unit-form-component': PropertyFloorNUnitForm,
        'property-inspection-form-component': PropertyInspectionForm,
        'property-insurance-form-component': PropertyInsuranceForm,
        'property-note-form-component': PropertyNoteForm,
        'property-owner-shares-form-component': PropertyOwnerSharesForm,
        'property-fees-charges-form-component': PropertyFeesNChargesForm,
        'property-take-on-balances-form-component': PropertyTakeOnBalanceForm,
        'property-man-fee-form-component': PropertyManagementFeeForm,
        'property-man-agreement-form-component': PropertyManagementAgreementForm,
        'property-man-detail-form-component': PropertyManagementDetailForm,
        'property-bank-account-form-component': PropertyBankAccountForm,
        'property-parking-bays-form-component': PropertyParkingBaysForm,
        'property-fund-form-component': PropertyFundForm,
        'property-keys-component': PropertyKeysForm,
        'property-recoverable-split-component': PropertyRecoverableSplitForm,
    },
    data() {
        return {
            property_code: '',
            lease_code: '',
            version_id: '',
            property_list: [],
            lease_list: [],
            lease_main_list: [],
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            read_only: this.initialReadOnly,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            template_tab: null,
            loading_setting: false,
            loading_page_setting: false,
            loading_company_details_modal: false,
            tab: null,
            shortcuts: [
                { icon: 'business', title: 'Go to property', shortcut_code: 'property' },
                { icon: 'table_chart', title: 'Lease Abstract', shortcut_code: 'lease_abstract' },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (current month)',
                    shortcut_code: 'tenant_activity_current_month',
                },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (all dates)',
                    shortcut_code: 'tenant_activity_all_date',
                },
                { icon: 'receipt', title: 'View Tax Invoices', shortcut_code: 'view_tax_invoice' },
                { icon: 'history', title: 'Lease Activity Logs', shortcut_code: 'lease_logs' },
                { icon: 'print', title: 'Print', shortcut_code: 'print' },
            ],
            force_load: false,
            lease_page_title: 'Lease Summary',
            error_server_msg: {},
            error_server_msg2: [],
            error_server_msg_modify_lease_modal: [],
            error_server_msg_assign_propety_code_modal: [],
            lease_existed: true,
            force_load_new_lease: 0,
            lease_commencement_date: '31/12/2999',
            tour_steps_new_lease: [],
            new_lease_code: '',
            created_by_name: '',
            property_name: '',
            created_by_email: '',
            created_date: '',
            property_form_type: '',
            process_state: '',
            form_description: '',
            comments: 'no comments',
            feedback_comment: '',
            tour_steps: [
                { target: '.v-step-property-code', content: 'Property code.' },
                { target: '.v-step-created-by', content: 'User name and email address submitted this form.' },
                { target: '.v-step-created-date', content: 'Date this form submitted.' },
                { target: '.v-step-comment', content: 'User comment when he/she submitted this form.' },
                {
                    target: '.v-step-feedback',
                    content: 'Feedback that sent to the client when either approving or rejecting a form.',
                },
                {
                    target: '.v-step-modify-lease-code-btn',
                    content: 'This is where you can modify the lease code of submitted form.',
                },
                {
                    target: '.v-step-assign-company-btn',
                    content: 'This is where you can re-assign either debtor or supplier of the lease.',
                },
                { target: '.v-step-form-yes-btn', content: 'This is the button for approving the lease form.' },
                { target: '.v-step-form-no-btn', content: 'This is the button for rejecting the lease form.' },
            ],
            modify_lease_code_modal: false,
            assign_propety_code_modal: false,
            reassign_property_code: '',
            reassign_company_name: '',
            reassign_company_address: '',
            reassign_company_city: '',
            reassign_company_postcode: '',
            tenant_country_code: '',
            reassign_company_email: '',
            reassign_company_debtor: false,
            reassign_company_supplier: false,
            reassign_company_existing: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            reassign_company_state: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            reassign_company_country: '',
            modify_lease_code_btn: false,
            assign_company_btn: false,
            form_yes_btn: false,
            form_no_btn: false,
            errorVacant: '',
            vacateProperty: '',
            vacateLease: '',
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'http_host',
            'dd_company_list',
            'doc_active_version',
            'sys_ver_control_list',
        ]),
        ...mapGetters(['getDDCountryStates', 'getLeaseDetails']),
    },
    mounted() {
        // console.log(localStorage.getItem('cirrus8_api_url'));
        this.fetchFormVersionControl();
        this.loadPropertyList();
        this.fetchCountryList();
        this.fetchAccountIncomeGroupedList();
        this.fetchBondPropertyList();
        this.fetchParamLeaseTypeList();
        this.fetchParamTenantTypeList();
        this.fetchRetailCategoryList();
        this.fetchRetailSubCategoryList();
        this.fetchRetailFineCategoryList();
        this.fetchCompanyList();
        let property_form_read_only = [
            { form_type: 'PROPERTY', form_section: 'PROPERTY_MAIN_DETAILS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_CONTACTS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_OWNER_SHARES' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_MANAGEMENT_DETAILS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_MANAGEMENT_AGREEMENTS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_MANAGEMENT_FEES' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_FEES_CHARGES' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_PARKING_BAYS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_KEYS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_FLOOR_UNITS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_DIARY' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_INSURANCE' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_NOTES' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_CONTACTS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_DOCUMENTS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_COM_HISTORY' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_SMS' },
            { form_type: 'PROPERTY', form_section: 'PROPERTY_EMAIL' },
            { form_type: 'PROPERTY', form_section: 'RECOVERABLE_SPLIT' },
        ];
        this.SET_PM_PROPERTY_FORM_READ_ONLY(property_form_read_only);
        this.tour_steps_new_lease = [
            {
                target: '.v-step-cancel-lease-button',
                content: `Cancel <strong>New</strong> lease <br> Shows the form for new lease`,
            },
            { target: '.v-step-property-select', content: 'Select the lease property you want to view or edit' },
            { target: '.v-step-new-lease-code', content: 'Enter new lease code' },
            { target: '.v-step-final-message', content: 'You have finish the page tour.' },
        ];
        this.property_code = this.initialPropertyCode;
        this.getPropertyClientSubmittedForm();
        // document.onreadystatechange = () => {
        //     if (document.readyState == "complete") {
        //         bus.$emit("loadLeaseMainFormSection","");
        //     }
        // }
    },
    methods: {
        ...mapActions([
            'fetchCountryList',
            'fetchAccountIncomeGroupedList',
            'fetchBondPropertyList',
            'fetchParamDivisionList',
            'fetchParamLeaseTypeList',
            'fetchParamTenantTypeList',
            'fetchRetailCategoryList',
            'fetchRetailSubCategoryList',
            'fetchRetailFineCategoryList',
            'fetchCompanyList',
            'fetchFormVersionControl',
        ]),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_PM_PROPERTY_FORM_READ_ONLY']),
        loadPropertyList: function () {
            this.$api.post('load-property-dropdown-list').then((response) => {
                this.property_list = response.data.data;
            });
        },
        loadLeaseList: function () {
            this.loading_setting = true;
            let form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('page_source', 'leaseFormTemplate');
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-lease-list').then((response) => {
                this.lease_list = response.data.data;
                this.loading_setting = false;
            });
        },
        selectLease: function (fieldKey, fieldValue, leaseAddress, fieldGroup) {
            this.lease_code = {
                fieldKey: fieldKey,
                fieldValue: fieldValue,
                leaseAddress: leaseAddress,
                fieldGroup: fieldGroup,
                field_key: fieldKey,
                field_value: fieldValue,
                lease_address: leaseAddress,
                field_group: fieldGroup,
            };
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        goToHref: function (param1, param2) {
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        goToShortcut: function (parameter) {
            switch (parameter) {
                case 'property':
                    window.open(
                        '?module=properties&command=v2_manage_property_page&property_code=' + this.property_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'company':
                    //module=companies&command=company&companyID=7ELEVEN
                    window.open(
                        '?module=companies&command=company&companyID=' + this.$company_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'lease_abstract':
                    break;
                case 'tenant_activity_current_month':
                    break;
                case 'tenant_activity_all_date':
                    break;
                case 'view_tax_invoice':
                    break;
                case 'print':
                    break;
            }
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        showNewLease: function () {
            this.form_mode = 1;
            this.lease_page_title = 'New Lease';
            this.new_lease_code = '';
        },
        cancelNewLease: function () {
            this.lease_page_title = 'Lease Summary';
            this.form_mode = 0;

            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_lease_code = '';
            this.lease_existed = true;
        },
        showLeaseListTable: function () {
            if (this.form_mode === 0) {
                this.form_mode = 0;
                this.lease_code = { fieldKey: '' };
            } else {
                this.error_msg = [];
                this.error_server_msg2 = [];
                this.new_lease_code = '';
                this.lease_existed = true;
            }
        },
        checkLeaseCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            let new_lease_code = this.new_lease_code.toUpperCase().trim();

            this.new_lease_code = new_lease_code;
            if (new_lease_code.length > 10) {
                // this.error_msg.push({id:'new_lease_code', message: 'Lease code allows 10 characters only'});
                this.error_server_msg2.push(['Lease code allows 10 characters only']);
            } else if (new_lease_code === '') {
                // this.error_msg.push({id:'new_lease_code', message: 'Please input a valid lease code'});
                this.error_server_msg2.push(['Please input a valid lease code']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('new_lease_code', new_lease_code);
                form_data.append('no_load', true);

                this.$api
                    .post(this.cirrus8_api_url + 'api/property/fetch/check-code-if-exist', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        this.lease_existed = response.data.lease_existed;
                        this.loading_page_setting = false;
                        if (!this.lease_existed) {
                            this.force_load_new_lease++;
                            // var child = this.$refs.main_form_new_lease;
                            // child.loadForm();
                        }
                    });
            }
        },
        getLeaseCode: function (value) {
            let lease_code = value.lease_code;
            let lease_name = value.lease_name;
            this.lease_commencement_date = value.lease_commencement_date;
            let label = lease_code + ' - ' + lease_name;
            this.cancelNewLease();
            // this.loadLeaseList();
            this.lease_code = {
                fieldKey: lease_code,
                field_key: lease_code,
                fieldValue: label,
                field_value: label,
                value: lease_code,
                label: label,
            };
        },
        startTour: function () {
            this.$tours['leaseTour'].start();
        },
        startNewLeaseTour: function () {
            this.$tours['newLeaseTour'].start();
        },
        getPropertyClientSubmittedForm: function () {
            if (this.property_code !== '') {
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/property/fetch/submitted-form-detail', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        this.loading_page_setting = false;
                        this.property_name = response.data.property_name;
                        this.created_by_name = response.data.created_by_name;
                        this.created_by_email = response.data.created_by_email;
                        this.created_date = response.data.created_date;
                        this.comments = response.data.comments;
                        this.feedback_comment = response.data.feedback_comment;
                        this.property_form_type = response.data.property_form_type;
                        this.process_state = response.data.process_state;
                        this.form_description = response.data.form_description;
                        if (this.comments === '') {
                            this.comments = 'no comments';
                        }
                    });
            }
        },
        processApproveForm: function () {
            this.errorVacant = false;
            this.error_msg = [];

            if (this.property_code !== '') {
                this.form_yes_btn = true;
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('feedback_comment', this.feedback_comment);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/property/process/approve-for-approval-property', form_data)
                    .then((response) => {
                        if (response.data.validation_errors.length > 0) {
                            this.error_server_msg2 = response.data.validation_errors;
                        } else {
                            if (response.data.status == 'success') {
                                setTimeout(this.getPropertyClientSubmittedForm(), 5000);
                                this.$noty.success('Successfully Approved.');
                            }
                        }
                        this.loading_page_setting = false;
                        this.form_yes_btn = false;
                    });
            }
        },
        processRejectForm: function () {
            this.errorVacant = false;
            this.error_msg = [];
            if (this.feedback_comment === '') {
                this.error_msg.push({ id: 'feedback_comment', message: 'You need to enter your feedback.' });
                this.$noty.error('You need to enter your feedback.');
            }
            if (this.property_code !== '' && this.feedback_comment !== '') {
                this.form_no_btn = true;
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('feedback_comment', this.feedback_comment);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/property/process/reject-for-approval-property', form_data)
                    .then((response) => {
                        if (response.data.validation_errors.length > 0) {
                            this.error_server_msg2 = response.data.validation_errors;
                        } else {
                            if (response.data.status == 'success') {
                                setTimeout(this.getPropertyClientSubmittedForm(), 5000);
                                this.$noty.success('Successfully Approved.');
                            }
                        }
                        this.loading_page_setting = false;
                        this.form_no_btn = false;
                    });
            }
        },
        getValueInList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.field_key === param1);
                if (filtered.length > 0) {
                    return filtered[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        fieldKey: '',
                        fieldValue: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        modifyLeaseCode: function () {
            this.modify_lease_code_btn = true;
            this.error_server_msg_modify_lease_modal = [];
            let new_lease_code = this.new_lease_code.toUpperCase().trim();
            this.new_lease_code = new_lease_code;
            if (new_lease_code.length > 10) {
                // this.error_msg.push({id:'new_lease_code', message: 'Lease code allows 10 characters only'});
                this.error_server_msg_modify_lease_modal.push(['Lease code allows 10 characters only']);
            } else if (new_lease_code === '') {
                // this.error_msg.push({id:'new_lease_code', message: 'Please input a valid lease code'});
                this.error_server_msg_modify_lease_modal.push(['Please input a valid lease code']);
            }
            if (this.error_server_msg_modify_lease_modal.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('new_lease_code', this.new_lease_code);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/property/update/modify-lease-code', form_data)
                    .then((response) => {
                        this.error_server_msg_modify_lease_modal = response.data.validation_errors;
                        this.loading_page_setting = false;
                        if (this.error_server_msg_modify_lease_modal.length <= 0) {
                            this.lease_code = this.new_lease_code;
                            this.modify_lease_code_modal = false;
                            this.modify_lease_code_btn = false;
                            this.$noty.success('Successfully Modified.');
                            // location.href = this.http_host+'?module=leases&command=lease_approval_page&property_code='+this.property_code+'&lease_code='+new_lease_code+'&version_id'+this.version_id;
                        }
                    });
            } else {
                this.modify_lease_code_btn = false;
            }
        },
        showReassignPropertyModal: function () {
            this.assign_propety_code_modal = true;
        },
        assignOrUpdateProperty: function () {
            this.assign_company_btn = true;
            let reassign_property_code = this.reassign_property_code;
            let property_code = this.property_code;
            this.error_server_msg_assign_propety_code_modal = [];
            if (reassign_property_code.length > 10) {
                this.error_server_msg_assign_propety_code_modal.push(['Property code allows 10 characters only']);
            } else if (reassign_property_code === '') {
                this.error_server_msg_assign_propety_code_modal.push(['Please input a valid Property code']);
            }

            if (this.error_server_msg_assign_propety_code_modal.length <= 0) {
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('reassign_property_code', reassign_property_code);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/property/update/temp-property-code', form_data)
                    .then((response) => {
                        let validation_errors = response.data.validation_errors;
                        this.error_server_msg_assign_propety_code_modal = validation_errors;
                        if (validation_errors.length === 0) {
                            this.$noty.success('Successfully Saved.');
                            window.open(
                                '?module=properties&command=property_approval_page&property_code=' +
                                    reassign_property_code.toUpperCase(),
                                '_self', // <- This is what makes it open in a new window.
                            );
                        }
                        this.assign_company_btn = false;
                    });
            } else {
                this.assign_company_btn = false;
            }
        },
        printProperty: function () {
            var printWindow = window.open(
                '?module=properties&command=property_print_page&property_code=' + this.property_code,
                '_blank', // <- This is what makes it open in a new window.
                'toolbar=yes,scrollbars=yes,resizable=no,top=20,left=30,width=900,height=600',
            );
            printWindow.focus();
        },
    },
    watch: {
        property_code: function () {
            // this.lease_code = {fieldKey:''};
            this.lease_list = [];
            this.SET_PROPERTY_CODE(this.property_code);
            this.loadLeaseList();
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
        reassign_company_existing: function () {
            if (this.reassign_company_existing.field_key !== '') {
                this.error_server_msg_assign_propety_code_modal = [];
                this.loading_company_details_modal = true;
                let form_data = new FormData();
                form_data.append('company_code', this.reassign_company_existing.field_key);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/company/load-company-details', form_data)
                    .then((response) => {
                        this.reassign_property_code = this.reassign_company_existing.field_key;
                        this.reassign_company_name = response.data.tenant_name;
                        this.reassign_company_address = response.data.tenant_address_1 + response.data.tenant_address_2;
                        this.reassign_company_city = response.data.tenant_suburb;
                        this.reassign_company_postcode = response.data.tenant_post_code;
                        this.tenant_country_code = response.data.tenant_country_code;
                        this.reassign_company_email = response.data.tenant_email;
                        let reassign_company_state = response.data.tenant_state.field_key;
                        this.reassign_company_state = this.getValueInList(
                            reassign_company_state,
                            this.tenant_country_code,
                        );
                        this.reassign_company_existing = this.getValueInList(
                            this.reassign_property_code,
                            this.dd_company_list,
                        );
                        this.loading_company_details_modal = false;
                    });
            }
        },
    },
    created() {},
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
