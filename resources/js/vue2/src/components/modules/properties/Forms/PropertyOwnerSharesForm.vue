<style>
#multiselect_owner .multiselect__content-wrapper {
    max-height: 150px !important;
}
</style>
<template>
    <div v-on:dblclick="doubleClickForm()">
        <div class="form-row">
            <v-card
                class="section-toolbar subHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">
                        Owner Shares: ({{ roundTo(total_percentage, 2) }}% Total Owner Shares)
                    </h6>
                    <v-spacer></v-spacer>
                    <cirrus-input
                        inputFormat="search"
                        v-if="isEditable()"
                        v-model="search_datatable"
                        placeholder="Search"
                        :edit_form="true"
                        style="padding-right: 1em"
                    ></cirrus-input>
                    <v-btn
                        x-small
                        v-show="isEditable() && !pmro_read_only"
                        icon
                        @click="modalAddData()"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="isEditable() && !pmro_read_only"
                        class="v-step-edit-button"
                        v-if="!edit_form"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="isEditable() && !pmro_read_only"
                        v-if="edit_form && !new_property"
                        icon
                        @click="resetForm()"
                    >
                        <v-icon color="red">undo</v-icon>
                    </v-btn>

                    <v-btn
                        x-small
                        v-show="isEditable()"
                        class="v-step-refresh-button"
                        icon
                        @click="loadForm()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        icon
                        x-small
                        v-show="isPropertyFormLive()"
                        @click="show_activity_log_modal = true"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
        </div>

        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="owner_share_list.length === 0 && !loading_setting"
            v-show="isEditable()"
        >
            <v-btn
                depressed
                small
                color="success"
                @click="modalAddData()"
                >Add Owner Share
            </v-btn>
        </v-col>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="owner_share_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="owner_share_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ owner_share_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.owner_description="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><strong>{{ item.owner_code }}: </strong
                                ><a @click="openOwnerInfoModal(owner_share_list.indexOf(item))">{{
                                    item.owner_name
                                }}</a></span
                            >
                        </div>
                    </template>
                    <template v-slot:item.owner_percentage="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.owner_percentage }}%</span>
                        </div>
                    </template>
                    <template v-slot:item.owner_amount="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                >{{ currency_symbol
                                }}{{ accountingAmountFormat(numberWithCommas(roundTo(item.owner_amount, 2))) }}</span
                            >
                        </div>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenAED(owner_share_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit
                        </v-icon>
                        <v-icon
                            color="red"
                            v-if="edit_form"
                            @click="deleteOwnerShare(owner_share_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="owner_share_list.length > 5"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteOwnerShare(owner_share_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Owner Share Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="owner_share_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        owner_share_arr.index === 'New'
                                            ? owner_share_arr.index
                                            : owner_share_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Owner:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                    id="multiselect_owner"
                                >
                                    <multiselect
                                        data-inverted=""
                                        :custom-label="nameWithDash"
                                        :data-tooltip="edit_form ? 'Owner' : false"
                                        v-model="owner_share_arr.owner"
                                        :options="owner_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        group-label="language"
                                        placeholder="Select an owner"
                                        track-by="field_key"
                                        label="field_value"
                                        v-if="owner_share_arr.index === 'New'"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <span
                                        class="form-input-text"
                                        v-if="owner_share_arr.index !== 'New'"
                                        >{{ owner_share_arr.owner_code }} - {{ owner_share_arr.owner_name }}</span
                                    >
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Percentage Owned:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="percentage"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="owner_share_arr.owner_percentage"
                                        size=""
                                        :disable_fix_decimal="true"
                                        :id="'owner_percentage' + owner_share_arr.index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Owner Percentage' : false"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Fixed Amount Paid to Owner:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="owner_share_arr.owner_amount"
                                        size=""
                                        :id="'owner_amount' + owner_share_arr.index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Owner Amount' : false"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous
                        </v-icon>
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="owner_share_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add
                        </v-icon>
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="owner_share_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteOwnerShare(owner_share_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="owner_share_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next
                        </v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->
        <v-dialog
            v-model="company_details_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Company Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="company_details_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="6"
                                    md="6"
                                >
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Company:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                <strong
                                                    ><a
                                                        @click="
                                                            goToCompanyShortcut(
                                                                company_details_arr.company_code,
                                                                '',
                                                                '',
                                                            )
                                                        "
                                                        >{{ company_details_arr.company_code }}</a
                                                    >
                                                    : {{ company_details_arr.company_name }}</strong
                                                >
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Owner Address:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_street }}
                                                {{ company_details_arr.company_city }}
                                                {{ company_details_arr.company_state }}
                                                {{ company_details_arr.company_postcode }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Email Address:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                            v-if="company_details_arr.company_email"
                                        >
                                            <div
                                                v-for="(data, index) in breakdownEmail(
                                                    company_details_arr.company_email,
                                                )"
                                                :key="index"
                                                v-html="linkLocationFormat('email', data)"
                                            ></div>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >{{ country_defaults.business_label }}:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_business_prefix
                                                }}{{ company_details_arr.company_abn }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >{{ country_defaults.tax_label }} Code:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_gst_code }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <div
                                        is="sui-divider"
                                        horizontal
                                    >
                                        <h4 is="sui-header">
                                            <i class="university icon"></i>
                                            Payment Details
                                        </h4>
                                    </div>
                                    <v-row
                                        class="form-row"
                                        v-if="country_defaults.display_bsb"
                                        v-show="company_details_arr.company_payment_method === '1'"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label right"
                                            >{{ country_defaults.bsb_label }}:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ formattedBsb }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        class="form-row"
                                        v-show="company_details_arr.company_payment_method === '1'"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label right"
                                            >Account Number:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_acc_no }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        class="form-row"
                                        v-show="company_details_arr.company_payment_method === '1'"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Account Name:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_acc_name }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        class="form-row"
                                        v-show="company_details_arr.company_payment_method === '1'"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Bank:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_bank_name }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        class="form-row"
                                        v-show="
                                            company_details_arr.company_payment_method === '2' ||
                                            company_details_arr.company_payment_method === '3'
                                        "
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Payment Method:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span
                                                class="form-input-text"
                                                v-if="company_details_arr.company_payment_method === '2'"
                                                >BPAY</span
                                            >
                                            <span
                                                class="form-input-text"
                                                v-if="company_details_arr.company_payment_method === '3'"
                                                >Cheque</span
                                            >
                                        </v-col>
                                    </v-row>
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="6"
                                    md="6"
                                >
                                    <v-row class="form-row">
                                        <v-col
                                            sm="5"
                                            md="5"
                                            class="contact-box"
                                            v-for="(
                                                company_details_arr_data, company_details_arr_index
                                            ) in company_details_arr.company_contact_list"
                                            :key="company_details_arr_index"
                                        >
                                            <b>{{ company_details_arr_data.contact_name }}</b
                                            ><br />
                                            {{ company_details_arr_data.contact_role_description }}<br />
                                            <div
                                                v-for="(
                                                    contact_details_list_data, contact_details_list_index
                                                ) in company_details_arr_data.contact_details_list"
                                                :key="contact_details_list_index"
                                            >
                                                <b>{{ contact_details_list_data.contact_detail_description }}</b>
                                                <span
                                                    v-if="
                                                        contact_details_list_data.contact_detail_description ===
                                                        'E-Mail'
                                                    "
                                                    v-html="
                                                        linkLocationFormat(
                                                            'email',
                                                            contact_details_list_data.contact_detail,
                                                        )
                                                    "
                                                ></span>
                                                <span
                                                    v-if="
                                                        contact_details_list_data.contact_detail_description ===
                                                            'Mobile Phone' ||
                                                        contact_details_list_data.contact_detail_description ===
                                                            'Owner Mobile' ||
                                                        contact_details_list_data.contact_detail_description ===
                                                            'Service Mobile' ||
                                                        contact_details_list_data.contact_detail_description ===
                                                            'Tenant Mobile'
                                                    "
                                                    v-html="
                                                        linkLocationFormat(
                                                            'mobile',
                                                            contact_details_list_data.contact_detail,
                                                        )
                                                    "
                                                ></span>
                                                <span v-else>{{ contact_details_list_data.contact_detail }}</span>
                                                <br />
                                            </div>
                                        </v-col>
                                    </v-row>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';
import { formatWithDelimiter } from '../../../../utils/sharedFunctions';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_OWNER_SHARES',
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Owner Code: Owner Name', value: 'owner_description', width: '30%' },
                { text: 'Percentage Owned', value: 'owner_percentage', align: 'right' },
                { text: 'Fixed Amount Paid to Owner', value: 'owner_amount', align: 'right' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            AED_modal: false,
            success_flag: false,
            company_details_modal: false,
            modal_current_ctr: 0,
            owner_share_list: [],
            owner_share_arr: [],
            company_details_arr: [],
            owner_list: [],
            total_percentage: 0.0,
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
                tax_label: 'GST',
                business_prefix: '',
                bsb_label: 'BSB',
                bsb_format: {
                    delimiter: '-',
                    delimiter_frequency: 3,
                },
            },
            currency_symbol: '$',
        };
    },
    methods: {
        formatWithDelimiter,
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        resetForm: function () {
            this.edit_form = false;
        },
        loadForm: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '') {
                this.loadOwnerShares();
            }
        },
        loadOwnerShares: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/owner-shares', form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            } else {
                let apiUrl = 'temp/property/fetch/owner-shares';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            }
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.owner_share_list = response.data.owner_share_list;
            let total_percentage = 0.0;
            for (let x = 0; x <= this.owner_share_list.length - 1; x++) {
                let percentage = parseFloat(this.owner_share_list[x].owner_percentage);
                total_percentage = total_percentage + percentage;
            }
            this.total_percentage = total_percentage;
            this.currency_symbol = response.data.currency_symbol;
        },
        modalSubmitData: function () {
            let index = this.owner_share_arr.index;
            let owner_code = this.owner_share_arr.owner.field_key;
            let owner_percentage = this.owner_share_arr.owner_percentage;
            let owner_amount = this.owner_share_arr.owner_amount;
            let status = this.owner_share_arr.status;

            let error_server_msg2 = [];
            if (owner_code === '') error_server_msg2.push(['You have not selected a company from the list.']);
            if (owner_percentage === '') {
                error_server_msg2.push(['You have not used a valid percentage figure.']);
            } else {
                if (owner_percentage === '')
                    error_server_msg2.push([
                        'You have not used a valid percentage figure (' + owner_percentage + '%).',
                    ]);
                if (isNaN(parseFloat(owner_percentage)))
                    error_server_msg2.push(['You have not used a valid percentage figure']);
            }
            if (owner_amount !== '' && isNaN(parseFloat(owner_amount)))
                error_server_msg2.push(['You have not used a valid amount figure']);

            if (status === 'new') {
                let check_duplicate_owner_flag = false;
                let owner_share_list = this.owner_share_list;
                for (let x = 0; x <= owner_share_list.length - 1; x++) {
                    let lc_owner_code = owner_share_list[x].owner.field_key;
                    if (lc_owner_code === owner_code) {
                        check_duplicate_owner_flag = true;
                    }
                }
                if (check_duplicate_owner_flag) error_server_msg2.push(['You have selected a duplicate company code.']);
            }

            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('owner_code', owner_code);
                form_data.append('owner_percentage', owner_percentage);
                form_data.append('owner_amount', owner_amount);
                form_data.append('status', status);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/update-or-create/owner-share';
                } else {
                    apiUrl = 'temp/property/update-or-create/owner-share';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.owner_share_arr.status = 'saved';
                    this.error_server_msg2 = response.data.error_server_msg2;

                    this.loading_setting = false;
                    if (this.error_server_msg2.length === 0) {
                        this.success_flag = true;
                        if (!this.new_property) {
                            this.edit_form = false;
                        }
                        this.loadForm();
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        modalPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.owner_share_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.owner_share_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.owner_share_arr = this.owner_share_list[this.modal_current_ctr];
            this.owner_share_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.owner_share_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.owner_share_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.owner_share_arr = this.owner_share_list[this.modal_current_ctr];
            this.owner_share_arr.index = this.modal_current_ctr;
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.owner_share_arr = this.owner_share_list[index];
            this.owner_share_arr.index = index;
            this.modal_current_ctr = index;
        },
        modalAddData: function () {
            this.edit_form = true;
            this.AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.owner_share_arr = {
                index: 'New',
                owner: { field_key: '', field_value: '' },
                owner_code: '',
                owner_name: '',
                owner_percentage: this.roundTo(100 - parseFloat(this.total_percentage), 6),
                owner_amount: '0.00',
                status: 'new',
            };
        },
        openOwnerInfoModal: function (index) {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let owner_code = this.owner_share_list[index].owner_code;
            this.company_details_modal = true;
            var form_data = new FormData();
            form_data.append('owner_code', owner_code);
            form_data.append('no_load', true);
            let apiUrl = 'company/load-company-all-details';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.company_details_arr = response.data.company_details_arr;
                this.loadCountryDefaults();
                this.loading_setting = false;
            });
        },
        loadOwnerList: function () {
            this.$api.post('ui/fetch/all-owners-list').then((response) => {
                this.owner_list = response.data.owner_list;
            });
        },
        async deleteOwnerShare(index) {
            if (index !== 'New') {
                let status = this.owner_share_list[index].status;
                let owner_code = this.owner_share_list[index].owner_code;

                if (status === 'new') {
                    this.owner_share_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('owner_code', owner_code);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/owner-share';
                        } else {
                            apiUrl = 'temp/property/delete/owner-share';
                        }
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.owner_share_list.splice(index, 1);
                            this.loading_setting = false;
                            this.owner_share_list_old = JSON.parse(JSON.stringify(this.owner_share_list));
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        });
                    }
                }
            }
        },
        breakdownEmail: function (email) {
            if (email !== '') return email.split(';');
        },
        loadCountryDefaults: function (country) {
            this.loading_page_setting = true;

            var form_data = new FormData();
            if (country) form_data.append('country', country);

            let api_url = this.cirrus8_api_url + 'admin/country_defaults/load';
            this.$api.post(api_url, form_data).then((response) => {
                this.error_server_msg2 = response.data.validation_errors;
                this.loading_page_setting = false;
                this.country_defaults = response.data.default;
            });
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.loadOwnerList();
            }
        },
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
        ...mapGetters(['getDDCountryStates']),
        totalPercentage: function () {
            let owner_share_list = this.owner_share_list;
            let total_percentage = 0.0;
            for (let x = 0; x <= owner_share_list.length - 1; x++) {
                let percentage = parseFloat(owner_share_list[x].owner_percentage);
                total_percentage = total_percentage + percentage;
            }
            return total_percentage;
        },
        formattedBsb() {
            return formatWithDelimiter(
                this.company_details_arr.company_bsb,
                this.country_defaults.bsb_format.delimiter,
                this.country_defaults.bsb_format.delimiter_frequency,
            );
        },
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();

        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'Owner Code: Owner Name', value: 'owner_description', width: 'auto' },
                { text: 'Percentage Owned', value: 'owner_percentage', align: 'right', width: 'auto' },
                { text: 'Fixed Amount Paid to Owner', value: 'owner_amount', align: 'right', width: 'auto' },
            ];
            this.items_per_page = 9999;
        }
    },
    created() {
        bus.$on('loadPropertyOwnerSharesFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
