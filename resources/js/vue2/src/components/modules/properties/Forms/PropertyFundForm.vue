<template>
    <div
        v-on:dblclick="doubleClickForm()"
        v-if="!isMultiplePropertyLedger"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Property Fund</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable()"
                    class="v-step-refresh-button"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="property_fund_list.length === 0 && !loading_setting"
            v-show="isEditable()"
        >
            <v-btn
                v-if="!pmro_read_only"
                depressed
                small
                color="success"
                @click="modalAddData()"
                >Add Property Fund
            </v-btn>
            <div
                style="margin: 10px 0px"
                v-else
            >
                No property funds at the moment
            </div>
        </v-col>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="property_fund_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="property_fund_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ property_fund_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.fund_partition_id="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.fund_partition_id }}</span>
                        </div>
                    </template>
                    <template v-slot:item.fund_name="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.fund_name }}</span>
                        </div>
                    </template>
                    <template v-slot:item.fund_account_list_description="{ item }">
                        <v-tooltip
                            left
                            max-width="200"
                        >
                            <template v-slot:activator="{ on, attrs }">
                                <v-icon
                                    v-bind="attrs"
                                    v-on="on"
                                    @click="modalOpenDetails(property_fund_list.indexOf(item))"
                                    >preview
                                </v-icon>
                            </template>
                            <span>{{ item.fund_account_list_description }}</span>
                        </v-tooltip>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenAED(property_fund_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit
                        </v-icon>
                        <v-icon
                            color="red"
                            v-if="edit_form"
                            @click="deletePropertyFund(property_fund_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="property_fund_list.length > 5"
                    v-if="isEditable()"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deletePropertyFund(property_fund_arr.index)"
            v-if="property_fund_arr.fund_account_list"
        >
            <v-card>
                <v-card-title class="headline">
                    Property Fund Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="property_fund_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        property_fund_arr.index === 'New'
                                            ? property_fund_arr.index
                                            : property_fund_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Property Fund ID:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                    id="multiselect_owner"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="property_fund_arr.fund_partition_id"
                                        size=""
                                        :id="'fund_id' + property_fund_arr.index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Fund Id' : false"
                                        :rules_length="10"
                                        :maxlength="10"
                                        :size="10"
                                        :edit_form="property_fund_arr.index === 'New'"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Property Fund Name:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="property_fund_arr.fund_name"
                                        size=""
                                        :id="'fund_name' + property_fund_arr.index"
                                        data-inverted=""
                                        :rules_length="50"
                                        :maxlength="50"
                                        :size="50"
                                        :data-tooltip="edit_form ? 'Fund name' : false"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <!--                <v-col xs="12" sm="12" md="12" class="form-label required">Accounts:</v-col>-->
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="d-flex justify-center"
                                >
                                    <vue-dual-list-select
                                        v-model="property_fund_arr.fund_account_list"
                                        v-bind:options="account_list"
                                    ></vue-dual-list-select>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous
                        </v-icon>
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="property_fund_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add
                        </v-icon>
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="property_fund_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deletePropertyFund(property_fund_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="property_fund_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next
                        </v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->

        <!--   AED modal      -->
        <v-dialog
            v-model="show_details_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Property Fund Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_details_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        property_fund_arr.index === 'New'
                                            ? property_fund_arr.index
                                            : property_fund_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Property Fund ID:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                    id="multiselect_owner"
                                >
                                    <span class="form-input-text">{{ property_fund_arr.fund_partition_id }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Property Fund Name:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{ property_fund_arr.fund_name }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Accounts:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <div
                                        v-for="(
                                            fund_account_list_data, fund_account_list_index
                                        ) in property_fund_arr.fund_account_list"
                                        :key="fund_account_list_index"
                                    >
                                        <span class="form-input-text"
                                            ><strong>{{ fund_account_list_data.field_key }}</strong> -
                                            {{ fund_account_list_data.field_value }}</span
                                        >
                                        <br />
                                    </div>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';
import Vue from 'vue';

Vue.component('vue-dual-list-select', require('../../../elements/VueDualListSelect.vue').default);

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_FUNDS',
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Property Fund ID', value: 'fund_partition_id', width: '30%', align: 'middle' },
                { text: 'Property Fund Name', value: 'fund_name' },
                { text: 'View Accounts', value: 'fund_account_list_description', sortable: false, align: 'center' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            AED_modal: false,
            success_flag: false,
            show_details_modal: false,
            modal_current_ctr: 0,
            property_fund_list: [],
            property_fund_arr: [],
            account_list: [],
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        this.loadAllAccounts();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'Property Fund ID', value: 'fund_partition_id', width: 'auto', align: 'middle' },
                { text: 'Property Fund Name', value: 'fund_name', width: 'auto' },
                {
                    text: 'View Accounts',
                    value: 'fund_account_list_description',
                    sortable: false,
                    align: 'center',
                    width: 'auto',
                },
            ];
            this.items_per_page = 9999;
        }
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        resetForm: function () {
            this.edit_form = false;
        },
        loadForm: function () {
            this.error_server_msg = {};
            if (this.property_code !== '') {
                this.loadPropertyFund();
            }
        },
        loadPropertyFund: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/property-funds', form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            } else {
                let apiUrl = 'temp/property/fetch/property-funds';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            }
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.property_fund_list = response.data.property_fund_list;
            let total_percentage = 0.0;
            for (let x = 0; x <= this.property_fund_list.length - 1; x++) {
                let percentage = parseFloat(this.property_fund_list[x].owner_percentage);
                total_percentage = total_percentage + percentage;
            }
            this.total_percentage = total_percentage;
        },
        modalSubmitData: function () {
            let index = this.property_fund_arr.index;
            let old_fund_partition_id = 'new';
            let old_fund_name = 'new';

            let fund_partition_id = this.property_fund_arr.fund_partition_id;
            let fund_name = this.property_fund_arr.fund_name;
            let fund_account_list = this.property_fund_arr.fund_account_list;
            let status = this.property_fund_arr.status;

            if (status !== 'new') {
                old_fund_partition_id = this.property_fund_list[index].fund_partition_id;
                old_fund_name = this.property_fund_list[index].fund_name;
            }
            let error_server_msg2 = [];
            if (fund_partition_id === '') error_server_msg2.push(['You have not provided a valid property fund ID.']);
            if (fund_name === '') error_server_msg2.push(['You have not provided a valid property fund name.']);

            if (fund_account_list.length === 0)
                error_server_msg2.push(['You have not selected an account from the list.']);

            if (status === 'new') {
                let check_duplicate_id_flag = false;
                let check_duplicate_name_flag = false;
                let property_fund_list = this.property_fund_list;
                for (let x = 0; x <= property_fund_list.length - 1; x++) {
                    let lc_fund_partition_id = property_fund_list[x].fund_partition_id;
                    let lc_fund_name = property_fund_list[x].fund_name;
                    if (lc_fund_partition_id === fund_partition_id) {
                        check_duplicate_id_flag = true;
                    }
                    if (lc_fund_name === fund_name) {
                        check_duplicate_name_flag = true;
                    }
                }
                if (check_duplicate_id_flag)
                    error_server_msg2.push(['Property fund ID already exists for this property.']);
                if (check_duplicate_name_flag)
                    error_server_msg2.push(['Property fund name already exists for this property.']);
            }

            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('fund_partition_id', fund_partition_id);
                form_data.append('old_fund_partition_id', old_fund_partition_id);
                form_data.append('fund_name', fund_name);
                form_data.append('old_fund_name', old_fund_name);
                form_data.append('fund_account_list', JSON.stringify(fund_account_list));
                form_data.append('status', status);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/update-or-create/property-fund';
                } else {
                    apiUrl = 'temp/property/update-or-create/property-fund';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.error_server_msg2 = response.data.error_server_msg2;

                    this.loading_setting = false;
                    if (this.error_server_msg2.length === 0) {
                        if (this.property_fund_arr.index === 'New')
                            this.property_fund_arr.index = this.property_fund_list.length;
                        this.property_fund_arr.fund_partition_id = response.data.fund_partition_id;
                        this.property_fund_arr.status = 'saved';

                        this.success_flag = true;
                        if (!this.new_property) {
                            this.edit_form = false;
                        }
                        this.loadForm();
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        modalPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.property_fund_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.property_fund_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.property_fund_arr = this.property_fund_list[this.modal_current_ctr];
            this.property_fund_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.property_fund_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.property_fund_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.property_fund_arr = this.property_fund_list[this.modal_current_ctr];
            this.property_fund_arr.index = this.modal_current_ctr;
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.property_fund_arr = this.property_fund_list[index];
            this.property_fund_arr.index = index;
            this.modal_current_ctr = index;
        },
        modalOpenDetails: function (index) {
            this.show_details_modal = true;
            this.property_fund_arr = this.property_fund_list[index];
            this.property_fund_arr.index = index;
            this.modal_current_ctr = index;
        },
        modalAddData: function () {
            this.edit_form = true;
            this.AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.property_fund_arr = {
                index: 'New',
                fund_account_list: [],
                fund_partition_id: '',
                fund_name: '',
                status: 'new',
            };
        },
        loadAllAccounts: function () {
            this.$api.post('loadAllAccountDropdown').then((response) => {
                this.account_list = response.data.grouped;
            });
        },
        async deletePropertyFund(index) {
            if (index !== 'New') {
                let status = this.property_fund_list[index].status;
                let fund_partition_id = this.property_fund_list[index].fund_partition_id;

                if (status === 'new') {
                    this.property_fund_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('fund_partition_id', fund_partition_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/property-fund';
                        } else {
                            apiUrl = 'temp/property/delete/property-fund';
                        }
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.property_fund_list.splice(index, 1);
                            this.loading_setting = false;
                            this.property_fund_list_old = JSON.parse(JSON.stringify(this.property_fund_list));
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        });
                    }
                }
            }
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
            }
        },
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_property_form_read_only',
            'sys_ver_control_list',
        ]),
        ...mapGetters(['getDDCountryStates']),
        isMultiplePropertyLedger() {
            return !!this.sys_ver_control_list?.isMultiplePropertyLedger;
        },
    },
    created() {
        bus.$on('loadPropertyFundFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
