<template>
    <div>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
            v-on:dblclick="doubleClickForm('MAIN_GENERAL_DETAILS', 'edit_form_gen')"
        >
            <v-card-actions>
                <h6 class="title font-weight-black">General Details</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    v-show="
                        !formSectionReadOnly(
                            pm_property_form_read_only,
                            form_type,
                            'MAIN_GENERAL_DETAILS',
                            is_inactive,
                        ) && !pmro_read_only
                    "
                    class="v-step-edit-button"
                    v-if="!edit_form_gen"
                    icon
                    @click="edit_form_gen = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable('MAIN_GENERAL_DETAILS') && !pmro_read_only"
                    v-if="edit_form_gen && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form_gen = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !formSectionReadOnly(
                            pm_property_form_read_only,
                            form_type,
                            'MAIN_GENERAL_DETAILS',
                            is_inactive,
                        ) && !pmro_read_only
                    "
                    v-if="edit_form_gen"
                    class="v-step-save-1-button"
                    icon
                    @click="saveForm()"
                >
                    <v-icon
                        light
                        color="green"
                        >check
                    </v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="isEditable('MAIN_GENERAL_DETAILS')"
                    class="v-step-refresh-button"
                    icon
                    @click="loadMainFormDetails()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="
                        show_activity_log_modal = true;
                        form_sub_section = 'PROPERTY_MAIN_DETAIL_GENERAL_DETAILS';
                    "
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
            v-on:dblclick="doubleClickForm('MAIN_GENERAL_DETAILS', 'edit_form_gen')"
        >
            <v-row class="form-row no-gutters">
                <v-col
                    xs="12"
                    sm="3"
                    md="3"
                    lg="3"
                    class="form-input"
                >
                    <!--							<div style="max-height: 300px !important;"> -->
                    <div
                        class="pt-1 pb-1"
                        v-show="isEditable('MAIN_GENERAL_DETAILS') && edit_form_gen"
                    >
                        <div v-if="isRemove">
                            <v-btn
                                x-small
                                tile
                                background
                                depressed
                                @click="removeImage"
                            >
                                <v-icon
                                    small
                                    color="primary"
                                >
                                    close
                                </v-icon>
                            </v-btn>
                        </div>
                        <div v-else>
                            <v-btn
                                x-small
                                tile
                                background
                                depressed
                                :class="'add-property-image'"
                                :loading="isSelecting"
                                @click="onButtonClick"
                            >
                                <v-icon
                                    small
                                    color="primary"
                                >
                                    add_a_photo
                                </v-icon>
                            </v-btn>
                            <input
                                ref="uploader"
                                class="d-none"
                                type="file"
                                accept="image/*"
                                @change="onFileChanged"
                            />
                        </div>
                    </div>
                    <v-img
                        id="property_image"
                        max-height="250px"
                        v-bind:src="property_image"
                        position="center bottom"
                        contain
                        dense
                    />
                    <!--</div>-->
                </v-col>
                <v-col
                    xs="12"
                    sm="9"
                    md="9"
                    lg="9"
                >
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        Property Name:
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form_gen"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <cirrus-input
                                            :id="'property_name'"
                                            v-model="property_name"
                                            :edit_form="edit_form_gen"
                                        ></cirrus-input>
                                        <v-btn
                                            x-small
                                            depressed
                                            color="normal"
                                            @click="deleteProperty()"
                                            v-if="user_type === 'A'"
                                            v-show="edit_form_gen && check_chargers_flag === '0'"
                                            >Delete Property
                                        </v-btn>
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        Property Address:
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form_gen"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <cirrus-input
                                            :id="'property_address'"
                                            v-model="property_address"
                                            :edit_form="edit_form_gen"
                                        ></cirrus-input>
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        {{ properCase(suburb_label) }}:
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form_gen"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <span v-if="edit_form_gen">
                                            <v-combobox
                                                v-if="property_country.field_key == 'AU'"
                                                v-model="property_suburb"
                                                :maxlength="40"
                                                :items="suburb_list_filtered"
                                                item-value="label"
                                                item-text="label"
                                                @change="suburbSelected(property_suburb)"
                                                auto-select-first
                                                hide-selected
                                                persistent-hint
                                                append-icon
                                                :search-input.sync="search_suburb"
                                                :hide-no-data="!search_suburb"
                                                dense
                                                ref="refSuburb"
                                                flat
                                            >
                                                <template v-slot:no-data>
                                                    <v-list-item>
                                                        <v-chip
                                                            v-model="search_suburb"
                                                            small
                                                        >
                                                            {{ search_suburb }}
                                                        </v-chip>
                                                    </v-list-item>
                                                </template>
                                            </v-combobox>
                                            <v-text-field
                                                :maxlength="40"
                                                dense
                                                v-model="property_suburb"
                                                v-else
                                            />
                                        </span>
                                        <span
                                            class="form-input-text"
                                            v-if="!edit_form_gen"
                                            >{{ property_suburb }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>

                    <v-row
                        class="form-row no-gutters"
                        v-if="country_defaults.display_state === true"
                    >
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        Property State:
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form_gen"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <multiselect
                                            tabindex="15"
                                            @input="propertyStateChanged()"
                                            v-if="edit_form_gen"
                                            v-model="property_state"
                                            :options="getDDCountryStates(property_country.field_key)"
                                            :allowEmpty="false"
                                            class="vue-select2 dropdown-left dropdown-300"
                                            group-label="language"
                                            placeholder="Select a state"
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                        <span
                                            v-if="!edit_form_gen"
                                            class="form-input-text"
                                            >{{ property_state.field_value }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        Post Code:
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form_gen"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <span v-if="edit_form_gen">
                                            <v-combobox
                                                v-if="property_country.field_key == 'AU'"
                                                v-model="property_post_code"
                                                :maxlength="10"
                                                :items="postcode_list_filtered"
                                                item-value="label"
                                                item-text="label"
                                                @change="postcodeSelected(property_post_code)"
                                                auto-select-first
                                                hide-selected
                                                persistent-hint
                                                append-icon
                                                :search-input.sync="searchPostcode"
                                                :hide-no-data="!searchPostcode"
                                                dense
                                                ref="refSuburb"
                                                flat
                                            >
                                                <template v-slot:no-data>
                                                    <v-list-item>
                                                        <v-chip
                                                            v-model="searchPostcode"
                                                            small
                                                        >
                                                            {{ searchPostcode }}
                                                        </v-chip>
                                                    </v-list-item>
                                                </template>
                                            </v-combobox>
                                            <v-text-field
                                                :maxlength="10"
                                                dense
                                                v-model="property_post_code"
                                                v-else
                                            />
                                        </span>
                                        <span
                                            class="form-input-text"
                                            v-if="!edit_form_gen"
                                            >{{ property_post_code }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        Country:
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form_gen"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <multiselect
                                            tabindex="0"
                                            v-if="edit_form_gen"
                                            v-model="property_country"
                                            :options="dd_country_list"
                                            :allowEmpty="false"
                                            class="vue-select2 dropdown-left dropdown-300"
                                            group-label="language"
                                            placeholder="Select a country"
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                        <span
                                            v-if="!edit_form_gen"
                                            class="form-input-text"
                                            >{{ property_country.field_value }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        {{ property_manager_label }}:
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form_gen"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <cirrus-single-select-v2
                                            v-if="edit_form_gen"
                                            v-model="man_prop_manager"
                                            :options="property_manager_list"
                                            ref="refPropertyType"
                                            trackBy="field_key"
                                            label="field_key_w_value"
                                            return="field_key"
                                            placeholder="Please select"
                                        />
                                        <span
                                            v-if="!edit_form_gen && man_prop_manager !== ''"
                                            class="form-input-text"
                                            >{{ man_prop_manager }} -
                                            {{ getDropdownName(man_prop_manager, property_manager_list) }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        Next Charge Date:
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form_gen"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <cirrus-icon-date-picker
                                            :id="'property_next_charge_date' + String(Math.random()).replace('.', '')"
                                            :index="property_next_charge_date_index"
                                            :size="'40'"
                                            v-model="property_next_charge_date"
                                            :edit_form="edit_form_gen"
                                        ></cirrus-icon-date-picker>
                                        <div
                                            class="tooltip"
                                            v-show="edit_form_gen"
                                        >
                                            <v-icon small>info</v-icon>
                                            <span
                                                class="tooltiptext"
                                                style="width: 300px !important; text-align: left"
                                            >
                                                Leave as default.
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                </v-col>
            </v-row>
        </div>
        <!-- Bank details is only available in new property new_property   -->
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
            v-on:dblclick="doubleClickForm('MAIN_BANK_ACCOUNT', 'edit_form_ba')"
            v-if="!isMultiplePropertyLedger"
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Bank Account</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    class="v-step-edit-button"
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, 'MAIN_BANK_ACCOUNT', is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form_ba"
                    icon
                    @click="edit_form_ba = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable('MAIN_BANK_ACCOUNT') && !pmro_read_only"
                    v-if="edit_form_ba && !new_property && !pmro_read_only"
                    icon
                    @click="
                        loadForm();
                        edit_form_ba = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="edit_form_ba && !pmro_read_only"
                    class="v-step-save-1-button"
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, 'MAIN_BANK_ACCOUNT', is_inactive) &&
                        !pmro_read_onl
                    "
                    icon
                    @click="saveForm()"
                >
                    <v-icon
                        light
                        color="green"
                        >check
                    </v-icon>
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    v-if="isEditable('MAIN_BANK_ACCOUNT')"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="
                        show_activity_log_modal = true;
                        form_sub_section = 'PROPERTY_MAIN_DETAIL_BANK_ACCOUNTS';
                    "
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <div
            class="page-form"
            v-if="!loading_setting && !isMultiplePropertyLedger"
            v-on:dblclick="doubleClickForm('MAIN_BANK_ACCOUNT', 'edit_form_ba')"
        >
            <div class="form-row">
                <v-row
                    class="form-row"
                    v-if="new_property"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        :class="edit_form_ba ? 'form-label required' : 'form-label'"
                        >Bank Account
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            v-if="edit_form_ba"
                            v-model="property_bank_account"
                            :options="bank_account_list"
                            :custom-label="nameWithDash"
                            :allowEmpty="false"
                            class="vue-select2 dropdown-left dropdown-300"
                            group-label="language"
                            placeholder="Select an account"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                        >
                            <span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="
                        !new_property || page_form_type == 'admin-approval-page' || page_form_type == 'client-temp-page'
                    "
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Bank Account
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">
                            <div>{{ property_bank_account_code }} - {{ property_bank_account_name }}</div>
                            <br />
                            <div>
                                To change the associated {{ trust_account_label }} for this property please contact
                                support - <EMAIL>
                            </div>
                        </span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="!isMultiplePropertyLedger"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">
                            <sui-checkbox
                                v-model="propertyUseEFTOnInvoice"
                                data-inverted=""
                                label="Use Principal Owner's EFT details on Tenant Invoices (Owner is paid direct by tenants)"
                                :disabled="!edit_form_ba"
                            />
                        </span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="!isMultiplePropertyLedger"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">
                            <sui-checkbox
                                v-model="property_auto_pay_owner_flag"
                                data-inverted=""
                                :data-tooltip="
                                    edit_form_ba ? 'Allow Owner Payments (Default setting for Payment Runs)' : false
                                "
                                label="Allow Owner Payments (Default setting for Payment Runs)"
                                :disabled="!edit_form_ba"
                            />
                        </span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="!isMultiplePropertyLedger"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">
                            <sui-checkbox
                                v-model="bank_partitioning_flag"
                                data-inverted=""
                                :data-tooltip="edit_form_ba ? 'Apply partitioning to the property' : false"
                                label="Apply partitioning to the property"
                                :disabled="!edit_form_ba"
                            />
                        </span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="!isMultiplePropertyLedger"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Default Amount to withhold <br />from Owner
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            :inputFormat="'dollar'"
                            custom_class="cirrus-input-table-textbox"
                            v-model="property_withhold_owner_amount"
                            size=""
                            :id="'property_withhold_owner_amount'"
                            data-inverted=""
                            :edit_form="edit_form_ba"
                        ></cirrus-input>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="!isMultiplePropertyLedger"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Default Note for withheld <br />amount
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            custom_class="cirrus-input-table-textbox"
                            v-model="property_withhold_owner_comment"
                            size=""
                            :id="'property_withhold_owner_comment'"
                            data-inverted=""
                            :edit_form="edit_form_ba"
                        ></cirrus-input>
                    </v-col>
                </v-row>
            </div>
        </div>

        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
            v-if="new_property || page_form_type == 'admin-approval-page' || page_form_type == 'client-temp-page'"
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Calendar Used</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    v-if="isEditable()"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>

        <div
            class="page-form"
            v-if="new_property || page_form_type == 'admin-approval-page' || page_form_type == 'client-temp-page'"
        >
            <div class="form-row">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        :class="
                            new_property ||
                            (page_form_type !== 'admin-approval-page' && page_form_type !== 'client-temp-page')
                                ? 'form-label required'
                                : 'form-label'
                        "
                        >Property Calendar
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            class="form-toggle"
                            v-model="property_calendar_index"
                            mandatory
                        >
                            <v-btn
                                small
                                text
                                :disabled="
                                    !(
                                        new_property ||
                                        (page_form_type !== 'admin-approval-page' &&
                                            page_form_type !== 'client-temp-page')
                                    )
                                "
                            >
                                Financial Year
                            </v-btn>
                            <v-btn
                                small
                                text
                                :disabled="
                                    !(
                                        new_property ||
                                        (page_form_type !== 'admin-approval-page' &&
                                            page_form_type !== 'client-temp-page')
                                    )
                                "
                            >
                                Calendar Year
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
            </div>
        </div>

        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
            v-on:dblclick="doubleClickForm('PROPERTY_OWNER_SHARES', 'edit_form_ow')"
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Owner</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    class="v-step-edit-button"
                    v-show="
                        !formSectionReadOnly(
                            pm_property_form_read_only,
                            form_type,
                            'PROPERTY_OWNER_SHARES',
                            is_inactive,
                        ) && !pmro_read_only
                    "
                    v-if="!edit_form_ow"
                    icon
                    @click="edit_form_ow = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable('PROPERTY_OWNER_SHARES') && !pmro_read_only"
                    v-if="edit_form_ow && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form_ow = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="edit_form_ow && !pmro_read_only"
                    class="v-step-save-1-button"
                    v-show="
                        !formSectionReadOnly(
                            pm_property_form_read_only,
                            form_type,
                            'PROPERTY_OWNER_SHARES',
                            is_inactive,
                        ) && !pmro_read_onl
                    "
                    icon
                    @click="saveForm()"
                >
                    <v-icon
                        light
                        color="green"
                        >check
                    </v-icon>
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    v-if="isEditable('PROPERTY_OWNER_SHARES')"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="
                        show_activity_log_modal = true;
                        form_sub_section = 'PROPERTY_MAIN_DETAIL_OWNER';
                    "
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <div
            class="page-form"
            v-if="!loading_setting"
            v-on:dblclick="doubleClickForm('PROPERTY_OWNER_SHARES', 'edit_form_ow')"
        >
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Principal Owner:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form_ow"
                            >
                                *
                            </td>
                            <td style="text-wrap: none">
                                <table
                                    v-if="edit_form_ow"
                                    style="border-spacing: 0"
                                >
                                    <tr>
                                        <td style="width: 312px">
                                            <cirrus-single-select-v2
                                                v-model="man_principal_owner"
                                                :options="principal_owner_list"
                                                ref="refPropertyType"
                                                trackBy="field_key"
                                                label="field_key_w_value"
                                                return="field_key"
                                                placeholder="Please select an owner"
                                            />
                                        </td>
                                        <td>
                                            <a
                                                v-if="man_principal_owner !== ''"
                                                v-on:click="goToCompanyShortcut(man_principal_owner)"
                                                data-tooltip="Go to company"
                                            >
                                                <v-icon
                                                    elevation="0"
                                                    icon
                                                    small
                                                    height="30"
                                                    >business
                                                </v-icon>
                                            </a>
                                        </td>
                                    </tr>
                                </table>
                                <span
                                    v-if="!edit_form_ow"
                                    class="form-input-text"
                                    >{{ man_principal_owner }} -
                                    {{ getDropdownName(man_principal_owner, principal_owner_list) }}</span
                                >
                                <a
                                    v-if="man_principal_owner !== '' && !edit_form_ow"
                                    v-on:click="goToCompanyShortcut(man_principal_owner)"
                                    data-tooltip="Go to company"
                                >
                                    <v-icon
                                        elevation="0"
                                        icon
                                        small
                                        height="30"
                                        >business
                                    </v-icon>
                                </a>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
        </div>
        <property-owner-shares-form-component
            :property_code="property_code"
            :version_id="version_id"
            :new_property="new_property"
            :page_form_type="page_form_type"
            :pmro_read_only="pmro_read_only"
            :is_inactive="is_inactive"
        ></property-owner-shares-form-component>

        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
            v-on:dblclick="doubleClickForm('MAIN_REPORTING', 'edit_form_re')"
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Reporting</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    class="v-step-edit-button"
                    v-if="!edit_form_re"
                    icon
                    @click="edit_form_re = true"
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, 'MAIN_REPORTING', is_inactive) &&
                        !pmro_read_only
                    "
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable('MAIN_REPORTING') && !pmro_read_only"
                    v-if="edit_form_re && !new_property && !pmro_read_only"
                    icon
                    @click="
                        loadForm();
                        edit_form_re = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="edit_form_re && !pmro_read_only"
                    class="v-step-save-1-button"
                    icon
                    @click="saveForm()"
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, 'MAIN_REPORTING', is_inactive) &&
                        !pmro_read_only
                    "
                >
                    <v-icon
                        light
                        color="green"
                        >check
                    </v-icon>
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    v-if="isEditable('MAIN_REPORTING')"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="
                        show_activity_log_modal = true;
                        form_sub_section = 'PROPERTY_MAIN_DETAIL_REPORTING';
                    "
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
            v-on:dblclick="doubleClickForm('MAIN_REPORTING', 'edit_form_re')"
        >
            <div class="form-row no-gutters">
                <v-row class="form-row no-gutters">
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Property Type:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form_re"
                            >
                                &nbsp&nbsp
                            </td>
                            <td>
                                <cirrus-single-select-v2
                                    v-if="edit_form_re"
                                    v-model="man_prop_type"
                                    :options="property_type_list"
                                    ref="refPropertyType"
                                    trackBy="field_key"
                                    label="field_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                                <span
                                    v-if="!edit_form_re"
                                    class="form-input-text"
                                    >{{ getDropdownName(man_prop_type, property_type_list) }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-row>
                <v-row class="form-row no-gutters">
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Property Group:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form_re"
                            >
                                &nbsp&nbsp
                            </td>
                            <td>
                                <cirrus-single-select-v2
                                    v-if="edit_form_re"
                                    v-model="man_prop_group"
                                    :options="property_group_list"
                                    ref="refPropertyGroup"
                                    trackBy="field_key"
                                    label="field_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                                <span
                                    v-if="!edit_form_re && property_group_list.length > 0"
                                    class="form-input-text"
                                    >{{ getDropdownName(man_prop_group, property_group_list) }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-row>
                <v-row class="form-row no-gutters">
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Payment Run Group:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form_re"
                            >
                                &nbsp&nbsp
                            </td>
                            <td>
                                <cirrus-single-select-v2
                                    v-if="edit_form_re"
                                    v-model="payment_group"
                                    :options="payment_group_list"
                                    ref="refPropertyGroup"
                                    trackBy="field_key"
                                    label="field_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                                <span
                                    v-if="!edit_form_re && payment_group_list.length > 0"
                                    class="form-input-text"
                                    >{{ getDropdownName(payment_group, payment_group_list) }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-row>
                <v-row class="form-row no-gutters">
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Accounting Basis:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form_re"
                            >
                                &nbsp&nbsp
                            </td>
                            <td>
                                <span
                                    class="form-input-text"
                                    v-show="!edit_form_re"
                                    v-if="man_accounting_basis_index === 0"
                                    >Cash</span
                                >
                                <span
                                    class="form-input-text"
                                    v-show="!edit_form_re"
                                    v-if="man_accounting_basis_index === 1"
                                    >Accrual</span
                                >
                                <v-btn-toggle
                                    class="form-toggle"
                                    v-show="edit_form_re"
                                    v-model="man_accounting_basis_index"
                                    mandatory
                                >
                                    <v-btn
                                        small
                                        text
                                        >Cash
                                    </v-btn>
                                    <v-btn
                                        small
                                        text
                                        >Accruals
                                    </v-btn>
                                </v-btn-toggle>
                            </td>
                        </tr>
                    </table>
                </v-row>
                <v-row class="form-row no-gutters">
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Lease Type:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form_re"
                            >
                                &nbsp&nbsp
                            </td>
                            <td>
                                <cirrus-single-select-v2
                                    v-if="edit_form_re"
                                    v-model="man_lease_type"
                                    :options="lease_type_list"
                                    ref="refPropertyLeaseType"
                                    trackBy="field_key"
                                    label="field_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                                <span
                                    v-if="!edit_form_re"
                                    class="form-input-text"
                                    >{{ getDropdownName(man_lease_type, lease_type_list) }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-row>
                <v-row class="form-row no-gutters"></v-row>
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    {{ tax_label.toUpperCase() }} Status:
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <span class="form-input-text">{{ property_reporting_gst_status }}</span>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    {{ tax_label.toUpperCase() }} Basis:
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form_re"
                                        v-if="property_reporting_gst_basis_index === 0"
                                        >Cash</span
                                    >
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form_re"
                                        v-if="property_reporting_gst_basis_index === 1"
                                        >Accrual</span
                                    >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-show="edit_form_re"
                                        v-model="property_reporting_gst_basis_index"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form_re"
                                        >
                                            Cash
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form_re"
                                        >
                                            Accrual
                                        </v-btn>
                                    </v-btn-toggle>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    Owner Report Type:
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form_re"
                                ></td>
                                <td>
                                    <cirrus-single-select-v2
                                        v-if="edit_form_re"
                                        v-model="property_reporting_report_type"
                                        :options="report_type_list"
                                        ref="refPropertyOwnerReportType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Select a report type"
                                    />
                                    <span
                                        v-if="!edit_form_re"
                                        class="form-input-text"
                                        >{{ getDropdownName(property_reporting_report_type, report_type_list) }}</span
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    {{ tax_label.toUpperCase() }} Report Period:
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form_re"
                                        v-if="property_reporting_gst_period_index === 0"
                                        >Monthly</span
                                    >
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form_re"
                                        v-if="property_reporting_gst_period_index === 1"
                                        >Quarterly</span
                                    >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-show="edit_form_re"
                                        v-model="property_reporting_gst_period_index"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form_re"
                                        >
                                            Monthly
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form_re"
                                        >
                                            Quarterly
                                        </v-btn>
                                    </v-btn-toggle>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                ></td>
                                <td
                                    class="required"
                                    v-if="edit_form_re"
                                ></td>
                                <td>
                                    <div v-if="edit_form_re">
                                        <sui-checkbox
                                            v-model="property_reporting_retail_flag"
                                            label="Is this a Retail property?"
                                        />
                                        <span class="form-input-text"><strong>Relates to reporting</strong></span>
                                    </div>
                                    <div v-if="!edit_form_re">
                                        <span
                                            class="form-input-text"
                                            v-if="property_reporting_retail_flag"
                                            >This is a Retail property</span
                                        >
                                        <span
                                            class="form-input-text"
                                            v-if="!property_reporting_retail_flag"
                                            >This is not a Retail property</span
                                        >
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                ></td>
                                <td
                                    class="required"
                                    v-if="edit_form_re"
                                ></td>
                                <td>
                                    <div v-if="edit_form_re">
                                        <sui-checkbox
                                            v-model="property_reporting_strata_flag"
                                            :label="'Is this a ' + strata_label + ' property?'"
                                        />
                                        <span class="form-input-text"><strong>Relates to reporting</strong></span>
                                    </div>
                                    <div v-if="!edit_form_re">
                                        <span
                                            class="form-input-text"
                                            v-if="property_reporting_strata_flag"
                                            >This is a {{ strata_label }} property</span
                                        >
                                        <span
                                            class="form-input-text"
                                            v-if="!property_reporting_strata_flag"
                                            >This is not a {{ strata_label }} property</span
                                        >
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                ></td>
                                <td
                                    class="required"
                                    v-if="edit_form_re"
                                ></td>
                                <td>
                                    <div v-if="edit_form_re">
                                        <sui-checkbox
                                            v-model="property_reporting_default_set_flag"
                                            label="Default Setting: Attach Supplier Invoices to Owner Report"
                                        />
                                        <span class="form-input-text"><strong></strong></span>
                                    </div>
                                    <div v-if="!edit_form_re">
                                        <span
                                            class="form-input-text"
                                            v-if="property_reporting_default_set_flag"
                                            >Attach Supplier Invoices to Owner Report is ON</span
                                        >
                                        <span
                                            class="form-input-text"
                                            v-if="!property_reporting_default_set_flag"
                                            >Attach Supplier Invoices to Owner Report is OFF</span
                                        >
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </div>
        </div>
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
            v-if="!new_property && page_form_type !== 'admin-approval-page' && page_form_type !== 'client-temp-page'"
            v-on:dblclick="doubleClickForm('MAIN_BOND_DEPOSIT', 'edit_form_bo')"
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Bond/Deposit</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    class="v-step-edit-button"
                    v-if="!edit_form_bo"
                    icon
                    @click="edit_form_bo = true"
                    v-show="isEditable('MAIN_BOND_DEPOSIT') && !pmro_read_only"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable('MAIN_BOND_DEPOSIT') && !pmro_read_only"
                    v-if="edit_form_bo && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form_bo = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="edit_form_bo && !pmro_read_only"
                    class="v-step-save-1-button"
                    icon
                    @click="saveForm()"
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, 'MAIN_BOND_DEPOSIT', is_inactive) &&
                        !pmro_read_only
                    "
                >
                    <v-icon
                        light
                        color="green"
                        >check
                    </v-icon>
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    v-if="isEditable('MAIN_BOND_DEPOSIT')"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="
                        show_activity_log_modal = true;
                        form_sub_section = 'PROPERTY_MAIN_DETAIL_BOND';
                    "
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="
                !loading_setting &&
                !new_property &&
                page_form_type !== 'admin-approval-page' &&
                page_form_type !== 'client-temp-page'
            "
            v-on:dblclick="doubleClickForm('MAIN_BOND_DEPOSIT', 'edit_form_bo')"
        >
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Bond/Deposit Property
                            </td>
                            <td
                                class="required"
                                v-if="edit_form_bo"
                            ></td>
                            <td>
                                <cirrus-single-select-v2
                                    v-if="edit_form_bo"
                                    v-model="property_bond_deposit"
                                    :options="property_bond_list"
                                    ref="refPropertyBond"
                                    trackBy="field_key"
                                    label="field_key_w_value"
                                    return="field_key"
                                    placeholder="Select a property"
                                />
                                <span
                                    v-if="!edit_form_bo && property_bond_deposit !== ''"
                                    class="form-input-text"
                                    >{{ property_bond_deposit }} -
                                    {{ getDropdownName(property_bond_deposit, property_bond_list) }}</span
                                >
                                &nbsp &nbsp
                                <span
                                    v-if="
                                        (typeof property_bond_deposit === 'object' || property_bond_deposit === '') &&
                                        edit_form_bo
                                    "
                                    class="form-input-text"
                                    ><v-btn
                                        x-small
                                        class="mt-1"
                                        depressed
                                        @click="showNewBondProperty()"
                                        >Add Bond/Deposit Property</v-btn
                                    ></span
                                >
                                <span
                                    v-if="typeof property_bond_deposit === 'string' && property_bond_deposit !== ''"
                                    class="form-input-text"
                                >
                                    <a @click="goToBondProperty(property_bond_deposit)">Go to Bond Property</a>
                                </span>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
        </div>

        <v-divider></v-divider>
        <v-card
            elevation="0"
            v-if="ifEitherOfSectionIsEditable()"
        >
            <v-card-actions v-if="ifEitherOfSectionIsEditable()">
                <v-spacer></v-spacer>
                <v-btn
                    class="v-step-save-2-button"
                    @click="saveForm()"
                    color="success"
                    dark
                    small
                >
                    {{ save_button_label }}
                </v-btn>
            </v-card-actions>
        </v-card>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_sub_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_bond_property_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitNewBond()"
        >
            <v-card>
                <v-card-title class="headline">
                    New Bond/Deposit Property
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_bond_property_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="bond_error_server_msg"
                        :errorMsg2="bond_error_server_msg2"
                    ></cirrus-server-error>
                    <!--Lease add-->
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Bond/Deposit Property Bank
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="new_bond_property_bank"
                                    :options="bank_account_list"
                                    :maxHeight="113"
                                    :custom-label="nameWithDash"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-300"
                                    group-label="language"
                                    placeholder="Please select..."
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                >
                                    <span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Bond/Deposit Property Code
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="new_bond_property_code"
                                    size=""
                                    :id="'new_bond_property_code'"
                                    data-inverted=""
                                    :edit_form="true"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Bond/Deposit Property Name
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="new_bond_property_name"
                                    size=""
                                    :id="'new_bond_property_name'"
                                    data-inverted=""
                                    :edit_form="true"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />

                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitNewBond()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="show_bond_property_modal = false"
                    >
                        <v-icon
                            left
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';
import Vue from 'vue';
import suburb_list from '../../../../plugins/australianSuburb.json';
import PropertyOwnerSharesForm from './PropertyOwnerSharesForm.vue';
import moment from 'moment';
import { REPORTING_GST_BASIS, REPORTING_GST_PERIOD } from '../../../../modules/Property/enums';

Vue.component('property-activity-log-component', require('./PropertyActivityLog.vue').default);

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        save_button_default_label: { type: String, default: 'Save details' },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
        country_default_settings: { type: String, default: '' },
    },
    data() {
        return {
            property_manager_label: 'Property Manager',
            trust_account_label: 'Trust Account',
            loading_setting: false,
            edit_form_gen: false,
            edit_form_ba: false,
            edit_form_ow: false,
            edit_form_re: false,
            edit_form_bo: false,
            error_server_msg: {},
            error_server_msg2: [],
            bond_error_server_msg: {},
            bond_error_server_msg2: [],
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_MAIN_DETAILS',
            form_sub_section: '',
            property_name: '',
            check_chargers_flag: '1',
            property_address: '',
            property_image_filename: '',
            property_suburb: '',
            property_post_code: '',
            property_country_old: { field_key: '', field_value: 'Please select...' },
            property_country: { field_key: '', field_value: 'Please select...' },
            property_state: { field_key: '', field_value: 'Please select...' },
            new_bond_property_code: '',
            new_bond_property_name: '',
            new_bond_property_bank: { field_key: '', field_value: 'Please select...' },
            property_next_charge_date: this.getFirstDayOfNextMonth(),
            property_next_charge_date_index: 0,
            property_reporting_gst_status: 'The GST status of the owner will be shown once the new owner is saved',
            property_reporting_report_type: '',
            report_type_list: [],
            suburb_list_filtered: [],
            postcode_list_filtered: [],
            bank_account_list: [],
            property_reporting_gst_basis_index: 0,
            property_reporting_gst_period_index: 0,
            property_reporting_retail_flag: false,
            property_reporting_strata_flag: false,
            property_reporting_default_set_flag: false,
            property_bond_deposit: '',
            property_bond_list: [],
            property_inspection_index: 1,
            property_inspection_amount: '0.00',
            property_inspection_payment_account: { field_key: '', field_value: '' },
            inspection_payment_account_list: [],
            property_inspection_frequency_index: 0,
            property_inspection_recoverable_index: 1,
            property_inspection_recoverable_account: { field_key: '', field_value: '' },
            inspection_recoverable_account_list: [],
            old_property_details: {},
            show_activity_log_modal: false,
            search_suburb: '',
            searchPostcode: '',
            property_bank_account: { field_key: '', field_value: 'Please select...' },
            property_bank_account_code: '',
            property_bank_account_name: '',
            propertyUseEFTOnInvoice: false,
            property_auto_pay_owner_flag: false,
            allow_bank_partition_flag: false,
            bank_partitioning_flag: false,
            property_withhold_owner_amount: '0.000',
            property_withhold_owner_comment: '',
            save_button_label: this.save_button_default_label,
            property_calendar_index: 0,
            property_rent_review_index: 1,
            property_rent_review_amount: '0.00',
            owners_income_closing: '0.00',
            vo_closing: '0.00',
            direct_recoverables_closing: '0.00',
            trial_balance_closing_balance: '0.00',
            trial_balance_closing_balance_main: '0.00',
            fund: [],
            property_rent_review_description: '',
            property_rent_review_expenses_account: { field_key: '', field_value: '' },
            isSelecting: false,
            selectedFile: '',
            property_image: '',
            property_image_file: null,
            default_property_image: this.$assetDomain + 'assets/images/icons/default-property-image-bg-white.png',
            old_property_image: '',
            isRemove: false,
            show_bond_property_modal: false,
            property_manager_list: [],
            man_prop_manager: '',
            man_principal_owner: '',
            man_prop_type: '',
            man_prop_group: '',
            payment_group: '',
            man_lease_type: '',
            principal_owner_list: [],
            property_type_list: [],
            property_group_list: [],
            payment_group_list: [],
            lease_type_list: [],
            man_accounting_basis_index: 0,
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
                tax_label: 'GST',
                business_prefix: '',
            },
            suburb_label: 'Suburb',
            strata_label: 'Strata',
            tax_label: 'GST',
            propertyId: 0,
        };
    },
    components: {
        'property-owner-shares-form-component': PropertyOwnerSharesForm,
    },
    mounted() {
        let country_default_settings = JSON.parse(atob(this.country_default_settings));
        this.property_manager_label = this.ucwords(country_default_settings.property_manager);
        this.suburb_label = country_default_settings.suburb;
        this.trust_account_label = this.ucwords(country_default_settings.trust_account);
        this.strata_label = this.ucwords(country_default_settings.strata);
        this.tax_label = country_default_settings.tax_label;
        this.property_reporting_gst_status =
            'The ' + this.tax_label.toUpperCase() + ' status of the owner will be shown once the new owner is saved';
        this.loadPropertyDetailsLists();
        this.loadManagementDetailsLists();
        if (this.save_button_default_label !== 'Continue to step 2') {
            if (this.new_property) {
                this.save_button_label = 'Create Property';
                this.property_image = this.default_property_image;
            } else {
                this.save_button_label = this.save_button_default_label;
            }
        }
        if (this.new_property) {
            this.edit_form_gen = true;
            this.edit_form_ba = true;
            this.edit_form_ow = true;
            this.edit_form_re = true;
            this.edit_form_bo = true;
            this.loadMainFormDetails(false);
        } else {
            this.loadForm();
            this.company_type = 1;
        }
        this.property_next_charge_date_index = Math.random();
    },
    methods: {
        isEditable: function (form_section = null) {
            if (!form_section) form_section = this.form_section;
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function (form_section, form_key) {
            if (!form_section) form_section = this.form_section;
            if (!this.pmro_read_only) {
                this[form_key] =
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        form_section,
                        this.is_inactive,
                    );
            }
        },
        loadForm: function () {
            this.error_server_msg = {};
            if (this.property_code !== '') {
                this.loadMainFormDetails();
            }
            if (!this.new_property) {
                this.edit_form_gen = false;
                this.edit_form_ba = false;
                this.edit_form_ow = false;
                this.edit_form_re = false;
                this.edit_form_bo = false;
            }
        },
        ifEitherOfSectionIsEditable: function () {
            return (
                this.edit_form_gen || this.edit_form_re || this.edit_form_ow || this.edit_form_bo || this.edit_form_ba
            );
        },
        saveForm: function () {
            this.error_server_msg2 = [];
            let property_name = this.property_name;
            let property_address = this.property_address;
            let property_suburb = this.property_suburb;
            let property_post_code = this.property_post_code;
            let property_state = this.property_state.field_key;
            let property_country_code = this.property_country.field_key;
            let property_next_charge_date = this.property_next_charge_date;
            let property_reporting_gst_status = this.property_reporting_gst_status;
            let property_reporting_report_type = this.property_reporting_report_type;
            let property_reporting_gst_basis_index = this.property_reporting_gst_basis_index;
            let property_reporting_gst_period_index = this.property_reporting_gst_period_index;
            let property_reporting_retail_flag = this.property_reporting_retail_flag;
            let property_reporting_strata_flag = this.property_reporting_strata_flag;
            let property_reporting_default_set_flag = this.property_reporting_default_set_flag;
            let property_bond_deposit = this.property_bond_deposit;
            let property_calendar_index = this.property_calendar_index;
            let property_inspection_index = this.property_inspection_index;
            let property_inspection_amount = this.property_inspection_amount;
            let property_inspection_payment_account = this.property_inspection_payment_account.field_key;
            let property_inspection_frequency_index = this.property_inspection_frequency_index;
            let property_inspection_recoverable_index = this.property_inspection_recoverable_index;
            let property_inspection_recoverable_account = this.property_inspection_recoverable_account.field_key;

            let error_server_msg2 = [];
            if (property_name === '') error_server_msg2.push(['You have not specified a property name.']);
            if (property_address === '') error_server_msg2.push(['You have not specified a property address.']);
            if (property_suburb === '')
                error_server_msg2.push(['You have not specified a property ' + this.suburb_label + '.']);
            if (this.new_property && !this.isMultiplePropertyLedger)
                if (this.property_bank_account.field_key === '') error_server_msg2.push(['Bank account is required.']);

            let property_withhold_owner_amount = this.property_withhold_owner_amount;
            if (property_withhold_owner_amount === '' || !property_withhold_owner_amount) {
            } else {
                if (isNaN(parseFloat(property_withhold_owner_amount))) {
                    error_server_msg2.push(['Please input a valid default amount to withhold from owner.']);
                }
            }

            if (property_inspection_index === 0) {
                if (property_inspection_payment_account === '')
                    error_server_msg2.push(['You have not entered a valid inspection account.']);
                if (property_inspection_amount === '')
                    error_server_msg2.push(['You have not entered a valid inspection amount.']);
                else if (isNaN(parseFloat(property_inspection_amount)))
                    error_server_msg2.push(['You have not entered a valid inspection amount.']);
            }
            let property_rent_review_index = this.property_rent_review_index;
            let property_rent_review_amount = this.property_rent_review_amount;
            let property_rent_review_description = this.property_rent_review_description;
            let property_rent_review_expenses_account = this.property_rent_review_expenses_account.field_key;
            if (property_rent_review_index === 0) {
                if (property_rent_review_expenses_account === '')
                    error_server_msg2.push(['You have not entered a valid rent review amount.']);
                if (property_rent_review_amount === '')
                    error_server_msg2.push(['You have not entered a valid inspection amount.']);
                else if (isNaN(parseFloat(property_rent_review_amount)))
                    error_server_msg2.push(['You have not entered a valid inspection amount.']);
            }
            let property_calendar_code = 'F';
            if (property_calendar_index === 1) property_calendar_code = 'C';
            let man_accounting_basis_index = this.man_accounting_basis_index;
            let man_accounting_basis = 'C';
            if (man_accounting_basis_index === 1) man_accounting_basis = 'A';
            let man_lease_type = this.man_lease_type;
            let man_principal_owner = this.man_principal_owner;
            let man_prop_group = this.man_prop_group;
            let payment_group = this.payment_group;
            let man_prop_manager = this.man_prop_manager;
            let man_prop_type = this.man_prop_type;
            if (man_principal_owner === '') error_server_msg2.push(['You have not specified a principal owner']);
            if (man_prop_manager === '')
                error_server_msg2.push(['You have not specified a ' + this.property_manager_label]);

            if (this.country_defaults.display_state === true) {
                if (!property_state) error_server_msg2.push(['You have not specified a state.']);
                else if (property_state === '') error_server_msg2.push(['You have not specified a state.']);
            }

            if (property_post_code === '' || !property_post_code)
                error_server_msg2.push(['You have not specified a post code.']);
            else {
                if (
                    (property_post_code.length < this.country_defaults.post_code_min_length ||
                        property_post_code.length > this.country_defaults.post_code_length) &&
                    property_post_code != ''
                ) {
                    let post_code_length_val = '4';
                    if (this.country_defaults.post_code_min_length != this.country_defaults.post_code_length) {
                        post_code_length_val =
                            this.country_defaults.post_code_min_length +
                            ' to ' +
                            this.country_defaults.post_code_length;
                    } else {
                        post_code_length_val = this.country_defaults.post_code_length;
                    }

                    var post_code_error_message = 'Post code must be ' + post_code_length_val + ' digits';

                    if (this.country_defaults.country_code == 'GB') {
                        post_code_error_message += '.';
                    } else {
                        post_code_error_message += ' (no spaces).';
                    }

                    error_server_msg2.push([post_code_error_message]);
                }
            }

            this.error_server_msg2 = error_server_msg2;
            if (error_server_msg2.length === 0) {
                this.loading_setting = true;
                let form_data = new FormData();
                form_data.append('propertyId', this.propertyId);
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('property_name', property_name);
                form_data.append('property_address', property_address);
                form_data.append('property_suburb', property_suburb);
                form_data.append('property_post_code', property_post_code);
                form_data.append('property_state', property_state);
                form_data.append('property_country_code', property_country_code);
                form_data.append('property_next_charge_date', property_next_charge_date);
                form_data.append('property_reporting_gst_status', property_reporting_gst_status);
                form_data.append('property_reporting_report_type', property_reporting_report_type);
                form_data.append('property_reporting_gst_basis_index', property_reporting_gst_basis_index);
                form_data.append('property_reporting_gst_period_index', property_reporting_gst_period_index);
                form_data.append('property_reporting_retail_flag', property_reporting_retail_flag);
                form_data.append('property_reporting_strata_flag', property_reporting_strata_flag);
                form_data.append('property_reporting_default_set_flag', property_reporting_default_set_flag);
                form_data.append('property_bond_deposit', property_bond_deposit);
                form_data.append('property_inspection_index', property_inspection_index);
                form_data.append('property_inspection_amount', property_inspection_amount);
                form_data.append('property_inspection_payment_account', property_inspection_payment_account);
                form_data.append('property_inspection_frequency_index', property_inspection_frequency_index);
                form_data.append('property_inspection_recoverable_index', property_inspection_recoverable_index);
                form_data.append('property_inspection_recoverable_account', property_inspection_recoverable_account);
                form_data.append('property_calendar_code', property_calendar_code);
                form_data.append('property_rent_review_index', property_rent_review_index);
                form_data.append('property_rent_review_amount', property_rent_review_amount);
                form_data.append('property_rent_review_description', property_rent_review_description);
                form_data.append('property_rent_review_expenses_account', property_rent_review_expenses_account);

                form_data.append('man_accounting_basis', man_accounting_basis);
                form_data.append('man_lease_type', man_lease_type);
                form_data.append('man_principal_owner', man_principal_owner);
                form_data.append('man_prop_group', man_prop_group);
                form_data.append('payment_group', payment_group);
                form_data.append('man_prop_manager', man_prop_manager);
                form_data.append('man_prop_type', man_prop_type);

                form_data.append('country_defaults', JSON.stringify(this.country_defaults));

                form_data.append('property_bank_account_code', this.property_bank_account.field_key);
                form_data.append('property_use_owner_eft_flag', this.propertyUseEFTOnInvoice);
                form_data.append('property_auto_pay_owner_flag', this.property_auto_pay_owner_flag);
                form_data.append('bank_partitioning_flag', this.bank_partitioning_flag);
                form_data.append('property_withhold_owner_amount', property_withhold_owner_amount);
                form_data.append('property_withhold_owner_comment', this.property_withhold_owner_comment);
                form_data.append(
                    'property_image_file_is_removed',
                    this.property_image === this.default_property_image ? '1' : '0',
                );
                if (this.property_image_file !== null)
                    form_data.append('property_image_file', this.property_image_file);

                form_data.append('accountingBasis', man_accounting_basis);
                form_data.append('isBankPartitioning', this.bank_partitioning_flag);
                form_data.append('isPropertyAutoPayOwner', this.property_auto_pay_owner_flag);
                form_data.append('isPropertyImageFileIsRemoved', this.property_image === this.default_property_image);
                form_data.append('isPropertyReportingDefault', property_reporting_default_set_flag);
                form_data.append('isPropertyReportingRetail', property_reporting_retail_flag);
                form_data.append('isPropertyReportingStrata', property_reporting_strata_flag);
                form_data.append('isPropertyUseOwnerEft', this.propertyUseEFTOnInvoice);
                form_data.append('leaseType', man_lease_type);
                form_data.append('paymentGroup', payment_group);
                form_data.append('propertyManager', man_prop_manager);
                form_data.append('principalOwner', man_principal_owner);
                form_data.append(
                    'propertyReportingGstBasis',
                    this.getReportingBasis(property_reporting_gst_basis_index),
                );
                form_data.append(
                    'propertyReportingGstPeriod',
                    this.getReportingBasisPeriod(property_reporting_gst_period_index),
                );
                form_data.append('accountingBasis', man_accounting_basis);
                form_data.append('leaseType', man_lease_type);
                form_data.append('principalOwner', man_principal_owner);
                form_data.append('propertyGroup', man_prop_group);
                form_data.append('paymentGroup', payment_group);
                form_data.append('propertyManager', man_prop_manager);
                form_data.append('propertyType', man_prop_type);

                let apiUrl = '';
                if (this.isPropertyFormLive()) apiUrl = 'property/update/main-form-details';
                else apiUrl = 'with-file-upload/temp/property/update-or-create/main-form-details';

                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.loading_setting = false;
                        this.error_server_msg2 = response.data.error_server_msg;

                        if (this.error_server_msg2.length <= 0) {
                            this.loadMainFormDetails();
                            if (this.new_property) {
                                this.edit_form_gen = true;
                                this.edit_form_ba = true;
                                this.edit_form_ow = true;
                                this.edit_form_re = true;
                                this.edit_form_bo = true;
                            } else {
                                this.edit_form_gen = false;
                                this.edit_form_ba = false;
                                this.edit_form_ow = false;
                                this.edit_form_re = false;
                                this.edit_form_bo = false;
                            }
                        }
                        if (this.new_property) {
                            if (this.error_server_msg2.length === 0) {
                                this.$emit('returnPropertyCode', {
                                    property_code: this.property_code,
                                    property_name: this.property_name,
                                });

                                this.$emit('returnPropertyIsExisted', {
                                    property_code: this.property_code,
                                    property_name: this.property_name,
                                });
                                this.$emit('returnPropertyFormSuccess', {
                                    property_code: this.property_code,
                                    property_name: this.property_name,
                                });
                            }
                        }

                        if (this.error_server_msg2.length === 0) {
                            bus.$emit('processSaveFormNotes', '');
                        }
                    });
            }
        },
        getReportingBasis: function (index) {
            return index === 0 ? REPORTING_GST_BASIS.CASH : REPORTING_GST_BASIS.ACCRUAL;
        },
        getReportingBasisPeriod: function (index) {
            return index === 0 ? REPORTING_GST_PERIOD.MONTHLY : REPORTING_GST_PERIOD.QUARTERLY;
        },
        loadMainFormDetails: function (enable_loading = true) {
            if (enable_loading) {
                this.loading_setting = true;
            }
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/main-form-details', form_data).then((response) => {
                    this.loadResponseToVariables(response);

                    if (!this.new_property)
                        this.updatePageTitle(response.data.property_code + ' - ' + response.data.property_name);

                    if (this.new_property) {
                        this.edit_form_gen = true;
                        this.edit_form_ba = true;
                        this.edit_form_ow = true;
                        this.edit_form_re = true;
                        this.edit_form_bo = true;
                    } else {
                        this.edit_form_gen = false;
                        this.edit_form_ba = false;
                        this.edit_form_ow = false;
                        this.edit_form_re = false;
                        this.edit_form_bo = false;
                    }
                });
            } else {
                let apiUrl = 'temp/property/fetch/main-form-details';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);

                    if (!this.new_property)
                        this.updatePageTitle(response.data.property_code + ' - ' + response.data.property_name);

                    if (this.new_property) {
                        this.edit_form_gen = true;
                        this.edit_form_ba = true;
                        this.edit_form_ow = true;
                        this.edit_form_re = true;
                        this.edit_form_bo = true;
                    } else {
                        this.edit_form_gen = false;
                        this.edit_form_ba = false;
                        this.edit_form_ow = false;
                        this.edit_form_re = false;
                        this.edit_form_bo = false;
                    }
                });
            }
            // this.loadBankBalance();
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.isRemove = false;

            this.propertyId = response.data.property_id;
            this.old_property_details.property_name = response.data.property_name;
            this.property_name = response.data.property_name;
            this.check_chargers_flag = response.data.check_chargers;
            this.old_property_details.property_address = response.data.property_address;
            this.property_address = response.data.property_address;
            this.property_image_filename = response.data.property_image_filename;

            this.old_property_details.property_suburb = response.data.property_suburb;
            this.property_suburb = response.data.property_suburb;

            this.old_property_details.property_post_code = response.data.property_post_code;
            this.property_post_code = response.data.property_post_code;
            this.property_country = response.data.property_country;
            this.property_country_old = response.data.property_country;

            this.old_property_details.property_state = response.data.property_state;
            this.property_state = response.data.property_state;

            this.suburbFilteredList(this.property_state.field_key);
            this.postcodeFilteredList(this.property_state.field_key);

            if (response.data.property_next_charge_date === '')
                this.property_next_charge_date = this.getFirstDayOfNextMonth();
            else this.property_next_charge_date = response.data.property_next_charge_date;
            this.old_property_details.property_next_charge_date = response.data.property_next_charge_date;
            // console.log(this.property_next_charge_date);

            this.old_property_details.property_reporting_gst_status = response.data.property_reporting_gst_status;
            this.property_reporting_gst_status = response.data.property_reporting_gst_status;

            this.old_property_details.property_reporting_report_type = response.data.property_reporting_report_type;
            this.property_reporting_report_type = response.data.property_reporting_report_type;

            this.old_property_details.property_reporting_gst_basis_index =
                response.data.property_reporting_gst_basis_index;
            this.property_reporting_gst_basis_index = response.data.property_reporting_gst_basis_index;

            this.old_property_details.property_reporting_gst_period_index =
                response.data.property_reporting_gst_period_index;
            this.property_reporting_gst_period_index = response.data.property_reporting_gst_period_index;

            this.old_property_details.property_reporting_retail_flag = response.data.property_reporting_retail_flag;
            this.property_reporting_retail_flag = response.data.property_reporting_retail_flag;

            this.old_property_details.property_reporting_strata_flag = response.data.property_reporting_strata_flag;
            this.property_reporting_strata_flag = response.data.property_reporting_strata_flag;
            this.old_property_details.property_reporting_default_set_flag =
                response.data.property_reporting_default_set_flag;
            this.property_reporting_default_set_flag = response.data.property_reporting_default_set_flag;

            this.old_property_details.property_bond_deposit = response.data.property_bond_deposit;
            this.property_bond_deposit = response.data.property_bond_deposit;

            this.old_property_details.property_inspection_index = response.data.property_inspection_index;
            this.property_inspection_index = response.data.property_inspection_index;

            this.old_property_details.property_inspection_amount = response.data.property_inspection_amount;
            this.property_inspection_amount = response.data.property_inspection_amount;

            this.old_property_details.property_inspection_payment_account =
                response.data.property_inspection_payment_account;
            this.property_inspection_payment_account = response.data.property_inspection_payment_account;

            this.old_property_details.property_inspection_frequency_index =
                response.data.property_inspection_frequency_index;
            this.property_inspection_frequency_index = response.data.property_inspection_frequency_index;

            this.old_property_details.property_inspection_recoverable_index =
                response.data.property_inspection_recoverable_index;
            this.property_inspection_recoverable_index = response.data.property_inspection_recoverable_index;

            this.old_property_details.property_inspection_recoverable_account =
                response.data.property_inspection_recoverable_account;
            this.property_inspection_recoverable_account = response.data.property_inspection_recoverable_account;

            this.property_rent_review_index = response.data.property_rent_review_index;
            if (this.property_rent_review_index === 0) {
                this.property_rent_review_amount = response.data.property_rent_review_amount;
                this.property_rent_review_description = response.data.property_rent_review_description;
                this.property_rent_review_expenses_account = response.data.property_rent_review_expenses_account;
            }
            this.property_calendar_index = response.data.property_calendar_index;

            this.property_bank_account = response.data.property_bank_account;
            this.property_bank_account_code = response.data.property_bank_account_code;
            this.property_bank_account_name = response.data.property_bank_account_name;
            this.property_withhold_owner_amount = response.data.property_withhold_owner_amount;
            this.property_withhold_owner_comment = response.data.property_withhold_owner_comment;
            this.propertyUseEFTOnInvoice = response.data.property_use_owner_eft_flag;
            this.property_auto_pay_owner_flag = response.data.property_auto_pay_owner_flag;
            this.bank_partitioning_flag = response.data.bank_partitioning_flag;
            if (response.data.property_image_path) {
                this.property_image = response.data.property_image_path;
                this.isRemove = true;
            } else this.property_image = this.default_property_image;

            let lc_man_accounting_basis = response.data.man_accounting_basis;
            let lc_man_lease_type = response.data.man_lease_type;
            let lc_man_principal_owner = response.data.man_principal_owner;
            let lc_man_prop_group = response.data.man_prop_group;
            let lc_payment_group = response.data.payment_group;
            let lc_man_prop_manager = response.data.man_prop_manager;
            let lc_man_prop_type = response.data.man_prop_type;

            if (lc_man_accounting_basis === 'A') {
                this.man_accounting_basis_index = 1;
            } else {
                this.man_accounting_basis_index = 0;
            }

            this.man_lease_type = lc_man_lease_type;
            this.man_principal_owner = lc_man_principal_owner;
            this.man_prop_group = lc_man_prop_group;
            this.payment_group = lc_payment_group;
            this.man_prop_manager = lc_man_prop_manager;
            this.man_prop_type = lc_man_prop_type;

            // this.loadCountryDefaults(response.data.country_code);
        },
        goToBondProperty: function (property_code) {
            if (this.page_form_type === 'admin-manage-page') {
                window.open(
                    '?module=properties&command=v2_manage_property_page&property_code=' + property_code,
                    '_blank', // <- This is what makes it open in a new window.
                );
            } else {
                window.open(
                    '?module=properties&command=property_summary_page&property_code=' + property_code,
                    '_blank', // <- This is what makes it open in a new window.
                );
            }
        },
        loadPropertyDetailsLists: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            this.$api.post('property/fetch/property-details-lists', form_data).then((response) => {
                this.bank_account_list = response.data.bank_account_list;
                this.report_type_list = response.data.report_type_list;
                this.property_bond_list = response.data.property_bond_list;

                this.inspection_payment_account_list = response.data.inspection_payment_account_list;
                this.inspection_recoverable_account_list = response.data.inspection_recoverable_account_list;
            });
        },
        suburbSelected(data) {
            // property_state
            if (this.$refs.refSuburb.selectedValues.length == 0) {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_post_code = data.pcode;
                    let state = data.State;
                    // this.property_state = this.getValueInList(state, this.getDDCountryStates("AU"));
                } else {
                    this.property_suburb = this.property_suburb;
                }
            } else {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_post_code = data.pcode;
                    let state = data.State;
                    // this.property_state = this.getValueInList(state, this.getDDCountryStates("AU"));
                }
            }
        },
        postcodeSelected(data) {
            // property_state
            if (this.$refs.refSuburb.selectedValues.length == 0) {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_post_code = data.pcode;
                    let state = data.State;
                    // this.property_state = this.getValueInList(state, this.getDDCountryStates("AU"));
                } else {
                    this.property_suburb = this.property_suburb;
                }
            } else {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_post_code = data.pcode;
                    let state = data.State;
                    // this.property_state = this.getValueInList(state, this.getDDCountryStates("AU"));
                }
            }
        },
        suburbFilteredList(stateVal) {
            this.suburb_list_filtered = [];
            let filteredItem = [];

            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.suburb_list_filtered = filteredItem;
        },
        postcodeFilteredList(stateVal) {
            this.postcode_list_filtered = [];
            let filteredItem = [];
            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.postcode_list_filtered = filteredItem;
        },
        propertyStateChanged: function () {
            this.suburbFilteredList(this.property_state.field_key);
            this.postcodeFilteredList(this.property_state.field_key);
        },
        onButtonClick() {
            this.isSelecting = true;
            window.addEventListener(
                'focus',
                () => {
                    this.isSelecting = false;
                },
                { once: true },
            );

            this.$refs.uploader.click();
        },
        onFileChanged(e) {
            var files = e.target.files || e.dataTransfer.files;
            if (
                files[0].type == 'image/png' ||
                files[0].type == 'image/jpg' ||
                files[0].type == 'image/jpeg' ||
                files[0].type == 'image/gif'
            ) {
                if (!files.length) return;
                this.createImage(files[0]);
            } else {
                this.$noty.error('Invalid file type. Please upload only image file.');
            }
        },
        createImage(file) {
            var image = new Image();
            var reader = new FileReader();
            this.old_property_image = this.property_image;
            this.property_image_file = file;
            reader.onload = (e) => {
                this.property_image = e.target.result;
            };
            reader.readAsDataURL(file);
            this.isRemove = true;
        },
        removeImage: function (item) {
            this.property_image = this.default_property_image;
            this.property_image_file = null;
            this.isRemove = false;
        },
        loadManagementDetailsLists: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('app_origin', 'property_page');
            form_data.append('version_id', this.version_id);
            let pm_new_flag = '0';
            if (!this.isPropertyFormLive()) pm_new_flag = '1';
            form_data.append('pm_new_flag', pm_new_flag);
            form_data.append('no_load', true);
            this.$api.post('property/fetch/management-details-lists', form_data).then((response) => {
                this.lease_type_list = response.data.lease_type_list;
                this.principal_owner_list = response.data.principal_owner_list;
                this.property_agent_list = response.data.property_agent_list;
                this.property_group_list = response.data.property_group_list;
                this.payment_group_list = response.data.payment_group_list;
                this.property_manager_list = response.data.property_manager_list;
                this.property_type_list = response.data.property_type_list;
                this.remittance_office_list = response.data.remittance_office_list;

                //Update Strata Label display in dropdown options
                let strata_label_value = this.strata_label;
                this.property_type_list.forEach(function (type) {
                    if (type.fieldKey === 'STRATA') {
                        type.fieldValue = strata_label_value;
                        type.field_value = strata_label_value;
                        type.label = strata_label_value;
                        type.field_key_w_value = 'STRATA ' + strata_label_value;
                    }
                });

                //Reorder dropdown items
                this.property_type_list.sort((a, b) => a.label.localeCompare(b.label));
            });
        },
        showNewBondProperty: function () {
            this.new_bond_property_code = 'ZB' + this.property_code.substring(0, 8);
            this.new_bond_property_name = 'Bond - ' + this.property_name;
            if (this.bank_account_list.length === 1) this.new_bond_property_bank = this.bank_account_list[0];
            this.show_bond_property_modal = true;
        },
        modalSubmitNewBond: function () {
            //
            this.loading_page_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('new_bond_property_code', this.new_bond_property_code.substring(0, 10));
            form_data.append('new_bond_property_name', this.new_bond_property_name);
            form_data.append('new_bond_property_bank', this.new_bond_property_bank.field_key);
            form_data.append('version_id', this.version_id);
            form_data.append('app_origin', 'property_page');
            let api_url = 'temp/property/create/bond-new-property-code';
            if (this.isLeaseFormLive()) {
                api_url = 'property/create/bond-new-property-code';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.loading_page_setting = false;
                this.show_bond_lease_modal = false;
                let error_server_msg2 = response.data.error_server_msg;
                this.bond_error_server_msg2 = error_server_msg2;
                if (response.data.status === 'Success') {
                    this.property_bond_deposit = this.new_bond_property_code.substring(0, 10);
                    this.loadPropertyDetailsLists();
                    this.show_bond_property_modal = false;
                }
            });
        },
        loadBankBalance: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            this.$api.post('property/fetch/bank-balances', form_data).then((response) => {
                this.owners_income_closing = response.data.owners_income_closing;
                this.vo_closing = response.data.vo_closing;
                this.direct_recoverables_closing = response.data.direct_recoverables_closing;
                this.trial_balance_closing_balance = response.data.trial_balance_closing_balance;
                this.trial_balance_closing_balance_main = response.data.trial_balance_closing_balance_main;
                this.fund = response.data.fund;
            });
        },
        async deleteProperty() {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.lease_status_index = 1;
                let form_data = new FormData();
                form_data.append('page_source', 'propertyMainForm');
                form_data.append('property_code', this.property_code);
                form_data.append('no_load', true);
                this.$api.post('property/delete/property-main', form_data).then((response) => {
                    let error_server_msg2 = response.data.error_server_msg;
                    this.error_server_msg2 = error_server_msg2;
                    if (error_server_msg2.length === 0) {
                        bus.$emit('clearPropertyDropdown', '');
                    }
                });
            }
        },
        loadCountryDefaults: function (country) {
            this.loading_page_setting = true;

            var form_data = new FormData();
            if (country) form_data.append('country', country);

            let api_url = this.cirrus8_api_url + 'admin/country_defaults/load';
            this.$api.post(api_url, form_data).then((response) => {
                this.error_server_msg2 = response.data.validation_errors;
                this.loading_page_setting = false;
                this.country_defaults = response.data.default;
                this.getDDCountryStates(country);
            });
        },
        getFirstDayOfNextMonth: function () {
            let current = new Date();
            this.property_next_charge_date_index = Math.random();
            return moment(
                moment(new Date(current.getFullYear(), current.getMonth(), 1))
                    .add(1, 'months')
                    .startOf('month'),
            ).format('DD/MM/YYYY');
        },
        getLengthOfObjectOrArray: function (variable) {
            if (Array.isArray(variable)) {
                // handle as array
                return variable.length;
            } else if (typeof variable === 'object') {
                // handle as object
                return Object.keys(variable).length;
            }
            return 0;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form_gen: function () {
            bus.$emit('changeEditMode', this.edit_form_gen);
        },
        property_suburb: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.search_suburb;
                this.property_suburb = newVal;
            }
        },
        property_post_code: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchPostcode;
                this.property_post_code = newVal;
            }
        },
        property_country: function () {
            // this.loadState(this.lease_tenant_country.value);
            if (this.property_country.field_key !== this.property_country_old.field_key) {
                this.property_suburb = '';
                this.property_post_code = '';
                this.property_state = { field_key: '', field_value: 'Please select...' };
                this.suburbFilteredList(this.property_state.field_key);
                this.postcodeFilteredList(this.property_state.field_key);
            }
            this.loadCountryDefaults(this.property_country.field_key);
        },
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'dd_country_list',
            'cirrus8_api_url',
            'sys_ver_control_list',
            'pm_property_form_read_only',
        ]),
        ...mapGetters(['getDDCountryStates']),
        isMultiplePropertyLedger() {
            return !!this.sys_ver_control_list?.isMultiplePropertyLedger;
        },
    },
    created() {
        bus.$on('loadPropertyMainFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) {
                this.edit_form_gen = false;
                this.edit_form_ba = false;
                this.edit_form_ow = false;
                this.edit_form_re = false;
                this.edit_form_bo = false;
            }
        });
        bus.$on('triggerDoubleClick', (data) => {
            this.doubleClickForm('MAIN_GENERAL_DETAILS', 'edit_form_gen');
        });
    },
    mixins: [global_mixins],
};
</script>
