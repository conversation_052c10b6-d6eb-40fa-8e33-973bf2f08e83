<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Floor(s) and Unit(s)</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="floor_search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    icon
                    @click="modalAddFloorData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable()"
                    class="v-step-refresh-button"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>

        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="floors_and_units_list.length === 0 && !loading_setting"
            v-show="isEditable()"
        >
            <v-btn
                v-if="!pmro_read_only"
                depressed
                small
                color="success"
                @click="modalAddFloorData()"
                >Add Floor</v-btn
            >
            <div
                style="margin: 10px 0px"
                v-else
            >
                No floors at the moment
            </div>
        </v-col>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="floors_and_units_list.length > 0"
                    dense
                    item-key="floor_id"
                    :headers="floor_headers"
                    :items="floors_and_units_list"
                    :items-per-page="floor_items_per_page"
                    hide-default-footer
                    :page.sync="floor_page"
                    :total-visible="7"
                    @page-count="floor_page_count = $event"
                    :search="floor_search_datatable"
                    :single-expand="!isPropertyInPrintView()"
                    :expanded.sync="expanded"
                    show-expand
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ floors_and_units_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>

                    <template v-slot:item.floor_unit_contents="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.floor_name }}</span>
                        </div>
                    </template>
                    <template v-slot:item.floor_common_area="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                >{{ numberWithCommas(roundTo(item.floor_common_area, 2)) }}
                                <span v-html="area_unit"></span
                            ></span>
                        </div>
                    </template>
                    <template v-slot:item.floor_total_area="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                >{{ numberWithCommas(roundTo(item.floor_total_area, 2)) }}
                                <span v-html="area_unit"></span
                            ></span>
                        </div>
                    </template>
                    <template v-slot:item.floor_vacant_area="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                >{{ numberWithCommas(roundTo(item.floor_vacant_area, 2)) }}
                                <span v-html="area_unit"></span
                            ></span>
                        </div>
                    </template>
                    <template v-slot:item.floor_sequence="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">
                                <span v-if="!edit_form">{{ item.floor_sequence }}</span>
                                <v-icon
                                    :color="item.floor_sequence === '1' ? 'gnoteHeadingreen' : 'green'"
                                    class="rotate90"
                                    v-if="edit_form"
                                    @click="setOrder('sequence-first', floors_and_units_list.indexOf(item))"
                                    >fast_rewind</v-icon
                                >
                                <v-icon
                                    :color="item.floor_sequence === '1' ? 'gnoteHeadingreen' : 'green'"
                                    class="rotate90"
                                    v-if="edit_form"
                                    @click="setOrder('sequence-up', floors_and_units_list.indexOf(item))"
                                    >arrow_left</v-icon
                                >
                                <v-icon
                                    :color="
                                        parseInt(item.floor_sequence) === floors_and_units_list.length
                                            ? 'gnoteHeadingreen'
                                            : 'green'
                                    "
                                    class="rotate90"
                                    v-if="edit_form"
                                    @click="setOrder('sequence-down', floors_and_units_list.indexOf(item))"
                                    >arrow_right</v-icon
                                >
                                <v-icon
                                    :color="
                                        parseInt(item.floor_sequence) === floors_and_units_list.length
                                            ? 'gnoteHeadingreen'
                                            : 'green'
                                    "
                                    class="rotate90"
                                    v-if="edit_form"
                                    @click="setOrder('sequence-last', floors_and_units_list.indexOf(item))"
                                    >fast_forward</v-icon
                                >
                            </span>
                        </div>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenFloorAED(floors_and_units_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit
                        </v-icon>
                        <v-icon
                            color="red"
                            v-if="edit_form"
                            @click="deleteFloor(floors_and_units_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>

                    <template v-slot:expanded-item="{ headers, item }">
                        <td :colspan="floor_headers.length">
                            <div style="float: left">
                                <div class="tree-vertical-line"></div>
                                <div class="tree-horizontal-line"></div>
                            </div>
                            <div style="padding-left: 35px">
                                <v-card
                                    class="section-toolbar subHeader"
                                    dark
                                    text
                                    tile
                                >
                                    <v-card-actions>
                                        <h6 class="title font-weight-black">Unit(s) for Floor {{ item.floor_name }}</h6>
                                        <v-spacer></v-spacer>
                                        <v-btn
                                            x-small
                                            v-show="isEditable()"
                                            v-if="edit_form"
                                            color="normal"
                                            light
                                            depressed
                                            @click="modalAddUnitData(item.floor_name)"
                                        >
                                            Add unit
                                        </v-btn>
                                    </v-card-actions>
                                </v-card>
                                <v-col
                                    class="text-center"
                                    v-if="item.floor_unit_list.length === 0 && !loading_setting"
                                    v-show="isEditable()"
                                >
                                    <v-btn
                                        v-if="!pmro_read_only"
                                        depressed
                                        small
                                        color="success"
                                        @click="modalAddUnitData(item.floor_name)"
                                        >Add Unit</v-btn
                                    >
                                    <div
                                        style="margin: 10px 0px"
                                        v-else
                                    >
                                        No units at the moment
                                    </div>
                                </v-col>
                                <sui-table
                                    v-show="item.floor_unit_list.length > 0"
                                    class="vue-data-grid"
                                    stackable
                                    width="100%"
                                    border="0"
                                >
                                    <sui-table-header>
                                        <sui-table-row class="fieldDescription">
                                            <sui-table-header-cell style="width: 40px">#</sui-table-header-cell>
                                            <sui-table-header-cell>Unit Code</sui-table-header-cell>
                                            <sui-table-header-cell>Description</sui-table-header-cell>
                                            <sui-table-header-cell class="text-center"
                                                >Start Date</sui-table-header-cell
                                            >
                                            <sui-table-header-cell class="text-center">End Date</sui-table-header-cell>
                                            <sui-table-header-cell class="text-center"
                                                >Area(<span v-html="area_unit"></span>)</sui-table-header-cell
                                            >
                                            <sui-table-header-cell class="text-center"
                                                >% of NLA(<span v-html="area_unit"></span>)</sui-table-header-cell
                                            >
                                            <sui-table-header-cell
                                                class="text-center"
                                                v-if="residential_flag"
                                                >Bedroom(s)</sui-table-header-cell
                                            >
                                            <sui-table-header-cell
                                                class="text-center"
                                                v-if="residential_flag"
                                                >Bathroom(s)</sui-table-header-cell
                                            >
                                            <sui-table-header-cell
                                                class="text-center"
                                                v-if="residential_flag"
                                                >Car Park(s)</sui-table-header-cell
                                            >
                                            <sui-table-header-cell class="text-center">Status</sui-table-header-cell>
                                            <sui-table-header-cell>Tenant</sui-table-header-cell>
                                            <sui-table-header-cell class="text-center"
                                                >Don't show in Tenancy Schedule</sui-table-header-cell
                                            >
                                            <sui-table-header-cell
                                                class="text-right"
                                                v-show="!isPropertyInPrintView()"
                                            ></sui-table-header-cell>
                                        </sui-table-row>
                                    </sui-table-header>
                                    <sui-table-body class="page-form">
                                        <sui-table-row
                                            class="form-row"
                                            v-for="(floor_unit_data, floor_unit_index) in item.floor_unit_list"
                                            :key="floor_unit_index"
                                        >
                                            <sui-table-cell style="width: 40px">
                                                <span class="form-input-text"
                                                    >{{ floors_and_units_list.indexOf(item) + 1 }}.{{
                                                        floor_unit_index + 1
                                                    }}</span
                                                >
                                            </sui-table-cell>
                                            <sui-table-cell class="text-left">{{
                                                floor_unit_data.unit_code
                                            }}</sui-table-cell>
                                            <sui-table-cell class="text-left">{{
                                                floor_unit_data.unit_description
                                            }}</sui-table-cell>
                                            <sui-table-cell class="text-center">
                                                <span v-if="default_start_date === floor_unit_data.unit_area_start_date"
                                                    >--</span
                                                >
                                                {{ floor_unit_data.unit_area_start_date }}
                                                <span v-if="default_start_date === floor_unit_data.unit_area_start_date"
                                                    >--</span
                                                >
                                            </sui-table-cell>
                                            <sui-table-cell class="text-center">{{
                                                floor_unit_data.unit_area_end_date
                                            }}</sui-table-cell>
                                            <sui-table-cell class="text-center">{{
                                                floor_unit_data.unit_area
                                            }}</sui-table-cell>
                                            <sui-table-cell class="text-center">
                                                {{
                                                    item.floor_total_area <= 0
                                                        ? floor_unit_data.unit_area / 1
                                                        : numberWithCommas(
                                                              roundTo(
                                                                  (parseFloat(floor_unit_data.unit_area) /
                                                                      parseFloat(item.floor_total_area)) *
                                                                      100,
                                                                  2,
                                                              ),
                                                          )
                                                }}
                                            </sui-table-cell>
                                            <sui-table-cell
                                                class="text-center"
                                                v-if="residential_flag"
                                                >{{ floor_unit_data.unit_bedroom }}</sui-table-cell
                                            >
                                            <sui-table-cell
                                                class="text-center"
                                                v-if="residential_flag"
                                                >{{ floor_unit_data.unit_bathroom }}</sui-table-cell
                                            >
                                            <sui-table-cell
                                                class="text-center"
                                                v-if="residential_flag"
                                                >{{ floor_unit_data.unit_car_park }}</sui-table-cell
                                            >
                                            <sui-table-cell class="text-center">{{
                                                floor_unit_data.unit_area_status_description
                                            }}</sui-table-cell>
                                            <sui-table-cell
                                                ><a
                                                    @click="
                                                        goToLeaseShortcut(property_code, floor_unit_data.lease_code)
                                                    "
                                                    >{{ floor_unit_data.lease_name }}</a
                                                ></sui-table-cell
                                            >
                                            <sui-table-cell class="text-center">
                                                <span
                                                    class="form-input-text"
                                                    v-if="floor_unit_data.unit_tenancy_flag"
                                                >
                                                    Yes
                                                </span>
                                                <span
                                                    class="form-input-text"
                                                    v-if="!floor_unit_data.unit_tenancy_flag"
                                                >
                                                    No
                                                </span>
                                            </sui-table-cell>
                                            <sui-table-cell
                                                class="text-center"
                                                v-show="!isPropertyInPrintView()"
                                            >
                                                <v-btn
                                                    v-if="!edit_form && !new_property"
                                                    x-small
                                                    @click="
                                                        modalOpenHistory(
                                                            floors_and_units_list.indexOf(item),
                                                            floor_unit_index,
                                                        )
                                                    "
                                                    >View History</v-btn
                                                >
                                                <v-btn
                                                    v-if="edit_form && !new_property"
                                                    x-small
                                                    @click="
                                                        modalOpenHistory(
                                                            floors_and_units_list.indexOf(item),
                                                            floor_unit_index,
                                                        )
                                                    "
                                                    >Edit History</v-btn
                                                >
                                                <v-icon
                                                    small
                                                    @click="
                                                        modalOpenUnitAED(
                                                            floors_and_units_list.indexOf(item),
                                                            floor_unit_index,
                                                        )
                                                    "
                                                    v-if="edit_form"
                                                    >fas fa-edit
                                                </v-icon>
                                                <v-icon
                                                    color="red"
                                                    v-if="edit_form"
                                                    @click="
                                                        deleteUnit(
                                                            floors_and_units_list.indexOf(item),
                                                            floor_unit_index,
                                                        )
                                                    "
                                                    >close
                                                </v-icon>
                                            </sui-table-cell>
                                        </sui-table-row>
                                    </sui-table-body>
                                </sui-table>
                            </div>
                        </td>
                    </template>

                    <template slot="body.append">
                        <tr style="background: #4caf50; color: white">
                            <th></th>
                            <th><strong>Overall Total</strong></th>
                            <th></th>
                            <th></th>
                            <th class="text-right">
                                <strong
                                    >{{ numberWithCommas(roundTo(total_floor_area, 2)) }}
                                    <span v-html="area_unit"></span
                                ></strong>
                            </th>
                            <th class="text-right">
                                <strong
                                    >{{ numberWithCommas(roundTo(total_vacant_area, 2)) }}
                                    <span v-html="area_unit"></span
                                ></strong>
                            </th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="floors_and_units_list.length > 10"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="floor_items_per_page"
                                        :options="[10, 20, 100]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="floor_page"
                                        :length="floor_page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_floor_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalFloorPrevData()"
            @keydown.ctrl.right="modalFloorNextData()"
            @keydown.ctrl.shift.enter="modalAddFloorData()"
            @keydown.ctrl.enter="modalFloorSubmitData()"
            @keydown.ctrl.delete="deleteFloor(floors_and_units_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Floor Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_floor_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="floors_and_units_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        floors_and_units_arr.index === 'New'
                                            ? floors_and_units_arr.index
                                            : floors_and_units_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Floor Name:</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                    id="multiselect_owner"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="floors_and_units_arr.floor_name"
                                        size=""
                                        :id="'floor_name' + floors_and_units_arr.index"
                                        data-inverted=""
                                        :maxlength="10"
                                        :data-tooltip="edit_form ? 'Floor Name' : false"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Common Area:</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="floors_and_units_arr.floor_common_area"
                                        size=""
                                        :id="'floor_common_area' + floors_and_units_arr.index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Common Area' : false"
                                        :edit_form="true"
                                    ></cirrus-input>
                                    <span v-html="area_unit"></span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Total Area</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text"
                                        >{{ floors_and_units_arr.floor_total_area }} <span v-html="area_unit"></span
                                    ></span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Vacant Area</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text"
                                        >{{ floors_and_units_arr.floor_vacant_area }} <span v-html="area_unit"></span
                                    ></span>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalFloorPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddFloorData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="floors_and_units_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalFloorSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddFloorData()"
                        v-if="floors_and_units_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteFloor(floors_and_units_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="floors_and_units_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalFloorNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->
        <!--   AED modal      -->
        <v-dialog
            v-model="AED_unit_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalUnitPrevData()"
            @keydown.ctrl.right="modalUnitNextData()"
            @keydown.ctrl.shift.enter="modalAddUnitData(floor_unit_arr.floor_name)"
            @keydown.ctrl.enter="modalUnitSubmitData()"
            @keydown.ctrl.delete="deleteUnit(floor_unit_arr.floor_index, floor_unit_arr.unit_index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Unit Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_unit_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="floor_unit_arr.floor_index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        {{
                                            floor_unit_arr.floor_index === 'New'
                                                ? floor_unit_arr.floor_index
                                                : floor_unit_arr.floor_index + 1
                                        }}.{{
                                            floor_unit_arr.unit_index === 'New'
                                                ? floor_unit_arr.unit_index
                                                : floor_unit_arr.unit_index + 1
                                        }}
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Unit Code:</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="floor_unit_arr.unit_code"
                                        size=""
                                        :id="'unit_code' + floor_unit_arr.unit_index"
                                        data-inverted=""
                                        :maxlength="10"
                                        :data-tooltip="edit_form ? 'Unit Code' : false"
                                        :edit_form="edit_form || floor_unit_arr.unit_index === 'New'"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Description:</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="floor_unit_arr.unit_description"
                                        size=""
                                        :id="'unit_description' + floor_unit_arr.unit_index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Description' : false"
                                        :edit_form="edit_form || floor_unit_arr.unit_index === 'New'"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Area(<span v-html="area_unit"></span>):</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="numberOnly"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="floor_unit_arr.unit_area"
                                        size=""
                                        :id="'unit_area' + floor_unit_arr.unit_index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Area' : false"
                                        :edit_form="floor_unit_arr.unit_index === 'New'"
                                    ></cirrus-input>
                                    <span v-html="area_unit"></span>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="residential_flag"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Bedroom(s):</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="floor_unit_arr.unit_bedroom"
                                        size=""
                                        :id="'unit_bedroom' + floor_unit_arr.unit_index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Bedroom' : false"
                                        :edit_form="edit_form || floor_unit_arr.unit_index === 'New'"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="residential_flag"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Bathroom(s):</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="floor_unit_arr.unit_bathroom"
                                        size=""
                                        :id="'unit_bathroom' + floor_unit_arr.unit_index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Bathroom' : false"
                                        :edit_form="edit_form || floor_unit_arr.unit_index === 'New'"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="residential_flag"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Car Park(s):</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="floor_unit_arr.unit_car_park"
                                        size=""
                                        :id="'unit_car_park' + floor_unit_arr.unit_index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Car Park' : false"
                                        :edit_form="edit_form || floor_unit_arr.unit_index === 'New'"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="floor_unit_arr.unit_index === 'New'"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Status</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <multiselect
                                            data-inverted=""
                                            openDirection="bottom"
                                            v-model="floor_unit_arr.unit_area_status"
                                            :options="info_unit_status_list"
                                            :allowEmpty="false"
                                            class="vue-select2 dropdown-left dropdown-400"
                                            placeholder="Select a status"
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <v-checkbox
                                            v-model="floor_unit_arr.unit_tenancy_flag"
                                            color="primary"
                                            label="Don't show in Tenancy Schedule"
                                        />
                                    </span>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalUnitPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddUnitData(floor_unit_arr.floor_name)"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="floor_unit_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalUnitSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddUnitData(floor_unit_arr.floor_name)"
                        v-if="floor_unit_arr.unit_index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteUnit(floor_unit_arr.floor_index, floor_unit_arr.unit_index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="floor_unit_arr.unit_index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalUnitNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->

        <!--    -->
        <v-dialog
            v-model="AED_unit_history_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Unit History
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_unit_history_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        The unit <strong>{{ floor_unit_history_arr.unit_code }}</strong> has been saved.
                    </v-alert>
                    <div>
                        <v-chip
                            class="ma-2"
                            color="primary"
                            label
                        >
                            Unit Name: {{ floor_unit_history_arr.unit_code }}
                        </v-chip>
                        <v-chip
                            class="ma-2"
                            color="primary"
                            label
                        >
                            Unit Description: {{ floor_unit_history_arr.unit_description }}
                        </v-chip>
                        <cirrus-content-loader v-if="loading_history"></cirrus-content-loader>
                        <div
                            class="page-form"
                            v-if="!loading_history"
                        >
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                >
                                    <v-simple-table
                                        fixed-header
                                        dense
                                        height="600px"
                                    >
                                        <template v-slot:default>
                                            <thead>
                                                <tr>
                                                    <th
                                                        class="text-left"
                                                        style="width: 60px"
                                                    >
                                                        #
                                                    </th>
                                                    <th class="text-center">Effective Start Date</th>
                                                    <th class="text-center">End Date</th>
                                                    <th class="text-center">Area</th>
                                                    <th class="text-left">Status</th>
                                                    <th class="text-left">Lease</th>
                                                    <th class="text-right"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    v-for="(
                                                        unit_history_data, unit_history_index
                                                    ) in floor_unit_history_arr.unit_history_list"
                                                    :key="unit_history_index"
                                                >
                                                    <td class="text-left">
                                                        <span class="form-input-text">{{
                                                            unit_history_index + 1
                                                        }}</span>
                                                        <span
                                                            class="form-input-text"
                                                            v-if="unit_history_data.continuity_check_failed"
                                                        >
                                                            <v-icon
                                                                small
                                                                color="warning"
                                                                >warning</v-icon
                                                            >
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        <cirrus-icon-date-picker
                                                            :size="'40'"
                                                            :id="'unit_history_data.' + unit_history_index"
                                                            v-model="unit_history_data.unit_area_start_date"
                                                            :edit_form="edit_form"
                                                            :key="
                                                                unit_history_index +
                                                                '_' +
                                                                unit_history_data.unit_area_start_date
                                                            "
                                                        ></cirrus-icon-date-picker>
                                                    </td>
                                                    <td class="text-center">
                                                        <cirrus-icon-date-picker
                                                            :size="'40'"
                                                            :id="
                                                                'unit_history_data.unit_area_end_date' +
                                                                unit_history_index
                                                            "
                                                            v-model="unit_history_data.unit_area_end_date"
                                                            :edit_form="edit_form"
                                                            :key="
                                                                unit_history_index +
                                                                '_' +
                                                                unit_history_data.unit_area_end_date
                                                            "
                                                        ></cirrus-icon-date-picker>
                                                    </td>
                                                    <td class="text-center">
                                                        <cirrus-input
                                                            inputFormat="numberOnly"
                                                            custom_class="cirrus-input-table-textbox"
                                                            v-model="unit_history_data.unit_area"
                                                            size=""
                                                            :id="'unit_area' + unit_history_index"
                                                            data-inverted=""
                                                            :edit_form="edit_form"
                                                        ></cirrus-input>
                                                        <span v-html="area_unit"></span>
                                                    </td>
                                                    <td class="text-left">
                                                        <multiselect
                                                            data-inverted=""
                                                            v-model="unit_history_data.unit_area_status"
                                                            :options="unit_status_list"
                                                            :allowEmpty="false"
                                                            :optionHeight="10"
                                                            openDirection="bottom"
                                                            class="vue-select2 dropdown-left dropdown-150"
                                                            placeholder="Select a status"
                                                            track-by="field_key"
                                                            label="field_value"
                                                            :show-labels="false"
                                                            v-if="edit_form"
                                                            ><span slot="noResult"
                                                                >Oops! No elements found. Consider changing the search
                                                                query.</span
                                                            >
                                                        </multiselect>
                                                        <span v-if="!edit_form">{{
                                                            unit_history_data.unit_area_status.field_value
                                                        }}</span>
                                                    </td>
                                                    <td class="text-left">
                                                        <multiselect
                                                            data-inverted=""
                                                            v-model="unit_history_data.unit_area_lease"
                                                            :options="lease_list"
                                                            :allowEmpty="false"
                                                            :optionHeight="10"
                                                            openDirection="bottom"
                                                            class="vue-select2 dropdown-left dropdown-150"
                                                            placeholder="Select a lease"
                                                            track-by="field_key"
                                                            label="field_key"
                                                            :show-labels="false"
                                                            v-if="
                                                                edit_form &&
                                                                unit_history_data.unit_area_status.field_key !== 'I' &&
                                                                unit_history_data.unit_area_status.field_key !== 'V'
                                                            "
                                                        >
                                                            <span slot="noResult"
                                                                >Oops! No elements found. Consider changing the search
                                                                query.</span
                                                            >
                                                        </multiselect>
                                                        <span
                                                            v-if="
                                                                !edit_form &&
                                                                unit_history_data.unit_area_lease.field_key !== ''
                                                            "
                                                            ><a
                                                                @click="
                                                                    goToLeaseShortcut(
                                                                        property_code,
                                                                        unit_history_data.unit_area_lease.field_key,
                                                                    )
                                                                "
                                                                >{{ unit_history_data.unit_area_lease.field_value }}</a
                                                            ></span
                                                        >
                                                    </td>
                                                    <td class="text-right">
                                                        <v-btn
                                                            v-if="edit_form"
                                                            color="success"
                                                            x-small
                                                            @click="updateUnitHistory(unit_history_index)"
                                                            >Save</v-btn
                                                        >
                                                        <v-icon
                                                            color="red"
                                                            v-if="edit_form"
                                                            @click="deleteUnitHistory(unit_history_index)"
                                                            >close
                                                        </v-icon>
                                                    </td>
                                                </tr>
                                                <tr
                                                    v-if="edit_form"
                                                    class="highlight"
                                                >
                                                    <td class="text-left">
                                                        <span class="form-input-text">New</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <cirrus-icon-date-picker
                                                            :size="'40'"
                                                            :id="'new_unit_area_start_date'"
                                                            v-model="new_unit_history_data.unit_area_start_date"
                                                            :edit_form="edit_form"
                                                        ></cirrus-icon-date-picker>
                                                    </td>
                                                    <td class="text-center">
                                                        <cirrus-icon-date-picker
                                                            :size="'40'"
                                                            :id="'new_unit_area_end_date'"
                                                            v-model="new_unit_history_data.unit_area_end_date"
                                                            :edit_form="edit_form"
                                                        ></cirrus-icon-date-picker>
                                                    </td>
                                                    <td class="text-center">
                                                        <cirrus-input
                                                            inputFormat="numberOnly"
                                                            custom_class="cirrus-input-table-textbox"
                                                            v-model="new_unit_history_data.unit_area"
                                                            size=""
                                                            :id="'new_unit_area'"
                                                            data-inverted=""
                                                            :edit_form="edit_form"
                                                        ></cirrus-input>
                                                        <span v-html="area_unit"></span>
                                                    </td>
                                                    <td class="text-left">
                                                        <multiselect
                                                            data-inverted=""
                                                            v-model="new_unit_history_data.unit_area_status"
                                                            :options="unit_status_list"
                                                            openDirection="bottom"
                                                            :allowEmpty="false"
                                                            class="vue-select2 dropdown-left dropdown-150"
                                                            placeholder="Select a status"
                                                            track-by="field_key"
                                                            label="field_value"
                                                            :show-labels="false"
                                                            ><span slot="noResult"
                                                                >Oops! No elements found. Consider changing the search
                                                                query.</span
                                                            >
                                                        </multiselect>
                                                    </td>
                                                    <td class="text-left">
                                                        <multiselect
                                                            data-inverted=""
                                                            openDirection="bottom"
                                                            v-model="new_unit_history_data.unit_area_lease"
                                                            :options="lease_list"
                                                            :allowEmpty="false"
                                                            class="vue-select2 dropdown-left dropdown-150"
                                                            placeholder="Select a lease"
                                                            track-by="field_key"
                                                            label="field_key"
                                                            v-if="
                                                                new_unit_history_data.unit_area_status.field_key !==
                                                                    'I' &&
                                                                new_unit_history_data.unit_area_status.field_key !== 'V'
                                                            "
                                                            :show-labels="false"
                                                            ><span slot="noResult"
                                                                >Oops! No elements found. Consider changing the search
                                                                query.</span
                                                            >
                                                        </multiselect>
                                                    </td>
                                                    <td class="text-right">
                                                        <v-btn
                                                            v-if="edit_form"
                                                            color="success"
                                                            x-small
                                                            @click="addUnitHistory()"
                                                            >Add New</v-btn
                                                        >
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </template>
                                    </v-simple-table>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_FLOOR_UNITS',
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            floor_headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Floor Name', value: 'floor_unit_contents', width: '50%', sortable: false },
                { text: 'Common Area', value: 'floor_common_area', align: 'right', sortable: false },
                { text: 'Total Area', value: 'floor_total_area', align: 'right', sortable: false },
                { text: 'Vacant Area', value: 'floor_vacant_area', align: 'right', sortable: false },
                { text: 'Order', value: 'floor_sequence', align: 'center', sortable: false },
                { text: '', value: 'data-table-expand', class: ['data-table-mini-action'], align: 'end' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            floor_page: 1,
            floor_page_count: 0,
            floor_items_per_page: 10,
            floor_search_datatable: '',
            AED_floor_modal: false,
            AED_unit_modal: false,
            AED_unit_history_modal: false,
            show_details_modal: false,
            loading_history: false,
            modal_floor_current_ctr: 0,
            modal_unit_current_ctr: 0,
            floors_and_units_list: [],
            floors_and_units_arr: [],
            floor_unit_arr: [],
            floor_unit_history_arr: [],
            account_list: [],
            expanded: [],
            total_floor_area: 0.0,
            total_vacant_area: 0.0,
            default_start_date: '01/01/1900',
            info_unit_status_list: [
                { field_key: 'I', field_value: 'Inactive' },
                { field_key: 'V', field_value: 'Vacant' },
            ],
            unit_status_list: [
                { field_key: 'I', field_value: 'Inactive' },
                { field_key: 'O', field_value: 'Occupied' },
                { field_key: 'V', field_value: 'Vacant' },
            ],
            residential_flag: false,
            success_flag: false,
            new_unit_history_data: {
                unit_area_start_date:
                    ('0' + new Date().getDay()).slice(-2) +
                    '/' +
                    ('0' + (new Date().getMonth() + 1)).slice(-2) +
                    '/' +
                    new Date().getFullYear(),
                unit_area_end_date:
                    ('0' + new Date().getDay()).slice(-2) +
                    '/' +
                    ('0' + (new Date().getMonth() + 1)).slice(-2) +
                    '/' +
                    new Date().getFullYear(),
                unit_area: '0.00',
                unit_area_status: { field_key: '', field_value: 'Please select...' },
                unit_area_lease: { field_key: '', field_value: 'Please select...' },
            },
            lease_list: [],
            area_unit: 'm&sup2;',
        };
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        resetForm: function () {
            this.edit_form = false;
        },
        loadForm: function () {
            this.error_server_msg = {};
            if (this.property_code !== '') {
                this.loadPropertyFloorNUnit();
            }
        },
        loadPropertyFloorNUnit: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/floors-and-units', form_data).then((response) => {
                    this.loadResponseToVariables(response);
                    if (this.new_property || this.AED_unit_history_modal) this.edit_form = true;
                    else this.edit_form = false;
                });
            } else {
                let apiUrl = 'temp/property/fetch/floors-and-units';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);
                    if (this.new_property || this.AED_unit_history_modal) this.edit_form = true;
                    else this.edit_form = false;
                });
            }
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.floors_and_units_list = response.data.floors_and_units_list;
            this.total_floor_area = response.data.total_floor_area;
            this.total_vacant_area = response.data.total_vacant_area;
            this.residential_flag = response.data.residential_flag;
            this.area_unit = response.data.area_unit;
            if (this.isPropertyInPrintView()) this.expanded = response.data.floors_and_units_list;
        },
        modalFloorSubmitData: function () {
            let index = this.floors_and_units_arr.index;
            let floor_id = this.floors_and_units_arr.floor_id;
            let status = this.floors_and_units_arr.status;
            let floor_name = this.floors_and_units_arr.floor_name;
            let floor_common_area = this.floors_and_units_arr.floor_common_area;

            let error_server_msg2 = [];
            if (floor_name === '') error_server_msg2.push(['You have not provided a valid floor name.']);

            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('floor_id', floor_id);
                form_data.append('floor_name', floor_name);
                form_data.append('floor_common_area', floor_common_area);
                form_data.append('status', status);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/update-or-create/floor';
                } else {
                    apiUrl = 'temp/property/update-or-create/floor';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.error_server_msg2 = response.data.error_server_msg2;
                    this.loadForm();
                    this.loading_setting = false;
                    if (this.error_server_msg2.length === 0) {
                        if (this.floors_and_units_arr.index === 'New')
                            this.floors_and_units_arr.index = this.floors_and_units_list.length;
                        this.floors_and_units_arr.floor_id = response.data.floor_id;
                        this.floors_and_units_arr.status = 'saved';

                        this.success_flag = true;
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        modalFloorPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.floors_and_units_arr.index;
            if (current_index === 'New') {
                this.modal_floor_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_floor_current_ctr = this.floors_and_units_list.length - 1;
                } else {
                    this.modal_floor_current_ctr = current_index;
                }
            }
            this.floors_and_units_arr = this.floors_and_units_list[this.modal_floor_current_ctr];
            this.floors_and_units_arr.index = this.modal_floor_current_ctr;
        },
        modalFloorNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.floors_and_units_arr.index;
            if (current_index === 'New') {
                this.modal_floor_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.floors_and_units_list.length - 1) {
                    this.modal_floor_current_ctr = 0;
                } else {
                    this.modal_floor_current_ctr = current_index;
                }
            }
            this.floors_and_units_arr = this.floors_and_units_list[this.modal_floor_current_ctr];
            this.floors_and_units_arr.index = this.modal_floor_current_ctr;
        },
        modalOpenFloorAED: function (index) {
            this.AED_floor_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.floors_and_units_arr = this.floors_and_units_list[index];
            this.floors_and_units_arr.index = index;
            this.modal_floor_current_ctr = index;
        },
        modalOpenUnitAED: function (floor_index, unit_index) {
            this.AED_unit_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.floor_unit_arr = this.floors_and_units_list[floor_index].floor_unit_list[unit_index];
            this.floor_unit_arr.floor_index = floor_index;
            this.floor_unit_arr.unit_index = unit_index;
            this.modal_floor_current_ctr = floor_index;
            this.modal_unit_current_ctr = unit_index;
        },
        modalOpenDetails: function (index) {
            this.show_details_modal = true;
            this.floors_and_units_arr = this.floors_and_units_list[index];
            this.floors_and_units_arr.index = index;
            this.modal_floor_current_ctr = index;
        },
        modalAddFloorData: function () {
            this.AED_floor_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.floors_and_units_arr = {
                index: 'New',
                floor_id: '',
                floor_name: '',
                floor_common_area: '0.00',
                floor_total_area: '0.00',
                floor_vacant_area: '0.00',
                status: 'new',
            };
        },
        modalAddUnitData: function (floor_name) {
            this.AED_unit_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.floor_unit_arr = {
                floor_index: this.modal_floor_current_ctr,
                floor_name: floor_name,
                unit_index: 'New',
                unit_code: '',
                unit_description: '',
                unit_area: '0.00',
                unit_bedroom: '0.00',
                unit_bathroom: '0.00',
                unit_car_park: '0.00',
                unit_tenancy_flag: false,
                unit_area_status: { field_key: '', field_value: '' },
                status: 'new',
            };
        },
        modalUnitSubmitData: function () {
            let floor_index = this.floor_unit_arr.floor_index;
            let floor_name = this.floor_unit_arr.floor_name;
            let unit_index = this.floor_unit_arr.unit_index;
            let floor_details = this.floors_and_units_list[floor_index];
            // let floor_name = floor_details.floor_name;

            let unit_id = this.floor_unit_arr.unit_id;
            let unit_code = this.floor_unit_arr.unit_code;
            let unit_description = this.floor_unit_arr.unit_description;
            let unit_area = this.floor_unit_arr.unit_area;
            let unit_bedroom = this.floor_unit_arr.unit_bedroom;
            let unit_bathroom = this.floor_unit_arr.unit_bathroom;
            let unit_car_park = this.floor_unit_arr.unit_car_park;
            let unit_tenancy_flag = this.floor_unit_arr.unit_tenancy_flag;
            // let unit_area_status = this.floor_unit_arr.unit_area_status;
            let unit_area_status = this.floor_unit_arr.unit_area_status.field_key;

            let status = this.floor_unit_arr.status;
            unit_code = unit_code.replace(' ', '');
            unit_code = unit_code.slice(0, 10);
            let error_server_msg2 = [];
            if (unit_code === '') error_server_msg2.push(['You have not provided a valid unit code.']);
            if (unit_code === '0') error_server_msg2.push(['Your unit code can not be 0.']);
            if (unit_code.length > 10) error_server_msg2.push(['You have not entered a valid unit code.']);
            if (unit_description === '') error_server_msg2.push(['Please enter a description in plain text.']);
            if (unit_area === '') error_server_msg2.push(['You have not entered a valid unit area.']);
            if (unit_area_status === '') error_server_msg2.push(['You need to enter a valid unit status.']);

            if (this.residential_flag) {
                if (unit_bedroom === '') error_server_msg2.push(['Number of Bedroom(s) ranges from 1 - 10 only.']);
                if (unit_bathroom === '') error_server_msg2.push(['Number of Bathroom(s) ranges from 1 - 10 only.']);
                if (unit_car_park === '') error_server_msg2.push(['Number of Car Park(s) ranges from 1 - 10 only.']);
            }

            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('unit_id', unit_id);
                form_data.append('floor_name', floor_name);
                form_data.append('unit_code', unit_code);
                form_data.append('unit_description', unit_description);
                form_data.append('unit_area', unit_area);
                form_data.append('unit_area_status', unit_area_status);
                form_data.append('unit_bedroom', unit_bedroom);
                form_data.append('unit_bathroom', unit_bathroom);
                form_data.append('unit_car_park', unit_car_park);
                form_data.append('unit_tenancy_flag', unit_tenancy_flag);
                form_data.append('status', status);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/update-or-create/unit';
                } else {
                    apiUrl = 'temp/property/update-or-create/unit';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.error_server_msg2 = response.data.error_server_msg2;
                    this.loadForm();
                    this.loading_setting = false;
                    if (this.error_server_msg2.length === 0) {
                        if (this.floor_unit_arr.index === 'New')
                            this.floor_unit_arr.index =
                                this.floors_and_units_list[this.modal_floor_current_ctr].floor_unit_list;
                        this.floor_unit_arr.unit_id = response.data.unit_id;
                        this.floor_unit_arr.status = 'saved';

                        this.success_flag = true;
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        modalUnitPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_floor_index = this.floor_unit_arr.floor_index;
            let current_unit_index = this.floor_unit_arr.unit_index;
            if (current_unit_index === 'New') {
                this.modal_unit_current_ctr = 0;
            } else {
                current_unit_index = current_unit_index - 1;
                if (current_unit_index === -1) {
                    this.modal_unit_current_ctr =
                        this.floors_and_units_list[this.modal_floor_current_ctr].floor_unit_list.length - 1;
                } else {
                    this.modal_unit_current_ctr = current_unit_index;
                }
            }
            this.floor_unit_arr =
                this.floors_and_units_list[this.modal_floor_current_ctr].floor_unit_list[this.modal_unit_current_ctr];
            this.floor_unit_arr.unit_index = this.modal_unit_current_ctr;
            this.floor_unit_arr.floor_index = this.modal_floor_current_ctr;
        },
        modalUnitNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_floor_index = this.floor_unit_arr.floor_index;
            let current_unit_index = this.floor_unit_arr.unit_index;
            if (current_unit_index === 'New') {
                this.modal_unit_current_ctr = 0;
            } else {
                current_unit_index = current_unit_index + 1;
                if (
                    current_unit_index >
                    this.floors_and_units_list[this.modal_floor_current_ctr].floor_unit_list.length - 1
                ) {
                    this.modal_unit_current_ctr = 0;
                } else {
                    this.modal_unit_current_ctr = current_unit_index;
                }
            }
            this.floor_unit_arr =
                this.floors_and_units_list[this.modal_floor_current_ctr].floor_unit_list[this.modal_unit_current_ctr];
            this.floor_unit_arr.unit_index = this.modal_unit_current_ctr;
            this.floor_unit_arr.floor_index = this.modal_floor_current_ctr;
        },
        loadAllAccounts: function () {
            this.$api.post('loadAllAccountDropdown').then((response) => {
                this.account_list = response.data.grouped;
            });
        },
        async deleteFloor(index) {
            if (index !== 'New') {
                let status = this.floors_and_units_list[index].status;
                let floor_id = this.floors_and_units_list[index].floor_id;
                let floor_name = this.floors_and_units_list[index].floor_name;
                let floor_unit_list = this.floors_and_units_list[index].floor_unit_list;
                let floor_sequence = this.floors_and_units_list[index].floor_sequence;
                if (floor_unit_list.length > 0) {
                    let dialog_prop = {
                        title: 'Warning',
                        message:
                            'This floor has units attached to it. You need to first delete the units attached to this floor.',
                        icon_show: true,
                    };
                    const result = await cirrusDialog(dialog_prop);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('floor_id', floor_id);
                        form_data.append('floor_name', floor_name);
                        form_data.append('floor_sequence', floor_sequence);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/floor';
                        } else {
                            apiUrl = 'temp/property/delete/floor';
                        }
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.floors_and_units_list.splice(index, 1);
                            this.loading_setting = false;
                            this.property_floor_units_list_old = JSON.parse(JSON.stringify(this.floors_and_units_list));
                        });
                    }
                }
            }
        },
        async deleteUnit(floor_index, unit_index) {
            if (unit_index !== 'New') {
                let floors_and_units_list = this.floors_and_units_list[floor_index].floor_unit_list[unit_index];
                let unit_id = floors_and_units_list.unit_id;
                let unit_code = floors_and_units_list.unit_code;
                let lease_code = floors_and_units_list.lease_code;
                let unit_area_status_code = floors_and_units_list.unit_area_status_code;
                if (unit_area_status_code === 'O') {
                    let dialog_prop = {
                        title: 'Warning',
                        message:
                            'Unit is/was attached to lease ' +
                            lease_code +
                            " and has associated transaction or lease charges. The unit can't be deleted. You need to detach the unit and make it inactive instead..",
                        icon_show: true,
                    };
                    const result = await cirrusDialog(dialog_prop);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('unit_id', unit_id);
                        form_data.append('unit_code', unit_code);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/unit';
                        } else {
                            apiUrl = 'temp/property/delete/unit';
                        }
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.floors_and_units_list[floor_index].floor_unit_list.splice(unit_index, 1);
                            this.loading_setting = false;
                        });
                    }
                }
            }
        },
        setOrder: function (action, key) {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            let next_id = parseInt(key) + 1;
            let prev_id = parseInt(key) - 1;
            let seq_size = this.floors_and_units_list.length;
            if (next_id + 1 >= seq_size) {
                next_id = seq_size - 1;
            }
            if (prev_id <= 0) {
                prev_id = 0;
            }

            let seq_id = this.floors_and_units_list[key].floor_id;
            let next_seq_id = this.floors_and_units_list[next_id].floor_id;
            let prev_seq_id = this.floors_and_units_list[prev_id].floor_id;

            let orig_seq = parseInt(this.floors_and_units_list[key].floor_sequence);
            let next_seq = parseInt(this.floors_and_units_list[key].floor_sequence) + 1;
            let prev_seq = parseInt(this.floors_and_units_list[key].floor_sequence) - 1;

            form_data.append('seq_id', seq_id);
            form_data.append('next_seq_id', next_seq_id);
            form_data.append('prev_seq_id', prev_seq_id);
            form_data.append('orig_seq', orig_seq);
            form_data.append('seq_size', seq_size.toString());
            form_data.append('next_seq', next_seq);
            form_data.append('prev_seq', prev_seq);

            let apiUrl = '';
            if (this.isPropertyFormLive()) {
                apiUrl = 'property/update/' + action;
            } else {
                apiUrl = 'property/update/' + action;
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.loadForm();
            });
        },
        modalOpenHistory: function (floor_index, unit_index) {
            this.AED_unit_history_modal = true;
            this.loading_history = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let floors_and_units_list = this.floors_and_units_list;

            let unit_area_id = floors_and_units_list[floor_index].floor_unit_list[unit_index].unit_area_id;
            let floor_name = floors_and_units_list[floor_index].floor_name;
            let unit_code = floors_and_units_list[floor_index].floor_unit_list[unit_index].unit_code;
            let unit_description = floors_and_units_list[floor_index].floor_unit_list[unit_index].unit_description;

            let unit_history_list = floors_and_units_list[floor_index].floor_unit_list[unit_index].unit_history_list;
            this.floor_unit_history_arr = {
                unit_area_id: unit_area_id,
                unit_code: unit_code,
                unit_description: unit_description,
                unit_history_list: unit_history_list,
            };

            this.new_unit_history_data = {
                floor_index: floor_index,
                unit_index: unit_index,
                floor_name: floor_name,
                unit_area_id: unit_area_id,
                unit_code: unit_code,
                unit_area_start_date:
                    ('0' + new Date().getDay()).slice(-2) +
                    '/' +
                    ('0' + (new Date().getMonth() + 1)).slice(-2) +
                    '/' +
                    new Date().getFullYear(),
                unit_area_end_date:
                    ('0' + new Date().getDay()).slice(-2) +
                    '/' +
                    ('0' + (new Date().getMonth() + 1)).slice(-2) +
                    '/' +
                    new Date().getFullYear(),
                unit_area: '0.00',
                unit_area_status: { field_key: 'I', field_value: 'Inactive' },
                unit_area_lease: { field_key: '', field_value: 'Please select...' },
            };
        },
        updateUnitHistory: function (history_index) {
            let floor_unit_history_arr = this.floor_unit_history_arr;
            let unit_history_list = floor_unit_history_arr.unit_history_list[history_index];
            let unit_area_id = unit_history_list.unit_area_id;
            let unit_code = floor_unit_history_arr.unit_code;

            let unit_area_start_date = unit_history_list.unit_area_start_date;
            let unit_area_end_date = unit_history_list.unit_area_end_date;
            let unit_area = unit_history_list.unit_area;
            let unit_area_status = unit_history_list.unit_area_status.field_key;
            let unit_area_lease = unit_history_list.unit_area_lease.field_key;
            if (unit_area_lease === null) unit_area_lease = '';
            if (unit_area_status === 'I') unit_area_lease = '';
            let error_server_msg2 = [];
            if (unit_area_status === 'O' && unit_area_lease === '') error_server_msg2.push(['Please select a lease.']);
            if (unit_area_start_date === '') error_server_msg2.push(['Please enter a valid start date.']);
            if (unit_area_end_date === '') error_server_msg2.push(['Please enter a valid end date.']);

            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length > 0) return;
            this.loading_history = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            form_data.append('unit_area_id', unit_area_id);
            form_data.append('unit_code', unit_code);
            form_data.append('unit_area_start_date', unit_area_start_date);
            form_data.append('unit_area_end_date', unit_area_end_date);
            form_data.append('unit_area', unit_area);
            form_data.append('unit_area_status', unit_area_status);
            form_data.append('unit_area_lease', unit_area_lease);

            let apiUrl = '';
            if (this.isPropertyFormLive()) {
                apiUrl = 'property/update/unit-history';
            } else {
                apiUrl = 'temp/property/update/unit-history';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.error_server_msg2 = response.data.error_server_msg2;
                this.loading_history = false;
                this.loading_setting = false;
                if (this.error_server_msg2.length === 0) {
                    this.loadForm();
                    this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                }
            });
        },
        addUnitHistory: function () {
            let new_unit_history_data = this.new_unit_history_data;

            let floor_index = new_unit_history_data.floor_index;
            let unit_index = new_unit_history_data.unit_index;
            let floor_name = new_unit_history_data.floor_name;
            let unit_area_id = new_unit_history_data.unit_area_id;
            let unit_code = new_unit_history_data.unit_code;
            let unit_area_start_date = new_unit_history_data.unit_area_start_date;
            let unit_area_end_date = new_unit_history_data.unit_area_end_date;
            let unit_area = new_unit_history_data.unit_area;
            let unit_area_status = new_unit_history_data.unit_area_status.field_key;
            let unit_area_lease = new_unit_history_data.unit_area_lease.field_key;
            if (unit_area_status === 'I') unit_area_lease = '';
            let error_server_msg2 = [];
            if (unit_area_status === 'O' && unit_area_lease === '') error_server_msg2.push(['Please select a lease.']);
            if (unit_area_start_date === '') error_server_msg2.push(['Please enter a valid start date.']);
            if (unit_area_end_date === '') error_server_msg2.push(['Please enter a valid end date.']);
            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length > 0) return;
            this.loading_history = false;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            form_data.append('unit_area_id', unit_area_id);
            form_data.append('floor_name', floor_name);
            form_data.append('unit_code', unit_code);
            form_data.append('unit_area_start_date', unit_area_start_date);
            form_data.append('unit_area_end_date', unit_area_end_date);
            form_data.append('unit_area', unit_area);
            form_data.append('unit_area_status', unit_area_status);
            form_data.append('unit_area_lease', unit_area_lease);

            let apiUrl = '';
            if (this.isPropertyFormLive()) apiUrl = 'property/create/unit-history';
            else apiUrl = 'temp/property/create/unit-history';

            this.$api.post(apiUrl, form_data).then((response) => {
                this.error_server_msg2 = response.data.error_server_msg2;
                this.loading_setting = false;
                if (this.error_server_msg2.length === 0) {
                    this.loadForm();
                    let unit_history_list_var = response.data.unit_history_list;
                    this.floor_unit_history_arr.unit_history_list = unit_history_list_var;
                    this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                }
            });
        },
        async deleteUnitHistory(history_index) {
            let floor_unit_history_arr = this.floor_unit_history_arr;
            let unit_history_list = floor_unit_history_arr.unit_history_list[history_index];
            let floor_index = floor_unit_history_arr.floor_index;
            let unit_index = floor_unit_history_arr.unit_index;
            let unit_area_id = unit_history_list.unit_area_id;
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('unit_area_id', unit_area_id);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) apiUrl = 'property/delete/unit-history';
                else apiUrl = 'temp/property/delete/unit-history';

                this.$api.post(apiUrl, form_data).then((response) => {
                    this.error_server_msg2 = response.data.error_server_msg2;
                    this.loading_setting = false;
                    if (this.error_server_msg2.length === 0) {
                        this.loadForm();
                        this.floor_unit_history_arr.unit_history_list.splice(history_index, 1);
                        this.success_flag = true;
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        loadLeaseList: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            this.$api.post('ui/fetch/all-lease-list', form_data).then((response) => {
                this.lease_list = response.data.lease_list;
            });
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.loadLeaseList();
            }
        },
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
        ...mapGetters(['getDDCountryStates']),
    },
    mounted() {
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
    },
    created() {
        bus.$on('loadPropertyFloorNUnitsFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
