<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Parking Bays</h6>
                <v-spacer></v-spacer>
                <v-btn
                    v-show="isEditable() && !pmro_read_only"
                    x-small
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="edit_form"
                    class="v-step-save-1-button"
                    @click="saveForm()"
                    icon
                >
                    <v-icon
                        light
                        color="green"
                        >check</v-icon
                    >
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    v-if="isEditable()"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="3"
                    md="3"
                    class="form-label"
                    >Leasable Parking Bays:</v-col
                >
                <v-col
                    xs="12"
                    sm="9"
                    md="9"
                    class="form-input"
                >
                    <cirrus-input
                        inputFormat="wholeNumberOnly"
                        v-model="leasable_parking_bays"
                        :edit_form="edit_form"
                    ></cirrus-input>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="3"
                    md="3"
                    class="form-label"
                    >Licensed Parking Bays:</v-col
                >
                <v-col
                    xs="12"
                    sm="9"
                    md="9"
                    class="form-input"
                >
                    <cirrus-input
                        inputFormat="wholeNumberOnly"
                        v-model="licensed_parking_bays"
                        :edit_form="edit_form"
                    ></cirrus-input>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="3"
                    md="3"
                    class="form-label"
                    >Casual Parking Bays:</v-col
                >
                <v-col
                    xs="12"
                    sm="9"
                    md="9"
                    class="form-input"
                >
                    <cirrus-input
                        inputFormat="wholeNumberOnly"
                        v-model="casual_parking_bays"
                        :edit_form="edit_form"
                    ></cirrus-input>
                </v-col>
            </v-row>
        </div>
        <v-divider></v-divider>
        <v-card
            elevation="0"
            v-if="edit_form"
        >
            <v-card-actions v-if="edit_form">
                <v-spacer></v-spacer>
                <v-btn
                    class="v-step-save-2-button"
                    @click="saveForm()"
                    color="success"
                    dark
                    small
                >
                    Save Parking Bays Details
                </v-btn>
            </v-card-actions>
        </v-card>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_PARKING_BAYS',
            leasable_parking_bays: '',
            licensed_parking_bays: '',
            casual_parking_bays: '',
        };
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            if (this.property_code !== '') {
                this.loadPropertyParkingBays();
            }
            if (!this.new_property) this.edit_form = false;
        },
        loadPropertyParkingBays: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let api_url;
            if (this.isPropertyFormLive()) {
                //get data from live
                api_url = 'property/fetch/parking-bays';
            } else {
                api_url = 'temp/property/fetch/parking-bays';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.leasable_parking_bays = response.data.leasable_parking_bays;
                this.licensed_parking_bays = response.data.licensed_parking_bays;
                this.casual_parking_bays = response.data.casual_parking_bays;
                this.loading_setting = false;
                if (this.new_property) this.edit_form = true;
                else this.edit_form = false;
            });
        },
        saveForm: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('leasable_parking_bays', this.leasable_parking_bays);
            form_data.append('licensed_parking_bays', this.licensed_parking_bays);
            form_data.append('casual_parking_bays', this.casual_parking_bays);
            form_data.append('no_load', true);
            let api_url;
            if (this.isPropertyFormLive()) {
                //get data from live
                api_url = 'property/update/parking-bays';
            } else {
                api_url = 'temp/property/update/parking-bays';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.loading_setting = false;
                if (this.new_property) this.edit_form = true;
                else this.edit_form = false;
            });
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                // this.loadCompanyList();
            }
        },
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
        ...mapGetters(['getDDCountryStates']),
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
    },
    created() {
        bus.$on('loadPropertyParkingBaysFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
