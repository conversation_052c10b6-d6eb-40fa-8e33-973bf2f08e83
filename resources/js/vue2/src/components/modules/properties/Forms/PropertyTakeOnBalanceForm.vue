<template>
    <div
        v-on:dblclick="doubleClickForm()"
        style="max-width: 100%"
    >
        <v-card
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Take On Balances</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    v-if="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                    v-show="isEditable() && !pmro_read_only"
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                    v-show="isEditable() && !pmro_read_only"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable()"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    v-if="isEditable()"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-col
                    class="text-center"
                    v-if="take_on_balance_list.length === 0"
                    v-show="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-btn
                        v-if="!pmro_read_only"
                        depressed
                        small
                        color="success"
                        @click="modalAddData()"
                        >Add Take On Balance</v-btn
                    >
                    <div
                        style="margin: 10px 0px"
                        v-else
                    >
                        No take on balances at the moment
                    </div>
                </v-col>
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="take_on_balance_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="take_on_balance_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.index="{ item }">
                        {{ take_on_balance_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.take_on_balance_account_code="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                >{{ item.take_on_balance_account_code }} - {{ item.take_on_balance_account_name }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.take_on_balance_cash="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                >{{ currency_symbol
                                }}{{
                                    accountingAmountFormat(numberWithCommas(roundTo(item.take_on_balance_cash, 3)))
                                }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.take_on_balance_accrual="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                >{{ currency_symbol
                                }}{{
                                    accountingAmountFormat(numberWithCommas(roundTo(item.take_on_balance_accrual, 3)))
                                }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.take_on_balance_year="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.take_on_balance_year }}</span>
                        </div>
                    </template>
                    <template v-slot:item.take_on_balance_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.take_on_balance_date }}</span>
                        </div>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenAED(take_on_balance_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit</v-icon
                        >
                        <v-icon
                            color="red"
                            @click="deleteTakeOnBalance(take_on_balance_list.indexOf(item))"
                            v-if="edit_form"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="take_on_balance_list.length > 5"
                    v-if="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </div>
        </div>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteTakeOnBalance(take_on_balance_add_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Take On Balance Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="take_on_balance_add_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        take_on_balance_add_arr.index === 'New'
                                            ? take_on_balance_add_arr.index
                                            : take_on_balance_add_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Account</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        v-if="take_on_balance_add_arr.index === 'New'"
                                        v-model="take_on_balance_add_arr.take_on_balance_account"
                                        :options="account_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        group-values="field_group_values"
                                        :groupSelect="false"
                                        group-label="field_group_names"
                                        :group-select="true"
                                        :custom-label="nameWithDash"
                                        placeholder="Please select..."
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <span
                                        v-if="take_on_balance_add_arr.index !== 'New'"
                                        class="form-input-text"
                                        >{{ take_on_balance_add_arr.take_on_balance_account_code }} -
                                        {{ take_on_balance_add_arr.take_on_balance_account_name }}</span
                                    >
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Take On Balance<br />(Cash)</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :inputFormat="'dollar'"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="take_on_balance_add_arr.take_on_balance_cash"
                                        size=""
                                        :id="'take_on_balance_cash'"
                                        data-inverted=""
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Take On Balance<br />(Accrual)</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :inputFormat="'dollar'"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="take_on_balance_add_arr.take_on_balance_accrual"
                                        size=""
                                        :id="'take_on_balance_accrual'"
                                        data-inverted=""
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Year</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="take_on_balance_add_arr.take_on_balance_year"
                                        size=""
                                        :id="'take_on_balance_year'"
                                        data-inverted=""
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'take_on_balance_date' + String(Math.random()).replace('.', '')"
                                        v-model="take_on_balance_add_arr.take_on_balance_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Date' : false"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="take_on_balance_add_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="take_on_balance_add_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteTakeOnBalance(take_on_balance_add_arr.index)"
                        v-if="take_on_balance_add_arr.diary_complete === '' && take_on_balance_add_arr.index !== 'New'"
                        color="error"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapState } from 'vuex';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_TAKE_ON_BALANCE',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            take_on_balance_list: [],
            take_on_balance_list_old: [],
            account_list: [],
            take_on_balance_add_arr: [],
            show_activity_log_modal: false,
            AED_modal: false,
            success_flag: false,
            modal_current_ctr: 0,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Code: Account Name', value: 'take_on_balance_account_code' },
                { text: 'Take On Balance (Cash)', value: 'take_on_balance_cash', align: 'end' },
                { text: 'Take On Balance (Accrual)', value: 'take_on_balance_accrual', align: 'end' },
                { text: 'Year', value: 'take_on_balance_year' },
                { text: 'Date', value: 'take_on_balance_date_raw' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            currency_symbol: '$',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        // console.log('aa');
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'Code: Account Name', value: 'take_on_balance_account_code', width: 'auto' },
                { text: 'Take On Balance (Cash)', value: 'take_on_balance_cash', align: 'end', width: 'auto' },
                { text: 'Take On Balance (Accrual)', value: 'take_on_balance_accrual', align: 'end', width: 'auto' },
                { text: 'Year', value: 'take_on_balance_year', width: 'auto' },
                { text: 'Date', value: 'take_on_balance_date_raw', width: 'auto' },
            ];
            this.items_per_page = 9999;
        }
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadParameters: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('no_load', true);
            let apiUrl = 'parameter/fetch/all-property-account';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.account_list = response.data.account_list;
            });
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '' || this.forceLoad) {
                // console.log('load property property details');
                this.loadTakeOnBalance();
            }
            if (!this.new_property) this.edit_form = false;
        },
        loadTakeOnBalance: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';

            if (this.isPropertyFormLive()) {
                apiUrl = 'property/fetch/take-on-balances';
            } else {
                apiUrl = 'temp/property/fetch/take-on-balances';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.take_on_balance_list = response.data.take_on_balance_list;
                this.currency_symbol = response.data.currency_symbol;
                this.loading_setting = false;
            });
            if (this.new_property) this.edit_form = true;
            else this.edit_form = false;
        },
        modalAddData: function () {
            this.edit_form = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.AED_modal = true;
            var d = new Date();
            this.take_on_balance_add_arr = {
                index: 'New',
                take_on_balance_account: { field_key: '', field_value: 'Please select ...' },
                take_on_balance_cash: '',
                take_on_balance_accrual: '',
                take_on_balance_year: '',
                take_on_balance_date: d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                status: 'new',
            };
        },
        modalSubmitData: function () {
            this.error_server_msg2 = [];
            let take_on_balance_account_code = this.take_on_balance_add_arr.take_on_balance_account.field_key;
            let take_on_balance_cash = this.take_on_balance_add_arr.take_on_balance_cash;
            let take_on_balance_accrual = this.take_on_balance_add_arr.take_on_balance_accrual;
            let take_on_balance_year = this.take_on_balance_add_arr.take_on_balance_year;
            let take_on_balance_date = this.take_on_balance_add_arr.take_on_balance_date;
            let status = this.take_on_balance_add_arr.status;

            if (take_on_balance_account_code === '')
                this.error_server_msg2.push(['You have not selected an account from the list.']);
            if (take_on_balance_year === '' && take_on_balance_year.length > 4 && !$.isNumeric(take_on_balance_year)) {
                this.error_server_msg2.push(['You have not selected an account from the list.']);
            }

            if (take_on_balance_cash === '' && isNaN(parseFloat(take_on_balance_cash))) {
                this.error_server_msg2.push(['You have not used a valid opening balance cash amount.']);
            }
            if (take_on_balance_accrual === '' && isNaN(parseFloat(take_on_balance_accrual))) {
                this.error_server_msg2.push(['You have not used a valid opening balance accrual amount.']);
            }
            if (take_on_balance_date === '') this.error_server_msg2.push(['Please make sure your date is valid.']);

            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);

                form_data.append('take_on_balance_account_code', take_on_balance_account_code);
                form_data.append('take_on_balance_cash', take_on_balance_cash);
                form_data.append('take_on_balance_accrual', take_on_balance_accrual);
                form_data.append('take_on_balance_year', take_on_balance_year);
                form_data.append('take_on_balance_date', take_on_balance_date);
                form_data.append('status', status);
                form_data.append('no_load', true);
                let api_url;
                if (this.isPropertyFormLive()) {
                    //get data from live
                    api_url = 'property/update-or-create/take-on-balance';
                } else {
                    api_url = 'temp/property/update-or-create/take-on-balance';
                }
                this.$api.post(api_url, form_data).then((response) => {
                    let error_server_msg = response.data.error_server_msg;
                    this.error_server_msg2 = error_server_msg;
                    if (error_server_msg.length === 0) {
                        this.success_flag = true;

                        if (this.take_on_balance_add_arr.status === 'new') {
                            this.take_on_balance_add_arr.index = this.take_on_balance_list.length;
                            this.take_on_balance_add_arr.take_on_balance_account_code =
                                this.take_on_balance_add_arr.take_on_balance_account.field_key;
                            this.take_on_balance_add_arr.take_on_balance_account_name =
                                this.take_on_balance_add_arr.take_on_balance_account.field_value;
                            this.take_on_balance_add_arr.status = 'saved';
                        }

                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                        this.loadForm();
                    }
                    this.loading_setting = false;
                });
            }
        },
        async deleteTakeOnBalance(index) {
            let status = this.take_on_balance_list[index].status;
            let take_on_balance_account_code = this.take_on_balance_list[index].take_on_balance_account_code;
            if (status === 'new') {
                this.take_on_balance_list.splice(index, 1);
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.loading_setting = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('take_on_balance_account_code', take_on_balance_account_code);
                    form_data.append('no_load', true);
                    let apiUrl = '';
                    if (this.isPropertyFormLive()) {
                        apiUrl = 'property/delete/take-on-balance';
                    } else {
                        apiUrl = 'temp/property/delete/take-on-balance';
                    }
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.take_on_balance_list.splice(index, 1);
                        this.loading_setting = false;
                        this.success_flag = true;
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    });
                }
            }
        },
        modalOpenAED: function (index) {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.AED_modal = true;
            this.take_on_balance_add_arr = this.take_on_balance_list[index];
            this.take_on_balance_add_arr.index = index;
            this.modal_current_ctr = index;
        },
        modalPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.take_on_balance_add_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.take_on_balance_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.take_on_balance_add_arr = this.take_on_balance_list[this.modal_current_ctr];
            this.take_on_balance_add_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.take_on_balance_add_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.take_on_balance_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.take_on_balance_add_arr = this.take_on_balance_list[this.modal_current_ctr];
            this.take_on_balance_add_arr.index = this.modal_current_ctr;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.loadParameters();
            }
        },
    },
    created() {
        bus.$on('loadPropertyTakeOnBalancesFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
