<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Management Fees</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable()"
                    class="v-step-refresh-button"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="management_fee_list.length === 0 && !loading_setting"
            v-show="isEditable()"
        >
            <v-btn
                v-if="!pmro_read_only"
                depressed
                small
                color="success"
                @click="modalAddData()"
                >Add Management Fees</v-btn
            >
            <div
                style="margin: 10px 0px"
                v-else
            >
                No management fees at the moment
            </div>
        </v-col>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="management_fee_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="management_fee_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ management_fee_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_fee_account_desc="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.man_fee_account_desc }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_fee_start_date="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.man_fee_start_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_fee_end_date="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.man_fee_end_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_fee_method_type_desc="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.man_fee_method_type_desc }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_fee_amount_percentage="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.man_fee_method_type === 1"
                            ></span>
                            <span
                                class="form-input-text"
                                v-if="item.man_fee_method_type === 2"
                                >{{ currency_symbol }}{{ accountingAmountFormat(item.man_fee_amount) }}</span
                            >
                            <span
                                class="form-input-text"
                                v-if="item.man_fee_method_type === 4"
                                >{{ roundTo(item.man_fee_account_range_tot_perc, 2) }}%</span
                            >
                            <span
                                class="form-input-text"
                                v-if="item.man_fee_method_type === 3 || item.man_fee_method_type === 5"
                                >{{ roundTo(item.man_fee_percentage, 2) }}%</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenAED(management_fee_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit</v-icon
                        >
                        <v-icon
                            color="red"
                            v-if="edit_form"
                            @click="deleteManagementFee(management_fee_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="management_fee_list.length > 5"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteManagementFee(management_fee_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Management Fee Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="management_fee_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        management_fee_arr.index === 'New'
                                            ? management_fee_arr.index
                                            : management_fee_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Account</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Account' : false"
                                        v-model="management_fee_arr.man_fee_account"
                                        :options="man_fee_account_list"
                                        group-values="field_group_values"
                                        :groupSelect="false"
                                        group-label="field_group_names"
                                        :group-select="true"
                                        :custom-label="nameWithDash"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-800"
                                        placeholder="Select an account"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="isMultiplePropertyLedger"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Property Ledger:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input multiselect_short"
                                >
                                    <cirrus-single-select-v2
                                        v-model="management_fee_arr.propertyLedgerId"
                                        :options="property_ledger_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Please select"
                                        :custom-label="nameWithCodeDash"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Period</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'man_fee_start_date'"
                                        v-model="management_fee_arr.man_fee_start_date"
                                        :edit_form="true"
                                    ></cirrus-icon-date-picker>
                                    &nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;&nbsp;
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'man_fee_start_date'"
                                        v-model="management_fee_arr.man_fee_end_date"
                                        :edit_form="true"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Method</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <v-btn-toggle
                                            class="form-toggle"
                                            v-model="management_fee_arr.man_fee_method_type"
                                            mandatory
                                        >
                                            <v-btn
                                                small
                                                text
                                                v-show="false"
                                            >
                                                NONE
                                            </v-btn>
                                            <v-btn
                                                x-small
                                                text
                                            >
                                                Nil
                                            </v-btn>
                                            <v-btn
                                                x-small
                                                text
                                            >
                                                Fixed
                                            </v-btn>
                                            <v-btn
                                                x-small
                                                text
                                            >
                                                Percentage of Total Receipts
                                            </v-btn>
                                            <v-btn
                                                x-small
                                                text
                                            >
                                                Percentage of account ranges
                                            </v-btn>
                                            <v-btn
                                                x-small
                                                text
                                            >
                                                Percentage of Total Receipts (Lease)
                                            </v-btn>
                                        </v-btn-toggle>
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="management_fee_arr.man_fee_method_type === 2"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Monthly Amount</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        inputFormat="dollar"
                                        v-model="management_fee_arr.man_fee_amount"
                                        size=""
                                        :id="'man_fee_amount'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Amount' : false"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="management_fee_arr.man_fee_method_type === 3"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Total Percentage</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        inputFormat="percentage"
                                        v-model="management_fee_arr.man_fee_percentage"
                                        size=""
                                        :id="'man_fee_amount'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Percentage' : false"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="management_fee_arr.man_fee_method_type === 5"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Default Lease Percentage</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        inputFormat="percentage"
                                        v-model="management_fee_arr.man_fee_percentage"
                                        size=""
                                        :id="'man_fee_amount'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Percentage' : false"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="management_fee_arr.man_fee_method_type === 4"
                            >
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="form-input"
                                >
                                    <cirrus-server-error
                                        :error_msg="error_server_msg_range"
                                        :errorMsg2="error_server_msg2_range"
                                    ></cirrus-server-error>
                                    <sui-table
                                        class="vue-data-grid"
                                        stackable
                                        width="100%"
                                        cellpadding="0"
                                        cellspacing="0"
                                        border="0"
                                        compact
                                    >
                                        <sui-table-header>
                                            <sui-table-row class="fieldDescription">
                                                <sui-table-header-cell style="width: 5px !important"
                                                    >#</sui-table-header-cell
                                                >
                                                <sui-table-header-cell text-align="center"
                                                    >From Account</sui-table-header-cell
                                                >
                                                <sui-table-header-cell text-align="center"
                                                    >To Account</sui-table-header-cell
                                                >
                                                <sui-table-header-cell text-align="center"
                                                    >Percentage</sui-table-header-cell
                                                >
                                                <sui-table-header-cell text-align="center"></sui-table-header-cell>
                                            </sui-table-row>
                                        </sui-table-header>
                                        <sui-table-body
                                            class="page-form"
                                            v-for="(
                                                management_fee_arr_data, management_fee_arr_index
                                            ) in management_fee_arr.man_fee_account_range_list"
                                            :key="management_fee_arr_index"
                                        >
                                            <sui-table-row class="form-row">
                                                <sui-table-cell
                                                    class="form-input"
                                                    style="width: 5px !important"
                                                >
                                                    {{ management_fee_arr_index + 1 }}
                                                </sui-table-cell>
                                                <sui-table-cell
                                                    class="form-input"
                                                    text-align="center"
                                                >
                                                    <multiselect
                                                        data-inverted=""
                                                        :data-tooltip="edit_form ? 'Account' : false"
                                                        openDirection="bottom"
                                                        v-model="management_fee_arr_data.account_from"
                                                        group-values="field_group_values"
                                                        group-label="field_group_names"
                                                        :options="management_fee_accounts_list"
                                                        :custom-label="nameWithDash"
                                                        :allowEmpty="false"
                                                        class="vue-select2 dropdown-left dropdown-300"
                                                        placeholder="Select an account"
                                                        track-by="field_key"
                                                        label="field_value"
                                                        :show-labels="false"
                                                        ><span slot="noResult"
                                                            >Oops! No elements found. Consider changing the search
                                                            query.</span
                                                        >
                                                    </multiselect>
                                                </sui-table-cell>
                                                <sui-table-cell
                                                    class="form-input"
                                                    text-align="center"
                                                >
                                                    <multiselect
                                                        data-inverted=""
                                                        :data-tooltip="edit_form ? 'Account' : false"
                                                        openDirection="bottom"
                                                        v-model="management_fee_arr_data.account_to"
                                                        group-values="field_group_values"
                                                        group-label="field_group_names"
                                                        :options="management_fee_accounts_list"
                                                        :custom-label="nameWithDash"
                                                        :allowEmpty="false"
                                                        class="vue-select2 dropdown-left dropdown-300"
                                                        placeholder="Select an account"
                                                        track-by="field_key"
                                                        label="field_value"
                                                        :show-labels="false"
                                                        ><span slot="noResult"
                                                            >Oops! No elements found. Consider changing the search
                                                            query.</span
                                                        >
                                                    </multiselect>
                                                </sui-table-cell>
                                                <sui-table-cell
                                                    class="form-input"
                                                    text-align="center"
                                                >
                                                    <cirrus-input
                                                        custom_class="cirrus-input-table-textbox"
                                                        inputFormat="percentage"
                                                        v-model="management_fee_arr_data.account_percentage"
                                                        size=""
                                                        :id="'account_percentage' + management_fee_arr_index"
                                                        data-inverted=""
                                                        :data-tooltip="edit_form ? 'Percentage' : false"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </sui-table-cell>

                                                <sui-table-cell
                                                    class="form-input"
                                                    style="text-align: right"
                                                >
                                                    <v-btn
                                                        small
                                                        color="success"
                                                        text
                                                        @click="
                                                            updateAccountRange(
                                                                management_fee_arr,
                                                                management_fee_arr_index,
                                                            )
                                                        "
                                                        >Update
                                                    </v-btn>
                                                    <v-icon
                                                        color="red"
                                                        @click="
                                                            deleteAccountRange(
                                                                management_fee_arr,
                                                                management_fee_arr_index,
                                                            )
                                                        "
                                                    >
                                                        close
                                                    </v-icon>
                                                </sui-table-cell>
                                            </sui-table-row>
                                        </sui-table-body>
                                        <sui-table-body>
                                            <sui-table-row class="form-row highlight">
                                                <sui-table-cell
                                                    class="form-input"
                                                    style="width: 5px !important"
                                                >
                                                    New
                                                </sui-table-cell>
                                                <sui-table-cell
                                                    class="form-input"
                                                    text-align="center"
                                                >
                                                    <multiselect
                                                        data-inverted=""
                                                        :data-tooltip="edit_form ? 'Account' : false"
                                                        openDirection="bottom"
                                                        v-model="new_account_from"
                                                        :options="management_fee_accounts_list"
                                                        :custom-label="nameWithDash"
                                                        :allowEmpty="false"
                                                        group-values="field_group_values"
                                                        group-label="field_group_names"
                                                        class="vue-select2 dropdown-left dropdown-300"
                                                        placeholder="Select an account"
                                                        track-by="field_key"
                                                        label="field_value"
                                                        :show-labels="false"
                                                        ><span slot="noResult"
                                                            >Oops! No elements found. Consider changing the search
                                                            query.</span
                                                        >
                                                    </multiselect>
                                                </sui-table-cell>
                                                <sui-table-cell
                                                    class="form-input"
                                                    text-align="center"
                                                >
                                                    <multiselect
                                                        data-inverted=""
                                                        :data-tooltip="edit_form ? 'Account' : false"
                                                        openDirection="bottom"
                                                        v-model="new_account_to"
                                                        :options="management_fee_accounts_list"
                                                        :custom-label="nameWithDash"
                                                        :allowEmpty="false"
                                                        group-values="field_group_values"
                                                        group-label="field_group_names"
                                                        class="vue-select2 dropdown-left dropdown-300"
                                                        placeholder="Select an account"
                                                        track-by="field_key"
                                                        label="field_value"
                                                        :show-labels="false"
                                                        ><span slot="noResult"
                                                            >Oops! No elements found. Consider changing the search
                                                            query.</span
                                                        >
                                                    </multiselect>
                                                </sui-table-cell>
                                                <sui-table-cell
                                                    class="form-input"
                                                    text-align="center"
                                                >
                                                    <cirrus-input
                                                        custom_class="cirrus-input-table-textbox"
                                                        inputFormat="percentage"
                                                        v-model="new_account_percentage"
                                                        size=""
                                                        :id="'account_percentage_new'"
                                                        data-inverted=""
                                                        :data-tooltip="edit_form ? 'Percentage' : false"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </sui-table-cell>
                                                <sui-table-cell
                                                    class="form-input"
                                                    style="text-align: right"
                                                >
                                                    <v-btn
                                                        small
                                                        color="success"
                                                        text
                                                        @click="addNewAccountRange(management_fee_arr)"
                                                        >Add New
                                                    </v-btn>
                                                </sui-table-cell>
                                            </sui-table-row>
                                        </sui-table-body>
                                    </sui-table>
                                </v-col>
                            </v-row>

                            <v-row
                                class="form-row"
                                v-if="management_fee_arr.man_fee_method_type === 1"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="management_fee_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Submit
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="management_fee_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteManagementFee(management_fee_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="management_fee_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';
import { fetchPropertyLedger } from '../../../../modules/Property/lib/ledger';
import {
    fetchManagementFeeData,
    saveManagementFeeData,
    updateManagementFeeData,
} from '../../../../modules/Property/lib/PropertyManagementFee';
import isNil from 'lodash/isNil';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_MANAGEMENT_FEES',
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            error_server_msg: {},
            error_server_msg_range: {},
            error_server_msg2: [],
            error_server_msg2_range: [],
            management_fee_list: [],
            management_fee_list_old: [],
            management_fee_arr: [],
            man_fee_account_list: [],
            management_fee_accounts_list: [],
            new_account_from: { field_key: '', field_value: 'Please select...' },
            new_account_to: { field_key: '', field_value: 'Please select...' },
            new_account_percentage: '0.00',
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Account', value: 'man_fee_account_desc' },
                { text: 'From', value: 'man_fee_start_date' },
                { text: 'To', value: 'man_fee_end_date' },
                { text: 'Method', value: 'man_fee_method_type_desc' },
                { text: 'Amount/Percentage', value: 'man_fee_amount_percentage', align: 'end' },
                {
                    text: '',
                    value: 'action1',
                    class: ['data-table-mini-action'],
                    align: 'end',
                    sortable: false,
                    width: '78px',
                },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            expanded: [],
            AED_modal: false,
            success_flag: false,
            modal_current_ctr: 0,
            currency_symbol: '$',
            property_ledger_list: [],
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                { text: 'Account', value: 'man_fee_account_desc', width: 'auto' },
                { text: 'From', value: 'man_fee_start_date', width: 'auto' },
                { text: 'To', value: 'man_fee_end_date', width: 'auto' },
                { text: 'Method', value: 'man_fee_method_type_desc', width: 'auto' },
                { text: 'Amount/Percentage', value: 'man_fee_amount_percentage', align: 'end', width: 'auto' },
            ];
            this.items_per_page = 9999;
        }
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            if (this.property_code !== '') {
                this.loadLedgerList();
                this.loadManagementFees();
            }
        },
        async loadManagementFees() {
            this.loading_setting = true;
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                const data = await fetchManagementFeeData(form_data);
                this.loadResponseToVariables(data);
                this.edit_form = false;
            } else {
                let apiUrl = 'temp/property/fetch/management-fees';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response.data);
                    if (this.new_property) this.edit_form = true;
                    else this.edit_form = false;
                });
            }
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.management_fee_list = response.management_fee_list;
            this.currency_symbol = response.currency_symbol;
        },
        modalPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            let current_index = this.management_fee_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.management_fee_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.management_fee_arr = this.management_fee_list[this.modal_current_ctr];
            this.management_fee_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            let current_index = this.management_fee_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.management_fee_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.management_fee_arr = this.management_fee_list[this.modal_current_ctr];
            this.management_fee_arr.index = this.modal_current_ctr;
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            this.management_fee_arr = this.management_fee_list[index];
            this.management_fee_arr.index = index;
            this.modal_current_ctr = index;
        },
        modalAddData: function () {
            this.edit_form = true;
            this.AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            var d = new Date();
            this.management_fee_arr = {
                index: 'New',
                man_fee_id: null,
                man_fee_account_code: '',
                man_fee_account: { field_key: '', field_value: '' },
                man_fee_start_date: '01/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                man_fee_end_date: '31/12/2999',
                man_fee_method_type: 1,
                man_fee_amount: '0.00',
                man_fee_percentage: '0.00',
                man_fee_account_range_list: [],
                propertyLedgerId: '',
                status: 'new',
            };
        },
        loadManagementFeeParameters: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            this.$api.post('ui/fetch/accounts-exp-list', form_data).then((response) => {
                this.man_fee_account_list = response.data.grouped;
            });
            this.$api.post('ui/fetch/accounts-inc-list', form_data).then((response) => {
                this.management_fee_accounts_list = response.data.grouped;
            });
        },
        async deleteManagementFee(index) {
            if (index !== 'New') {
                if (this.management_fee_list.length === 1) {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'You cannot delete this management fee.',
                        icon_show: true,
                    };
                    cirrusDialog(dialog_prop);
                    return;
                }
                let status = this.management_fee_list[index].status;
                let man_fee_id = this.management_fee_list[index].man_fee_id;
                this.success_flag = false;
                if (status === 'new') {
                    this.management_fee_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('man_fee_id', man_fee_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/management-fee';
                        } else {
                            apiUrl = 'temp/property/delete/management-fee';
                        }
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.management_fee_list.splice(index, 1);
                            this.loading_setting = false;
                            this.management_fee_list_old = JSON.parse(JSON.stringify(this.management_fee_list));
                            this.loadManagementFees();
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        });
                    }
                }
            }
        },
        addNewAccountRange: function (management_fee_arr) {
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            let index = management_fee_arr.index;
            let man_fee_id = management_fee_arr.man_fee_id;
            let man_fee_account_range_list = management_fee_arr.man_fee_account_range_list;
            let new_account_from = this.new_account_from.field_key;
            let new_account_to = this.new_account_to.field_key;
            let new_account_percentage = this.new_account_percentage;
            let error_server_msg2 = [];
            this.success_flag = false;
            if (new_account_from === '') error_server_msg2.push(['You have not specified a valid account from.']);
            if (new_account_to === '') error_server_msg2.push(['You have not specified a valid account to.']);
            if (new_account_percentage === '')
                error_server_msg2.push(['You have not specified a valid account percentage.']);
            this.error_server_msg2_range = error_server_msg2;
            if (this.error_server_msg2_range.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('man_fee_id', man_fee_id);
                form_data.append('new_account_from', new_account_from);
                form_data.append('new_account_to', new_account_to);
                form_data.append('new_account_percentage', new_account_percentage);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/create/management-fee-range';
                } else {
                    apiUrl = 'temp/property/create/management-fee-range';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    let error_server_msg2 = response.data.error_server_msg2;
                    if (error_server_msg2.length > 0) {
                        this.error_server_msg2 = error_server_msg2;
                    } else {
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                        this.error_server_msg_range = {};
                        this.error_server_msg2_range = [];
                        this.management_fee_arr.man_fee_account_range_list = response.data.man_fee_account_range_list;

                        if (this.error_server_msg2.length === 0) {
                            this.new_account_from = { field_key: '', field_value: 'Please select...' };
                            this.new_account_to = { field_key: '', field_value: 'Please select...' };
                            this.new_account_percentage = '0.00';
                            this.loadManagementFees();
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        }
                    }
                    this.loading_setting = false;
                });
            }
        },
        updateAccountRange: function (management_fee_arr, management_fee_arr_index) {
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            let index = management_fee_arr.index;
            let man_fee_id = management_fee_arr.man_fee_id;
            let man_fee_account_range_data = management_fee_arr.man_fee_account_range_list[management_fee_arr_index];
            let man_acc_id = man_fee_account_range_data.man_acc_id;
            let account_from_code = man_fee_account_range_data.account_from.field_key;
            let account_to_code = man_fee_account_range_data.account_to.field_key;
            let account_percentage = man_fee_account_range_data.account_percentage;
            let error_server_msg2 = [];
            this.success_flag = false;
            if (account_from_code === '') error_server_msg2.push(['You have not specified a valid account from.']);
            if (account_to_code === '') error_server_msg2.push(['You have not specified a valid account to.']);
            if (account_percentage === '')
                error_server_msg2.push(['You have not specified a valid account percentage.']);
            this.error_server_msg2_range = error_server_msg2;
            if (this.error_server_msg2_range.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('man_fee_id', man_fee_id);
                form_data.append('man_acc_id', man_acc_id);
                form_data.append('account_from_code', account_from_code);
                form_data.append('account_to_code', account_to_code);
                form_data.append('account_percentage', account_percentage);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/update/management-fee-range';
                } else {
                    apiUrl = 'temp/property/update/management-fee-range';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    let error_server_msg2 = response.data.error_server_msg2;
                    if (error_server_msg2.length > 0) {
                        this.error_server_msg2 = error_server_msg2;
                    } else {
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                        this.error_server_msg_range = {};
                        this.error_server_msg2_range = [];
                        this.management_fee_arr.man_fee_account_range_list = response.data.man_fee_account_range_list;
                        this.management_fee_list[index].man_fee_account_range_list =
                            response.data.man_fee_account_range_list;
                        this.management_fee_list[index].man_fee_account_range_tot_perc =
                            response.data.total_percentage_acc_range;

                        if (this.error_server_msg2.length === 0) {
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        }
                    }
                    this.loading_setting = false;
                });
            }
        },
        async deleteAccountRange(management_fee_arr, management_fee_arr_index) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                let index = management_fee_arr.index;
                let man_fee_id = management_fee_arr.man_fee_id;
                let man_fee_account_range_list =
                    management_fee_arr.man_fee_account_range_list[management_fee_arr_index];
                let account_from_code = man_fee_account_range_list.account_from_code;
                let account_to_code = man_fee_account_range_list.account_to_code;
                this.success_flag = false;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('man_fee_id', man_fee_id);
                form_data.append('account_from_code', account_from_code);
                form_data.append('account_to_code', account_to_code);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/delete/management-fee-range';
                } else {
                    apiUrl = 'temp/property/delete/management-fee-range';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.management_fee_arr.man_fee_account_range_list.splice(management_fee_arr_index, 1);
                    this.loading_setting = false;
                    this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        async modalSubmitData() {
            let index = this.management_fee_arr.index;
            let man_fee_id = this.management_fee_arr.man_fee_id;
            let man_fee_account_code = this.management_fee_arr.man_fee_account.field_key;
            let propertyLedgerId = this.management_fee_arr.propertyLedgerId;
            let man_fee_start_date = this.management_fee_arr.man_fee_start_date;
            let man_fee_end_date = this.management_fee_arr.man_fee_end_date;
            let man_fee_method_type = this.management_fee_arr.man_fee_method_type;
            let man_fee_amount = this.management_fee_arr.man_fee_amount;
            let man_fee_percentage = this.management_fee_arr.man_fee_percentage;
            let man_fee_account_range_list = this.management_fee_arr.man_fee_account_range_list;
            let status = this.management_fee_arr.status;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.error_server_msg_range = {};
            this.error_server_msg2_range = [];
            this.success_flag = false;
            let error_server_msg2 = [];
            if (man_fee_method_type === null) error_server_msg2.push(['You have not specified a valid method.']);
            if (man_fee_account_code === '') error_server_msg2.push(['You have not specified a valid account.']);
            if (man_fee_start_date === '') error_server_msg2.push(['You have not specified a valid start date.']);
            if (man_fee_end_date === '') error_server_msg2.push(['You have not specified a valid end date.']);

            switch (man_fee_method_type) {
                case 2:
                    if (man_fee_amount === '') error_server_msg2.push(['You have not specified a valid amount.']);
                    else if (isNaN(parseFloat(man_fee_amount)))
                        error_server_msg2.push(['You have not entered a valid amount.']);
                    else if (parseFloat(man_fee_amount) < 0)
                        error_server_msg2.push(['You have not entered a valid amount.']);
                    break;
                case 3:
                    if (man_fee_percentage === '')
                        error_server_msg2.push(['You have not specified a valid percentage.']);
                    else if (isNaN(parseFloat(man_fee_percentage)))
                        error_server_msg2.push(['You have not entered a valid percentage.']);
                    else if (parseFloat(man_fee_percentage) < 0)
                        error_server_msg2.push(['You have not entered a valid percentage.']);
                    break;
                case 5:
                    if (man_fee_percentage === '')
                        error_server_msg2.push(['You have not specified a valid percentage.']);
                    else if (isNaN(parseFloat(man_fee_percentage)))
                        error_server_msg2.push(['You have not entered a valid percentage.']);
                    else if (parseFloat(man_fee_percentage) < 0)
                        error_server_msg2.push(['You have not entered a valid percentage.']);
                    break;
            }
            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('man_fee_id', man_fee_id);
                form_data.append('man_fee_account_code', man_fee_account_code);
                form_data.append('propertyLedgerId', propertyLedgerId);
                form_data.append('man_fee_start_date', man_fee_start_date);
                form_data.append('man_fee_end_date', man_fee_end_date);
                form_data.append('man_fee_method_type', man_fee_method_type);
                form_data.append('man_fee_amount', man_fee_amount);
                form_data.append('man_fee_percentage', man_fee_percentage);
                form_data.append('status', status);
                form_data.append('no_load', true);
                if (this.isPropertyFormLive()) {
                    try {
                        let data;
                        if (isNil(man_fee_id)) {
                            data = await saveManagementFeeData(form_data);
                        } else {
                            data = await updateManagementFeeData(form_data);
                        }
                        if (data) {
                            this.loadForm();
                        }
                    } catch (errors) {
                        if (typeof errors === 'object') {
                            this.error_server_msg2 = [Object.values(errors).flat()];
                        } else {
                            this.error_server_msg2 = ['An unexpected error occurred'];
                        }
                    }
                } else {
                    let apiUrl = 'temp/property/update-or-create/management-fee';
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.error_server_msg2 = response.data.error_server_msg2;
                        if (!this.new_property) this.edit_form = false;
                        this.loading_setting = false;
                        if (this.error_server_msg2.length === 0) {
                            this.loadForm();
                            this.success_flag = true;
                            this.AED_modal = false;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        }
                    });
                }
            }
        },
        nameWithCodeDash({ code, field_value }) {
            if (`${code}` === '') return `${field_value}`;
            return `${code} — ${field_value}`;
        },
        isMultiplePropertyLedger() {
            return !!this.sys_ver_control_list?.isMultiplePropertyLedger;
        },
        async loadLedgerList() {
            this.property_ledger_list = [];
            try {
                const ledgerList = await fetchPropertyLedger(this.property_code);
                // Normalize possible response shapes, then map to [{ field_key, field_value }]
                const list = Array.isArray(ledgerList)
                    ? ledgerList
                    : Array.isArray(ledgerList?.data)
                      ? ledgerList.data
                      : Array.isArray(ledgerList?.list)
                        ? ledgerList.list
                        : [];
                this.property_ledger_list = list.map(({ id, propertyLedgerCode, description }) => ({
                    field_key: id,
                    code: propertyLedgerCode,
                    field_value: description,
                }));
            } catch (err) {}
        },
    },
    created() {
        bus.$on('loadPropertyManagementFeeFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.loadManagementFeeParameters();
            }
        },
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_property_form_read_only',
            'sys_ver_control_list',
        ]),
        ...mapGetters(['getDDCountryStates']),
    },
    mixins: [global_mixins],
};
</script>
