<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Management Details</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                    v-if="edit_form"
                    class="v-step-save-1-button"
                    icon
                    @click="saveForm()"
                >
                    <v-icon
                        light
                        color="green"
                        >check</v-icon
                    >
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    v-if="isEditable()"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>

        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Property Agent:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                &nbsp&nbsp
                            </td>
                            <td>
                                <cirrus-single-select-v2
                                    v-if="edit_form"
                                    v-model="man_prop_agent"
                                    :options="property_agent_list"
                                    ref="refPropertyType"
                                    trackBy="field_key"
                                    label="field_key_w_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                                <span
                                    v-if="!edit_form && man_prop_agent !== ''"
                                    class="form-input-text"
                                    >{{ man_prop_agent }} -
                                    {{ getDropdownName(man_prop_agent, property_agent_list) }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>

            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Remittance Office:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                &nbsp&nbsp
                            </td>
                            <td>
                                <cirrus-single-select-v2
                                    v-if="edit_form"
                                    v-model="man_remittance_office"
                                    :options="remittance_office_list"
                                    ref="refPropertyType"
                                    trackBy="field_key"
                                    label="field_key_w_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                                <span
                                    v-if="!edit_form && man_remittance_office !== ''"
                                    class="form-input-text"
                                    >{{ man_remittance_office }} -
                                    {{ getDropdownName(man_remittance_office, remittance_office_list) }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
        </div>
        <v-divider></v-divider>
        <v-card
            elevation="0"
            v-if="edit_form"
        >
            <v-card-actions v-if="edit_form">
                <v-spacer></v-spacer>
                <v-btn
                    class="v-step-save-2-button"
                    @click="saveForm()"
                    color="success"
                    dark
                    small
                >
                    Save Details
                </v-btn>
            </v-card-actions>
        </v-card>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        edit_flag: { type: Boolean, default: false },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_MANAGEMENT_DETAILS',
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            man_prop_agent: '',
            man_remittance_office: '',
            man_accounting_basis_index: 0,
            property_agent_list: [],
            property_manager_list: [],
            principal_owner_list: [],
            property_type_list: [],
            remittance_office_list: [],
            property_group_list: [],
            lease_type_list: [],
            old_property_details: {},
            property_inspection_index: 1,
            property_inspection_amount: '0.00',
            property_inspection_payment_account: { field_key: '', field_value: '' },
            inspection_payment_account_list: [],
            property_inspection_frequency_index: 0,
            property_inspection_recoverable_index: 1,
            property_inspection_recoverable_account: { field_key: '', field_value: '' },
            inspection_recoverable_account_list: [],
            property_rent_review_index: 1,
            property_rent_review_amount: '0.00',
            property_rent_review_description: '',
            property_rent_review_expenses_account: { field_key: '', field_value: '' },
        };
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        resetForm: function () {
            this.edit_form = false;
            if (Object.keys(this.old_property_details).length > 0) {
                this.man_prop_agent = this.old_property_details.man_prop_agent;
                this.man_prop_manager = this.old_property_details.man_prop_manager;
                this.man_principal_owner = this.old_property_details.man_principal_owner;
                this.man_prop_type = this.old_property_details.man_prop_type;
                this.man_remittance_office = this.old_property_details.man_remittance_office;
                this.man_prop_group = this.old_property_details.man_prop_group;
                this.man_lease_type = this.old_property_details.man_lease_type;
                this.man_accounting_basis_index = this.old_property_details.man_accounting_basis_index;
            }
        },
        loadForm: function () {
            this.error_server_msg = {};
            if (this.property_code !== '') {
                this.loadManagementDetails();
            }
            if (!this.new_property) this.edit_form = false;
        },
        saveForm: function () {
            this.error_server_msg2 = [];
            let man_accounting_basis_index = this.man_accounting_basis_index;
            let man_accounting_basis = 'C';
            if (man_accounting_basis_index === 1) man_accounting_basis = 'A';
            let man_prop_agent = this.man_prop_agent;
            let man_remittance_office = this.man_remittance_office;
            let error_server_msg2 = [];
            this.error_server_msg2 = error_server_msg2;
            if (error_server_msg2.length === 0) {
                this.loading_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('man_remittance_office', man_remittance_office);
                form_data.append('man_prop_agent', man_prop_agent);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/update/management-details';
                } else {
                    apiUrl = 'temp/property/update/management-details';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loading_setting = false;
                    this.error_server_msg = response.data.error_server_msg;
                });
            }
        },
        loadManagementDetails: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/management-details', form_data).then((response) => {
                    this.loadResponseToVariables(response);
                    if (this.new_property) this.edit_form = true;
                    else this.edit_form = false;
                });
            } else {
                let apiUrl = 'temp/property/fetch/management-details';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);
                    if (this.new_property) this.edit_form = true;
                    else this.edit_form = false;
                });
            }
        },
        loadManagementDetailsLists: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            this.$api.post('property/fetch/management-details-lists', form_data).then((response) => {
                this.property_agent_list = response.data.property_agent_list;
                this.remittance_office_list = response.data.remittance_office_list;
            });
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            let lc_man_accounting_basis = response.data.man_accounting_basis;
            let lc_man_lease_type = response.data.man_lease_type;
            let lc_man_principal_owner = response.data.man_principal_owner;
            let lc_man_prop_agent = response.data.man_prop_agent;
            let lc_man_prop_group = response.data.man_prop_group;
            let lc_man_prop_manager = response.data.man_prop_manager;
            let lc_man_prop_type = response.data.man_prop_type;
            let lc_man_remittance_office = response.data.man_remittance_office;

            if (lc_man_accounting_basis === 'A') {
                this.man_accounting_basis_index = 1;
            } else {
                this.man_accounting_basis_index = 0;
            }

            this.man_lease_type = lc_man_lease_type;
            this.man_principal_owner = lc_man_principal_owner;
            this.man_prop_agent = lc_man_prop_agent;
            this.man_prop_group = lc_man_prop_group;
            this.man_prop_manager = lc_man_prop_manager;
            this.man_prop_type = lc_man_prop_type;
            this.man_remittance_office = lc_man_remittance_office;

            this.property_inspection_index = response.data.property_inspection_index;
            this.property_inspection_amount = response.data.property_inspection_amount;
            this.property_inspection_payment_account = response.data.property_inspection_payment_account;
            this.property_inspection_frequency_index = response.data.property_inspection_frequency_index;
            this.property_inspection_recoverable_index = response.data.property_inspection_recoverable_index;
            this.property_inspection_recoverable_account = response.data.property_inspection_recoverable_account;
            this.property_rent_review_index = response.data.property_rent_review_index;
            if (this.property_rent_review_index === 0) {
                this.property_rent_review_amount = response.data.property_rent_review_amount;
                this.property_rent_review_description = response.data.property_rent_review_description;
                this.property_rent_review_expenses_account = response.data.property_rent_review_expenses_account;
            }
        },
        loadPropertyDetailsLists: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            this.$api.post('property/fetch/property-details-lists', form_data).then((response) => {
                this.bank_account_list = response.data.bank_account_list;
                this.report_type_list = response.data.report_type_list;
                this.property_bond_list = response.data.property_bond_list;
                this.inspection_payment_account_list = response.data.inspection_payment_account_list;
                this.inspection_recoverable_account_list = response.data.inspection_recoverable_account_list;
            });
        },
        getDropdownName(code, options) {
            let label = code ? code : '';
            options.forEach((row) => {
                if (row.field_key) if (row.field_key.trim() === code.trim()) label = row.field_value;
            });
            return label;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
            }
        },
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
        ...mapGetters(['getDDCountryStates']),
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        this.loadManagementDetailsLists();
        this.loadPropertyDetailsLists();
    },
    created() {
        bus.$on('loadPropertyManagementDetailFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
