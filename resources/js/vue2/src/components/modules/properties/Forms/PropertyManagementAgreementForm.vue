<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Management Agreements</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                    class="v-step-refresh-button"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>

        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="management_agreement_list.length === 0 && !loading_setting"
            v-show="isEditable()"
        >
            <v-btn
                v-if="!pmro_read_only"
                depressed
                small
                color="success"
                @click="modalAddData()"
                >Add Management Agreement</v-btn
            >
            <div
                style="margin: 10px 0px"
                v-else
            >
                No management agreements at the moment
            </div>
        </v-col>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="management_agreement_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="management_agreement_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ management_agreement_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_agreement_description="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.man_agreement_description }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_agreement_start_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.man_agreement_start_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_agreement_end_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.man_agreement_end_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.man_agreement_diarise_flag="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><label>{{ item.man_agreement_diarise_flag ? '' : 'Not ' }}Diarised</label></span
                            >
                        </div>
                    </template>
                    <template v-slot:item.man_agreement_file="{ item }">
                        <cirrus-single-upload-button2
                            v-if="item.on_s3 != 1"
                            :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                            v-model="item.man_agreement_file"
                            accept_type="pdf"
                            :size_limit="20"
                            :has_saved_file="
                                item.man_agreement_file_old !== '' &&
                                (typeof item.man_agreement_file_old === 'string' ||
                                    item.man_agreement_file_old instanceof String)
                                    ? true
                                    : false
                            "
                            :edit_form="false"
                        ></cirrus-single-upload-button2>

                        <a
                            target="_blank"
                            v-if="item.on_s3 == 1"
                            v-on:click="downloadS3File(item.s3_filepath, item.s3_filename, 'pdf')"
                            ><img
                                :src="asset_domain + 'assets/images/icons/pdf.png'"
                                alt="Adobe Logo"
                                class="icon"
                                style="width: 19px"
                            />&nbsp; Download</a
                        >
                    </template>
                    <template v-slot:item.publish_to_owner="{ item }">
                        <span v-if="item.publish_to_owner">Published to owner</span>
                        <span v-if="!item.publish_to_owner">Not Published to owner</span>
                    </template>
                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenAED(management_agreement_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit
                        </v-icon>
                        <v-icon
                            color="red"
                            v-if="edit_form"
                            @click="deleteManagementAgreement(management_agreement_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="management_agreement_list.length > 5"
                    v-if="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteManagementAgreement(management_agreement_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Management Agreement Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="management_agreement_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        management_agreement_arr.index === 'New'
                                            ? management_agreement_arr.index
                                            : management_agreement_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Description</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="management_agreement_arr.man_agreement_description"
                                        size=""
                                        :id="'insurance_insurer'"
                                        data-inverted=""
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Start Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'management_agreement_start_date' + String(Math.random()).replace('.', '')"
                                        v-model="management_agreement_arr.man_agreement_start_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Start Date' : false"
                                        :edit_form="true"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >End Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'management_agreement_end_date' + String(Math.random()).replace('.', '')"
                                        v-model="management_agreement_arr.man_agreement_end_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'End Date' : false"
                                        :edit_form="true"
                                    ></cirrus-icon-date-picker>
                                    <span
                                        style="position: absolute; margin-top: 4px"
                                        v-bind:class="management_agreement_arr.man_agreement_diarise_id ? 'hidden' : ''"
                                    >
                                        <v-checkbox
                                            v-model="management_agreement_arr.man_agreement_diarise_flag"
                                            :light="true"
                                            label="Diarise"
                                            ripple="false"
                                            dense
                                        ></v-checkbox>
                                    </span>
                                    <span
                                        style="position: absolute; margin-top: 4px; padding-left: 5px"
                                        v-bind:class="
                                            !management_agreement_arr.man_agreement_diarise_id ? 'hidden' : ''
                                        "
                                    >
                                        Diarised
                                    </span>
                                    <input
                                        type="hidden"
                                        v-model="management_agreement_arr.man_agreement_diarise_flag"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Publish to owner</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <sui-checkbox
                                        v-model="management_agreement_arr.publish_to_owner"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Publish to Owner' : false"
                                    />
                                    <input
                                        type="hidden"
                                        v-model="management_agreement_arr.publish_to_owner"
                                        :id="'man_agreement_diarise_flag'"
                                        :edit_form="true"
                                    />
                                </v-col>
                            </v-row>
                            <!--              <v-row class="form-row">-->
                            <!--                <v-col xs="12" sm="2" md="2" class="form-label">Diarise</v-col>-->
                            <!--                <v-col xs="12" sm="10" md="10" class="form-input">-->
                            <!--                  <div v-bind:class="management_agreement_arr.man_agreement_diarise_id ? 'hidden' : ''">-->
                            <!--                    <sui-checkbox v-model="management_agreement_arr.man_agreement_diarise_flag" data-inverted=""-->
                            <!--                                  :data-tooltip="edit_form ? 'Diarise' : false"/>-->
                            <!--                  </div>-->
                            <!--                  <label class="form-input-text"-->
                            <!--                         v-bind:class="!management_agreement_arr.man_agreement_diarise_id ? 'hidden' : ''">{{-->
                            <!--                      management_agreement_arr.man_agreement_diarise_id ? '' : 'Not '-->
                            <!--                    }}Diarised</label>-->
                            <!--                  <input type="hidden" v-model="management_agreement_arr.man_agreement_diarise_flag"-->
                            <!--                         :id="'man_agreement_diarise_flag'" :edit_form="true">-->
                            <!--                </v-col>-->
                            <!--              </v-row>-->

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >File(s)</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-upload-button2
                                        :id="'lease_management_agreement_file_' + management_agreement_arr.index"
                                        v-model="management_agreement_arr.man_agreement_file"
                                        accept_type="pdf"
                                        :size_limit="20"
                                        :has_saved_file="
                                            management_agreement_arr.man_agreement_file_old !== '' &&
                                            (typeof management_agreement_arr.man_agreement_file_old === 'string' ||
                                                management_agreement_arr.man_agreement_file_old instanceof String)
                                                ? true
                                                : false
                                        "
                                        :edit_form="true"
                                    ></cirrus-single-upload-button2>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="management_agreement_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="management_agreement_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteManagementAgreement(management_agreement_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="management_agreement_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_MANAGEMENT_AGREEMENTS',
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            management_agreement_list: [],
            management_agreement_arr: [],
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Description', value: 'man_agreement_description' },
                { text: 'Start Date', value: 'man_agreement_start_date_raw' },
                { text: 'To Date', value: 'man_agreement_end_date_raw' },
                { text: 'Diarise', value: 'man_agreement_diarise_flag' },
                { text: 'File', value: 'man_agreement_file' },
                { text: '', value: 'publish_to_owner' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            AED_modal: false,
            success_flag: false,
            modal_current_ctr: 0,
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                { text: 'Description', value: 'man_agreement_description', width: 'auto' },
                { text: 'Start Date', value: 'man_agreement_start_date', width: 'auto' },
                { text: 'To Date', value: 'man_agreement_end_date', width: 'auto' },
                { text: 'Diarise', value: 'man_agreement_diarise_flag', width: 'auto' },
                { text: 'File', value: 'man_agreement_file', width: 'auto' },
                { text: '', value: 'publish_to_owner', width: 'auto' },
            ];
            this.items_per_page = 9999;
        }
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        resetForm: function () {
            this.edit_form = false;
            if (Object.keys(this.old_property_details).length > 0) {
                this.man_prop_agent = this.old_property_details.man_prop_agent;
                this.man_prop_manager = this.old_property_details.man_prop_manager;
                this.man_principal_owner = this.old_property_details.man_principal_owner;
                this.man_prop_type = this.old_property_details.man_prop_type;
                this.man_remittance_office = this.old_property_details.man_remittance_office;
                this.man_prop_group = this.old_property_details.man_prop_group;
                this.man_lease_type = this.old_property_details.man_lease_type;
                this.man_accounting_basis_index = this.old_property_details.man_accounting_basis_index;
            }
        },
        loadForm: function () {
            this.error_server_msg = {};
            if (this.property_code !== '') {
                this.loadManagementAgreement();
            }
        },
        modalSubmitData: function () {
            this.error_server_msg2 = [];
            let man_agreement_id = this.management_agreement_arr.man_agreement_id;
            let man_agreement_description = this.management_agreement_arr.man_agreement_description;
            let man_agreement_start_date = this.management_agreement_arr.man_agreement_start_date;
            let man_agreement_end_date = this.management_agreement_arr.man_agreement_end_date;
            let man_agreement_file = this.management_agreement_arr.man_agreement_file;
            let publish_to_owner = this.management_agreement_arr.publish_to_owner;
            let man_agreement_diarise_flag = this.management_agreement_arr.man_agreement_diarise_flag;
            if (man_agreement_diarise_flag) man_agreement_diarise_flag = 1;
            else man_agreement_diarise_flag = 0;
            let man_agreement_diarise_id = this.management_agreement_arr.man_agreement_diarise_id;
            let status = this.management_agreement_arr.status;
            man_agreement_file = man_agreement_file != null ? man_agreement_file[0] : null;
            let error_server_msg2 = [];
            if (man_agreement_start_date === '' || man_agreement_start_date === null)
                error_server_msg2.push(['You have not specified a valid start date.']);
            if (man_agreement_end_date === '' || man_agreement_end_date === null)
                error_server_msg2.push(['You have not specified a valid end date.']);
            if (man_agreement_description === '')
                error_server_msg2.push(['You have not specified a valid description.']);
            this.error_server_msg2 = error_server_msg2;
            if (error_server_msg2.length === 0) {
                this.loading_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('man_agreement_id', man_agreement_id);
                form_data.append('man_agreement_description', man_agreement_description);
                form_data.append('man_agreement_start_date', man_agreement_start_date);
                form_data.append('man_agreement_end_date', man_agreement_end_date);
                form_data.append('man_agreement_file', man_agreement_file);
                form_data.append('publish_to_owner', publish_to_owner);
                form_data.append('man_agreement_diarise_flag', man_agreement_diarise_flag);
                form_data.append('man_agreement_diarise_id', man_agreement_diarise_id);
                form_data.append('status', status);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'with-file-upload/property/update-or-create/management-agreement';
                } else {
                    apiUrl = 'with-file-upload/temp/property/update-or-create/management-agreement';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loading_setting = false;
                    if (!this.new_property) {
                        this.edit_form = false;
                    }
                    this.error_server_msg2 = response.data.error_server_msg2;
                    if (this.error_server_msg2.length === 0) {
                        this.success_flag = true;
                        if (this.management_agreement_arr.index === 'New')
                            this.management_agreement_arr.index = this.management_agreement_list.length;
                        this.management_agreement_arr.man_agreement_id = response.data.man_agreement_id;
                        this.management_agreement_arr.status = 'saved';
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                        this.loadForm();
                    }
                });
            }
        },
        loadManagementAgreement: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/management-agreements', form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            } else {
                let apiUrl = 'temp/property/fetch/management-agreements';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);
                    if (this.new_property) this.edit_form = true;
                    else this.edit_form = false;
                });
            }
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.management_agreement_list = response.data.management_agreement_list;
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadManagement_' + id;
        },
        async deleteManagementAgreement(index) {
            if (index !== 'New') {
                let status = this.management_agreement_list[index].status;
                let man_agreement_id = this.management_agreement_list[index].man_agreement_id;

                if (status === 'new') {
                    this.management_agreement_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('lease_code', this.lease_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('man_agreement_id', man_agreement_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/management-agreement';
                        } else {
                            apiUrl = 'temp/property/delete/management-agreement';
                        }
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.management_agreement_list.splice(index, 1);
                            this.loading_setting = false;
                            if (response.data.load_diary_list_flag) {
                                //emit diary component
                            }
                            this.management_agreement_list_old = JSON.parse(
                                JSON.stringify(this.management_agreement_list),
                            );
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        });
                    }
                }
            }
        },
        modalPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.management_agreement_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.management_agreement_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.management_agreement_arr = this.management_agreement_list[this.modal_current_ctr];
            this.management_agreement_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.management_agreement_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.management_agreement_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.management_agreement_arr = this.management_agreement_list[this.modal_current_ctr];
            this.management_agreement_arr.index = this.modal_current_ctr;
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.management_agreement_arr = this.management_agreement_list[index];
            this.management_agreement_arr.index = index;
            this.modal_current_ctr = index;
            bus.$emit('refreshSingleUploadComponent', '');
        },
        modalAddData: function () {
            this.edit_form = true;
            this.AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.management_agreement_arr = {
                index: 'New',
                man_agreement_id: '',
                man_agreement_description: '',
                man_agreement_start_date:
                    d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                man_agreement_end_date:
                    d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                man_agreement_file: null,
                man_agreement_file_old: null,
                publish_to_owner: false,
                man_agreement_diarise_flag: this.auto_diarise,
                man_agreement_diarise_id: '',
                status: 'new',
            };
            bus.$emit('refreshSingleUploadComponent', '');
        },
        downloadS3File: function (this_file, this_file_name, file_type) {
            //console.log(this_file, this_file_name, file_type); //uncomment for testing only
            // this.workOrdersLoading = true;
            let params = new FormData();

            params.append('un', this.username);
            params.append('currentDB', this.current_db);
            params.append('user_type', this.user_type);

            params.append('s3path', this_file);

            axios.post(this.cirrus8_api_url + 'api/property/fetch/download-s3', params).then((response) => {
                if (response.data.data.file) {
                    this.downloadFile(response.data.data.file, this_file_name, file_type);
                }
            });
        },
        downloadFile(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;
            let blob = new Blob([this.s2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            // a.download = name+'.'+format;
            a.download = name;
            a.click();
        },
        s2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
            }
        },
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_property_form_read_only',
            'auto_diarise',
        ]),
        ...mapGetters(['getDDCountryStates']),
    },
    created() {
        bus.$on('loadPropertyManagementAgreementFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
