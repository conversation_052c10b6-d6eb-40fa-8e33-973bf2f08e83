<template>
    <div
        class="property-keys"
        v-on:dblclick="doubleClickForm()"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Keys</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable()"
                    class="v-step-refresh-button"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="property_keys_list.length === 0 && !loading_setting"
            v-show="isEditable()"
        >
            <v-btn
                v-if="!pmro_read_only"
                depressed
                small
                color="success"
                @click="modalAddData()"
                >Add Key
            </v-btn>
            <div
                style="margin: 10px 0px"
                v-else
            >
                No keys at the moment
            </div>
        </v-col>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="property_keys_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="property_keys_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ property_keys_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.key_reference="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.key_reference }}</span>
                        </div>
                    </template>
                    <template v-slot:item.key_description="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.key_description }}</span>
                        </div>
                    </template>
                    <template v-slot:item.key_status="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.key_status }}</span>
                        </div>
                    </template>
                    <template v-slot:item.key_taker="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.key_status_code === 'O'"
                                >{{ item.key_taker }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.key_check_out_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.key_status_code === 'O'"
                                >{{ item.key_check_out }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.key_return_due_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.key_status_code === 'O'"
                                >{{ item.key_return_due }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.key_date_return_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.key_date_return }}</span>
                        </div>
                    </template>
                    <template v-slot:item.key_tenant_code="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.key_status_code === 'O'"
                            >
                                <a @click="openCompanyInfoModal(item.key_tenant_code)">{{ item.key_tenant_code }}</a>
                            </span>
                        </div>
                    </template>
                    <template v-slot:item.tooltip="{ item }">
                        <v-icon
                            style="font-size: 21px; position: relative; top: -1px"
                            :title="keyHoverDesc(item, 0)"
                        >
                            info
                        </v-icon>
                    </template>
                    <template v-slot:item.action2="{ item }">
                        <div class="form-row no-border-line">
                            <v-btn
                                v-if="edit_form"
                                x-small
                                @click="openKeyHistory(property_keys_list.indexOf(item))"
                            >
                                Show History
                            </v-btn>
                        </div>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-btn
                            v-if="edit_form && item.key_status_code === 'O' && !new_property"
                            x-small
                            @click="modalOpenAED(property_keys_list.indexOf(item), 'CHECK-IN')"
                            >Check In
                        </v-btn>
                        <v-btn
                            v-if="edit_form && item.key_status_code === 'I' && !new_property"
                            x-small
                            @click="modalOpenAED(property_keys_list.indexOf(item), 'CHECK-OUT')"
                            >Check Out
                        </v-btn>
                        <v-icon
                            small
                            @click="modalOpenAED(property_keys_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit
                        </v-icon>
                        <v-icon
                            color="red"
                            v-if="edit_form && item.key_status_code !== 'X'"
                            @click="deleteCheckPropertyKey('table', property_keys_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="property_keys_list.length > 5"
                    v-if="isEditable()"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>

        <!--   AED modal      -->
        <v-dialog
            v-model="show_tenant_link_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Company Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_tenant_link_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="6"
                                    md="6"
                                >
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Company:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                <strong
                                                    ><a
                                                        @click="
                                                            goToCompanyShortcut(
                                                                company_details_arr.company_code,
                                                                '',
                                                                '',
                                                            )
                                                        "
                                                        >{{ company_details_arr.company_code }}</a
                                                    >
                                                    : {{ company_details_arr.company_name }}</strong
                                                >
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Owner Address:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_street }}
                                                {{ company_details_arr.company_city }}
                                                {{ company_details_arr.company_state }}
                                                {{ company_details_arr.company_postcode }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Email Address:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                            v-if="company_details_arr.company_email"
                                        >
                                            <div
                                                v-for="(data, index) in breakdownEmail(
                                                    company_details_arr.company_email,
                                                )"
                                                :key="index"
                                                v-html="linkLocationFormat('email', data)"
                                            ></div>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                        >
                                            {{ company_details_arr.company_business_label }}:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_business_prefix
                                                }}{{ company_details_arr.company_abn }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >{{ country_defaults.tax_label }} Code:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_gst_code }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <div
                                        is="sui-divider"
                                        horizontal
                                    >
                                        <h4 is="sui-header">
                                            <i class="university icon"></i>
                                            Payment Details
                                        </h4>
                                    </div>
                                    <v-row
                                        class="form-row"
                                        v-if="country_defaults.display_bsb"
                                        v-show="company_details_arr.company_payment_method === '1'"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label right"
                                            >{{ country_defaults.bsb_label }}:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ formattedBsb }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        class="form-row"
                                        v-show="company_details_arr.company_payment_method === '1'"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label right"
                                            >Account Number:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_acc_no }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        class="form-row"
                                        v-show="company_details_arr.company_payment_method === '1'"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Account Name:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_acc_name }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        class="form-row"
                                        v-show="company_details_arr.company_payment_method === '1'"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Bank:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ company_details_arr.company_bank_name }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        class="form-row"
                                        v-show="
                                            company_details_arr.company_payment_method === '2' ||
                                            company_details_arr.company_payment_method === '3'
                                        "
                                    >
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label"
                                            >Payment Method:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <span
                                                class="form-input-text"
                                                v-if="company_details_arr.company_payment_method === '2'"
                                                >BPAY</span
                                            >
                                            <span
                                                class="form-input-text"
                                                v-if="company_details_arr.company_payment_method === '3'"
                                                >Cheque</span
                                            >
                                        </v-col>
                                    </v-row>
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="6"
                                    md="6"
                                >
                                    <v-row class="form-row">
                                        <v-col
                                            sm="5"
                                            md="5"
                                            class="contact-box"
                                            v-for="(
                                                company_details_arr_data, company_details_arr_index
                                            ) in company_details_arr.company_contact_list"
                                            :key="company_details_arr_index"
                                        >
                                            <b>{{ company_details_arr_data.contact_name }}</b
                                            ><br />
                                            {{ company_details_arr_data.contact_role_description }}<br />
                                            <div
                                                v-for="(
                                                    contact_details_list_data, contact_details_list_index
                                                ) in company_details_arr_data.contact_details_list"
                                                :key="contact_details_list_index"
                                            >
                                                <b>{{ contact_details_list_data.contact_detail_description }}</b>
                                                <span
                                                    v-if="
                                                        contact_details_list_data.contact_detail_description ===
                                                        'E-Mail'
                                                    "
                                                    v-html="
                                                        linkLocationFormat(
                                                            'email',
                                                            contact_details_list_data.contact_detail,
                                                        )
                                                    "
                                                ></span>
                                                <span
                                                    v-if="
                                                        contact_details_list_data.contact_detail_description ===
                                                            'Mobile Phone' ||
                                                        contact_details_list_data.contact_detail_description ===
                                                            'Owner Mobile' ||
                                                        contact_details_list_data.contact_detail_description ===
                                                            'Service Mobile' ||
                                                        contact_details_list_data.contact_detail_description ===
                                                            'Tenant Mobile'
                                                    "
                                                    v-html="
                                                        linkLocationFormat(
                                                            'mobile',
                                                            contact_details_list_data.contact_detail,
                                                        )
                                                    "
                                                ></span>
                                                <span v-else>{{ contact_details_list_data.contact_detail }}</span>
                                                <br />
                                            </div>
                                        </v-col>
                                    </v-row>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="key_history_modal"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Key History</h6>
                    <v-spacer></v-spacer>
                    <cirrus-input
                        inputFormat="search"
                        v-if="isEditable()"
                        v-model="search_datatable_history"
                        placeholder="Search"
                        :edit_form="true"
                        style="padding-right: 1em"
                    ></cirrus-input>
                    <v-btn
                        x-small
                        v-show="isEditable()"
                        class="v-step-refresh-button"
                        icon
                        @click="loadPropertyKeyHistory()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="key_history_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-actions>
            </v-card>
            <v-card>
                <v-card-text>
                    <div class="property-key-history">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    v-show="property_keys_history_list.length === 0"
                                >
                                    Key doesn't have any associated transaction
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    v-show="property_keys_history_list.length > 0"
                                >
                                    <!--datatable start-->
                                    <v-data-table
                                        class="c8-datatable-custom"
                                        v-show="property_keys_history_list.length > 0"
                                        dense
                                        item-key="id"
                                        :headers="headers_history"
                                        :items="property_keys_history_list"
                                        :items-per-page="items_per_page_history"
                                        hide-default-footer
                                        :page.sync="page_history"
                                        :total-visible="7"
                                        @page-count="page_count_history = $event"
                                        :search="search_datatable_history"
                                        :calculate-widths="true"
                                    >
                                        <template v-slot:item.index="{ item }">
                                            {{ property_keys_history_list.indexOf(item) + 1 }}
                                        </template>
                                        <template v-slot:item.item_no="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">{{ item.item_no }}</span>
                                            </div>
                                        </template>
                                        <template v-slot:item.key_reference="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">{{ item.key_reference }}</span>
                                            </div>
                                        </template>
                                        <template v-slot:item.key_taker="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">{{ item.key_taker }}</span>
                                            </div>
                                        </template>
                                        <template v-slot:item.key_check_out_raw="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">{{ item.key_check_out }}</span>
                                            </div>
                                        </template>
                                        <template v-slot:item.key_return_due_raw="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">{{ item.key_return_due }}</span>
                                            </div>
                                        </template>
                                        <template v-slot:item.key_check_out_tooltip="{ item }">
                                            <v-icon
                                                style="font-size: 21px; position: relative; top: -1px"
                                                :title="keyHoverDesc(item, 1)"
                                                >info
                                            </v-icon>
                                        </template>
                                        <template v-slot:item.key_check_in_tooltip="{ item }">
                                            <v-icon
                                                style="font-size: 21px; position: relative; top: -1px"
                                                :title="keyHoverDesc(item, 2)"
                                                >info
                                            </v-icon>
                                        </template>
                                        <template v-slot:item.key_date_return_raw="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">{{ item.key_date_return }}</span>
                                            </div>
                                        </template>
                                        <template v-slot:item.key_tenant_code="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">
                                                    <a @click="openCompanyInfoModal(item.key_tenant_code)">{{
                                                        item.key_tenant_code
                                                    }}</a>
                                                </span>
                                            </div>
                                        </template>
                                        <template v-slot:item.key_file_out="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">
                                                    <cirrus-single-upload-button2
                                                        :withLinkUploader="true"
                                                        v-if="item.key_file_out !== ''"
                                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                                        v-model="item.key_file_out"
                                                        :size_limit="20"
                                                        accept_type="pdf-image"
                                                        :has_saved_file="
                                                            item.key_file_out_old !== '' &&
                                                            (typeof item.key_file_out_old === 'string' ||
                                                                item.key_file_out_old instanceof String)
                                                                ? true
                                                                : false
                                                        "
                                                        :edit_form="edit_form"
                                                    ></cirrus-single-upload-button2>
                                                </span>
                                            </div>
                                        </template>
                                        <template v-slot:item.key_file_in="{ item }">
                                            <div class="form-row no-border-line">
                                                <span class="form-input-text">
                                                    <cirrus-single-upload-button2
                                                        :withLinkUploader="true"
                                                        v-if="item.key_file_in !== ''"
                                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                                        v-model="item.key_file_in"
                                                        :size_limit="20"
                                                        accept_type="pdf-image"
                                                        :has_saved_file="
                                                            item.key_file_in_old !== '' &&
                                                            (typeof item.key_file_in_old === 'string' ||
                                                                item.key_file_in_old instanceof String)
                                                                ? true
                                                                : false
                                                        "
                                                        :edit_form="edit_form"
                                                    ></cirrus-single-upload-button2>
                                                </span>
                                            </div>
                                        </template>
                                    </v-data-table>
                                    <v-row
                                        class="form-row"
                                        v-show="property_keys_history_list.length > 5"
                                        v-if="isEditable()"
                                    >
                                        <v-col
                                            xs="12"
                                            sm="12"
                                            md="12"
                                        >
                                            <table class="c8-datatable-custom-footer">
                                                <tr>
                                                    <td class="">Rows per page:</td>
                                                    <td>
                                                        <multiselect
                                                            v-model="items_per_page_history"
                                                            :options="[5, 10, 15]"
                                                            :allowEmpty="false"
                                                            class="vue-select2 dropdown-left dropdown-200"
                                                            :show-labels="false"
                                                            ><span slot="noResult"
                                                                >Oops! No elements found. Consider changing the search
                                                                query.</span
                                                            >
                                                        </multiselect>
                                                    </td>
                                                    <td></td>
                                                    <td>
                                                        <v-pagination
                                                            v-model="page_history"
                                                            :length="page_count_history"
                                                            :total-visible="5"
                                                        ></v-pagination>
                                                    </td>
                                                </tr>
                                            </table>
                                        </v-col>
                                    </v-row>
                                    <!--datatable end-->
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteCheckPropertyKey('modal', property_keys_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    {{ AED_modal_title }}
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="property_keys_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        property_keys_arr.index === 'New'
                                            ? property_keys_arr.index
                                            : property_keys_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Reference:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="property_keys_arr.key_reference"
                                        size=""
                                        :id="'key_id' + property_keys_arr.index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Reference' : false"
                                        :rules_length="10"
                                        :maxlength="10"
                                        :size="10"
                                        :edit_form="
                                            edit_form &&
                                            key_action_type === 'EDIT' &&
                                            property_keys_arr.key_status_code !== 'X'
                                        "
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Description:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        v-model="property_keys_arr.key_description"
                                        size=""
                                        :id="'key_id' + property_keys_arr.index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Description' : false"
                                        :rules_length="10"
                                        :maxlength="10"
                                        :size="10"
                                        :edit_form="
                                            edit_form &&
                                            key_action_type === 'EDIT' &&
                                            property_keys_arr.key_status_code !== 'X'
                                        "
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="property_keys_arr.key_status_code === 'X'"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Status:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-select-v2
                                        :hasEmpty="false"
                                        v-model="property_keys_arr.key_status_code_new"
                                        :options="key_status_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Select a status"
                                    />
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="
                                    property_keys_arr.key_status_code === 'X' &&
                                    property_keys_arr.key_status_code_new === 'I'
                                "
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Comment:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-textarea
                                        v-model="activate_reason"
                                        auto-grow
                                        rows="5"
                                        full-width
                                        class="noteTextArea"
                                    ></v-textarea>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="showCheckOutDiv"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Who has the key?:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        v-model="property_keys_arr.key_taker"
                                        size=""
                                        :id="'key_id' + property_keys_arr.index"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Key Taker' : false"
                                        :edit_form="
                                            (property_keys_arr.key_status_code === 'O' && key_action_type === 'EDIT') ||
                                            (property_keys_arr.key_status_code === 'I' &&
                                                key_action_type === 'CHECK-OUT')
                                        "
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="showCheckOutDiv"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Company:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <!--                  <multiselect v-if="(property_keys_arr.key_status_code === 'O' && key_action_type === 'EDIT') || (property_keys_arr.key_status_code === 'I' && key_action_type === 'CHECK-OUT')" v-model="property_keys_arr.key_tenant" :options="company_list" :allowEmpty="false"-->
                                    <!--                               class="vue-select2 dropdown-left dropdown-400" :custom-label="nameWithDash" @select="changeCompany"-->
                                    <!--                               group-label="language" placeholder="Select a company" track-by="field_key" :options-limit="10000"-->
                                    <!--                               label="field_value" :show-labels="false"><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>-->
                                    <!--                  </multiselect>-->
                                    <cirrus-single-select-v2
                                        v-if="
                                            (property_keys_arr.key_status_code === 'O' && key_action_type === 'EDIT') ||
                                            (property_keys_arr.key_status_code === 'I' &&
                                                key_action_type === 'CHECK-OUT')
                                        "
                                        v-model="property_keys_arr.key_tenant_code"
                                        :options="company_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_key_w_value"
                                        return="field_key"
                                        placeholder="Select a company"
                                    />

                                    <span
                                        v-if="
                                            (property_keys_arr.key_status_code === 'I' && key_action_type === 'EDIT') ||
                                            (property_keys_arr.key_status_code === 'O' &&
                                                key_action_type === 'CHECK-IN' &&
                                                property_keys_arr.key_tenant !== '')
                                        "
                                        class="form-input-text"
                                    >
                                        {{ property_keys_arr.key_tenant_code }} -
                                        {{ property_keys_arr.key_tenant_name }}
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="showCheckOutDiv"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Check Out:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'property_next_charge_date'"
                                        :key="Math.random()"
                                        v-model="property_keys_arr.key_check_out"
                                        :edit_form="
                                            (property_keys_arr.key_status_code === 'O' && key_action_type === 'EDIT') ||
                                            (property_keys_arr.key_status_code === 'I' &&
                                                key_action_type === 'CHECK-OUT')
                                        "
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="showCheckOutDiv"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Return Due:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'property_next_charge_date'"
                                        :key="Math.random()"
                                        v-model="property_keys_arr.key_return_due"
                                        :edit_form="key_action_type !== 'CHECK-IN'"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="showCheckInDiv"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Check In:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'property_next_charge_date'"
                                        :key="Math.random()"
                                        v-model="property_keys_arr.key_date_return"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="showCheckOutInDiv"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >File:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-single-upload-button2
                                        :withLinkUploader="true"
                                        accept_type="pdf"
                                        :size_limit="20"
                                        v-if="
                                            (property_keys_arr.key_status_code === 'O' && key_action_type === 'EDIT') ||
                                            (property_keys_arr.key_status_code === 'I' &&
                                                key_action_type === 'CHECK-OUT')
                                        "
                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                        v-model="property_keys_arr.key_file_out"
                                        :has_saved_file="
                                            property_keys_arr.key_file_out_old !== '' &&
                                            (typeof property_keys_arr.key_file_out_old === 'string' ||
                                                property_keys_arr.key_file_out_old instanceof String)
                                                ? true
                                                : false
                                        "
                                        :edit_form="true"
                                    ></cirrus-single-upload-button2>
                                    <v-icon
                                        :data-tooltip="'Remove attachment'"
                                        small
                                        color="red"
                                        v-if="
                                            ((property_keys_arr.key_status_code === 'O' &&
                                                key_action_type === 'EDIT') ||
                                                (property_keys_arr.key_status_code === 'I' &&
                                                    key_action_type === 'CHECK-OUT')) &&
                                            property_keys_arr.key_file_out_old !== '' &&
                                            (typeof property_keys_arr.key_file_out_old === 'string' ||
                                                property_keys_arr.key_file_out_old instanceof String)
                                                ? true
                                                : false
                                        "
                                        @click="
                                            property_keys_arr.key_file_out_old = '';
                                            property_keys_arr.key_file_out = '';
                                        "
                                    >
                                        close
                                    </v-icon>
                                    <cirrus-single-upload-button2
                                        :withLinkUploader="true"
                                        accept_type="pdf"
                                        :size_limit="20"
                                        v-if="
                                            (property_keys_arr.key_status_code === 'I' && key_action_type === 'EDIT') ||
                                            (property_keys_arr.key_status_code === 'O' &&
                                                key_action_type === 'CHECK-IN')
                                        "
                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                        v-model="property_keys_arr.key_file_in"
                                        :has_saved_file="
                                            property_keys_arr.key_file_in_old !== '' &&
                                            (typeof property_keys_arr.key_file_in_old === 'string' ||
                                                property_keys_arr.key_file_in_old instanceof String)
                                                ? true
                                                : false
                                        "
                                        :edit_form="true"
                                    ></cirrus-single-upload-button2>
                                    <v-icon
                                        :data-tooltip="'Remove attachment'"
                                        small
                                        color="red"
                                        v-if="
                                            ((property_keys_arr.key_status_code === 'I' &&
                                                key_action_type === 'EDIT') ||
                                                (property_keys_arr.key_status_code === 'O' &&
                                                    key_action_type === 'CHECK-IN')) &&
                                            property_keys_arr.key_file_in_old !== '' &&
                                            (typeof property_keys_arr.key_file_in_old === 'string' ||
                                                property_keys_arr.key_file_in_old instanceof String)
                                                ? true
                                                : false
                                        "
                                        @click="
                                            property_keys_arr.key_file_in_old = '';
                                            property_keys_arr.key_file_in = '';
                                        "
                                    >
                                        close
                                    </v-icon>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="showCheckOutInDiv"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Notes:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-textarea
                                        v-if="
                                            (property_keys_arr.key_status_code === 'I' && key_action_type === 'EDIT') ||
                                            (property_keys_arr.key_status_code === 'O' &&
                                                key_action_type === 'CHECK-IN')
                                        "
                                        v-model="property_keys_arr.key_in_notes"
                                        auto-grow
                                        rows="5"
                                        full-width
                                        class="noteTextArea"
                                    ></v-textarea>
                                    <v-textarea
                                        v-if="
                                            (property_keys_arr.key_status_code === 'O' && key_action_type === 'EDIT') ||
                                            (property_keys_arr.key_status_code === 'I' &&
                                                key_action_type === 'CHECK-OUT')
                                        "
                                        v-model="property_keys_arr.key_out_notes"
                                        auto-grow
                                        rows="5"
                                        full-width
                                        class="noteTextArea"
                                    ></v-textarea>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous
                        </v-icon>
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="property_keys_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add
                        </v-icon>
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="property_keys_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteCheckPropertyKey('modal', property_keys_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="property_keys_arr.index !== 'New' && property_keys_arr.key_status_code !== 'X'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next
                        </v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_modal_del_reg_flag"
            max-width="800"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Delete Key Register
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_modal_del_reg_flag = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <v-alert
                        type="info"
                        dense
                        tile
                        text
                        dark
                        color="subPrimary"
                        v-if="delete_message !== ''"
                    >
                        <span> <strong>Warning:</strong> <span v-html="delete_message"></span> </span>
                    </v-alert>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="property-budget-lock-table-header"
                                    >Comment:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="form-input"
                                >
                                    <v-textarea
                                        v-model="delete_comment"
                                        auto-grow
                                        rows="14"
                                        full-width
                                        class="noteTextArea"
                                    ></v-textarea>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="error"
                        depressed
                        small
                        @click="deletePropertyKey(delete_key_index, delete_key_id)"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Delete
                    </v-btn>
                    <v-btn
                        color="normal"
                        depressed
                        small
                        @click="show_modal_del_reg_flag = false"
                    >
                        Cancel
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';
import { formatWithDelimiter } from '../../../../utils/sharedFunctions';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_KEYS',
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            key_history_modal: false,
            show_tenant_link_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            headers: [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                { text: 'Reference', value: 'key_reference', align: 'middle', width: 'auto' },
                { text: 'Description', value: 'key_description', width: 'auto' },
                { text: 'Status', value: 'key_status', align: 'center', width: 'auto' },
                { text: 'Who has the keys?', value: 'key_taker', align: 'center', width: 'auto' },
                { text: 'Check Out', value: 'key_check_out_raw', align: 'center', width: 'auto' },
                { text: 'Return Due', value: 'key_return_due_raw', align: 'center', width: 'auto' },
                { text: 'Check In', value: 'key_date_return_raw', align: 'center', width: 'auto' },
                { text: 'Link', value: 'key_tenant_code', align: 'center', width: 'auto' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            headers_history: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Reference', value: 'key_reference', align: 'middle', width: '170px' },
                { text: 'Who has the keys?', value: 'key_taker', align: 'center', width: '170px' },
                { text: 'Company', value: 'key_tenant_code' },
                { text: 'Check Out', value: 'key_check_out_raw', align: 'center' },
                { text: '', value: 'key_check_out_tooltip', align: 'center' },
                { text: 'Return Due', value: 'key_return_due_raw', align: 'center' },
                { text: 'Check In', value: 'key_date_return_raw', align: 'center' },
                { text: '', value: 'key_check_in_tooltip', align: 'center' },
                { text: 'File (Check Out)', value: 'key_file_out', align: 'center' },
                { text: 'File (Check In)', value: 'key_file_in', align: 'center' },
            ],
            page_history: 1,
            page_count_history: 0,
            items_per_page_history: 5,
            search_datatable_history: '',
            AED_modal: false,
            success_flag: false,
            show_details_modal: false,
            show_modal_del_reg_flag: false,
            modal_current_ctr: 0,
            property_keys_list: [],
            property_keys_history_list: [],
            property_keys_arr: { property_keys_history_length: 0 },
            account_list: [],
            company_list: [],
            key_action_type: 'EDIT',
            AED_modal_title: 'Key Information',
            company_code: '',
            lease_tenant_name: '',
            lease_tenant_street: '',
            lease_tenant_address_1: '',
            lease_tenant_address_2: '',
            lease_tenant_suburb: '',
            lease_tenant_post_code: '',
            lease_tenant_email: '',
            lease_tenant_country: '',
            lease_tenant_state: '',
            tenant_dir_bank: '',
            tenant_bank_name: '',
            tenant_bsb: '',
            tenant_acc_no: '',
            tenant_acc_name: '',
            tenant_gst_no: '',
            tenant_gst_code: '',
            delete_comment: '',
            delete_key_id: '',
            delete_key_index: '',
            delete_message: '',
            activate_reason: '',
            company_details_arr: [],
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
                tax_label: 'GST',
                business_prefix: '',
                bsb_label: 'BSB',
                bsb_format: {
                    delimiter: '-',
                    delimiter_frequency: 3,
                },
            },
            key_status_list: [
                { field_key: 'I', field_value: 'Check In' },
                { field_key: 'X', field_value: 'Inactive' },
            ],
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                { text: 'Reference', value: 'key_reference', align: 'middle', width: 'auto' },
                { text: 'Description', value: 'key_description', width: 'auto' },
                { text: 'Status', value: 'key_status', align: 'center', width: 'auto' },
                { text: 'Who has the keys?', value: 'key_taker', align: 'center', width: 'auto' },
                { text: 'Check Out', value: 'key_check_out_raw', align: 'center', width: 'auto' },
                { text: 'Return Due', value: 'key_return_due_raw', align: 'center', width: 'auto' },
                { text: 'Check In', value: 'key_date_return_raw', align: 'center', width: 'auto' },
                { text: 'Link', value: 'key_tenant_code', align: 'center', width: 'auto' },
            ];
            this.items_per_page = 9999;
        }
        if (this.new_property) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Reference', value: 'key_reference', align: 'middle' },
                { text: 'Description', value: 'key_description' },
                { text: '', value: 'action1', align: 'end', sortable: false },
            ];
        }
    },
    methods: {
        formatWithDelimiter,
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        resetForm: function () {
            this.edit_form = false;
        },
        loadForm: function () {
            this.error_server_msg = {};
            if (this.property_code !== '') {
                this.loadPropertyKey();
            }
        },
        loadPropertyKey: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/keys', form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            } else {
                let apiUrl = 'temp/property/fetch/keys';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            }
            if (this.new_property) this.edit_form = true;
            else this.edit_form = false;
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.property_keys_list = response.data.property_keys_list;
        },
        modalSubmitData: function () {
            let index = this.property_keys_arr.index;
            let key_id = this.property_keys_arr.key_id;
            let key_detail_id = this.property_keys_arr.key_detail_id;
            let key_reference = this.property_keys_arr.key_reference;
            let key_description = this.property_keys_arr.key_description;
            let status = this.property_keys_arr.status;
            let key_status_code = this.property_keys_arr.key_status_code;
            let key_status_code_new = this.property_keys_arr.key_status_code_new;
            let activate_reason = this.property_keys_arr.activate_reason;
            let property_keys_history_length = this.property_keys_arr.property_keys_history_length;
            let error_server_msg2 = [];
            if (key_reference === '') error_server_msg2.push(['You have not entered a valid key reference.']);
            if (key_description === '') error_server_msg2.push(['Please enter a description in plain text.']);

            let key_taker = '';
            let key_tenant_code = '';
            let key_return_due = '';
            let key_date_return = '';
            let key_file_in = '';
            let key_in_notes = '';
            let key_check_out = '';
            let key_out_notes = '';
            let key_file_out = [];
            if (index !== 'New') {
                key_taker = this.property_keys_arr.key_taker;
                key_tenant_code = this.property_keys_arr.key_tenant_code;
                key_return_due = this.property_keys_arr.key_return_due;
                //Check in
                key_date_return = this.property_keys_arr.key_date_return;
                key_file_in = this.property_keys_arr.key_file_in;
                key_in_notes = this.property_keys_arr.key_in_notes;
                if (
                    (key_status_code === 'I' && this.key_action_type === 'EDIT') ||
                    this.key_action_type === 'CHECK-IN'
                ) {
                    if (key_date_return === '') error_server_msg2.push(['You have not entered a valid Check In date.']);
                }
                //Check out
                key_check_out = this.property_keys_arr.key_check_out;
                key_file_out = this.property_keys_arr.key_file_out;
                key_out_notes = this.property_keys_arr.key_out_notes;
                if (
                    (key_status_code === 'O' && this.key_action_type === 'EDIT') ||
                    this.key_action_type === 'CHECK-OUT'
                ) {
                    if (key_taker === '') error_server_msg2.push(['You have not entered who Check out the key.']);
                    if (key_check_out === '') error_server_msg2.push(['You have not entered a valid Check Out Date.']);
                }
            }
            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('key_status_code', key_status_code);
                form_data.append('key_status_code_new', key_status_code_new);
                form_data.append('activate_reason', this.activate_reason);
                form_data.append('key_id', key_id);
                form_data.append('key_detail_id', key_detail_id);
                form_data.append('key_reference', key_reference);
                form_data.append('key_description', key_description);

                form_data.append('key_taker', key_taker);
                form_data.append('key_tenant', key_tenant_code);
                form_data.append('key_return_due', key_return_due);
                form_data.append('key_date_return', key_date_return);
                form_data.append('key_in_notes', key_in_notes);
                form_data.append('key_check_out', key_check_out);
                form_data.append('key_out_notes', key_out_notes);
                form_data.append('property_keys_history_length', property_keys_history_length);
                form_data.append('status', status);
                form_data.append('no_load', true);
                if (this.property_keys_arr.key_file_in)
                    form_data.append('key_doc_file_in', this.property_keys_arr.key_file_in[0]);
                if (this.property_keys_arr.key_file_out)
                    form_data.append('key_doc_file_out', this.property_keys_arr.key_file_out[0]);

                let apiUrl = '';
                switch (this.key_action_type) {
                    case 'CHECK-IN':
                        if (this.isPropertyFormLive())
                            apiUrl = 'with-file-upload/property/update-or-create/key-check-in';
                        else apiUrl = 'with-file-upload/temp/property/update-or-create/key-check-in';

                        break;
                    case 'CHECK-OUT':
                        if (this.isPropertyFormLive())
                            apiUrl = 'with-file-upload/property/update-or-create/key-check-out';
                        else apiUrl = 'with-file-upload/temp/property/update-or-create/key-check-out';

                        break;
                    default:
                        if (this.isPropertyFormLive()) apiUrl = 'with-file-upload/property/update-or-create/key';
                        else apiUrl = 'with-file-upload/temp/property/update-or-create/key';

                        break;
                }

                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.error_server_msg2 = response.data.error_server_msg2;
                        this.loading_setting = false;
                        if (this.error_server_msg2.length === 0) {
                            if (this.property_keys_arr.index === 'New') {
                                this.property_keys_arr.index = this.property_keys_list.length;
                                this.property_keys_arr.key_status_code = 'I';
                                this.property_keys_arr.property_keys_history_length = 0;
                            }
                            this.property_keys_arr.key_id = response.data.key_id;
                            this.property_keys_arr.status = 'saved';

                            this.loadForm();
                            this.success_flag = true;
                            if (!this.new_property) this.edit_form = false;

                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        }
                    });
            }
        },
        modalPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.property_keys_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.property_keys_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.property_keys_arr = this.objectClone(this.property_keys_list[this.modal_current_ctr]);
            this.property_keys_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.property_keys_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.property_keys_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.property_keys_arr = this.objectClone(this.property_keys_list[this.modal_current_ctr]);
            this.property_keys_arr.index = this.modal_current_ctr;
        },
        modalOpenAED: function (index, key_action_type = 'EDIT') {
            this.activate_reason = '';
            let property_code = this.property_code;
            let key_reference = this.property_keys_list[index].key_reference;
            let key_id = this.property_keys_list[index].key_id;

            var form_data = new FormData();
            form_data.append('property_code', property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('key_reference', key_reference);
            form_data.append('key_id', key_id);
            form_data.append('no_load', true);

            let apiUrl = 'temp/property/fetch/keys-history';
            if (this.isPropertyFormLive()) apiUrl = 'property/fetch/keys-history';

            this.$api.post(apiUrl, form_data).then((response) => {
                this.property_keys_history_list = response.data.property_keys_history_list;
                let property_keys_history_length = this.property_keys_history_list.length;
                this.AED_modal = true;
                this.error_server_msg = {};
                this.error_server_msg2 = [];
                this.property_keys_arr = this.objectClone(this.property_keys_list[index]);
                this.property_keys_arr.index = index;
                this.property_keys_arr.property_keys_history_length = property_keys_history_length;
                this.key_action_type = key_action_type;
                this.modal_current_ctr = index;
                switch (key_action_type) {
                    case 'CHECK-IN':
                        this.AED_modal_title = 'Check In Information';
                        // this.property_keys_arr.key_taker = "";
                        // this.property_keys_arr.key_tenant = { field_key: '', field_value: '' };
                        // this.property_keys_arr.key_return_due = '';
                        this.property_keys_arr.key_date_return = '';
                        this.property_keys_arr.key_file_in = null;
                        this.property_keys_arr.key_in_notes = '';
                        // this.property_keys_arr.key_check_out = '';
                        this.property_keys_arr.key_file_out = null;
                        this.property_keys_arr.key_out_notes = '';
                        break;
                    case 'CHECK-OUT':
                        this.AED_modal_title = 'Check Out Information';
                        this.property_keys_arr.key_taker = '';
                        this.property_keys_arr.key_tenant = '';
                        this.property_keys_arr.key_return_due = '';
                        this.property_keys_arr.key_date_return = '';
                        this.property_keys_arr.key_file_in = null;
                        this.property_keys_arr.key_in_notes = '';
                        this.property_keys_arr.key_check_out = '';
                        this.property_keys_arr.key_file_out = null;
                        this.property_keys_arr.key_out_notes = '';
                        break;
                    default:
                        this.AED_modal_title = 'Update Key Information';
                        break;
                }
            });
        },
        modalOpenDetails: function (index) {
            this.show_details_modal = true;
            this.property_keys_arr = this.objectClone(this.property_keys_list[index]);
            this.property_keys_arr.index = index;
            this.modal_current_ctr = index;
        },
        modalAddData: function () {
            this.edit_form = true;
            this.AED_modal = true;
            // this.key_action_type = "ADD";
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.property_keys_arr = {
                index: 'New',
                key_id: '',
                key_detail_id: '',
                key_history_count: 0,
                key_reference: [],
                key_description: '',
                key_status_code: 'I',
                key_status_code_new: 'I',
                property_keys_history_length: 0,
                status: 'new',
            };
        },
        loadAllAccounts: function () {
            this.$api.post('loadAllAccountDropdown').then((response) => {
                this.account_list = response.data.grouped;
            });
        },
        async deletePropertyKey(index, key_id) {
            //
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('key_id', key_id);
            form_data.append('delete_comment', this.delete_comment);
            form_data.append('no_load', true);
            let apiUrl = '';
            if (this.isPropertyFormLive()) apiUrl = 'property/delete/key';
            else apiUrl = 'temp/property/delete/key';

            this.$api.post(apiUrl, form_data).then((response) => {
                this.loading_setting = false;
                this.error_server_msg2 = response.data.error_server_msg2;
                if (this.error_server_msg2.length === 0) {
                    this.success_flag = true;
                    this.show_modal_del_reg_flag = false;
                    this.loadForm();
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                }
            });
        },
        async deleteCheckPropertyKey(type, index) {
            this.delete_comment = '';
            this.delete_message = '';
            if (index !== 'New') {
                let key_id = this.property_keys_arr.key_id;
                let key_history_count = this.property_keys_arr.key_history_count;
                if (type === 'table') {
                    key_id = this.property_keys_list[index].key_id;
                    key_history_count = this.property_keys_list[index].key_history_count;
                }
                this.delete_key_id = key_id;
                this.delete_key_index = index;
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    if (key_history_count > 0)
                        this.delete_message =
                            "Key has associated transaction. The key can't be deleted. The key will be inactive instead.";
                    this.show_modal_del_reg_flag = true;
                }
            }
        },
        loadCompanyList: function () {
            // console.log(this.lease_bond_deposit_property);
            var form_data = new FormData();
            form_data.append('all', '1');
            this.$api.post('company/fetch/all-company-dropdown-list', form_data).then((response) => {
                this.company_list = response.data.company_list;
            });
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadKeysDocs_' + id;
        },
        openKeyHistory: function (index) {
            this.key_history_modal = true;
            this.loadPropertyKeyHistory(index);
        },
        loadPropertyKeyHistory: function (index) {
            // this.loading_setting = true;

            let property_code = this.property_code;
            let key_reference = this.property_keys_list[index].key_reference;
            let key_id = this.property_keys_list[index].key_id;

            var form_data = new FormData();
            form_data.append('property_code', property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('key_reference', key_reference);
            form_data.append('key_id', key_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/keys-history', form_data).then((response) => {
                    this.property_keys_history_list = response.data.property_keys_history_list;
                });
            } else {
                let apiUrl = 'temp/property/fetch/keys-history';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.property_keys_history_list = response.data.property_keys_history_list;
                });
            }
        },
        openTenantLink: function (company_code) {
            this.show_tenant_link_modal = true;
            this.loadCompanyDetails(company_code);
        },
        loadCompanyDetails: function (company_code) {
            let lease_company_code = company_code;
            if (lease_company_code !== '') {
                var form_data = new FormData();
                form_data.append('company_code', lease_company_code);
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('no_load', true);
                this.$api.post('company/load-company-details', form_data).then((response) => {
                    this.company_code = response.data.company_code;

                    this.lease_tenant_name = response.data.tenant_name;
                    this.lease_tenant_street = response.data.tenant_street;
                    this.lease_tenant_address_1 = response.data.tenant_address_1;
                    this.lease_tenant_address_2 = response.data.tenant_address_2;
                    this.lease_tenant_suburb = response.data.tenant_suburb;
                    this.lease_tenant_post_code = response.data.tenant_post_code;
                    this.lease_tenant_email = response.data.tenant_email;

                    this.lease_tenant_country = response.data.tenant_country;
                    this.lease_tenant_state = response.data.tenant_state.field_key;
                    this.lease_tenant_state_list = response.data.tenant_state_list;

                    this.tenant_dir_bank = response.data.tenant_dir_bank;
                    this.tenant_bank_name = response.data.tenant_bank_name;
                    this.tenant_bsb = response.data.tenant_bsb;
                    this.tenant_acc_no = response.data.tenant_acc_no;
                    this.tenant_acc_name = response.data.tenant_acc_name;
                    this.tenant_gst_no = response.data.tenant_gst_no;
                    this.tenant_gst_code = response.data.tenant_gst_code;

                    this.loading_page_setting = false;
                });
            }
        },
        changeCompany: function (option) {
            if (this.property_keys_arr.key_taker === '') this.property_keys_arr.key_taker = option.field_value;
        },
        openCompanyInfoModal: function (company_code) {
            this.show_tenant_link_modal = true;
            var form_data = new FormData();
            form_data.append('owner_code', company_code);
            form_data.append('no_load', true);
            let apiUrl = 'company/load-company-all-details';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.company_details_arr = response.data.company_details_arr;
                this.loadCountryDefaults();
                this.loading_setting = false;
            });
        },
        breakdownEmail: function (email) {
            if (email !== '') return email.split(';');
        },
        loadCountryDefaults: function (country) {
            this.loading_page_setting = true;

            var form_data = new FormData();
            if (country) form_data.append('country', country);

            let api_url = this.cirrus8_api_url + 'admin/country_defaults/load';
            this.$api.post(api_url, form_data).then((response) => {
                this.error_server_msg2 = response.data.validation_errors;
                this.loading_page_setting = false;
                this.country_defaults = response.data.default; // this.tenantStateChanged();
            });
        },
        keyHoverDesc: function (item, switch_index) {
            let hover_message = '';
            if (switch_index === 0) {
                if (item.key_updated_by) hover_message += 'Modified By: ' + item.key_updated_by;
                if (item.key_date_updated)
                    hover_message += (hover_message ? '\n' : '') + 'Modified Date: ' + item.key_date_updated;
                if (item.key_created_by)
                    hover_message += (hover_message ? '\n' : '') + 'Created By: ' + item.key_created_by;
                if (item.key_date_created)
                    hover_message += (hover_message ? '\n' : '') + 'Created Date: ' + item.key_date_created;
            }
            if (switch_index === 1) {
                if (item.key_out_notes) hover_message += 'Notes: ' + item.key_out_notes;
                if (item.key_checked_out_by)
                    hover_message += (hover_message ? '\n' : '') + 'Checked Out By: ' + item.key_checked_out_by;
                if (item.key_checked_out_date)
                    hover_message += (hover_message ? '\n' : '') + 'Checked Out Date: ' + item.key_checked_out_date;
            }
            if (switch_index === 2) {
                if (item.key_in_notes) hover_message += 'Notes: ' + item.key_in_notes;
                if (item.key_checked_in_by)
                    hover_message += (hover_message ? '\n' : '') + 'Checked In By: ' + item.key_checked_in_by;
                if (item.key_checked_in_date)
                    hover_message += (hover_message ? '\n' : '') + 'Checked In Date: ' + item.key_checked_in_date;
            }
            if (hover_message === '') hover_message = 'No details';
            return hover_message;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.loadAllAccounts();
                this.loadCompanyList();
                this.headers = [
                    { text: '#', value: 'index', sortable: false, width: '40px' },
                    { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                    { text: 'Reference', value: 'key_reference', align: 'middle' },
                    { text: 'Description', value: 'key_description' },
                    { text: 'Status', value: 'key_status', align: 'center' },
                    { text: 'Who has the keys?', value: 'key_taker', align: 'center' },
                    { text: 'Check Out', value: 'key_check_out_raw', align: 'center' },
                    { text: 'Return Due', value: 'key_return_due_raw', align: 'center' },
                    { text: 'Check In', value: 'key_date_return_raw', align: 'center' },
                    { text: 'Link', value: 'key_tenant_code', align: 'center' },
                    { text: 'Keys History', value: 'action2', align: 'center' },
                    { text: '', value: 'tooltip', align: 'center' },
                    { text: '', value: 'action1', align: 'end', sortable: false, width: '100px' },
                ];
            } else {
                this.headers = [
                    { text: '#', value: 'index', sortable: false, width: 'auto' },
                    { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                    { text: 'Reference', value: 'key_reference', align: 'middle', width: 'auto' },
                    { text: 'Description', value: 'key_description', width: 'auto' },
                    { text: 'Status', value: 'key_status', align: 'center', width: 'auto' },
                    { text: 'Who has the keys?', value: 'key_taker', align: 'center', width: 'auto' },
                    { text: 'Check Out', value: 'key_check_out_raw', align: 'center', width: 'auto' },
                    { text: 'Return Due', value: 'key_return_due_raw', align: 'center', width: 'auto' },
                    { text: 'Check In', value: 'key_date_return_raw', align: 'center', width: 'auto' },
                    { text: 'Link', value: 'key_tenant_code', align: 'center', width: 'auto' },
                ];
            }
        },
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
        ...mapGetters(['getDDCountryStates']),
        showCheckOutDiv() {
            let property_keys_arr = this.property_keys_arr;
            let property_keys_history_length = property_keys_arr.property_keys_history_length;
            let key_action_type = this.key_action_type;
            return key_action_type === 'CHECK-OUT' || key_action_type === 'CHECK-IN';
        },
        showCheckInDiv() {
            let property_keys_arr = this.property_keys_arr;
            let property_keys_history_length = property_keys_arr.property_keys_history_length;
            let key_action_type = this.key_action_type;
            return key_action_type === 'CHECK-IN';
        },
        showCheckOutInDiv() {
            let property_keys_arr = this.property_keys_arr;
            let property_keys_history_length = property_keys_arr.property_keys_history_length;
            let key_action_type = this.key_action_type;
            return key_action_type === 'CHECK-IN' || key_action_type === 'CHECK-OUT';
        },
        formattedBsb() {
            return formatWithDelimiter(
                this.company_details_arr.company_bsb,
                this.country_defaults.bsb_format.delimiter,
                this.country_defaults.bsb_format.delimiter_frequency,
            );
        },
    },
    created() {
        bus.$on('loadPropertyKeysFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
