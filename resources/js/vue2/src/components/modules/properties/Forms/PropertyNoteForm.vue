<style>
.property-note-card {
    padding-top: 6px;
    padding-bottom: 6px;
    padding-left: 0px;
    padding-right: 0px;
}
</style>
<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Property Notes</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="search"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>

                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property && property_note_list.length > 0"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && property_note_list.length > 0"
                    icon
                    @click="modalSubmitData()"
                >
                    <v-icon
                        light
                        color="green"
                        >check
                    </v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable()"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="showPropertyActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="property_note_list.length === 0 && !loading_setting"
            v-show="isEditable()"
        >
            <v-btn
                v-if="!pmro_read_only"
                depressed
                small
                color="success"
                @click="modalAddData()"
                >Add Note
            </v-btn>
            <div
                style="margin: 10px 0px"
                v-else
            >
                No property notes at the moment
            </div>
        </v-col>
        <v-data-iterator
            :items="property_note_list"
            :search="search"
            hide-default-footer
            disable-pagination
            v-if="property_note_list.length > 0 && !loading_setting"
        >
            <template v-slot:default="props">
                <v-row class="ma-0">
                    <v-col
                        v-for="(item, index) in props.items"
                        :key="index"
                        cols="12"
                        sm="12"
                        md="12"
                        lg="12"
                        class="property-note-card"
                    >
                        <v-card
                            color=""
                            class=""
                        >
                            <v-card-actions class="pa-2">
                                <strong
                                    >{{ property_note_list.indexOf(item) + 1
                                    }}<span>:({{ item.item_no }}).&nbsp</span></strong
                                >
                                <span>{{ item.note_heading.field_value }}</span>
                                &nbsp|&nbsp<strong>{{ item.note_timestamp }}</strong> by
                                <strong>{{ item.note_user }}</strong>
                                <v-spacer></v-spacer>
                                <v-icon
                                    color="green"
                                    class="rotate90"
                                    v-show="!read_only"
                                    v-if="edit_form"
                                    @click="setOrder('note-sequence-first', index)"
                                    >fast_rewind
                                </v-icon>
                                <v-icon
                                    color="green"
                                    class="rotate90"
                                    v-show="!read_only"
                                    v-if="edit_form"
                                    @click="setOrder('note-sequence-up', index)"
                                    >arrow_left
                                </v-icon>
                                <v-icon
                                    color="green"
                                    class="rotate90"
                                    v-show="!read_only"
                                    v-if="edit_form"
                                    @click="setOrder('note-sequence-down', index)"
                                    >arrow_right
                                </v-icon>
                                <v-icon
                                    color="green"
                                    class="rotate90"
                                    v-show="!read_only"
                                    v-if="edit_form"
                                    @click="setOrder('note-sequence-last', index)"
                                    >fast_forward
                                </v-icon>
                                <v-icon
                                    small
                                    @click="modalOpenAED(property_note_list.indexOf(item))"
                                    v-if="edit_form"
                                >
                                    fas fa-edit
                                </v-icon>
                                <v-icon
                                    color="red"
                                    v-show="!read_only"
                                    v-if="edit_form"
                                    @click="deleteNote(index)"
                                >
                                    close
                                </v-icon>
                            </v-card-actions>
                            <v-divider light></v-divider>
                            <v-layout>
                                <v-flex
                                    xs12
                                    class="noteTextAreaBgColor"
                                >
                                    <div class="pa-3">
                                        <pre style="text-wrap: pretty">{{ item.note_description }}</pre>
                                    </div>
                                </v-flex>
                            </v-layout>
                        </v-card>
                    </v-col>
                </v-row>
            </template>
        </v-data-iterator>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteNote(property_notes_add_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Property Note Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Property add-->
                    <div :key="property_notes_add_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="form-input"
                                >
                                    <v-card
                                        color=""
                                        class="property-notes-container"
                                    >
                                        <v-card-actions class="pa-2">
                                            <strong><span class="form-input-text">Note Header</span>&nbsp</strong>
                                            <multiselect
                                                v-model="property_notes_add_arr.note_heading"
                                                :options="note_header_list"
                                                :allowEmpty="false"
                                                class="vue-select2 dropdown-left dropdown-800"
                                                group-label="language"
                                                placeholder="Select a heading"
                                                track-by="field_key"
                                                label="field_value"
                                                :show-labels="false"
                                                ><span slot="noResult"
                                                    >Oops! No elements found. Consider changing the search query.</span
                                                >
                                            </multiselect>
                                            <v-spacer></v-spacer>
                                        </v-card-actions>
                                        <v-divider light></v-divider>
                                        <v-layout>
                                            <v-flex xs12>
                                                <v-textarea
                                                    v-model="property_notes_add_arr.note_description"
                                                    auto-grow
                                                    rows="14"
                                                    full-width
                                                    class="noteTextArea"
                                                    v-show="!read_only"
                                                ></v-textarea>
                                            </v-flex>
                                        </v-layout>
                                    </v-card>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous
                        </v-icon>
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="property_notes_add_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add
                        </v-icon>
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="property_notes_add_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteNote(property_notes_add_arr.index)"
                        v-if="property_notes_add_arr.index !== 'New'"
                        color="error"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next
                        </v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        force_load: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_NOTES',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            property_notes_add_arr: [],
            property_note_list: [],
            property_note_list_old: [],
            note_header_list: [],
            show_activity_log_modal: false,
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search: '',
            AED_modal: false,
            success_flag: false,
            modal_current_ctr: 0,
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '' || this.force_load) {
                // console.log('load property property details');
                this.loadPropertyNote();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.property_note_list = JSON.parse(JSON.stringify(this.property_note_list_old));
        },
        modalAddData: function () {
            this.edit_form = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.AED_modal = true;
            this.property_notes_add_arr = {
                index: 'New',
                note_id: '',
                note_timestamp: '',
                note_user: '',
                note_description: '',
                note_sequence: '',
                note_heading_code: '',
                note_heading: {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                status: 'new',
            };
        },
        loadPropertyNote: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';
            if (this.isPropertyFormLive()) {
                apiUrl = 'property/fetch/notes';
            } else {
                apiUrl = 'temp/property/fetch/notes';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.note_header_list = response.data.note_header_list;
                this.property_note_list = response.data.property_note_list;
                this.property_note_list_old = response.data.property_note_list;
                this.loading_setting = false;
            });
            if (this.new_property) this.edit_form = true;
            else this.edit_form = false;
        },
        async deleteNote(index) {
            if (index !== 'New') {
                let status = this.property_note_list[index].status;
                let note_id = this.property_note_list[index].note_id;

                if (status === 'new') {
                    this.property_note_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('note_id', note_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/note';
                        } else {
                            apiUrl = 'temp/property/delete/note';
                        }
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.property_note_list.splice(index, 1);
                            this.loading_setting = false;
                        });
                    }
                }
            }
        },
        modalSubmitData: function () {
            let errorArr = [];
            let note_heading = this.property_notes_add_arr.note_heading.field_value;
            let note_description = this.property_notes_add_arr.note_description;
            if (note_heading === '' || note_heading === 'Please select ...') {
                errorArr.push(['You have not entered a valid note heading.']);
            }
            if (note_description === '') {
                errorArr.push(['You have not entered a valid note.']);
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                let save_arr = [];
                save_arr[0] = this.property_notes_add_arr;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('property_note_list', JSON.stringify(save_arr));

                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/update-or-create/notes';
                } else {
                    apiUrl = 'temp/property/update-or-create/notes';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadForm();
                    if (!this.new_property) this.edit_form = false;
                    if (this.property_notes_add_arr.status === 'new') {
                        this.property_notes_add_arr.index = this.property_note_list.length;
                        this.property_notes_add_arr.note_heading_code = response.data.note_heading_code;
                        this.property_notes_add_arr.note_id = response.data.note_id;
                        this.property_notes_add_arr.note_sequence = response.data.note_sequence;
                        this.property_notes_add_arr.note_user = response.data.note_user;
                        this.property_notes_add_arr.status = 'saved';
                    }
                    this.loading_setting = false;
                    // this.$noty.success("Successfully Saved");
                    this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        setOrder: function (action, key) {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            let nextId = parseInt(key) + 1;
            let prevId = parseInt(key) - 1;
            let seq_size = this.property_note_list.length;
            if (nextId + 1 >= seq_size) {
                nextId = seq_size - 1;
            }
            if (prevId <= 0) {
                prevId = 0;
            }

            let seq_id = this.property_note_list[key].note_id;
            let next_seq_id = this.property_note_list[nextId].note_id;
            let prev_seq_id = this.property_note_list[prevId].note_id;

            let orig_seq = parseInt(this.property_note_list[key].note_sequence);
            let next_seq = parseInt(this.property_note_list[key].note_sequence) + 1;
            let prev_seq = parseInt(this.property_note_list[key].note_sequence) - 1;

            form_data.append('seq_id', seq_id);
            form_data.append('next_seq_id', next_seq_id);
            form_data.append('prev_seq_id', prev_seq_id);
            form_data.append('orig_seq', orig_seq);
            form_data.append('seq_size', seq_size.toString());
            form_data.append('next_seq', next_seq);
            form_data.append('prev_seq', prev_seq);

            let apiUrl = '';
            if (this.isPropertyFormLive()) {
                apiUrl = 'property/update/' + action;
            } else {
                apiUrl = 'temp/property/update/' + action;
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.loadForm();
                // this.edit_form = false;
                // this.loading_setting = false;
            });
        },
        showPropertyActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        modalPrevData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.property_notes_add_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.property_note_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.property_notes_add_arr = this.property_note_list[this.modal_current_ctr];
            this.property_notes_add_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.property_notes_add_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.property_note_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.property_notes_add_arr = this.property_note_list[this.modal_current_ctr];
            this.property_notes_add_arr.index = this.modal_current_ctr;
        },
        modalOpenAED: function (index) {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.AED_modal = true;
            this.property_notes_add_arr = this.property_note_list[index];
            this.property_notes_add_arr.index = index;
            this.modal_current_ctr = index;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        force_load: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadPropertyNoteSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
