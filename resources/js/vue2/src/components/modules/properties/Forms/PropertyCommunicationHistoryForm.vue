<template>
    <div
        style="max-width: 100%"
        v-on:dblclick="doubleClickForm()"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Communication History</h6>
                &nbsp
                <v-btn-toggle
                    class="form-toggle"
                    v-model="communication_type"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Email
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        SMS
                    </v-btn>
                </v-btn-toggle>
                <v-spacer></v-spacer>
                <h6
                    class="title"
                    v-show="limit_by === 0"
                >
                    Latest 50 records
                </h6>
                &nbsp
                <v-btn
                    v-show="limit_by === 0"
                    x-small
                    tile
                    text
                    @click="limit_by = 1"
                >
                    Show all
                </v-btn>
                &nbsp &nbsp
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive) &&
                        communication_type === 0
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !new_property &&
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)
                    "
                    v-if="edit_form"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_property && isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div class="page-form">
            <div
                class="form-row"
                v-show="communication_type === 0"
            >
                <property-email-component
                    :property_code="property_code"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :edit_form="edit_form"
                ></property-email-component>
            </div>
            <div
                class="form-row"
                v-show="communication_type === 1"
            >
                <property-sms-component
                    :property_code="property_code"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :edit_form="edit_form"
                ></property-sms-component>
            </div>
        </div>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import PropertyEmail from './PropertyEmail.vue';
import PropertySMS from './PropertySMS.vue';
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_property: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    components: {
        'property-email-component': PropertyEmail,
        'property-sms-component': PropertySMS,
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_COM_HISTORY',
            loading_setting: true,
            communication_type: 0,
            limit_by: 0,
            edit_form: false,
            show_activity_log_modal: false,
            readonly: this.read_only,
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
    },
    methods: {
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                )
                    this.edit_form = true;
                else this.edit_form = false;
            }
        },
        loadForm: function () {
            let limit_by = this.getLimitByValue(this.limit_by);
            bus.$emit('loadPropertyEmailSection', {
                property_code: this.property_code,
                limit_by: limit_by,
            });
            bus.$emit('loadPropertySMSSection', {
                property_code: this.property_code,
                limit_by: limit_by,
            });
        },
        modalAddData: function () {
            var d = new Date();
            bus.$emit('openEMAILAEModal', {
                history_id: '',
                category: '',
                recipients: '',
                subject: '',
                date_sent: d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                attachments: null,
                attachments_old: null,
                property_code: this.property_code,
            });
        },
        resetForm: function () {
            this.edit_form = false;
            this.loadForm();
        },
        handleWatchedProperties() {
            let limit_by = this.getLimitByValue(this.limit_by);
            if (this.communication_type === 0) {
                bus.$emit('loadPropertyEmailSection', {
                    property_code: this.property_code,
                    limit_by: limit_by,
                });
            }
            if (this.communication_type === 1) {
                bus.$emit('loadPropertySMSSection', {
                    property_code: this.property_code,
                    limit_by: limit_by,
                });
            }
        },
        getLimitByValue(limit_by) {
            switch (limit_by) {
                case 0:
                    return 50;
                case 1:
                    return -1;
                default:
                    return 50;
            }
        },
    },
    watch: {
        property_code: 'handleWatchedProperties',
        communication_type: 'handleWatchedProperties',
        limit_by: 'handleWatchedProperties',
    },
    created() {
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
