<style>
.variance_col {
    background-color: #eaeaea !important;
}

.v-data-table-header > tr > th,
.v-data-table-header > tr > th > span {
    white-space: nowrap;
}

body.c8-dark #frame #container .property-budget-section .vue-data-grid thead tr:last-child th span {
    color: #ffffff;
}

body.c8-dark #frame #container .property-budget-section #budget-form-toggle .v-btn--text .v-btn__content {
    color: inherit !important;
}

body.c8-dark .v-input--switch__track:not(.v-input--is-label-active) {
    background-color: grey !important;
}

body.c8-dark .v-input--is-label-active .v-input--switch__track,
body.c8-dark .v-input--is-label-active .v-input--switch__thumb {
    background-color: rgb(255, 149, 7) !important;
}
</style>
<template>
    <div class="property-budget-section">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Budget</h6>
                <v-chip
                    label
                    x-small
                    color="primary"
                    text-color="white"
                    class="ma-1"
                >
                    <v-icon
                        left
                        x-small
                        >label
                    </v-icon>
                    {{ input_type_lbl }}
                </v-chip>
                <v-chip
                    label
                    x-small
                    color="primary"
                    text-color="white"
                    class="ma-1"
                >
                    <v-icon
                        left
                        x-small
                        >calendar_today
                    </v-icon>
                    {{ financial_year_lbl }}
                </v-chip>
                <v-spacer></v-spacer>
                <v-switch
                    class="budget-form-switch"
                    v-model="budget_setting_flag"
                    inset
                    dense
                    flat
                    x-small
                    :ripple="false"
                    color="#ff9507"
                    :label="`Budget Settings`"
                    style="min-width: 140px; width: 140px; padding-right: 10px; margin: 0px"
                ></v-switch>
                <cirrus-input
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <div v-if="budget_setting_flag">
            <div class="page-form">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Input Type
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            class="form-toggle"
                            id="budget-form-toggle"
                            v-model="input_type"
                            mandatory
                        >
                            <v-btn
                                small
                                depressed
                                text
                                disabled
                            >
                                NONE
                            </v-btn>
                            <v-btn
                                small
                                depressed
                                text
                            >
                                Cash Budget
                            </v-btn>
                            <v-btn
                                small
                                depressed
                                text
                            >
                                Cash Forecast
                            </v-btn>
                            <v-btn
                                small
                                depressed
                                text
                            >
                                Accruals Budget
                            </v-btn>
                            <v-btn
                                small
                                depressed
                                text
                            >
                                Accruals Forecast
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >For financial year
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            openDirection="bottom"
                            v-model="financial_year"
                            :options="year_list"
                            :allowEmpty="false"
                            class="vue-select2 dropdown-left dropdown-300"
                            group-label="language"
                            placeholder="Select a financial year"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <span
                            class="form-input-text"
                            v-if="from_date != ''"
                        >
                            &nbsp;&nbsp;{{ from_date }} to {{ to_date }}
                        </span>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input right"
                    >
                        <v-btn
                            depressed
                            small
                            color="primary"
                            @click="loadForm()"
                            >Load Budget
                        </v-btn>
                    </v-col>
                </v-row>
            </div>
        </div>
        <cirrus-content-loader
            v-show="budget_setting_dimmer"
            type="table-tbody"
        ></cirrus-content-loader>
        <div
            :key="refresh_index"
            v-if="!budget_setting_dimmer"
        >
            <v-data-table
                class="c8-datatable-custom expenses-datatable"
                dense
                item-key="accountCode"
                :headers="expenses_headers"
                :items="budget_overview_expenses"
                group-by="account_archive"
                :items-per-page="-1"
                hide-default-footer
                :search="search_datatable"
                :calculate-widths="true"
                no-results-text="No expenses account"
                :expanded.sync="expanded"
                show-expand
            >
                <template v-slot:group.header="{ group }">
                    <td
                        colspan="17"
                        v-if="group === '1'"
                    >
                        <strong>{{ showGroupLabel(group) }}</strong>
                    </td>
                </template>

                <template v-slot:item.index_ui="{ item, index }">
                    {{ index + 1 }}
                </template>
                <template v-slot:item.accountName="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.accountCode }} - {{ item.accountName }}</span>
                    </div>
                </template>
                <template v-slot:item.period_0="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_0, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_1="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_1, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_2="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_2, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_3="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_3, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_4="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_4, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_5="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_5, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_6="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_6, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_7="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_7, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_8="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_8, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_9="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_9, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_10="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_10, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_11="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_11, 2)))
                        }}</span>
                    </div>
                </template>
                <template v-slot:item.period_total="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{
                            accountingFormat(numberWithCommas(roundTo(item.period_total, 2)))
                        }}</span>
                    </div>
                </template>

                <template v-slot:item.action1="{ item }">
                    <a
                        href="#"
                        data-inverted=""
                        data-position="left center"
                        :data-tooltip="item.comment !== '' ? item.comment.substring(0, 35) + '...' : 'No comment'"
                    >
                        <v-icon
                            small
                            :class="!checkIfEmpty(item.comment) ? 'comment-icon-has-content' : 'comment-icon'"
                            dark
                            >comment
                        </v-icon>
                    </a>
                </template>
                <template v-slot:item.data-table-expand="{ item, expand, isExpanded }">
                    <v-icon
                        v-if="
                            (isExpanded && (item.account_GL === 'EXP.OUT' || item.account_GL === 'EXP.REC')) ||
                            is_strata_flag
                        "
                        @click="expand(!isExpanded)"
                        >mdi-chevron-up
                    </v-icon>
                    <v-icon
                        v-if="
                            (!isExpanded && (item.account_GL === 'EXP.OUT' || item.account_GL === 'EXP.REC')) ||
                            is_strata_flag
                        "
                        @click="expand(!isExpanded)"
                        >mdi-chevron-down
                    </v-icon>
                </template>
                <template v-slot:expanded-item="{ headers, item }">
                    <td :colspan="expenses_headers.length">
                        <div style="float: left">
                            <div class="tree-vertical-line"></div>
                            <div class="tree-horizontal-line"></div>
                        </div>
                        <div style="padding-left: 35px; min-height: 100px">
                            <div style="text-align: center">
                                <div class="ui mini statistic">
                                    <div class="label">Total SQM</div>
                                    <div class="value">
                                        {{
                                            accountingFormat(
                                                numberWithCommas(
                                                    roundTo(
                                                        totalPropertySqmBudgetOverview(
                                                            budget_overview_expenses.indexOf(item),
                                                        ),
                                                        2,
                                                    ),
                                                ),
                                            )
                                        }}
                                    </div>
                                </div>
                                <div class="ui mini statistic">
                                    <div class="label">Account Cost</div>
                                    <div class="value">
                                        {{ accountingFormat(numberWithCommas(roundTo(item.value, 2))) }}
                                    </div>
                                </div>
                                <div class="ui mini statistic">
                                    <div class="label">Total Cost</div>
                                    <div class="value">
                                        {{
                                            accountingFormat(
                                                numberWithCommas(
                                                    roundTo(
                                                        accountCodeTotalCostBudgetOverview(
                                                            budget_overview_expenses.indexOf(item),
                                                        ),
                                                        2,
                                                    ),
                                                ),
                                            )
                                        }}
                                    </div>
                                </div>
                            </div>
                            <div class="page-form">
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        ><strong>Allocation Type:</strong>
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <span class="form-input-text">{{ item.allocationType.field_value }}</span>
                                    </v-col>
                                </v-row>
                            </div>
                            <div>
                                <table class="vue-data-grid ui tablet stackable table">
                                    <thead>
                                        <tr class="fieldDescription">
                                            <th class="text-start">#</th>
                                            <th class="text-start">
                                                <span
                                                    ><sui-checkbox
                                                        v-model="item.checkAllFlag"
                                                        :disabled="true"
                                                /></span>
                                            </th>
                                            <th class="text-start"><span>Unit</span></th>
                                            <th class="text-center"><span>SQM</span></th>
                                            <th class="text-center"><span>Parking Bays</span></th>
                                            <th class="text-start"><span>Lease Code</span></th>
                                            <th class="text-start"><span>Lease Name</span></th>
                                            <th class="text-right"><span>Percentage</span></th>
                                            <th class="text-right"><span>Cost</span></th>
                                        </tr>
                                    </thead>
                                    <tbody class="page-form">
                                        <tr
                                            class="form-row"
                                            v-for="(
                                                allocationBreakdownData, index
                                            ) in item.accountAllocationBreakdownArr"
                                            :key="index"
                                        >
                                            <td style="width: 1px">
                                                {{ index + 1 }}
                                            </td>
                                            <td style="width: 1px">
                                                <sui-checkbox
                                                    v-model="allocationBreakdownData.status"
                                                    :disabled="true"
                                                />
                                            </td>
                                            <td>
                                                {{ allocationBreakdownData.unitCode }}
                                            </td>
                                            <td class="text-center">
                                                {{ allocationBreakdownData.unitArea }}
                                            </td>
                                            <td class="text-center">
                                                {{ allocationBreakdownData.parkingBays }}
                                            </td>
                                            <td align="left">
                                                {{ allocationBreakdownData.leaseCode }}
                                            </td>
                                            <td align="left">
                                                {{ allocationBreakdownData.leaseName }}
                                            </td>

                                            <td
                                                align="right"
                                                class="text-right"
                                            >
                                                {{ accountingFormat(allocationBreakdownData.customPercentage) }}%
                                            </td>
                                            <td
                                                align="right"
                                                class="text-right"
                                            >
                                                {{
                                                    accountingFormat(
                                                        numberWithCommas(
                                                            roundTo(allocationBreakdownData.customValue, 2),
                                                        ),
                                                    )
                                                }}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </td>
                </template>

                <template slot="body.append">
                    <tr class="subHeader">
                        <th></th>
                        <th><strong>Total Expenses</strong></th>
                        <th
                            v-for="expOverallTotalColIndex in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
                            :key="expOverallTotalColIndex"
                        >
                            <strong>
                                <div class="right">
                                    {{
                                        accountingFormat(
                                            numberWithCommas(
                                                roundTo(getBudgetExpensesFinalTotal(expOverallTotalColIndex), 2),
                                            ),
                                        )
                                    }}
                                </div>
                            </strong>
                        </th>
                        <th class="text-right">
                            {{ accountingFormat(numberWithCommas(roundTo(getRowTotalFinalExp(), 2))) }}
                        </th>
                        <th></th>
                        <th></th>
                    </tr>
                </template>
            </v-data-table>
        </div>
    </div>
</template>

<script>
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import isEmpty from 'lodash/isEmpty';

export default {
    name: 'PropertyBudgetSection',
    props: {
        property_code: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            getColumnOverallTotalExpenses: ['0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00'],
            budgetExpensesArr: [],
            budget_overview_expenses: [],
            accountGroupList: [],
            expanded: [],
            expenses_headers: [],
            from_year_old_budget: '1900',
            to_year_old_budget: '2999',
            from_year: '',
            to_year: '',
            input_type_lbl: '',
            financial_year_lbl: '',
            from_date: '',
            to_date: '',
            year_list: [],
            unit_start_date: '',
            unit_start_date_2: '',
            unit_start_date_old: '',
            search_datatable: '',
            financial_year: { label: '', value: '', field_value: '', field_key: '' },
            actualYear: { label: '', value: '', field_value: '', field_key: '' },
            input_type: 1,
            is_strata_flag: false,
            budget_setting_flag: false,
            budget_setting_dimmer: false,
            refresh_index: Math.random(),
        };
    },
    methods: {
        accountingFormat: function (amount) {
            let evaluated_amount = eval(amount.toString().replace(/,/g, ''));
            let absolute_amount = Math.abs(evaluated_amount);
            let formatted_amount = this.numberWithCommas(absolute_amount);
            let accounting_amount;
            if (evaluated_amount < 0) {
                accounting_amount = '(' + formatted_amount + ')';
            } else {
                accounting_amount = amount;
            }
            return accounting_amount;
        },
        computeExpensesTotal: function () {
            this.getColumnOverallTotalExpenses[0] = this.computeColumnOverallTotalExp(0);
            this.getColumnOverallTotalExpenses[1] = this.computeColumnOverallTotalExp(1);
            this.getColumnOverallTotalExpenses[2] = this.computeColumnOverallTotalExp(2);
            this.getColumnOverallTotalExpenses[3] = this.computeColumnOverallTotalExp(3);
            this.getColumnOverallTotalExpenses[4] = this.computeColumnOverallTotalExp(4);
            this.getColumnOverallTotalExpenses[5] = this.computeColumnOverallTotalExp(5);
            this.getColumnOverallTotalExpenses[6] = this.computeColumnOverallTotalExp(6);
            this.getColumnOverallTotalExpenses[7] = this.computeColumnOverallTotalExp(7);
        },
        computeColumnOverallTotalExp: function (budgetAccountHeaderPeriodArrIndex) {
            let total = 0.0;

            for (let x = 0; x <= this.budgetExpensesArr.length - 1; x++) {
                let accountValue = eval(this.budgetExpensesArr[x].value.toString().replace(/,/g, ''));
                let accountValueLastYear = eval(this.budgetExpensesArr[x].valueLastYear.toString().replace(/,/g, ''));
                let accountActualLastYear = eval(this.budgetExpensesArr[x].actualLastYear.toString().replace(/,/g, ''));
                let accountTotalSQM = eval(this.budgetExpensesArr[x].totalSQM);
                switch (budgetAccountHeaderPeriodArrIndex) {
                    case 0:
                        if (typeof this.budgetExpensesArr[x].accountCode !== 'undefined') {
                            total = total + accountValueLastYear;
                        }
                        break;
                    case 1:
                        if (typeof this.budgetExpensesArr[x].accountCode !== 'undefined') {
                            total = total + accountActualLastYear;
                        }
                        break;
                    case 2:
                        if (typeof this.budgetExpensesArr[x].accountCode !== 'undefined') {
                            total = total + (accountValue - accountValueLastYear);
                        }
                        break;
                    case 3:
                        break;
                    case 4:
                        if (typeof this.budgetExpensesArr[x].accountCode !== 'undefined') {
                            total = total + (accountValue - accountActualLastYear);
                        }
                        break;
                    case 5:
                        break;
                    case 6:
                        if (typeof this.budgetExpensesArr[x].accountCode !== 'undefined') {
                            total = total + eval(this.budgetExpensesArr[x].value.toString().replace(/,/g, ''));
                        }
                        break;
                    case 7:
                        if (typeof this.budgetExpensesArr[x].accountCode !== 'undefined') {
                            total = total + eval(this.roundTo(accountValue / this.divisorClean(accountTotalSQM), 2));
                        }
                        break;
                }
            }
            return total;
        },
        async loadExpensesBudget() {
            this.budgetExpensesArr = this.objectClone([]);
            this.budget_overview_expenses = this.objectClone([]);
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('financialYear', this.financial_year.field_key);
            form_data.append('actualYear', 2023);
            form_data.append('inputType', this.input_type);
            form_data.append('actual_input_type', 0);
            form_data.append('remaining_input_type', 1);
            form_data.append('methodActual', 0);
            form_data.append('actualType', 0);
            form_data.append('copyToYear', '');
            form_data.append('actualPeriodMonthFrom', 7);
            form_data.append('actualPeriodMonthFromYear', 2022);
            form_data.append('actualPeriodMonthFromPeriod', 1);
            form_data.append('actualPeriodMonthTo', 7);
            form_data.append('actualPeriodMonthToYear', 2022);
            form_data.append('actualPeriodMonthToPeriod', 1);
            form_data.append('unit_start_date', '01/07/2023');
            form_data.append('copyFrom', '0');
            form_data.append('no_load', true);

            await this.$api.post('vue/property/budget/loadExpensesBudget', form_data).then((response) => {
                this.budgetExpensesArr = response.data.budgetExpensesArr;
                let accountGroupList = this.accountGroupList;
                let budgetExpensesArr = this.budgetExpensesArr;
                budgetExpensesArr.sort(function (a, b) {
                    if (a.accountCode < b.accountCode) return -1;
                    if (a.accountCode > b.accountCode) return 1;
                    return 0;
                });

                for (let x = 0; x <= accountGroupList.length - 1; x++) {
                    let groupCode = accountGroupList[x].groupCode;
                    let groupDescription = accountGroupList[x].groupDescription;

                    let filtered = budgetExpensesArr.filter((m) => m.account_GL === groupCode);
                    if (filtered.length > 0) {
                        for (let y = 0; y <= filtered.length - 1; y++) {
                            filtered[y]['accountGroupCode'] = groupCode;
                            filtered[y]['accountGroupDesc'] = groupDescription;
                            filtered[y]['accountGroupLastAccountCode'] = filtered[filtered.length - 1]['accountCode'];
                            this.budget_overview_expenses.push(filtered[y]);
                        }
                    }
                }

                for (let index in this.budget_overview_expenses) {
                    this.budget_overview_expenses[index]['account_name_with_code'] =
                        this.budget_overview_expenses[index].accountCode +
                        ' ' +
                        this.budget_overview_expenses[index].accountName;
                    let period_total = 0.0;
                    for (let index_2 in this.budget_overview_expenses[index].accountSplitBreakdownArr) {
                        let amount = this.budget_overview_expenses[index].accountSplitBreakdownArr[index_2].value;
                        this.budget_overview_expenses[index]['period_' + index_2] = amount;
                        period_total = period_total + Number(amount);
                    }
                    this.budget_overview_expenses[index]['period_total'] = period_total;
                }
                this.refresh_index = Math.random();
                this.budget_setting_dimmer = false;
                this.computeExpensesTotal();
            });
        },
        loadParameters: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('budget_version', 'V2');
            form_data.append('no_load', true);
            this.$api.post('vue/property/budget/loadBudgetParameters', form_data).then((response) => {
                this.year_list = response.data.yearList;
                this.is_strata_flag = response.data.is_strata_flag;
                // this.lease_profile_flag = response.data.lease_profile_flag;
                const financialYearKey = response.data.currentPeriodYear;
                if (financialYearKey === '') {
                    let dialog_prop = {
                        title: 'Error',
                        message: 'There is no property calendar found for property: ' + this.propertyID.field_key,
                        icon_show: true,
                        icon: 'error',
                    };
                    cirrusDialog(dialog_prop);
                } else {
                    const lastClosedPeriodYear = response.data.lastClosedPeriodYear;
                    const actualYearKey = response.data.currentPeriodYear - 1;
                    let filtered = this.year_list.filter((m) => m.calendarYear === financialYearKey);
                    this.from_date = filtered[0].calendarStartDate;
                    this.to_date = filtered[0].calendarEndDate;
                    this.unit_start_date = filtered[0].calendarStartDate;
                    this.unit_start_date_2 = filtered[0].calendarStartDate;
                    this.unit_start_date_old = filtered[0].calendarStartDate;
                    this.financial_year = {
                        label: response.data.currentPeriodYear,
                        value: response.data.currentPeriodYear,
                        field_key: response.data.currentPeriodYear,
                        field_value: response.data.currentPeriodYear,
                    };
                    switch (this.input_type) {
                        case 1:
                            this.input_type_lbl = 'Cash Budget';
                            break;
                        case 2:
                            this.input_type_lbl = 'Cash Forecast';
                            break;
                        case 3:
                            this.input_type_lbl = 'Accruals Budget';
                            break;
                        case 4:
                            this.input_type_lbl = 'Accruals Forecast';
                            break;
                    }
                    this.financial_year_lbl = response.data.currentPeriodYear;
                    this.loadForm();
                }
            });
        },
        getRowTotalExpBugdetOverview: function (budgetArrIndex) {
            let total = 0.0;
            if (this.budget_overview_expenses.length > 0) {
                for (
                    let z = 0;
                    z <= this.budget_overview_expenses[budgetArrIndex].accountSplitBreakdownArr.length - 1;
                    z++
                ) {
                    if (!isNaN(this.budget_overview_expenses[budgetArrIndex].accountSplitBreakdownArr[z].value)) {
                        total =
                            total +
                            eval(this.budget_overview_expenses[budgetArrIndex].accountSplitBreakdownArr[z].value);
                    }
                }
            }
            return this.roundTo(total, 2);
        },
        getBudgetExpensesFinalTotal: function (budgetArrIndex) {
            let total = 0.0;
            if (this.budget_overview_expenses.length > 0) {
                for (let x = 0; x < this.budget_overview_expenses.length; x++) {
                    for (let z = 0; z <= this.budget_overview_expenses[x].accountSplitBreakdownArr.length - 1; z++) {
                        if (this.budget_overview_expenses[x].accountSplitBreakdownArr[z].period === budgetArrIndex) {
                            if (!isNaN(this.budget_overview_expenses[x].accountSplitBreakdownArr[z].value)) {
                                total =
                                    total + eval(this.budget_overview_expenses[x].accountSplitBreakdownArr[z].value);
                            }
                        }
                    }
                }
            }
            return this.roundTo(total, 2);
        },
        getRowTotalFinalExp: function () {
            let total = 0.0;
            if (this.budget_overview_expenses.length > 0) {
                for (let x = 0; x < this.budget_overview_expenses.length; x++) {
                    for (let z = 0; z <= this.budget_overview_expenses[x].accountSplitBreakdownArr.length - 1; z++) {
                        if (!isNaN(this.budget_overview_expenses[x].accountSplitBreakdownArr[z].value)) {
                            total = total + eval(this.budget_overview_expenses[x].accountSplitBreakdownArr[z].value);
                        }
                    }
                }
            }
            return this.roundTo(total, 2);
        },
        totalPropertySqmBudgetOverview: function (index) {
            let getAllocationBreakdownArr = this.budget_overview_expenses[index].accountAllocationBreakdownArr;
            let totalUnitSQM = 0;
            for (let x = 0; x <= getAllocationBreakdownArr.length - 1; x++) {
                if (getAllocationBreakdownArr[x].status != '0' && getAllocationBreakdownArr[x].status) {
                    let getUnitArea = eval(getAllocationBreakdownArr[x].unitArea);
                    totalUnitSQM = totalUnitSQM + getUnitArea;
                }
            }
            return totalUnitSQM;
        },
        accountCodeTotalCostBudgetOverview: function (index) {
            let getAllocationBreakdownArr = this.budget_overview_expenses[index].accountAllocationBreakdownArr;
            let totalUnitCost = 0;
            for (let x = 0; x <= getAllocationBreakdownArr.length - 1; x++) {
                if (getAllocationBreakdownArr[x].status != '0' && getAllocationBreakdownArr[x].status) {
                    let getUnitCost = eval(getAllocationBreakdownArr[x].customValue);
                    totalUnitCost = totalUnitCost + getUnitCost;
                }
            }
            return totalUnitCost;
        },
        loadForm: function () {
            this.search_datatable = '';
            this.budget_setting_dimmer = true;
            this.financial_year_lbl = this.financial_year.field_key;
            switch (this.input_type) {
                case 1:
                    this.input_type_lbl = 'Cash Budget';
                    break;
                case 2:
                    this.input_type_lbl = 'Cash Forecast';
                    break;
                case 3:
                    this.input_type_lbl = 'Accruals Budget';
                    break;
                case 4:
                    this.input_type_lbl = 'Accruals Forecast';
                    break;
            }
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('financialYear', this.financial_year.field_key);
            form_data.append('actualYear', 2099);
            form_data.append('inputType', this.input_type);
            form_data.append('actual_input_type', 0);
            form_data.append('remaining_input_type', 1);
            form_data.append('actualType', 0);
            form_data.append('copyToYear', 0);
            form_data.append('unit_start_date', '01/07/2023');
            form_data.append('budget_version', 'V2');
            form_data.append('no_load', true);
            this.$api.post('vue/property/budget/loadBudgetParameterData', form_data).then((response) => {
                this.accountGroupList = response.data.accountGroupList;
                this.propertyCurrentPeriod = response.data.propertyCurrentPeriod;
                this.periodMonthsList = response.data.periodMonthsList;
                this.actualPeriodMonthFrom = response.data.actualPeriodMonthFrom;
                this.actualPeriodMonthTo = response.data.actualPeriodMonthTo;
                this.budgetLocked = response.data.budgetLocked;
                this.thereIsMISCLease = response.data.thereIsMISCLease;
                this.actualYear = {
                    label: response.data.actualYear,
                    value: response.data.actualYear,
                    field_value: response.data.actualYear,
                    field_key: response.data.actualYear,
                };
                this.expenses_headers = [
                    { text: '#', value: 'index_ui', sortable: false, width: '40px' },
                    { text: 'Name', value: 'account_name_with_code' },
                ];
                for (let index in this.propertyCurrentPeriod) {
                    this.expenses_headers.push({
                        text: this.propertyCurrentPeriod[index],
                        value: 'period_' + (index - 1),
                        align: 'end',
                        width: '100px',
                    });
                }
                this.expenses_headers.push({ text: 'Total', value: 'period_total', align: 'end', width: '100px' });
                this.expenses_headers.push({ text: '', value: 'action1', align: 'end', sortable: false });
                this.expenses_headers.push({
                    text: '',
                    value: 'data-table-expand',
                    class: ['data-table-mini-action'],
                    align: 'end',
                });
                this.loadExpensesBudget();
            });
        },
        showGroupLabel: function (status) {
            if (status == 0) return 'Active';
            if (status == 1) return 'Archived';
            return 'New';
        },
        checkIfEmpty: function (value) {
            return isEmpty(value);
        },
    },
    mounted() {
        this.loadParameters();
    },
    watch: {
        property_code: function () {
            this.loadParameters();
        },
        financial_year: function () {
            let filtered = this.year_list.filter((m) => m.calendarYear === this.financial_year.field_key);
            this.from_date = filtered[0].calendarStartDate;
            this.to_date = filtered[0].calendarEndDate;
            this.unit_start_date = filtered[0].calendarStartDate;

            this.from_year = this.from_date.substr(-2);
            this.to_year = this.to_date.substr(-2);

            let get_from_date = this.from_date.substr(-4);
            let get_to_date = this.to_year.substr(-4);

            let from_year_old_budget = (parseInt(get_from_date) - 1).toString();
            let to_year_old_budget = (parseInt(get_to_date) - 1).toString();
            this.from_year_old_budget = from_year_old_budget.substr(-2);
            this.to_year_old_budget = to_year_old_budget.substr(-2);
        },
    },
    created() {
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    created() {
        bus.$on('loadPropertyBudgetSection', (data) => {
            this.loadForm();
        });
    },
    mixins: [global_mixins],
};
</script>

<style scoped></style>
