<style>
.multiselect_short .multiselect__content-wrapper {
    max-height: 150px !important;
}
</style>
<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Fees and Charges</h6>
                <v-spacer></v-spacer>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <div
            class="sundry_charge_page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-card
                    class="section-toolbar subHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h6 class="title font-weight-black">Property Inspection Fee</h6>
                        <v-spacer></v-spacer>
                        <v-btn
                            x-small
                            v-show="isEditable() && !pmro_read_only"
                            class="v-step-edit-button"
                            v-if="!edit_form"
                            icon
                            @click="edit_form = true"
                        >
                            <v-icon>edit</v-icon>
                        </v-btn>
                        <v-btn
                            x-small
                            v-show="isEditable()"
                            class="v-step-refresh-button"
                            icon
                            @click="loadForm()"
                        >
                            <v-icon>refresh</v-icon>
                        </v-btn>
                        <v-btn
                            icon
                            x-small
                            v-show="isPropertyFormLive()"
                            @click="
                                show_activity_log_modal = true;
                                form_sub_section = 'PROPERTY_FEES_CHARGES_INSPECTION';
                            "
                        >
                            <v-icon>history</v-icon>
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </div>
            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <div
                class="page-form"
                v-if="!loading_setting"
            >
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    Inspection
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form"
                                        v-if="property_inspection_index === 0"
                                        >Yes</span
                                    >
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form"
                                        v-if="property_inspection_index === 1"
                                        >No</span
                                    >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-show="edit_form"
                                        v-model="property_inspection_index"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form"
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form"
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row no-gutters"
                    v-if="property_inspection_index === 0"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    Inspection Amount:
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        inputFormat="dollar"
                                        :size="'10'"
                                        :id="'property_inspection_amount'"
                                        v-model="property_inspection_amount"
                                        :edit_form="edit_form"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row no-gutters"
                    v-if="property_inspection_index === 0"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    Payment Account:
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <multiselect
                                        v-if="edit_form"
                                        v-model="property_inspection_payment_account"
                                        :options="inspection_payment_account_list"
                                        :custom-label="nameWithDash"
                                        group-values="field_group_values"
                                        :groupSelect="false"
                                        group-label="field_group_names"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-300"
                                        placeholder="Select an account"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <span
                                        v-if="!edit_form"
                                        class="form-input-text"
                                        >{{ property_inspection_payment_account.field_key }} -
                                        {{ property_inspection_payment_account.field_value }}</span
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row no-gutters"
                    v-if="property_inspection_index === 0"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    Frequency:
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form"
                                        v-if="property_inspection_frequency_index === 0"
                                        >Monthly</span
                                    >
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form"
                                        v-if="property_inspection_frequency_index === 1"
                                        >Quarterly</span
                                    >
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form"
                                        v-if="property_inspection_frequency_index === 2"
                                        >Semi-Annual</span
                                    >
                                    <span
                                        class="form-input-text"
                                        v-show="!edit_form"
                                        v-if="property_inspection_frequency_index === 3"
                                        >Annual</span
                                    >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-show="edit_form"
                                        v-model="property_inspection_frequency_index"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form"
                                        >
                                            Monthly
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form"
                                        >
                                            Quarterly
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form"
                                        >
                                            Semi-Annual
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form"
                                        >
                                            Annual
                                        </v-btn>
                                    </v-btn-toggle>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </div>
            <div class="form-row">
                <v-card
                    class="section-toolbar subHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h6 class="title font-weight-black">Rent Review Fee</h6>
                        <v-spacer></v-spacer>
                        <v-btn
                            x-small
                            v-show="isEditable() && !pmro_read_only"
                            class="v-step-edit-button"
                            v-if="!edit_form"
                            icon
                            @click="edit_form = true"
                        >
                            <v-icon>edit</v-icon>
                        </v-btn>
                        <v-btn
                            x-small
                            v-show="isEditable()"
                            class="v-step-refresh-button"
                            icon
                            @click="loadForm()"
                        >
                            <v-icon>refresh</v-icon>
                        </v-btn>
                        <v-btn
                            icon
                            x-small
                            v-show="isPropertyFormLive()"
                            @click="
                                show_activity_log_modal = true;
                                form_sub_section = 'PROPERTY_FEES_CHARGES_RENT_REVIEW';
                            "
                        >
                            <v-icon>history</v-icon>
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </div>
        </div>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Rent Review Fee
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <span
                                    class="form-input-text"
                                    v-show="!edit_form"
                                    v-if="property_rent_review_index === 0"
                                    >Yes</span
                                >
                                <span
                                    class="form-input-text"
                                    v-show="!edit_form"
                                    v-if="property_rent_review_index === 1"
                                    >No</span
                                >
                                <v-btn-toggle
                                    class="form-toggle"
                                    v-show="edit_form"
                                    v-model="property_rent_review_index"
                                    mandatory
                                >
                                    <v-btn
                                        small
                                        text
                                        :disabled="!edit_form"
                                    >
                                        Yes
                                    </v-btn>
                                    <v-btn
                                        small
                                        text
                                        :disabled="!edit_form"
                                    >
                                        No
                                    </v-btn>
                                </v-btn-toggle>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <v-row
                class="form-row no-gutters"
                v-if="property_rent_review_index === 0"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Amount:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <cirrus-input
                                    inputFormat="dollar"
                                    :size="'10'"
                                    :id="'property_rent_review_amount'"
                                    v-model="property_rent_review_amount"
                                    :edit_form="edit_form"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <v-row
                class="form-row no-gutters"
                v-if="property_rent_review_index === 0"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Description:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <cirrus-input
                                    :size="'10'"
                                    :id="'property_rent_review_description'"
                                    v-model="property_rent_review_description"
                                    :edit_form="edit_form"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <v-row
                class="form-row no-gutters"
                v-if="property_rent_review_index === 0"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                Expense Account:
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <multiselect
                                    v-if="edit_form"
                                    v-model="property_rent_review_expenses_account"
                                    :options="inspection_payment_account_list"
                                    :custom-label="nameWithDash"
                                    :allowEmpty="false"
                                    group-values="field_group_values"
                                    :groupSelect="false"
                                    group-label="field_group_names"
                                    class="vue-select2 dropdown-left dropdown-300"
                                    :allow-empty="false"
                                    placeholder="Select an account"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                                <span
                                    v-if="!edit_form"
                                    class="form-input-text"
                                    >{{ property_rent_review_expenses_account.field_key }} -
                                    {{ property_rent_review_expenses_account.field_value }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <v-divider></v-divider>
            <v-card
                elevation="0"
                v-if="edit_form"
            >
                <v-card-actions v-if="edit_form">
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="saveForm()"
                        color="success"
                        dark
                        small
                    >
                        Save Inspection and Rent Review Fee
                    </v-btn>
                </v-card-actions>
            </v-card>
        </div>
        <div
            class="form-row"
            v-if="!loading_setting"
        >
            <v-card
                class="section-toolbar subHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Pending One-Off Sundries</h6>
                    <v-spacer></v-spacer>
                    <cirrus-input
                        inputFormat="search"
                        v-if="isEditable()"
                        v-model="pending_sundry_charge_search_datatable"
                        placeholder="Search"
                        :edit_form="true"
                        style="padding-right: 1em"
                    ></cirrus-input>
                    <v-btn
                        x-small
                        v-show="isEditable() && !pmro_read_only"
                        icon
                        @click="modalPendingAddData()"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="isEditable() && !pmro_read_only"
                        class="v-step-edit-button"
                        v-if="!edit_form"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>

                    <v-btn
                        x-small
                        v-show="isEditable()"
                        class="v-step-refresh-button"
                        icon
                        @click="loadForm()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        icon
                        x-small
                        v-show="isPropertyFormLive()"
                        @click="
                            show_activity_log_modal = true;
                            form_sub_section = 'PROPERTY_FEES_CHARGES_PENDING_SUNDRIES';
                        "
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
            <v-col
                class="text-center"
                v-if="pending_sundry_charge_list.length === 0 && !loading_setting"
                v-show="isEditable()"
            >
                <v-btn
                    v-if="!pmro_read_only"
                    depressed
                    small
                    color="success"
                    @click="modalPendingAddData()"
                    >Add Pending One-Off Sundries
                </v-btn>
                <div
                    style="margin: 10px 0px"
                    v-else
                >
                    No pending one-off sundries at the moment
                </div>
            </v-col>
            <!--datatable start-->
            <v-data-table
                v-show="pending_sundry_charge_list.length > 0"
                class="c8-datatable-custom"
                dense
                item-key="id"
                :headers="pending_sundry_charge_headers"
                :items="pending_sundry_charge_list"
                :items-per-page="pending_sundry_charge_items_per_page"
                hide-default-footer
                :page.sync="pending_sundry_charge_page"
                :total-visible="7"
                @page-count="pending_sundry_charge_page_count = $event"
                :search="pending_sundry_charge_search_datatable"
                :calculate-widths="true"
            >
                <template v-slot:item.index="{ item }">
                    {{ pending_sundry_charge_list.indexOf(item) + 1 }}
                </template>
                <template v-slot:item.item_no="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.item_no }}</span>
                    </div>
                </template>
                <template v-slot:item.adhoc_type_description="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.adhoc_type_description }}</span>
                    </div>
                </template>
                <template v-slot:item.adhoc_description="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.adhoc_description }}</span>
                    </div>
                </template>
                <template v-slot:item.adhoc_account_description="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.adhoc_account_description }}</span>
                    </div>
                </template>
                <template v-slot:item.adhoc_amount="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ currency_symbol }}{{ item.adhoc_amount }}</span>
                    </div>
                </template>
                <template v-slot:item.adhoc_created_at="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.adhoc_created_at }}</span>
                    </div>
                </template>

                <template v-slot:item.action1="{ item }">
                    <v-icon
                        small
                        @click="modalOpenPendingAED(pending_sundry_charge_list.indexOf(item))"
                        v-if="edit_form"
                        >fas fa-edit
                    </v-icon>
                    <v-icon
                        color="red"
                        v-if="edit_form"
                        @click="deletePendingSundry(pending_sundry_charge_list.indexOf(item))"
                    >
                        close
                    </v-icon>
                </template>
            </v-data-table>
            <v-row
                class="form-row"
                v-show="pending_sundry_charge_list.length > 5"
                v-if="isEditable()"
            >
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                >
                    <table class="c8-datatable-custom-footer">
                        <tr>
                            <td class="">Rows per page:</td>
                            <td>
                                <multiselect
                                    v-model="pending_sundry_charge_items_per_page"
                                    :options="[5, 10, 15]"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-200"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </td>
                            <td></td>
                            <td>
                                <v-pagination
                                    v-model="pending_sundry_charge_page"
                                    :length="pending_sundry_charge_page_count"
                                    :total-visible="5"
                                ></v-pagination>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--datatable end-->
            <v-card
                class="section-toolbar subHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Sundry Charges</h6>
                    <v-spacer></v-spacer>
                    <cirrus-input
                        inputFormat="search"
                        v-if="isEditable()"
                        v-model="sundry_charge_search_datatable"
                        placeholder="Search"
                        :edit_form="true"
                        style="padding-right: 1em"
                    ></cirrus-input>
                    <v-btn
                        x-small
                        v-show="isEditable() && !pmro_read_only"
                        icon
                        @click="modalSundryAddData()"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="isEditable() && !pmro_read_only"
                        class="v-step-edit-button"
                        v-if="!edit_form"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>

                    <v-btn
                        x-small
                        v-show="isEditable()"
                        class="v-step-refresh-button"
                        icon
                        @click="loadForm()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        icon
                        x-small
                        v-show="isPropertyFormLive()"
                        @click="
                            show_activity_log_modal = true;
                            form_sub_section = 'PROPERTY_FEES_CHARGES_SUNDRIES';
                        "
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
            <v-col
                class="text-center"
                v-if="sundry_charge_list.length === 0 && !loading_setting"
                v-show="isEditable()"
            >
                <v-btn
                    v-if="!pmro_read_only"
                    depressed
                    small
                    color="success"
                    @click="modalSundryAddData()"
                    >Add Sundry Charge
                </v-btn>
                <div
                    style="margin: 10px 0px"
                    v-else
                >
                    No sundry charges at the moment
                </div>
            </v-col>
            <!--datatable start-->
            <v-data-table
                v-show="sundry_charge_list.length > 0"
                class="c8-datatable-custom"
                dense
                item-key="id"
                :headers="sundry_charge_headers"
                :items="sundry_charge_list"
                :items-per-page="sundry_charge_items_per_page"
                hide-default-footer
                :page.sync="sundry_charge_page"
                :total-visible="7"
                @page-count="sundry_charge_page_count = $event"
                :search="sundry_charge_search_datatable"
                :calculate-widths="true"
            >
                <template v-slot:item.index="{ item }">
                    {{ sundry_charge_list.indexOf(item) + 1 }}
                </template>
                <template v-slot:item.item_no="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.item_no }}</span>
                    </div>
                </template>
                <template v-slot:item.adhoc_type_description="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.adhoc_type_description }}</span>
                    </div>
                </template>
                <template v-slot:item.adhoc_description="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.adhoc_description }}</span>
                    </div>
                </template>
                <template v-slot:item.adhoc_account_description="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.adhoc_account_description }}</span>
                    </div>
                </template>
                <template v-slot:item.sundry_value="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ currency_symbol }}{{ item.sundry_value }}</span>
                    </div>
                </template>
                <template v-slot:item.sundry_charging_method_description="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.sundry_charging_method_description }}</span>
                    </div>
                </template>

                <template v-slot:item.action1="{ item }">
                    <v-icon
                        small
                        @click="modalOpenSundryAED(sundry_charge_list.indexOf(item))"
                        v-if="edit_form"
                        >fas fa-edit
                    </v-icon>
                    <v-icon
                        color="red"
                        v-if="edit_form"
                        @click="deleteSundry(sundry_charge_list.indexOf(item))"
                        >close
                    </v-icon>
                </template>
            </v-data-table>
            <v-row
                class="form-row"
                v-show="sundry_charge_list.length > 5"
                v-if="isEditable()"
            >
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                >
                    <table class="c8-datatable-custom-footer">
                        <tr>
                            <td class="">Rows per page:</td>
                            <td>
                                <multiselect
                                    v-model="sundry_charge_items_per_page"
                                    :options="[5, 10, 15]"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-200"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </td>
                            <td></td>
                            <td>
                                <v-pagination
                                    v-model="sundry_charge_page"
                                    :length="sundry_charge_page_count"
                                    :total-visible="5"
                                ></v-pagination>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--datatable end-->
        </div>

        <!--   AED modal      -->
        <v-dialog
            v-model="pending_AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPendingPrevData()"
            @keydown.ctrl.right="modalPendingNextData()"
            @keydown.ctrl.shift.enter="modalPendingAddData()"
            @keydown.ctrl.enter="modalSubmitPendingData()"
            @keydown.ctrl.delete="deletePendingSundry(pending_sundry_charge_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Pending One-Off Sundries Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="pending_AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!---->
                    <div :key="pending_sundry_charge_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        pending_sundry_charge_arr.index === 'New'
                                            ? pending_sundry_charge_arr.index
                                            : pending_sundry_charge_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Account:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input multiselect_short"
                                >
                                    <multiselect
                                        data-inverted=""
                                        :custom-label="nameWithDash"
                                        v-model="pending_sundry_charge_arr.adhoc_account"
                                        :options="account_exp_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select an account"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Type:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input multiselect_short"
                                >
                                    <multiselect
                                        data-inverted=""
                                        v-model="pending_sundry_charge_arr.adhoc_type"
                                        :options="adhoc_type_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        group-label="language"
                                        placeholder="Select a type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Description:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="pending_sundry_charge_arr.adhoc_description"
                                        size=""
                                        :id="'adhoc_description' + pending_sundry_charge_arr.index"
                                        data-inverted=""
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Amount:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="pending_sundry_charge_arr.adhoc_amount"
                                        size=""
                                        :id="'adhoc_amount' + pending_sundry_charge_arr.index"
                                        data-inverted=""
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPendingPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous
                        </v-icon>
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPendingAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="pending_sundry_charge_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add
                        </v-icon>
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitPendingData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Submit
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPendingAddData()"
                        v-if="pending_sundry_charge_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deletePendingSundry(pending_sundry_charge_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="pending_sundry_charge_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPendingNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next
                        </v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->

        <!--   AED modal      -->
        <v-dialog
            v-model="sundry_AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalSundryPrevData()"
            @keydown.ctrl.right="modalSundryNextData()"
            @keydown.ctrl.shift.enter="modalSundryAddData()"
            @keydown.ctrl.enter="modalSubmitSundryData()"
            @keydown.ctrl.delete="deleteSundry(sundry_charge_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Sundry Charge Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="sundry_AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!---->
                    <div :key="sundry_charge_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        sundry_charge_arr.index === 'New'
                                            ? sundry_charge_arr.index
                                            : sundry_charge_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Account:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input multiselect_short"
                                >
                                    <multiselect
                                        data-inverted=""
                                        :custom-label="nameWithDash"
                                        v-model="sundry_charge_arr.sundry_account"
                                        :options="account_exp_sundry_list"
                                        group-values="field_group_values"
                                        :groupSelect="false"
                                        group-label="field_group_names"
                                        :group-select="true"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select an account"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="isMultiplePropertyLedger"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Property Ledger:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input multiselect_short"
                                >
                                    <cirrus-single-select-v2
                                        v-model="sundry_charge_arr.propertyLedgerId"
                                        :options="property_ledger_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Please select"
                                        :custom-label="nameWithCodeDash"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Description:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="sundry_charge_arr.sundry_description"
                                        size=""
                                        :id="'sundry_description' + sundry_charge_arr.index"
                                        data-inverted=""
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Amount:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="sundry_charge_arr.sundry_value"
                                        size=""
                                        :id="'sundry_value' + sundry_charge_arr.index"
                                        data-inverted=""
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Charging Method:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input multiselect_short"
                                >
                                    <multiselect
                                        data-inverted=""
                                        v-model="sundry_charge_arr.sundry_charging_method"
                                        :options="charging_method_list"
                                        :allowEmpty="false"
                                        :optionHeight="10"
                                        :maxHeight="20"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        group-label="language"
                                        placeholder="Select a method"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSundryPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous
                        </v-icon>
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSundryAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="sundry_charge_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add
                        </v-icon>
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitSundryData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Submit
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSundryAddData()"
                        v-if="sundry_charge_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteSundry(sundry_charge_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="sundry_charge_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSundryNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next
                        </v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_sub_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';
import { fetchPropertyLedger } from '../../../../modules/Property/lib/ledger';
import isNil from 'lodash/isNil';
import {
    fetchFeesAndChargesData,
    saveSundryData,
    updateSundryData,
} from '../../../../modules/Property/lib/PropertyFeesAndCharges';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_FEES_CHARGES',
            form_sub_section: '',
            loading_setting: false,
            edit_form: false,
            show_activity_log_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            sundry_charge_headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Account', value: 'sundry_account_description' },
                { text: 'Description', value: 'sundry_description' },
                { text: 'Amount', value: 'sundry_value', sortable: false, align: 'middle' },
                { text: 'Charging Method', value: 'sundry_charging_method_description', sortable: false },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            sundry_charge_page: 1,
            sundry_charge_page_count: 0,
            sundry_charge_items_per_page: 5,
            sundry_charge_search_datatable: '',
            pending_sundry_charge_headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Type', value: 'adhoc_type_description' },
                { text: 'Description', value: 'adhoc_description' },
                { text: 'Account', value: 'adhoc_account_description', sortable: false },
                { text: 'Amount', value: 'adhoc_amount', sortable: false, align: 'right' },
                { text: 'Created Date', value: 'adhoc_created_at', sortable: false },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            pending_sundry_charge_page: 1,
            pending_sundry_charge_page_count: 0,
            pending_sundry_charge_items_per_page: 5,
            pending_sundry_charge_search_datatable: '',
            pending_AED_modal: false,
            sundry_AED_modal: false,
            success_flag: false,
            show_details_modal: false,
            modal_current_ctr: 0,
            sundry_charge_list: [],
            pending_sundry_charge_list: [],
            sundry_charge_arr: [],
            pending_sundry_charge_arr: [],
            adhoc_type_list: [],
            account_exp_sundry_list: [],
            account_exp_list: [],
            charging_method_list: [
                { field_key: '-1', field_value: 'Always Charged' },
                { field_key: '0', field_value: 'Monthly' },
                { field_key: '1', field_value: 'Yearly - January' },
                { field_key: '2', field_value: 'Yearly - February' },
                { field_key: '3', field_value: 'Yearly - March' },
                { field_key: '4', field_value: 'Yearly - April' },
                { field_key: '5', field_value: 'Yearly - May' },
                { field_key: '6', field_value: 'Yearly - June' },
                { field_key: '7', field_value: 'Yearly - July' },
                { field_key: '8', field_value: 'Yearly - August' },
                { field_key: '9', field_value: 'Yearly - September' },
                { field_key: '10', field_value: 'Yearly - October' },
                { field_key: '11', field_value: 'Yearly - November' },
                { field_key: '12', field_value: 'Yearly - December' },
            ],
            property_inspection_index: 1,
            property_inspection_amount: '0.00',
            property_inspection_payment_account: { field_key: '', field_value: '' },
            inspection_payment_account_list: [],
            property_inspection_frequency_index: 0,
            property_inspection_recoverable_index: 1,
            property_inspection_recoverable_account: { field_key: '', field_value: '' },
            inspection_recoverable_account_list: [],
            property_rent_review_index: 1,
            property_rent_review_amount: '0.00',
            property_rent_review_description: '',
            property_rent_review_expenses_account: { field_key: '', field_value: '' },
            currency_symbol: '$',
            property_ledger_list: [],
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.sundry_charge_headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                { text: 'Account', value: 'sundry_account_description', width: 'auto' },
                { text: 'Description', value: 'sundry_description', width: 'auto' },
                { text: 'Amount', value: 'sundry_value', sortable: false, align: 'middle', width: 'auto' },
                {
                    text: 'Charging Method',
                    value: 'sundry_charging_method_description',
                    sortable: false,
                    width: 'auto',
                },
            ];
            this.pending_sundry_charge_headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                { text: 'Type', value: 'adhoc_type_description', width: 'auto' },
                { text: 'Description', value: 'adhoc_description', width: 'auto' },
                { text: 'Account', value: 'adhoc_account_description', sortable: false, width: 'auto' },
                { text: 'Amount', value: 'adhoc_amount', sortable: false, align: 'right', width: 'auto' },
                { text: 'Created Date', value: 'adhoc_created_at', sortable: false, width: 'auto' },
            ];
            this.sundry_charge_items_per_page = 9999;
            this.pending_sundry_charge_items_per_page = 9999;
        }
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        resetForm: function () {
            this.edit_form = false;
        },
        loadForm: function () {
            this.error_server_msg = {};
            if (this.property_code !== '') {
                this.loadLedgerList();
                this.loadFeesNCharges();
            }
        },
        loadPropertyDetailsLists: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            this.$api.post('property/fetch/property-details-lists', form_data).then((response) => {
                this.bank_account_list = response.data.bank_account_list;
                this.report_type_list = response.data.report_type_list;
                this.property_bond_list = response.data.property_bond_list;
                this.inspection_payment_account_list = response.data.inspection_payment_account_list;
                this.inspection_recoverable_account_list = response.data.inspection_recoverable_account_list;
            });
        },
        async loadFeesNCharges() {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                const data = await fetchFeesAndChargesData(form_data);
                this.loadResponseToVariables(data);
                this.edit_form = false;
            } else {
                let apiUrl = 'temp/property/fetch/fees-and-charges';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);
                    this.edit_form = false;
                });
            }
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.pending_sundry_charge_list = response.data.pending_sundry_charge_list;
            this.sundry_charge_list = response.data.sundry_charge_list;
            this.adhoc_type_list = this.withFieldProps(response.data.adhoc_type_list);
            this.property_inspection_index = response.data.property_inspection_index;
            this.property_inspection_amount = response.data.property_inspection_amount;
            this.property_inspection_payment_account = response.data.property_inspection_payment_account;
            this.property_inspection_frequency_index = response.data.property_inspection_frequency_index;
            this.property_inspection_recoverable_index = response.data.property_inspection_recoverable_index;
            this.property_inspection_recoverable_account = response.data.property_inspection_recoverable_account;
            this.property_rent_review_index = response.data.property_rent_review_index;
            if (this.property_rent_review_index === 0) {
                this.property_rent_review_amount = response.data.property_rent_review_amount;
                this.property_rent_review_description = response.data.property_rent_review_description;
                this.property_rent_review_expenses_account = response.data.property_rent_review_expenses_account;
            }
            this.currency_symbol = response.data.currency_symbol;
        },
        modalPendingPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.pending_sundry_charge_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.pending_sundry_charge_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.pending_sundry_charge_arr = this.pending_sundry_charge_list[this.modal_current_ctr];
            this.pending_sundry_charge_arr.index = this.modal_current_ctr;
        },
        saveForm: function () {
            this.error_server_msg2 = [];
            let property_inspection_index = this.property_inspection_index;
            let property_inspection_amount = this.property_inspection_amount;
            let property_inspection_payment_account = this.property_inspection_payment_account.field_key;
            let property_inspection_frequency_index = this.property_inspection_frequency_index;
            let error_server_msg2 = [];
            if (property_inspection_index === 0) {
                if (property_inspection_payment_account === '')
                    error_server_msg2.push(['You have not entered a valid inspection account.']);
                if (property_inspection_amount === '')
                    error_server_msg2.push(['You have not entered a valid inspection amount.']);
                else if (isNaN(parseFloat(property_inspection_amount)))
                    error_server_msg2.push(['You have not entered a valid inspection amount.']);
            }
            let property_rent_review_index = this.property_rent_review_index;
            let property_rent_review_amount = this.property_rent_review_amount;
            let property_rent_review_description = this.property_rent_review_description;
            let property_rent_review_expenses_account = this.property_rent_review_expenses_account.field_key;
            if (property_rent_review_index === 0) {
                if (property_rent_review_expenses_account === '')
                    error_server_msg2.push(['You have not entered a valid rent review account.']);
                if (property_rent_review_description === '')
                    error_server_msg2.push(['You have not entered a valid description.']);
                if (property_rent_review_amount === '')
                    error_server_msg2.push(['You have not entered a valid rent review amount.']);
                else if (isNaN(parseFloat(property_rent_review_amount)))
                    error_server_msg2.push(['You have not entered a valid rent review amount.']);
            }
            this.error_server_msg2 = error_server_msg2;

            if (error_server_msg2.length === 0) {
                this.loading_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('property_inspection_index', property_inspection_index);
                form_data.append('property_inspection_amount', property_inspection_amount);
                form_data.append('property_inspection_payment_account', property_inspection_payment_account);
                form_data.append('property_inspection_frequency_index', property_inspection_frequency_index);
                form_data.append('property_rent_review_index', property_rent_review_index);
                form_data.append('property_rent_review_amount', property_rent_review_amount);
                form_data.append('property_rent_review_description', property_rent_review_description);
                form_data.append('property_rent_review_expenses_account', property_rent_review_expenses_account);

                let dataStatus = this.isPropertyFormLive() ? 'approved' : 'unapproved';
                const apiUrl = `property/update/${dataStatus}/inspection-n-rent-review-fee`;
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loading_setting = false;
                    this.error_server_msg2 = response.data.error_server_msg;
                    if (this.error_server_msg2.length <= 0) {
                        this.loadFeesNCharges();
                        if (this.new_property) this.edit_form = true;
                        else this.edit_form = false;
                        this.$emit('loadPropertyInspectionSection', []);
                    }
                    if (this.new_property) {
                        if (this.error_server_msg2.length === 0) {
                            this.$emit('returnPropertyCode', {
                                property_code: this.property_code,
                                property_name: this.property_name,
                            });
                            this.$emit('returnPropertyIsExisted', {
                                property_code: this.property_code,
                                property_name: this.property_name,
                            });
                            this.$emit('returnPropertyFormSuccess', {
                                property_code: this.property_code,
                                property_name: this.property_name,
                            });
                        }
                    }
                });
            }
        },
        modalPendingNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.pending_sundry_charge_arr.index;
            if (current_index === 'New') this.modal_current_ctr = 0;
            else {
                current_index = current_index + 1;
                if (current_index > this.pending_sundry_charge_list.length - 1) this.modal_current_ctr = 0;
                else this.modal_current_ctr = current_index;
            }
            this.pending_sundry_charge_arr = this.pending_sundry_charge_list[this.modal_current_ctr];
            this.pending_sundry_charge_arr.index = this.modal_current_ctr;
        },
        modalOpenPendingAED: function (index) {
            this.edit_form = true;
            this.pending_AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.pending_sundry_charge_arr = this.pending_sundry_charge_list[index];
            this.pending_sundry_charge_arr.index = index;
            this.modal_current_ctr = index;
        },
        modalPendingAddData: function () {
            this.edit_form = true;
            this.pending_AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.pending_sundry_charge_arr = {
                index: 'New',
                adhoc_type: { field_key: '', field_value: 'Please select...' },
                adhoc_account: { field_key: '', field_value: 'Please select...' },
                adhoc_description: '',
                adhoc_amount: '',
                status: 'new',
            };
        },
        modalSundryPrevData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.sundry_charge_arr.index;
            if (current_index === 'New') this.modal_current_ctr = 0;
            else {
                current_index = current_index - 1;
                if (current_index === -1) this.modal_current_ctr = this.sundry_charge_list.length - 1;
                else this.modal_current_ctr = current_index;
            }
            this.sundry_charge_arr = this.sundry_charge_list[this.modal_current_ctr];
            this.sundry_charge_arr.index = this.modal_current_ctr;
        },
        modalSundryNextData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.sundry_charge_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.sundry_charge_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.sundry_charge_arr = this.sundry_charge_list[this.modal_current_ctr];
            this.sundry_charge_arr.index = this.modal_current_ctr;
        },
        modalOpenSundryAED: function (index) {
            this.sundry_AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.sundry_charge_arr = this.sundry_charge_list[index];
            this.sundry_charge_arr.index = index;
            this.modal_current_ctr = index;
        },
        modalSundryAddData: function () {
            this.edit_form = true;
            this.sundry_AED_modal = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.sundry_charge_arr = {
                index: 'New',
                sundry_account: { field_key: '', field_value: 'Please select...' },
                propertyLedgerId: '',
                sundry_charging_method: { field_key: '', field_value: 'Please select...' },
                sundry_description: '',
                sundry_value: '',
                sundry_id: '',
                status: 'new',
            };
        },
        modalSubmitPendingData: function () {
            let index = this.pending_sundry_charge_arr.index;
            let status = this.pending_sundry_charge_arr.status;
            let adhoc_id = this.pending_sundry_charge_arr.adhoc_id;
            let adhoc_type_code = this.pending_sundry_charge_arr.adhoc_type.field_key;
            let adhoc_account_code = this.pending_sundry_charge_arr.adhoc_account.field_key;
            let adhoc_description = this.pending_sundry_charge_arr.adhoc_description;
            let adhoc_amount = this.pending_sundry_charge_arr.adhoc_amount;

            let error_server_msg2 = [];
            if (adhoc_type_code === '') error_server_msg2.push(['You have not provided a valid type.']);
            if (adhoc_account_code === '') error_server_msg2.push(['You have not provided a valid account.']);
            if (adhoc_description === '') error_server_msg2.push(['You have not provided a valid description.']);
            if (adhoc_amount === '') error_server_msg2.push(['You have not provided a valid amount.']);

            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('status', status);
                form_data.append('adhoc_id', adhoc_id);
                form_data.append('adhoc_type_code', adhoc_type_code);
                form_data.append('adhoc_account_code', adhoc_account_code);
                form_data.append('adhoc_description', adhoc_description);
                form_data.append('adhoc_amount', adhoc_amount);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/update-or-create/pending-sundry-charge';
                } else {
                    apiUrl = 'temp/property/update-or-create/pending-sundry-charge';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.error_server_msg2 = response.data.error_server_msg2;
                    this.loadForm();
                    this.loading_setting = false;
                    if (this.error_server_msg2.length === 0 && status === 'new') {
                        this.pending_AED_modal = false;
                    }
                    if (this.error_server_msg2.length === 0) {
                        this.success_flag = true;
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        async modalSubmitSundryData() {
            let index = this.sundry_charge_arr.index;
            let status = this.sundry_charge_arr.status;
            let sundry_id = this.sundry_charge_arr.sundry_id;
            let sundry_account = this.sundry_charge_arr.sundry_account.field_key;
            let propertyLedgerId = this.sundry_charge_arr.propertyLedgerId;
            let sundry_charging_method = this.sundry_charge_arr.sundry_charging_method.field_key;
            let sundry_description = this.sundry_charge_arr.sundry_description;
            let sundry_value = this.sundry_charge_arr.sundry_value;

            let error_server_msg2 = [];
            if (sundry_charging_method === '') error_server_msg2.push(['You have not provided a valid charging type.']);
            if (sundry_account === '') error_server_msg2.push(['You have not provided a valid account.']);
            if (sundry_description === '') error_server_msg2.push(['You have not provided a valid description.']);
            if (sundry_value === '') error_server_msg2.push(['You have not provided a valid amount.']);

            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('status', status);
                form_data.append('sundry_id', sundry_id);
                form_data.append('sundry_account', sundry_account);
                form_data.append('propertyLedgerId', propertyLedgerId);
                form_data.append('sundry_charging_method', sundry_charging_method);
                form_data.append('sundry_description', sundry_description);
                form_data.append('sundry_value', sundry_value);
                form_data.append('no_load', true);

                if (this.isPropertyFormLive()) {
                    try {
                        let data;
                        if (isNil(sundry_id)) {
                            data = await saveSundryData(form_data);
                        } else {
                            data = await updateSundryData(form_data);
                        }
                        if (data) {
                            this.loadForm();
                        }
                    } catch (errors) {
                        if (typeof errors === 'object') {
                            this.error_server_msg2 = [Object.values(errors).flat()];
                        } else {
                            this.error_server_msg2 = ['An unexpected error occurred'];
                        }
                    }
                } else {
                    let apiUrl = 'temp/property/update-or-create/sundry-charge';
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.error_server_msg2 = response.data.error_server_msg2;
                        this.loadForm();
                        this.loading_setting = false;
                        if (this.error_server_msg2.length === 0 && status === 'new') {
                            this.sundry_AED_modal = false;
                        }
                        if (this.error_server_msg2.length === 0) {
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        }
                    });
                }
            }
        },
        loadExpSundryAccounts: function () {
            this.$api.post('ui/fetch/accounts-exp-sundry-list').then((response) => {
                this.account_exp_sundry_list = response.data.grouped;
            });
        },
        loadExpAccounts: function () {
            this.$api.post('ui/fetch/account-exp-lists').then((response) => {
                this.account_exp_list = response.data.ungrouped;
            });
        },
        async deletePendingSundry(index) {
            if (index !== 'New') {
                let status = this.pending_sundry_charge_list[index].status;
                let adhoc_id = this.pending_sundry_charge_list[index].adhoc_id;

                if (status === 'new') {
                    this.pending_sundry_charge_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('adhoc_id', adhoc_id);
                        form_data.append('no_load', true);
                        let apiUrl = 'temp/property/delete/pending-sundry-charge';
                        if (this.isPropertyFormLive()) apiUrl = 'property/delete/pending-sundry-charge';

                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.pending_sundry_charge_list.splice(index, 1);
                            this.loading_setting = false;
                        });
                    }
                }
            }
        },
        async deleteSundry(index) {
            if (index !== 'New') {
                let status = this.sundry_charge_list[index].status;
                let sundry_id = this.sundry_charge_list[index].sundry_id;

                if (status === 'new') {
                    this.sundry_charge_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('sundry_id', sundry_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/sundry-charge';
                        } else {
                            apiUrl = 'temp/property/delete/sundry-charge';
                        }
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.sundry_charge_list.splice(index, 1);
                            this.loading_setting = false;
                        });
                    }
                }
            }
        },
        async loadLedgerList() {
            this.property_ledger_list = [];
            try {
                const ledgerList = await fetchPropertyLedger(this.property_code);
                // Normalize possible response shapes, then map to [{ field_key, field_value }]
                const list = Array.isArray(ledgerList)
                    ? ledgerList
                    : Array.isArray(ledgerList?.data)
                      ? ledgerList.data
                      : Array.isArray(ledgerList?.list)
                        ? ledgerList.list
                        : [];
                this.property_ledger_list = list.map(({ id, propertyLedgerCode, description }) => ({
                    field_key: id,
                    code: propertyLedgerCode,
                    field_value: description,
                }));
            } catch (err) {}
        },
        nameWithCodeDash({ code, field_value }) {
            if (`${code}` === '') return `${field_value}`;
            return `${code} — ${field_value}`;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.loadExpSundryAccounts();
                this.loadExpAccounts();
                this.loadPropertyDetailsLists();
            }
        },
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_property_form_read_only',
            'sys_ver_control_list',
        ]),
        ...mapGetters(['getDDCountryStates']),
        isMultiplePropertyLedger() {
            return !!this.sys_ver_control_list?.isMultiplePropertyLedger;
        },
    },
    created() {
        bus.$on('loadPropertySundryChargesFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
