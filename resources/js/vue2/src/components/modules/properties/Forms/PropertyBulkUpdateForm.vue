<template>
    <div class="c8-page">
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header :title="page_title" />
            </v-toolbar-title>
            <div class="flex-grow-1"></div>
        </v-toolbar>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Options:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        v-model="search_type"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                            title="Property"
                        >
                            Property
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            title="Lease"
                        >
                            Lease
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            title="Companies"
                        >
                            Companies
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Value Type:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="type_code"
                        :options="type_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a type"
                        track-by="value"
                        label="value"
                        :show-labels="false"
                    >
                    </multiselect>
                    <v-btn
                        :loading="type_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        :disabled="disable_value_type"
                        v-on:click="changeSearchType('type_code')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!---------------Current Values----------------->
            <!--Property Manager-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current {{ property_manager_label }}:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="manager_code"
                        :options="manager_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        group-label="language"
                        :placeholder="'Select a ' + { property_manager_label }"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <v-btn
                        :loading="manager_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('manager_code')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Property Group-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Property Group:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="old_property_group"
                        :options="property_group_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a property group"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <v-btn
                        :loading="property_group_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('group_code')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Payment Run List-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Payment Run Group:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="old_payment_run"
                        :options="payment_run_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a payment run group"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <v-btn
                        :loading="payment_run_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('payment_run')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Owner Report-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 3"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Owner Report Type:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="old_owner_report"
                        :options="owner_report_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select an owner report type"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <v-btn
                        :loading="owner_report_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('owner_report')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Property Agent-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 4"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Property Agent:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="old_property_agent"
                        :options="property_agent_list"
                        trackBy="field_key"
                        label="field_key_w_value"
                        return="field_key"
                        placeholder="Please select"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                    />
                    <v-btn
                        :loading="property_agent_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('property_agent')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Remittance Office-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 5"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Remitance Office:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="old_remittance_office"
                        :options="remittance_office_list"
                        trackBy="field_key"
                        label="field_key_w_value"
                        return="field_key"
                        placeholder="Please select"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                    />
                    <v-btn
                        :loading="remittance_office_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('owner_report')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Current Property Inspection Fee-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 6"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Inspection Fee:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="old_inspection_fee"
                    >
                        <v-btn
                            small
                            tile
                            text
                            :key="1"
                            v-bind:id="1"
                            v-text="'Yes'"
                            value="1"
                            class="no-text-transform"
                        ></v-btn>
                        <v-btn
                            small
                            tile
                            text
                            :key="0"
                            v-bind:id="0"
                            v-text="'No'"
                            value="0"
                            class="no-text-transform"
                        ></v-btn>
                    </v-btn-toggle>
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('inspection_fee')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>
            <!--Current Property Inspection Fee Amount-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 6.1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Inspection Fee Amount:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :inputFormat="'dollar'"
                        custom_class="cirrus-input-table-textbox"
                        v-model="old_inspection_fee_amount"
                        size=""
                        :id="'old_inspection_fee_amount'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('inspection_fee_amount')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Current Property Inspection Fee Account-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 6.2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Inspection Fee Account:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-group
                        class="property-code-dropdown"
                        v-model="old_inspection_fee_account"
                        :options="inspection_payment_account_list"
                        group-values="fieldGroupValues"
                        group-label="fieldGroupNames"
                        track-by="field_key"
                        label="field_value"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                        :displayDelay="true"
                    />
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('inspection_fee_account')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>
            <!--Current Property Inspection Fee Account-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 6.3"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Inspection Fee Frequency:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="form-toggle"
                        v-show="true"
                        v-model="old_inspection_fee_frequency"
                        mandatory
                    >
                        <v-btn
                            small
                            text
                        >
                            Monthly
                        </v-btn>
                        <v-btn
                            small
                            text
                        >
                            Quarterly
                        </v-btn>
                        <v-btn
                            small
                            text
                        >
                            Semi-Annual
                        </v-btn>
                        <v-btn
                            small
                            text
                        >
                            Annual
                        </v-btn>
                    </v-btn-toggle>
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('inspection_fee_frequency')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Current Property Rent Review Fee-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 7"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Rent Review Fee:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="old_rent_review_fee"
                    >
                        <v-btn
                            small
                            tile
                            text
                            :key="1"
                            v-bind:id="1"
                            v-text="'Yes'"
                            value="1"
                            class="no-text-transform"
                        ></v-btn>
                        <v-btn
                            small
                            tile
                            text
                            :key="0"
                            v-bind:id="0"
                            v-text="'No'"
                            value="0"
                            class="no-text-transform"
                        ></v-btn>
                    </v-btn-toggle>
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('rent_review_fee')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>
            <!--Current Property Rent Review Fee Amount-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 7.1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Rent Review Fee Amount:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :inputFormat="'dollar'"
                        custom_class="cirrus-input-table-textbox"
                        v-model="old_rent_review_fee_amount"
                        size=""
                        :id="'old_rent_review_fee_amount'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('rent_review_fee_amount')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>
            <!--Current Property Rent Review Fee Description-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 7.2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Rent Review Description:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="old_rent_review_fee_desc"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                    <v-btn
                        :loading="false"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('rent_review_fee_description')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Current Property Rent Review Fee Account-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 7.3"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Rent Review Account:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-group
                        class="property-code-dropdown"
                        v-model="old_rent_review_fee_account"
                        :options="inspection_payment_account_list"
                        group-values="fieldGroupValues"
                        group-label="fieldGroupNames"
                        track-by="field_key"
                        label="field_value"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                        :displayDelay="true"
                    />
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Property"
                        v-on:click="changeSearchType('rent_review_fee_account')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Lease Tenant Type-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Tenant Type:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="old_tenant_type"
                        :options="tenant_type_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a tenant type"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <v-btn
                        :loading="tenant_type_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Leases"
                        v-on:click="changeSearchType('tenant_type')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Division-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Division:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="old_division"
                        :options="division_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select division"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <v-btn
                        :loading="division_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Leases"
                        v-on:click="changeSearchType('division')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Fixed Interest Rate-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Fixed Interest Rate:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :inputFormat="'dollar'"
                        custom_class="cirrus-input-table-textbox"
                        v-model="old_fixed_interest_rate"
                        size=""
                        :id="'old_fixed_interest_rate'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                    %
                    <v-btn
                        :loading="false"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load leases"
                        v-on:click="changeSearchType('fixed_interest')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Description-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 3"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Description:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        custom_class="cirrus-input-table-textbox"
                        v-model="old_description"
                        size=""
                        :id="'old_description'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                    <v-btn
                        :loading="false"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load leases"
                        v-on:click="changeSearchType('interest_description')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Grace Period-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 4"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Grace Period:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :inputFormat="'dollar'"
                        custom_class="cirrus-input-table-textbox"
                        v-model="old_grace_period"
                        size=""
                        :id="'old_grace_period'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                    <v-btn
                        :loading="false"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load leases"
                        v-on:click="changeSearchType('interest_grace_period')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Current Direct Management Fee-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 5"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Direct Management Fee:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="old_direct_management_fee"
                    >
                        <v-btn
                            small
                            tile
                            text
                            :key="1"
                            v-bind:id="1"
                            v-text="'Yes'"
                            value="1"
                            class="no-text-transform"
                        ></v-btn>
                        <v-btn
                            small
                            tile
                            text
                            :key="0"
                            v-bind:id="0"
                            v-text="'No'"
                            value="0"
                            class="no-text-transform"
                        ></v-btn>
                    </v-btn-toggle>
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Leases"
                        v-on:click="changeSearchType('direct_management_fee')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Current Direct Management Fee Account-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 6"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Management Fee Account:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-group
                        v-model="old_direct_management_fee_account"
                        :options="dd_account_income_grouped_list"
                        group-values="field_group_values"
                        group-label="fieldGroupNames"
                        track-by="field_key"
                        label="field_value"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                    />
                    <v-btn
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Leases"
                        v-on:click="changeSearchType('direct_management_fee_account')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!-- Current Direct Management Fee Account Description-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 7"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Account Description:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="old_direct_management_fee_account_desc"
                        data-inverted=""
                        :id="'old_direct_management_fee_account_desc'"
                        :edit_form="true"
                        title="Current Direct Management Fee Account Description"
                        :error_msg="error_msg"
                    ></cirrus-input>
                    <v-btn
                        :loading="false"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        :disabled="disable_value_type"
                        title="Load Leases"
                        v-on:click="changeSearchType('direct_management_fee_account_desc')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!-- Current Direct Management Fee Percentage-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 8"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Management Fee %:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="old_direct_management_fee_percentage"
                        data-inverted=""
                        :id="'old_direct_management_fee_percentage'"
                        :inputFormat="'dollar'"
                        :edit_form="true"
                        title="Current Direct Management Fee Percentage"
                        :error_msg="error_msg"
                    ></cirrus-input>
                    <v-btn
                        :loading="false"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        :disabled="disable_value_type"
                        title="Load Leases"
                        v-on:click="changeSearchType('direct_management_fee_percentage')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Company Group-->
            <v-row
                class="form-row"
                v-if="search_type === 2 && type_code.key === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Company Group:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="old_company_group"
                        :options="company_group_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select company group"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <v-btn
                        :loading="company_group_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Companies"
                        v-on:click="changeSearchType('company_group')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Company Supplier Type-->
            <v-row
                class="form-row"
                v-if="search_type === 2 && type_code.key === 1 && form_mode === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Company Supplier Type:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="old_company_supplier_type"
                        :options="company_supplier_type_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select supplier type"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <v-btn
                        :loading="company_supplier_type_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Companies"
                        v-on:click="changeSearchType('company_supplier_type')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!--Company Send Remittance-->
            <v-row
                class="form-row"
                v-if="search_type === 2 && type_code.key === 2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Current Send Remittance:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="old_send_remittance"
                    >
                        <v-btn
                            small
                            tile
                            text
                            :key="1"
                            v-bind:id="1"
                            v-text="'Yes'"
                            value="1"
                            class="no-text-transform"
                        ></v-btn>
                        <v-btn
                            small
                            tile
                            text
                            :key="0"
                            v-bind:id="0"
                            v-text="'No'"
                            value="0"
                            class="no-text-transform"
                        ></v-btn>
                    </v-btn-toggle>
                    <v-btn
                        :loading="company_list.length <= 0"
                        depressed
                        elevation="0"
                        tile
                        small
                        color="normal"
                        height="30"
                        title="Load Companies"
                        v-on:click="changeSearchType('company_send_remittance')"
                    >
                        <v-icon>arrow_right</v-icon>
                    </v-btn>
                </v-col>
            </v-row>

            <!------------Lists of Properties, Leases and Companies--------------->
            <v-row
                class="form-row"
                v-if="search_type === 1 || search_type === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                ></v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-checkbox
                        v-on:click="changeSearchType('include_inactive_properties')"
                        v-model="include_inactive_properties"
                        :label="'Include Inactive Properties'"
                        ripple="false"
                        dense
                    ></v-checkbox>
                </v-col>
            </v-row>

            <v-row
                class="form-row"
                v-if="
                    manager_code.field_key !== '' ||
                    old_property_group.value !== '' ||
                    old_payment_run.value !== '' ||
                    old_owner_report.value !== '' ||
                    old_property_agent ||
                    old_remittance_office ||
                    old_inspection_fee ||
                    old_inspection_fee_amount ||
                    old_inspection_fee_account ||
                    old_inspection_fee_frequency != null ||
                    old_rent_review_fee ||
                    old_rent_review_fee_amount ||
                    old_rent_review_fee_account ||
                    old_rent_review_fee_desc
                "
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Property:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="properties"
                        v-bind:withKey="false"
                        v-bind:options="list_select"
                        v-if="list_select.length > 0"
                    />
                </v-col>
            </v-row>

            <v-row
                class="form-row"
                v-if="
                    old_tenant_type.value !== '' ||
                    old_division.value !== '' ||
                    old_fixed_interest_rate !== null ||
                    old_grace_period !== null ||
                    old_description !== null ||
                    old_direct_management_fee !== null ||
                    old_direct_management_fee_account !== null ||
                    old_direct_management_fee_account_desc !== null ||
                    old_direct_management_fee_percentage !== null
                "
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Lease:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="leases"
                        v-bind:withKey="false"
                        v-bind:options="lease_list"
                        v-if="lease_list.length > 0"
                    />
                </v-col>
            </v-row>

            <v-row
                class="form-row"
                v-if="
                    search_type === 2 &&
                    (old_company_group.value !== '' ||
                        old_company_supplier_type.value !== '' ||
                        old_send_remittance !== null)
                "
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Company:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="companies"
                        v-bind:withKey="false"
                        v-bind:options="list_select"
                        v-if="list_select.length > 0"
                    />
                </v-col>
            </v-row>

            <!-----------New Values---------->
            <!--Property Manager-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 0 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New {{ property_manager_label }}:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="manager_code_new"
                        :options="manager_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        group-label="language"
                        :placeholder="'Select a ' + { property_manager_label }"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>

            <!--Property Group-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 1 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Property Group:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="new_property_group"
                        :options="property_group_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a property group"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>

            <!--Payment Run List-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 2 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Payment Run Group:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="new_payment_run"
                        :options="payment_run_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a payment run group"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>

            <!--Owner Report-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 3 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Owner Report Type:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="new_owner_report"
                        :options="owner_report_list"
                        :allowEmpty="false"
                        old_owner_report
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select an owner report type"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>

            <!--Property Agent-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 4 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Property Agent:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="new_property_agent"
                        :options="property_agent_list"
                        trackBy="field_key"
                        label="field_key_w_value"
                        return="field_key"
                        placeholder="Please select"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                    />
                </v-col>
            </v-row>

            <!--Remittance Office-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 5 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Remitance Office:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="new_remittance_office"
                        :options="remittance_office_list"
                        trackBy="field_key"
                        label="field_key_w_value"
                        return="field_key"
                        placeholder="Please select"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                    />
                </v-col>
            </v-row>

            <!-----New Inspection Fee------>
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 6 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Inspection Fee:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="new_inspection_fee"
                    >
                        <v-btn
                            small
                            tile
                            text
                            :key="1"
                            v-bind:id="1"
                            v-text="'Yes'"
                            value="1"
                            class="no-text-transform"
                        ></v-btn>
                        <v-btn
                            small
                            tile
                            text
                            :key="0"
                            v-bind:id="0"
                            v-text="'No'"
                            value="0"
                            class="no-text-transform"
                        ></v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <template
                v-if="
                    search_type === 0 && type_code.key === 6 && new_inspection_fee === '1' && old_inspection_fee === '0'
                "
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Inspection Amount:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            :inputFormat="'dollar'"
                            v-model="inspection_amount"
                            data-inverted=""
                            :edit_form="true"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Payment Account:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-single-select-group
                            class="property-code-dropdown"
                            v-model="property_inspection_payment_account"
                            :options="inspection_payment_account_list"
                            group-values="fieldGroupValues"
                            group-label="fieldGroupNames"
                            track-by="field_key"
                            label="field_value"
                            :displayDelay="true"
                        />
                    </v-col>
                </v-row>

                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Frequency:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            class="form-toggle"
                            v-show="true"
                            v-model="inspection_frequency"
                            mandatory
                        >
                            <v-btn
                                small
                                text
                            >
                                Monthly
                            </v-btn>
                            <v-btn
                                small
                                text
                            >
                                Quarterly
                            </v-btn>
                            <v-btn
                                small
                                text
                            >
                                Semi-Annual
                            </v-btn>
                            <v-btn
                                small
                                text
                            >
                                Annual
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
            </template>

            <!--New Property Inspection Fee Amount-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 6.1 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Inspection Fee Amount:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :inputFormat="'dollar'"
                        custom_class="cirrus-input-table-textbox"
                        v-model="new_inspection_fee_amount"
                        size=""
                        :id="'new_inspection_fee_amount'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                </v-col>
            </v-row>

            <!--New Property Inspection Fee Account-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 6.2 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Inspection Fee Account:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-group
                        class="property-code-dropdown"
                        v-model="new_inspection_fee_account"
                        :options="inspection_payment_account_list"
                        group-values="fieldGroupValues"
                        group-label="fieldGroupNames"
                        track-by="field_key"
                        label="field_value"
                        :displayDelay="true"
                    />
                </v-col>
            </v-row>

            <!--New Property Inspection Fee Frequency-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 6.3 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Inspection Fee Frequency:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="form-toggle"
                        v-show="true"
                        v-model="new_inspection_fee_frequency"
                        mandatory
                    >
                        <v-btn
                            small
                            text
                        >
                            Monthly
                        </v-btn>
                        <v-btn
                            small
                            text
                        >
                            Quarterly
                        </v-btn>
                        <v-btn
                            small
                            text
                        >
                            Semi-Annual
                        </v-btn>
                        <v-btn
                            small
                            text
                        >
                            Annual
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <!-----New Rent Review Fee------>
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 7 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Rent Review Fee:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="new_rent_review_fee"
                    >
                        <v-btn
                            small
                            tile
                            text
                            :key="1"
                            v-bind:id="1"
                            v-text="'Yes'"
                            value="1"
                            class="no-text-transform"
                        ></v-btn>
                        <v-btn
                            small
                            tile
                            text
                            :key="0"
                            v-bind:id="0"
                            v-text="'No'"
                            value="0"
                            class="no-text-transform"
                        ></v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <!--New Property Rent Review Fee Amount-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 7.1 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Rent Review Fee Amount:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :inputFormat="'dollar'"
                        custom_class="cirrus-input-table-textbox"
                        v-model="new_rent_review_fee_amount"
                        size=""
                        :id="'new_rent_review_fee_amount'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                </v-col>
            </v-row>
            <!--New Property Rent Review Fee Description-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 7.2 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Rent Review Description:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="new_rent_review_fee_desc"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                </v-col>
            </v-row>

            <!--New Property Rent Review Fee Account-->
            <v-row
                class="form-row"
                v-if="search_type === 0 && type_code.key === 7.3 && property_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Rent Review Account:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-group
                        class="property-code-dropdown"
                        v-model="new_rent_review_fee_account"
                        :options="inspection_payment_account_list"
                        group-values="fieldGroupValues"
                        group-label="fieldGroupNames"
                        track-by="field_key"
                        label="field_value"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                        :displayDelay="true"
                    />
                </v-col>
            </v-row>

            <template
                v-if="
                    search_type === 0 &&
                    type_code.key === 7 &&
                    new_rent_review_fee === '1' &&
                    old_rent_review_fee === '0'
                "
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Rent Review Amount:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            :inputFormat="'dollar'"
                            v-model="rent_review_amount"
                            data-inverted=""
                            :edit_form="true"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Description:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            v-model="rent_review_description"
                            data-inverted=""
                            :edit_form="true"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Expense Account:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-single-select-group
                            class="property-code-dropdown"
                            v-model="property_rent_review_expenses_account"
                            :options="inspection_payment_account_list"
                            group-values="fieldGroupValues"
                            group-label="fieldGroupNames"
                            track-by="field_key"
                            label="field_value"
                            :displayDelay="true"
                        />
                    </v-col>
                </v-row>
            </template>

            <!--Lease Tenant Type-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 0 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Tenant Type:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="new_tenant_type"
                        :options="tenant_type_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a tenant type"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>

            <!--Division-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 1 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Division:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="new_division"
                        :options="division_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select division"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>

            <!--Fixed Interest Rate-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 2 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Fixed Interest Rate:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :inputFormat="'dollar'"
                        custom_class="cirrus-input-table-textbox"
                        v-model="new_fixed_interest_rate"
                        size=""
                        :id="'new_fixed_interest_rate'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                    %
                </v-col>
            </v-row>

            <!--Description-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 3 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Description:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        custom_class="cirrus-input-table-textbox"
                        v-model="new_description"
                        size=""
                        :id="'new_description'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                </v-col>
            </v-row>

            <!--Grace Period-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 4 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Grace Period:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :inputFormat="'dollar'"
                        custom_class="cirrus-input-table-textbox"
                        v-model="new_grace_period"
                        size=""
                        :id="'new_grace_period'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-input>
                </v-col>
            </v-row>
            <!-----New Direct Management Fee------>
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 5 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Direct Management Fee:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="new_direct_management_fee"
                    >
                        <v-btn
                            small
                            tile
                            text
                            :key="1"
                            v-bind:id="1"
                            v-text="'Yes'"
                            value="1"
                            class="no-text-transform"
                        ></v-btn>
                        <v-btn
                            small
                            tile
                            text
                            :key="0"
                            v-bind:id="0"
                            v-text="'No'"
                            value="0"
                            class="no-text-transform"
                        ></v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <template
                v-if="
                    search_type === 1 &&
                    type_code.key === 5 &&
                    new_direct_management_fee === '1' &&
                    old_direct_management_fee === '0'
                "
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Account:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            v-on:input="lease_account_desc = lease_accounts.fieldValue"
                            data-inverted=""
                            v-model="lease_accounts"
                            :options="dd_account_income_grouped_list"
                            group-values="field_group_values"
                            :groupSelect="false"
                            group-label="field_group_names"
                            :group-select="true"
                            class="vue-select2 dropdown-left dropdown-400"
                            placeholder="Please select ..."
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                    </v-col>
                </v-row>

                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Account Description:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            v-model="lease_account_desc"
                            data-inverted=""
                            :edit_form="true"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        ><strong>Management Percentage:</strong></v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            :inputFormat="'dollar'"
                            v-model="lease_man_percentage"
                            data-inverted=""
                            :edit_form="true"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </v-col>
                </v-row>
            </template>

            <!--New Direct Management Fee Account-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 6 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Management Fee Account:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-group
                        v-model="new_direct_management_fee_account"
                        :options="dd_account_income_grouped_list"
                        group-values="field_group_values"
                        group-label="field_value"
                        track-by="field_key"
                        label="field_value"
                        :customClass="'multiselect vue-select2 dropdown-left dropdown-400'"
                    />
                </v-col>
            </v-row>

            <!-- New Direct Management Fee Account Description-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 7 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Account Description:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="new_direct_management_fee_account_desc"
                        data-inverted=""
                        :id="'new_direct_management_fee_account_desc'"
                        :edit_form="true"
                        title="New Direct Management Fee Account Description"
                        :error_msg="error_msg"
                    ></cirrus-input>
                </v-col>
            </v-row>

            <!-- New Direct Management Fee Percentage-->
            <v-row
                class="form-row"
                v-if="search_type === 1 && type_code.key === 8 && lease_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Management Fee %:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="new_direct_management_fee_percentage"
                        data-inverted=""
                        :id="'new_direct_management_fee_percentage'"
                        :inputFormat="'dollar'"
                        :edit_form="true"
                        title="New Direct Management Fee Percentage"
                        :error_msg="error_msg"
                    ></cirrus-input>
                </v-col>
            </v-row>

            <!--Company Group-->
            <v-row
                class="form-row"
                v-if="search_type === 2 && type_code.key === 0 && company_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Company Group:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="new_company_group"
                        :options="company_group_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select new company group"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>

            <!--Company Supplier Type-->
            <v-row
                class="form-row"
                v-if="search_type === 2 && type_code.key === 1 && company_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Company Supplier Type:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="new_company_supplier_type"
                        :options="company_supplier_type_list"
                        :allowEmpty="false"
                        :options-limit="10000"
                        class="vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select new supplier type"
                        track-by="value"
                        :custom-label="nameWithoutDash"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>

            <v-row
                class="form-row"
                v-if="search_type === 2 && type_code.key === 2 && company_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >New Send Remittance:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type form-toggle"
                        mandatory
                        v-model="new_send_remittance"
                    >
                        <v-btn
                            small
                            tile
                            text
                            :key="1"
                            v-bind:id="1"
                            v-text="'Yes'"
                            value="1"
                            class="no-text-transform"
                        ></v-btn>
                        <v-btn
                            small
                            tile
                            text
                            :key="0"
                            v-bind:id="0"
                            v-text="'No'"
                            value="0"
                            class="no-text-transform"
                        ></v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                ></v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn
                        v-if="search_type === 0"
                        class="form-text-button"
                        tile
                        color="primary"
                        depressed
                        elevation="0"
                        small
                        @click="updateProperty()"
                    >
                        Update Property
                    </v-btn>
                    <v-btn
                        v-if="search_type === 1"
                        class="form-text-button"
                        tile
                        color="primary"
                        depressed
                        elevation="0"
                        small
                        @click="updateLease()"
                    >
                        Update Lease
                    </v-btn>
                    <v-btn
                        v-if="search_type === 2"
                        class="form-text-button"
                        tile
                        color="primary"
                        depressed
                        elevation="0"
                        small
                        @click="updateCompany()"
                    >
                        Update Company
                    </v-btn>
                </v-col>
            </v-row>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';

const TYPE_LIST_DEFAULT = [
    { key: 0, value: 'Property Manager' },
    { key: 1, value: 'Property Group' },
    { key: 2, value: 'Payment Run Group' },
    { key: 3, value: 'Owner Report Type' },
    { key: 4, value: 'Property Agent' },
    { key: 5, value: 'Remittance Office' },
    { key: 6, value: 'Inspection Fee Option' },
    { key: 6.1, value: 'Inspection Fee Amount' },
    { key: 6.2, value: 'Inspection Fee Payment Account' },
    { key: 6.3, value: 'Inspection Fee Frequency' },
    { key: 7, value: 'Rent Review Fee Option' },
    { key: 7.1, value: 'Rent Review Fee Amount' },
    { key: 7.2, value: 'Rent Review Fee Description' },
    { key: 7.3, value: 'Rent Review Fee Expense Account' },
];
const TYPE_LIST_LEASE = [
    { key: 0, value: 'Tenant Type' },
    { key: 1, value: 'Division' },
    { key: 2, value: 'Fixed Interest Rate' },
    { key: 3, value: 'Interest Description' },
    { key: 4, value: 'Grace Period' },
    { key: 5, value: ' Direct Management Fee Option' },
    { key: 6, value: ' Direct Management Fee Account' },
    { key: 7, value: ' Direct Management Fee Account Description' },
    { key: 8, value: ' Direct Management Fee Percentage' },
];
const TYPE_LIST_COMPANY = [
    { key: 0, value: 'Company Group' },
    { key: 1, value: 'Supplier Type' },
    { key: 2, value: 'Send Remittance' },
];

export default {
    data() {
        return {
            property_manager_label: 'Property Manager',
            disable_value_type: false,
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            manager_code: { field_key: '' },
            manager_code_new: { field_key: '' },
            manager_list: [],
            property_code: [],
            properties: [],
            property_list: [],
            type_code: { key: 0, value: this.property_manager_label },
            type_list: TYPE_LIST_DEFAULT,
            old_property_group: { value: '' },
            new_property_group: { value: '' },
            property_group_list: [],
            old_payment_run: { value: '' },
            new_payment_run: { value: '' },
            payment_run_list: [],
            old_owner_report: { value: '' },
            new_owner_report: { value: '' },
            owner_report_list: [],
            old_property_agent: null, //{value:''},
            new_property_agent: null, //{value:''},
            property_agent_list: [],
            old_remittance_office: null,
            new_remittance_office: null,
            remittance_office_list: [],
            old_inspection_fee: null,
            new_inspection_fee: null,
            inspection_payment_account_list: [],
            property_inspection_payment_account: '',
            inspection_amount: '',
            inspection_frequency: 0,
            old_inspection_fee_amount: null,
            new_inspection_fee_amount: null,
            old_inspection_fee_account: null,
            new_inspection_fee_account: null,
            old_inspection_fee_frequency: null,
            new_inspection_fee_frequency: null,
            old_rent_review_fee: null,
            new_rent_review_fee: null,
            old_rent_review_fee_amount: null,
            new_rent_review_fee_amount: null,
            old_rent_review_fee_desc: null,
            new_rent_review_fee_desc: null,
            old_rent_review_fee_account: null,
            new_rent_review_fee_account: null,
            rent_review_amount: '',
            rent_review_description: '',
            property_rent_review_expenses_account: '',
            old_tenant_type: { value: '' },
            new_tenant_type: { value: '' },
            tenant_type_list: [],
            old_division: { value: '' },
            new_division: { value: '' },
            division_list: [],
            old_company_group: { value: '' },
            new_company_group: { value: '' },
            company_group_list: [],
            old_company_supplier_type: { value: '' },
            new_company_supplier_type: { value: '' },
            company_supplier_type_list: [],
            old_send_remittance: null,
            new_send_remittance: null,
            old_direct_management_fee: null,
            new_direct_management_fee: null,
            old_direct_management_fee_account: null,
            new_direct_management_fee_account: null,
            old_direct_management_fee_account_desc: null,
            new_direct_management_fee_account_desc: null,
            old_direct_management_fee_percentage: null,
            new_direct_management_fee_percentage: null,
            lease_accounts: [],
            leases: [],
            lease_list: [],
            companies: [],
            company_list: [],
            old_fixed_interest_rate: null,
            new_fixed_interest_rate: null,
            old_grace_period: null,
            new_grace_period: null,
            old_description: null,
            new_description: null,
            lease_account_desc: '',
            lease_man_percentage: '',
            responsive_show: true,
            loading_content_setting: false,
            loading_page_setting: false,
            page_title: 'Quick Update Tools',
            include_inactive_properties: false,
            dd_account_income_grouped_list: [],
            error_server_msg: {},
            error_server_msg2: [],
            frequency: ['M', 'Q', 'S', 'A'],
        };
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'lease_details']),
        list_select() {
            let lists = this.property_list;
            if (this.search_type === 1) {
                lists = this.lease_list;
            } else if (this.search_type === 2) {
                lists = this.company_list;
            }
            let data = [];
            if (lists.length > 0) {
                let groups = {};
                for (var i = 0; i < lists.length; i++) {
                    let row = lists[i];
                    if (typeof groups[row.field_group] === 'undefined') {
                        groups[row.field_group] = {
                            fieldGroupNames: row.field_group,
                            fieldGroupValues: [row],
                        };
                    } else {
                        groups[row.field_group].fieldGroupValues.push(row);
                    }
                }
                for (var grp in groups) {
                    data.push(groups[grp]);
                }
            }
            return data;
        },
    },
    mounted() {
        this.loadManagerList();
        this.loadCountryDefaults();
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.property_manager_label = this.ucwords(this.country_defaults.property_manager);
                this.type_list = [
                    { key: 0, value: this.property_manager_label },
                    { key: 1, value: 'Property Group' },
                    { key: 2, value: 'Payment Run Group' },
                    { key: 3, value: 'Owner Report Type' },
                    { key: 4, value: 'Property Agent' },
                    { key: 5, value: 'Remittance Office' },
                    { key: 6, value: 'Inspection Fee Option' },
                    { key: 6.1, value: 'Inspection Fee Amount' },
                    { key: 6.2, value: 'Inspection Fee Payment Account' },
                    { key: 6.3, value: 'Inspection Fee Frequency' },
                    { key: 7, value: 'Rent Review Fee Option' },
                    { key: 7.1, value: 'Rent Review Fee Amount' },
                    { key: 7.2, value: 'Rent Review Fee Description' },
                    { key: 7.3, value: 'Rent Review Fee Expense Account' },
                ];
            });
        },
        loadManagerList: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            this.$api.post(this.cirrus8_api_url + 'api/loadPortfolioManagersList', form_data).then((response) => {
                const defaultValue = {
                    fieldGroup: '',
                    fieldKey: '',
                    fieldValue: '',
                    fieldValueComplete: 'Please select..',
                    field_value: '',
                    field_key: '',
                };
                this.manager_list.push(defaultValue);
                response.data.pm_data.map((row) => {
                    this.manager_list.push(row);
                });
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadPropertyGroupList: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            this.$api.post(this.cirrus8_api_url + 'api/dropDowns/propertyGroups', form_data).then((response) => {
                const defaultValue = {
                    fieldGroup: '',
                    fieldKey: '',
                    fieldValue: '',
                    fieldValueComplete: 'Please select..',
                    value: '',
                };
                this.property_group_list.push(defaultValue);
                response.data.map((row) => {
                    this.property_group_list.push(row);
                });
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadPaymentRunList: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            form_data.append('paramType', 'PAYGROUP');
            this.$api.post(this.cirrus8_api_url + 'api/dropDowns/paramTypes', form_data).then((response) => {
                const defaultValue = {
                    fieldGroup: '',
                    fieldKey: '',
                    fieldValue: '',
                    fieldValueComplete: 'Please select..',
                    value: '',
                };
                this.payment_run_list.push(defaultValue);
                response.data.map((row) => {
                    this.payment_run_list.push(row);
                });
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadOwnerReportList: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            this.$api.post(this.cirrus8_api_url + 'api/dropDowns/ownerReportList', form_data).then((response) => {
                const defaultValue = {
                    fieldGroup: '',
                    fieldKey: '',
                    fieldValue: '',
                    fieldValueComplete: 'Please select..',
                    value: '',
                };
                this.owner_report_list.push(defaultValue);
                response.data.map((row) => {
                    this.owner_report_list.push(row);
                });
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadPropertyAgentList: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            form_data.append('user_type', this.user_type);
            this.$api.post(this.cirrus8_api_url + 'api/agent-dropdown-list', form_data).then((response) => {
                this.property_agent_list = Object.keys(response.data).reduce(
                    (acc, key) => {
                        if (key !== 'not_encoded') {
                            acc[key] = response.data[key];
                        }
                        return acc;
                    },
                    [{}],
                );
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadPropertyRemittanceOffice: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            form_data.append('user_type', this.user_type);
            this.$api.post(this.cirrus8_api_url + 'api/office-dropdown-list', form_data).then((response) => {
                this.remittance_office_list = Object.keys(response.data).reduce(
                    (acc, key) => {
                        if (key !== 'not_encoded') {
                            acc[key] = response.data[key];
                        }
                        return acc;
                    },
                    [{}],
                );
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadInspectionPaymentAccounts: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            this.$api.post('ui/fetch/account-exp-lists').then((response) => {
                this.inspection_payment_account_list = response.data.grouped.map((row) => {
                    const values = row.field_group_values.map((row) => {
                        return {
                            field_key: row.field_key,
                            field_value: row.field_value,
                            label: row.field_key_w_value,
                            value: row.field_key,
                        };
                    });
                    return {
                        fieldGroupNames: row.field_group_names.toLowerCase().replace(/\b\w/g, (s) => s.toUpperCase()),
                        fieldGroupValues: values,
                    };
                });
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadTenantTypeList: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            form_data.append('paramType', 'TENANT');
            this.$api.post(this.cirrus8_api_url + 'api/dropDowns/paramTypes', form_data).then((response) => {
                const defaultValue = {
                    fieldGroup: '',
                    fieldKey: '',
                    fieldValue: '',
                    fieldValueComplete: 'Please select..',
                    value: '',
                };
                this.tenant_type_list.push(defaultValue);
                response.data.map((row) => {
                    this.tenant_type_list.push(row);
                });
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadDivisionList: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            form_data.append('paramType', 'DIVISION');
            this.$api.post(this.cirrus8_api_url + 'api/dropDowns/paramTypes', form_data).then((response) => {
                const defaultValue = {
                    fieldGroup: '',
                    fieldKey: '',
                    fieldValue: '',
                    fieldValueComplete: 'Please select..',
                    value: '',
                };
                this.division_list.push(defaultValue);
                response.data.map((row) => {
                    this.division_list.push(row);
                });
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadUnitDetails: function () {
            this.loading_content_setting = true;
            const form_data = new FormData();
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';
            apiUrl = this.cirrus8_api_url + 'api/lease/fetch/management-fees';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.dd_account_income_grouped_list = response.data.accountData.map((row) => {
                    const groupValues = row.fieldGroupValues.map((rw) => {
                        return {
                            fieldKey: rw.fieldKey,
                            fieldValue: rw.label,
                            field_key: rw.field_key,
                            field_value: rw.field_value,
                            label: rw.field_value,
                            value: rw.value,
                        };
                    });
                    return {
                        fieldGroupNames: row.fieldGroupNames,
                        fieldGroupValues: groupValues,
                        field_group_names: row.field_group_names,
                        field_group_values: groupValues,
                    };
                });
                this.loading_content_setting = false;
            });
        },
        loadCompanyGroupList: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            form_data.append('paramType', 'COMPGROUP');
            this.$api.post(this.cirrus8_api_url + 'api/dropDowns/paramTypes', form_data).then((response) => {
                const defaultValue = {
                    fieldGroup: '',
                    fieldKey: '',
                    fieldValue: '',
                    fieldValueComplete: 'Please select..',
                    value: '',
                };
                this.company_group_list.push(defaultValue);
                response.data.map((row) => {
                    this.company_group_list.push(row);
                });
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        loadCompanySupplierType: function () {
            this.loading_content_setting = true;
            this.disable_value_type = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('no_load', true);
            form_data.append('paramType', 'SUPPLIER');
            this.$api.post(this.cirrus8_api_url + 'api/dropDowns/paramTypes', form_data).then((response) => {
                this.company_supplier_type_list = response.data;
                this.loading_content_setting = false;
                this.disable_value_type = false;
            });
        },
        getValueInGroupList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((p) => {
                    let field_group_values = p.field_group_values.findIndex((c) => {
                        return c.field_key === param1;
                    });
                    return field_group_values !== -1;
                });
                if (filtered.length > 0) {
                    let field_group_values = filtered[0].field_group_values.filter((m) => m.field_key === param1);
                    return field_group_values[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        fieldKey: '',
                        fieldValue: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        nameWithDash({ field_key, field_value, lease_property }) {
            switch (this.search_type) {
                case 1:
                    if (!field_value) {
                        return 'Please select...';
                    }
                    if (lease_property) return `${field_key} — ${field_value} (${lease_property})`;
                    return `${field_key} — ${field_value}`;
                    break;

                default:
                    if (!field_value) {
                        return 'Please select...';
                    }
                    return `${field_key} — ${field_value}`;
                    break;
            }
        },
        nameWithoutDash({ label, value, item }) {
            switch (this.search_type) {
                case 1:
                    if (!value) {
                        return 'Please select...';
                    }
                    if (item) return `${label}`;
                    return `${label}`;
                    break;

                default:
                    if (!value) {
                        return 'Please select...';
                    }
                    return `${label}`;
                    break;
            }
        },
        getValueInList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.field_key === param1);
                if (filtered.length > 0) {
                    return filtered[0];
                } else {
                    return { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' };
                }
            } else {
                return { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' };
            }
        },
        loadPropertyList: function () {
            let form_data = new FormData();
            const frequency =
                this.old_inspection_fee_frequency != null ? this.frequency[this.old_inspection_fee_frequency] : null;
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append('manager_code', this.manager_code.field_key);
            form_data.append('property_group_code', this.old_property_group.value);
            form_data.append('payment_run_code', this.old_payment_run.value);
            form_data.append('owner_report', this.old_owner_report.value);
            form_data.append('property_agent', this.old_property_agent);
            form_data.append('remittance_office', this.old_remittance_office);
            form_data.append('type_code', this.type_code.key);
            form_data.append('active_only', !this.include_inactive_properties ? '1' : '0');
            form_data.append('inspection_fee', this.old_inspection_fee);
            form_data.append('inspection_fee_amount', this.old_inspection_fee_amount);
            form_data.append('inspection_fee_account', this.old_inspection_fee_account);
            form_data.append('inspection_fee_frequency', frequency);
            form_data.append('rent_review_fee_amount', this.old_rent_review_fee_amount);
            form_data.append('rent_review_fee_account', this.old_rent_review_fee_account);
            form_data.append('rent_review_fee_description', this.old_rent_review_fee_desc);
            form_data.append('rent_review_fee', this.old_rent_review_fee);
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-dropdown-list', form_data).then((response) => {
                this.property_list = response.data.data.map((row) => {
                    return {
                        fieldGroup: row.field_group,
                        fieldKey: row.id + '::' + row.field_key,
                        fieldValue: row.field_key + ' - ' + row.field_value,
                        field_group: row.field_group,
                        field_key: row.id,
                        field_value: row.field_key + ' - ' + row.field_value,
                    };
                });
                this.loading_content_setting = false;
            });
        },
        loadLeaseList: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append(
                'tenant_type',
                typeof this.old_tenant_type.field_key === 'undefined' ? '' : this.old_tenant_type.field_key,
            );
            form_data.append(
                'division',
                typeof this.old_division.field_key === 'undefined' ? '' : this.old_division.field_key,
            );
            form_data.append('fixed_interest_rate', this.old_fixed_interest_rate);
            form_data.append('interest_grace_period', this.old_grace_period);
            form_data.append('interest_description', this.old_description);
            form_data.append('active_property_only', !this.include_inactive_properties ? '1' : '0');
            form_data.append('direct_management_fee', this.old_direct_management_fee);
            form_data.append('direct_management_fee_account', this.old_direct_management_fee_account);
            form_data.append('direct_management_fee_account_desc', this.old_direct_management_fee_account_desc);
            form_data.append('direct_management_fee_percentage', this.old_direct_management_fee_percentage);
            form_data.append('no_load', true);
            this.$api.post('load-property-lease-list', form_data).then((response) => {
                this.lease_list = response.data.group.map((row) => {
                    const values = row.field_group_values.map((row) => {
                        return {
                            fieldGroup: row.field_group === 'C' ? 'Current' : 'Vacant ',
                            fieldKey: row.id + '::' + row.lease_property + '::' + row.field_key,
                            fieldValue: row.lease_property + ' - ' + row.field_key + ' - ' + row.field_value,
                            field_group: row.field_group === 'C' ? 'Current' : 'Vacant ',
                            field_key: row.field_key, //+"-"+row.id+"-"+row.lease_property,
                            field_value: row.field_value,
                        };
                    });
                    return {
                        fieldGroupNames: row.field_group_names.toLowerCase().replace(/\b\w/g, (s) => s.toUpperCase()),
                        fieldGroupValues: values,
                    };
                });
                this.loading_content_setting = false;
            });
        },
        loadCompanyList: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            let send_remittance = this.old_send_remittance;
            form_data.append('page_source', 'PropertyBulkUpdateForm');
            form_data.append(
                'company_group',
                typeof this.old_company_group.field_key === 'undefined' ? '' : this.old_company_group.field_key,
            );
            form_data.append(
                'company_supplier_type',
                typeof this.old_company_supplier_type.field_key === 'undefined'
                    ? ''
                    : this.old_company_supplier_type.field_key,
            );
            form_data.append(
                'company_send_remittance',
                typeof this.old_send_remittance === 'undefined' ? null : this.old_send_remittance,
            );
            form_data.append('no_load', true);
            this.$api.post('company-dropdown-list', form_data).then((response) => {
                this.company_list = response.data.map((row) => {
                    return {
                        fieldGroup: row.fieldGroup,
                        fieldKey: row.value,
                        fieldValue: row.label,
                        field_group: row.fieldGroup,
                        field_key: row.value,
                        field_value: row.label,
                    };
                });
                this.loading_content_setting = false;
            });
        },
        updateProperty: function () {
            let properties = [];
            this.error_msg = [];
            this.error_server_msg2 = [];
            switch (this.type_code.key) {
                case 0:
                    let manager_code = this.manager_code.field_key;
                    let manager_code_new = this.manager_code_new.field_key;
                    if (manager_code === '') {
                        this.error_msg.push({
                            id: 'manager_code',
                            message: 'Please select current ' + this.property_manager_label + '.',
                        });
                        this.error_server_msg2.push(['Please select current ' + this.property_manager_label + '.']);
                    }
                    if (manager_code_new === '' || typeof manager_code_new === 'undefined') {
                        this.error_msg.push({
                            id: 'manager_code_new',
                            message: 'Please select a new ' + this.property_manager_label + '.',
                        });
                        this.error_server_msg2.push(['Please select a new ' + this.property_manager_label + '.']);
                    }
                    if (manager_code_new === manager_code) {
                        this.error_msg.push({
                            id: 'manager_code_new',
                            message:
                                'Please select a new ' +
                                this.property_manager_label +
                                ' different from current ' +
                                this.property_manager_label +
                                '.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new ' +
                                this.property_manager_label +
                                ' different from current ' +
                                this.property_manager_label +
                                '.',
                        ]);
                    }
                    break;
                case 1:
                    let old_property_group = this.old_property_group.value;
                    let new_property_group = this.new_property_group.value;
                    if (old_property_group === '') {
                        this.error_msg.push({
                            id: 'old_property_group',
                            message: 'Please select current Property Group.',
                        });
                        this.error_server_msg2.push(['Please select current Property Group.']);
                    }
                    if (new_property_group === '' || typeof new_property_group === 'undefined') {
                        this.error_msg.push({
                            id: 'new_property_group',
                            message: 'Please select a new Property Group.',
                        });
                        this.error_server_msg2.push(['Please select a new Property Group.']);
                    }
                    if (new_property_group === old_property_group) {
                        this.error_msg.push({
                            id: 'new_property_group',
                            message: 'Please select a new Property Group different from current Property Group.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Property Group different from current Property Group.',
                        ]);
                    }
                    break;
                case 2:
                    let old_payment_run = this.old_payment_run.value;
                    let new_payment_run = this.new_payment_run.value;
                    if (old_payment_run === '') {
                        this.error_msg.push({
                            id: 'old_payment_run',
                            message: 'Please select current Payment Run Group.',
                        });
                        this.error_server_msg2.push(['Please select current Payment Run Group.']);
                    }
                    if (new_payment_run === '' || typeof new_payment_run === 'undefined') {
                        this.error_msg.push({
                            id: 'new_payment_run',
                            message: 'Please select a new Payment Run Group.',
                        });
                        this.error_server_msg2.push(['Please select a new Payment Run Group.']);
                    }
                    if (new_payment_run === old_payment_run) {
                        this.error_msg.push({
                            id: 'new_payment_run',
                            message: 'Please select a new Payment Run Group different from current Payment Run Group.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Payment Run Group different from current Payment Run Group.',
                        ]);
                    }
                    break;
                case 3:
                    let old_owner_report = this.old_owner_report.value;
                    let new_owner_report = this.new_owner_report.value;
                    if (old_owner_report === '') {
                        this.error_msg.push({ id: 'old_owner_report', message: 'Please select current Owner Report.' });
                        this.error_server_msg2.push(['Please select current Owner Report.']);
                    }
                    if (new_owner_report === '' || typeof new_owner_report === 'undefined') {
                        this.error_msg.push({ id: 'new_owner_report', message: 'Please select a new Owner Report.' });
                        this.error_server_msg2.push(['Please select a new Owner Report.']);
                    }
                    if (new_owner_report === old_owner_report) {
                        this.error_msg.push({
                            id: 'new_owner_report',
                            message: 'Please select a new Owner Report different from current Owner Report.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Owner Report different from current Owner Report.',
                        ]);
                    }
                    break;
                case 4:
                    if (!this.old_property_agent) {
                        this.error_msg.push({
                            id: 'old_property_agent',
                            message: 'Please select current Property Agent.',
                        });
                        this.error_server_msg2.push(['Please select current Property Agent.']);
                    }
                    if (!this.new_property_agent) {
                        this.error_msg.push({
                            id: 'old_property_agent',
                            message: 'Please select a new Property Agent.',
                        });
                        this.error_server_msg2.push(['Please select a new Property Agent.']);
                    }
                    if (
                        this.new_property_agent &&
                        this.old_property_agent &&
                        this.new_property_agent === this.old_property_agent
                    ) {
                        this.error_msg.push({
                            id: 'new_property_agent',
                            message: 'Please select a new Property Agent different from current Property Agent.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Property Agent different from current Property Agent.',
                        ]);
                    }
                    break;
                case 5:
                    if (!this.old_remittance_office) {
                        this.error_msg.push({
                            id: 'old_remittance_office',
                            message: 'Please select current Remittance Office.',
                        });
                        this.error_server_msg2.push(['Please select current Remittance Office.']);
                    }
                    if (!this.new_remittance_office) {
                        this.error_msg.push({
                            id: 'new_remittance_office',
                            message: 'Please select a new Remittance Office.',
                        });
                        this.error_server_msg2.push(['Please select a new Remittance Office.']);
                    }
                    if (
                        this.new_remittance_office &&
                        this.old_remittance_office &&
                        this.new_remittance_office === this.old_remittance_office
                    ) {
                        this.error_msg.push({
                            id: 'new_remittance_office',
                            message: 'Please select a new Remittance Office different from current Remittance Office.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Remittance Office different from current Remittance Office.',
                        ]);
                    }
                    break;
                case 6:
                    const old_inspection_fee = parseInt(this.old_inspection_fee);
                    const new_inspection_fee = parseInt(this.new_inspection_fee);
                    if (old_inspection_fee === null) {
                        this.error_msg.push({
                            id: 'old_inspection_fee',
                            message: 'Please select current Inspection Fee.',
                        });
                        this.error_server_msg2.push(['Please enter current Inspection Fee.']);
                    }
                    if (new_inspection_fee === null || typeof new_inspection_fee === 'undefined') {
                        this.error_msg.push({
                            id: 'new_inspection_fee',
                            message: 'Please select a new Inspection Fee.',
                        });
                        this.error_server_msg2.push(['Please enter a new valid Inspection Fee.']);
                    }
                    if (new_inspection_fee === old_inspection_fee) {
                        this.error_msg.push({
                            id: 'new_inspection_fee',
                            message:
                                'Please select a new Inspection Fee option different from current Inspection Fee option.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Inspection Fee option different from current Inspection Fee option.',
                        ]);
                    }
                    if (new_inspection_fee === 1 && new_inspection_fee !== old_inspection_fee) {
                        if (isNaN(this.inspection_amount) || this.inspection_amount === '') {
                            this.error_msg.push({
                                id: 'inspection_amount',
                                message: 'Inspection amount must be numeric.',
                            });
                            this.error_server_msg2.push(['Inspection amount must be numeric.']);
                        }
                        if (this.property_inspection_payment_account === '') {
                            this.error_msg.push({
                                id: 'inspection_accounts',
                                message: 'Please select a new Inspection Fee Account.',
                            });
                            this.error_server_msg2.push(['Please select a new Inspection Account.']);
                        }
                    }
                    break;
                case 6.1:
                    if (this.old_inspection_fee_amount === null) {
                        this.error_server_msg2.push(['Please enter current Inspection Fee Amount.']);
                    }
                    if (isNaN(this.old_inspection_fee_amount) || this.old_inspection_fee_amount === '') {
                        this.error_server_msg2.push(['Current Inspection Fee Amount must be numeric.']);
                    }
                    if (this.new_inspection_fee_amount === null) {
                        this.error_server_msg2.push(['Please enter a new Inspection Fee Amount.']);
                    }
                    if (parseFloat(this.new_inspection_fee_amount) === parseFloat(this.old_inspection_fee_amount)) {
                        this.error_server_msg2.push([
                            'Please enter a new Inspection Fee Amount different from current Inspection Fee Amount.',
                        ]);
                    }
                    if (isNaN(this.new_inspection_fee_amount) || this.new_inspection_fee_amount === '') {
                        this.error_server_msg2.push(['New Inspection Fee Amount must be numeric.']);
                    }
                    break;
                case 6.2:
                    if (this.old_inspection_fee_account === null) {
                        this.error_server_msg2.push(['Please select current Inspection Fee Account.']);
                    }
                    if (this.new_inspection_fee_account === null) {
                        this.error_server_msg2.push(['Please select a new Inspection Fee Account.']);
                    }
                    if (this.new_inspection_fee_account === this.old_inspection_fee_account) {
                        this.error_server_msg2.push([
                            'Please select a new Inspection Fee Account different from current Inspection Fee Account.',
                        ]);
                    }
                    break;
                case 6.3:
                    if (this.old_inspection_fee_frequency === null) {
                        this.error_server_msg2.push(['Please enter current Inspection Fee Frequency.']);
                    }
                    if (this.new_inspection_fee_frequency === null) {
                        this.error_server_msg2.push(['Please enter a new Inspection Fee Frequency.']);
                    }
                    if (this.new_inspection_fee_frequency === this.old_inspection_fee_frequency) {
                        this.error_server_msg2.push([
                            'Please select a new Inspection Fee Frequency different from current Inspection Fee Frequency.',
                        ]);
                    }
                    break;
                case 7:
                    const old_rent_review_fee = parseInt(this.old_rent_review_fee);
                    const new_rent_review_fee = parseInt(this.new_rent_review_fee);
                    if (old_rent_review_fee === null) {
                        this.error_msg.push({
                            id: 'old_rent_review_fee',
                            message: 'Please select current Rent Review Fee.',
                        });
                        this.error_server_msg2.push(['Please enter current Rent Review Fee.']);
                    }
                    if (new_rent_review_fee === null || typeof new_rent_review_fee === 'undefined') {
                        this.error_msg.push({
                            id: 'new_rent_review_fee',
                            message: 'Please select a new  Rent Review Fee.',
                        });
                        this.error_server_msg2.push(['Please enter a new valid  Rent Review Fee.']);
                    }
                    if (new_rent_review_fee === old_rent_review_fee) {
                        this.error_msg.push({
                            id: 'new_rent_review_fee',
                            message:
                                'Please select a new Rent Review Fee option different from current Rent Review Fee option.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Rent Review Fee option different from current Rent Review Fee option.',
                        ]);
                    }

                    if (new_rent_review_fee === 1 && new_rent_review_fee !== old_rent_review_fee) {
                        if (isNaN(this.rent_review_amount) || this.rent_review_amount === '') {
                            this.error_msg.push({
                                id: 'rent_review_amount',
                                message: 'Rent Review amount must be numeric.',
                            });
                            this.error_server_msg2.push(['Rent Review amount must be numeric.']);
                        }
                        if (this.rent_review_description === null || this.rent_review_description === '') {
                            this.error_msg.push({
                                id: 'rent_review_description',
                                message: 'Please enter a new Rent Review Fee Description.',
                            });
                            this.error_server_msg2.push(['Please enter a new Rent Review Fee Description.']);
                        }
                        if (this.property_rent_review_expenses_account === '') {
                            this.error_msg.push({
                                id: 'rent_review_accounts',
                                message: 'Please select a new Rent Review Fee Account.',
                            });
                            this.error_server_msg2.push(['Please select a new Rent Review Account.']);
                        }
                    }
                    break;
                case 7.1:
                    if (this.old_rent_review_fee_amount === null) {
                        this.error_server_msg2.push(['Please enter current Rent Review Fee Amount.']);
                    }
                    if (isNaN(this.old_rent_review_fee_amount)) {
                        this.error_server_msg2.push(['Current Rent Review Fee Amount must be numeric.']);
                    }
                    if (this.new_rent_review_fee_amount === null) {
                        this.error_server_msg2.push(['Please enter a new Rent Review Fee Amount.']);
                    }
                    if (parseFloat(this.new_rent_review_fee_amount) === parseFloat(this.old_rent_review_fee_amount)) {
                        this.error_server_msg2.push([
                            'Please enter a new Rent Review Fee Amount different from current Rent Review Fee Amount.',
                        ]);
                    }
                    if (isNaN(this.new_rent_review_fee_amount) || this.new_rent_review_fee_amount === '') {
                        this.error_server_msg2.push(['New Rent Review Fee Amount must be numeric.']);
                    }
                    break;
                case 7.2:
                    if (this.old_rent_review_fee_desc === null) {
                        this.error_server_msg2.push(['Please enter current Rent Review Fee Description.']);
                    }
                    if (this.new_rent_review_fee_desc === null) {
                        this.error_server_msg2.push(['Please enter a new Rent Review Fee Description.']);
                    }
                    if (this.new_rent_review_fee_desc === this.old_rent_review_fee_desc) {
                        this.error_server_msg2.push([
                            'Please enter a new Rent Review Fee Description different from current Rent Review Fee Description.',
                        ]);
                    }
                    break;
                case 7.3:
                    if (this.old_rent_review_fee_account === null) {
                        this.error_server_msg2.push(['Please select current Rent Review Fee Account.']);
                    }
                    if (this.new_rent_review_fee_account === null) {
                        this.error_server_msg2.push(['Please select a new Rent Review Fee Account.']);
                    }
                    if (this.new_rent_review_fee_account === this.old_rent_review_fee_account) {
                        this.error_server_msg2.push([
                            'Please select a new Rent Review Fee Account different from current Rent Review Fee Account.',
                        ]);
                    }
                    break;
            }
            if (this.properties.length === 0) {
                this.error_msg.push({ id: 'properties', message: 'Please select a property.' });
                this.error_server_msg2.push(['Please select a property.']);
            } else {
                this.properties.map(function (value, key) {
                    properties.push(value['value']);
                });
            }

            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('page_source', 'PropertyBulkUpdateForm');
                form_data.append('old_manager_code', this.manager_code.field_key);
                form_data.append('new_manager_code', this.manager_code_new.field_key);
                form_data.append('old_property_group_code', this.old_property_group.value);
                form_data.append('new_property_group_code', this.new_property_group.value);
                form_data.append('old_payment_run', this.old_payment_run.value);
                form_data.append('new_payment_run', this.new_payment_run.value);
                form_data.append('old_owner_report', this.old_owner_report.value);
                form_data.append('new_owner_report', this.new_owner_report.value);
                form_data.append('old_property_agent', this.old_property_agent);
                form_data.append('new_property_agent', this.new_property_agent);
                form_data.append('old_remittance_office', this.old_remittance_office);
                form_data.append('new_remittance_office', this.new_remittance_office);
                form_data.append('old_inspection_fee', this.old_inspection_fee);
                form_data.append('new_inspection_fee', this.new_inspection_fee);
                form_data.append('inspection_amount', this.inspection_amount);
                form_data.append('inspection_payment_account', this.property_inspection_payment_account);
                form_data.append('inspection_frequency', this.inspection_frequency);
                form_data.append('new_inspection_fee_amount', this.new_inspection_fee_amount);
                form_data.append('old_inspection_fee_amount', this.old_inspection_fee_amount);
                form_data.append('new_inspection_fee_account', this.new_inspection_fee_account);
                form_data.append('old_inspection_fee_account', this.old_inspection_fee_account);
                form_data.append('new_inspection_fee_frequency', this.frequency[this.new_inspection_fee_frequency]);
                form_data.append('old_inspection_fee_frequency', this.frequency[this.old_inspection_fee_frequency]);
                form_data.append('old_rent_review_fee', this.old_rent_review_fee);
                form_data.append('new_rent_review_fee', this.new_rent_review_fee);
                form_data.append('rent_review_amount', this.rent_review_amount);
                form_data.append('new_rent_review_fee_amount', this.new_rent_review_fee_amount);
                form_data.append('old_rent_review_fee_amount', this.old_rent_review_fee_amount);
                form_data.append('new_rent_review_fee_desc', this.new_rent_review_fee_desc);
                form_data.append('old_rent_review_fee_desc', this.old_rent_review_fee_desc);
                form_data.append('new_rent_review_fee_account', this.new_rent_review_fee_account);
                form_data.append('old_rent_review_fee_account', this.old_rent_review_fee_account);
                form_data.append('property_rent_review_expenses_account', this.property_rent_review_expenses_account);
                form_data.append('rent_review_description', this.rent_review_description);
                form_data.append('type_code', this.type_code.key);
                form_data.append('properties', properties.join());
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/property/update/property-details', form_data)
                    .then((response) => {
                        this.$noty.success(response.data);
                        this.loading_page_setting = false;
                    });
            }
        },
        updateLease: function () {
            let leases = [];
            this.error_msg = [];
            this.error_server_msg2 = [];
            switch (this.type_code.key) {
                case 0:
                    let old_tenant_type = this.old_tenant_type.field_key;
                    let new_tenant_type = this.new_tenant_type.field_key;
                    if (old_tenant_type === '' || typeof old_tenant_type === 'undefined') {
                        this.error_msg.push({ id: 'old_tenant_type', message: 'Please select current Tenant Type.' });
                        this.error_server_msg2.push(['Please select current Tenant Type.']);
                    }
                    if (new_tenant_type === '' || typeof new_tenant_type === 'undefined') {
                        this.error_msg.push({ id: 'new_tenant_type', message: 'Please select a new Tenant Type.' });
                        this.error_server_msg2.push(['Please select a new Tenant Type.']);
                    }
                    if (new_tenant_type === old_tenant_type) {
                        this.error_msg.push({
                            id: 'new_tenant_type',
                            message: 'Please select a new Tenant Type different from current Tenant Type.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Tenant Type different from current Tenant Type.',
                        ]);
                    }
                    break;
                case 1:
                    let old_division = this.old_division.field_key;
                    let new_division = this.new_division.field_key;
                    if (old_division === '' || typeof old_division === 'undefined') {
                        this.error_msg.push({ id: 'old_division', message: 'Please select current Division.' });
                        this.error_server_msg2.push(['Please select current Division.']);
                    }
                    if (new_division === '' || typeof new_division === 'undefined') {
                        this.error_msg.push({ id: 'new_division', message: 'Please select a new Division.' });
                        this.error_server_msg2.push(['Please select a new Division.']);
                    }
                    if (new_division === old_division) {
                        this.error_msg.push({
                            id: 'new_division',
                            message: 'Please select a new Division different from Current Division.',
                        });
                        this.error_server_msg2.push(['Please select a new Division different from Current Division.']);
                    }
                    break;
                case 2:
                    let old_fixed_interest_rate = this.old_fixed_interest_rate;
                    let new_fixed_interest_rate = this.new_fixed_interest_rate;
                    if (isNaN(old_fixed_interest_rate)) {
                        this.error_msg.push({
                            id: 'old_fixed_interest_rate',
                            message: 'Current Fixed Interest Rate must be numeric.',
                        });
                        this.error_server_msg2.push(['Current Fixed Interest Rate must be numeric.']);
                    }
                    if (isNaN(new_fixed_interest_rate)) {
                        this.error_msg.push({
                            id: 'new_fixed_interest_rate',
                            message: 'New Fixed Interest Rate must be numeric.',
                        });
                        this.error_server_msg2.push(['New Fixed Interest Rate must be numeric.']);
                    }

                    if (old_fixed_interest_rate === null) {
                        this.error_msg.push({
                            id: 'old_fixed_interest_rate',
                            message: 'Please enter current Fixed Interest Rate.',
                        });
                        this.error_server_msg2.push(['Please enter current Fixed Interest Rate.']);
                    }
                    if (new_fixed_interest_rate === null || typeof new_fixed_interest_rate === 'undefined') {
                        this.error_msg.push({
                            id: 'new_fixed_interest_rate',
                            message: 'Please enter a new valid Fixed Interest Rate.',
                        });
                        this.error_server_msg2.push(['Please enter a new valid Fixed Interest Rate.']);
                    }
                    if (parseFloat(new_fixed_interest_rate) === parseFloat(old_fixed_interest_rate)) {
                        this.error_msg.push({
                            id: 'new_fixed_interest_rate',
                            message:
                                'Please enter a new Fixed Interest Rate different from current Fixed Interest Rate.',
                        });
                        this.error_server_msg2.push([
                            'Please enter a new fixed interest rate different from current fixed interest rate.',
                        ]);
                    }
                    break;
                case 3:
                    let old_description = this.old_description;
                    let new_description = this.new_description;
                    if (old_description === null) {
                        this.error_msg.push({ id: 'old_description', message: 'Please enter current description.' });
                        this.error_server_msg2.push(['Please enter current description.']);
                    }
                    if (new_description === null || typeof new_description === 'undefined') {
                        this.error_msg.push({ id: 'new_description', message: 'Please enter a new description.' });
                        this.error_server_msg2.push(['Please enter a new description.']);
                    }
                    if (new_description === old_description) {
                        this.error_msg.push({
                            id: 'new_description',
                            message: 'Please enter a new description different from current description.',
                        });
                        this.error_server_msg2.push([
                            'Please enter a new description different from current description.',
                        ]);
                    }
                    break;
                case 4:
                    let old_grace_period = this.old_grace_period;
                    let new_grace_period = this.new_grace_period;
                    if (isNaN(old_grace_period)) {
                        this.error_msg.push({
                            id: 'old_grace_period',
                            message: 'Current Grace Period must be numeric.',
                        });
                        this.error_server_msg2.push(['Current Grace Period must be numeric.']);
                    }
                    if (isNaN(new_grace_period)) {
                        this.error_msg.push({ id: 'new_grace_period', message: 'New Grace Period must be numeric.' });
                        this.error_server_msg2.push(['New Grace Period must be numeric.']);
                    }
                    if (old_grace_period === null) {
                        this.error_msg.push({ id: 'old_grace_period', message: 'Please enter current Grace Period.' });
                        this.error_server_msg2.push(['Please enter current Grace Period.']);
                    }
                    if (new_grace_period === null || typeof new_grace_period === 'undefined') {
                        this.error_msg.push({
                            id: 'new_grace_period',
                            message: 'Please enter a new valid Grace Period.',
                        });
                        this.error_server_msg2.push(['Please enter a new valid Grace Period.']);
                    }
                    if (parseFloat(new_grace_period) === parseFloat(old_grace_period)) {
                        this.error_msg.push({
                            id: 'new_grace_period',
                            message: 'Please enter a new Grace Period different from current Grace Period.',
                        });
                        this.error_server_msg2.push([
                            'Please enter a new Grace Period different from current Grace Period.',
                        ]);
                    }
                    break;
                case 5:
                    const old_direct_management_fee = parseInt(this.old_direct_management_fee);
                    const new_direct_management_fee = parseInt(this.new_direct_management_fee);
                    if (old_direct_management_fee === null) {
                        this.error_msg.push({
                            id: 'old_direct_management_fee',
                            message: 'Please select current Direct Management Fee.',
                        });
                        this.error_server_msg2.push(['Please enter current Direct Management Fee.']);
                    }
                    if (new_direct_management_fee === null || typeof new_direct_management_fee === 'undefined') {
                        this.error_msg.push({
                            id: 'new_direct_management_fee',
                            message: 'Please select a new Direct Management Fee.',
                        });
                        this.error_server_msg2.push(['Please enter a new valid Direct Management Fee.']);
                    }
                    if (new_direct_management_fee === old_direct_management_fee) {
                        this.error_msg.push({
                            id: 'new_direct_management_fee',
                            message:
                                'Please select a new Direct Management Fee option different from current Direct Management Fee option.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Direct Management Fee option different from current Direct Management Fee option.',
                        ]);
                    }
                    if (new_direct_management_fee === 1 && new_direct_management_fee !== old_direct_management_fee) {
                        if (this.lease_accounts.length === 0) {
                            this.error_msg.push({
                                id: 'lease_accounts',
                                message: 'Please select a new Direct Management Fee Account.',
                            });
                            this.error_server_msg2.push(['Please select a new Direct Management Fee Account.']);
                        }
                        if (this.lease_account_desc === null || this.lease_account_desc === '') {
                            this.error_msg.push({
                                id: 'lease_account_desc',
                                message: 'Please enter a new Direct Management Fee Account Description.',
                            });
                            this.error_server_msg2.push([
                                'Please enter a new Direct Management Fee Account Description.',
                            ]);
                        }
                        if (this.lease_man_percentage <= 0) {
                            this.error_msg.push({
                                id: 'lease_man_percentage',
                                message: 'Please enter a new Direct Management Fee Percentage.',
                            });
                            this.error_server_msg2.push(['Please enter a new Direct Management Fee Percentage.']);
                        }
                        if (isNaN(this.lease_man_percentage)) {
                            this.error_msg.push({
                                id: 'lease_man_percentage',
                                message: 'New Management Percentage must be numeric.',
                            });
                            this.error_server_msg2.push(['New Management Percentage must be numeric.']);
                        }
                    }

                    break;
                case 6:
                    if (this.old_direct_management_fee_account === null) {
                        this.error_server_msg2.push(['Please select current Direct Management Fee Account.']);
                    }
                    if (this.new_direct_management_fee_account === null) {
                        this.error_server_msg2.push(['Please select a new Direct Management Fee Account.']);
                    }
                    if (this.new_direct_management_fee_account === this.old_direct_management_fee_account) {
                        this.error_server_msg2.push([
                            'Please select a new Direct Management Fee Account different from current Direct Management Fee Account.',
                        ]);
                    }
                    break;
                case 7:
                    if (this.old_direct_management_fee_account_desc === null) {
                        this.error_server_msg2.push([
                            'Please enter current Direct Management Fee Account Description.',
                        ]);
                    }
                    if (this.new_direct_management_fee_account_desc === null) {
                        this.error_server_msg2.push(['Please enter a new Direct Management Fee Account Description.']);
                    }
                    if (this.new_direct_management_fee_account_desc === this.old_direct_management_fee_account_desc) {
                        this.error_server_msg2.push([
                            'Please enter a new Direct Management Fee Account Description different from current Direct Management Fee Account Description.',
                        ]);
                    }
                    break;
                case 8:
                    if (this.old_direct_management_fee_percentage === null) {
                        this.error_server_msg2.push(['Please enter current Management Fee Percentage.']);
                    }
                    if (isNaN(this.old_direct_management_fee_percentage)) {
                        this.error_server_msg2.push(['Current Management Fee Percentage must be numeric.']);
                    }
                    if (this.new_direct_management_fee_percentage === null) {
                        this.error_server_msg2.push(['Please enter a new Management Fee Percentage.']);
                    }
                    if (
                        parseFloat(this.new_direct_management_fee_percentage) ===
                        parseFloat(this.old_direct_management_fee_percentage)
                    ) {
                        this.error_server_msg2.push([
                            'Please enter a new Management Fee Percentage different from current Management Fee Percentage.',
                        ]);
                    }
                    if (isNaN(this.new_direct_management_fee_percentage)) {
                        this.error_server_msg2.push(['New Management Fee Percentage must be numeric']);
                    }
                    break;
            }
            if (this.leases.length === 0) {
                this.error_msg.push({ id: 'leases', message: 'Please select a lease.' });
                this.error_server_msg2.push(['Please select a lease.']);
            } else {
                this.leases.map(function (value, key) {
                    leases.push(value['value']);
                });
            }

            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('page_source', 'PropertyBulkUpdateForm');
                form_data.append('old_tenant_type', this.old_tenant_type.field_key);
                form_data.append('new_tenant_type', this.new_tenant_type.field_key);
                form_data.append('old_division', this.old_division.field_key);
                form_data.append('new_division', this.new_division.field_key);
                form_data.append('old_fixed_interest_rate', this.old_fixed_interest_rate);
                form_data.append('new_fixed_interest_rate', this.new_fixed_interest_rate);
                form_data.append('old_description', this.old_description);
                form_data.append('new_description', this.new_description);
                form_data.append('old_grace_period', this.old_grace_period);
                form_data.append('new_grace_period', this.new_grace_period);
                form_data.append('old_direct_management_fee', parseInt(this.old_direct_management_fee));
                form_data.append('new_direct_management_fee', parseInt(this.new_direct_management_fee));
                form_data.append('old_direct_management_fee_account', this.old_direct_management_fee_account);
                form_data.append('new_direct_management_fee_account', this.new_direct_management_fee_account);
                form_data.append('old_direct_management_fee_account_desc', this.old_direct_management_fee_account_desc);
                form_data.append('new_direct_management_fee_account_desc', this.new_direct_management_fee_account_desc);
                form_data.append('old_direct_management_fee_percentage', this.old_direct_management_fee_percentage);
                form_data.append('new_direct_management_fee_percentage', this.new_direct_management_fee_percentage);
                form_data.append('lease_account', this.lease_accounts.value);
                form_data.append('lease_account_desc', this.lease_account_desc);
                form_data.append('lease_man_percentage', this.lease_man_percentage);
                form_data.append('type_code', this.type_code.key);
                form_data.append('leases', leases.join());
                form_data.append('no_load', true);
                this.$api.post(this.cirrus8_api_url + 'api/lease/update/lease-details', form_data).then((response) => {
                    this.$noty.success(response.data);
                    this.loading_page_setting = false;
                });
            }
        },
        updateCompany: function () {
            let companies = [];
            this.error_msg = [];
            this.error_server_msg2 = [];
            switch (this.type_code.key) {
                case 0:
                    let old_group = this.old_company_group.value;
                    let new_group = this.new_company_group.value;
                    if (old_group === '') {
                        this.error_msg.push({ id: 'old_group', message: 'Please select current Company Group.' });
                        this.error_server_msg2.push(['Please select current Company Group.']);
                    }
                    if (new_group === '' || typeof new_group === 'undefined') {
                        this.error_msg.push({ id: 'new_group', message: 'Please select a new Company Group.' });
                        this.error_server_msg2.push(['Please select a new Company Group.']);
                    }
                    if (new_group === old_group) {
                        this.error_msg.push({
                            id: 'new_group',
                            message: 'Please select a new Company Group different from current Company Group.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Company Group different from current Company Group.',
                        ]);
                    }
                    break;
                case 1:
                    let old_supplier_type = this.old_company_supplier_type.value;
                    let new_supplier_type = this.new_company_supplier_type.value;
                    if (old_supplier_type === '') {
                        this.error_msg.push({
                            id: 'old_supplier_type',
                            message: 'Please select current Supplier Type.',
                        });
                        this.error_server_msg2.push(['Please select current Supplier Type.']);
                    }
                    if (new_supplier_type === '' || typeof new_supplier_type === 'undefined') {
                        this.error_msg.push({ id: 'new_supplier_type', message: 'Please select a new Supplier Type.' });
                        this.error_server_msg2.push(['Please select a new Supplier Type.']);
                    }
                    if (new_supplier_type === old_supplier_type) {
                        this.error_msg.push({
                            id: 'new_supplier_type',
                            message: 'Please select a new Supplier Type different from current Supplier Type.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Supplier Type different from current Supplier Type.',
                        ]);
                    }
                    break;
                case 2:
                    let old_send_remittance = this.old_send_remittance;
                    let new_send_remittance = this.new_send_remittance;
                    if (old_send_remittance === '') {
                        this.error_msg.push({
                            id: 'old_send_remittance',
                            message: 'Please select current Send Remittance.',
                        });
                        this.error_server_msg2.push(['Please select current Send Remittance.']);
                    }
                    if (new_send_remittance === '' || typeof new_send_remittance === 'undefined') {
                        this.error_msg.push({
                            id: 'new_send_remittance',
                            message: 'Please select a new Send Remittance.',
                        });
                        this.error_server_msg2.push(['Please select a new Send Remittance.']);
                    }
                    if (new_send_remittance === old_send_remittance) {
                        this.error_msg.push({
                            id: 'new_send_remittance',
                            message:
                                'Please select a new Send Remittance option different from current Send Remittance option.',
                        });
                        this.error_server_msg2.push([
                            'Please select a new Send Remittance option different from current Send Remittance option.',
                        ]);
                    }
                    break;
            }
            if (this.companies.length === 0) {
                this.error_msg.push({ id: 'companies', message: 'Please select a company.' });
                this.error_server_msg2.push(['Please select a company.']);
            } else {
                this.companies.map(function (value, key) {
                    companies.push(value['value']);
                });
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('page_source', 'PropertyBulkUpdateForm');
                form_data.append('old_group', this.old_company_group.field_key);
                form_data.append('new_group', this.new_company_group.field_key);
                form_data.append('old_supplier_type', this.old_company_supplier_type.field_key);
                form_data.append('new_supplier_type', this.new_company_supplier_type.field_key);
                form_data.append('old_send_remittance', this.old_send_remittance);
                form_data.append('new_send_remittance', this.new_send_remittance);
                form_data.append('type_code', this.type_code.key);
                form_data.append('companies', companies.join());
                form_data.append('no_load', true);
                this.$api.post(this.cirrus8_api_url + 'api/company/company-bulk-update', form_data).then((response) => {
                    this.$noty.success(response.data);
                    this.loading_page_setting = false;
                });
            }
        },
        startTour: function () {
            this.$tours['leaseTour'].start();
        },
        startNewLeaseTour: function () {
            this.$tours['newLeaseTour'].start();
        },
        reset_filter: function () {
            switch (this.search_type) {
                case 0:
                    this.type_list = TYPE_LIST_DEFAULT;
                    this.type_code = { key: 0, value: this.property_manager_label };
                    break;
                case 1:
                    this.type_list = TYPE_LIST_LEASE;
                    this.type_code = { key: 0, value: 'Tenant Type' };
                    break;
                case 2:
                    this.type_list = TYPE_LIST_COMPANY;
                    this.type_code = { key: 0, value: 'Company Group' };
                    break;
            }

            this.property_code = { field_key: '' };
            this.properties = [];
            this.manager_code = { field_key: '' };
            this.manager_code_new = { field_key: '' };
            this.old_property_group = { value: '' };
            this.new_property_group = { value: '' };
            this.old_payment_run = { value: '' };
            this.new_payment_run = { value: '' };
            this.old_owner_report = { value: '' };
            this.new_owner_report = { value: '' };
            this.old_property_agent = null;
            this.new_property_agent = null;
            this.old_remittance_office = null;
            this.new_remittance_office = null;
            this.old_tenant_type = { value: '' };
            this.new_tenant_type = { value: '' };
            this.old_division = { value: '' };
            this.new_division = { value: '' };
            this.leases = [];
            this.old_company_group = { value: '' };
            this.new_company_group = { value: '' };
            this.companies = [];
            this.error_msg = [];
            this.error_server_msg2 = [];
        },
        changeSearchType: function (type) {
            this.properties = [];
            this.companies = [];
            this.leases = [];
            switch (type) {
                case 'type_code':
                    this.company_list = [];
                    this.lease_list = [];
                    this.property_code = { field_key: '' };
                    this.property_list = [];
                    this.manager_code = { field_key: '' };
                    this.manager_code_new = { field_key: '' };
                    this.old_property_group = { value: '' };
                    this.new_property_group = { value: '' };
                    this.old_owner_report = { value: '' };
                    this.new_owner_report = { value: '' };
                    this.old_property_agent = null;
                    this.new_property_agent = null;
                    this.old_remittance_office = null;
                    this.new_remittance_office = null;
                    this.old_inspection_fee = null;
                    this.new_inspection_fee = null;
                    this.old_inspection_fee_amount = null;
                    this.new_inspection_fee_amount = null;
                    this.old_inspection_fee_account = null;
                    this.new_inspection_fee_account = null;
                    this.old_inspection_fee_frequency = null;
                    this.new_inspection_fee_frequency = null;
                    this.property_inspection_payment_account = '';
                    this.inspection_amount = '';
                    this.old_rent_review_fee = null;
                    this.new_rent_review_fee = null;
                    this.old_rent_review_fee_amount = null;
                    this.new_rent_review_fee_amount = null;
                    this.old_rent_review_fee_account = null;
                    this.new_rent_review_fee_account = null;
                    this.old_rent_review_fee_desc = null;
                    this.new_rent_review_fee_desc = null;
                    this.old_payment_run = { value: '' };
                    this.new_payment_run = { value: '' };
                    this.old_tenant_type = { value: '' };
                    this.new_tenant_type = { value: '' };
                    this.old_division = { value: '' };
                    this.new_division = { value: '' };
                    this.old_fixed_interest_rate = null;
                    this.new_fixed_interest_rate = null;
                    this.old_grace_period = null;
                    this.new_grace_period = null;
                    this.old_description = null;
                    this.new_description = null;
                    this.old_direct_management_fee = null;
                    this.new_direct_management_fee = null;
                    this.old_direct_management_fee_account = null;
                    this.new_direct_management_fee_account = null;
                    this.old_direct_management_fee_account_desc = null;
                    this.new_direct_management_fee_account_desc = null;
                    this.old_direct_management_fee_percentage = null;
                    this.new_direct_management_fee_percentage = null;
                    this.old_company_group = { value: '' };
                    this.new_company_group = { value: '' };
                    this.old_company_supplier_type = { value: '' };
                    this.new_company_supplier_type = { value: '' };
                    this.old_send_remittance = null;
                    this.new_send_remittance = null;

                    this.error_msg = [];
                    this.error_server_msg2 = [];
                    this.manager_list = [];
                    this.property_group_list = [];
                    this.payment_run_list = [];
                    this.owner_report_list = [];
                    this.tenant_type_list = [];
                    this.division_list = [];
                    this.company_group_list = [];
                    this.company_supplier_type_list = [];
                    if (this.search_type === 0) {
                        switch (this.type_code.key) {
                            case 0:
                                this.loadManagerList();
                                break;
                            case 1:
                                this.loadPropertyGroupList();
                                break;
                            case 2:
                                this.loadPaymentRunList();
                                break;
                            case 3:
                                this.loadOwnerReportList();
                                break;
                            case 4:
                                this.loadPropertyAgentList();
                                break;
                            case 5:
                                this.loadPropertyRemittanceOffice();
                                break;
                            case 6:
                            case 6.2:
                            case 7:
                            case 7.3:
                                this.loadInspectionPaymentAccounts();
                                break;
                        }
                    } else if (this.search_type === 1) {
                        switch (this.type_code.key) {
                            case 0:
                                this.loadTenantTypeList();
                                break;
                            case 1:
                                this.loadDivisionList();
                                break;
                            case 6:
                                this.loadUnitDetails();
                                break;
                        }
                    } else {
                        switch (this.type_code.key) {
                            case 0:
                                this.loadCompanyGroupList();
                                break;
                            case 1:
                                this.loadCompanySupplierType();
                                break;
                        }
                    }

                    break;
                case 'manager_code':
                    this.lease_code = { field_key: '' };
                    this.lease_list = [];
                    this.property_code = { field_key: '' };
                    this.property_list = [];
                    if (this.search_type === 0 && this.manager_code.field_key !== '') {
                        this.loadPropertyList();
                    }
                    break;
                case 'group_code':
                    this.property_code = { field_key: '' };
                    this.property_list = [];
                    if (this.search_type === 0 && this.old_property_group.value !== '') {
                        this.loadPropertyList();
                    }
                    break;
                case 'payment_run':
                    this.property_code = { field_key: '' };
                    this.property_list = [];
                    if (this.search_type === 0 && this.old_payment_run.value !== '') {
                        this.loadPropertyList();
                    }
                    break;
                case 'owner_report':
                    this.property_code = { field_key: '' };
                    this.property_list = [];
                    if (this.search_type === 0 && this.old_owner_report.value !== '') {
                        this.loadPropertyList();
                    }
                    break;
                case 'property_agent':
                    this.property_code = { field_key: '' };
                    this.property_list = [];
                    if (this.search_type === 0 && this.old_property_agent) {
                        this.loadPropertyList();
                    }
                    break;
                case 'remittance_office':
                    this.property_code = { field_key: '' };
                    this.property_list = [];
                    if (this.search_type === 0 && this.old_remittance_office) {
                        this.loadPropertyList();
                    }
                    break;
                case 'inspection_fee':
                    this.property_list = [];
                    if (this.search_type === 0 && this.old_inspection_fee !== null) {
                        this.loadPropertyList();
                    }
                    break;
                case 'inspection_fee_amount':
                    this.property_list = [];
                    this.error_server_msg2 = [];
                    if (this.old_inspection_fee_amount === null) {
                        this.error_server_msg2.push(['Please enter current Inspection Fee Amount.']);
                    }
                    if (isNaN(this.old_inspection_fee_amount)) {
                        this.error_server_msg2.push([' Current Inspection Fee Amount must be numeric.']);
                    }

                    if (this.search_type === 0 && this.error_server_msg2.length <= 0) {
                        this.loadPropertyList();
                    }
                    break;
                case 'inspection_fee_account':
                    this.property_list = [];
                    this.error_server_msg2 = [];
                    if (this.old_inspection_fee_account === null) {
                        this.error_server_msg2.push(['Please select current Inspection Fee Account.']);
                    }
                    if (this.search_type === 0 && this.error_server_msg2 <= 0) {
                        this.loadPropertyList();
                    }
                    break;
                case 'inspection_fee_frequency':
                    this.property_list = [];
                    if (this.search_type === 0 && this.old_inspection_fee_frequency !== null) {
                        this.loadPropertyList();
                    }
                    break;
                case 'rent_review_fee':
                    this.property_list = [];
                    if (this.search_type === 0 && this.old_rent_review_fee !== null) {
                        this.loadPropertyList();
                    }
                    break;
                case 'rent_review_fee_amount':
                    this.property_list = [];
                    this.error_server_msg2 = [];
                    if (this.old_rent_review_fee_amount === null) {
                        this.error_server_msg2.push(['Please enter current Rent Review Fee Amount.']);
                    }
                    if (isNaN(this.old_rent_review_fee_amount)) {
                        this.error_server_msg2.push(['Current Rent Review Fee Amount must be numeric.']);
                    }
                    if (this.search_type === 0 && this.error_server_msg2.length <= 0) {
                        this.loadPropertyList();
                    }
                    break;
                case 'rent_review_fee_account':
                    this.property_list = [];
                    this.error_server_msg2 = [];
                    if (this.old_rent_review_fee_account === null) {
                        this.error_server_msg2.push(['Please select current Rent Review Fee Account.']);
                    }
                    if (this.search_type === 0 && this.error_server_msg2.length <= 0) {
                        this.loadPropertyList();
                    }
                    break;
                case 'rent_review_fee_description':
                    this.property_list = [];
                    this.error_server_msg2 = [];
                    if (this.old_rent_review_fee_desc === null) {
                        this.error_server_msg2.push(['Please enter current Rent Review Fee Description.']);
                    }
                    if (this.search_type === 0 && this.error_server_msg2.length <= 0) {
                        this.loadPropertyList();
                    }
                    break;
                case 'tenant_type':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_tenant_type.value !== '') {
                        this.loadLeaseList();
                    }
                    break;
                case 'division':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_division.value !== '') {
                        this.loadLeaseList();
                    }
                    break;
                case 'fixed_interest':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_fixed_interest_rate !== null) {
                        this.loadLeaseList();
                    }
                    break;
                case 'interest_description':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_description !== null) {
                        this.loadLeaseList();
                    }
                    break;
                case 'interest_grace_period':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_grace_period !== null) {
                        this.loadLeaseList();
                    }
                    break;
                case 'direct_management_fee':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_direct_management_fee !== null) {
                        this.loadLeaseList();
                    }
                    break;
                case 'direct_management_fee_account':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_direct_management_fee_account !== null) {
                        this.loadLeaseList();
                    }
                    break;
                case 'direct_management_fee_account_desc':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_direct_management_fee_account_desc !== null) {
                        this.loadLeaseList();
                    }
                    break;
                case 'direct_management_fee_percentage':
                    this.lease_list = [];
                    if (this.search_type === 1 && this.old_direct_management_fee_percentage !== null) {
                        this.loadLeaseList();
                    }
                    break;
                case 'company_group':
                    this.company_list = [];
                    if (this.search_type === 2 && this.old_company_group.value !== '') {
                        this.loadCompanyList();
                    }
                    break;
                case 'include_inactive_properties':
                    if (this.search_type === 0) {
                        // && this.property_list.length > 0){
                        this.property_list = [];
                        this.loadPropertyList();
                    } else if (this.search_type === 1 && this.lease_list.length > 0) {
                        this.lease_list = [];
                        this.loadLeaseList();
                    }
                    break;
                case 'company_supplier_type':
                    this.company_list = [];
                    if (this.search_type === 2 && this.old_company_supplier_type.value !== '') {
                        this.loadCompanyList();
                    }
                    break;
                case 'company_send_remittance':
                    this.company_list = [];
                    if (this.search_type === 2 && this.old_send_remittance !== null) {
                        this.loadCompanyList();
                    }
                    break;
            }
        },
    },
    watch: {
        type_code: function () {
            this.changeSearchType('type_code');
        },
        manager_code: function () {
            this.changeSearchType('manager_code');
        },
        old_property_group: function () {
            this.changeSearchType('group_code');
        },
        old_payment_run: function () {
            this.changeSearchType('payment_run');
        },
        old_owner_report: function () {
            this.changeSearchType('owner_report');
        },
        old_property_agent: function () {
            this.changeSearchType('property_agent');
        },
        old_remittance_office: function () {
            this.changeSearchType('remittance_office');
        },
        old_inspection_fee: function () {
            this.changeSearchType('inspection_fee');
        },
        old_inspection_fee_account: function () {
            this.changeSearchType('inspection_fee_account');
        },
        old_inspection_fee_frequency: function () {
            this.changeSearchType('inspection_fee_frequency');
        },
        old_rent_review_fee: function () {
            this.changeSearchType('rent_review_fee');
        },
        old_rent_review_fee_account: function () {
            this.changeSearchType('rent_review_fee_account');
        },
        old_tenant_type: function () {
            this.changeSearchType('tenant_type');
        },
        old_division: function () {
            this.changeSearchType('division');
        },
        old_company_group: function () {
            this.changeSearchType('company_group');
        },
        old_company_supplier_type: function () {
            this.changeSearchType('company_supplier_type');
        },
        old_send_remittance: function () {
            this.changeSearchType('company_send_remittance');
        },
        search_type: function () {
            this.reset_filter();
        },
        old_direct_management_fee: function () {
            this.changeSearchType('direct_management_fee');
        },
        new_direct_management_fee: function () {
            this.loadUnitDetails();
        },
        old_direct_management_fee_account: function () {
            this.changeSearchType('direct_management_fee_account');
        },
    },
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
.c8-page .page-form .form-row .form-label {
    max-width: 210px !important;
}
</style>
