<template>
    <div
        class="property-keys"
        v-on:dblclick="doubleClickForm()"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Recoverable Split</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    icon
                    @click="modalOpenAED(-1)"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable()"
                    class="v-step-refresh-button"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <v-col
            class="text-center"
            v-if="property_recoverable_split_list.length === 0 && !loading_setting"
            v-show="isEditable()"
        >
            <v-btn
                v-if="!pmro_read_only"
                depressed
                small
                color="success"
                @click="modalOpenAED(-1)"
                >Add Recoverable Split Account</v-btn
            >
            <div
                style="margin: 10px 0px"
                v-else
            >
                No Recoverable Split Account at the moment
            </div>
        </v-col>
        <div v-if="!loading_setting">
            <div class="form-row">
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="property_recoverable_split_list.length > 0"
                    dense
                    item-key="floor_id"
                    :headers="headers"
                    :items="property_recoverable_split_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :single-expand="!isPropertyInPrintView()"
                    show-expand
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ property_recoverable_split_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            style="font-size: 21px; position: relative; top: -1px"
                            title="Unit breakdown is not updated"
                            color="red"
                            v-if="item.there_is_inactive_unit_flag"
                            >info</v-icon
                        >
                        <span
                            ><v-chip
                                x-small
                                class="ma-2"
                                :color="
                                    computeTotalUnitPercentage(property_recoverable_split_list.indexOf(item)) > 100
                                        ? 'red'
                                        : 'grey'
                                "
                                text-color="white"
                                >{{
                                    computeTotalUnitPercentage(property_recoverable_split_list.indexOf(item))
                                }}%</v-chip
                            ></span
                        >
                        <v-icon
                            small
                            @click="modalOpenAED(property_recoverable_split_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit</v-icon
                        >
                        <v-icon
                            color="red"
                            v-if="edit_form"
                            @click="deleteAccount(item.property_recoverable_split_id)"
                            >close</v-icon
                        >
                    </template>
                    <template v-slot:expanded-item="{ headers, item }">
                        <td :colspan="headers.length">
                            <div style="float: left">
                                <div class="tree-vertical-line"></div>
                                <div class="tree-horizontal-line"></div>
                            </div>
                            <div style="padding-left: 35px">
                                <v-card
                                    class="section-toolbar subHeader"
                                    dark
                                    text
                                    tile
                                >
                                    <v-card-actions>
                                        <h6 class="title font-weight-black">Unit(s)</h6>
                                        <v-spacer></v-spacer>
                                    </v-card-actions>
                                </v-card>
                                <sui-table
                                    v-show="item.unit_list.length > 0"
                                    class="vue-data-grid"
                                    stackable
                                    width="100%"
                                    border="0"
                                >
                                    <sui-table-header>
                                        <sui-table-row class="fieldDescription">
                                            <sui-table-header-cell style="width: 40px">#</sui-table-header-cell>
                                            <sui-table-header-cell>Unit Code</sui-table-header-cell>
                                            <sui-table-header-cell>Description</sui-table-header-cell>
                                            <sui-table-header-cell class="text-right">Percentage</sui-table-header-cell>
                                            <sui-table-header-cell class="text-right"></sui-table-header-cell>
                                        </sui-table-row>
                                    </sui-table-header>
                                    <sui-table-body class="page-form">
                                        <sui-table-row
                                            class="form-row"
                                            v-for="(unit_data, unit_index) in item.unit_list"
                                            :key="unit_index"
                                            :class="itemRowBackground(unit_data)"
                                        >
                                            <sui-table-cell style="width: 40px">
                                                <span class="form-input-text"
                                                    >{{ property_recoverable_split_list.indexOf(item) + 1 }}.{{
                                                        unit_index + 1
                                                    }}</span
                                                >
                                            </sui-table-cell>
                                            <sui-table-cell class="text-left">{{ unit_data.unit_code }}</sui-table-cell>
                                            <sui-table-cell class="text-left">{{ unit_data.unit_name }}</sui-table-cell>
                                            <sui-table-cell class="text-right">
                                                <cirrus-input
                                                    inputFormat="percentage"
                                                    :size="'10'"
                                                    :key="
                                                        'lease_charge_item_amount_' +
                                                        property_recoverable_split_list.indexOf(item) +
                                                        unit_index
                                                    "
                                                    v-model="unit_data.unit_percentage"
                                                    :edit_form="edit_form"
                                                ></cirrus-input>
                                            </sui-table-cell>
                                            <sui-table-cell
                                                class="text-right"
                                                v-show="!isPropertyInPrintView()"
                                            >
                                                <v-btn
                                                    depressed
                                                    x-small
                                                    color="success"
                                                    v-if="edit_form"
                                                    v-show="
                                                        !formSectionReadOnly(
                                                            pm_property_form_read_only,
                                                            form_type,
                                                            form_section,
                                                            is_inactive,
                                                        )
                                                    "
                                                    @click="updateUnit(item, unit_data)"
                                                >
                                                    Update
                                                </v-btn>
                                                <v-icon
                                                    color="red"
                                                    v-if="edit_form && unit_data.property_recoverable_split_unit_id"
                                                    @click="
                                                        deleteAccountUnit(unit_data.property_recoverable_split_unit_id)
                                                    "
                                                    >close</v-icon
                                                >
                                            </sui-table-cell>
                                        </sui-table-row>
                                    </sui-table-body>
                                </sui-table>
                            </div>
                        </td>
                    </template>
                </v-data-table>
            </div>
        </div>
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Recoverable Split Account Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <!---->
                    <div :key="property_recoverable_split_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{ property_recoverable_split_arr.item_no }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Account:</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input multiselect_short"
                                >
                                    <cirrus-single-select-v2
                                        v-model="property_recoverable_split_arr.account_code"
                                        :options="account_exp_list"
                                        ref="refExpAccount"
                                        trackBy="field_key"
                                        label="field_key_w_value"
                                        return="field_key"
                                        placeholder="Select an account"
                                    />
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalOpenAED(-1)"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="AED_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog, objectClone } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'RECOVERABLE_SPLIT',
            loading_setting: false,
            edit_form: false,
            error_server_msg: {},
            error_server_msg2: [],
            property_recoverable_split_list: [],
            property_recoverable_split_arr: [],
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Account Code', value: 'account_code', sortable: true, width: '10%' },
                { text: 'Account Name', value: 'account_name', sortable: true },
                { text: '', value: 'data-table-expand', class: ['data-table-mini-action'], align: 'end' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '250px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 10,
            search_datatable: '',
            expanded: false,
            AED_modal: false,
            show_activity_log_modal: false,
            account_exp_list: [],
        };
    },
    mounted() {
        this.loadForm();
        this.loadExpAccounts();
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        resetForm: function () {
            this.edit_form = false;
        },
        loadForm: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '') {
                this.loadPropertyRecoverableSplit();
            }
        },
        loadPropertyRecoverableSplit: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            if (this.isPropertyFormLive()) {
                //get data from live
                this.$api.post('property/fetch/recoverable-split-list', form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            } else {
                let apiUrl = 'temp/property/fetch/recoverable-split-list';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadResponseToVariables(response);
                });
            }
            if (this.new_property) this.edit_form = true;
            else this.edit_form = false;
        },
        loadResponseToVariables: function (response) {
            this.loading_setting = false;
            this.property_recoverable_split_list = response.data.recoverable_split_list;
        },
        computeTotalUnitPercentage: function (index) {
            let total = 0;
            if (this.property_recoverable_split_list.length === 0) return total;
            let unit_list = this.property_recoverable_split_list[index].unit_list;
            for (let x = 0; x <= unit_list.length - 1; x++) {
                let percentage = parseFloat(unit_list[x].unit_percentage);
                total += percentage;
            }
            return this.roundTo(total, 2);
        },
        async deleteAccount(table_id) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('property_recoverable_split_id', table_id);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                if (this.isPropertyFormLive()) {
                    //get data from live
                    this.$api.post('property/delete/recoverable-split-account', form_data).then((response) => {
                        this.loadForm();
                    });
                } else {
                    let apiUrl = 'temp/property/delete/recoverable-split-account';
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.loadForm();
                    });
                }
            }
        },
        async deleteAccountUnit(table_id) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('property_recoverable_split_unit_id', table_id);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                if (this.isPropertyFormLive()) {
                    //get data from live
                    this.$api.post('property/delete/recoverable-split-account-unit', form_data).then((response) => {
                        this.loadForm();
                    });
                } else {
                    let apiUrl = 'temp/property/delete/recoverable-split-account-unit';
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.loadForm();
                    });
                }
            }
        },
        updateUnit: function (main_item, item) {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let table_id = item.property_recoverable_split_unit_id;
            let unit_code = item.unit_code;
            let unit_name = item.unit_name;
            let unit_percentage = item.unit_percentage;
            // this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('property_recoverable_split_id', main_item.property_recoverable_split_id);
            form_data.append('property_recoverable_split_unit_id', table_id);
            form_data.append('unit_code', unit_code);
            form_data.append('unit_name', unit_name);
            form_data.append('unit_percentage', unit_percentage);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let api_url = 'temp/property/update-or-create/recoverable-split-account-unit';
            if (this.isPropertyFormLive()) api_url = 'property/update-or-create/recoverable-split-account-unit';
            this.$api.post(api_url, form_data).then((response) => {
                // this.loadForm();
                // this.loading_setting = false;
                this.error_server_msg2 = response.data.error_server_msg2;
                if (this.error_server_msg2.length === 0) {
                    //property_recoverable_split_unit_id
                    let index = this.property_recoverable_split_list.indexOf(main_item);
                    let index2 = this.property_recoverable_split_list[index].unit_list.indexOf(item);
                    this.property_recoverable_split_list[index].unit_list[index2].property_recoverable_split_unit_id =
                        response.data.property_recoverable_split_unit_id;
                }
            });
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let account_code = '';
            let account_name = '';
            let item_no = 'New';
            let property_recoverable_split_id = '';
            if (index >= 0) {
                let property_recoverable_split_data = this.property_recoverable_split_list[index];
                account_code = property_recoverable_split_data.account_code;
                account_name = property_recoverable_split_data.account_name;
                account_name = property_recoverable_split_data.item_no;
                property_recoverable_split_id = property_recoverable_split_data.property_recoverable_split_id;
            }
            this.property_recoverable_split_arr = {
                index: Math.random(),
                item_no: item_no,
                account_code: account_code,
                account_name: account_name,
                property_recoverable_split_id: property_recoverable_split_id,
            };
            // console.log(this.property_recoverable_split_arr);
        },
        loadExpAccounts: function () {
            this.$api.post('parameter/fetch/income-dr-list').then((response) => {
                this.account_exp_list = response.data.ungrouped;
            });
        },
        modalSubmitData: function () {
            let data_arr = this.property_recoverable_split_arr;
            let account_code = data_arr.account_code;
            let property_recoverable_split_id = data_arr.property_recoverable_split_id;
            let error_arr = [];
            if (!account_code) error_arr.push(['Please select an account.']);
            this.error_server_msg2 = error_arr;
            if (this.error_server_msg2.length > 0) return;
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('property_recoverable_split_id', property_recoverable_split_id);
            form_data.append('account_code', account_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let api_url = 'temp/property/update-or-create/recoverable-split-account';
            if (this.isPropertyFormLive()) api_url = 'property/update-or-create/recoverable-split-account';
            this.$api.post(api_url, form_data).then((response) => {
                this.loading_setting = false;
                this.error_server_msg2 = response.data.error_server_msg2;
                if (this.error_server_msg2.length === 0) {
                    this.property_recoverable_split_arr.property_recoverable_split_id =
                        response.data.property_recoverable_split_id;
                    this.loadForm();
                }
            });
        },
        itemRowBackground: function (item) {
            // console.log(item);
            return item.unit_show_flag ? 'inactive_style' : '';
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
            }
        },
    },
    computed: {
        ...mapState(['pm_property_form_read_only']),
    },
    created() {
        bus.$on('loadPropertyRecoverableSplitFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
<style>
.inactive_style {
    background: #f7f7f7;
    color: #b2b2b2;
}
.inactive_style .text-start .form-row .form-input-text {
    color: #b2b2b2 !important;
}
</style>
