<template>
    <div
        v-on:dblclick="!readonly && isEditable()"
        style="max-width: 100%"
    >
        <v-card
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Bank Account</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable()"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="edit_form"
                    class="v-step-save-1-button"
                    icon
                    @click="saveForm()"
                >
                    <v-icon
                        light
                        color="green"
                        >check</v-icon
                    >
                </v-btn>
                <v-btn
                    x-small
                    class="v-step-refresh-button"
                    v-if="isEditable()"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Bank Account</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">
                            <div>{{ property_bank_account_code }} - {{ property_bank_account_name }}</div>
                            <br />
                            <div>
                                To change the associated {{ trust_account_label }} for this property please contact
                                support - <EMAIL>
                            </div>
                        </span>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">
                            <sui-checkbox
                                v-model="property_auto_pay_owner_flag"
                                data-inverted=""
                                :data-tooltip="
                                    edit_form ? 'Allow Owner Payments (Default setting for Payment Runs)' : false
                                "
                                label="Allow Owner Payments (Default setting for Payment Runs)"
                            />
                        </span>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">
                            <sui-checkbox
                                v-model="bank_partitioning_flag"
                                data-inverted=""
                                :data-tooltip="edit_form ? 'Apply partitioning to the property' : false"
                                label="Apply partitioning to the property"
                            />
                        </span>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Default Amount to withhold <br />from Owner</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            :inputFormat="'dollar'"
                            custom_class="cirrus-input-table-textbox"
                            v-model="property_withhold_owner_amount"
                            size=""
                            :id="'property_withhold_owner_amount'"
                            data-inverted=""
                            :edit_form="edit_form"
                        ></cirrus-input>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Default Note for withheld <br />amount</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            custom_class="cirrus-input-table-textbox"
                            v-model="property_withhold_owner_comment"
                            size=""
                            :id="'property_withhold_owner_comment'"
                            data-inverted=""
                            :edit_form="edit_form"
                        ></cirrus-input>
                    </v-col>
                </v-row>
            </div>
        </div>
        <v-divider></v-divider>
        <v-card
            elevation="0"
            v-if="edit_form"
        >
            <v-card-actions v-if="edit_form">
                <v-spacer></v-spacer>
                <v-btn
                    class="v-step-save-2-button"
                    @click="saveForm()"
                    color="success"
                    dark
                    small
                >
                    Save Bank Account Details
                </v-btn>
            </v-card-actions>
        </v-card>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';
import { mapState } from 'vuex';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            trust_account_label: 'Trust Account',
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_BANK_ACCOUNT',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            property_bank_account: { field_key: '', field_value: 'Please select...' },
            property_bank_account_code: '',
            property_bank_account_name: '',
            property_auto_pay_owner_flag: false,
            allow_bank_partition_flag: false,
            bank_partitioning_flag: false,
            property_withhold_owner_amount: '0.000',
            property_withhold_owner_comment: '',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        this.loadCountryDefaults();
        // console.log('aa');
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.trust_account_label = this.ucwords(this.country_defaults.trust_account);
            });
        },

        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        loadParameters: function () {
            // var form_data = new FormData();
            // form_data.append('property_code', this.property_code);
            // form_data.append('no_load', true);
            // let apiUrl = 'parameter/fetch/property-insurance';
            // this.$api.post(apiUrl, form_data).then(response => {
            //   this.property_insurance_type_list = response.data.property_insurance_type_list;
            // });
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '' || this.forceLoad) {
                // console.log('load property property details');
                this.loadPropertyBankAccount();
            }
            if (!this.new_property) this.edit_form = false;
        },
        loadPropertyBankAccount: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';

            if (this.isPropertyFormLive()) {
                apiUrl = 'property/fetch/bank-account';
            } else {
                apiUrl = 'temp/property/fetch/bank-account';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.property_bank_account_code = response.data.property_bank_account_code;
                this.property_bank_account_name = response.data.property_bank_account_name;

                this.property_bank_account = {
                    field_key: this.property_bank_account_code,
                    field_value: this.property_bank_account_name,
                };

                this.property_auto_pay_owner_flag = response.data.property_auto_pay_owner_flag;
                this.allow_bank_partition_flag = response.data.allow_bank_partition_flag;
                this.bank_partitioning_flag = response.data.bank_partitioning_flag;
                this.property_withhold_owner_amount = response.data.property_withhold_owner_amount;
                this.property_withhold_owner_comment = response.data.property_withhold_owner_comment;
                this.loading_setting = false;
            });
        },
        saveForm: function () {
            this.error_server_msg2 = [];
            let property_withhold_owner_amount = this.property_withhold_owner_amount;
            if (property_withhold_owner_amount === '' || !property_withhold_owner_amount) {
            } else {
                if (isNaN(parseFloat(property_withhold_owner_amount))) {
                    this.error_server_msg2.push(['Please input a valid default amount to withhold from owner.']);
                }
            }

            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);

                form_data.append('property_bank_account_code', this.property_bank_account.field_key);
                form_data.append('property_auto_pay_owner_flag', this.property_auto_pay_owner_flag);
                form_data.append('bank_partitioning_flag', this.bank_partitioning_flag);
                form_data.append('property_withhold_owner_amount', property_withhold_owner_amount);
                form_data.append('property_withhold_owner_comment', this.property_withhold_owner_comment);
                form_data.append('no_load', true);
                let api_url;
                if (this.isPropertyFormLive()) {
                    //get data from live
                    api_url = 'property/update/bank-account-details';
                } else {
                    api_url = 'temp/property/update/bank-account-details';
                }
                this.$api.post(api_url, form_data).then((response) => {
                    this.loadForm();
                    this.loading_setting = false;
                });
            }
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.loadParameters();
            }
        },
    },
    created() {
        bus.$on('loadPropertyBankAccountFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
