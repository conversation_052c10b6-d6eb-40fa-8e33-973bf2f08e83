<template>
    <div
        class="page-form"
        v-on:dblclick="doubleClickForm()"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Calendar</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="historical_calendar_search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
            </v-card-actions>
        </v-card>

        <div class="page-form">
            <div class="form-row">
                <v-card
                    class="section-toolbar subHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h6 class="title font-weight-black">Property Calendar</h6>
                        <v-spacer></v-spacer>
                        <v-btn
                            x-small
                            v-if="isEditable() && !pmro_read_only"
                            icon
                            @click="modalAddCalendar()"
                        >
                            <v-icon>add</v-icon>
                        </v-btn>

                        <v-btn
                            x-small
                            v-if="isEditable()"
                            class="v-step-refresh-button"
                            icon
                            @click="loadForm()"
                        >
                            <v-icon>refresh</v-icon>
                        </v-btn>
                    </v-card-actions>
                </v-card>
                <v-progress-linear
                    indeterminate
                    color="cyan"
                    v-if="loading_setting"
                ></v-progress-linear>
                <div
                    class="text-center"
                    v-if="historical_calendar_list.length === 0 && !loading_setting"
                >
                    <v-alert
                        text
                        class="my-0"
                        prominent
                        dense
                        tile
                        transition="scale-transition"
                        color="red lighten-2"
                    >
                        <v-row
                            align="center"
                            no-gutters
                            dense
                        >
                            <v-col
                                class="col-12"
                                style="font-size: 11px"
                            >
                                <div style="padding: 2px">There are no year in master calendar</div>
                            </v-col>
                        </v-row>
                    </v-alert>
                </div>
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="historical_calendar_list.length > 0"
                    dense
                    item-key="id"
                    :headers="historical_calendar_headers"
                    :items="historical_calendar_list"
                    :items-per-page="-1"
                    hide-default-footer
                    :search="historical_calendar_search_datatable"
                    :calculate-widths="true"
                    @click:row="selectYear"
                >
                    <template v-slot:item.index="{ item }">
                        {{ historical_calendar_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.calendar_year="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.calendar_year }}</span>
                        </div>
                    </template>
                    <template v-slot:item.calendar_period_ctr="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.calendar_period_ctr }}</span>
                        </div>
                    </template>
                    <template v-slot:item.calendar_start_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.calendar_start_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.calendar_end_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.calendar_end_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.closed="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">
                                <v-icon
                                    v-if="item.closed"
                                    color="success"
                                    >check</v-icon
                                >
                            </span>
                        </div>
                    </template>
                </v-data-table>
                <!--datatable end-->

                <v-card
                    class="section-toolbar subHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h6 class="title font-weight-black">Selected Calendar</h6>
                        <v-spacer></v-spacer>

                        <v-btn
                            x-small
                            v-show="isEditable() && !pmro_read_only"
                            class="v-step-edit-button"
                            v-if="!edit_form"
                            icon
                            @click="edit_form = true"
                        >
                            <v-icon>edit</v-icon>
                        </v-btn>
                        <v-btn
                            x-small
                            v-show="isEditable()"
                            class="v-step-refresh-button"
                            icon
                            @click="loadPropertyCalendarPeriod(current_year)"
                        >
                            <v-icon>refresh</v-icon>
                        </v-btn>
                    </v-card-actions>
                </v-card>
                <cirrus-server-error
                    :error_msg="error_server_msg"
                    :errorMsg2="error_server_msg2"
                ></cirrus-server-error>
                <v-progress-linear
                    indeterminate
                    color="cyan"
                    v-if="loading_setting"
                ></v-progress-linear>
                <div
                    class="text-center"
                    v-if="calendar_period_list.length === 0 && !loading_setting"
                >
                    <v-alert
                        text
                        class="my-0"
                        prominent
                        dense
                        tile
                        transition="scale-transition"
                        color="red lighten-2"
                    >
                        <v-row
                            align="center"
                            no-gutters
                            dense
                        >
                            <v-col
                                class="col-12"
                                style="font-size: 11px"
                            >
                                <div style="padding: 2px">
                                    There are no periods in the master calendar for the date you have selected
                                </div>
                            </v-col>
                        </v-row>
                    </v-alert>
                </div>
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="calendar_period_list.length > 0"
                    dense
                    item-key="calendar_id"
                    :headers="calendar_period_headers"
                    :items="calendar_period_list"
                    :items-per-page="-1"
                    hide-default-footer
                    :search="historical_calendar_search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ calendar_period_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.calendar_year="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.calendar_year }}</span>
                        </div>
                    </template>
                    <template v-slot:item.calendar_period="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.calendar_period }}</span>
                        </div>
                    </template>
                    <template v-slot:item.calendar_start_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <cirrus-icon-date-picker
                                :size="'40'"
                                :id="'calendar_start_date' + String(Math.random()).replace('.', '')"
                                v-model="item.calendar_start_date"
                                :edit_form="edit_form && !item.calendar_closed_flag"
                            ></cirrus-icon-date-picker>
                        </div>
                    </template>
                    <template v-slot:item.calendar_end_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <cirrus-icon-date-picker
                                :size="'40'"
                                :id="'calendar_end_date' + String(Math.random()).replace('.', '')"
                                v-model="item.calendar_end_date"
                                :edit_form="edit_form && !item.calendar_closed_flag"
                            ></cirrus-icon-date-picker>
                        </div>
                    </template>
                    <template v-slot:item.period_status_action="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="edit_form"
                            >
                                <v-icon
                                    v-if="item.calendar_closed_flag"
                                    color="success"
                                    x-small
                                    >check</v-icon
                                >
                                <v-icon
                                    v-if="!item.calendar_closed_flag"
                                    color="success"
                                    x-small
                                    >library_books</v-icon
                                >
                                |
                                <a
                                    :data-tooltip="
                                        item.calendar_closed_flag
                                            ? 'Open this period and all prior periods'
                                            : 'Close this period and all prior periods'
                                    "
                                >
                                    <v-icon
                                        color="green"
                                        class="rotate90"
                                        @click="toggleUpPeriod(calendar_period_list.indexOf(item))"
                                        >arrow_left
                                    </v-icon>
                                </a>
                                <a
                                    :data-tooltip="
                                        item.calendar_closed_flag
                                            ? 'Open this period and all succeeding periods'
                                            : 'Close this period and all succeeding periods'
                                    "
                                >
                                    <v-icon
                                        color="green"
                                        class="rotate90"
                                        @click="toggleDownPeriod(calendar_period_list.indexOf(item))"
                                        >arrow_right
                                    </v-icon>
                                </a>

                                |
                                <a
                                    @click="openPeriod(calendar_period_list.indexOf(item))"
                                    v-if="item.calendar_closed_flag"
                                    >Open</a
                                >
                                <a
                                    @click="closePeriod(calendar_period_list.indexOf(item))"
                                    v-if="!item.calendar_closed_flag"
                                    >Close</a
                                >
                            </span>
                        </div>
                    </template>
                    <template v-slot:item.gl_period_status_action="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="edit_form"
                            >
                                <v-icon
                                    v-if="item.calendar_gl_closed_flag"
                                    color="success"
                                    x-small
                                    >check</v-icon
                                >
                                <v-icon
                                    v-if="!item.calendar_gl_closed_flag"
                                    color="success"
                                    x-small
                                    >library_books</v-icon
                                >
                                |
                                <v-icon
                                    color="green"
                                    class="rotate90"
                                    @click="toggleUpGLPeriod(calendar_period_list.indexOf(item))"
                                    >arrow_left
                                </v-icon>
                                <v-icon
                                    color="green"
                                    class="rotate90"
                                    @click="toggleDownGLPeriod(calendar_period_list.indexOf(item))"
                                    >arrow_right
                                </v-icon>
                                |
                                <a
                                    @click="openGLPeriod(calendar_period_list.indexOf(item))"
                                    v-if="item.calendar_gl_closed_flag"
                                    >Open</a
                                >
                                <a
                                    @click="closeGLPeriod(calendar_period_list.indexOf(item))"
                                    v-if="!item.calendar_gl_closed_flag"
                                    >Close</a
                                >
                            </span>
                        </div>
                    </template>
                    <template v-slot:item.action="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="edit_form && !item.calendar_closed_flag"
                            >
                                <v-btn
                                    color="success"
                                    x-small
                                    @click="updatePeriod(calendar_period_list.indexOf(item))"
                                    >Update</v-btn
                                >
                            </span>
                        </div>
                    </template>
                </v-data-table>
                <!--datatable end-->
                <v-dialog
                    v-model="show_add_calendar"
                    max-width="600"
                    content-class="c8-page"
                >
                    <v-card
                        class="section-toolbar"
                        dark
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">
                                Add Property Calendar : {{ master_calendar.cal_desc }}
                            </h6>
                            <v-spacer></v-spacer>
                            <a
                                href="#"
                                class="dialog-close"
                                @click.prevent="show_add_calendar = false"
                            >
                                <v-icon>mdi-close</v-icon>
                            </a>
                        </v-card-actions>
                    </v-card>
                    <v-card>
                        <v-card-text>
                            <cirrus-server-error
                                :error_msg="error_server_msg"
                                :errorMsg2="error_server_msg2"
                            ></cirrus-server-error>
                            <v-alert
                                type="success"
                                dense
                                tile
                                text
                                v-if="success_flag"
                            >
                                Successfully Saved
                            </v-alert>
                            <div>
                                <div class="page-form">
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="form-label"
                                            >For year ending
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="form-input"
                                        >
                                            <multiselect
                                                v-model="year_ending"
                                                :options="calendar_list"
                                                openDirection="bottom"
                                                class="vue-select2 dropdown-left dropdown-300"
                                                group-label="language"
                                                placeholder="Select a year"
                                                track-by="field_key"
                                                label="field_value"
                                                :show-labels="false"
                                                ><span slot="noResult"
                                                    >Oops! No elements found. Consider changing the search query.</span
                                                >
                                            </multiselect>
                                        </v-col>
                                    </v-row>
                                </div>
                            </div>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer />
                            <v-btn
                                class="v-step-save-2-button"
                                @click="modalSubmitData()"
                                color="success"
                                dark
                                depressed
                                small
                            >
                                <v-icon
                                    left
                                    dark
                                    size="18"
                                    >check
                                </v-icon>
                                Save
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
            </div>
        </div>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { mapGetters, mapState } from 'vuex';

export default {
    props: {
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            loading_setting: false,
            edit_form: false,
            show_add_calendar: false,
            success_flag: false,
            error_server_msg: {},
            error_server_msg2: [],
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_CALENDAR',
            historical_calendar_list: [],
            calendar_period_list: [],
            historical_calendar_headers: [
                { text: '#', value: 'index', sortable: false, width: 50 },
                { text: 'Year', value: 'calendar_year', width: 100 },
                { text: 'Period', value: 'calendar_period_ctr', align: 'center', width: 100 },
                { text: 'Start Date', value: 'calendar_start_date_raw', width: 287 },
                { text: 'End Date', value: 'calendar_end_date_raw', width: 287 },
                { text: 'Year Status', value: 'closed', align: 'end', sortable: false },
            ],
            calendar_period_headers: [
                { text: '#', value: 'index', sortable: false, width: 50 },
                { text: 'Year', value: 'calendar_year', width: 100 },
                { text: 'Period', value: 'calendar_period', align: 'center', width: 100 },
                { text: 'Start Date', value: 'calendar_start_date_raw', width: 287 },
                { text: 'End Date', value: 'calendar_end_date_raw', width: 287 },
                { text: 'Period Status', value: 'period_status_action', align: 'center', sortable: false },
                { text: 'GL Period Status', value: 'gl_period_status_action', align: 'center', sortable: false },
                { text: '', value: 'action', align: 'end', sortable: false, width: '78px' },
            ],
            historical_calendar_search_datatable: '',
            calendar_period_search_datatable: '',
            current_year: '',
            open_message:
                'The calendar period will be opened, this may effect prior period reporting and current transactions being entered. Calendars are normally managed at the global level. Would you like to continue with the change?',
            close_message:
                'The calendar period will be closed. Calendars are normally managed at the global level. Would you like to continue with the change?',
            master_calendar_list: [],
            calendar_list: [],
            master_calendar: { field_key: 'Financial', field_value: 'Financial' },
            year_ending: { field_key: '', field_value: '' },
        };
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            if (this.property_code !== '') {
                this.loadPropertyCalendar();
                this.loadParameters();
            }
        },
        modalSubmitData: function () {
            let master_calendar_code = this.master_calendar.value;
            let year_ending_code = this.year_ending.field_key;

            let error_server_msg2 = [];
            if (master_calendar_code === '') error_server_msg2.push(['You have not selected a master calendar.']);
            if (year_ending_code === '') error_server_msg2.push(['You have not entered a valid year.']);
            this.error_server_msg2 = error_server_msg2;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('master_calendar_code', master_calendar_code);
                form_data.append('year_ending_code', year_ending_code);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isPropertyFormLive()) {
                    apiUrl = 'property/create/calendar-periods';
                } else {
                    apiUrl = 'temp/property/create/calendar-periods';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    let error_server_msg2 = response.data.error_server_msg2;
                    if (error_server_msg2.length > 0) {
                        this.error_server_msg2 = error_server_msg2;
                    } else {
                        this.success_flag = true;
                        this.current_year = year_ending_code;
                        this.show_add_calendar = false;
                        this.loadPropertyCalendar();
                        this.loadPropertyCalendarPeriod(this.current_year);
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        loadPropertyCalendar: function () {
            this.loading_setting = true;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let api_url;
            if (this.isPropertyFormLive()) {
                //get data from live
                api_url = 'property/fetch/calendars';
            } else {
                api_url = 'temp/property/fetch/calendars';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.historical_calendar_list = response.data.property_calendar_list;
                this.current_year = response.data.current_year;
                this.year_ending = {
                    field_key: parseInt(response.data.next_year),
                    field_value: parseInt(response.data.next_year),
                };
                this.loading_setting = false;
                if (this.current_year !== '') {
                    this.loadPropertyCalendarPeriod(this.current_year);
                }
                this.master_calendar = response.data.calendar_type;
                if (this.new_property) this.edit_form = true;
                else this.edit_form = false;
            });
        },
        loadPropertyCalendarPeriod: function (calendar_year) {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('calendar_year', calendar_year);
            form_data.append('no_load', true);
            let api_url;
            if (this.isPropertyFormLive()) {
                //get data from live
                api_url = 'property/fetch/calendar-periods';
            } else {
                api_url = 'temp/property/fetch/calendar-periods';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.calendar_period_list = response.data.calendar_period_list;
                this.loading_setting = false;
            });
        },
        async openPeriod(index) {
            let dialog_prop = {
                title: 'Warning',
                message: this.open_message,
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.togglePeriodStatus(index, 'calendar-open-period');
            }
        },
        async closePeriod(index) {
            let dialog_prop = {
                title: 'Warning',
                message: this.close_message,
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.togglePeriodStatus(index, 'calendar-close-period');
            }
        },
        async openGLPeriod(index) {
            let dialog_prop = {
                title: 'Warning',
                message: this.open_message,
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.togglePeriodStatus(index, 'calendar-gl-open-period');
            }
        },
        async closeGLPeriod(index) {
            let dialog_prop = {
                title: 'Warning',
                message: this.close_message,
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.togglePeriodStatus(index, 'calendar-gl-close-period');
            }
        },
        togglePeriodStatus: function (index, url) {
            let calendar_period_data = this.calendar_period_list[index];
            let calendar_id = calendar_period_data.calendar_id;
            let calendar_year = calendar_period_data.calendar_year;
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('calendar_id', calendar_id);
            form_data.append('calendar_year', calendar_year);
            form_data.append('no_load', true);
            let api_url;
            if (this.isPropertyFormLive()) {
                //get data from live
                api_url = 'property/update/' + url;
            } else {
                api_url = 'temp/property/update/' + url;
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.error_server_msg2 = response.data.error_server_msg2;
                console.log(this.error_server_msg2.length);
                if (this.error_server_msg2.length === 0) {
                    this.loadPropertyCalendarPeriod(calendar_year);
                    this.loading_setting = false;
                }
            });
        },
        async toggleUpPeriod(index) {
            let calendar_period_data = this.calendar_period_list[index];
            let calendar_id = calendar_period_data.calendar_id;
            let calendar_closed_flag = calendar_period_data.calendar_closed_flag;
            let calendar_gl_closed_flag = calendar_period_data.calendar_gl_closed_flag;
            let message = this.close_message;
            if (calendar_closed_flag) {
                message = this.open_message;
            }
            let dialog_prop = {
                title: 'Warning',
                message: message,
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                if (calendar_closed_flag) {
                    this.togglePeriodStatus(index, 'calendar-open-up-period');
                } else {
                    this.togglePeriodStatus(index, 'calendar-close-up-period');
                }
            }
        },
        async toggleDownPeriod(index) {
            let calendar_period_data = this.calendar_period_list[index];
            let calendar_id = calendar_period_data.calendar_id;
            let calendar_closed_flag = calendar_period_data.calendar_closed_flag;
            let calendar_gl_closed_flag = calendar_period_data.calendar_gl_closed_flag;
            let message = this.close_message;
            if (calendar_closed_flag) {
                message = this.open_message;
            }
            let dialog_prop = {
                title: 'Warning',
                message: message,
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                if (calendar_closed_flag) {
                    this.togglePeriodStatus(index, 'calendar-open-down-period');
                } else {
                    this.togglePeriodStatus(index, 'calendar-close-down-period');
                }
            }
        },
        async toggleUpGLPeriod(index) {
            let calendar_period_data = this.calendar_period_list[index];
            let calendar_id = calendar_period_data.calendar_id;
            let calendar_closed_flag = calendar_period_data.calendar_closed_flag;
            let calendar_gl_closed_flag = calendar_period_data.calendar_gl_closed_flag;
            let message = this.close_message;
            if (calendar_gl_closed_flag) {
                message = this.open_message;
            }
            let dialog_prop = {
                title: 'Warning',
                message: message,
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                if (calendar_gl_closed_flag) {
                    this.togglePeriodStatus(index, 'calendar-gl-open-up-period');
                } else {
                    this.togglePeriodStatus(index, 'calendar-gl-close-up-period');
                }
            }
        },
        async toggleDownGLPeriod(index) {
            let calendar_period_data = this.calendar_period_list[index];
            let calendar_id = calendar_period_data.calendar_id;
            let calendar_closed_flag = calendar_period_data.calendar_closed_flag;
            let calendar_gl_closed_flag = calendar_period_data.calendar_gl_closed_flag;
            let message = this.close_message;
            if (calendar_gl_closed_flag) {
                message = this.open_message;
            }
            let dialog_prop = {
                title: 'Warning',
                message: message,
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                if (calendar_gl_closed_flag) {
                    this.togglePeriodStatus(index, 'calendar-gl-open-down-period');
                } else {
                    this.togglePeriodStatus(index, 'calendar-gl-close-down-period');
                }
            }
        },
        updatePeriod: function (index) {
            let calendar_period_data = this.calendar_period_list[index];
            let calendar_id = calendar_period_data.calendar_id;
            let calendar_year = calendar_period_data.calendar_year;
            let calendar_start_date = calendar_period_data.calendar_start_date;
            let calendar_end_date = calendar_period_data.calendar_end_date;

            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('calendar_id', calendar_id);
            form_data.append('calendar_year', calendar_year);
            form_data.append('calendar_start_date', calendar_start_date);
            form_data.append('calendar_end_date', calendar_end_date);
            form_data.append('no_load', true);
            let api_url;
            if (this.isPropertyFormLive()) {
                //get data from live
                api_url = 'property/update/calendar-period';
            } else {
                api_url = 'temp/property/update/calendar-period';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.loadPropertyCalendarPeriod(calendar_year);
                this.loading_setting = false;
            });
        },
        modalAddCalendar: function () {
            this.error_server_msg2 = [];
            this.show_add_calendar = true;
        },
        loadParameters: function () {
            // master_calendar_list
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let api_url = 'parameter/fetch/property-calendar';
            this.$api.post(api_url, form_data).then((response) => {
                this.master_calendar_list = response.data.master_calendar_list;
                this.calendar_list = response.data.calendar_list;
            });
        },
        selectYear: function (row) {
            this.loadPropertyCalendarPeriod(row.calendar_year);
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
            }
        },
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
        ...mapGetters(['getDDCountryStates']),
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.historical_calendar_headers = [
                { text: '#', value: 'index', sortable: false, width: 50 },
                { text: 'Year', value: 'calendar_year', width: 100 },
                { text: 'Period', value: 'calendar_period_ctr', align: 'center', width: 100 },
                { text: 'Start Date', value: 'calendar_start_date_raw', width: 287 },
                { text: 'End Date', value: 'calendar_end_date_raw', width: 287 },
                { text: 'Year Status', value: 'closed', align: 'end', sortable: false },
            ];
            this.calendar_period_headers = [
                { text: '#', value: 'index', sortable: false, width: 50 },
                { text: 'Year', value: 'calendar_year', width: 100 },
                { text: 'Period', value: 'calendar_period', align: 'center', width: 100 },
                { text: 'Start Date', value: 'calendar_start_date_raw', width: 287 },
                { text: 'End Date', value: 'calendar_end_date_raw', width: 287 },
                {
                    text: 'Period Status',
                    value: 'period_status_action',
                    align: 'center',
                    sortable: false,
                    width: 'auto',
                },
                {
                    text: 'GL Period Status',
                    value: 'gl_period_status_action',
                    align: 'center',
                    sortable: false,
                    width: 'auto',
                },
            ];
        }
    },
    created() {
        bus.$on('loadPropertyCalendarFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
