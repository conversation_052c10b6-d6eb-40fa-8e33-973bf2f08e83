<style>
.icon.indemnity-limit-field-icon {
    font-size: 12px;
    top: 10px !important;
    left: 0px !important;
    font-weight: 500;
}
</style>
<template>
    <div
        v-on:dblclick="doubleClickForm()"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Property Insurance</h6>
                <v-spacer></v-spacer>
                <h6
                    class="title font-weight-black"
                    style="padding-right: 3px"
                    v-if="isPropertyFormLive() && cms_country == 'AU'"
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                >
                    Get an insurance quote with
                </h6>
                <v-btn
                    @click="show_kbi_modal = true"
                    small
                    color="normal"
                    v-if="isPropertyFormLive() && cms_country == 'AU'"
                    v-show="
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                >
                    <img
                        :src="asset_domain + 'assets/images/kbi-logo.svg'"
                        class="icon"
                        width="25"
                        height="25"
                    />
                </v-btn>
                <cirrus-input
                    inputFormat="search"
                    v-if="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>

                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)
                    "
                    v-if="edit_form && !new_property && property_insurance_list.length > 0"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-col
                    class="text-center"
                    v-if="property_insurance_list.length === 0"
                    v-show="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-btn
                        v-if="!pmro_read_only"
                        depressed
                        small
                        color="success"
                        @click="modalAddData()"
                        >Add Insurance</v-btn
                    >
                    <div
                        style="margin: 10px 0px"
                        v-else
                    >
                        No property insurances at the moment
                    </div>
                </v-col>
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="property_insurance_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="property_insurance_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ property_insurance_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_type_description="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_type_description }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_insurer="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_insurer }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_premium_amount="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><span
                                    v-if="
                                        item.insurance_premium_amount !== '' && item.insurance_premium_amount !== null
                                    "
                                    >{{ currency_symbol }}</span
                                >{{
                                    accountingAmountFormat(numberWithCommas(roundTo(item.insurance_premium_amount, 2)))
                                }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.insurance_policy_number="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_policy_number }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_start_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_start_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_expiry_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_expiry_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_paid_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_paid_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_notes="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_notes }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_file="{ item }">
                        <cirrus-single-upload-button2
                            :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                            v-model="item.insurance_file"
                            :size_limit="20"
                            :has_saved_file="
                                item.insurance_file_old !== '' &&
                                (typeof item.insurance_file_old === 'string' ||
                                    item.insurance_file_old instanceof String)
                                    ? true
                                    : false
                            "
                            :edit_form="false"
                            :error_msg="error_msg"
                            accept_type="pdf"
                        ></cirrus-single-upload-button2>
                    </template>
                    <template v-slot:item.insurance_diarise="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><label>{{ item.insurance_diarise_id ? '' : 'Not ' }}Diarised</label></span
                            >
                        </div>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenAED(property_insurance_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit
                        </v-icon>
                        <v-icon
                            color="red"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="deleteInsurance(property_insurance_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="property_insurance_list.length > 5"
                    v-if="!formSectionReadOnly(pm_property_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :form_section="form_section"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteInsurance(property_insurance_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Insurance Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--property add-->
                    <div :key="property_insurance_arr.index_id">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        property_insurance_arr.index === 'New'
                                            ? property_insurance_arr.index
                                            : property_insurance_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Insurance Type' : false"
                                        v-model="property_insurance_arr.insurance_type"
                                        :options="property_insurance_type_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        group-label="language"
                                        placeholder="Select a insurance type"
                                        track-by="field_key"
                                        label="field_value"
                                        :edit_form="true"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Insurer</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="property_insurance_arr.insurance_insurer"
                                        size=""
                                        :id="'insurance_insurer'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Insurer' : false"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Premium Amount</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <div
                                        class="ui left icon input"
                                        v-if="true"
                                    >
                                        <cirrus-input
                                            v-model="property_insurance_arr.insurance_premium_amount"
                                            :inputFormat="'dollar'"
                                            size=""
                                            :id="'insurancePolicyAmount'"
                                            data-inverted=""
                                            :data-tooltip="edit_form ? 'Premium Amount' : false"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                        <i class="icon indemnity-limit-field-icon">{{ currency_symbol }}</i>
                                    </div>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Policy Number</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="property_insurance_arr.insurance_policy_number"
                                        size=""
                                        :id="'insurance_policy_number'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Policy No.' : false"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Start Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'property_insurance_start_date' + String(Math.random()).replace('.', '')"
                                        v-model="property_insurance_arr.insurance_start_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Start Date' : false"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Expiry Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'property_insurance_expiry_date' + String(Math.random()).replace('.', '')"
                                        v-model="property_insurance_arr.insurance_expiry_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Expiry Date' : false"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Paid Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'property_insurance_paid_date' + String(Math.random()).replace('.', '')"
                                        v-model="property_insurance_arr.insurance_paid_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Paid Date' : false"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Note</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="property_insurance_arr.insurance_notes"
                                        size=""
                                        :id="'insurance_notes'"
                                        data-inverted=""
                                        :data-tooltip="edit_form ? 'Notes' : false"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >File(s)</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-upload-button2
                                        :id="'property_insurance_file_' + property_insurance_arr.index"
                                        v-model="property_insurance_arr.insurance_file"
                                        :size_limit="20"
                                        :has_saved_file="
                                            property_insurance_arr.insurance_file_old !== '' &&
                                            (typeof property_insurance_arr.insurance_file_old === 'string' ||
                                                property_insurance_arr.insurance_file_old instanceof String)
                                                ? true
                                                : false
                                        "
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                        accept_type="pdf"
                                    ></cirrus-single-upload-button2>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Diarise</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <div v-bind:class="property_insurance_arr.insurance_diarise_id ? 'hidden' : ''">
                                        <sui-checkbox
                                            v-model="property_insurance_arr.insurance_diarise"
                                            data-inverted=""
                                            :data-tooltip="edit_form ? 'Diarise' : false"
                                        />
                                    </div>
                                    <label
                                        class="form-input-text"
                                        v-bind:class="!property_insurance_arr.insurance_diarise_id ? 'hidden' : ''"
                                        >{{ property_insurance_arr.insurance_diarise_id ? '' : 'Not ' }}Diarised</label
                                    >
                                    <input
                                        type="hidden"
                                        v-model="property_insurance_arr.insurance_diarise"
                                        :id="'insurance_diarise'"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    />
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="property_insurance_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="property_insurance_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteInsurance(property_insurance_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="property_insurance_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_kbi_modal"
            max-width="700"
            content-class="c8-page"
            scrollable
        >
            <kbi-form-section :property_code="property_code" />
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import KBIFormModal from '../../InsuranceExtra/Forms/KBIFormModal.vue';

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    components: {
        'kbi-form-section': KBIFormModal,
    },
    data() {
        return {
            asset_domain: this.$assetDomain,
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_INSURANCE',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            property_insurance_list: [],
            property_insurance_list_old: [],
            property_insurance_type_list: [],
            readonly: this.read_only,
            show_activity_log_modal: false,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Type', value: 'insurance_type_description', sortable: false, width: '10%' },
                { text: 'Insurer', value: 'insurance_insurer', sortable: false },
                {
                    text: 'Premium Limit',
                    value: 'insurance_premium_amount',
                    align: 'end',
                    sortable: false,
                    width: '10%',
                },
                { text: 'Policy No.', value: 'insurance_policy_number', sortable: false, width: '10%' },
                { text: 'Start Date', value: 'insurance_start_date_raw', width: '8%' },
                { text: 'Exp. Date', value: 'insurance_expiry_date_raw', width: '8%' },
                { text: 'Paid Date', value: 'insurance_paid_date_raw', width: '8%' },
                { text: 'Note', value: 'insurance_notes', width: '10%' },
                { text: 'File(s)', value: 'insurance_file', sortable: false, width: '8%' },
                { text: 'Diarise', value: 'insurance_diarise', sortable: false, width: '8%' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            show_kbi_modal: false,
            AED_modal: false,
            property_insurance_arr: [],
            modal_current_ctr: 0,
            success_flag: false,
            cms_country: 'AU',
            currency_symbol: '$',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        // console.log('aa');
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                { text: 'Type', value: 'insurance_type_description', sortable: false, width: 'auto' },
                { text: 'Insurer', value: 'insurance_insurer', sortable: false, width: 'auto' },
                {
                    text: 'Premium Limit',
                    value: 'insurance_premium_amount',
                    align: 'end',
                    sortable: false,
                    width: 'auto',
                },
                { text: 'Policy No.', value: 'insurance_policy_number', sortable: false, width: 'auto' },
                { text: 'Start Date', value: 'insurance_start_date_raw', width: 'auto' },
                { text: 'Exp. Date', value: 'insurance_expiry_date_raw', width: 'auto' },
                { text: 'Paid Date', value: 'insurance_paid_date_raw', width: 'auto' },
                { text: 'Note', value: 'insurance_notes', width: 'auto' },
                { text: 'File(s)', value: 'insurance_file', sortable: false, width: 'auto' },
                { text: 'Diarise', value: 'insurance_diarise', sortable: false, width: 'auto' },
            ];
            this.items_per_page = 9999;
        }
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_property_form_read_only',
            'auto_diarise',
        ]),
    },
    methods: {
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadParameters: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('no_load', true);
            let apiUrl = 'parameter/fetch/property-insurance';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.property_insurance_type_list = response.data.property_insurance_type_list;
            });
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '' || this.forceLoad) {
                // console.log('load property property details');
                this.loadPropertyInsurance();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.property_insurance_list = [];
            this.property_insurance_list = JSON.parse(JSON.stringify(this.property_insurance_list_old));
        },
        modalAddData: function () {
            this.edit_form = true;
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.property_insurance_arr = [];
            let new_object = {
                index_id: Math.random(),
                index: 'New',
                insurance_id: '',
                insurance_type: { field_key: '', field_value: 'Please select ...' },
                insurance_insurer: '',
                insurance_premium_amount: '',
                insurance_policy_number: '',
                insurance_start_date: null,
                insurance_expiry_date: null,
                insurance_paid_date: null,
                insurance_notes: '',
                insurance_file: null,
                insurance_file_old: null,
                insurance_diarise_id: '',
                status: 'new',
                insurance_diarise: this.auto_diarise,
            };
            this.property_insurance_arr = this.objectClone(new_object);
        },
        loadPropertyInsurance: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';

            if (this.isPropertyFormLive()) apiUrl = 'property/fetch/insurance';
            else apiUrl = 'temp/property/fetch/insurance';

            //  apiUrl = 'property/loadTempLeaseInsurance';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.property_insurance_list = response.data.property_insurance_list;
                this.property_insurance_list_old = response.data.property_insurance_list;
                // this.property_insurance_type_list = response.data.property_insurance_type_list;
                if (
                    this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.items_per_page = this.property_insurance_list.length;
                }
                this.cms_country = response.data.cms_country;
                this.currency_symbol = response.data.currency_symbol;
                this.loading_setting = false;
            });
            if (this.new_property) this.edit_form = true;
            else this.edit_form = false;
        },
        modalSubmitData: function () {
            let errorArr = [];
            let insurance_type = this.property_insurance_arr.insurance_type.field_key;
            let insurance_insurer = this.property_insurance_arr.insurance_insurer;
            let insurance_policy_number = this.property_insurance_arr.insurance_policy_number;
            let insurance_expiry_date = this.property_insurance_arr.insurance_expiry_date;
            let insurance_premium_amount = this.property_insurance_arr.insurance_premium_amount;

            if (insurance_type === '') errorArr.push(['You have not entered a valid policy type for the insurance.']);
            if (insurance_insurer === '') errorArr.push(['You have not entered a valid insurer for the insurance.']);
            if (insurance_policy_number === '')
                errorArr.push(['You have not entered a valid policy no for the insurance.']);
            if (insurance_expiry_date === '' || insurance_expiry_date === null)
                errorArr.push(['You have not entered a valid expiry date for the insurance.']);
            if (insurance_premium_amount !== '')
                if (isNaN(parseFloat(insurance_premium_amount)))
                    errorArr.push(['You have not entered a valid Premium Amount.']);

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                let save_arr = [];
                save_arr[0] = this.property_insurance_arr;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('property_insurance_list', JSON.stringify(save_arr));

                let insurance_file = [];
                if (this.property_insurance_arr.insurance_file) {
                    insurance_file = this.property_insurance_arr.insurance_file[0];
                    form_data.append('property_insurance_file', insurance_file);
                }
                let apiUrl = '';
                if (this.isPropertyFormLive()) apiUrl = 'with-file-upload/property/update-or-create/insurance';
                else apiUrl = 'with-file-upload/temp/property/update-or-create/insurance';
                // apiUrl = 'property/saveTempLeaseInsurance';
                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.loading_setting = false;
                        this.error_server_msg2 = response.data.error_server_msg2;
                        if (this.error_server_msg2.length === 0) {
                            if (response.data.diary == 1) bus.$emit('loadPropertyDiaryFormSection', '');
                            this.property_insurance_list = [];
                            this.loadForm();
                            if (!this.new_property) this.edit_form = false;
                            if (this.property_insurance_arr.status === 'new') {
                                this.property_insurance_arr.index = this.property_insurance_list.length;
                                this.property_insurance_arr.insurance_id = response.data.insurance_id;
                                this.property_insurance_arr.insurance_file = response.data.insurance_file;
                                this.property_insurance_arr.insurance_file_old = response.data.insurance_file;
                                this.property_insurance_arr.status = 'saved';
                            }
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        }
                    });
            }
        },
        async deleteInsurance(index) {
            if (index !== 'New') {
                let status = this.property_insurance_list[index].status;
                let insurance_id = this.property_insurance_list[index].insurance_id;

                if (status === 'new') {
                    this.property_insurance_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('insurance_id', insurance_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isPropertyFormLive()) {
                            apiUrl = 'property/delete/insurance';
                        } else {
                            apiUrl = 'temp/property/delete/insurance';
                        }
                        //  apiUrl = 'property/deleteTempLeaseInsurance';
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.property_insurance_list.splice(index, 1);
                            this.loading_setting = false;

                            this.property_insurance_list_old = JSON.parse(JSON.stringify(this.property_insurance_list));
                        });
                    }
                }
            }
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadInsurance_' + id;
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        modalPrevData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.property_insurance_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.property_insurance_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.property_insurance_arr = this.objectClone(this.property_insurance_list[this.modal_current_ctr]);
            this.property_insurance_arr.index = this.modal_current_ctr;
            // console.log(this.property_insurance_arr.insurance_file);
            // console.log(this.property_insurance_arr.insurance_file_old);
            // let check = this.property_insurance_arr.insurance_file === this.property_insurance_arr.insurance_file_old;
            // bus.$emit("refreshSingleUploadComponent","");
        },
        modalNextData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.property_insurance_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.property_insurance_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.property_insurance_arr = this.objectClone(this.property_insurance_list[this.modal_current_ctr]);
            this.property_insurance_arr.index = this.modal_current_ctr;
            // console.log(this.property_insurance_arr.index);
            // console.log(this.property_insurance_arr.insurance_file);
            // console.log(this.property_insurance_arr.insurance_file_old);
            // let check = this.property_insurance_arr.insurance_file === this.property_insurance_arr.insurance_file_old;
            // bus.$emit("refreshSingleUploadComponent","");
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.property_insurance_arr = this.objectClone(this.property_insurance_list[index]);
            this.property_insurance_arr.index = index;
            this.modal_current_ctr = index;
            bus.$emit('refreshSingleUploadComponent', '');
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.loadParameters();
            }
        },
    },
    created() {
        bus.$on('loadPropertyInsuranceSection', (data) => {
            this.loadForm();
        });
        bus.$on('closeKBIModal', (data) => {
            this.show_kbi_modal = false;
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
