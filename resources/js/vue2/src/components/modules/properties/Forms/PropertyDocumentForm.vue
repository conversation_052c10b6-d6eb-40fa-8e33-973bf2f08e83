<style>
.file-description-textbox {
    min-width: 300px !important;
}

.documents-table {
    border: 1px solid rgba(34, 36, 38, 0.15);
}

td.text-start,
td.text-end {
    padding-top: 0.4em !important;
    padding-bottom: 0.4em !important;
}

.documents-table .option {
    font-size: 21px !important;
}

ul.v-pagination button {
    height: 30px !important;
    min-width: 30px !important;
    min-height: 30px !important;
}

.documents-table button.v-icon {
    font-size: 21px;
}
</style>
<template>
    <div
        v-on:dblclick="doubleClickForm()"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Documents</h6>
                &nbsp
                <v-btn-toggle
                    v-if="sys_ver_control_list.isFolderSystemOn"
                    class="form-toggle"
                    v-model="doc_active_version"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                        @click="setActiveVersion(0)"
                    >
                        Version 1
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                        @click="setActiveVersion(1)"
                    >
                        Beta
                    </v-btn>
                </v-btn-toggle>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="!readonly && isEditable() && !pmro_read_only"
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!readonly && isEditable() && !pmro_read_only"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    v-if="edit_form && !new_property"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!readonly && isEditable()"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isPropertyFormLive()"
                    @click="showPropertyActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-col
                    class="text-center"
                    v-if="property_documents_list.length === 0"
                    v-show="isEditable()"
                >
                    <v-btn
                        v-if="!pmro_read_only"
                        depressed
                        small
                        color="success"
                        @click="modalAddData()"
                        >Add Document
                    </v-btn>
                    <div
                        style="margin: 10px 0px"
                        v-else
                    >
                        No property documents at the moment
                    </div>
                </v-col>
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom documents-table"
                    v-show="property_documents_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="property_documents_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.index="{ item }">
                        {{ property_documents_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.document_title="{ item }">
                        <cirrus-input
                            custom_class="cirrus-input-table-textbox"
                            v-model="item.document_title"
                            size=""
                            data-inverted=""
                            :data-tooltip="edit_form ? 'Document Title' : false"
                            :edit_form="false"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </template>
                    <template v-slot:item.document_description="{ item }">
                        <cirrus-input
                            custom_class="file-description-textbox"
                            v-model="item.document_description"
                            size=""
                            data-inverted=""
                            :data-tooltip="edit_form ? 'Description' : false"
                            :edit_form="false"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </template>
                    <template v-slot:item.document_period_date_raw="{ item }">
                        <cirrus-icon-date-picker
                            :size="'40'"
                            :id="'document_period_date' + Math.random()"
                            :key="Math.random()"
                            v-model="item.document_period_date"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                    </template>
                    <template v-slot:item.document_reporting_period_raw="{ item }">
                        <span class="form-input-text">{{ item.document_reporting_period }}</span>
                    </template>
                    <template v-slot:item.enable_external_link="{ item }">
                        <table v-if="item.is_external != 1 && item.on_s3 != 1">
                            <tbody>
                                <tr v-if="!item.enable_external_link">
                                    <td style="margin: 0; padding: 0">
                                        <cirrus-single-upload-button2
                                            :withLinkUploader="true"
                                            :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                            v-model="item.filename"
                                            :has_saved_file="
                                                item.filename_old !== '' &&
                                                (typeof item.filename_old === 'string' ||
                                                    item.filename_old instanceof String)
                                                    ? true
                                                    : false
                                            "
                                            :edit_form="edit_form"
                                            accept_type="pdf"
                                            :error_msg="error_msg"
                                            :size_limit="20"
                                        ></cirrus-single-upload-button2>
                                    </td>
                                    <td
                                        style="margin: 0; padding: 0"
                                        v-if="edit_form && !item.filename"
                                    >
                                        <span>or</span>
                                    </td>
                                    <td
                                        style="margin: 0; padding: 0"
                                        v-if="edit_form && !item.filename"
                                    >
                                        <a
                                            href="#"
                                            @click="item.enable_external_link = 1"
                                            ><img
                                                :src="asset_domain + 'assets/images/icons/link_icon_blue.png'"
                                                class="icon"
                                                style="width: 19px"
                                        /></a>
                                    </td>
                                </tr>
                                <tr v-if="item.enable_external_link == 1">
                                    <td style="margin: 0; padding: 0">
                                        <cirrus-input
                                            custom_class="cirrus-input-table-textbox"
                                            maxlength="255"
                                            v-model="item.external_url"
                                            size=""
                                            :edit_form="edit_form"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                        <v-btn
                                            :edit_form="edit_form"
                                            x-small
                                            class=""
                                            @click="
                                                item.enable_external_link = 0;
                                                item.external_url = '';
                                            "
                                            >Cancel
                                        </v-btn>
                                        <input
                                            type="hidden"
                                            v-model="item.enable_external_link"
                                            :edit_form="edit_form"
                                            :error_msg="error_msg"
                                        />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <a
                            target="_blank"
                            v-if="item.is_external == 1"
                            :href="item.external_url"
                            ><img
                                :src="asset_domain + 'assets/images/icons/link_icon_blue.png'"
                                alt="Adobe Logo"
                                class="icon"
                                style="width: 20px"
                            />&nbsp; Download</a
                        >
                        <a
                            target="_blank"
                            v-if="item.on_s3 == 1"
                            v-on:click="downloadS3File(item.s3_filepath, item.s3_filename, 'pdf')"
                            ><img
                                :src="asset_domain + 'assets/images/icons/pdf.png'"
                                alt="Adobe Logo"
                                class="icon"
                                style="width: 19px"
                            />&nbsp; Download</a
                        >
                    </template>
                    <template v-slot:item.publish_to_owner="{ item }">
                        <div v-if="edit_form">
                            <sui-checkbox
                                v-model="item.publish_to_owner"
                                data-inverted=""
                                :data-tooltip="edit_form ? 'Publish to Owner' : false"
                            />
                        </div>
                        <span v-if="item.publish_to_owner && !edit_form">Published to owner</span>
                    </template>
                    <template v-slot:item.include_owner_report="{ item }">
                        <div v-if="edit_form">
                            <sui-checkbox
                                v-model="item.include_owner_report"
                                data-inverted=""
                                :data-tooltip="edit_form ? 'Include with Owner Reports' : false"
                            />
                        </div>
                        <span v-if="item.include_owner_report && !edit_form">Included to owner reports</span>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-btn
                            x-small
                            color="success"
                            v-if="edit_form"
                            @click="updateDocuments(property_documents_list.indexOf(item))"
                            >Update
                        </v-btn>
                        <v-icon
                            color="red"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="deleteDocuments(property_documents_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="property_documents_list.length > 5"
                    v-if="isEditable()"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->

                <!--   AED modal      -->
                <v-dialog
                    v-model="AED_modal"
                    max-width="1000"
                    content-class="c8-page"
                    @keydown.ctrl.enter="modalSubmitData()"
                >
                    <v-card>
                        <v-card-title class="headline">
                            Document Information
                            <a
                                href="#"
                                class="dialog-close"
                                @click.prevent="AED_modal = false"
                            >
                                <v-icon>mdi-close</v-icon>
                            </a>
                        </v-card-title>
                        <v-card-text>
                            <cirrus-server-error
                                :error_msg="error_server_msg"
                                :errorMsg2="error_server_msg2"
                            ></cirrus-server-error>
                            <v-alert
                                type="success"
                                dense
                                tile
                                text
                                v-if="success_flag"
                            >
                                Successfully Saved
                            </v-alert>
                            <!--Lease add-->
                            <div :key="property_documents_arr.index">
                                <div class="page-form">
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="form-label"
                                            >#
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                {{ property_documents_arr.index }}
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="form-label required"
                                            >Title:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="form-input"
                                        >
                                            <cirrus-input
                                                custom_class="cirrus-input-table-textbox"
                                                v-model="property_documents_arr.document_title"
                                                maxlength="50"
                                                :id="'key_id' + property_documents_arr.index"
                                                data-inverted=""
                                                :data-tooltip="'Document Title'"
                                                :edit_form="true"
                                            ></cirrus-input>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="form-label required"
                                            >Description:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="form-input"
                                        >
                                            <cirrus-input
                                                custom_class="cirrus-input-table-textbox"
                                                v-model="property_documents_arr.document_description"
                                                size=""
                                                :id="'key_id' + property_documents_arr.index"
                                                data-inverted=""
                                                :data-tooltip="'Document Description'"
                                                :edit_form="true"
                                            ></cirrus-input>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="form-label"
                                            >Period Date:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="form-input"
                                        >
                                            <cirrus-icon-date-picker
                                                :size="'40'"
                                                :id="'document_period_date' + property_documents_arr.index"
                                                v-model="property_documents_arr.document_period_date"
                                                :edit_form="true"
                                                :error_msg="error_msg"
                                            ></cirrus-icon-date-picker>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="form-label required"
                                            >File:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="form-input"
                                        >
                                            <table v-if="property_documents_arr.is_external != 1">
                                                <tbody>
                                                    <tr v-if="!property_documents_arr.enable_external_link">
                                                        <td style="margin: 0; padding: 0">
                                                            <cirrus-single-upload-button2
                                                                :withLinkUploader="true"
                                                                :id="
                                                                    getIdOfUploadButton(
                                                                        new Date().getTime() + Math.random(),
                                                                    )
                                                                "
                                                                v-model="property_documents_arr.filename"
                                                                :has_saved_file="
                                                                    property_documents_arr.filename_old !== '' &&
                                                                    (typeof property_documents_arr.filename_old ===
                                                                        'string' ||
                                                                        property_documents_arr.filename_old instanceof
                                                                            String)
                                                                        ? true
                                                                        : false
                                                                "
                                                                :edit_form="true"
                                                                accept_type="pdf"
                                                                :error_msg="error_msg"
                                                                :size_limit="20"
                                                            ></cirrus-single-upload-button2>
                                                        </td>
                                                        <td
                                                            style="margin: 0; padding: 0"
                                                            v-if="!property_documents_arr.filename"
                                                        >
                                                            <span>or</span>
                                                        </td>
                                                        <td
                                                            style="margin: 0; padding: 0"
                                                            v-if="!property_documents_arr.filename"
                                                        >
                                                            <a
                                                                href="#"
                                                                @click="property_documents_arr.enable_external_link = 1"
                                                                ><img
                                                                    :src="
                                                                        asset_domain +
                                                                        'assets/images/icons/link_icon_blue.png'
                                                                    "
                                                                    class="icon"
                                                                    style="width: 19px"
                                                            /></a>
                                                        </td>
                                                    </tr>
                                                    <tr v-if="property_documents_arr.enable_external_link == 1">
                                                        <td style="margin: 0; padding: 0">
                                                            <cirrus-input
                                                                custom_class="cirrus-input-table-textbox"
                                                                maxlength="255"
                                                                v-model="property_documents_arr.external_url"
                                                                size=""
                                                                :edit_form="true"
                                                                :error_msg="error_msg"
                                                            ></cirrus-input>
                                                            <v-btn
                                                                :edit_form="true"
                                                                x-small
                                                                class=""
                                                                @click="
                                                                    property_documents_arr.enable_external_link = 0;
                                                                    property_documents_arr.external_url = '';
                                                                "
                                                                >Cancel
                                                            </v-btn>
                                                            <input
                                                                type="hidden"
                                                                v-model="property_documents_arr.enable_external_link"
                                                                :edit_form="true"
                                                                :error_msg="error_msg"
                                                            />
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="form-label"
                                            >Publish to Owner:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                <sui-checkbox
                                                    v-model="property_documents_arr.publish_to_owner"
                                                    data-inverted=""
                                                    :data-tooltip="edit_form ? 'Publish to Owner' : false"
                                                />
                                            </span>
                                        </v-col>
                                    </v-row>
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="form-label"
                                            >Include with Owner Reports:
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="form-input"
                                        >
                                            <span class="form-input-text">
                                                <sui-checkbox
                                                    v-model="property_documents_arr.include_owner_report"
                                                    data-inverted=""
                                                    :data-tooltip="edit_form ? 'Include with Owner Reports' : false"
                                                />
                                            </span>
                                        </v-col>
                                    </v-row>

                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="2"
                                            md="2"
                                            class="form-label"
                                        ></v-col>
                                        <v-col
                                            xs="12"
                                            sm="10"
                                            md="10"
                                            class="form-input"
                                        >
                                        </v-col>
                                    </v-row>
                                </div>
                            </div>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer />
                            <v-btn
                                class="v-step-save-2-button"
                                @click="modalSubmitData()"
                                data-tooltip="CTR + ENTER"
                                color="success"
                                dark
                                depressed
                                small
                            >
                                <v-icon
                                    left
                                    dark
                                    size="18"
                                    >check
                                </v-icon>
                                Save
                            </v-btn>
                            <v-btn
                                class="v-step-save-2-button"
                                @click="modalAddData()"
                                v-if="property_documents_arr.index === 'New'"
                                data-tooltip="CTR + ENTER"
                                color="warning"
                                dark
                                depressed
                                small
                            >
                                <v-icon
                                    left
                                    dark
                                    size="18"
                                    >clear_all
                                </v-icon>
                                Clear
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
                <!--   AED modal      -->

                <v-dialog
                    v-model="show_activity_log_modal"
                    max-width="1000"
                    content-class="c8-page"
                >
                    <v-card>
                        <v-card-title class="headline">
                            Activity Log
                            <a
                                href="#"
                                class="dialog-close"
                                @click.prevent="show_activity_log_modal = false"
                            >
                                <v-icon>mdi-close</v-icon>
                            </a>
                        </v-card-title>
                        <v-card-text>
                            <property-activity-log-component
                                v-if="show_activity_log_modal"
                                :property_code="property_code"
                                :form_section="form_section"
                            ></property-activity-log-component>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer />
                            <v-btn
                                color="primary"
                                depressed
                                small
                                @click="show_activity_log_modal = false"
                            >
                                <v-icon
                                    left
                                    dark
                                    size="18"
                                    >mdi-close
                                </v-icon>
                                Close
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
            </div>
        </div>
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        new_property: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'PROPERTY',
            form_section: 'PROPERTY_DOCUMENTS',
            asset_domain: this.$assetDomain,
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            AED_modal: false,
            property_documents_list: [],
            property_documents_arr: [],
            property_documents_list_old: [],
            readonly: this.read_only,
            show_activity_log_modal: false,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Document Title', value: 'document_title' },
                { text: 'Description', value: 'document_description' },
                { text: 'Period Date', value: 'document_period_date_raw' },
                { text: 'Reporting Period', value: 'document_reporting_period_raw', sortable: false },
                { text: 'File', value: 'enable_external_link', sortable: false },
                { text: '', value: 'publish_to_owner', sortable: false },
                { text: '', value: 'include_owner_report', sortable: false },
                { text: '', value: 'action1', align: 'end', sortable: false },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            filename: '',
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loading_setting = false;
        this.loadForm();
        // console.log('aa');
        if (this.edit_flag) {
            this.edit_form = true;
        }
        if (this.new_property) {
            this.edit_form = true;
        }
        if (this.isPropertyInPrintView()) {
            this.headers = [
                { text: '#', value: 'index', sortable: false, width: 'auto' },
                { text: 'ID', value: 'item_no', sortable: false, width: 'auto' },
                { text: 'Document Title', value: 'document_title', width: 'auto' },
                { text: 'Description', value: 'document_description', width: 'auto' },
                { text: 'Period Date', value: 'document_period_date_raw', width: 'auto' },
                { text: 'Reporting Period', value: 'document_reporting_period_raw', sortable: false, width: 'auto' },
                { text: 'File', value: 'enable_external_link', sortable: false, width: 'auto' },
                { text: '', value: 'publish_to_owner', sortable: false, width: 'auto' },
                { text: '', value: 'include_owner_report', sortable: false, width: 'auto' },
            ];
            this.items_per_page = 9999;
        } else {
            this.$api
                .post('ui/fetch/param-document-view-number-setup', { form: 'PROPERTY', no_load: true })
                .then((response) => {
                    this.items_per_page = response.data.param_value;
                });
        }
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_property_form_read_only',
            'doc_active_version',
            'sys_ver_control_list',
        ]),
    },
    methods: {
        ...mapMutations(['SET_DOC_ACTIVE_VERSION']),
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                )
                    this.edit_form = true;
                else this.edit_form = false;
            }
        },
        isEditable: function () {
            if (this.new_property) {
                return true;
            } else {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.form_type,
                    this.form_section,
                    this.is_inactive,
                )
                    ? true
                    : false;
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '' || this.forceLoad) {
                // console.log('load property property details');
                this.loadPropertyDocument();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.property_documents_list = [];
            //                console.log(this.propertyDiaryListOld);
            this.property_documents_list = JSON.parse(JSON.stringify(this.property_documents_list_old));
        },
        modalAddData: function () {
            // this.edit_form = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.AED_modal = true;
            this.property_documents_arr = {
                index: 'New',
                document_title: '',
                document_description: '',
                filename: '',
                filename_old: '',
                document_id: '',
                document_period_date: null,
                publish_to_owner: false,
                include_owner_report: false,
                status: 'new',
                is_external: 0,
                enable_external_link: 0,
                external_url: '',
            };
        },
        loadPropertyDocument: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            let apiUrl = '';

            if (this.isPropertyFormLive()) {
                apiUrl = 'property/fetch/documents';
            } else {
                apiUrl = 'temp/property/fetch/documents';
            }

            this.$api.post(apiUrl, form_data).then((response) => {
                this.property_documents_list = response.data.property_docs_list;
                this.property_documents_list_old = response.data.property_docs_list;

                if (
                    this.formSectionReadOnly(
                        this.pm_property_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.items_per_page = this.property_documents_list.length;
                }
                this.loading_setting = false;
            });
            if (this.new_property) this.edit_form = true;
            else this.edit_form = false;
        },
        modalSubmitData: function () {
            let errorArr = [];
            let document_title = this.property_documents_arr.document_title;
            let document_description = this.property_documents_arr.document_description;
            let document_period_date = this.property_documents_arr.document_period_date;
            let publish_to_owner = this.property_documents_arr.publish_to_owner;
            let include_owner_report = this.property_documents_arr.include_owner_report;
            let filename = this.property_documents_arr.filename;
            let filename_old = this.property_documents_arr.filename_old;
            let external_url = this.property_documents_arr.external_url;
            let enable_external_link = this.property_documents_arr.enable_external_link;
            if (document_title === '') errorArr.push(['You have not entered a valid title for the document.']);
            if (document_description === '')
                errorArr.push(['You have not entered a valid description for the document.']);
            if (enable_external_link === 0) {
                if (filename === '') {
                    errorArr.push(['You have not entered a valid attachment for the document.']);
                }
            } else {
                if (external_url === '') {
                    errorArr.push(['You have not entered a valid attachment for the document.']);
                }
            }

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('document_title', document_title);
                form_data.append('document_description', document_description);
                form_data.append('document_period_date', document_period_date);
                form_data.append('publish_to_owner', publish_to_owner);
                form_data.append('include_owner_report', include_owner_report);
                form_data.append('external_url', external_url);
                form_data.append('filename', filename);
                form_data.append('no_load', true);
                if (filename !== filename_old) {
                    form_data.append('docs_file', this.property_documents_arr.filename[0]);
                }
                let apiUrl;
                if (this.isPropertyFormLive()) apiUrl = 'with-file-upload/property/create/document';
                else apiUrl = 'with-file-upload/temp/property/create/document';

                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.loadForm();
                        if (!this.new_property) {
                            this.edit_form = false;
                        }
                        this.loading_setting = false;
                        this.AED_modal = false;
                    });
            }
        },
        async deleteDocuments(index) {
            let status = this.property_documents_list[index].status;
            let document_id = this.property_documents_list[index].document_id;
            if (status === 'new') {
                this.property_documents_list.splice(index, 1);
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.loading_setting = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('document_id', document_id);
                    form_data.append('no_load', true);
                    let apiUrl;
                    if (this.isPropertyFormLive()) {
                        apiUrl = 'property/delete/document';
                    } else {
                        apiUrl = 'temp/property/delete/document';
                    }
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.property_documents_list.splice(index, 1);
                        this.loading_setting = false;

                        this.property_documents_list_old = JSON.parse(JSON.stringify(this.property_documents_list));
                    });
                }
            }
        },
        setOrder: function (action, index) {
            let OldIndex = 0;
            let NewIndex = 0;

            if (action == 'updateSeqDown') {
                if (this.property_documents_list.length == index + 1) return false;

                OldIndex = index;
                NewIndex = index + 1;
            } else if (action == 'updateSeqUp') {
                if (index == 0) return false;

                OldIndex = index;
                NewIndex = index - 1;
            }

            let createdBy = this.property_documents_list[NewIndex].createdBy;
            let dateCreated = this.property_documents_list[NewIndex].dateCreated;
            let document_description = this.property_documents_list[NewIndex].document_description;
            let document_id = this.property_documents_list[NewIndex].document_id;
            let document_title = this.property_documents_list[NewIndex].document_title;
            let documentType = this.property_documents_list[NewIndex].documentType;
            let external_url = this.property_documents_list[NewIndex].external_url;
            let filename = this.property_documents_list[NewIndex].filename;
            let is_external = this.property_documents_list[NewIndex].is_external;
            let publish_to_owner = this.property_documents_list[NewIndex].publish_to_owner;
            let include_owner_report = this.property_documents_list[NewIndex].include_owner_report;
            let status = this.property_documents_list[NewIndex].status;

            this.property_documents_list[NewIndex].createdBy = this.property_documents_list[OldIndex].createdBy;
            this.property_documents_list[NewIndex].dateCreated = this.property_documents_list[OldIndex].dateCreated;
            this.property_documents_list[NewIndex].document_description =
                this.property_documents_list[OldIndex].document_description;
            this.property_documents_list[NewIndex].document_id = this.property_documents_list[OldIndex].document_id;
            this.property_documents_list[NewIndex].document_title =
                this.property_documents_list[OldIndex].document_title;
            this.property_documents_list[NewIndex].documentType = this.property_documents_list[OldIndex].documentType;
            this.property_documents_list[NewIndex].external_url = this.property_documents_list[OldIndex].external_url;
            this.property_documents_list[NewIndex].filename = this.property_documents_list[OldIndex].filename;
            this.property_documents_list[NewIndex].is_external = this.property_documents_list[OldIndex].is_external;
            this.property_documents_list[NewIndex].publish_to_owner =
                this.property_documents_list[OldIndex].publish_to_owner;
            this.property_documents_list[NewIndex].include_owner_report =
                this.property_documents_list[OldIndex].include_owner_report;
            this.property_documents_list[NewIndex].status = this.property_documents_list[OldIndex].status;

            this.property_documents_list[OldIndex].createdBy = createdBy;
            this.property_documents_list[OldIndex].dateCreated = dateCreated;
            this.property_documents_list[OldIndex].document_description = document_description;
            this.property_documents_list[OldIndex].document_id = document_id;
            this.property_documents_list[OldIndex].document_title = document_title;
            this.property_documents_list[OldIndex].documentType = documentType;
            this.property_documents_list[OldIndex].external_url = external_url;
            this.property_documents_list[OldIndex].filename = filename;
            this.property_documents_list[OldIndex].is_external = is_external;
            this.property_documents_list[OldIndex].publish_to_owner = publish_to_owner;
            this.property_documents_list[OldIndex].include_owner_report = include_owner_report;
            this.property_documents_list[OldIndex].status = status;

            // console.log( this.property_documents_list[NewIndex]);
        },
        setLast: function (action, index) {
            if (this.property_documents_list.length == index + 1) return false;

            let lastIndex = this.property_documents_list.length - 1;

            let createdBy = this.property_documents_list[index].createdBy;
            let dateCreated = this.property_documents_list[index].dateCreated;
            let document_description = this.property_documents_list[index].document_description;
            let document_id = this.property_documents_list[index].document_id;
            let document_title = this.property_documents_list[index].document_title;
            let documentType = this.property_documents_list[index].documentType;
            let external_url = this.property_documents_list[index].external_url;
            let filename = this.property_documents_list[index].filename;
            let is_external = this.property_documents_list[index].is_external;
            let publish_to_owner = this.property_documents_list[index].publish_to_owner;
            let include_owner_report = this.property_documents_list[index].include_owner_report;
            let status = this.property_documents_list[index].status;

            for (let x = 0; x < this.property_documents_list.length; x++) {
                if (x >= index && x != lastIndex) {
                    this.property_documents_list[x].createdBy = this.property_documents_list[x + 1].createdBy;
                    this.property_documents_list[x].dateCreated = this.property_documents_list[x + 1].dateCreated;
                    this.property_documents_list[x].document_description =
                        this.property_documents_list[x + 1].document_description;
                    this.property_documents_list[x].document_id = this.property_documents_list[x + 1].document_id;
                    this.property_documents_list[x].document_title = this.property_documents_list[x + 1].document_title;
                    this.property_documents_list[x].documentType = this.property_documents_list[x + 1].documentType;
                    this.property_documents_list[x].external_url = this.property_documents_list[x + 1].external_url;
                    this.property_documents_list[x].filename = this.property_documents_list[x + 1].filename;
                    this.property_documents_list[x].is_external = this.property_documents_list[x + 1].is_external;
                    this.property_documents_list[x].publish_to_owner =
                        this.property_documents_list[x + 1].publish_to_owner;
                    this.property_documents_list[x].include_owner_report =
                        this.property_documents_list[x + 1].include_owner_report;
                    this.property_documents_list[x].status = this.property_documents_list[x + 1].status;
                } else if (x == lastIndex) {
                    this.property_documents_list[lastIndex].createdBy = createdBy;
                    this.property_documents_list[lastIndex].dateCreated = dateCreated;
                    this.property_documents_list[lastIndex].document_description = document_description;
                    this.property_documents_list[lastIndex].document_id = document_id;
                    this.property_documents_list[lastIndex].document_title = document_title;
                    this.property_documents_list[lastIndex].documentType = documentType;
                    this.property_documents_list[lastIndex].external_url = external_url;
                    this.property_documents_list[lastIndex].filename = filename;
                    this.property_documents_list[lastIndex].is_external = is_external;
                    this.property_documents_list[lastIndex].publish_to_owner = publish_to_owner;
                    this.property_documents_list[lastIndex].include_owner_report = include_owner_report;
                    this.property_documents_list[lastIndex].status = status;
                }
            }
        },
        setFirst: function (action, index) {
            if (0 == index) return false;

            let createdBy = this.property_documents_list[index].createdBy;
            let dateCreated = this.property_documents_list[index].dateCreated;
            let document_description = this.property_documents_list[index].document_description;
            let document_id = this.property_documents_list[index].document_id;
            let document_title = this.property_documents_list[index].document_title;
            let documentType = this.property_documents_list[index].documentType;
            let external_url = this.property_documents_list[index].external_url;
            let filename = this.property_documents_list[index].filename;
            let is_external = this.property_documents_list[index].is_external;
            let publish_to_owner = this.property_documents_list[index].publish_to_owner;
            let include_owner_report = this.property_documents_list[index].include_owner_report;
            let status = this.property_documents_list[index].status;

            for (let x = this.property_documents_list.length - 1; x >= 0; x--) {
                if (x <= index && x != 0) {
                    this.property_documents_list[x].createdBy = this.property_documents_list[x - 1].createdBy;
                    this.property_documents_list[x].dateCreated = this.property_documents_list[x - 1].dateCreated;
                    this.property_documents_list[x].document_description =
                        this.property_documents_list[x - 1].document_description;
                    this.property_documents_list[x].document_id = this.property_documents_list[x - 1].document_id;
                    this.property_documents_list[x].document_title = this.property_documents_list[x - 1].document_title;
                    this.property_documents_list[x].documentType = this.property_documents_list[x - 1].documentType;
                    this.property_documents_list[x].external_url = this.property_documents_list[x - 1].external_url;
                    this.property_documents_list[x].filename = this.property_documents_list[x - 1].filename;
                    this.property_documents_list[x].is_external = this.property_documents_list[x - 1].is_external;
                    this.property_documents_list[x].publish_to_owner =
                        this.property_documents_list[x - 1].publish_to_owner;
                    this.property_documents_list[x].include_owner_report =
                        this.property_documents_list[x - 1].include_owner_report;
                    this.property_documents_list[x].status = this.property_documents_list[x - 1].status;
                } else if (x == 0) {
                    this.property_documents_list[0].createdBy = createdBy;
                    this.property_documents_list[0].dateCreated = dateCreated;
                    this.property_documents_list[0].document_description = document_description;
                    this.property_documents_list[0].document_id = document_id;
                    this.property_documents_list[0].document_title = document_title;
                    this.property_documents_list[0].documentType = documentType;
                    this.property_documents_list[0].external_url = external_url;
                    this.property_documents_list[0].filename = filename;
                    this.property_documents_list[0].is_external = is_external;
                    this.property_documents_list[0].publish_to_owner = publish_to_owner;
                    this.property_documents_list[0].include_owner_report = include_owner_report;
                    this.property_documents_list[0].status = status;
                }
            }
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadDocs_' + id;
        },
        showPropertyActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        updateDocuments: function (index) {
            let document_period_date = this.property_documents_list[index].document_period_date;
            let document_id = this.property_documents_list[index].document_id;
            let publish_to_owner = this.property_documents_list[index].publish_to_owner;
            let include_owner_report = this.property_documents_list[index].include_owner_report;

            // let filename = this.property_documents_list[index].filename;
            let filename_old = this.property_documents_list[index].filename_old;
            let external_url = this.property_documents_list[index].external_url;
            let enable_external_link = this.property_documents_list[index].enable_external_link;
            let filename =
                this.property_documents_list[index].filename || this.property_documents_list[index].external_url;
            let errorArr = [];
            if (filename === '') {
                errorArr.push([index + 1 + '. You have not entered a valid attachment for the document.']);
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('document_id', document_id);
                form_data.append('document_period_date', document_period_date);
                form_data.append('publish_to_owner', publish_to_owner);
                form_data.append('include_owner_report', include_owner_report);
                form_data.append('external_url', external_url);
                form_data.append('filename', filename);
                if (filename !== filename_old) {
                    form_data.append('docs_file', this.property_documents_list[index].filename[0]);
                }
                let apiUrl;
                if (this.isPropertyFormLive()) apiUrl = 'with-file-upload/property/update/document';
                else apiUrl = 'with-file-upload/temp/property/update/document';

                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.loadForm();
                        // this.edit_form = false;
                        this.loading_setting = false;
                    });
            }
        },
        downloadS3File: function (this_file, this_file_name, file_type) {
            //console.log(this_file, this_file_name, file_type); //uncomment for testing only
            // this.workOrdersLoading = true;
            let params = new FormData();

            params.append('un', this.username);
            params.append('currentDB', this.current_db);
            params.append('user_type', this.user_type);

            params.append('s3path', this_file);

            axios.post(this.cirrus8_api_url + 'api/property/fetch/download-s3', params).then((response) => {
                if (response.data.data.file) {
                    this.downloadFile(response.data.data.file, this_file_name, file_type);
                }
            });
        },
        downloadFile(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;
            let blob = new Blob([this.s2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            // a.download = name+'.'+format;
            a.download = name;
            a.click();
        },
        s2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
        setActiveVersion: function (status) {
            this.SET_DOC_ACTIVE_VERSION(status);
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadPropertyDocumentSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
