<template>
    <div class="page-form">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Property Overview</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    icon
                    @click="initialPageLoad()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader
            type="table-tbody"
            v-if="loading_setting"
        ></cirrus-content-loader>
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Property</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        openDirection="bottom"
                        v-model="property_code"
                        :options="property_list"
                        group-values="fieldGroupValues"
                        :groupSelect="false"
                        group-label="fieldGroupNames"
                        :group-select="true"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        placeholder="Select a property"
                        track-by="fieldKey"
                        label="fieldValue"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Input Type</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="form-toggle"
                        v-model="input_type"
                        mandatory
                    >
                        <v-btn
                            small
                            depressed
                            text
                        >
                            ALL
                        </v-btn>
                        <v-btn
                            small
                            depressed
                            text
                        >
                            Cash Budget
                        </v-btn>
                        <v-btn
                            small
                            depressed
                            text
                        >
                            Cash Forecast
                        </v-btn>
                        <v-btn
                            small
                            depressed
                            text
                        >
                            Accruals Budget
                        </v-btn>
                        <v-btn
                            small
                            depressed
                            text
                        >
                            Accruals Forecast
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >For financial year</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        openDirection="bottom"
                        v-model="financial_year"
                        :options="year_list"
                        class="vue-select2 dropdown-left dropdown-300"
                        group-label="language"
                        placeholder="Select a financial year"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Portfolio</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="man_prop_manager"
                        :options="property_manager_list"
                        :custom-label="nameWithDash"
                        class="vue-select2 dropdown-left dropdown-300"
                        group-label="language"
                        placeholder="Please select"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                    >
                        <span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Locked/Unlocked By</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="modified_by"
                        :options="modified_by_list"
                        class="vue-select2 dropdown-left dropdown-300"
                        group-label="language"
                        placeholder="Please select"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                    >
                        <span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>
        </div>
        <v-data-table
            class="c8-datatable-custom"
            dense
            :item-class="itemRowBackground"
            item-key="id"
            :headers="headers"
            :items="property_budget_list"
            :items-per-page="items_per_page"
            hide-default-footer
            :page.sync="page"
            :total-visible="50"
            @page-count="page_count = $event"
            :search="search_datatable"
        >
            <template v-slot:item.index="{ item }">
                {{ property_budget_list.indexOf(item) + 1 }}
            </template>
            <template v-slot:item.lock_status="{ item }">
                <div class="form-row no-border-line">
                    <v-icon
                        size="18"
                        v-show="item.lock_status === '0'"
                        >lock_open</v-icon
                    >
                    <v-icon
                        size="18"
                        v-show="item.lock_status === '1'"
                        >lock</v-icon
                    >
                </div>
            </template>
            <template v-slot:item.property_code="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.property_code }}</span>
                </div>
            </template>
            <template v-slot:item.property_name="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.property_name }}</span>
                </div>
            </template>
            <template v-slot:item.portfolio_code="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.portfolio_code }}</span>
                </div>
            </template>
            <template v-slot:item.portfolio_name="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.portfolio_name }}</span>
                </div>
            </template>
            <template v-slot:item.vo_balanced="{ item }">
                <div class="form-row no-border-line">
                    <span
                        class="form-input-text"
                        v-if="item.budget_input_type === ''"
                        ><v-icon>remove</v-icon></span
                    >
                    <span
                        class="form-input-text"
                        v-else-if="item.vo_balanced === '1'"
                        >Yes</span
                    >
                    <span
                        class="form-input-text"
                        v-else
                        >No</span
                    >
                </div>
            </template>
            <template v-slot:item.man_fee_balanced="{ item }">
                <div class="form-row no-border-line">
                    <span
                        class="form-input-text"
                        v-if="item.budget_input_type === ''"
                        ><v-icon>remove</v-icon></span
                    >
                    <span
                        class="form-input-text"
                        v-else-if="item.man_fee_balanced === '1'"
                        >Yes</span
                    >
                    <span
                        class="form-input-text"
                        v-else
                        >No</span
                    >
                </div>
            </template>
            <template v-slot:item.charge_balanced="{ item }">
                <div class="form-row no-border-line">
                    <span
                        class="form-input-text"
                        v-if="item.budget_input_type === ''"
                        ><v-icon>remove</v-icon></span
                    >
                    <span
                        class="form-input-text"
                        v-else-if="item.charge_balanced === '1'"
                        >Yes</span
                    >
                    <span
                        class="form-input-text"
                        v-else
                        >No</span
                    >
                </div>
            </template>
        </v-data-table>
        <v-row
            class="form-row"
            v-show="property_budget_list.length > 20"
        >
            <v-col
                xs="12"
                sm="12"
                md="12"
            >
                <table class="c8-datatable-custom-footer">
                    <tr>
                        <td class="">Rows per page:</td>
                        <td>
                            <multiselect
                                v-model="items_per_page"
                                :options="[5, 10, 15, 25, 50, 100]"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-200"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </td>
                        <td></td>
                        <td>
                            <v-pagination
                                v-model="page"
                                :length="page_count"
                                :total-visible="5"
                            ></v-pagination>
                        </td>
                    </tr>
                </table>
            </v-col>
        </v-row>
    </div>
</template>
<script>
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';
export default {
    props: {
        currency_symbol: { type: String, default: '$' },
        country_default_settings: { type: String, default: '' },
    },
    data() {
        return {
            variable_outgoings_abr_label: 'VO',
            loading_setting: false,
            edit_form: false,
            error_server_msg: {},
            error_server_msg2: [],
            modal_current_ctr: 0,
            property_budget_list: [],
            property_code: { field_key: '', field_value: 'Please select...' },
            financial_year: { field_key: new Date().getFullYear(), field_value: new Date().getFullYear() },
            man_prop_manager: { field_key: '', field_value: 'Please select...' },
            modified_by: { field_key: '', field_value: 'Please select...' },
            property_list: [],
            modified_by_list: [],
            property_manager_list: [],
            year_list: [],
            input_type: 0,
            headers: [],
            page: 1,
            page_count: 0,
            items_per_page: 50,
            search_datatable: '',
        };
    },
    mounted() {
        this.initialPageLoad();
        this.loadPropertyList();
        this.loadManagementDetailsLists();
        this.loadModifiedByList();
        this.loadCountryDefaults();

        let country_default_settings = JSON.parse(atob(this.country_default_settings));

        this.variable_outgoings_abr_label = country_default_settings.vo.toUpperCase();
        this.headers = [
            { text: '#', value: 'index', sortable: false, width: '40px' },
            { text: 'Year', value: 'budget_year' },
            { text: 'Status', value: 'lock_status', align: 'center', sortable: false },
            { text: 'Budget Type', value: 'budget_input_type' },
            { text: 'Property Code', value: 'property_code' },
            { text: 'Property Name', value: 'property_name' },
            { text: 'Portfolio Code', value: 'portfolio_code' },
            { text: 'Portfolio Name', value: 'portfolio_name' },
            {
                text: this.variable_outgoings_abr_label + ' Balanced',
                value: 'vo_balanced',
                align: 'center',
                sortable: false,
            },
            { text: 'Man Fee Balanced', value: 'man_fee_balanced', align: 'center', sortable: false },
            { text: 'Charge Balanced', value: 'charge_balanced', align: 'center', sortable: false },
            { text: 'Executed By', value: 'modified_by' },
        ];
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.variable_outgoings_label = this.ucwords(this.country_defaults.variable_outgoings);
            });
        },
        itemRowBackground: function (item) {
            return item.budget_input_type === '' ? 'disabled_style' : '';
        },
        initialPageLoad: function () {
            let property_code = '';
            let financial_year = '';
            let man_prop_manager = '';
            let modified_by = '';
            if (this.property_code !== null) property_code = this.property_code.field_key;
            if (this.financial_year !== null) financial_year = this.financial_year.field_key;
            if (this.man_prop_manager !== null) man_prop_manager = this.man_prop_manager.field_key;
            if (this.modified_by !== null) modified_by = this.modified_by.field_key;
            var form_data = new FormData();
            form_data.append('property_code', property_code);
            form_data.append('financial_year', financial_year);
            form_data.append('man_prop_manager', man_prop_manager);
            form_data.append('modified_by', modified_by);
            form_data.append('input_type', this.input_type);
            form_data.append('no_load', true);
            this.$api.post('property/budget/loadBudgetOverview', form_data).then((response) => {
                this.property_budget_list = response.data.property_budget_list;
                this.year_list = response.data.year_list;
            });
        },
        loadPropertyList: function () {
            this.budgetSettingDimmer = true;
            let form_data = new FormData();
            form_data.append('budget_version', 'V2');
            form_data.append('no_load', true);
            this.$api.post('vue/loadAPIPropertyDropDownListRedis', form_data).then((response) => {
                this.property_list = response.data.data;
            });
        },
        loadManagementDetailsLists: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('property/fetch/management-details-lists', form_data).then((response) => {
                this.property_manager_list = response.data.property_manager_list;
            });
        },
        loadModifiedByList: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('property/budget/load-modified-by-list', form_data).then((response) => {
                this.modified_by_list = response.data.modified_by_list;
            });
        },
    },
    watch: {
        property_code: function () {
            this.initialPageLoad();
        },
        input_type: function () {
            this.initialPageLoad();
        },
        financial_year: function () {
            this.initialPageLoad();
        },
        man_prop_manager: function () {
            this.initialPageLoad();
        },
        modified_by: function () {
            this.initialPageLoad();
        },
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
<style>
.disabled_style {
    background-color: #f5f5f5 !important;
}
</style>
