<style>
/*body.c8-dark #frame #container  input[type=text],*/
/*body.c8-dark #frame #container  textarea {*/
/*    background: var(--dark-primary-color) !important;*/
/*    color: var(--primary-text-color) !important;*/
/*    border: 1px solid var(--primary-text-color) !important;*/
/*}*/
body.c8-dark #frame #container .ui.selectable.table tbody tr:hover,
body.c8-dark #frame #container .ui.table tbody tr td.selectable:hover {
    background: var(--dark-ligther-color) !important;
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #utility-meter-div .v-dialog__content,
body.c8-dark #frame #container #utility-meter-div .v-overlay {
    background: transparent !important;
}

body.c8-dark #frame #container input.multiselect__input {
    border: 0px !important;
}

body.c8-dark #frame #container .theme--light.v-btn.v-btn--disabled,
body.c8-dark #frame #container .theme--light.v-btn.v-btn--disabled.v-btn_loading,
body.c8-dark #frame #container .theme--light.v-btn.v-btn--disabled .v-icon {
    color: rgba(255, 255, 255, 26) !important;
}

body.c8-dark #frame #container .theme--light .v-btn--outlined .v-btn__content .v-icon,
body.c8-dark #frame #container .theme--light .v-btn--round .v-btn__content .v-icon {
    color: rgba(255, 255, 255, 26) !important;
}

/*body.c8-dark #frame #container #utility-meter-div .v-data-footer .v-data-footer__select{*/

/*}*/

body.c8-dark
    #frame
    #container
    #utility-meter-div
    .v-text-field
    > .v-input__control
    > .v-input__slot
    > .v-text-field__slot {
    border: 1px solid var(--primary-text-color) !important;
}

body.c8-dark #frame #container div i.v-icon.notranslate.material-icons.theme--light,
body.c8-dark #frame #container div i.v-icon.notranslate.mdi.mdi-menu-down.theme--light {
    color: #fff !important;
    z-index: 999;
}

.v-dialog__content {
    background-color: transparent !important;
}

.v-menu__content {
    position: fixed !important;
}

body.c8-dark #frame #container .v-application .titleHeader,
body.c8-dark #frame #container .v-application .titleHeader div {
    background-color: #000 !important;
    border: none !important;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <div
            class="cirrus8-loader-vue-js"
            v-if="loading_progressBar"
        >
            <div style="padding-top: 15%; text-align: center">
                <v-progress-linear
                    v-model="progressBar"
                    height="30"
                    striped
                >
                    <strong>{{ Math.ceil(progressBar) }}%</strong>
                </v-progress-linear>
            </div>
        </div>
        <v-toolbar flat>
            <v-toolbar-title>
                <h1
                    class="ui header"
                    style="color: #7f8c8d; font-size: 2vw; font-weight: normal; letter-spacing: -2px"
                >
                    Utility Meter
                </h1>
            </v-toolbar-title>
            <div class="flex-grow-1"></div>

            <v-btn
                color="primary"
                class="v-step-new-lease-button"
                @click="addNewMeter()"
                v-if="form_mode === 0"
            >
                <v-icon>add</v-icon>
                New Meter
            </v-btn>
            <v-btn
                color="black"
                class="v-step-cancel-lease-button"
                @click="cancelMeter()"
                dark
                v-if="form_mode === 1"
            >
                <v-icon color="red">close</v-icon>
                Cancel Meter
            </v-btn>
        </v-toolbar>

        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Property
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select
                        v-model="property_code"
                        :options="dd_property_list"
                        ref="propertyCodeDropDown"
                    />
                    <div
                        style="margin-top: -30px; margin-left: 302px"
                        v-if="dd_property_list && form_mode == 0"
                    >
                        <sui-button
                            style="height: 30px !important"
                            :loading="dd_property_list.length <= 0"
                            class="dropdown-button"
                            icon="caret right icon"
                            @click="forceRerender()"
                        />
                    </div>
                    <!--U: dropdown END-->
                </v-col>
            </v-row>
        </div>

        <sui-table
            v-if="search_type != 1 && property_code !== '' && form_mode === 0"
            name="meter-list"
            stackable
            selectable
            class="fixed_headers"
        >
            <sui-table-header class="fixed_headers_thead">
                <sui-table-row>
                    <sui-table-header-cell
                        colspan="5"
                        class="fieldDescription headerTr"
                        >Matching Records - click to refine your search
                    </sui-table-header-cell>
                    <sui-table-header-cell
                        class="fieldDescription headerTr"
                        text-align="right"
                        width="10%"
                    >
                        <v-spacer></v-spacer>
                        <v-btn
                            class="v-step-save-1-button"
                            @click="showImportReading()"
                            color="primary"
                            dark
                            right
                            small
                        >
                            IMPORT READING
                        </v-btn>
                    </sui-table-header-cell>
                </sui-table-row>
                <sui-table-row>
                    <sui-table-cell colspan="6">
                        <cirrus-input
                            inputFormat="search"
                            v-model="searchMeterType"
                            placeholder="Search"
                            :edit_form="true"
                            style="padding-right: 1em"
                        ></cirrus-input>
                    </sui-table-cell>
                </sui-table-row>
                <sui-table-row class="fieldDescription">
                    <sui-table-header-cell class="fieldDescription headerTr">Meter Type</sui-table-header-cell>
                    <sui-table-header-cell class="fieldDescription headerTr">Meter Number</sui-table-header-cell>
                    <sui-table-header-cell class="fieldDescription headerTr">Description</sui-table-header-cell>
                    <sui-table-header-cell class="fieldDescription headerTr">Last Reading Date</sui-table-header-cell>
                    <sui-table-header-cell
                        class="fieldDescription headerTr"
                        text-align="right"
                        >Last Reading
                    </sui-table-header-cell>
                    <sui-table-header-cell
                        class="fieldDescription headerTr"
                        text-align="right"
                        width="5"
                        >Action
                    </sui-table-header-cell>
                </sui-table-row>
            </sui-table-header>
            <sui-table-body v-if="loading_content_setting">
                <sui-table-row>
                    <sui-table-cell
                        colspan="6"
                        class="center"
                    >
                        <cirrus-content-loader v-if="loading_content_setting"></cirrus-content-loader>
                    </sui-table-cell>
                </sui-table-row>
            </sui-table-body>
            <sui-table-body>
                <sui-table-row
                    v-for="meterListData in property_meter_list"
                    :key="meterListData.meter_number"
                >
                    <sui-table-cell
                        class="left"
                        @click="selectMeter(meterListData)"
                    >
                        {{ meterListData.meter_type_description }}
                    </sui-table-cell>
                    <sui-table-cell @click="selectMeter(meterListData)">
                        {{ meterListData.meter_number }}
                    </sui-table-cell>
                    <sui-table-cell @click="selectMeter(meterListData)">
                        {{ meterListData.meter_description }}
                    </sui-table-cell>
                    <sui-table-cell @click="selectMeter(meterListData)">
                        {{ meterListData.last_reading_date }}
                    </sui-table-cell>
                    <sui-table-cell
                        text-align="right"
                        @click="selectMeter(meterListData)"
                    >
                        {{ meterListData.last_reading }}
                    </sui-table-cell>
                    <sui-table-cell text-align="right">
                        <v-btn
                            title="Delete"
                            x-small
                            icon
                            @click="confirmDelete(meterListData.meter_id, 'Property Meter')"
                        >
                            <v-icon color="red">delete_forever</v-icon>
                        </v-btn>
                    </sui-table-cell>
                </sui-table-row>
            </sui-table-body>
        </sui-table>

        <v-tabs
            class="cirrus-tab-theme"
            icons-and-text
            v-model="template_tab"
            show-arrows
            v-if="(property_code !== '' || new_property_code_ok) && search_type == 1"
        >
            <v-tabs-slider color="white"></v-tabs-slider>

            <v-tab
                href="#tab-1"
                v-if="responsive_show && form_mode === 1"
                class="v-step-form-tab"
            >
                New Meter
                <v-icon
                    small
                    dense
                    >menu_book
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-1"
                v-if="responsive_show && form_mode === 0"
                class="v-step-form-tab"
            >
                Meter Setup
                <v-icon
                    small
                    dense
                    >menu_book
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-2"
                v-if="responsive_show"
                v-show="form_mode === 0"
            >
                Meter Readings
                <v-icon
                    small
                    dense
                    >payments
                </v-icon>
            </v-tab>

            <v-tab
                href="#tab-1"
                class="primary--text v-step-form-tab"
                v-if="!responsive_show && form_mode === 1"
            >
                <v-icon
                    small
                    dense
                    >menu_book
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-1"
                class="primary--text v-step-form-tab"
                v-if="!responsive_show && form_mode === 0"
            >
                <v-icon
                    small
                    dense
                    >menu_book
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-2"
                class="primary--text"
                v-if="!responsive_show && form_mode === 0"
            >
                <v-icon
                    small
                    dense
                    >payments
                </v-icon>
            </v-tab>

            <v-tab-item :value="'tab-1'">
                <!-- Meter details section -->
                <div
                    id="meter_details_section"
                    class="page-form"
                    v-cloak
                    v-on:dblclick="doubleClickForm('meter')"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Meter Details</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                            <v-btn
                                x-small
                                data-tooltip="Edit"
                                v-if="form_mode !== 1 && !edit_meter_form"
                                class="v-step-edit-button"
                                icon
                                @click="edit_meter_form = true"
                            >
                                <v-icon>edit</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Undo Changes"
                                v-if="edit_meter_form && form_mode !== 1"
                                class="v-step-revert-button"
                                icon
                                @click="resetMeterForm()"
                            >
                                <v-icon color="red">undo</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Update Details"
                                v-if="edit_meter_form && form_mode !== 1"
                                class="v-step-save-1-button"
                                icon
                                @click="saveMeterForm()"
                            >
                                <v-icon
                                    light
                                    color="green"
                                    >check
                                </v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Refresh"
                                v-if="form_mode !== 1"
                                class="v-step-refresh-button"
                                icon
                                @click="loadMeterDetails()"
                            >
                                <v-icon>refresh</v-icon>
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting_meter"></cirrus-content-loader>
                    <div v-if="!loading_setting_meter">
                        <!-- METER NUMBER -->
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Meter Number:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_meter_form && form_mode !== 1">{{ meter_number }}</td>
                                        <td v-if="edit_meter_form || form_mode === 1">
                                            <v-text-field
                                                v-model="meter_number"
                                                :maxlength="50"
                                                dense
                                            />
                                            <br />
                                            <v-chip
                                                v-if="error_msg.length > 0 && errorData.id === 'meter_number'"
                                                v-for="(errorData, index) in error_msg"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <!-- METER TYPE -->
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Meter Type:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_meter_form && form_mode !== 1">{{ meter_type_description }}</td>
                                        <td v-if="edit_meter_form || form_mode === 1">
                                            <cirrus-single-select
                                                v-model="meter_type"
                                                :options="dd_meter_type_list"
                                            />
                                            <v-chip
                                                v-if="error_msg.length > 0 && errorData.id === 'meter_type'"
                                                v-for="(errorData, index) in error_msg"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <br />
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <!-- DESCRIPTION -->
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Description:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_meter_form && form_mode !== 1">{{ meter_description }}</td>
                                        <td v-if="edit_meter_form || form_mode === 1">
                                            <v-text-field
                                                v-model="meter_description"
                                                :maxlength="80"
                                                dense
                                            />
                                            <br />
                                            <v-chip
                                                v-if="error_msg.length > 0 && errorData.id === 'meter_description'"
                                                v-for="(errorData, index) in error_msg"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <!-- UNIT PRICE -->
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Unit Price:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_meter_form && form_mode !== 1">
                                            {{ formatAsCurrency(meter_charges, 4) }}
                                        </td>
                                        <td v-if="edit_meter_form || form_mode === 1">
                                            <v-text-field
                                                v-model="meter_charges"
                                                dense
                                            />
                                            <v-chip
                                                v-if="error_msg.length > 0 && errorData.id === 'meter_charges'"
                                                v-for="(errorData, index) in error_msg"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <!-- ACCOUNT CODE -->
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Account Code:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_meter_form && form_mode !== 1">{{ account_description }}</td>
                                        <td v-if="edit_meter_form || form_mode === 1">
                                            <!--                                            <cirrus-single-select v-model="account_code" :options="dd_account_list"/>-->

                                            <cirrus-single-select-group
                                                v-model="account_code"
                                                :options="dd_account_group_list"
                                                group-values="fieldGroupValues"
                                                group-label="fieldGroupNames"
                                                track-by="field_key"
                                                label="field_value"
                                                :selectedValue="selected_account_code"
                                                ref="refAccountCode"
                                            />
                                            <v-chip
                                                v-if="error_msg.length > 0 && errorData.id === 'account_code'"
                                                v-for="(errorData, index) in error_msg"
                                                :key="index"
                                                outlined
                                                color="error"
                                            >
                                                <br />
                                                <v-icon left>error</v-icon>
                                                {{ errorData.message }}
                                            </v-chip>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                        <!-- GENERATE SUPPORTING DOCUMENT-->
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Generate supporting information:</strong>
                                        </td>
                                        <td class="required">*</td>
                                        <td v-if="!edit_meter_form && form_mode !== 1">{{ generate_report }}</td>
                                        <td v-else>
                                            <v-btn-toggle
                                                class="v-step-search-type form-toggle"
                                                mandatory
                                                v-model="is_generate_report"
                                            >
                                                <v-btn
                                                    small
                                                    tile
                                                    text
                                                    v-bind:id="0"
                                                    v-text="'Yes'"
                                                    class="no-text-transform"
                                                ></v-btn>
                                                <v-btn
                                                    small
                                                    tile
                                                    text
                                                    v-bind:id="1"
                                                    v-text="'No'"
                                                    class="no-text-transform"
                                                ></v-btn>
                                            </v-btn-toggle>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <!-- end of agent Meter section -->

                <!-- Initial Reading section -->
                <div
                    id="temp_meter_reading_section"
                    v-if="form_mode === 1"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Meter Reading</h6>
                            &nbsp&nbsp
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting_reading"></cirrus-content-loader>
                    <div
                        v-if="!loading_setting_reading"
                        class="page-form"
                    >
                        <div class="page-list">
                            <div class="c8-page-table">
                                <!-- Initial Reading Date -->
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Last Reading Date:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-icon-date-picker
                                                        :size="'40'"
                                                        v-model="reading_date"
                                                        :error_msg="error_msg"
                                                    >
                                                    </cirrus-icon-date-picker>
                                                    <br />
                                                    <v-chip
                                                        v-if="error_msg.length > 0 && errorData.id === 'reading_date'"
                                                        v-for="(errorData, index) in error_msg"
                                                        :key="index"
                                                        outlined
                                                        color="error"
                                                    >
                                                        <v-icon left>error</v-icon>
                                                        {{ errorData.message }}
                                                    </v-chip>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <!-- Initial Reading -->
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong>Initial Reading:</strong>
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-text-field
                                                        v-model="initial_reading"
                                                        dense
                                                    />
                                                    <br />
                                                    <v-chip
                                                        v-if="
                                                            error_msg.length > 0 && errorData.id === 'initial_reading'
                                                        "
                                                        v-for="(errorData, index) in error_msg"
                                                        :key="index"
                                                        outlined
                                                        color="error"
                                                    >
                                                        <v-icon left>error</v-icon>
                                                        {{ errorData.message }}
                                                    </v-chip>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end of Initial Reading section -->

                <!-- Additional Charges section -->
                <div
                    id="charges_details_section"
                    v-if="form_mode === 0"
                    class="page-form"
                    v-cloak
                    v-on:dblclick="doubleClickForm('charges')"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Additional Charges</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                            <v-btn
                                x-small
                                data-tooltip="Edit"
                                v-if="form_mode !== 1 && !edit_charges_form"
                                class="v-step-edit-button"
                                icon
                                @click="edit_charges_form = true"
                            >
                                <v-icon>edit</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                v-show="edit_charges_form"
                                data-tooltip="Done"
                                v-if="edit_charges_form"
                                class="v-step-save-1-button"
                                icon
                                @click="edit_charges_form = false"
                            >
                                <v-icon
                                    light
                                    color="green"
                                    >check
                                </v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Refresh"
                                v-if="form_mode !== 1"
                                class="v-step-refresh-button"
                                icon
                                @click="loadChargesDetails()"
                            >
                                <v-icon>refresh</v-icon>
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting_charges"></cirrus-content-loader>
                    <div
                        v-if="!loading_setting_charges"
                        class="page-form"
                    >
                        <div class="page-list">
                            <div class="c8-page-table">
                                <table>
                                    <tbody>
                                        <tr class="form-row">
                                            <td colspan="2">
                                                <v-text-field
                                                    v-model="searchCharges"
                                                    placeholder="Search..."
                                                    dense
                                                />
                                            </td>
                                            <td width="25">&nbsp;</td>
                                            <td width="25%">&nbsp;</td>
                                            <td width="25%">&nbsp;</td>
                                            <td
                                                width="25%"
                                                align="right"
                                            >
                                                <v-btn
                                                    v-if="edit_charges_form"
                                                    class="v-step-save-1-button"
                                                    @click="showAddCharges()"
                                                    color="primary"
                                                    dark
                                                    right
                                                    small
                                                >
                                                    ADD CHARGES
                                                </v-btn>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-data-table
                                    :headers="chargesHeaders"
                                    :items="charges_items"
                                    :search="searchCharges"
                                    dense
                                >
                                    <template v-slot:item.charges_type_description="{ item }">
                                        <a
                                            v-if="edit_charges_form"
                                            title="Click to update"
                                            @click="showUpdateCharges(item)"
                                        >
                                            {{ item.charges_type_description }}
                                        </a>
                                        <span v-if="!edit_charges_form">{{ item.charges_type_description }}</span>
                                    </template>
                                    <template v-slot:item.charges_amount="{ item }">
                                        {{ formatAsCurrency(item.charges_amount, 6) }}
                                    </template>
                                    <template v-slot:item.delete_action="{ item }">
                                        <v-btn
                                            v-if="edit_charges_form"
                                            title="Delete"
                                            x-small
                                            icon
                                            @click="confirmDelete(item.charges_id, 'Charges')"
                                        >
                                            <v-icon color="red">delete_forever</v-icon>
                                        </v-btn>
                                    </template>
                                </v-data-table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end of Additional Charges section -->

                <!-- Temp Additional Charges section -->
                <div
                    id="charges_new_details_section"
                    v-if="form_mode === 1"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Additional Charges</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting_charges"></cirrus-content-loader>
                    <div
                        v-if="!loading_setting_charges"
                        class="page-form"
                    >
                        <div class="page-list">
                            <div class="c8-page-table">
                                <table>
                                    <tbody>
                                        <tr class="form-row">
                                            <td colspan="2">
                                                <v-text-field
                                                    v-model="searchCharges"
                                                    placeholder="Search..."
                                                    dense
                                                />
                                            </td>
                                            <td width="25">&nbsp;</td>
                                            <td width="25%">&nbsp;</td>
                                            <td width="25%">&nbsp;</td>
                                            <td
                                                width="25%"
                                                align="right"
                                            >
                                                <v-btn
                                                    class="v-step-save-1-button"
                                                    @click="showAddCharges()"
                                                    color="primary"
                                                    dark
                                                    right
                                                    small
                                                >
                                                    ADD CHARGES
                                                </v-btn>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-data-table
                                    :headers="chargesHeaders"
                                    :items="charges_items_new"
                                    :search="searchCharges"
                                    dense
                                >
                                    <template v-slot:item.charges_amount="{ item }">
                                        {{ formatAsCurrency(item.charges_amount, 6) }}
                                    </template>
                                    <template v-slot:item.delete_action="{ item }">
                                        <v-btn
                                            title="Delete"
                                            x-small
                                            icon
                                            @click="deleteTempRow(item.row_id, 'Charges')"
                                        >
                                            <v-icon color="red">delete_forever</v-icon>
                                        </v-btn>
                                    </template>
                                </v-data-table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end of Temp Additional Charges section -->

                <!-- Allocation section -->
                <div
                    id="allocation_section"
                    v-if="form_mode === 0"
                    class="page-form"
                    v-cloak
                    v-on:dblclick="doubleClickForm('allocation')"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Allocation</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                            <v-btn
                                x-small
                                data-tooltip="Edit"
                                v-if="form_mode !== 1 && !edit_allocation_form"
                                class="v-step-edit-button"
                                icon
                                @click="edit_allocation_form = true"
                            >
                                <v-icon>edit</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                v-show="edit_allocation_form"
                                data-tooltip="Done"
                                v-if="edit_allocation_form"
                                class="v-step-save-1-button"
                                icon
                                @click="edit_allocation_form = false"
                            >
                                <v-icon
                                    light
                                    color="green"
                                    >check
                                </v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Refresh"
                                v-if="form_mode !== 1"
                                class="v-step-refresh-button"
                                icon
                                @click="loadAllocationDetails()"
                            >
                                <v-icon>refresh</v-icon>
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting_allocation"></cirrus-content-loader>
                    <div
                        v-if="!loading_setting_allocation"
                        class="page-form"
                    >
                        <div class="page-list">
                            <div class="c8-page-table">
                                <table>
                                    <tbody>
                                        <tr class="form-row">
                                            <td colspan="2">
                                                <v-text-field
                                                    v-model="searchAllocation"
                                                    placeholder="Search..."
                                                    dense
                                                />
                                            </td>
                                            <td
                                                width="50%"
                                                align="right"
                                            >
                                                <v-btn
                                                    v-if="edit_allocation_form"
                                                    class="v-step-save-1-button"
                                                    @click="showAddAllocation()"
                                                    color="primary"
                                                    dark
                                                    right
                                                    small
                                                >
                                                    ADD ALLOCATION
                                                </v-btn>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-data-table
                                    :headers="allocationHeaders"
                                    :items="allocation_items"
                                    :search="searchAllocation"
                                    dense
                                >
                                    <template v-slot:item.allocation_unit="{ item }">
                                        <a
                                            v-if="edit_allocation_form"
                                            title="Click to update"
                                            @click="showUpdateAllocation(item)"
                                        >
                                            {{ item.allocation_unit }}
                                        </a>
                                        <span v-if="!edit_allocation_form">{{ item.allocation_unit }}</span>
                                    </template>
                                    <template v-slot:item.delete_action="{ item }">
                                        <v-btn
                                            v-if="edit_allocation_form"
                                            title="Delete"
                                            x-small
                                            icon
                                            @click="confirmDelete(item.allocation_id, 'Allocation')"
                                        >
                                            <v-icon color="red">delete_forever</v-icon>
                                        </v-btn>
                                    </template>
                                </v-data-table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end of Allocation section -->

                <!-- Temp Allocation section -->
                <div
                    id="temp_allocation_section"
                    v-if="form_mode === 1"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Allocation</h6>
                            &nbsp&nbsp
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting_allocation"></cirrus-content-loader>
                    <div
                        v-if="!loading_setting_allocation"
                        class="page-form"
                    >
                        <div class="page-list">
                            <div class="c8-page-table">
                                <table>
                                    <tbody>
                                        <tr class="form-row">
                                            <td colspan="2">
                                                <v-text-field
                                                    v-model="searchAllocation"
                                                    placeholder="Search..."
                                                    dense
                                                />
                                            </td>
                                            <td
                                                width="50%"
                                                align="right"
                                            >
                                                <v-btn
                                                    class="v-step-save-1-button"
                                                    @click="showAddAllocation()"
                                                    color="primary"
                                                    dark
                                                    right
                                                    small
                                                >
                                                    ADD ALLOCATION
                                                </v-btn>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-data-table
                                    :headers="allocationHeaders"
                                    :items="allocation_items_new"
                                    :search="searchAllocation"
                                    dense
                                >
                                    <template v-slot:item.delete_action="{ item }">
                                        <v-btn
                                            title="Delete"
                                            x-small
                                            icon
                                            @click="deleteTempRow(item.row_id, 'Allocation')"
                                        >
                                            <v-icon color="red">delete_forever</v-icon>
                                        </v-btn>
                                    </template>
                                </v-data-table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end of Temp Allocation section -->

                <v-row
                    class="form-row no-gutters"
                    v-if="form_mode === 1"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <br />
                        <br /><br />
                        <v-btn
                            class="v-step-save-1-button"
                            @click="saveMeterForm()"
                            color="primary"
                            dark
                            absolute
                            right
                            small
                        >
                            SAVE METER
                        </v-btn>
                    </v-col>
                </v-row>
            </v-tab-item>
            <v-tab-item
                :value="'tab-2'"
                v-show="form_mode === 0"
            >
                <!-- Meter Reading section -->
                <div
                    id="meter_reading_section"
                    class="page-form"
                    v-cloak
                    v-on:dblclick="doubleClickForm('reading')"
                >
                    <v-card
                        class="section-toolbar"
                        color="titleHeader"
                        text
                        tile
                    >
                        <v-card-actions>
                            <h6 class="title font-weight-black">Meter Reading</h6>
                            &nbsp&nbsp
                            <v-spacer></v-spacer>
                            <v-btn
                                x-small
                                data-tooltip="Edit"
                                v-if="form_mode !== 1 && !edit_reading_form"
                                class="v-step-edit-button"
                                icon
                                @click="edit_reading_form = true"
                            >
                                <v-icon>edit</v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                v-show="edit_reading_form"
                                data-tooltip="Done"
                                v-if="edit_reading_form"
                                class="v-step-save-1-button"
                                icon
                                @click="edit_reading_form = false"
                            >
                                <v-icon
                                    light
                                    color="green"
                                    >check
                                </v-icon>
                            </v-btn>
                            <v-btn
                                x-small
                                data-tooltip="Refresh"
                                v-if="form_mode !== 1"
                                class="v-step-refresh-button"
                                icon
                                @click="loadReadingDetails()"
                            >
                                <v-icon>refresh</v-icon>
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                    <cirrus-content-loader v-if="loading_setting_reading"></cirrus-content-loader>
                    <div
                        v-if="!loading_setting_reading"
                        class="page-form"
                    >
                        <div class="page-list">
                            <div class="c8-page-table">
                                <table>
                                    <tbody>
                                        <tr class="form-row">
                                            <td colspan="2">
                                                <v-text-field
                                                    v-model="searchReading"
                                                    placeholder="Search..."
                                                    dense
                                                />
                                            </td>
                                            <td
                                                width="50%"
                                                align="right"
                                            >
                                                <v-btn
                                                    v-if="edit_reading_form"
                                                    class="v-step-save-1-button"
                                                    @click="showAddReading()"
                                                    color="primary"
                                                    dark
                                                    right
                                                    small
                                                >
                                                    ADD READING
                                                </v-btn>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-data-table
                                    :loading="readingLoading"
                                    loading-text="Loading... Please Wait..."
                                    :headers="readingHeaders"
                                    :items="reading_items"
                                    :search="searchReading"
                                    dense
                                >
                                    <!--                                    <template v-slot:item.reading_date="{ item }" >-->
                                    <!--                                        <a v-if="edit_reading_form" title="Click to update" @click="showUpdateReading(item)"> {{ item.reading_date }} </a>-->
                                    <!--                                        <span v-if="!edit_reading_form">{{ item.reading_date }}</span>-->
                                    <!--                                    </template>-->
                                    <template v-slot:item.invoice_number="{ item }">
                                        <a
                                            v-if="item.invoice_number != ''"
                                            title="Download Invoice"
                                            @click="downloadTaxInvoice(item)"
                                        >
                                            {{ item.invoice_number }}
                                        </a>
                                    </template>
                                    <template v-slot:item.generate_support_docs="{ item }">
                                        <a
                                            v-if="item.generate_support_docs == 1"
                                            title="Download Supporting Documents"
                                            @click="downloadSupportingDocument(item)"
                                        >
                                            <img
                                                :src="asset_domain + 'assets/images/icons/pdf.png'"
                                                alt="Adobe Logo"
                                                class="icon"
                                            />
                                        </a>
                                    </template>

                                    <template v-slot:item.reading_unit_charges="{ item }">
                                        {{ formatAsCurrency(item.reading_unit_charges, 4) }}
                                    </template>
                                    <template v-slot:item.reading_total_unit_charges="{ item }">
                                        <a
                                            v-if="item.reading_total_unit_charges != 0"
                                            title="Unit Charges Breakdowm"
                                            @click="showAdditionalChargesBreakdown(item.reading_breakdown, 'UNIT')"
                                        >
                                            {{ formatAsCurrency(item.reading_total_unit_charges, 2) }}
                                        </a>
                                        <span v-if="item.reading_total_unit_charges == 0">{{
                                            formatAsCurrency(item.reading_total_unit_charges, 2)
                                        }}</span>
                                    </template>
                                    <template v-slot:item.reading_additional_charges="{ item }">
                                        <a
                                            v-if="item.reading_additional_charges != 0"
                                            title="Additional Charges Breakdown"
                                            @click="showAdditionalChargesBreakdown(item.reading_breakdown, 'CHARGES')"
                                        >
                                            {{ formatAsCurrency(item.reading_additional_charges, 2) }}
                                        </a>
                                        <span v-if="item.reading_additional_charges == 0">{{
                                            formatAsCurrency(item.reading_additional_charges, 2)
                                        }}</span>
                                    </template>
                                    <template v-slot:item.invoice_amt="{ item }">
                                        {{ formatAsCurrency(item.invoice_amt, 2) }}
                                    </template>
                                    <template v-slot:item.delete_action="{ item }">
                                        <v-btn
                                            v-if="reading_items.length == 1 && edit_reading_form"
                                            title="Edit"
                                            x-small
                                            icon
                                            @click="editReading(item)"
                                        >
                                            <v-icon color="yellow">edit</v-icon>
                                        </v-btn>
                                        <v-btn
                                            v-if="
                                                edit_reading_form &&
                                                reading_items.length > 1 &&
                                                reading_items.length - 1 == reading_items.indexOf(item)
                                            "
                                            title="Delete"
                                            x-small
                                            icon
                                            @click="
                                                confirmDelete(
                                                    item.reading_id,
                                                    'Meter Reading',
                                                    item.is_invoice_generated,
                                                )
                                            "
                                        >
                                            <v-icon color="red">delete_forever</v-icon>
                                        </v-btn>
                                    </template>
                                </v-data-table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end of Additional Charges section -->
            </v-tab-item>
        </v-tabs>

        <!-- SHOW ADD/UPDATE CHARGES DIALOG -->
        <v-dialog
            v-model="dialogCharges"
            max-width="700"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>{{ modal_charges_title }}</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="closeChargesModal()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: 300; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <!-- CHARGES TYPE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Charges Type
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    ref="refChargesType"
                                    v-model="charges_type"
                                    :options="dd_charges_type_list"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- DESCRIPTION -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Description
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="charges_description"
                                    :maxlength="80"
                                    dense
                                />
                            </v-col>
                        </v-row>

                        <!-- AMOUNT -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Amount
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="charges_amount"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- ACCOUNT CODE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Account Code
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <!--                                <cirrus-single-select-->
                                <!--                                    ref="refChargesAccountCode"-->
                                <!--                                    v-model="charges_account_code"-->
                                <!--                                    :options="dd_account_list"-->
                                <!--                                    dense-->
                                <!--                                />-->
                                <cirrus-single-select-group
                                    v-model="charges_account_code"
                                    :options="dd_account_group_list"
                                    group-values="fieldGroupValues"
                                    group-label="fieldGroupNames"
                                    track-by="field_key"
                                    label="field_value"
                                    :selectedValue="selected_charges_account_code"
                                    ref="refChargesAccountCode"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <br />
                <br />
                <br />
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        v-if="new_meter == 0"
                        @click="saveCharges()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        {{ charges_button_label }}
                    </v-btn>
                    <v-btn
                        class="v-step-save-1-button"
                        v-if="new_meter == 1"
                        @click="addTempCharges()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        {{ charges_button_label }}
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF ADD/UPDATE CHARGES DIALOG -->

        <!-- SHOW ADD/UPDATE ALLOCATION DIALOG -->
        <v-dialog
            v-model="dialogAllocation"
            max-width="700"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>{{ modal_allocation_title }}</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="closeAllocationModal()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: 250; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <!-- UNIT -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Unit
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select
                                    v-model="allocation_unit"
                                    :options="property_unit_list"
                                    dense
                                    v-if="allocation_add"
                                />
                                <span v-if="allocation_update">{{ allocation_unit }}</span>
                            </v-col>
                        </v-row>
                        <!-- PERCENTAGE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Percentage (%)
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="allocation_percentage"
                                    dense
                                />
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        v-if="new_meter == 0"
                        @click="saveAllocation()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        {{ allocation_button_label }}
                    </v-btn>
                    <v-btn
                        class="v-step-save-1-button"
                        v-if="new_meter == 1"
                        @click="addTempAllocation()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        {{ allocation_button_label }}
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF ADD/UPDATE ALLOCATION DIALOG -->

        <!-- SHOW ADD READING DIALOG -->
        <v-dialog
            v-model="dialogReading"
            max-width="900"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>{{ modal_reading_title }}</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="closeReadingModal()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <div
                    v-if="!loading_setting_reading"
                    class="page-form"
                >
                    <div class="page-list">
                        <div class="c8-page-table">
                            <v-card-text>
                                <v-data-table
                                    :headers="readingHistoryHeaders"
                                    :items="last_five_readings"
                                    :sort-by="['reading_id']"
                                    :search="searchReading"
                                    hide-default-footer
                                    class="elevation-1"
                                    dense
                                >
                                    <template
                                        class="test"
                                        v-slot:top
                                    >
                                        <!--//v-toolbar flat color="#006581" height="30px;"//-->
                                        <v-toolbar
                                            flat
                                            color="#3489A1"
                                            height="30px;"
                                            class="payment-list-header"
                                        >
                                            <v-toolbar-title style="font-size: 12px; color: #ffffff; font-weight: bold"
                                                >Last 5 Readings
                                            </v-toolbar-title>
                                        </v-toolbar>
                                    </template>
                                    <template v-slot:item.invoice_number="{ item }">
                                        <a
                                            v-if="item.invoice_number != ''"
                                            title="Download Invoice"
                                            @click="downloadTaxInvoice(item)"
                                        >
                                            {{ item.invoice_number }}
                                        </a>
                                    </template>
                                    <template v-slot:item.reading_unit_charges="{ item }">
                                        {{ formatAsCurrency(item.reading_unit_charges, 4) }}
                                    </template>
                                    <template v-slot:item.generate_support_docs="{ item }">
                                        <a
                                            v-if="item.generate_support_docs == 1"
                                            title="Download Supporting Documents"
                                            @click="downloadSupportingDocument(item)"
                                        >
                                            <img
                                                :src="asset_domain + 'assets/images/icons/pdf.png'"
                                                alt="Adobe Logo"
                                                class="icon"
                                            />
                                        </a>
                                    </template>

                                    <template v-slot:item.reading_total_unit_charges="{ item }">
                                        <a
                                            v-if="item.reading_total_unit_charges != 0"
                                            title="Unit Charges Breakdowm"
                                            @click="showAdditionalChargesBreakdown(item.reading_breakdown, 'UNIT')"
                                        >
                                            {{ formatAsCurrency(item.reading_total_unit_charges, 2) }}
                                        </a>
                                        <span v-if="item.reading_total_unit_charges == 0">{{
                                            formatAsCurrency(item.reading_total_unit_charges, 2)
                                        }}</span>
                                    </template>
                                    <template v-slot:item.reading_additional_charges="{ item }">
                                        <a
                                            v-if="item.reading_additional_charges != 0"
                                            title="Additional Charges Breakdown"
                                            @click="showAdditionalChargesBreakdown(item.reading_breakdown, 'CHARGES')"
                                        >
                                            {{ formatAsCurrency(item.reading_additional_charges, 2) }}
                                        </a>
                                        <span v-if="item.reading_additional_charges == 0">{{
                                            formatAsCurrency(item.reading_additional_charges, 2)
                                        }}</span>
                                    </template>
                                    <template v-slot:item.invoice_amt="{ item }">
                                        {{ formatAsCurrency(item.invoice_amt, 2) }}
                                    </template>
                                </v-data-table>
                            </v-card-text>
                        </div>
                    </div>
                </div>
                <divider></divider>
                <div
                    class="body c8-page"
                    style="height: 250px; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row"></v-row>
                        <!-- DATE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Date
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    v-model="reading_date"
                                    ref="refReadingDate"
                                    :error_msg="error_msg"
                                >
                                </cirrus-icon-date-picker>
                            </v-col>
                        </v-row>
                        <!-- READING -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Reading
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="reading_present"
                                    dense
                                />
                            </v-col>
                        </v-row>
                        <!-- READING -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Create Charge
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-btn-toggle
                                    v-model="reading_invoice"
                                    mandatory
                                >
                                    <v-btn
                                        small
                                        text
                                        value="1"
                                    >
                                        Yes
                                    </v-btn>
                                    <v-btn
                                        small
                                        text
                                        value="0"
                                    >
                                        No
                                    </v-btn>
                                </v-btn-toggle>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        @click="addReading()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        {{ reading_button_label }}
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="closeReadingModal()"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF ADD/UPDATE READING DIALOG -->

        <!-- SHOW READING HISTORY -->
        <v-dialog
            top
            v-model="showReadingHistory"
            width="1200"
            content-class="c8-page"
            style="width: 60%"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Meter Reading History</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="showReadingHistory = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <v-card-text>
                        <v-data-table
                            :headers="readingHistoryHeaders"
                            :items="last_five_readings"
                            :search="searchReading"
                            dense
                        >
                            <template v-slot:item.invoice_number="{ item }">
                                <a
                                    v-if="item.invoice_number != ''"
                                    title="Download Invoice"
                                    @click="downloadTaxInvoice(item)"
                                >
                                    {{ item.invoice_number }}
                                </a>
                            </template>
                            <template v-slot:item.reading_unit_charges="{ item }">
                                {{ formatAsCurrency(item.reading_unit_charges, 4) }}
                            </template>
                            <template v-slot:item.generate_support_docs="{ item }">
                                <a
                                    v-if="item.generate_support_docs == 1"
                                    title="Download Supporting Documents"
                                    @click="downloadSupportingDocument(item)"
                                >
                                    <img
                                        :src="asset_domain + 'assets/images/icons/pdf.png'"
                                        alt="Adobe Logo"
                                        class="icon"
                                    />
                                </a>
                            </template>
                            <template v-slot:item.reading_total_unit_charges="{ item }">
                                <a
                                    v-if="item.reading_total_unit_charges != 0"
                                    title="Unit Charges Breakdowm"
                                    @click="showAdditionalChargesBreakdown(item.reading_breakdown, 'UNIT')"
                                >
                                    {{ formatAsCurrency(item.reading_total_unit_charges, 2) }}
                                </a>
                                <span v-if="item.reading_total_unit_charges == 0">{{
                                    formatAsCurrency(item.reading_total_unit_charges, 2)
                                }}</span>
                            </template>
                            <template v-slot:item.reading_additional_charges="{ item }">
                                <a
                                    v-if="item.reading_additional_charges != 0"
                                    title="Additional Charges Breakdown"
                                    @click="showAdditionalChargesBreakdown(item.reading_breakdown, 'CHARGES')"
                                >
                                    {{ formatAsCurrency(item.reading_additional_charges, 2) }}
                                </a>
                                <span v-if="item.reading_additional_charges == 0">{{
                                    formatAsCurrency(item.reading_additional_charges, 2)
                                }}</span>
                            </template>
                            <template v-slot:item.invoice_amt="{ item }">
                                {{ formatAsCurrency(item.invoice_amt, 2) }}
                            </template>
                        </v-data-table>
                    </v-card-text>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF READING HISTORY -->

        <!-- SHOW BREAKDOWN ADDITIONAL CHARGES DIALOG -->
        <v-dialog
            v-model="dialogChargesBreakdown"
            max-width="900"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span v-if="unitChargesDisplay">Unit Charges Breakdown</span>
                    <span v-else>Additional Charges Breakdown</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogChargesBreakdown = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial; padding: 0px 5px"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr
                                v-if="!unitChargesDisplay"
                                class="fieldDescription"
                            >
                                <td>Lease Code</td>
                                <td>Charges Type</td>
                                <td>Description</td>
                                <td>Account</td>
                                <td align="right">No. of Days</td>
                                <td align="right">Amount</td>
                                <td align="right">Total Amount</td>
                            </tr>
                            <tr
                                v-else
                                class="fieldDescription"
                            >
                                <td>Lease Code</td>
                                <td>Description</td>
                                <td>Account</td>
                                <td align="right">Reading Consumption</td>
                                <td align="right">Unit Price</td>
                                <td align="right">Percentage Allocation</td>
                                <td align="right">Total Amount</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(data, index) in charges_breakdown_items"
                                :key="index"
                            >
                                <td>{{ data.lease_code }}</td>
                                <td v-if="!unitChargesDisplay">{{ data.reading_charges_type_desc }}</td>
                                <td>{{ data.reading_charges_description }}</td>
                                <td>{{ data.reading_charges_account_desc }}</td>
                                <td
                                    v-if="!unitChargesDisplay"
                                    align="right"
                                >
                                    {{ data.reading_charges_days }}
                                </td>
                                <td
                                    v-if="!unitChargesDisplay"
                                    align="right"
                                >
                                    {{ formatAsCurrency(data.reading_charges_amount, 6) }}
                                </td>
                                <td
                                    v-if="unitChargesDisplay"
                                    align="right"
                                >
                                    {{ data.reading_consumption }}
                                </td>
                                <td
                                    v-if="unitChargesDisplay"
                                    align="right"
                                >
                                    {{ formatAsCurrency(data.reading_unit_charges, 4) }}
                                </td>
                                <td
                                    v-if="unitChargesDisplay"
                                    align="right"
                                >
                                    {{ data.percent_allocated }}%
                                </td>
                                <td align="right">{{ formatAsCurrency(data.reading_charges_total, 2) }}</td>
                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td
                                    colspan="2"
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    Total :
                                </td>
                                <td
                                    align="right"
                                    style="font-weight: bold"
                                >
                                    {{ formatAsCurrency(totalAdditionalCharges, 2) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <!--//v-btn color="primary" text @click="modalARBreakdown = false">Close</v-btn//-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF BREAKDOWN ADDITIONAL CHARGES DIALOG -->

        <!-- SHOW IMPORT READING DIALOG -->
        <v-dialog
            v-model="dialogImportReading"
            max-width="800"
            content-class="c8-page"
            id="importReading"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Import Reading</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="closeDialogImportReading"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <div class="body c8-page">
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Excel file to upload
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <div
                                    class="pdf-upload"
                                    style="max-width: 80%"
                                >
                                    <div
                                        class="tile"
                                        @click="$refs.file.click()"
                                        v-bind:class="{ loading: pdfUploading }"
                                    >
                                        <span
                                            class="text-uppercase"
                                            v-if="pdfUploading"
                                        >
                                            <v-icon>mdi-spin mdi-loading</v-icon>
                                            uploading
                                        </span>
                                        <span
                                            v-else-if="!pdfUploading && files.length === 0"
                                            class="text-uppercase"
                                        >
                                            Drag and drop files here...
                                        </span>

                                        <span v-else-if="!pdfUploading && files.length > 0 && files[0].name !== null">
                                            {{ files[0].name }}
                                        </span>
                                    </div>
                                    <input
                                        type="file"
                                        ref="file"
                                        hidden
                                        multiple
                                        @input="handleImportUpload()"
                                        @click="removeFile()"
                                        id="importUpload"
                                    />
                                </div>
                                <v-btn
                                    text
                                    x-small
                                    @click="downloadTemplate()"
                                    color="primary"
                                    >* Download Template
                                </v-btn>
                            </v-col>
                        </v-row>
                    </div>
                    <cirrus-server-error
                        :error_msg="error_import_msg"
                        :errorMsg2="error_import_msg2"
                    ></cirrus-server-error>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        @click="importReadingData()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        IMPORT READING
                    </v-btn>
                </v-card-actions>
                <div></div>
            </v-card>
        </v-dialog>
        <!-- END OF IMPORT READING DIALOG -->

        <!-- SHOW CREATE CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialogConfirmation"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <!--                    <v-icon color="warning">warning</v-icon>  -->
                    Create Meter Reading
                    <a
                        href="#"
                        class="dialog-close"
                        @click="closeCreateMeterReading()"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div
                        v-if="is_vacant"
                        style="margin: 10"
                        color="warning"
                    >
                        <v-icon color="warning">warning</v-icon>
                        Unit allocated on this meter number is vacant.
                    </div>
                    <div
                        v-if="!is_vacant"
                        class="body c8-page"
                        style="min-height: initial; padding: 10px"
                    >
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Property
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span>{{ new_property_label }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Transaction Date
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        v-model="transaction_date"
                                        :error_msg="error_msg"
                                    >
                                    </cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Create Interim Tax Invoice
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        v-model="is_generate"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Due Date
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        v-model="invoice_due_date"
                                        :error_msg="error_msg"
                                    >
                                    </cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Send to Tenant
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        v-model="send_email"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                    <br />
                                    <v-span
                                        v-if="send_email == 0 && is_generate == 0"
                                        style="color: #00baf2 !important"
                                        >{{ tenant_email }}
                                    </v-span>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="send_email == 0 && is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >CC to {{ portfolio_manager_label }}
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle v-model="send_cc">
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <!--                            <v-row class="form-row" v-if="is_generate == 0">-->
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Attachment
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <div
                                        class="pdf-upload"
                                        style="max-width: 300px"
                                    >
                                        <div
                                            class="tile"
                                            @click="$refs.fileAttach.click()"
                                            v-bind:class="{ loading: pdfUploading }"
                                        >
                                            <span
                                                class="text-uppercase"
                                                v-if="pdfUploading"
                                            >
                                                <v-icon>mdi-spin mdi-loading</v-icon>
                                                uploading
                                            </span>
                                            <span
                                                v-else-if="!pdfUploading && attachment_file.length === 0"
                                                class="text-uppercase"
                                            >
                                                Drag and drop files here...
                                            </span>

                                            <span
                                                v-else-if="
                                                    !pdfUploading &&
                                                    attachment_file.length > 0 &&
                                                    attachment_file[0].name !== null
                                                "
                                            >
                                                {{ attachment_file[0].name }}
                                            </span>
                                        </div>
                                        <input
                                            type="file"
                                            ref="fileAttach"
                                            accept="application/pdf"
                                            hidden
                                            multiple
                                            @input="handleAttachmentUpload()"
                                            id="attachmentsUpload"
                                        />
                                    </div>
                                </v-col>
                            </v-row>

                            <br />
                            <br />
                            <v-tabs v-model="tabLease">
                                <v-tab
                                    v-for="item in lease_list"
                                    :key="item.value"
                                    dense
                                >
                                    {{ item.value }}
                                </v-tab>
                            </v-tabs>
                            <v-card
                                class="section-toolbar titleHeader"
                                text
                                tile
                            >
                                <v-card-actions style="padding-right: 20px; margin-top: -10px">
                                    <h6
                                        class="title font-weight-black"
                                        style="padding-left: 10px"
                                    >
                                        Reading Details
                                    </h6>
                                    &nbsp&nbsp
                                    <v-spacer></v-spacer>
                                    <!-- ACTION BUTTONS - START -->
                                    <v-btn
                                        x-small
                                        data-tooltip="Save Changes"
                                        class="v-step-save-1-button"
                                        icon
                                        @click="updateDescription()"
                                    >
                                        <!-- <v-icon light color="green">save</v-icon>-->
                                        <v-icon>done</v-icon>
                                    </v-btn>
                                    <v-btn
                                        x-small
                                        data-tooltip="Refresh"
                                        class="v-step-refresh-button"
                                        icon
                                        @click="refreshDescription()"
                                    >
                                        <v-icon>refresh</v-icon>
                                    </v-btn>
                                </v-card-actions>
                            </v-card>
                            <div class="page-list">
                                <div class="c8-page-table">
                                    <v-data-table
                                        :headers="readingListAddReading"
                                        :items="unit_transaction_list"
                                        :search="searchReading"
                                        :loading="loadingReadingList"
                                        loading-text="Loading... Please Wait..."
                                        hide-default-footer
                                        class="elevation-1"
                                        dense
                                    >
                                        <template v-slot:item="{ item }">
                                            <tr>
                                                <td>{{ item.line_number }}</td>
                                                <td>
                                                    <input
                                                        type="text"
                                                        :maxlength="40"
                                                        name="description"
                                                        class="c-input"
                                                        v-model="item.description"
                                                        value="item.description"
                                                    />
                                                </td>
                                                <td>{{ item.account_code }}</td>
                                                <td align="center">{{ item.last_reading_date }}</td>
                                                <td align="center">{{ item.reading_date }}</td>
                                                <td
                                                    v-if="item.line_number == 1"
                                                    align="right"
                                                >
                                                    <a
                                                        title="Show details"
                                                        @click="dialogPreviousBreakdown = true"
                                                    >
                                                        {{ formatAsCurrency(item.net_amount, 2) }}
                                                    </a>
                                                </td>
                                                <td
                                                    v-else
                                                    align="right"
                                                >
                                                    {{ formatAsCurrency(item.net_amount, 2) }}
                                                </td>
                                                <td align="right">{{ formatAsCurrency(item.tax_amount, 2) }}</td>
                                                <td align="right">{{ formatAsCurrency(item.gross_amount, 2) }}</td>
                                            </tr>
                                        </template>
                                        <template v-slot:body.append>
                                            <tr class="highlight">
                                                <td
                                                    colspan="5"
                                                    align="right"
                                                >
                                                    &nbsp;
                                                </td>
                                                <td align="right">
                                                    {{ formatAsCurrency(total_unit_transactions.total_net, 2) }}
                                                </td>
                                                <td align="right">
                                                    {{ formatAsCurrency(total_unit_transactions.total_tax, 2) }}
                                                </td>
                                                <td align="right">
                                                    {{ formatAsCurrency(total_unit_transactions.total_gross, 2) }}
                                                </td>
                                            </tr>
                                        </template>
                                    </v-data-table>
                                </div>
                            </div>
                        </div>
                        <!--                        <table class="data-grid data-tbls" cellpadding="3" cellspacing="0" border="0">-->
                        <!--                            <thead>-->
                        <!--                            <tr class="fieldDescription">-->
                        <!--                                <td width="10%">Line Number</td>-->
                        <!--                                <td width="20%">Description</td>-->
                        <!--                                <td width="12%">Account Code</td>-->
                        <!--                                <td width="12%" align="center">Reading From</td>-->
                        <!--                                <td width="12%" align="center">Reading To</td>-->
                        <!--                                <td width="12%" align="right">Net Amount</td>-->
                        <!--                                <td width="12%" align="right">Tax Amount</td>-->
                        <!--                                <td width="12%" align="right">Gross Amount</td>-->
                        <!--                            </tr>-->
                        <!--                            </thead>-->
                        <!--                            <tbody>-->
                        <!--                            <tr v-for="(data, index) in unit_transaction_list" :key="index">-->
                        <!--                                <td>{{ data.line_number }}</td>-->
                        <!--                                <td>-->
                        <!--                                    <input type="text" :maxlength="40" name="description" class="c-input" v-model="data.description" value="data.description">-->
                        <!--                                </td>-->
                        <!--                                <td>{{ data.account_code }}</td>-->
                        <!--                                <td align="center">{{ data.last_reading_date }}</td>-->
                        <!--                                <td align="center">{{ data.reading_date }}</td>-->
                        <!--                                <td v-if="data.line_number == 1" align="right">-->
                        <!--                                    <a title="Show details" @click="dialogPreviousBreakdown = true"> {{ formatAsCurrency(data.net_amount,2) }} </a>-->
                        <!--                                </td>-->
                        <!--                                <td v-else align="right">{{ formatAsCurrency(data.net_amount,2) }}</td>-->
                        <!--                                <td align="right">{{ formatAsCurrency(data.tax_amount,2) }}</td>-->
                        <!--                                <td align="right">{{ formatAsCurrency(data.gross_amount,2) }}</td>-->
                        <!--                            </tr>-->
                        <!--                            <tr class="highlight">-->
                        <!--                                <td colspan="5" align="right">&nbsp;</td>-->
                        <!--                                <td align="right">{{ formatAsCurrency(total_unit_transactions.total_net,2) }}</td>-->
                        <!--                                <td align="right">{{ formatAsCurrency(total_unit_transactions.total_tax,2) }}</td>-->
                        <!--                                <td align="right">{{ formatAsCurrency(total_unit_transactions.total_gross,2) }}</td>-->
                        <!--                            </tr>-->
                        <!--                            </tbody>-->

                        <!--                        </table>-->
                        <br /><br />
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        v-if="!is_vacant"
                        depressed
                        small
                        @click="generateInvoiceReading(data)"
                        >PROCESS
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="saveReading()"
                        >Skip
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF CREATE CONFIRMATION DIALOG -->

        <!-- SHOW IMPORT CONFIRMATION DIALOG -->
        <v-dialog
            v-model="dialogImportConfirmation"
            v-for="(reading, index) in raw_readings"
            :key="reading.meter_id"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <!--                    <v-icon color="warning">warning</v-icon>  -->
                    Generate Invoice
                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogImportConfirmation = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial; padding: 0px 5px"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr class="fieldDescription">
                                <td width="30%">Property</td>
                                <td width="30%">Lease</td>
                                <td width="15%">Meter Number</td>
                                <td
                                    width="10%"
                                    align="center"
                                >
                                    Reading Date
                                </td>
                                <td
                                    width="10%"
                                    align="right"
                                >
                                    Reading
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(data, index) in raw_readings"
                                :key="index"
                            >
                                <td>{{ data.property_name }}</td>
                                <td v-if="data.show_allocated">
                                    <div v-for="unit in data.unit_allocated">
                                        {{ unit.lease_code }} - {{ unit.lease_name }}
                                    </div>
                                </td>
                                <td v-else>{{ data.lease_code }} - {{ data.lease_name }}</td>
                                <td>{{ data.meter_number }}</td>
                                <td align="center">{{ data.reading_date }}</td>
                                <td align="right">{{ data.reading_present }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="showOptionConfirmation()"
                        >PROCESS
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="showProcessAll()"
                        >PROCESS - ALL
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="dialogImportConfirmation = false"
                        >Cancel
                    </v-btn>
                    <!--                    <v-btn color="primary" v-if="!is_vacant" depressed small @click="generateReading()">Generate Invoice</v-btn>-->
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF IMPORT CONFIRMATION DIALOG -->

        <!-- SHOW INVOICE OPTIONS DIALOG -->
        <v-dialog
            v-model="dialogOptionConfirmation"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <!--                    <v-icon color="warning">warning</v-icon>  -->
                    Generate Invoice
                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogOptionConfirmation = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div
                        v-if="is_vacant"
                        style="margin: 10"
                        color="warning"
                    >
                        <v-icon color="warning">warning</v-icon>
                        Unit allocated on this meter number is vacant.
                        <div class="page-form">
                            <!-- PROPERTY -->
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                <strong>Property:</strong>
                                            </td>
                                            <td class="required">&nbsp;</td>
                                            <td>{{ new_property_label }}</td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                            <!-- METER NUMBER -->
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                <strong>Meter Number:</strong>
                                            </td>
                                            <td class="required">&nbsp;</td>
                                            <td>{{ meter_number }}</td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                            <!-- METER TYPE -->
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                <strong>Meter Type:</strong>
                                            </td>
                                            <td class="required">&nbsp;</td>
                                            <td>{{ meter_type }}</td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                            <!-- DESCRIPTION -->
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                <strong>Description:</strong>
                                            </td>
                                            <td class="required">&nbsp;</td>
                                            <td>{{ meter_description }}</td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                    <div
                        v-else
                        class="body c8-page"
                        style="min-height: initial; padding: 10px"
                    >
                        <div
                            class="body c8-page"
                            style="height: auto; min-height: initial; padding: 10px"
                        >
                            <v-card-text>
                                <div class="page-form">
                                    <div class="page-list">
                                        <div class="c8-page-table">
                                            <v-data-table
                                                :headers="readingHistoryImportHeaders"
                                                :items="last_five_readings"
                                                :sort-by="['reading_id']"
                                                :search="searchReading"
                                                hide-default-footer
                                                class="elevation-1"
                                                dense
                                            >
                                                <template v-slot:top>
                                                    <v-toolbar
                                                        flat
                                                        color="#3489A1"
                                                        height="30px;"
                                                        class="payment-list-header"
                                                    >
                                                        <v-toolbar-title
                                                            style="font-size: 12px; color: #ffffff; font-weight: bold"
                                                            >Last 5 Readings
                                                        </v-toolbar-title>
                                                    </v-toolbar>
                                                </template>
                                                <template v-slot:item="{ item }">
                                                    <tr
                                                        v-if="item.temp_reading == 1"
                                                        style="background-color: rgb(255 161 36)"
                                                    >
                                                        <td>{{ item.reading_date }}</td>
                                                        <td class="right">{{ item.reading_present }}</td>
                                                        <td class="right">{{ item.reading_consumption }}</td>
                                                        <td class="right">
                                                            {{ formatAsCurrency(item.reading_unit_charges, 4) }}
                                                        </td>
                                                        <td class="right">
                                                            {{ formatAsCurrency(item.reading_total_unit_charges, 2) }}
                                                        </td>
                                                        <td class="right">
                                                            {{ formatAsCurrency(item.reading_additional_charges, 2) }}
                                                        </td>
                                                    </tr>
                                                    <tr v-else>
                                                        <td>{{ item.reading_date }}</td>
                                                        <td class="right">{{ item.reading_present }}</td>
                                                        <td class="right">{{ item.reading_consumption }}</td>
                                                        <td class="right">
                                                            {{ formatAsCurrency(item.reading_unit_charges, 4) }}
                                                        </td>
                                                        <td class="right">
                                                            {{ formatAsCurrency(item.reading_total_unit_charges, 2) }}
                                                        </td>
                                                        <td class="right">
                                                            {{ formatAsCurrency(item.reading_additional_charges, 2) }}
                                                        </td>
                                                    </tr>
                                                </template>
                                            </v-data-table>
                                        </div>
                                    </div>
                                </div>
                            </v-card-text>
                        </div>
                        <div class="page-form">
                            <v-row class="form-row"></v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Property
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span>{{ new_property_label }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Transaction Date
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        v-model="transaction_date"
                                        :error_msg="error_msg"
                                    >
                                    </cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Create Interim Tax Invoice
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        v-model="is_generate"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Due Date
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        v-model="invoice_due_date"
                                        :error_msg="error_msg"
                                    >
                                    </cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Send to Tenant
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        v-model="send_email"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                    <br />
                                    <v-span
                                        v-if="send_email == 0 && is_generate == 0"
                                        style="color: #00baf2 !important"
                                        >{{ tenant_email }}
                                    </v-span>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="send_email == 0 && is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >CC to {{ portfolio_manager_label }}
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle v-model="send_cc">
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <!--                            <v-row class="form-row" v-if="is_generate == 0">-->
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Attachment
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <!--                                    <cirrus-single-upload-button :edit_form="true"-->
                                    <!--                                                                 :id="getIdOfUploadButton(new Date().getTime() + Math.random())"-->
                                    <!--                                                                 v-model="attachment_file"-->
                                    <!--                                                                 accept_type="pdf">-->
                                    <!--                                    </cirrus-single-upload-button>-->
                                    <div
                                        class="pdf-upload"
                                        style="max-width: 300px"
                                    >
                                        <div
                                            class="tile"
                                            @click="$refs.fileAttach.click()"
                                            v-bind:class="{ loading: pdfUploading }"
                                        >
                                            <span
                                                class="text-uppercase"
                                                v-if="pdfUploading"
                                            >
                                                <v-icon>mdi-spin mdi-loading</v-icon>
                                                uploading
                                            </span>
                                            <span
                                                v-else-if="!pdfUploading && attachment_file.length === 0"
                                                class="text-uppercase"
                                            >
                                                Drag and drop files here...
                                            </span>

                                            <span
                                                v-else-if="
                                                    !pdfUploading &&
                                                    attachment_file.length > 0 &&
                                                    attachment_file[0].name !== null
                                                "
                                            >
                                                {{ attachment_file[0].name }}
                                            </span>
                                        </div>
                                        <input
                                            type="file"
                                            ref="fileAttach"
                                            accept="application/pdf"
                                            hidden
                                            multiple
                                            @input="handleAttachmentUpload()"
                                            id="attachmentsUpload"
                                        />
                                    </div>
                                </v-col>
                            </v-row>

                            <br />
                            <br />
                            <v-tabs v-model="tabLease">
                                <v-tab
                                    v-for="item in lease_list"
                                    :key="item.value"
                                    dense
                                >
                                    {{ item.value }}
                                </v-tab>
                            </v-tabs>
                            <v-card
                                class="section-toolbar titleHeader"
                                text
                                tile
                            >
                                <v-card-actions style="padding-right: 20px; margin-top: -10px">
                                    <h6
                                        class="title font-weight-black"
                                        style="padding-left: 10px"
                                    >
                                        Reading Details
                                    </h6>
                                    &nbsp&nbsp
                                    <v-spacer></v-spacer>
                                    <!-- ACTION BUTTONS - START -->
                                    <v-btn
                                        x-small
                                        data-tooltip="Save Changes"
                                        class="v-step-save-1-button"
                                        icon
                                        @click="updateDescription()"
                                    >
                                        <!-- <v-icon light color="green">save</v-icon>-->
                                        <v-icon>done</v-icon>
                                    </v-btn>
                                    <v-btn
                                        x-small
                                        data-tooltip="Refresh"
                                        class="v-step-refresh-button"
                                        icon
                                        @click="refreshDescription()"
                                    >
                                        <v-icon>refresh</v-icon>
                                    </v-btn>
                                </v-card-actions>
                            </v-card>
                            <div class="page-list">
                                <div class="c8-page-table">
                                    <v-data-table
                                        :headers="readingListAddReading"
                                        :items="unit_transaction_list"
                                        :search="searchReading"
                                        :loading="loadingReadingList"
                                        loading-text="Loading... Please Wait..."
                                        hide-default-footer
                                        class="elevation-1"
                                        dense
                                    >
                                        <template v-slot:item="{ item }">
                                            <tr>
                                                <td>{{ item.line_number }}</td>
                                                <td>
                                                    <input
                                                        type="text"
                                                        :maxlength="40"
                                                        name="description"
                                                        class="c-input"
                                                        v-model="item.description"
                                                        value="item.description"
                                                    />
                                                </td>
                                                <td>{{ item.account_code }}</td>
                                                <td align="center">{{ item.last_reading_date }}</td>
                                                <td align="center">{{ item.reading_date }}</td>
                                                <td
                                                    v-if="item.line_number == 1"
                                                    align="right"
                                                >
                                                    <a
                                                        title="Show details"
                                                        @click="dialogPreviousBreakdown = true"
                                                    >
                                                        {{ formatAsCurrency(item.net_amount, 2) }}
                                                    </a>
                                                </td>
                                                <td
                                                    v-else
                                                    align="right"
                                                >
                                                    {{ formatAsCurrency(item.net_amount, 2) }}
                                                </td>
                                                <td align="right">{{ formatAsCurrency(item.tax_amount, 2) }}</td>
                                                <td align="right">{{ formatAsCurrency(item.gross_amount, 2) }}</td>
                                            </tr>
                                        </template>
                                        <template v-slot:body.append>
                                            <tr class="highlight">
                                                <td
                                                    colspan="5"
                                                    align="right"
                                                >
                                                    &nbsp;
                                                </td>
                                                <td align="right">
                                                    {{ formatAsCurrency(total_unit_transactions.total_net, 2) }}
                                                </td>
                                                <td align="right">
                                                    {{ formatAsCurrency(total_unit_transactions.total_tax, 2) }}
                                                </td>
                                                <td align="right">
                                                    {{ formatAsCurrency(total_unit_transactions.total_gross, 2) }}
                                                </td>
                                            </tr>
                                        </template>
                                    </v-data-table>
                                </div>
                            </div>
                        </div>
                        <br /><br />
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        v-if="!is_vacant"
                        depressed
                        small
                        @click="generateInvoiceImportReading(import_data_index)"
                        >PROCESS
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="saveImportReading(import_data_index)"
                        >Skip
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF INVOICE OPTIONS DIALOG -->

        <!-- SHOW INVOICE OPTIONS DIALOG -->
        <v-dialog
            v-model="dialogProcessAll"
            max-width="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <!--                    <v-icon color="warning">warning</v-icon>  -->
                    Generate Invoice
                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogProcessAll = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div
                        class="body c8-page"
                        style="min-height: initial; padding: 10px"
                    >
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Transaction Date
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        v-model="transaction_date"
                                        :error_msg="error_msg"
                                    >
                                    </cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Create Interim Tax Invoice
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        v-model="is_generate"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Due Date
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        v-model="invoice_due_date"
                                        :error_msg="error_msg"
                                    >
                                    </cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Send to Tenant
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        v-model="send_email"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="send_email == 0 && is_generate == 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >CC to {{ portfolio_manager_label }}
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <v-btn-toggle v-model="send_cc">
                                        <v-btn
                                            small
                                            text
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        v-if="!is_vacant"
                        depressed
                        small
                        @click="generateInvoiceImportReadingAll()"
                        >PROCESS
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="dialogProcessAll = false"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF INVOICE OPTIONS DIALOG -->

        <!-- SHOW PROCESS ALL RESULT DIALOG -->
        <v-dialog
            v-model="dialogProcessAllResult"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Result Summary
                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogProcessAllResult = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <div
                    class="body no-padding"
                    style="height: auto; min-height: initial; padding: 0px 5px"
                >
                    <table
                        class="data-grid data-tbls"
                        cellpadding="3"
                        cellspacing="0"
                        border="0"
                    >
                        <thead>
                            <tr class="fieldDescription">
                                <td width="30%">Meter Number</td>
                                <td width="30%">Property</td>
                                <td width="30%">Lease</td>
                                <td width="10%">Invoice No.</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(data, index) in process_all_results"
                                :key="index"
                            >
                                <td>{{ data.meter_number }}</td>
                                <td>{{ data.property_name }}</td>
                                <td
                                    v-if="data.withError"
                                    colspane="3"
                                    style="color: #f44336 !important"
                                >
                                    <i>{{ data.errorMessage }}</i>
                                </td>
                                <td v-if="!data.withError">{{ data.lease_name }}</td>
                                <td v-if="!data.withError && data.invoice_number != ''">
                                    <a
                                        title="Download Invoice"
                                        @click="downloadInvoice(data)"
                                        >{{ data.invoice_number }}</a
                                    >
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </v-card>
        </v-dialog>
        <!-- END OF PROCESS ALL RESULT DIALOG -->

        <!-- SHOW DELETE DIALOG -->
        <v-dialog
            v-model="dialogDelete"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-text>
                    <p class="c8-padding-1">{{ deleteMessage }}</p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="deleteRowConfirmed()"
                        >Yes
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogDelete = false"
                        >No
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF DELETE DIALOG -->

        <!-- SHOW BREAKDOWN PREVIOUS READING DIALOG -->
        <v-dialog
            v-model="dialogPreviousBreakdown"
            max-width="600"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Meter Reading Details</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogPreviousBreakdown = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <div
                    class="body c8-page"
                    style="height: auto; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="5"
                                md="5"
                                class="form-label"
                                >Last Reading Date
                            </v-col>
                            <v-col
                                xs="12"
                                sm="7"
                                md="7"
                                class="form-input"
                            >
                                <v-col>{{ previous_record.last_reading_date }}</v-col>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="5"
                                md="5"
                                class="form-label"
                                >Last Reading
                            </v-col>
                            <v-col
                                xs="12"
                                sm="7"
                                md="7"
                                class="form-input"
                            >
                                <v-col>{{ previous_record.last_reading }}</v-col>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="5"
                                md="5"
                                class="form-label"
                                >New Reading Date
                            </v-col>
                            <v-col
                                xs="12"
                                sm="7"
                                md="7"
                                class="form-input"
                            >
                                <v-col>{{ previous_record.new_reading_date }}</v-col>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="5"
                                md="5"
                                class="form-label"
                                >New Reading
                            </v-col>
                            <v-col
                                xs="12"
                                sm="7"
                                md="7"
                                class="form-input"
                            >
                                <v-col>{{ previous_record.new_reading }}</v-col>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="5"
                                md="5"
                                class="form-label"
                                >Consumption
                            </v-col>
                            <v-col
                                xs="12"
                                sm="7"
                                md="7"
                                class="form-input"
                            >
                                <v-col>{{ previous_record.new_consumption }}</v-col>
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        text
                        small
                        @click="dialogPreviousBreakdown = false"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF BREAKDOWN PREVIOUS READING DIALOG -->

        <!-- SHOW UPDATE INITIAL READING DIALOG -->
        <v-dialog
            v-model="dialogUpdateInitialReading"
            max-width="700"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <span>Update Initial Reading</span>
                    <a
                        href="#"
                        class="dialog-close"
                        @click="dialogUpdateInitialReading = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>

                <div
                    class="body c8-page"
                    style="height: 200; min-height: initial; padding: 10px"
                >
                    <div class="page-form">
                        <v-row class="form-row"></v-row>
                        <!-- LAST READING DATE -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Last Reading Date
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    v-model="update_last_reading_date"
                                    :error_msg="error_msg"
                                >
                                </cirrus-icon-date-picker>
                            </v-col>
                        </v-row>
                        <!-- INITIAL READING -->
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Initial Reading
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    v-model="update_initial_reading"
                                    dense
                                />
                            </v-col>
                        </v-row>
                    </div>
                </div>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-1-button"
                        @click="updateInitialReading()"
                        color="primary"
                        dark
                        right
                        small
                    >
                        UPDATE
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- END OF UPDATE INITIAL READING DIALOG -->
    </div>
</template>

<script>
import Vue from 'vue';
import SuiVue from 'semantic-ui-vue';
import Multiselect from 'vue-multiselect';
import vSelect from 'vue-select';
import { mapActions, mapMutations, mapState } from 'vuex';
import XLSX from 'xlsx';
import axios from 'axios';
import global_mixins from '../../../../plugins/mixins';
import moment from 'moment/moment';

Vue.use(SuiVue);
Vue.component('v-select', vSelect);
Vue.component('multiselect', Multiselect);

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    data() {
        return {
            asset_domain: this.$assetDomain,
            loading_page_setting: false,
            loading_setting: false,
            loading_setting_meter: false,
            loading_setting_charges: false,
            loading_setting_allocation: false,
            loading_setting_reading: false,
            loading_progressBar: false,
            progressBar: 0,

            template_tab: 'tab-1',
            pdfUploading: false,
            attachments: [],
            file: '',
            files: [],
            tenant_email: '',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            error_import_msg: {},
            error_import_msg2: [],
            reloadComponents: 0,
            search_type: 0,
            form_mode: 0,
            new_meter: 0,
            window_size: {
                x: 0,
                y: 0,
            },

            property_meter_list: [],
            property_meter_list_all: [],
            property_code: '',
            property_unit_list: [],

            selectedPropertyCode: [],

            chargesHeaders: [
                { text: 'Charges Type', value: 'charges_type_description', sortable: false, width: '20%' },
                { text: 'Description', value: 'charges_description', sortable: false, width: '25%' },
                { text: 'Account', value: 'charges_account_code_desc', sortable: false, width: '25%' },
                { text: 'Amount', value: 'charges_amount', sortable: false, width: '20%', align: 'end' },
                { text: 'Delete', value: 'delete_action', sortable: false, width: '10%', align: 'end' },
            ],
            charges_items: [],
            charges_items_new: [],

            allocationHeaders: [
                { text: 'Unit', value: 'allocation_unit', sortable: false, width: '25%' },
                { text: 'Percentage (%)', value: 'allocation_percentage', sortable: false, width: '25%', align: 'end' },
                { text: 'Delete', value: 'delete_action', sortable: false, width: '50%', align: 'end' },
            ],
            allocation_items: [],
            allocation_items_new: [],

            readingHeaders: [
                { text: 'Date', value: 'reading_date', sortable: false, width: '10%' },
                { text: 'Invoice Number', value: 'invoice_number', sortable: false, width: '10%' },
                { text: 'Files', value: 'generate_support_docs', sortable: false, width: '5%' },
                { text: 'Reading', value: 'reading_present', sortable: false, align: 'end', width: '10%' },
                { text: 'Consumption', value: 'reading_consumption', sortable: false, align: 'end', width: '10%' },
                { text: 'Unit Price', value: 'reading_unit_charges', sortable: false, align: 'end', width: '10%' },
                { text: 'Charges', value: 'reading_total_unit_charges', sortable: false, align: 'end', width: '11%' },
                {
                    text: 'Additional Charges',
                    value: 'reading_additional_charges',
                    sortable: false,
                    align: 'end',
                    width: '11%',
                },
                { text: 'Total (Gross)', value: 'invoice_amt', sortable: false, align: 'end', width: '13%' },
                { text: 'Action', value: 'delete_action', sortable: false, width: '10%', align: 'end' },
            ],
            readingHistoryHeaders: [
                { text: 'Date', value: 'reading_date', sortable: false, width: '10%' },
                { text: 'Invoice Number', value: 'invoice_number', sortable: false, width: '10%' },
                { text: 'Files', value: 'generate_support_docs', sortable: false, width: '5%' },
                { text: 'Reading', value: 'reading_present', sortable: false, align: 'end', width: '10%' },
                { text: 'Consumption', value: 'reading_consumption', sortable: false, align: 'end', width: '10%' },
                { text: 'Unit Price', value: 'reading_unit_charges', sortable: false, align: 'end', width: '10%' },
                { text: 'Charges', value: 'reading_total_unit_charges', sortable: false, align: 'end', width: '11%' },
                {
                    text: 'Additional Charges',
                    value: 'reading_additional_charges',
                    sortable: false,
                    align: 'end',
                    width: '11%',
                },
                { text: 'Total (Gross)', value: 'invoice_amt', sortable: false, align: 'end', width: '13%' },
            ],
            readingHistoryImportHeaders: [
                { text: 'Date', value: 'reading_date', sortable: false, width: '10%' },
                { text: 'Reading', value: 'reading_present', sortable: false, align: 'end', width: '10%' },
                { text: 'Consumption', value: 'reading_consumption', sortable: false, align: 'end', width: '10%' },
                { text: 'Unit Price', value: 'reading_unit_charges', sortable: false, align: 'end', width: '10%' },
                { text: 'Charges', value: 'reading_total_unit_charges', sortable: false, align: 'end', width: '13%' },
                {
                    text: 'Additional Charges',
                    value: 'reading_additional_charges',
                    sortable: false,
                    align: 'end',
                    width: '13%',
                },
            ],
            reading_items: [],
            reading_items_new: [],
            edit_meter_form: false,
            edit_charges_form: false,
            edit_allocation_form: false,
            edit_reading_form: false,
            showReadingHistory: false,

            searchCharges: '',
            searchAllocation: '',
            searchReading: '',

            meter_id: '',
            meter_number: '',
            meter_type: '',
            meter_description: '',
            meter_charges: '',
            account_code: '',
            account_description: '',
            meter_type_description: '',
            is_generate_report: 0,
            old_is_generate_report: 0,
            generate_report: '',
            old_meter_number: '',
            old_meter_type: '',
            old_meter_description: '',
            old_meter_charges: '',
            old_account_code: '',

            dialogCharges: false,
            modal_charges_title: '',
            charges_button_label: '',
            charge_update: false,
            charge_add: false,
            charges_id: '',
            charges_type: '',
            charges_description: '',
            charges_amount: '',
            charges_account_code: '',
            selected_charges_account_code: [],

            dialogAllocation: false,
            modal_allocation_title: '',
            allocation_button_label: '',
            allocation_update: false,
            allocation_add: false,
            allocation_id: '',
            allocation_unit: '',
            allocation_percentage: '',

            dialogReading: false,
            modal_reading_title: '',
            reading_button_label: '',
            reading_date: '',
            reading_present: '',
            reading_invoice: 0,
            initial_reading: '',

            dialogChargesBreakdown: false,
            // chargesBreakdownHeaders:[
            //     {text: 'Charges Type', value: 'reading_charges_type_desc', sortable: false, width: '15%'},
            //     {text: 'Description', value: 'reading_charges_description', sortable: false, width: '25%'},
            //     {text: 'Account', value: 'reading_charges_account_desc', sortable: false, width: '25%'},
            //     {text: 'No. of Days', value: 'reading_charges_days', sortable: false, width: '10%', align: 'end'},
            //     {text: 'Amount', value: 'reading_charges_amount', sortable: false, width: '10%', align: 'end'},
            //     {text: 'Total Amount', value: 'reading_charges_total', sortable: false, width: '15%', align: 'end'},
            // ],
            charges_breakdown_items: [],
            totalAdditionalCharges: 0,
            unitChargesDisplay: false,
            // searchChargesBreakdown:"",

            dialogImportReading: false,
            import_reading_data: [],

            isSelecting: false,
            file_name: '',
            isRemove: false,
            file_attached: '',

            // dialogInvoiceReading: false,
            // invoice_from_date: "",
            // invoice_to_date: "",

            dialogConfirmation: false,
            is_vacant: false,
            attachment_file: [],
            upload_file: '',

            raw_readings: [],
            dialogImportConfirmation: false,
            send_email: 1,
            send_cc: 1,
            is_generate: 1,
            dialogOptionConfirmation: false,
            transaction_date: '',
            invoice_due_date: '',
            new_reading_data: [],
            new_transactions: [],
            import_data_index: '',
            new_property_label: '',
            new_lease_label: '',
            lease_list: [],
            unit_lease_import: '',
            total_transactions: [],
            total_unit_transactions: [],
            unit_transaction: [],
            unit_transaction_list: [],

            tabLease: '',
            dialogProcessAll: false,
            dialogProcessAllResult: false,
            process_all_results: [],
            count_process_all: 0,
            dialogDelete: false,
            deleteMessage: '',
            deleteRowID: '',
            deleteRowType: '',
            searchMeterType: '',

            previous_record: [],
            last_five_readings: [],
            dialogPreviousBreakdown: false,

            dialogUpdateInitialReading: false,
            update_last_reading_date: '',
            update_initial_reading: '',
            update_reading_id: '',

            readingListAddReading: [
                { text: 'Line Number', value: 'line_number', sortable: false, width: '10%' },
                { text: 'Description', value: 'description', sortable: false, width: '10%' },
                { text: 'Account Code', value: 'account_code', sortable: false, width: '10%' },
                { text: 'Reading From', value: 'last_reading_date', sortable: false, align: 'center', width: '10%' },
                { text: 'Reading To', value: 'reading_date', sortable: false, align: 'center', width: '10%' },
                { text: 'Net Amount', value: 'net_amount', sortable: false, align: 'end', width: '10%' },
                { text: 'Tax Amount', value: 'tax_amount', sortable: false, align: 'end', width: '10%' },
                { text: 'Gross Amount', value: 'gross_amount', sortable: false, align: 'end', width: '10%' },
            ],
            loadingReadingList: false,
            modifiedDescription: [],
            readingLoading: false,
            currency_symbol: '$',
            portfolio_manager_label: 'Portfolio Manager',
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.fetchPropertyList();
        this.fetchUtilityMeterTypeList();
        this.fetchUtilityChargesTypeList();
        this.fetchAccountList();
        this.fetchAccountGroupList();
        this.loadCountryDefaults();
    },
    computed: {
        ...mapState([
            'user_type',
            'cirrus8_api_url',
            'dd_property_list',
            'dd_meter_type_list',
            'dd_charges_type_list',
            'dd_account_list',
            'dd_account_group_list',
        ]),
        orderLastFiveReadings: function () {
            return _.orderBy(this.last_five_readings, ['reading_id'], ['asc']);
        },
    },
    methods: {
        currentDate() {
            let current = new Date();
            return current.getDate() / current.getMonth() + 1 / current.getFullYear();
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },

        ...mapActions([
            'fetchPropertyList',
            'fetchUtilityMeterTypeList',
            'fetchUtilityChargesTypeList',
            'fetchAccountList',
            'fetchAccountGroupList',
        ]),
        ...mapMutations([
            'SET_PROPERTY_CODE',
            'SET_PUSH_DD_PROPERTY_LIST',
            'SET_DD_METER_TYPE_LIST',
            'SET_DD_CHARGES_TYPE_LIST',
            'SET_DD_ACCOUNT_LIST',
        ]),
        loadCountryDefaults: function () {
            // this.loading_page_setting = true;

            var form_data = new FormData();
            // form_data.append('country', country);
            let api_url = this.cirrus8_api_url + 'admin/country_defaults/load';
            this.$api.post(api_url, form_data).then((response) => {
                this.error_server_msg2 = response.data.validation_errors;
                // this.loading_page_setting = false;
                this.defaultCurrency = response.data.default['currency_symbol'];
            });
        },
        addNewMeter: function () {
            this.error_msg = [];
            this.template_tab = 'tab-1';
            this.reading_date = '';
            this.initial_reading = '';
            this.charges_items_new = [];
            this.allocation_items_new = [];
            this.allocation_percentage = '';
            this.allocation_items = [];
            this.meter_id = '';
            this.meter_number = '';
            this.meter_type = '';
            this.meter_description = '';
            this.meter_charges = '';
            this.account_code = '';
            this.is_generate_report = 1;
            this.selected_account_code = '';

            this.form_mode = 1;
            this.new_meter = 1;
            this.search_type = 1;
        },
        cancelMeter: function () {
            this.form_mode = 0;
            this.new_meter = 0;
            this.search_type = 0;
            this.clearMeterForm();
        },
        clearMeterForm: function () {
            this.meter_id = '';
            this.meter_number = '';
            this.meter_type = '';
            this.meter_description = '';
            this.meter_charges = '';
            this.account_code = '';
            this.charges_items = '';
            this.allocation_items = '';
            this.reading_items = '';
            this.account_description = '';
            this.meter_type_description = '';
            this.is_generate_report = 1;
            this.old_is_generate_report = '';
            this.old_meter_number = '';
            this.old_meter_type = '';
            this.old_meter_description = '';
            this.old_meter_charges = '';
            this.old_account_code = '';
        },
        forceRerender: function () {
            this.search_type = 0;
            this.loadPropertyMeterList();
        },
        loadPropertyMeterList: function () {
            this.loading_setting = true;

            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('form_mode', this.form_mode);
            formData.append('property_code', this.property_code);
            formData.append('meter_number', this.meter_number);
            formData.append('meter_id', this.meter_id);
            formData.append('meter_type', this.meter_type);
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/property-meter-list', formData)
                .then((response) => {
                    if (response.data.meterData.length > 0) {
                        this.property_meter_list = response.data.meterData;
                        this.property_meter_list_all = response.data.meterData;
                    } else {
                        this.property_meter_list = '';
                        this.property_meter_list_all = '';
                    }
                    this.property_unit_list = response.data.propertyUnitList;
                    this.portfolio_manager_label = this.ucwords(response.data.portfolioManagerLabel);
                    this.loading_setting = false;
                });
        },
        selectMeter: function (meterData) {
            this.template_tab = 'tab-2';
            this.search_type = 1;
            this.new_meter = 0;
            this.edit_meter_form = false;
            this.edit_charges_form = false;
            this.edit_allocation_form = false;
            this.edit_readng_form = false;
            this.meter_id = meterData.meter_id;
            this.meter_number = meterData.meter_number;
            this.meter_type = meterData.meter_type;
            this.meter_description = meterData.meter_description;
            this.meter_charges = meterData.meter_charges;
            this.account_code = meterData.account_code;
            this.charges_items = meterData.additonal_charges;
            this.allocation_items = meterData.meter_allocation;
            this.reading_items = meterData.meter_readings;
            this.last_five_readings = meterData.last_five_readings;
            this.account_description = meterData.account_description;
            this.generate_report = meterData.generate_report;
            this.meter_type_description = meterData.meter_type_description;
            this.is_generate_report = parseInt(meterData.is_generate_report);
            this.selected_account_code = {
                fieldGroup: meterData.account_group,
                fieldKey: meterData.account_code,
                fieldValue: meterData.account_name,
                field_group: meterData.account_group,
                field_key: meterData.account_code,
                field_value: meterData.account_name,
                value: meterData.account_code,
                label: meterData.account_code + ' - ' + meterData.account_name,
            };

            this.old_meter_number = meterData.meter_number;
            this.old_meter_type = meterData.meter_type;
            this.old_meter_description = meterData.meter_description;
            this.old_meter_charges = meterData.meter_charges;
            this.old_account_code = meterData.account_code;
            this.old_is_generate_report = parseInt(meterData.is_generate_report);
            this.loadReadingDetails();
        },

        resetMeterForm: function () {
            this.edit_meter_form = false;
            this.meter_number = this.old_meter_number;
            this.meter_type = this.old_meter_type;
            this.meter_description = this.old_meter_description;
            this.meter_charges = this.old_meter_charges;
            this.account_code = this.old_account_code;
            this.is_generate_report = this.old_is_generate_report;
        },
        saveMeterForm: function () {
            let validate = true;
            this.error_msg = [];
            if (this.meter_number == '') {
                var meter_number_error = 'You have not specified a valid meter number.';
                this.$noty.error(meter_number_error);
                this.error_msg.push({
                    id: 'meter_number',
                    message: meter_number_error,
                });
                validate = false;
            }

            if (this.meter_number != '' && this.verifyMeterNumber(this.meter_number)) {
                var meter_number_validate_error =
                    'Please use only alpha numeric, space, dashes and underscores for the meter number.';
                this.error_msg.push({
                    id: 'meter_number',
                    message: meter_number_validate_error,
                });
                this.$noty.error(meter_number_validate_error);
                validate = false;
            }
            if (this.meter_type == '' || this.meter_type == null) {
                var meter_type_error = 'You have not specified a valid meter type.';
                this.error_msg.push({
                    id: 'meter_type',
                    message: meter_type_error,
                });
                this.$noty.error(meter_type_error);
                validate = false;
            }
            if (this.meter_description == '') {
                var meter_description_error = 'You have not specified a valid description.';
                this.error_msg.push({
                    id: 'meter_description',
                    message: meter_description_error,
                });
                this.$noty.error(meter_description_error);
                validate = false;
            }
            if (!$.isNumeric(this.meter_charges) && this.meter_charges != '' && this.meter_charges != null) {
                var meter_charges_error = 'Please use only numeric characters for unit price.';
                this.error_msg.push({
                    id: 'meter_charges',
                    message: meter_charges_error,
                });
                this.$noty.error(meter_charges_error);
                validate = false;
            }
            if (this.account_code == '' || this.account_code == null) {
                var account_code_error = 'You have not specified a valid account code.';
                this.error_msg.push({
                    id: 'account_code',
                    message: account_code_error,
                });
                this.$noty.error(account_code_error);
                validate = false;
            }
            if (this.reading_date == '' && this.form_mode == 1) {
                var reading_date_error = 'You have not specified a valid reading date.';
                this.error_msg.push({
                    id: 'reading_date',
                    message: reading_date_error,
                });
                this.$noty.error(reading_date_error);
                validate = false;
            }
            if (this.initial_reading == '' && this.form_mode == 1) {
                var initial_reading_error = 'You have not specified a valid reading.';
                this.error_msg.push({
                    id: 'initial_reading',
                    message: initial_reading_error,
                });
                this.$noty.error(initial_reading_error);
                validate = false;
            }
            if (this.initial_reading != '' && this.form_mode == 1 && !$.isNumeric(this.initial_reading)) {
                var initial_reading_validate_error = 'Please use only numeric characters for initial reading.';
                this.error_msg.push({
                    id: 'initial_reading',
                    message: initial_reading_validate_error,
                });
                this.$noty.error(initial_reading_validate_error);
                validate = false;
            }
            if (this.allocation_items_new.length == 0 && this.form_mode == 1) {
                var allocation_items_new_error = 'You have not specified an allocation.';
                this.error_msg.push({
                    id: 'allocation_items_new',
                    message: allocation_items_new_error,
                });
                this.$noty.error(allocation_items_new_error);
                validate = false;
            }
            if (validate) {
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('property_code', this.property_code);
                formData.append('meter_number', this.meter_number);
                formData.append('meter_type', this.meter_type);
                formData.append('meter_description', this.meter_description);
                formData.append('meter_charges', this.meter_charges);
                formData.append('account_code', this.account_code);
                formData.append('form_mode', this.form_mode);
                formData.append('meter_id', this.meter_id);
                formData.append('is_generate_report', this.is_generate_report);
                if (this.form_mode == 1) {
                    formData.append('reading_date', this.reading_date);
                    formData.append('initial_reading', this.initial_reading);
                    formData.append('charges_data', JSON.stringify(this.charges_items_new));
                    formData.append('allocation_data', JSON.stringify(this.allocation_items_new));
                }
                this.$api
                    .post(this.cirrus8_api_url + 'api/utility-meter/save-meter-details', formData)
                    .then((response) => {
                        if (response.data.status == 'success') {
                            if (this.form_mode == 0) {
                                this.loadMeterDetails();
                                this.$noty.success('Meter Updated.');
                            } else {
                                this.form_mode = 0;
                                this.meter_id = response.data.new_meter_id;
                                this.loadPropertyMeterList();
                                this.loadChargesDetails();
                                this.loadAllocationDetails();
                                this.loadReadingDetails();
                                this.loadMeterDetails();
                                this.$noty.success('Meter Added.');
                            }
                        } else {
                            this.$noty.error(response.data.validation_errors);
                            this.error_msg.push({
                                id: 'meter_number',
                                message: response.data.validation_errors,
                            });
                        }
                        this.loading_setting_meter = false;
                    });
            }
        },

        loadMeterDetails: function () {
            this.edit_meter_form = false;
            this.loading_setting_meter = true;
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.property_code);
            formData.append('meter_number', this.meter_number);
            formData.append('meter_type', this.meter_type);
            formData.append('meter_id', this.meter_id);
            this.$api.post(this.cirrus8_api_url + 'api/utility-meter/property-meter', formData).then((response) => {
                if (response.data.length > 0) {
                    this.meter_id = response.data[0].meter_id;
                    this.meter_number = response.data[0].meter_number;
                    this.meter_type = response.data[0].meter_type;
                    this.meter_description = response.data[0].meter_description;
                    this.meter_charges = response.data[0].meter_charges;
                    this.account_code = response.data[0].account_code;
                    this.meter_type_description = response.data[0].meter_type_description;
                    this.account_description = response.data[0].account_description;
                    this.generate_report = response.data[0].generate_report;

                    this.is_generate_report = parseInt(response.data[0].is_generate_report);
                    this.old_meter_number = response.data[0].meter_number;
                    this.old_meter_type = response.data[0].meter_type;
                    this.old_meter_description = response.data[0].meter_description;
                    this.old_meter_charges = response.data[0].meter_charges;
                    this.old_account_code = response.data[0].account_code;
                    this.old_is_generate_report = parseInt(response.data[0].is_generate_report);
                }
                this.loading_setting_meter = false;
            });
        },
        loadChargesDetails: function () {
            this.edit_charges_form = false;
            this.loading_setting_charges = true;
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('meter_id', this.meter_id);
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/property-meter-charges', formData)
                .then((response) => {
                    this.charges_items = response.data;
                    this.loading_setting_charges = false;
                });
        },
        loadAllocationDetails: function () {
            this.edit_allocation_form = false;
            this.loading_setting_allocation = true;
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('meter_id', this.meter_id);
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/property-meter-allocation', formData)
                .then((response) => {
                    this.allocation_items = response.data;
                    this.loading_setting_allocation = false;
                });
        },
        loadReadingDetails: function () {
            this.edit_reading_form = false;
            this.loading_setting_reading = true;
            this.reading_items = [];
            this.last_five_readings = [];
            this.readingLoading = true;
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('meter_id', this.meter_id);
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/property-meter-reading', formData)
                .then((response) => {
                    this.reading_items = response.data.readingItems;
                    this.last_five_readings = response.data.lastFiveReadings;
                    this.currency_symbol = response.data.currencySymbol;
                    this.is_generate_report = parseInt(response.data.isGenerateReport);
                    this.readingLoading = false;
                });
            // this.loading_setting_reading = false;
            setTimeout(() => (this.loading_setting_reading = false), 2000);
        },

        showAddCharges: function () {
            this.charge_add = true;
            this.modal_charges_title = 'Add Additional Charges';
            this.charges_button_label = 'ADD CHARGES';
            this.dialogCharges = true;
        },
        showUpdateCharges: function (chargesData) {
            this.modal_charges_title = 'Update Additional Charges';
            this.charges_button_label = 'UPDATE CHARGES';
            this.charges_id = chargesData.charges_id;
            this.charges_description = chargesData.charges_description;
            this.charges_type = chargesData.charges_type;
            this.charges_amount = chargesData.charges_amount;
            this.charges_account_code = chargesData.charges_account_code;
            this.selected_charges_account_code = {
                fieldGroup: chargesData.charges_account_group,
                fieldKey: chargesData.charges_account_code,
                fieldValue: chargesData.charges_account_name,
                field_group: chargesData.charges_account_group,
                field_key: chargesData.charges_account_code,
                field_value: chargesData.charges_account_name,
                value: chargesData.charges_account_code,
                label: chargesData.charges_account_code + ' - ' + chargesData.charges_account_name,
            };
            if (this.$refs.refChargesAccountCode != undefined) {
                this.$refs.refChargesAccountCode.select_val = this.selected_charges_account_code;
            }
            this.charge_update = true;
            this.dialogCharges = true;
        },

        closeChargesModal: function () {
            if (this.charges_type != '') {
                this.$refs.refChargesType.select_val = '';
                this.charges_type = '';
            }

            if (this.charges_account_code != '') {
                this.$refs.refChargesAccountCode.select_val = '';
                this.charges_account_code = '';
                this.selected_charges_account_code = '';
            }
            this.charges_description = '';
            this.charges_amount = '';
            this.charge_add = false;
            this.charge_update = false;
            this.dialogCharges = false;
        },
        saveCharges: function () {
            let validate = true;
            this.error_msg = [];
            if (this.charges_type == '' || this.charges_type == null) {
                var charges_type_error = 'You have not specified a valid charges type.';
                this.$noty.error(charges_type_error);
                validate = false;
            }
            if (this.charges_description == '') {
                var charges_description_error = 'You have not specified a valid description.';
                this.$noty.error(charges_description_error);
                validate = false;
            }
            if (
                (!$.isNumeric(this.charges_amount) && this.charges_amount != '' && this.charges_amount != null) ||
                this.charges_amount == ''
            ) {
                var charges_amount_error = 'Please use only numeric characters for amount.';
                this.$noty.error(charges_amount_error);
                validate = false;
            }

            if (this.charges_account_code == '' || this.charges_account_code == null) {
                var charges_account_code_error = 'You have not specified a valid account code.';
                this.$noty.error(charges_account_code_error);
                validate = false;
            }
            if (validate) {
                var successMessage = '';
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('meter_id', this.meter_id);
                formData.append('property_code', this.property_code);
                formData.append('charges_type', this.charges_type);
                formData.append('charges_description', this.charges_description);
                formData.append('charges_amount', this.charges_amount);
                formData.append('charges_account_code', this.charges_account_code);
                if (this.charge_add) {
                    formData.append('form_mode', 1);
                    successMessage = 'Additional Charges Added.';
                }
                if (this.charge_update) {
                    formData.append('form_mode', 0);
                    formData.append('charges_id', this.charges_id);
                    successMessage = 'Additional Charges Updated.';
                }

                this.$api
                    .post(this.cirrus8_api_url + 'api/utility-meter/utility-save-charges', formData)
                    .then((response) => {
                        if (response.data.status == 'success') {
                            this.loadChargesDetails();
                            this.$noty.success(successMessage);
                            this.closeChargesModal();
                        }
                    });
            }
        },
        addTempCharges: function () {
            let validate = true;
            this.error_msg = [];

            if (this.charges_type == '' || this.charges_type == null) {
                var charges_type_error = 'You have not specified a valid charges type.';
                this.$noty.error(charges_type_error);
                validate = false;
            }
            if (this.charges_description == '') {
                var charges_description_error = 'You have not specified a valid description.';
                this.$noty.error(charges_description_error);
                validate = false;
            }
            if (
                (!$.isNumeric(this.charges_amount) && this.charges_amount != '' && this.charges_amount != null) ||
                this.charges_amount == ''
            ) {
                var charges_amount_error = 'Please use only numeric characters for amount.';
                this.$noty.error(charges_amount_error);
                validate = false;
            }

            if (this.charges_account_code == '' || this.charges_account_code == null) {
                var charges_account_code_error = 'You have not specified a valid account code.';
                this.$noty.error(charges_account_code_error);
                validate = false;
            }
            if (validate) {
                let rowID = this.charges_items_new.length;
                let temp_charges = [
                    {
                        row_id: rowID + 1,
                        charges_type: this.charges_type,
                        charges_type_description: this.getNameFromList(
                            this.dd_charges_type_list,
                            this.charges_type,
                            false,
                        ),
                        charges_description: this.charges_description,
                        charges_account_code: this.charges_account_code,
                        charges_account_code_desc: this.getNameFromList(
                            this.dd_account_list,
                            this.charges_account_code,
                            false,
                        ),
                        charges_amount: this.charges_amount,
                    },
                ];
                this.charges_items_new = this.charges_items_new.concat(temp_charges);
                this.closeChargesModal();
            }
        },

        showAddAllocation: function () {
            let totalAlloc = 0;
            if (this.new_meter == 1) totalAlloc = this.computeTotalAlloc(this.allocation_items_new);
            else {
                totalAlloc = this.computeTotalAlloc(this.allocation_items);
            }

            if (totalAlloc == 100) {
                this.$noty.error('Unit allocation already 100%');
            } else {
                this.allocation_add = true;
                this.allocation_update = false;
                this.dialogAllocation = true;
                this.modal_allocation_title = 'Add Allocation';
                this.allocation_button_label = 'ADD ALLOCATION';
            }
        },

        computeTotalAlloc: function (arrayData) {
            let tAlloc = 0;
            if (arrayData.length > 0) {
                for (var i = 0; arrayData.length > i; i++) {
                    tAlloc += parseFloat(arrayData[i].allocation_percentage);
                }
            }
            return tAlloc;
        },

        showUpdateAllocation: function (allocationData) {
            this.allocation_update = true;
            this.allocation_add = false;
            this.dialogAllocation = true;
            this.modal_allocation_title = 'Update Allocation';
            this.allocation_button_label = 'UPDATE ALLOCATION';
            this.allocation_id = allocationData.allocation_id;
            this.allocation_unit = allocationData.allocation_unit;
            this.allocation_percentage = allocationData.allocation_percentage;
        },
        closeAllocationModal: function () {
            this.allocation_unit = '';
            this.allocation_percentage = '';
            this.allocation_add = false;
            this.allocation_update = false;
            this.dialogAllocation = false;
        },
        saveAllocation: function () {
            let validate = true;
            this.error_msg = [];
            if (this.allocation_percentage) {
                if (this.allocation_percentage > 100) {
                    var allocation_percentage_error =
                        'You have not used a valid percentage figure (' + this.allocation_percentage + '%).';
                    this.error_msg.push({
                        id: 'allocation_percentage',
                        message: allocation_percentage_error,
                    });
                    this.$noty.error(allocation_percentage_error);
                    validate = false;
                }
                let totalAlloc = 0;
                totalAlloc = this.computeTotalAlloc(this.allocation_items) + parseFloat(this.allocation_percentage);
                if (totalAlloc > 100 && this.allocation_add) {
                    var allocation_percentage_error = 'Allocation total is more than 100%.';
                    this.error_msg.push({
                        id: 'allocation_percentage',
                        message: allocation_percentage_error,
                    });
                    this.$noty.error(allocation_percentage_error);
                    validate = false;
                }
            }

            if (
                this.allocation_percentage == '' ||
                (this.allocation_percentage != '' && !$.isNumeric(this.allocation_percentage))
            ) {
                var allocation_percentage_validate_error = 'Please use only numeric characters for percentage.';
                this.error_msg.push({
                    id: 'allocation_percentage',
                    message: allocation_percentage_validate_error,
                });
                this.$noty.error(allocation_percentage_validate_error);
                validate = false;
            }

            if (this.allocation_unit == '' || this.allocation_unit == null) {
                var allocation_unit_error = 'You have not specified a valid unit.';
                this.error_msg.push({
                    id: 'allocation_unit',
                    message: allocation_unit_error,
                });
                this.$noty.error(allocation_unit_error);
                validate = false;
            }
            if (validate) {
                var successMessage = '';
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('meter_id', this.meter_id);
                formData.append('property_code', this.property_code);
                formData.append('allocation_unit', this.allocation_unit);
                formData.append('allocation_percentage', this.allocation_percentage);
                if (this.allocation_add) {
                    formData.append('form_mode', '1');
                    successMessage = 'Allocation Added.';
                }
                if (this.allocation_update) {
                    formData.append('form_mode', '0');
                    formData.append('allocation_id', this.allocation_id);
                    successMessage = 'Allocation Updated.';
                }

                this.$api
                    .post(this.cirrus8_api_url + 'api/utility-meter/utility-save-allocation', formData)
                    .then((response) => {
                        if (response.data.status == 'success') {
                            this.loadAllocationDetails();
                            this.$noty.success(successMessage);
                            this.closeAllocationModal();
                        } else {
                            this.$noty.error(response.data.validation_errors);
                        }
                    });
            }
        },
        addTempAllocation: function () {
            let validate = true;
            if (this.allocation_percentage) {
                if (this.allocation_percentage > 100) {
                    var allocation_percentage_error =
                        'You have not used a valid percentage figure (' + this.allocation_percentage + '%).';
                    this.error_msg.push({
                        id: 'allocation_percentage',
                        message: allocation_percentage_error,
                    });
                    this.$noty.error(allocation_percentage_error);
                    validate = false;
                }
                let totalAlloc = 0;
                totalAlloc = this.computeTotalAlloc(this.allocation_items) + parseFloat(this.allocation_percentage);
                if (totalAlloc > 100 && this.allocation_add) {
                    var allocation_percentage_error = 'Allocation total is more than 100%.';
                    this.error_msg.push({
                        id: 'allocation_percentage',
                        message: allocation_percentage_error,
                    });
                    this.$noty.error(allocation_percentage_error);
                    validate = false;
                }
            }

            if (
                this.allocation_percentage == '' ||
                (this.allocation_percentage != '' && !$.isNumeric(this.allocation_percentage))
            ) {
                var allocation_percentage_validate_error = 'Please use only numeric characters for percentage.';
                this.error_msg.push({
                    id: 'allocation_percentage',
                    message: allocation_percentage_validate_error,
                });
                this.$noty.error(allocation_percentage_validate_error);
                validate = false;
            }

            if (this.allocation_unit == '' || this.allocation_unit == null) {
                var allocation_unit_error = 'You have not specified a valid unit.';
                this.error_msg.push({
                    id: 'allocation_unit',
                    message: allocation_unit_error,
                });
                this.$noty.error(allocation_unit_error);
                validate = false;
            }
            if (validate) {
                let rowID = this.allocation_items_new.length;
                let go = true;
                if (rowID > 0) {
                    for (let a = 0; a < rowID; a++) {
                        if (this.allocation_items_new[a].allocation_unit == this.allocation_unit) {
                            go = false;
                            var allocation_unit_error = 'You have entered a unit already in use for this meter setup';
                            this.error_msg.push({
                                id: 'allocation_unit',
                                message: allocation_unit_error,
                            });
                            this.$noty.error(allocation_unit_error);
                        }
                    }
                }
                if (go) {
                    let temp_allocation = [
                        {
                            row_id: rowID + 1,
                            allocation_unit: this.allocation_unit,
                            allocation_percentage: this.allocation_percentage,
                        },
                    ];
                    this.allocation_items_new = this.allocation_items_new.concat(temp_allocation);
                    this.closeAllocationModal();
                }
            }
        },

        showAddReading: function () {
            this.modal_reading_title = 'Add Meter Reading';
            this.reading_button_label = 'ADD READING';
            if (this.reading_date != '') {
                if (this.$refs.refReadingDate) this.$refs.refReadingDate.date_str = '';
                this.reading_date = '';
            }
            this.reading_present = '';
            this.dialogReading = true;
        },
        showAdditionalChargesBreakdown: function (breakdownData, type) {
            this.charges_breakdown_items = '';
            // this.charges_breakdown_items = breakdownData;
            this.unitChargesDisplay = false;
            let unitChargeList = [];
            let additionalChargeList = [];
            let overallUntiTotal = 0;
            let overallChargesTotal = 0;
            $.each(breakdownData, function (item, value) {
                if (value.reading_charges_type == 'MAIN') {
                    unitChargeList.push(value);
                    overallUntiTotal += parseFloat(value.reading_charges_total);
                } else {
                    additionalChargeList.push(value);
                    overallChargesTotal += parseFloat(value.reading_charges_total);
                }
            });
            if (type == 'UNIT') {
                this.charges_breakdown_items = unitChargeList;
                this.totalAdditionalCharges = overallUntiTotal;
                this.unitChargesDisplay = true;
            } else {
                this.charges_breakdown_items = additionalChargeList;
                this.totalAdditionalCharges = overallChargesTotal;
                this.unitChargesDisplay = false;
            }

            this.dialogChargesBreakdown = true;
        },
        closeReadingModal: function () {
            this.reading_id = '';
            if (this.reading_date != '') {
                if (this.$refs.refReadingDate) this.$refs.refReadingDate.date_str = '';
            }
            this.reading_date = '';
            this.reading_present = '';
            this.reading_invoice = '';
            this.is_generate = 1;
            this.send_email = 1;
            this.send_cc = 1;
            this.dialogReading = false;
        },
        closeCreateMeterReading: function () {
            this.lease_list = [];
            this.is_generate = 1;
            this.send_email = 1;
            this.send_cc = 1;
            this.invoice_due_date = '';
            this.transaction_date = '';
            this.attachment_file = [];
            this.tabLease = '';
            this.dialogConfirmation = false;
        },
        addReading: function () {
            let validate = true;

            this.total_transactions = [];
            this.total_unit_transactions = [];
            this.unit_transaction = [];
            this.unit_transaction_list = [];
            this.modifiedDescription = [];
            this.error_msg = [];
            if (this.reading_date == '') {
                var reading_date_error = 'You have not specified a valid reading date.';
                this.error_msg.push({
                    id: 'reading_date',
                    message: reading_date_error,
                });
                this.$noty.error(reading_date_error);
                validate = false;
            }
            if (this.reading_present == '' || this.reading_present == 0) {
                var reading_present_error = 'You have not specified a valid reading.';
                this.error_msg.push({
                    id: 'reading_present',
                    message: reading_present_error,
                });
                this.$noty.error(reading_present_error);
                validate = false;
            }
            if (this.reading_present != '' && !$.isNumeric(this.reading_present)) {
                var reading_present_validate_error = 'Please use only numeric characters for reading.';
                this.error_msg.push({
                    id: 'reading_present',
                    message: reading_present_validate_error,
                });
                this.$noty.error(reading_present_validate_error);
                validate = false;
            }
            if (validate) {
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('meter_number', this.meter_number);
                formData.append('meter_id', this.meter_id);
                formData.append('property_code', this.property_code);
                formData.append('reading_present', this.reading_present);
                formData.append('reading_date', this.reading_date);
                formData.append('reading_invoice', this.reading_invoice);
                formData.append('meter_charges', this.meter_charges);
                formData.append('form_mode', '1');
                if (this.reading_invoice == 1) {
                    this.$api
                        .post(this.cirrus8_api_url + 'api/utility-meter/utility-add-reading', formData)
                        .then((response) => {
                            if (response.data.status == 'success') {
                                this.is_vacant = response.data.vacant;
                                this.unit_transaction = response.data.unit_alloc_trans;
                                this.new_property_label = response.data.property_label;
                                this.new_lease_label = response.data.lease_label;
                                this.lease_list = response.data.lease_list;
                                this.total_transactions = response.data.total_transactions;
                                this.transaction_date = this.currentDate;
                                this.previous_record = response.data.previous_record;
                                this.dialogConfirmation = true;
                            } else {
                                this.$noty.error(response.data.validation_errors);
                            }
                        });
                } else {
                    this.saveReading();
                }
            }
        },
        saveReading: function (rowMeter) {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('meter_number', this.meter_number);
            formData.append('meter_id', this.meter_id);
            formData.append('property_code', this.property_code);
            formData.append('reading_present', this.reading_present);
            formData.append('reading_date', this.reading_date);
            formData.append('reading_invoice', this.reading_invoice);
            formData.append('meter_charges', this.meter_charges);
            formData.append('form_mode', '1');
            if (rowMeter) {
                formData.append('from_import', true);
                formData.append('import_data', JSON.stringify(rowMeter));
            }
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/utility-save-reading', formData)
                .then((response) => {
                    if (response.data.status == 'success') {
                        this.loadReadingDetails();
                        this.$noty.success('Reading Added.');
                        this.dialogConfirmation = false;
                        this.closeReadingModal();
                        this.closeCreateMeterReading();

                        this.loadNextRawDataToProcess();
                    } else {
                        this.$noty.error(response.data.validation_errors);
                    }
                });
        },

        saveImportReading: function (dataID) {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('meter_number', this.meter_number);
            formData.append('meter_id', this.meter_id);
            formData.append('property_code', this.property_code);
            formData.append('reading_present', this.reading_present);
            formData.append('reading_date', this.reading_date);
            formData.append('reading_invoice', this.reading_invoice);
            formData.append('meter_charges', this.meter_charges);
            formData.append('form_mode', '1');
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/utility-save-reading', formData)
                .then((response) => {
                    if (response.data.status == 'success') {
                        this.$noty.success('Reading Added.');
                        this.dialogConfirmation = false;
                        this.closeReadingModal();
                        this.closeCreateMeterReading();
                        this.loadNextRawDataToProcess(dataID);
                    } else {
                        this.$noty.error(response.data.validation_errors);
                    }
                });
        },
        generateInvoiceReading: function () {
            let validate = true;
            this.error_msg = [];
            if (this.invoice_due_date == '' && this.is_generate == 0) {
                var invoice_due_date_error = 'You have not specified a valid date.';
                this.error_msg.push({
                    id: 'invoice_due_date',
                    message: invoice_due_date_error,
                });
                this.$noty.error(invoice_due_date_error);
                validate = false;
            }
            if (this.transaction_date == '') {
                var invoice_due_date_error = 'You have not specified a valid date.';
                this.error_msg.push({
                    id: 'transaction_date',
                    message: transaction_date_error,
                });
                this.$noty.error(transaction_date_error);
                validate = false;
            }
            let file_attached = null;
            if (this.attachment_file !== null) {
                file_attached = this.attachment_file[0];
            }
            if (validate) {
                this.loading_page_setting = true;
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('meter_number', this.meter_number);
                formData.append('meter_id', this.meter_id);
                formData.append('property_code', this.property_code);
                formData.append('reading_present', this.reading_present);
                formData.append('reading_date', this.reading_date);
                formData.append('reading_invoice', this.reading_invoice);
                formData.append('meter_charges', this.meter_charges);
                formData.append('form_mode', '1');
                formData.append('file_attached', file_attached);
                formData.append('send_email', this.send_email);
                formData.append('invoice_due_date', this.invoice_due_date);
                formData.append('transaction_date', this.transaction_date);
                formData.append('modified_description', JSON.stringify(this.modifiedDescription));
                formData.append('is_generate', this.is_generate);
                this.$api
                    .post(
                        this.cirrus8_api_url + 'api/with-file-upload/utility-meter/utility-generate-invoice-2',
                        formData,
                    )
                    .then((response) => {
                        if (response.data.status == 'success') {
                            if (this.is_generate == 0 || response.data.is_generate_supp_docs == 1) {
                                this.toInvoice(response.data);
                                // this.loading_page_setting = false;
                            } else {
                                this.loadReadingDetails();
                                // this.dialogConfirmation = false;
                                this.closeReadingModal();
                                this.closeCreateMeterReading();
                                this.$noty.success('Reading successfully processed');
                                // this.loading_page_setting = false;
                            }
                        } else {
                            this.$noty.error(response.data.validation_errors);
                            // this.loading_page_setting = false;
                        }
                    });
            }
            setTimeout(() => (this.loading_page_setting = false), 2000);
        },
        async toInvoice(data) {
            const batch_nos = await data.batch_number;
            let counter = 0;
            for (const batch_no of batch_nos) {
                let document_ids = data.document_ids;
                let readingID = data.reading_id;
                let generateSupportDocs = data.is_generate_supp_docs;
                let generateInvoice = this.is_generate == 0 ? 1 : 0;
                this.error_main_form = data.validation_errors;
                let sendEmail = 0;
                if (this.send_email == 0) {
                    sendEmail = 1;
                }
                let sendCc = 0;
                if (this.send_cc == 0) {
                    sendCc = 1;
                }
                let readingNotes = data.reading_notes;
                var formData = new FormData();
                formData.append('batchNumber', batch_no);
                formData.append('sendEmail', sendEmail);
                formData.append('sendCc', sendCc);
                formData.append('documentAPTag', '0');
                formData.append('documentIDs', document_ids);
                formData.append('energytec', '1');
                formData.append('readingID', readingID);
                formData.append('readingInvoice', '1');
                formData.append('readingNotes', readingNotes[counter]);
                formData.append('generateReadingInvoice', generateInvoice);
                formData.append('generateSupportDocs', generateSupportDocs);
                let url = '?action=generateInterimInvoice&module=ar&command=generateTaxInvoice';
                const req = await axios.post(url, formData).then((response) => {
                    let object = response.data;
                    if (typeof object === 'string' && generateInvoice == 1) {
                        this.$noty.error('Nothing was processed');
                    } else {
                        let obj = response.data;
                        if (obj[1]) {
                            if (this.send_email == 0) {
                                this.$noty.error(obj[1]);
                            }
                        }
                        if (obj[2]) {
                            if (this.send_email == 0) {
                                this.$noty.success(obj[2]);
                            }
                        }
                        this.$noty.success('Reading successfully processed');
                    }

                    counter++;
                });
            }
            this.loadReadingDetails();
            this.closeReadingModal();
            this.closeCreateMeterReading();
        },
        async generateInvoiceImportReading(dataID) {
            let validate = true;
            this.error_msg = [];
            if (this.invoice_due_date == '' && this.is_generate == 0) {
                var invoice_due_date_error = 'You have not specified a valid due date.';
                this.error_msg.push({
                    id: 'invoice_due_date',
                    message: invoice_due_date_error,
                });
                this.$noty.error(invoice_due_date_error);
                validate = false;
            }
            if (this.transaction_date == '') {
                var invoice_due_date_error = 'You have not specified a valid transaction date.';
                this.error_msg.push({
                    id: 'transaction_date',
                    message: transaction_date_error,
                });
                this.$noty.error(transaction_date_error);
                validate = false;
            }
            let file_attached = null;
            if (this.attachment_file !== null) {
                file_attached = this.attachment_file[0];
            }
            if (validate) {
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('meter_number', this.raw_readings[dataID].meter_number);
                formData.append('meter_id', this.raw_readings[dataID].meter_id);
                formData.append('property_code', this.raw_readings[dataID].property_code);
                formData.append('reading_present', this.raw_readings[dataID].reading_present);
                formData.append('reading_date', this.raw_readings[dataID].reading_date);
                formData.append('reading_invoice', this.raw_readings[dataID].reading_invoice);
                formData.append('meter_charges', this.meter_charges);
                formData.append('form_mode', '1');
                formData.append('file_attached', file_attached);
                formData.append('send_email', this.send_email);
                formData.append('invoice_due_date', this.invoice_due_date);
                formData.append('transaction_date', this.transaction_date);
                formData.append('modified_description', JSON.stringify(this.modifiedDescription));
                formData.append('is_generate', this.is_generate);
                const res = await this.$api
                    .post(
                        this.cirrus8_api_url + 'api/with-file-upload/utility-meter/utility-generate-invoice-2',
                        formData,
                    )
                    .then((response) => {
                        if (response.data.status == 'success') {
                            if (this.is_generate == 0 || response.data.is_generate_supp_docs == 1) {
                                let batch_nos = response.data.batch_number;
                                for (let b = 0; batch_nos.length > b; b++) {
                                    let document_ids = response.data.document_ids;
                                    let readingID = response.data.reading_id;
                                    this.error_main_form = response.data.validation_errors;
                                    let sendEmail = 0;
                                    if (this.send_email == 0) {
                                        sendEmail = 1;
                                    }
                                    let sendCc = 0;
                                    if (this.send_cc == 0) {
                                        sendCc = 1;
                                    }
                                    let readingNotes = response.data.reading_notes;
                                    let generateSupportDocs = response.data.is_generate_supp_docs;
                                    let generateInvoice = this.is_generate == 0 ? 1 : 0;
                                    var formData = new FormData();
                                    formData.append('batchNumber', batch_nos[b]);
                                    formData.append('sendEmail', sendEmail);
                                    formData.append('sendCc', sendCc);
                                    formData.append('documentAPTag', '0');
                                    formData.append('documentIDs', document_ids);
                                    formData.append('energytec', '1');
                                    formData.append('readingID', readingID);
                                    formData.append('readingInvoice', '1');
                                    formData.append('readingNotes', readingNotes[b]);
                                    formData.append('generateReadingInvoice', generateInvoice);
                                    formData.append('generateSupportDocs', generateSupportDocs);
                                    let url = '?action=generateInterimInvoice&module=ar&command=generateTaxInvoice';
                                    axios.post(url, formData).then((response) => {
                                        let object = response.data;
                                        if (typeof object === 'string') {
                                            this.$noty.error('Nothing was processed');
                                        } else {
                                            let obj = response.data;
                                            if (obj[1]) {
                                                if (this.send_email == 0) {
                                                    this.$noty.error(obj[1]);
                                                }
                                            }
                                            if (obj[2]) {
                                                if (this.send_email == 0) {
                                                    this.$noty.success(obj[2]);
                                                }
                                            }
                                            this.loadReadingDetails();
                                            this.dialogConfirmation = false;
                                            this.closeReadingModal();
                                            this.$noty.success('Reading successfully processed');
                                        }
                                        location.href = '#system-message';
                                    });
                                }
                            } else {
                                // this.closeReadingModal();
                                this.$noty.success('Reading successfully processed');
                            }
                            this.raw_readings[dataID].is_processed = true;
                            this.dialogOptionConfirmation = false;
                            this.loadNextRawDataToProcess(dataID);
                        } else {
                            this.$noty.error(response.data.validation_errors);
                        }
                    });
            }
        },

        async generateInterimInvoiceImport(formData, rData) {
            let processData = [];
            let url = '?action=generateInterimInvoice&module=ar&command=generateTaxInvoice';
            const res = await axios.post(url, formData).then((response) => {
                let object = response.data;
                if (typeof object === 'string') {
                    // this.$noty.error("Nothing was processed");
                } else {
                    let obj = response.data;
                    if (obj[0]) {
                        processData.fileID = obj[0];
                        let a = obj[0];
                        processData.invoice_number = a[0]['invoiceNumber'];
                        processData.leaseID = a[0]['leaseID'];
                    }
                }
            });
            return processData;
        },

        generateInvoiceImportReadingAllx: function () {
            let validate = true;
            this.error_msg = [];
            if (this.invoice_due_date == '' && this.is_generate == 0) {
                var invoice_due_date_error = 'You have not specified a valid due date.';
                this.error_msg.push({
                    id: 'invoice_due_date',
                    message: invoice_due_date_error,
                });
                this.$noty.error(invoice_due_date_error);
                validate = false;
            }
            if (this.transaction_date == '') {
                var transaction_date_error = 'You have not specified a valid transaction date.';
                this.error_msg.push({
                    id: 'transaction_date',
                    message: transaction_date_error,
                });
                this.$noty.error(transaction_date_error);
                validate = false;
            }

            if (validate) {
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('raw_readings', JSON.stringify(this.raw_readings));
                formData.append('form_mode', '1');
                formData.append('send_email', this.send_email);
                formData.append('invoice_due_date', this.invoice_due_date);
                formData.append('transaction_date', this.transaction_date);
                formData.append('is_generate', this.is_generate);
                this.$api
                    .post(this.cirrus8_api_url + 'api/utility-meter/utility-generate-invoice-all', formData)
                    .then((response) => {
                        if (response.data.status == 'success') {
                            if (this.is_generate == 0) {
                                let batch_nos = response.data.batch_number;
                                for (let b = 0; batch_nos.length > b; b++) {
                                    let document_ids = response.data.document_ids;

                                    this.error_main_form = response.data.validation_errors;
                                    let sendEmail = 0;
                                    if (this.send_email == 0) {
                                        sendEmail = 1;
                                    }
                                    let sendCc = 0;
                                    if (this.send_cc == 0) {
                                        sendCc = 1;
                                    }
                                    var formData = new FormData();
                                    formData.append('batchNumber', batch_nos[b]);
                                    formData.append('sendEmail', sendEmail);
                                    formData.append('sendCc', sendCc);
                                    formData.append('documentAPTag', '0');
                                    formData.append('documentIDs', document_ids);
                                    formData.append('energytec', '1');
                                    let url = '?action=generateInterimInvoice&module=ar&command=generateTaxInvoice';
                                    axios.post(url, formData).then((response) => {
                                        let object = response.data;
                                        if (typeof object === 'string') {
                                            this.$noty.error('Nothing was processed');
                                        } else {
                                            let obj = response.data;
                                            if (obj[1]) {
                                                this.$noty.error(obj[1]);
                                            }
                                            if (obj[2]) {
                                                this.$noty.success(obj[2]);
                                                this.loadReadingDetails();
                                                this.dialogConfirmation = false;
                                                this.closeReadingModal();
                                            }
                                        }
                                        location.href = '#system-message';
                                    });
                                }
                            } else {
                                // this.closeReadingModal();
                                this.$noty.success('Reading successfully processed');
                            }
                            this.dialogImportConfirmation = false;
                            this.dialogImportReading = false;
                            this.dialogProcessAll = false;
                        } else {
                            this.$noty.error(response.data.validation_errors);
                        }
                    });
            }
        },
        generateInvoiceImportReadingAll: function () {
            this.loading_page_setting = true;
            let validate = true;
            this.error_msg = [];
            this.process_all_results = [];
            if (this.invoice_due_date == '' && this.is_generate == 0) {
                var invoice_due_date_error = 'You have not specified a valid due date.';
                this.error_msg.push({
                    id: 'invoice_due_date',
                    message: invoice_due_date_error,
                });
                this.$noty.error(invoice_due_date_error);
                validate = false;
            }
            if (this.transaction_date == '') {
                var transaction_date_error = 'You have not specified a valid transaction date.';
                this.error_msg.push({
                    id: 'transaction_date',
                    message: transaction_date_error,
                });
                this.$noty.error(transaction_date_error);
                validate = false;
            }

            if (validate) {
                this.loading_progressBar = true;
                this.processAll(this.raw_readings);
            }
        },
        async processAll(rawData) {
            this.dialogImportConfirmation = false;
            this.dialogImportReading = false;
            this.dialogProcessAll = false;
            let rowProcessed = 1;
            let successProcess = false;
            let processData = rawData;
            let withError = false;
            for (let rData of processData) {
                if (rowProcessed > 100) {
                    rowProcessed = 100;
                }
                this.progressBar = (rowProcessed / rawData.length) * 100;
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('raw_readings', JSON.stringify(rData));
                formData.append('form_mode', '1');
                formData.append('send_email', this.send_email);
                formData.append('invoice_due_date', this.invoice_due_date);
                formData.append('transaction_date', this.transaction_date);
                formData.append('is_generate', this.is_generate);
                let invoiceNumber = '';
                const req = await this.$api
                    .post(this.cirrus8_api_url + 'api/utility-meter/utility-generate-invoice-all', formData)
                    .then((response) => {
                        if (response.data.status == 'success') {
                            successProcess = true;
                            // if( this.is_generate == 0 ) {
                            this.toInvoiceAll(response.data, rData);
                            rowProcessed++;
                            // }
                            // else{
                            //     // this.closeReadingModal();
                            //     // this.$noty.success("Reading successfully processed");
                            //     this.process_all_results = this.process_all_results.concat(rData);
                            //     rowProcessed++;
                            // }
                        } else {
                            withError = true;
                            rData.withError = true;
                            rData.errorMessage = response.data.validation_errors[0];
                            this.process_all_results = this.process_all_results.concat(rData);
                            this.$noty.error(response.data.validation_errors);
                        }
                    });
            }
            this.finishProcessAll(withError);
        },
        finishProcessAll: function (withError) {
            this.loadPropertyMeterList();
            this.loadReadingDetails();
            this.loading_progressBar = false;
            this.dialogProcessAllResult = true;
            if (withError) {
                this.$noty.warning('Some Meter Reading was not successfully processed');
            } else {
                this.$noty.success('Reading successfully processed. Invoice(s) generated where selected.');
            }
        },
        async toInvoiceAll(data, rData) {
            const batch_nos = await data.batch_number;
            let counter = 0;
            for (const batch_no of batch_nos) {
                let document_ids = data.document_ids;
                let readingID = data.reading_id;
                this.error_main_form = data.validation_errors;
                let sendEmail = 0;
                if (this.send_email == 0) {
                    sendEmail = 1;
                }
                let sendCc = 0;
                if (this.send_cc == 0) {
                    sendCc = 1;
                }
                let readingNotes = data.reading_notes;
                let leases = data.leases;
                let generateInvoice = this.is_generate == 0 ? 1 : 0;
                var formData = new FormData();
                formData.append('batchNumber', batch_no);
                formData.append('sendEmail', sendEmail);
                formData.append('sendCc', sendCc);
                formData.append('documentAPTag', '0');
                formData.append('documentIDs', document_ids);
                formData.append('energytec', '1');
                formData.append('readingID', readingID);
                formData.append('readingInvoice', '1');
                formData.append('readingNotes', readingNotes[counter]);
                formData.append('generateReadingInvoice', generateInvoice);
                const processedData = await this.generateInterimInvoice(formData, rData).then((pData) => {
                    let pRaw = {
                        meter_number: rData.meter_number,
                        property_code: rData.property_code,
                        property_name: rData.property_name,
                        lease_code: pData.leaseID,
                        lease_name: leases[counter],
                        fileID: pData.fileID,
                        invoice_number: pData.invoice_number,
                    };
                    this.process_all_results.push(pRaw);
                });
                counter++;
            }
        },
        async generateInterimInvoice(formData, rData) {
            let processData = [];
            let url = '?action=generateInterimInvoice&module=ar&command=generateTaxInvoice';
            const res = await axios.post(url, formData).then((response) => {
                let object = response.data;
                if (typeof object === 'string') {
                    // this.$noty.error("Nothing was processed");
                } else {
                    let obj = response.data;
                    if (obj[0]) {
                        processData.fileID = obj[0];
                        let a = obj[0];
                        processData.invoice_number = a[0]['invoiceNumber'];
                        processData.leaseID = a[0]['leaseID'];
                    }
                }
            });
            return processData;
        },
        deleteRow: function (rowID, rowType) {
            var formData = new FormData();
            let successMessage = rowType + ' Deleted.';
            formData.append('user_type', this.user_type);
            formData.append('row_type', rowType);
            formData.append('row_id', rowID);
            this.$api.post(this.cirrus8_api_url + 'api/utility-meter/utility-delete', formData).then((response) => {
                if (response.data.status == 'success') {
                    this.$noty.success(successMessage);
                    if (rowType == 'Charges') {
                        this.loadChargesDetails();
                    }
                    if (rowType == 'Allocation') {
                        this.loadAllocationDetails();
                    }
                    if (rowType == 'Meter Reading') {
                        this.loadReadingDetails();
                    }
                    if (rowType == 'Property Meter') {
                        this.forceRerender();
                    }
                } else {
                    this.$noty.error(response.data.validation_errors);
                }
            });
        },
        confirmDelete: function (rowID, rowType, invGenerated) {
            this.deleteRowID = rowID;
            this.deleteRowType = rowType;
            if (rowType == 'Charges') {
                this.deleteMessage = 'Are you sure you want to delete this additional charge?';
            } else if (rowType == 'Allocation') {
                this.deleteMessage = 'Are you sure you want to delete this allocation?';
            } else if (rowType == 'Meter Reading') {
                if (invGenerated == 1)
                    this.deleteMessage =
                        'This reading has been charged to the tenant. Deleting this line will not delete the charge from the tenants account.';
                else {
                    this.deleteMessage = 'Are you sure you want to delete this meter reading?';
                }
            } else if (rowType == 'Property Meter') {
                this.deleteMessage = 'Are you sure you want to delete this utility meter?';
            } else {
                this.deleteMessage = 'Are you sure you want to delete this?';
            }
            this.dialogDelete = true;
        },

        deleteRowConfirmed: function () {
            var formData = new FormData();
            let successMessage = this.deleteRowType + ' Deleted.';
            formData.append('user_type', this.user_type);
            formData.append('row_type', this.deleteRowType);
            formData.append('row_id', this.deleteRowID);
            this.$api.post(this.cirrus8_api_url + 'api/utility-meter/utility-delete', formData).then((response) => {
                if (response.data.status == 'success') {
                    this.$noty.success(successMessage);
                    if (this.deleteRowType == 'Charges') {
                        this.loadChargesDetails();
                    }
                    if (this.deleteRowType == 'Allocation') {
                        this.loadAllocationDetails();
                    }
                    if (this.deleteRowType == 'Meter Reading') {
                        this.loadReadingDetails();
                    }
                    if (this.deleteRowType == 'Property Meter') {
                        this.forceRerender();
                    }
                } else {
                    this.$noty.error(response.data.validation_errors);
                }
            });
            this.dialogDelete = false;
        },

        deleteTempRow: function (rowID, rowType) {
            if (rowType == 'Charges') {
                let newCharges = this.charges_items_new;
                let tempCharges = [];
                $.each(newCharges, function (i, v) {
                    if (v.row_id != rowID) {
                        tempCharges.push(v);
                    }
                });
                this.charges_items_new = tempCharges;
            }
            if (rowType == 'Allocation') {
                let newAllocation = this.allocation_items_new;
                let tempAllocation = [];
                $.each(newAllocation, function (i, v) {
                    if (v.row_id != rowID) {
                        tempAllocation.push(v);
                    }
                });
                this.allocation_items_new = tempAllocation;
            }
            if (rowType == 'Meter Reading') {
                let newReading = this.reading_items_new;
                let tempReading = [];
                $.each(newReading, function (i, v) {
                    if (v.row_id != rowID) {
                        tempReading.push(v);
                    }
                });
                this.reading_items_new = tempReading;
            }
        },
        verifyMeterNumber: function (inputString) {
            var pattern = new RegExp('^[a-zA-Z0-9 _-]+$');
            var isNotValid = !pattern.test(inputString);
            return isNotValid;
        },
        formatAsCurrency: function (amt, dec) {
            amt = parseFloat(amt);
            if (amt) {
                dec = dec || 0;
                if (amt < 0) {
                    amt = amt * -1;
                    amt = amt.toFixed(dec);
                    var _amt = amt.toString().split('.', 2);
                    return (
                        this.currency_symbol +
                        ' (' +
                        _amt[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
                        '.' +
                        _amt[1] +
                        ')'
                    );
                } else {
                    amt = amt.toFixed(dec);
                    var _amt = amt.toString().split('.', 2);
                    return this.currency_symbol + ' ' + _amt[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '.' + _amt[1];
                }
            } else {
                amt = 0;
                amt = amt.toFixed(dec);
                var _amt = amt.toString().split('.', 2);
                return this.currency_symbol + ' ' + _amt[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.') + '.' + _amt[1];
            }
        },

        showImportReading: function () {
            this.removeFile();
            this.error_import_msg = {};
            this.error_import_msg2 = [];
            this.import_reading_data = [];
            this.dialogImportReading = true;
        },
        importReadingData: function () {
            if (this.import_reading_data.length > 0) {
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('reading_data', JSON.stringify(this.import_reading_data));
                this.$api
                    .post(this.cirrus8_api_url + 'api/utility-meter/utility-import-reading', formData)
                    .then((response) => {
                        if (response.data.status == 'success') {
                            this.raw_readings = response.data.for_insert;
                            this.count_process_all = response.data.total_units;
                            this.error_import_msg = {};
                            this.dialogImportConfirmation = true;
                        } else {
                            this.$noty.error(response.data.error_message);
                            this.error_import_msg = response.data.validation_errors;
                        }
                    });
            } else {
                this.$noty.error('No files found.');
            }
        },

        loadNextRawDataToProcess: function (dataID) {
            let data = this.raw_readings;
            let index = dataID + 1;
            this.tabLease = '';
            this.transaction_date = this.currentDate;
            if (data[index] && index < data.length) {
                this.import_data_index = index;
                this.new_reading_data = data[index];
                this.new_property_label = data[index].property_name;
                this.is_vacant = data[index].is_vacant;
                if (!this.is_vacant) {
                    this.new_transactions = data[index].to_process;
                    this.lease_list = data[index].to_process.lease_list;
                } else {
                    this.meter_type = data[index].meter_type;
                    this.meter_description = data[index].meter_description;
                    this.new_transactions = [];
                    this.lease_list = [];
                }

                this.unit_transaction = data[index].to_process.unit_alloc_trans;
                this.total_transactions = data[index].total_transaction;
                this.previous_record = data[index].previous_record;
                this.meter_number = data[index].meter_number;
                this.meter_id = data[index].meter_id;
                this.property_code = data[index].property_code;
                this.reading_present = data[index].reading_present;
                this.reading_date = data[index].reading_date;
                this.reading_invoice = data[index].reading_invoice;
                this.meter_charges = data[index].reading_unit_charges;
                let temp_readings = data[index].last_five_readings.concat(data[index].temp_new_reading_list[index]);
                this.last_five_readings = temp_readings;

                this.send_email = 1;
                this.send_cc = 1;
                this.is_generate = 1;
                this.invoice_due_date = '';
                this.attachment_file = [];
                this.unit_lease_import = '';
                this.unit_transaction_list = [];
                this.total_unit_transactions = [];
                this.dialogOptionConfirmation = true;
            } else {
                this.loadPropertyMeterList();
                this.loadReadingDetails();
                this.dialogImportConfirmation = false;
                this.dialogImportReading = false;
                this.dialogOptionConfirmation = false;
            }
        },
        showOptionConfirmation: function () {
            let data = this.raw_readings;
            let index = 0;
            this.tabLease = '';
            this.transaction_date = this.currentDate;

            this.import_data_index = index;
            this.new_reading_data = data[index];
            this.new_property_label = data[index].property_name;
            this.is_vacant = data[index].is_vacant;
            if (!this.is_vacant) {
                this.new_transactions = data[index].to_process;
                this.lease_list = data[index].to_process.lease_list;
            } else {
                this.meter_type = data[index].meter_type;
                this.meter_description = data[index].meter_description;
                this.new_transactions = [];
                this.lease_list = [];
            }
            this.unit_transaction = data[index].to_process.unit_alloc_trans;
            this.total_transactions = data[index].total_transaction;
            this.previous_record = data[index].previous_record;
            this.meter_number = data[index].meter_number;
            this.meter_id = data[index].meter_id;
            this.property_code = data[index].property_code;
            this.reading_present = data[index].reading_present;
            this.reading_date = data[index].reading_date;
            this.reading_invoice = data[index].reading_invoice;
            this.meter_charges = data[index].reading_unit_charges;
            let temp_readings = data[index].last_five_readings.concat(data[index].temp_new_reading_list[index]);
            this.last_five_readings = temp_readings;
            this.send_email = 1;
            this.send_cc = 1;
            this.is_generate = 1;
            this.invoice_due_date = '';
            this.attachment_file = [];
            if (this.unit_lease_import) {
                this.$refs.refUnitLeaseImport.select_val = '';
            }
            this.unit_lease_import = '';
            this.unit_transaction_list = [];
            this.total_unit_transactions = [];
            this.dialogOptionConfirmation = true;
        },

        getNameFromList: function (arrayVar, value, camelCase, getLabel) {
            if (arrayVar && value) {
                if (camelCase) {
                    var fieldData = arrayVar.find(({ fieldKey }) => fieldKey === value);

                    if (getLabel !== undefined) {
                        return fieldData.label;
                    } else {
                        return fieldData.fieldValue;
                    }
                } else {
                    var fieldData = arrayVar.find(({ field_key }) => field_key === value);

                    if (getLabel !== undefined) {
                        return fieldData.label;
                    } else {
                        return fieldData.field_value;
                    }
                }
            }
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadReading_' + id;
        },

        downloadTemplate: function () {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('property_code', this.property_code);
            // this.$api.post(this.cirrus8_api_url + 'api/utility-meter/utility-download-template', formData, {responseType: 'blob'}).then(response => {
            //     const url = window.URL.createObjectURL(new Blob([response.data]));
            //     const link = document.createElement('a');
            //     link.href = url;
            //     link.setAttribute('download', 'utilityMeterTemplate.xlsx');
            //     document.body.appendChild(link);
            //     link.click();
            //
            // });
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/utility-download-template', formData)
                .then((response) => {
                    let res = response.data;
                    if (res.file && res.file.name && res.file.type && res.file.data) {
                        this.printDownload(res.file.data, res.file.name + moment().unix(), res.file.type);
                    }
                });
        },
        printDownload(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;

            let blob = new Blob([this.printS2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            a.download = name + '.' + format;
            a.click();
        },
        printS2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
        closeDialogImportReading: function () {
            if (this.files.length >= 1) {
                this.$refs.file.value = null;
            }
            this.files = [];
            this.pdfUploading = false;
            this.dialogImportReading = false;
        },
        handleImportUpload: function () {
            this.file = '';
            this.files = [];
            this.$refs.fileUpload = [];
            this.error_import_msg = {};
            this.error_import_msg2 = [];
            this.import_reading_data = [];

            let files = this.$refs.file.files;
            if (files.length > 0) {
                this.files = files;
                this.pdfUploading = true;
                this.onFileChanged(event);
            }
        },
        onFileChanged: function (e) {
            // if(this.upload_file) {
            var f = e.target.files || e.dataTransfer.files;
            if (f.length && f[0].type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                this.file_attached = f[0];
                this.file_name = f[0].name;

                this.file = e.target.files ? e.target.files[0] : null;
                if (this.file) {
                    const reader = new FileReader();

                    reader.onload = (e) => {
                        /* Parse data */
                        const bstr = e.target.result;
                        const wb = XLSX.read(bstr, { type: 'binary', cellText: false, cellDates: true });
                        /* Get first worksheet */
                        const wsname = wb.SheetNames[0];
                        const ws = wb.Sheets[wsname];
                        /* Convert array of arrays */
                        const data = XLSX.utils.sheet_to_json(ws, { header: 1, raw: false, dateNF: 'dd/mm/yyyy' });
                        this.import_reading_data = data;
                    };
                    reader.readAsBinaryString(this.file);
                }
                this.pdfUploading = false;
            } else {
                this.files = [];
                this.$noty.error('Invalid file type. Please upload only excel file.');
                this.$refs.file.value = null;
                this.pdfUploading = false;
            }
        },
        handleAttachmentUpload: function () {
            let attachFiles = this.$refs.fileAttach.files;
            this.attachment_file = this.$refs.fileAttach.files;
            // if (files.length > 0) {
            //     this.pdfUploading = true;
            //     this.onFileChanged(event);
            // }
        },
        getLeaseCode: function (rowID) {
            let leaseRow = this.lease_list[rowID];
            this.tenant_email = '';
            let leaseValue = '';
            if (leaseRow) {
                this.tenant_email = leaseRow.tenant_email;
                leaseValue = leaseRow.value;
            }
            return leaseValue;
        },

        showProcessAll: function () {
            this.send_email = 1;
            this.send_cc = 1;
            this.is_generate = 1;
            this.invoice_due_date = '';
            this.transaction_date = this.currentDate;
            this.dialogProcessAll = true;
        },
        downloadInvoice: function (rawData) {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('rawData', JSON.stringify(rawData));
            formData.append('transaction_date', this.transaction_date);
            formData.append('no_load', true);
            // send email
            this.$api.post(this.cirrus8_api_url + 'api/utility-meter/download-invoice', formData).then((response) => {
                if (response.data.status == 'error') {
                    this.$noty.error(response.data.error_message);
                } else {
                    document.location.href = 'download.php?fileID=' + response.data.fileID;
                    this.$noty.success('Invoice downloaded.');
                }
            });
        },
        downloadTaxInvoice: function (readingData) {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('rawData', JSON.stringify(readingData));
            formData.append('is_generate_report', this.is_generate_report);
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/download-tax-invoice', formData)
                .then((response) => {
                    if (response.data.status == 'error') {
                        this.$noty.error(response.data.error_message);
                    } else {
                        document.location.href = 'download.php?fileID=' + response.data.fileID;
                        this.$noty.success('Invoice downloaded.');
                    }
                });
        },
        downloadSupportingDocument: function (readingData) {
            var formData = new FormData();
            formData.append('user_type', this.user_type);
            formData.append('rawData', JSON.stringify(readingData));
            formData.append('is_generate_report', this.is_generate_report);
            this.$api
                .post(this.cirrus8_api_url + 'api/utility-meter/download-supporting-document', formData)
                .then((response) => {
                    if (response.data.status == 'error') {
                        this.$noty.error(response.data.error_message);
                    } else {
                        document.location.href = 'download.php?fileID=' + response.data.fileID;
                        this.$noty.success('Supporting document downloaded successfully.');
                    }
                });
        },
        removeFile: function () {
            if (this.files.length > 0) {
                this.$refs.file.value = null;
                this.handleImportUpload();
            }
        },

        editReading: function (item) {
            this.update_last_reading_date = item.reading_date;
            this.update_initial_reading = item.reading_present;
            this.update_reading_id = item.reading_id;
            this.dialogUpdateInitialReading = true;
        },
        updateInitialReading: function () {
            let validate = true;
            if (this.update_last_reading_date == '' || this.update_last_reading_date == null) {
                this.$noty.error('You have not specified a valid last reading date.');
                validate = false;
            }
            if (this.update_initial_reading == '') {
                this.$noty.error('You have not specified a valid initial reading.');
                validate = false;
            }
            if (this.update_initial_reading != '' && !$.isNumeric(this.update_initial_reading)) {
                this.$noty.error('Please use only numeric characters for initial reading.');
                validate = false;
            }
            if (validate) {
                var formData = new FormData();
                formData.append('user_type', this.user_type);
                formData.append('meter_id', this.meter_id);
                formData.append('reading_id', this.update_reading_id);
                formData.append('update_initial_reading', this.update_initial_reading);
                formData.append('update_last_reading_date', this.update_last_reading_date);
                this.$api
                    .post(this.cirrus8_api_url + 'api/utility-meter/utility-update-reading', formData)
                    .then((response) => {
                        if (response.data.status == 'success') {
                            this.loadReadingDetails();
                            this.$noty.success('Reading Updated');
                            this.dialogUpdateInitialReading = false;
                        }
                    });
            }
        },
        updateDescription: function () {
            this.loadingReadingList = true;
            let rList = this.unit_transaction_list;
            let leaseCode = this.getLeaseCode(this.tabLease);
            for (var r = 0; r < rList.length; r++) {
                if (rList[r]['description'] != rList[r]['o_description']) {
                    rList[r]['o_description'] = rList[r]['description'];
                    rList[r]['modified'] = 1;
                }
                rList[r]['leaseCode'] = leaseCode;
            }
            let modDesc = this.modifiedDescription;
            modDesc[this.tabLease] = rList;
            this.modifiedDescription = modDesc;
            this.unit_transaction_list = rList;
            this.loadingReadingList = false;
            this.$noty.success('Description updated successfully.');
        },
        refreshDescription: function () {
            this.loadingReadingList = true;
            let rList = this.unit_transaction_list;
            for (var r = 0; r < rList.length; r++) {
                rList[r]['description'] = rList[r]['o_description'];
            }
            this.unit_transaction_list = rList;
            this.loadingReadingList = false;
            this.$noty.success('List has been refreshed.');
        },
        doubleClickForm: function ($type) {
            switch ($type) {
                case 'meter':
                    if (!this.edit_meter_form) {
                        this.edit_meter_form = true;
                        this.edit_form = 1;
                    }
                    // else{
                    //     this.edit_form = 0;
                    //     this.edit_meter_form = false;
                    // }
                    break;
                case 'charges':
                    if (!this.edit_charges_form) {
                        this.edit_charges_form = true;
                        this.edit_form = 1;
                    }
                    // else{
                    //     this.edit_charges_form = false;
                    //     this.edit_form = 0;
                    // }
                    break;
                case 'allocation':
                    if (!this.edit_allocation_form) {
                        this.edit_allocation_form = true;
                        this.edit_form = 1;
                    }
                    // else{
                    //     this.edit_allocation_form = false;
                    //     this.edit_form = 0;
                    // }
                    break;
                case 'reading':
                    if (!this.edit_reading_form) {
                        this.edit_reading_form = true;
                        this.edit_form = 1;
                    }
                    // else{
                    //     this.edit_reading_form = false;
                    //     this.edit_form = 0;
                    // }
                    break;
            }
        },
    },
    watch: {
        // 'dd_property_list' : function(){
        //     if(this.dd_property_list.length > 0 && this.$refs.propertyCodeDropDown && this.property_code != ""){
        //         this.$refs.propertyCodeDropDown.forceChange(this.property_code,this.dd_property_list)
        //     }
        // },
        property_code: function () {
            if (this.property_code) {
                if (this.form_mode == 1) {
                    this.search_type = 1;
                } else {
                    this.search_type = 0;
                }
                this.loadPropertyMeterList();
            } else {
                this.property_code = '';
            }
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        tabLease: function () {
            let leaseCode = this.getLeaseCode(this.tabLease);
            if (leaseCode) {
                this.unit_transaction_list = this.unit_transaction[leaseCode];
                this.total_unit_transactions = this.total_transactions[leaseCode];
            } else {
                this.unit_transaction_list = [];
                this.total_unit_transactions = [];
            }
        },
        unit_lease_import: function () {
            if (this.unit_lease_import) {
                this.unit_transaction_list = this.unit_transaction[this.unit_lease_import];
                this.total_unit_transactions = this.total_transactions[this.unit_lease_import];
            } else {
                this.unit_transaction_list = [];
                this.total_unit_transactions = [];
            }
        },
        searchMeterType: function () {
            let tempMeter = this.property_meter_list_all;
            // Process search input
            if (this.searchMeterType != '' && this.searchMeterType) {
                tempMeter = tempMeter.filter((item) => {
                    return (
                        item.meter_number.toUpperCase().includes(this.searchMeterType.toUpperCase()) ||
                        item.meter_description.toUpperCase().includes(this.searchMeterType.toUpperCase())
                    );
                });
            }

            tempMeter = tempMeter.sort((a, b) => {
                let fa = a.meter_number.toLowerCase(),
                    fb = b.meter_number.toLowerCase();

                if (fa < fb) {
                    return -1;
                }
                if (fa > fb) {
                    return 1;
                }
                return 0;
            });

            this.property_meter_list = tempMeter;
        },
        process_all_results: function () {
            if (this.process_all_results.length === this.count_process_all) {
                this.loading_page_setting = false;
            }
        },
    },
    mixins: [global_mixins],
};
</script>

<style lang="scss" scoped>
.tile-holder {
    position: relative;
    padding: 0 5px 20px;
    border: none;
}

.tile-holder .tile {
    position: relative;
    background-color: #f5f7fa;
    align-self: center;
    text-align: center;
    padding: 40px 10px;
    color: #848ea1;
    font-size: 12px;
    cursor: pointer;
}

#attachmentsUpload {
    position: absolute;
    margin: 0;
    padding: 0;
    width: 300px;
    height: 94px;
    outline: none;
    opacity: 0;
    display: block;
    top: 10px;
    left: 5px;
    cursor: pointer;
}

#importUpload {
    position: absolute;
    margin: 0;
    padding: 0;
    width: 300px;
    height: 94px;
    outline: none;
    opacity: 0;
    display: block;
    top: 10px;
    left: 5px;
    cursor: pointer;
}

.attachments {
    padding: 10px;
}

.pdf-upload {
    position: relative;

    .tile {
        background-color: #f5f7fa;
        align-self: center;
        text-align: center;
        padding: 40px 10px;
        color: #848ea1;
        font-size: 12px;
        cursor: pointer;

        &.loading {
            padding-top: 30px;
        }
    }

    .remove-file {
        text-align: center;
        padding: 10px;

        a {
            color: #c50c0c;
        }
    }
}
</style>
