<template>
    <div class="c8-page">
        <div
            class="page-form"
            style="min-height: 350px"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Company Type:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type"
                        v-model="company_type"
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            All
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            Debtor
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            Owner
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            Supplier
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Show Inactive:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type"
                        v-model="show_active"
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <div class="text-right">
                <cirrus-input
                    class="dashboard-search-field"
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                ></cirrus-input>
            </div>
            <v-data-table
                class="c8-datatable-custom documents-table"
                v-show="company_list.length > 0"
                dense
                item-key="id"
                :headers="headers"
                :items="company_list"
                :items-per-page="5000"
                hide-default-footer
                :total-visible="5000"
                :search="search_datatable"
            >
                <template v-slot:item.value="{ item }">
                    <a :href="'?module=companies&command=company_v2&companyID=' + item.value">{{ item.value }}</a>
                </template>

                <template v-slot:item.companyAddress="{ item }">
                    {{ item.companyAddress }} {{ item.companyCity }}, {{ item.companyState }} {{ item.companyPostCode }}
                </template>

                <template v-slot:item.abn="{ item }">
                    <span
                        v-if="
                            country_defaults.business_prefix &&
                            country_defaults.business_prefix != '' &&
                            item.abn &&
                            item.abn.trim() != ''
                        "
                        >{{ country_defaults.business_prefix }}</span
                    >{{ item.abn }}
                </template>
            </v-data-table>
        </div>
    </div>
</template>

<script>
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

import { mapState, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
const Swal = require('sweetalert2');
export default {
    props: {},
    data() {
        return {
            loading_content_setting: false,
            company_list: [],
            company_list_old: [],
            search_tbl: '',
            show_active: 1,
            company_type: 0,

            headers: [
                { text: 'Company Code', value: 'value', sortable: true, align: 'left', width: '150px' },
                { text: 'Company Name', value: 'label', sortable: true, align: 'left' },
                { text: 'ABN', value: 'abn', sortable: true, align: 'left' },
                { text: 'Address', value: 'companyAddress', sortable: true, align: 'left' },
                { text: 'Payment Method', value: 'method', sortable: true, align: 'left', width: '200px' },
                { text: 'Company Type', value: 'companyType', sortable: true, align: 'left' },
            ],
            search_datatable: '',
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
                tax_label: 'GST',
                business_prefix: '',
            },
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadCountryDefaults();
        this.loadCompanyList();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    methods: {
        loadCompanyList: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('company_type', this.company_type);
            form_data.append('show_active', this.show_active);
            form_data.append('encode', 1);
            this.$api.post('company-dropdown-list', form_data).then((response) => {
                this.search_tbl = '';
                this.company_list = response.data;
                this.company_list_old = response.data;
                this.loading_content_setting = false;
            });
        },
        //     error=>{
        //   let status = error.response.status;
        //   let loader_component = document.getElementById("ngLoader-UI");
        //   if(status && status == 440){
        //     let elem_append = loader_component.querySelector(".loader-ui")
        //     elem_append.innerHTML = ''
        //     elem_append.insertAdjacentHTML('afterbegin', '<div class="desc" style="width:315px;text-align:center;font-size:14px;margin:10px auto;">'+
        //         '<span class="title">Your session has expired and you are being logged out. Please login again to keep using Cirrus8.</span>'+
        //         '</div>' )
        //     setTimeout(function(){
        //       window.location = "http://localhost/c8-api-dev/public/sso/session/destroy?app=cirrus8"
        //     }, 3000);
        //   }
        // });
        //},
        loadCountryDefaults: function () {
            var form_data = new FormData();
            // form_data.append('country', this.company_country);
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;

                this.headers.forEach((header) => {
                    if (header.text == 'ABN') {
                        header.text = this.country_defaults.business_label;
                        header.width = '150px';
                    }
                });
            });
        },
    },
    watch: {
        company_type: function () {
            this.loadCompanyList();
        },
        show_active: function () {
            this.loadCompanyList();
        },
        search_tbl: function () {
            if (this.search_tbl == '') this.company_list = this.company_list_old;
            else {
                let var_old = this.company_list_old;
                this.company_list = [];
                for (let x = 0; x < var_old.length; x++) {
                    let check = 0;
                    for (const [key, value] of Object.entries(var_old[x]))
                        if (value !== null) if (value.toLowerCase().includes(this.search_tbl.toLowerCase())) check = 1;

                    if (check == 1) this.company_list.push(var_old[x]);
                }
            }
        },
    },
    mixins: [global_mixins],
};
</script>
