<template>
    <div class="c8-page">
        <div
            class="page-form"
            style="min-height: 350px"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >{{ property_manager_label }}:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="manager_code"
                        :options="manager_list"
                        :allowEmpty="false"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        group-label="language"
                        :placeholder="'Select a ' + property_manager_label"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult"
                            >Oops! No elements found. Consider changing the search query.</span
                        ></multiselect
                    >
                </v-col>
            </v-row>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Property:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="property_code"
                        :options="property_list"
                        :allowEmpty="false"
                        class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        group-label="language"
                        placeholder="Select a property"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult"
                            >Oops! No elements found. Consider changing the search query.</span
                        ></multiselect
                    >
                </v-col>
            </v-row>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Show Vacated:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type"
                        v-model="show_inactive"
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <div class="text-right">
                <cirrus-input
                    class="dashboard-search-field"
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                ></cirrus-input>
            </div>
            <v-data-table
                class="c8-datatable-custom documents-table"
                v-show="lease_list.length > 0"
                dense
                item-key="id"
                :headers="headers"
                :items="lease_list"
                :items-per-page="5000"
                hide-default-footer
                :total-visible="5000"
                :search="search_datatable"
            >
                <template v-slot:item.property_code="{ item }">
                    <a
                        :href="
                            user_type == 'A'
                                ? '?module=properties&command=v2_manage_property_page&property_code=' +
                                  property_code.field_key
                                : '?module=properties&command=property_summary_page&property_code=' +
                                  property_code.field_key
                        "
                        >{{ property_code.field_key }}</a
                    >
                </template>

                <template v-slot:item.field_key="{ item }">
                    <a
                        :href="
                            user_type == 'A'
                                ? '?module=leases&command=lease_page_v2&lease_code=' +
                                  item.field_key +
                                  '&property_code=' +
                                  property_code.field_key
                                : '?module=leases&command=lease_summary_page&property_code=' +
                                  property_code.field_key +
                                  '&lease_code=' +
                                  item.field_key
                        "
                        >{{ item.field_key }}</a
                    >
                </template>

                <template v-slot:item.debtor_code="{ item }">
                    <a :href="'?module=companies&command=company_v2&companyID=' + item.debtor_code">{{ item.owner }}</a>
                </template>
            </v-data-table>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
const Swal = require('sweetalert2');
export default {
    props: {},
    data() {
        return {
            property_manager_label: 'Property Manager',
            loading_content_setting: false,
            property_code: '',
            property_list: [],
            lease_list: [],
            lease_list_old: [],
            search_tbl: '',
            show_inactive: 1,
            manager_list: [],
            manager_code: { field_key: '', field_value: 'Please select...' },

            headers: [
                { text: 'Property', value: 'property_code', sortable: true, align: 'left' },
                { text: 'Lease Code', value: 'field_key', sortable: true, align: 'left' },
                { text: 'Lease Name', value: 'field_value', sortable: true, align: 'left' },
                { text: 'Tenancy Location', value: 'tenancy_location', sortable: true, align: 'left' },
                { text: 'Unit Code', value: 'unit_code', sortable: true, align: 'left' },
                { text: 'Debtor Company', value: 'debtor_code', sortable: true, align: 'left' },
                { text: 'Lease Expiry', value: 'lease_expiry_date', align: 'center', sortable: false },
                { text: 'CRN', value: 'crn', align: 'end', sortable: false, align: 'left' },
            ],
            search_datatable: '',
        };
    },
    mounted() {
        this.loadManagerList();
        this.loadCountryDefaults();
        //    this.loadPropertyList();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    created() {
        bus.$on('load_list', (data, name) => {
            this.property_code = { field_key: data, field_value: name };
            this.loadLeaseList(data);
        });

        bus.$on('accept_manager', (data, name) => {
            if (!data && !this.property_list.length) this.loadPropertyList();
            else if (this.manager_code.field_key == data) return;
            if (data) {
                this.manager_code = { field_key: data, field_value: name };
                this.loadPropertyList();
            }
        });
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.property_manager_label = this.ucwords(this.country_defaults.property_manager);
            });
        },
        loadManagerList: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('no_load', true);
            form_data.append('display_with_prop_only', true);
            this.$api.post(this.cirrus8_api_url + 'api/loadPortfolioManagersList', form_data).then((response) => {
                let head = [{ field_key: '', field_value: 'Please select...' }];
                this.manager_list = head.concat(response.data.pm_data);
                this.loading_content_setting = false;
            });
        },
        loadPropertyList: function () {
            let form_data = new FormData();
            form_data.append('managerID', this.manager_code.field_key);
            form_data.append('active', 1);
            form_data.append('datatable', 1);
            this.$api.post('vue/loadAPIPropertyDropDownList', form_data).then((response) => {
                let head = [{ field_key: '', field_value: 'Please select...' }];
                this.property_list = head.concat(response.data.data);
            });
        },
        loadLeaseList: function (prop) {
            this.loading_setting = true;
            let form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('current_only', this.show_inactive);
            form_data.append('page_source', 'leaseFormTemplate');
            form_data.append('datatable', 1);
            form_data.append('no_load', true);
            this.$api.post(this.cirrus8_api_url + 'api/load-property-lease-list', form_data).then((response) => {
                this.search_tbl = '';
                this.lease_list_old = response.data.data;
                this.lease_list = response.data.data;
                this.loading_setting = false;
            });
        },
    },
    watch: {
        property_code: function () {
            this.loadLeaseList();
        },
        show_inactive: function () {
            this.loadLeaseList();
        },
        manager_code: function () {
            this.property_code = '';
            this.loadPropertyList();
        },
        search_tbl: function () {
            if (this.search_tbl == '') this.lease_list = this.lease_list_old;
            else {
                let var_old = this.lease_list_old;
                this.lease_list = [];
                for (let x = 0; x < var_old.length; x++) {
                    let check = 0;
                    for (const [key, value] of Object.entries(var_old[x]))
                        if (value !== null) if (value.toLowerCase().includes(this.search_tbl.toLowerCase())) check = 1;

                    if (check == 1) this.lease_list.push(var_old[x]);
                }
            }
        },
    },
    mixins: [global_mixins],
};
</script>
