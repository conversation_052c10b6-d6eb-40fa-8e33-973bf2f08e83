<template>
    <div class="c8-page">
        <div
            class="page-form"
            style="min-height: 350px"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Show Inactive Ledger:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type"
                        v-model="show_inactive"
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Show Inactive Sub-Ledger:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="v-step-search-type"
                        v-model="show_vacant"
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>

            <div class="text-right">
                <cirrus-input
                    class="dashboard-search-field"
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                ></cirrus-input>
            </div>
            <v-data-table
                class="c8-datatable-custom documents-table"
                v-show="lease_list.length > 0"
                dense
                item-key="id"
                :headers="headers"
                :items="lease_list"
                :items-per-page="5000"
                hide-default-footer
                :total-visible="5000"
                :search="search_datatable"
            >
                <template v-slot:item.ledger_code="{ item }">
                    <a :href="'?module=salesTrust&command=manage_ledger_v2&ledger_code=' + item.ledger_code">{{
                        item.ledger_code
                    }}</a>
                </template>

                <template v-slot:item.ownerName="{ item }">
                    <a :href="'?module=companies&command=company_v2&companyID=' + item.debtor_code">{{
                        item.ownerName
                    }}</a>
                </template>
            </v-data-table>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
const Swal = require('sweetalert2');
export default {
    props: {},
    data() {
        return {
            loading_content_setting: false,

            lease_list: [],
            lease_list_old: [],
            search_tbl: '',
            show_inactive: 1,
            show_vacant: 1,
            headers: [
                { text: 'Ledger Code', value: 'ledger_code', sortable: true, align: 'left' },
                { text: 'Ledger Description', value: 'ledger_name', sortable: true, align: 'left' },
                { text: 'Sub-Ledger Code', value: 'sub_ledger_code', sortable: true, align: 'left' },
                { text: 'Sub-Ledger Description', value: 'sub_ledger_name', sortable: true, align: 'left' },
                { text: 'Sub-Ledger Company Code', value: 'debtor_code', sortable: true, align: 'left' },
                { text: 'Sub-Ledger Company Name', value: 'ownerName', sortable: true, align: 'left' },
                { text: 'Sub-Ledger Type', value: 'sub_ledger_type', align: 'left', sortable: true },
            ],
            search_datatable: '',
        };
    },
    mounted() {
        this.loadLeaseList();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    methods: {
        loadLeaseList: function () {
            this.loading_content_setting = true;

            // var formData = new FormData();
            // formData.append('un', this.username);
            // formData.append('current_db', this.current_db);
            // formData.append('user_type', this.user_type);
            //
            // formData.append('show_vacant', this.show_vacant);
            // formData.append('show_inactive', this.show_inactive);
            //
            // let apiUrl = this.cirrus8_api_url + 'api/sales-trust/ledger/get-all-sub-ledgers';
            let form_data = new FormData();
            form_data.append('show_vacant', this.show_vacant);
            form_data.append('show_inactive', this.show_inactive);
            form_data.append('datatable', 1);
            this.$api
                .post(this.cirrus8_api_url + 'api/sales-trust/ledger/get-all-sub-ledgers', form_data)
                .then((response) => {
                    this.lease_list = response.data;
                    this.lease_list_old = response.data;

                    this.loading_content_setting = false;
                });
        },
    },
    watch: {
        show_inactive: function () {
            this.loadLeaseList();
        },
        show_vacant: function () {
            this.loadLeaseList();
        },

        search_tbl: function () {
            if (this.search_tbl == '') this.lease_list = this.lease_list_old;
            else {
                let var_old = this.lease_list_old;
                this.lease_list = [];
                for (let x = 0; x < var_old.length; x++) {
                    let check = 0;
                    for (const [key, value] of Object.entries(var_old[x]))
                        if (value !== null) if (value.toLowerCase().includes(this.search_tbl.toLowerCase())) check = 1;

                    if (check == 1) this.lease_list.push(var_old[x]);
                }
            }
        },
    },
    mixins: [global_mixins],
};
</script>
