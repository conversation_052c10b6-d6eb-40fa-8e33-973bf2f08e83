<template>
    <div class="c8-page">
        <div
            class="page-form"
            style="min-height: 350px"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >{{ property_manager_label }}:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="manager_code"
                        :options="manager_list"
                        :allowEmpty="false"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        group-label="language"
                        :placeholder="'Select a ' + property_manager_label"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult"
                            >Oops! No elements found. Consider changing the search query.</span
                        ></multiselect
                    >
                </v-col>
            </v-row>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Owner:</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="owner_code"
                        :options="owner_list"
                        :allowEmpty="false"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        group-label="language"
                        placeholder="Select an owner"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult"
                            >Oops! No elements found. Consider changing the search query.</span
                        ></multiselect
                    >
                </v-col>
            </v-row>

            <div class="text-right">
                <cirrus-input
                    class="dashboard-search-field"
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                ></cirrus-input>
            </div>
            <v-data-table
                class="c8-datatable-custom documents-table"
                v-show="property_list.length > 0"
                dense
                item-key="id"
                :headers="headers"
                :items="property_list"
                :items-per-page="5000"
                hide-default-footer
                :total-visible="5000"
                :search="search_datatable"
            >
                <template v-slot:item.field_key="{ item }">
                    <a
                        :href="
                            user_type == 'A'
                                ? '?module=properties&command=v2_manage_property_page&property_code=' + item.field_key
                                : '?module=properties&command=property_summary_page&property_code=' + item.field_key
                        "
                        >{{ item.field_key }}</a
                    >
                </template>

                <template v-slot:item.ownerCode="{ item }">
                    <a :href="'?module=companies&command=company_v2&companyID=' + item.ownerCode">{{ item.owner }}</a>
                </template>

                <template v-slot:item.action1="{ item }">
                    <a
                        @click="loadLeaseList(item.field_key, item.field_value)"
                        href="#"
                        ><v-icon color="blue darken-2">list</v-icon></a
                    >
                </template>
            </v-data-table>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
const Swal = require('sweetalert2');
export default {
    props: {},
    data() {
        return {
            property_manager_label: 'Property Manager',
            suburb_label: 'Suburb',
            loading_content_setting: false,
            property_code: '',
            property_list_old: [],
            property_list: [],
            lease_list: [],
            owner_list: [],
            owner_code: { field_key: '', field_value: 'Please select...' },
            manager_list: [],
            manager_code: { field_key: '', field_value: 'Please select...' },
            search_tbl: '',

            headers: [
                { text: 'Property Code', value: 'field_key', sortable: true, align: 'left' },
                { text: 'Property Name', value: 'fieldValue', sortable: true, align: 'left' },
                { text: 'Suburb', value: 'sub_urb', sortable: true, align: 'left' },
                { text: 'Post Code', value: 'post_code', sortable: true, align: 'left' },
                { text: 'Property Manager', value: 'manager', sortable: true, align: 'left' },
                { text: 'Primary Owner', value: 'ownerCode', sortable: true, align: 'left' },
                { text: 'Leases', value: 'action1', align: 'end', sortable: false, align: 'center' },
            ],
            search_datatable: '',
        };
    },
    mounted() {
        this.loadOwnerList();
        this.loadManagerList();
        this.loadPropertyList();
        this.loadCountryDefaults();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    created() {
        bus.$on('accept_manager_prop', (data, name) => {
            if (this.manager_code.field_key == data) return;
            this.manager_code = { field_key: data, field_value: name };
            this.loadPropertyList();
        });
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.property_manager_label = this.ucwords(this.country_defaults.property_manager);
                this.suburb_label = this.ucwords(this.country_defaults.suburb);

                this.headers = [
                    { text: 'Property Code', value: 'field_key', sortable: true, align: 'left' },
                    { text: 'Property Name', value: 'fieldValue', sortable: true, align: 'left' },
                    { text: this.suburb_label, value: 'sub_urb', sortable: true, align: 'left' },
                    { text: 'Post Code', value: 'post_code', sortable: true, align: 'left' },
                    { text: this.property_manager_label, value: 'manager', sortable: true, align: 'left' },
                    { text: 'Primary Owner', value: 'ownerCode', sortable: true, align: 'left' },
                    { text: 'Leases', value: 'action1', align: 'end', sortable: false, align: 'center' },
                ];
            });
        },

        loadLeaseList: function (prop, name) {
            this.$emit('eventname', 'tab-3');
            setTimeout(function () {
                bus.$emit('load_list', prop, name);
            }, 200);
        },
        loadOwnerList: function () {
            this.loading_content_setting = true;
            this.$api.post('owner-dropdown-list', {}).then((response) => {
                let head = [{ field_key: '', field_value: 'Please select...' }];
                this.owner_list = head.concat(response.data);
                this.loading_content_setting = false;
            });
        },
        loadManagerList: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('no_load', true);
            form_data.append('display_with_prop_only', true);
            this.$api.post(this.cirrus8_api_url + 'api/loadPortfolioManagersList', form_data).then((response) => {
                let head = [{ field_key: '', field_value: 'Please select...' }];
                this.manager_list = head.concat(response.data.pm_data);
                this.loading_content_setting = false;
            });
        },
        loadPropertyList: function () {
            let form_data = new FormData();
            form_data.append('managerID', this.manager_code.field_key);
            form_data.append('OwnerID', this.owner_code.field_key);
            form_data.append('active', 1);
            form_data.append('datatable', 1);
            this.$api.post('vue/loadAPIPropertyDropDownList', form_data).then((response) => {
                this.search_tbl = '';
                this.property_list = response.data.data;
                this.property_list_old = response.data.data;
            });
        },
    },
    watch: {
        owner_code: function () {
            this.loadPropertyList();
        },
        manager_code: function () {
            this.loadPropertyList();
        },
        search_tbl: function () {
            if (this.search_tbl == '') this.property_list = this.property_list_old;
            else {
                let var_old = this.property_list_old;
                this.property_list = [];
                for (let x = 0; x < var_old.length; x++) {
                    let check = 0;
                    for (const [key, value] of Object.entries(var_old[x]))
                        if (value !== null) if (value.toLowerCase().includes(this.search_tbl.toLowerCase())) check = 1;

                    if (check == 1) this.property_list.push(var_old[x]);
                }
            }
        },
    },
    mixins: [global_mixins],
};
</script>
