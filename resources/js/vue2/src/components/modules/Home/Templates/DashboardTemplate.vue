<template>
    <div class="c8-page">
        <div class="page-form">
            <div class="form-row">
                <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
                <v-tabs
                    icons-and-text
                    dark
                    v-model="template_tab"
                    show-arrows
                    class="dashboard"
                >
                    <v-tabs-slider color="orange"></v-tabs-slider>

                    <v-tab
                        href="#tab-1"
                        class="v-step-form-tab"
                    >
                        <div style="margin-top: -5px">
                            <span
                                ><v-icon
                                    class="fa"
                                    style="vertical-align: bottom"
                                    color="hsla(0,0%,100%,.6)"
                                    size="19"
                                    >mdi-view-dashboard</v-icon
                                ></span
                            >
                            <span
                                style="vertical-align: text-bottom; line-height: 13px"
                                class="fa fa-text"
                                >Dashboard</span
                            >
                        </div>
                    </v-tab>
                    <v-tab
                        href="#tab-2"
                        @click="loadPropDefaultManager()"
                    >
                        <div style="margin-bottom: 5px">
                            <span
                                style="font-size: 15px"
                                class="fa fa-building"
                            ></span
                            >&nbsp;<span
                                class="fa fa-text"
                                style="vertical-align: text-bottom; line-height: 10px"
                                >Properties</span
                            >
                        </div>
                    </v-tab>
                    <v-tab
                        href="#tab-3"
                        @click="loadLeaseDefaultManager()"
                    >
                        <div style="margin-top: -6px">
                            <v-icon
                                class="fa"
                                style="vertical-align: bottom; margin-bottom: -1px"
                                color="hsla(0,0%,100%,.6)"
                                size="21"
                                >mdi-home-floor-l
                            </v-icon>
                            <span
                                class="fa fa-text"
                                style="vertical-align: text-bottom; line-height: 13px"
                                >Leases</span
                            >
                        </div>
                    </v-tab>
                    <v-tab href="#tab-4">
                        <div style="margin-bottom: 3px">
                            <span
                                style="font-size: 15px"
                                class="fa fa-address-card"
                            ></span>
                            <span
                                class="fa fa-text"
                                style="vertical-align: text-bottom; line-height: 12px"
                                >Companies</span
                            >
                        </div>
                    </v-tab>
                    <v-tab
                        href="#tab-5"
                        v-if="user_type == 'A'"
                    >
                        <div style="margin-bottom: 1px">
                            <span
                                style="font-size: 15px"
                                :class="
                                    country_code == 'AU'
                                        ? 'fa fa-dollar'
                                        : country_code == 'GB'
                                          ? 'fa fa-pound-sign'
                                          : ''
                                "
                            ></span>
                            <span class="fa fa-text">Ledger</span>
                        </div>
                    </v-tab>

                    <v-tab-item :value="'tab-1'"
                        ><br />
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="5"
                                lg="5"
                                xl="5"
                            >
                                <v-card
                                    v-if="quick_links.length"
                                    class="section-toolbar"
                                    dark
                                    color="titleHeader"
                                    text
                                    tile
                                >
                                    <v-card-actions>
                                        <h6 class="title font-weight-black">Quick Reports</h6>
                                    </v-card-actions>
                                </v-card>
                                <div
                                    v-if="quick_links.length"
                                    class="page-form"
                                >
                                    <table
                                        class="data-grid"
                                        width="100%"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                    >
                                        <tr
                                            v-for="(quick_links_data, index) in quick_links"
                                            :key="index"
                                        >
                                            <td @click="open_location(quick_links_data.link)">
                                                <span class="fa fa-list"></span>
                                                <a :href="quick_links_data.link"> {{ quick_links_data.description }}</a>
                                            </td>
                                        </tr>
                                    </table>
                                </div>

                                <v-card
                                    v-if="tasks_assign.length"
                                    class="section-toolbar"
                                    dark
                                    color="titleHeader"
                                    text
                                    tile
                                >
                                    <v-card-actions>
                                        <h6 class="title font-weight-black">
                                            Task
                                            <a href="?module=taskManagement&command=manage_task"
                                                ><img
                                                    style="vertical-align: middle"
                                                    :src="assetDomain + 'assets/images/icons/open.png'"
                                            /></a>
                                            <a href="?command=oneOffChargeTasks&module=ar"
                                                ><img
                                                    style="vertical-align: middle"
                                                    :src="assetDomain + 'assets/images/icons/add.png'"
                                            /></a>
                                        </h6>
                                    </v-card-actions>
                                </v-card>
                                <div
                                    v-if="tasks_assign.length"
                                    class="page-form"
                                >
                                    <table
                                        class="data-grid"
                                        width="100%"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                    >
                                        <tr class="fieldDescription">
                                            <td width=""></td>
                                            <td>Task</td>
                                            <td>Subject</td>
                                            <td>Status</td>
                                        </tr>
                                        <tr
                                            v-for="(item, index) in tasks_assign"
                                            :key="index"
                                        >
                                            <td>
                                                <v-btn
                                                    text
                                                    icon
                                                    color="info"
                                                    x-small
                                                    title="Activities"
                                                    data-position="bottom right"
                                                    @click="showActivityModal(item.task_row_id)"
                                                >
                                                    <v-icon>history</v-icon>
                                                </v-btn>
                                                <v-btn
                                                    text
                                                    icon
                                                    color="warning"
                                                    x-small
                                                    title="Edit"
                                                    data-position="bottom right"
                                                    @click="showEditModal(item)"
                                                >
                                                    <v-icon>mdi-square-edit-outline</v-icon>
                                                </v-btn>
                                                <v-btn
                                                    text
                                                    icon
                                                    color="primary"
                                                    x-small
                                                    title="Comment"
                                                    data-position="bottom right"
                                                    @click="showCommentModal(item)"
                                                >
                                                    <v-icon>mdi-comment-plus-outline</v-icon>
                                                </v-btn>
                                            </td>
                                            <td>{{ item.description }}</td>
                                            <td>{{ item.subject }}</td>
                                            <td>{{ item.status }}</td>
                                        </tr>
                                    </table>
                                </div>

                                <v-card
                                    class="section-toolbar"
                                    dark
                                    color="titleHeader"
                                    text
                                    tile
                                >
                                    <v-card-actions>
                                        <h6
                                            style="width: 50%; max-width: 50%"
                                            class="title font-weight-black"
                                        >
                                            Forms
                                        </h6>
                                        <div
                                            style="width: 50%; max-width: 50%"
                                            class="v-input text-right"
                                        >
                                            <h6 class="title font-weight-black">
                                                <a
                                                    style="color: white"
                                                    v-if="hide_process == 1"
                                                    @click="hide_process = 0"
                                                    >Show Processed</a
                                                >
                                            </h6>
                                            <h6 class="title font-weight-black">
                                                <a
                                                    style="color: white"
                                                    v-if="hide_process == 0"
                                                    @click="hide_process = 1"
                                                    >Hide Processed</a
                                                >
                                            </h6>
                                        </div>
                                    </v-card-actions>
                                </v-card>
                                <div class="page-form">
                                    <table
                                        class="data-grid"
                                        width="100%"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                    >
                                        <tr class="fieldDescription">
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                View
                                            </td>
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                Create
                                            </td>
                                            <td class="title">&nbsp;</td>
                                            <td
                                                width="1%"
                                                align="right"
                                            >
                                                Rejected
                                            </td>
                                            <td
                                                width="1%"
                                                align="right"
                                            >
                                                Draft
                                            </td>
                                            <td
                                                width="1%"
                                                align="right"
                                            >
                                                Unprocessed
                                            </td>
                                            <td
                                                v-if="hide_process == 0"
                                                width="1%"
                                                align="right"
                                            >
                                                Processed
                                            </td>
                                        </tr>

                                        <tr>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=chargeReview_v2&module=leases&viewpage=1"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=chargeReview_v2&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=chargeReview_v2&module=leases&viewpage=1"
                                                    >Charge and Rent Reviews</a
                                                ><span v-else>Charge and Rent Reviews</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.rentReview['2'] }}
                                            </td>
                                            <td align="right">&nbsp</td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.rentReview['0'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.rentReview['1'] }}
                                            </td>
                                        </tr>

                                        <tr v-if="country_code == 'GB'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=reviewLease&module=leases&viewpage=1"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=chargeReviewLease&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=reviewLease&module=leases&viewpage=1"
                                                    >Quick Rent Reviews (Beta)</a
                                                ><span v-else>Quick Rent Reviews (Beta)</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.bulkRentReview['2'] }}
                                            </td>
                                            <td align="right">&nbsp</td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.bulkRentReview['0'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.bulkRentReview['1'] }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=oneOffChargeTasks&module=ar"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        user_type == 'C'
                                                            ? '?command=oneOffCharge&module=ar'
                                                            : '?module=ar&command=invoice'
                                                    "
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=oneOffChargeTasks&module=ar"
                                                    >Charges and Credits</a
                                                ><span v-else>Charges and Credits</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.chargeCredit['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.chargeCredit['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.chargeCredit['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.chargeCredit['3'] }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                <a href="?module=configuration&command=bankDetailList"
                                                    ><img
                                                        alt="list"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                        style="vertical-align: middle"
                                                /></a>
                                            </td>
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                <a href="?module=configuration&command=bankDetail"
                                                    ><img
                                                        alt="change"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                        style="vertical-align: middle"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?module=configuration&command=bankDetailList"
                                                    >Changes to Payment Details</a
                                                ><span v-else>Changes to Payment Details</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.paymentDetail['Rejected'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            ></td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{
                                                    count_forms.paymentDetail['Pending']
                                                }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.paymentDetail['Approved'] }}
                                            </td>
                                        </tr>

                                        <tr
                                            v-if="
                                                load_tab1 &&
                                                count_forms.authorizer.length > 0 &&
                                                count_forms.authorizer[0] > 0 &&
                                                count_forms.trustAccount['0'] > 0
                                            "
                                        >
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                <a href="?module=configuration&command=trustAccountDetailsList"
                                                    ><img
                                                        alt="list"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                        style="vertical-align: middle"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'"></td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?module=configuration&command=trustAccountDetailsList"
                                                    >Changes to {{ trust_account_label }} Details</a
                                                ><span v-else>Changes to {{ trust_account_label }} Details</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.trustAccount['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            ></td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.trustAccount['0'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.trustAccount['1'] }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=companyTasks&module=companies">
                                                    <img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        user_type == 'C'
                                                            ? '?command=company&module=companies&companyStatus=2'
                                                            : '?command=company_v2&module=companies'
                                                    "
                                                >
                                                    <img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                    />
                                                </a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=companyTasks&module=companies"
                                                    >Companies</a
                                                ><span v-else>Companies</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.company['3'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            ></td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.company['0'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.company['1'] }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=diary&module=home"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=diary&module=home"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=diaryReport&module=home"
                                                    >Diary</a
                                                ><span v-else>Diary</span>
                                            </td>

                                            <td align="right">&nbsp;</td>
                                            <!-- rejected -->
                                            <td align="right">&nbsp;</td>
                                            <td align="right">&nbsp;</td>
                                            <td
                                                align="right"
                                                v-if="hide_process == 0"
                                            >
                                                &nbsp;
                                            </td>
                                        </tr>

                                        <tr v-if="show_lease_beta === '1'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=leaseTasks&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        '?command=' +
                                                        (country_code == 'GB' ? 'lease_page_v2' : 'home') +
                                                        '&module=leases'
                                                    "
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=leaseTasks&module=leases"
                                                    >Leases</a
                                                ><span v-else>Leases</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.leases['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.leases['3'] }}
                                            </td>
                                        </tr>
                                        <tr v-if="show_lease_beta === '1'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=leaseTasks&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=lease_page_v2&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=leaseTasks&module=leases"
                                                    >Leases Beta</a
                                                ><span v-else>Leases Beta</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.leases['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.leases['3'] }}
                                            </td>
                                        </tr>
                                        <tr v-if="show_lease_beta === '2'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=leaseTasks&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        '?command=' +
                                                        (country_code == 'GB' ? 'lease_page_v2' : 'home') +
                                                        '&module=leases'
                                                    "
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=leaseTasks&module=leases"
                                                    >Leases</a
                                                ><span v-else>Leases</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.leases['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.leases['3'] }}
                                            </td>
                                        </tr>
                                        <tr v-if="show_lease_beta === '3'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=leaseTasks&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=lease_page_v2&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=leaseTasks&module=leases"
                                                    >Leases</a
                                                ><span v-else>Leases</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.leases['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.leases['3'] }}
                                            </td>
                                        </tr>

                                        <tr v-if="['1', '2', '3'].indexOf(show_lease_beta) === -1">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=leaseTasks&module=leases"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        '?command=' +
                                                        (country_code == 'GB' ? 'lease_page_v2' : 'home') +
                                                        '&module=leases'
                                                    "
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=leaseTasks&module=leases"
                                                    >Leases</a
                                                ><span v-else>Leases</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.leases['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.leases['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.leases['3'] }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=viewPaymentsBeta&module=ap"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?module=ap&command=payments"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=viewPaymentsBeta&module=ap"
                                                    >Payments</a
                                                ><span v-else>Payments</span>
                                            </td>
                                            <td align="right">&nbsp;</td>
                                            <!-- rejected -->
                                            <td align="right">&nbsp;</td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{
                                                    parseInt(count_forms.payments['0']) +
                                                    parseInt(count_forms.payments['2'])
                                                }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.payments['3'] }}
                                            </td>
                                        </tr>

                                        <!-- For TA Automated Invoice link in Dashboard - START -->
                                        <tr v-if="user_type == 'A' && ai_ta_approval == 1">
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                <a href="?command=pmList&module=ai">
                                                    <img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                    />
                                                </a>
                                            </td>
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            ></td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'A'"
                                                    href="?command=pmList&module=ai"
                                                    >Automated Invoice</a
                                                ><span v-else>Automated Invoice</span>
                                            </td>
                                            <td
                                                v-if="load_tab1"
                                                align="right"
                                            >
                                                &nbsp;
                                            </td>
                                            <td
                                                v-if="load_tab1"
                                                align="right"
                                            >
                                                &nbsp;
                                            </td>
                                            <td
                                                v-if="load_tab1"
                                                align="right"
                                            >
                                                <strong class="alert">{{ count_forms.automatedInvoice['0'] }}</strong>
                                            </td>
                                            <td
                                                v-if="load_tab1 && hide_process == 0"
                                                align="right"
                                                class="processed"
                                            >
                                                {{ count_forms.automatedInvoice['1'] }}
                                            </td>
                                        </tr>
                                        <!-- For TA Automated Invoice link in Dashboard - END -->

                                        <tr v-if="user_type == 'C' && user_sub_type != 'PMRO' && ai_pm_approval == 1">
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                <a href="?command=pmList&module=ai">
                                                    <img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                    />
                                                </a>
                                            </td>
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            ></td>
                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=pmList&module=ai"
                                                    >Automated Invoice</a
                                                ><span v-else>Automated Invoice</span>
                                            </td>
                                            <td
                                                v-if="load_tab1"
                                                align="right"
                                            >
                                                &nbsp;
                                            </td>
                                            <td
                                                v-if="load_tab1"
                                                align="right"
                                            >
                                                &nbsp;
                                            </td>
                                            <td
                                                v-if="load_tab1"
                                                align="right"
                                            >
                                                <strong class="alert">{{ count_forms.automatedInvoice['0'] }}</strong>
                                            </td>
                                            <td
                                                v-if="load_tab1 && hide_process == 0"
                                                align="right"
                                                class="processed"
                                            >
                                                {{ count_forms.automatedInvoice['1'] }}
                                            </td>
                                        </tr>

                                        <tr v-if="show_property_beta === '1'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=propertyTasks&module=properties"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        '?command=' +
                                                        (country_code == 'GB' ? 'v2_manage_property_page' : 'home') +
                                                        '&module=properties'
                                                    "
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=propertyTasks&module=properties"
                                                    >Properties</a
                                                ><span v-else>Properties</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.properties['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.properties['3'] }}
                                            </td>
                                        </tr>
                                        <tr v-if="show_property_beta === '1'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=propertyTasks&module=properties"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=v2_manage_property_page&module=properties"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=propertyTasks&module=properties"
                                                    >Properties Beta</a
                                                ><span v-else>Properties Beta</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.properties['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.properties['3'] }}
                                            </td>
                                        </tr>
                                        <tr v-if="show_property_beta === '2'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=propertyTasks&module=properties"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        '?command=' +
                                                        (country_code == 'GB' ? 'v2_manage_property_page' : 'home') +
                                                        '&module=properties'
                                                    "
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=propertyTasks&module=properties"
                                                    >Properties</a
                                                ><span v-else>Properties</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.properties['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.properties['3'] }}
                                            </td>
                                        </tr>
                                        <tr v-if="show_property_beta === '3'">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=propertyTasks&module=properties"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=v2_manage_property_page&module=properties"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=propertyTasks&module=properties"
                                                    >Properties</a
                                                ><span v-else>Properties</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.properties['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.properties['3'] }}
                                            </td>
                                        </tr>
                                        <tr v-if="['1', '2', '3'].indexOf(show_property_beta) === -1">
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=propertyTasks&module=properties"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        '?command=' +
                                                        (country_code == 'GB' ? 'v2_manage_property_page' : 'home') +
                                                        '&module=properties'
                                                    "
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=propertyTasks&module=properties"
                                                    >Properties</a
                                                ><span v-else>Properties</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['2'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.properties['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.properties['1'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.properties['3'] }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=viewReceipt&module=ar"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    :href="
                                                        user_type == 'C'
                                                            ? '?command=receipt&module=ar'
                                                            : '?command=receipting&module=ar'
                                                    "
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=viewReceipt&module=ar"
                                                    >Receipts</a
                                                ><span v-else>Receipts</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.receipts['1'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            ></td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{
                                                    parseInt(count_forms.receipts['0']) +
                                                    parseInt(count_forms.receipts['2'])
                                                }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.receipts['3'] }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                <a href="?command=viewTransferJournals&module=ap"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td
                                                v-if="user_sub_type != 'PMRO'"
                                                class="action"
                                            >
                                                <a href="?command=transferJournal&module=ap"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=viewTransferJournals&module=ap"
                                                    >Transfer Journals</a
                                                ><span v-else>Transfer Journals</span>
                                            </td>
                                            <td align="right">&nbsp;</td>
                                            <!-- rejected -->
                                            <td align="right">&nbsp;</td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.journal['0'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.journal['1'] }}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a href="?command=viewStandingCharges&module=ar"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/open.png'"
                                                /></a>
                                            </td>
                                            <td v-if="user_sub_type != 'PMRO'">
                                                <a
                                                    v-if="user_type == 'A'"
                                                    href="?command=generateCharges&module=ar"
                                                    ><img
                                                        style="vertical-align: middle"
                                                        :src="assetDomain + 'assets/images/icons/add.png'"
                                                /></a>
                                            </td>

                                            <td>
                                                <a
                                                    v-if="user_type == 'C'"
                                                    href="?command=viewStandingCharges&module=ar"
                                                    >Standing Charges</a
                                                ><span v-else>Standing Charges</span>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.standingCharges['1'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                {{ count_forms.standingCharges['0'] }}
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1"
                                            >
                                                <strong class="alert">{{ count_forms.standingCharges['2'] }}</strong>
                                            </td>
                                            <td
                                                align="right"
                                                v-if="load_tab1 && hide_process == 0"
                                                class="processed"
                                            >
                                                {{ count_forms.standingCharges['3'] }}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </v-col>

                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="7"
                                lg="7"
                                xl="7"
                                id="right_panel"
                            >
                                <v-card
                                    class="section-toolbar"
                                    dark
                                    color="titleHeader"
                                    text
                                    tile
                                >
                                    <v-card-actions>
                                        <h6 class="title font-weight-black">Calendar</h6>
                                    </v-card-actions>
                                </v-card>

                                <v-row>
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="4"
                                        xl="4"
                                    >
                                        <h1>{{ calendar_selected }}</h1>
                                    </v-col>
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="8"
                                        xl="8"
                                        class="text-right"
                                    >
                                        <table style="width: 100%">
                                            <tr>
                                                <td
                                                    class="portfolio-filter"
                                                    style="float: right"
                                                >
                                                    <multiselect
                                                        v-model="manager_code"
                                                        :options="manager_list"
                                                        :allowEmpty="false"
                                                        class="v-step-property-select vue-select2 dropdown-left dropdown-400 dashboard-portfolio-filter"
                                                        :custom-label="nameWithDash"
                                                        group-label="language"
                                                        track-by="field_key"
                                                        label="field_value"
                                                        :show-labels="false"
                                                        ><span slot="noResult"
                                                            >Oops! No elements found. Consider changing the search
                                                            query.</span
                                                        ></multiselect
                                                    >
                                                </td>
                                                <td
                                                    class="portfolio-filter-label"
                                                    style="float: right; padding-top: 3px"
                                                >
                                                    Portfolio Filter &nbsp;&nbsp;&nbsp;
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <table
                                    width="100%"
                                    cellspacing="0"
                                    cellpadding="0"
                                    border="0"
                                    id="calendar"
                                    class="calendar"
                                >
                                    <tbody>
                                        <tr class="header">
                                            <td colspan="5"></td>
                                            <td
                                                colspan="2"
                                                align="right"
                                            >
                                                <a
                                                    @click="loadCalendar(new Date())"
                                                    title="Today"
                                                    ><img :src="assetDomain + 'assets/images/icons/diary.png'"
                                                /></a>
                                            </td>
                                        </tr>
                                        <tr class="dayList">
                                            <td>Monday</td>
                                            <td>Tuesday</td>
                                            <td>Wednesday</td>
                                            <td>Thursday</td>
                                            <td>Friday</td>
                                            <td>Saturday</td>
                                            <td>Sunday</td>
                                        </tr>
                                        <tr
                                            class="week"
                                            v-for="(calendarRow, index) in calendar"
                                            :key="index"
                                        >
                                            <td
                                                @click="loadCalendar(data['current'])"
                                                v-for="(data, key) in calendarRow"
                                                :key="key"
                                                :class="
                                                    typeof calendar_event[data['date']] != 'undefined' &&
                                                    data['class'] != 'selected'
                                                        ? data['date'] == today
                                                            ? 'today'
                                                            : calendar_event[data['date']]['tracc'].length
                                                              ? 'tracc'
                                                              : 'event'
                                                        : data['class']
                                                "
                                            >
                                                <a href="#">{{ data['day'] }}</a>
                                                <div
                                                    class="calendarItems"
                                                    v-if="typeof calendar_event[data['date']] != 'undefined'"
                                                >
                                                    <img
                                                        v-if="calendar_event[data['date']]['rentReviewCount'] > 0"
                                                        :src="
                                                            assetDomain +
                                                            'assets/images/icons/' +
                                                            (country_code == 'AU'
                                                                ? 'money_dollar.png'
                                                                : country_code == 'GB'
                                                                  ? 'money_pound.png'
                                                                  : '')
                                                        "
                                                        :title="
                                                            calendar_event[data['date']]['rentReviewCount'] +
                                                            ' Rent Reviews'
                                                        "
                                                    />
                                                    <img
                                                        v-if="calendar_event[data['date']]['leaseExpiresCount'] > 0"
                                                        :src="assetDomain + 'assets/images/icons/leaseExpires.png'"
                                                        :title="
                                                            calendar_event[data['date']]['leaseExpiresCount'] +
                                                            ' Leases Expire'
                                                        "
                                                    />
                                                    <img
                                                        v-if="calendar_event[data['date']]['otherCount'] > 0"
                                                        :src="assetDomain + 'assets/images/icons/star.png'"
                                                        :title="
                                                            calendar_event[data['date']]['otherCount'] +
                                                            ' Other event(s)'
                                                        "
                                                    />
                                                    <span
                                                        v-for="(data2, key2) in calendar_event[data['date']]['tracc']"
                                                        :key="key2"
                                                        ><br />{{ data2 }}</span
                                                    >
                                                </div>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td>
                                                <a @click="update_calendar('minusYear')"
                                                    ><img
                                                        :src="assetDomain + 'assets/images/calendar/step_back_off.png'"
                                                /></a>
                                                <a @click="update_calendar('minusMonth')"
                                                    ><img :src="assetDomain + 'assets/images/calendar/back_off.png'"
                                                /></a>
                                            </td>
                                            <td colspan="5">&nbsp;</td>
                                            <td class="right">
                                                <a @click="update_calendar('plusMonth')"
                                                    ><img :src="assetDomain + 'assets/images/calendar/forward_off.png'"
                                                /></a>
                                                <a @click="update_calendar('plusYear')"
                                                    ><img
                                                        :src="
                                                            assetDomain + 'assets/images/calendar/step_forward_off.png'
                                                        "
                                                /></a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <v-card
                                    class="section-toolbar"
                                    dark
                                    color="titleHeader"
                                    text
                                    tile
                                    v-if="typeof calendar_event[pass_to_server] != 'undefined'"
                                >
                                    <v-card-actions>
                                        <h6
                                            class="title font-weight-black"
                                            style="width: 100%"
                                        >
                                            Diary Items for {{ cur_day[calendar_current.getDay()] }},
                                            {{ dateOrdinal(calendar_current.getDate()) }}
                                            {{ months[calendar_current.getMonth()] }},
                                            {{ calendar_current.getFullYear() }}

                                            <div
                                                class="text-right"
                                                style="float: right"
                                            >
                                                <cirrus-input
                                                    class="dashboard-search-field dashboard-diary-search"
                                                    inputFormat="search"
                                                    v-model="search_datatable1"
                                                    placeholder="Search"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </div>
                                        </h6>
                                    </v-card-actions>
                                </v-card>
                                <div
                                    class="page-form"
                                    v-if="typeof calendar_event[pass_to_server] != 'undefined'"
                                >
                                    <table
                                        class="data-grid"
                                        width="100%"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                    >
                                        <tr
                                            v-for="(calendarEvent, index) in calendar_event[pass_to_server]['events']"
                                            :key="index"
                                            :class="
                                                calendar_event[pass_to_server]['events'][index].propertyID == ''
                                                    ? 'highlight'
                                                    : ''
                                            "
                                        >
                                            <td
                                                colspan="7"
                                                v-if="calendarEvent.propertyID == ''"
                                            >
                                                cirrus8 Event: {{ calendarEvent.diaryText }}
                                            </td>
                                        </tr>
                                    </table>

                                    <v-data-table
                                        class="c8-datatable-custom documents-table"
                                        v-show="calendar_event[pass_to_server]['events'].length > 0"
                                        dense
                                        item-key="id"
                                        :headers="user_sub_type != 'PMRO' ? headers : headersPMRO"
                                        :items="calendar_event[pass_to_server]['events']"
                                        :items-per-page="10"
                                        :search="search_datatable1"
                                        :custom-sort="customSort"
                                        :custom-filter="customSearch"
                                    >
                                        <template v-slot:item="{ item }">
                                            <tr :class="item.propertyID == '' ? 'highlight' : ''">
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <v-icon
                                                        color="red"
                                                        title="Delete"
                                                        @click="
                                                            delete_diary(
                                                                item.propertyID,
                                                                item.diaryID,
                                                                calendar_event[pass_to_server]['events'].indexOf(item),
                                                                0,
                                                            )
                                                        "
                                                    >
                                                        close
                                                    </v-icon>
                                                    <!--                              <a @click="delete_diary(item.propertyID,item.diaryID , calendar_three_months.indexOf(item),1)" title="Delete" href="#" >-->
                                                    <!--                                  <img x-small :src="assetDomain + 'assets/images/icons/delete_v2.png'" /></a>-->
                                                </td>
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <img
                                                        v-if="item.diaryCompleted != null && item.diaryCompleted != ''"
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/accept.png'"
                                                    />
                                                    <v-icon
                                                        class="edit-diary"
                                                        small
                                                        @click="
                                                            copy_diary(
                                                                item,
                                                                item.diaryID,
                                                                calendar_event[pass_to_server]['events'].indexOf(item),
                                                                0,
                                                            )
                                                        "
                                                        v-if="item.diaryCompleted == null || item.diaryCompleted == ''"
                                                        title="Edit diary item"
                                                    >
                                                        fas fa-edit
                                                    </v-icon>
                                                </td>
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <img
                                                        v-if="item.diaryCompleted != null && item.diaryCompleted != ''"
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/accept.png'"
                                                    />
                                                    <v-icon
                                                        color="green"
                                                        @click="
                                                            complete_diary(
                                                                item.diaryID,
                                                                calendar_event[pass_to_server]['events'].indexOf(item),
                                                                0,
                                                            )
                                                        "
                                                        v-if="item.diaryCompleted == null || item.diaryCompleted == ''"
                                                        title="Close diary item"
                                                    >
                                                        check
                                                        <!--                                  <img x-small class="icon domroll assets/images/icons/add.png" :src="assetDomain + 'assets/images/icons/delete_green.png'">-->
                                                    </v-icon>
                                                </td>
                                                <td v-if="item.propertyID != ''">
                                                    {{ item.diaryDate }}
                                                </td>

                                                <td v-if="item.propertyID != ''">
                                                    <a
                                                        title="View property details"
                                                        :href="
                                                            user_type == 'C'
                                                                ? '?command=property_summary_page&module=properties&property_code=' +
                                                                  item.propertyID
                                                                : '?command=v2_manage_property_page&module=properties&property_code=' +
                                                                  item.propertyID
                                                        "
                                                        >{{ item.propertyID }}</a
                                                    ><br />{{ item.propertyName }}
                                                </td>
                                                <td v-if="item.propertyID != ''">
                                                    <a
                                                        v-if="item.leaseID != ''"
                                                        title="View lease details"
                                                        :href="
                                                            user_type == 'C'
                                                                ? '?command=lease_summary_page&module=leases&lease_code=' +
                                                                  item.leaseID +
                                                                  '&property_code=' +
                                                                  item.propertyID
                                                                : '?command=lease_page_v2&module=leases&lease_code=' +
                                                                  item.leaseID +
                                                                  '&property_code=' +
                                                                  item.propertyID
                                                        "
                                                        >{{ item.leaseID }}</a
                                                    >
                                                    <br />{{ item.leaseName }}
                                                </td>

                                                <td
                                                    v-if="item.propertyID != ''"
                                                    width=""
                                                >
                                                    <b
                                                        ><a
                                                            v-if="user_type == 'C' && item.diaryCode == 'RENTREV'"
                                                            :href="
                                                                '?command=chargeReview_v2&module=leases&selectionMethod=property&property_code=' +
                                                                item.propertyID +
                                                                '&lease_code=' +
                                                                item.leaseID
                                                            "
                                                            >{{ diary_type[item.diaryCode] }}</a
                                                        ><span v-else>{{ diary_type[item.diaryCode] }}</span></b
                                                    >{{ item.diaryText ? ' : ' + item.diaryText : '' }}
                                                </td>

                                                <td
                                                    v-if="item.propertyID != '' && user_sub_type != 'PMRO'"
                                                    style="text-align: right"
                                                >
                                                    <v-icon
                                                        :class="
                                                            lease_diary_comment_list[item.diaryID] > 0
                                                                ? 'diary-comment-btn green-diary-comment-btn'
                                                                : 'diary-comment-btn'
                                                        "
                                                        :style="
                                                            lease_diary_comment_list[item.diaryID] > 0
                                                                ? 'color:green'
                                                                : ''
                                                        "
                                                        v-if="item.diaryCommentFlag != ''"
                                                        @click="
                                                            showDiaryCommentModal(
                                                                item.diaryID,
                                                                item.propertyID,
                                                                item.leaseID,
                                                                item.diaryDate,
                                                                diary_type[item.diaryCode],
                                                                item.diaryText,
                                                            )
                                                        "
                                                        >comment
                                                    </v-icon>
                                                    <img
                                                        v-else
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/msg.png'"
                                                        style="cursor: pointer"
                                                    />
                                                </td>
                                            </tr>
                                        </template>
                                    </v-data-table>
                                </div>

                                <v-card
                                    class="section-toolbar"
                                    dark
                                    color="titleHeader"
                                    text
                                    tile
                                    v-if="calendar_three_months.length"
                                >
                                    <v-card-actions>
                                        <h6
                                            class="title font-weight-black"
                                            style="width: 100%"
                                        >
                                            <img
                                                :src="assetDomain + 'assets/images/icons/weather_cloudy.png'"
                                                style="vertical-align: middle"
                                            />
                                            Future Items (3 Months)

                                            <div
                                                class="text-right"
                                                style="float: right"
                                            >
                                                <cirrus-input
                                                    class="dashboard-search-field dashboard-diary-search"
                                                    inputFormat="search"
                                                    v-model="search_datatable2"
                                                    placeholder="Search"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </div>
                                        </h6>
                                    </v-card-actions>
                                </v-card>

                                <div
                                    class="page-form"
                                    v-if="calendar_three_months.length"
                                >
                                    <table
                                        class="data-grid"
                                        width="100%"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                    >
                                        <tr
                                            v-for="(calendarEvent, index) in calendar_three_months"
                                            :key="index"
                                            :class="calendar_three_months[index].propertyID == '' ? 'highlight' : ''"
                                        ></tr>
                                    </table>

                                    <v-data-table
                                        class="c8-datatable-custom documents-table"
                                        v-show="calendar_three_months.length > 0"
                                        dense
                                        item-key="id"
                                        :headers="user_sub_type != 'PMRO' ? headers : headersPMRO"
                                        :items="calendar_three_months"
                                        :items-per-page="10"
                                        :search="search_datatable2"
                                        :custom-sort="customSort"
                                        :custom-filter="customSearch"
                                    >
                                        <template v-slot:item="{ item }">
                                            <tr
                                                :key="index"
                                                :class="item.propertyID == '' ? 'highlight' : ''"
                                            >
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <v-icon
                                                        color="red"
                                                        title="Delete"
                                                        @click="
                                                            delete_diary(
                                                                item.propertyID,
                                                                item.diaryID,
                                                                calendar_three_months.indexOf(item),
                                                                1,
                                                            )
                                                        "
                                                    >
                                                        close
                                                    </v-icon>
                                                    <!--                              <a @click="delete_diary(item.propertyID,item.diaryID , calendar_three_months.indexOf(item),1)" title="Delete" href="#" >-->
                                                    <!--                                  <img x-small :src="assetDomain + 'assets/images/icons/delete_v2.png'" /></a>-->
                                                </td>
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <img
                                                        v-if="item.diaryCompleted != null && item.diaryCompleted != ''"
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/accept.png'"
                                                    />
                                                    <v-icon
                                                        class="edit-diary"
                                                        small
                                                        @click="
                                                            copy_diary(
                                                                item,
                                                                item.diaryID,
                                                                calendar_three_months.indexOf(item),
                                                                1,
                                                            )
                                                        "
                                                        v-if="item.diaryCompleted == null || item.diaryCompleted == ''"
                                                        title="Edit diary item"
                                                    >
                                                        fas fa-edit
                                                    </v-icon>
                                                </td>
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <img
                                                        v-if="item.diaryCompleted != null && item.diaryCompleted != ''"
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/accept.png'"
                                                    />
                                                    <v-icon
                                                        color="green"
                                                        @click="
                                                            complete_diary(
                                                                item.diaryID,
                                                                calendar_three_months.indexOf(item),
                                                                1,
                                                            )
                                                        "
                                                        v-if="item.diaryCompleted == null || item.diaryCompleted == ''"
                                                        title="Close diary item"
                                                    >
                                                        check
                                                        <!--                                  <img   class="icon domroll assets/images/icons/add.png" :src="assetDomain + 'assets/images/icons/delete_green.png'">-->
                                                    </v-icon>
                                                </td>

                                                <td v-if="item.propertyID != ''">
                                                    {{ item.diaryDate }}
                                                </td>
                                                <td v-if="item.propertyID != ''">
                                                    <a
                                                        title="View property details"
                                                        :href="
                                                            user_type == 'C'
                                                                ? '?command=property_summary_page&module=properties&property_code=' +
                                                                  item.propertyID
                                                                : '?command=v2_manage_property_page&module=properties&property_code=' +
                                                                  item.propertyID
                                                        "
                                                        >{{ item.propertyID }}</a
                                                    ><br />{{ item.propertyName }}
                                                </td>
                                                <td v-if="item.propertyID != ''">
                                                    <a
                                                        v-if="item.leaseID != ''"
                                                        title="View lease details"
                                                        :href="
                                                            user_type == 'C'
                                                                ? '?command=lease_summary_page&module=leases&lease_code=' +
                                                                  item.leaseID +
                                                                  '&property_code=' +
                                                                  item.propertyID
                                                                : '?command=lease_page_v2&module=leases&lease_code=' +
                                                                  item.leaseID +
                                                                  '&property_code=' +
                                                                  item.propertyID
                                                        "
                                                        >{{ item.leaseID }}</a
                                                    >
                                                    <br />{{ item.leaseName }}
                                                </td>

                                                <td
                                                    v-if="item.propertyID != ''"
                                                    width=""
                                                >
                                                    <b>{{ diary_type[item.diaryCode] }}</b
                                                    >{{ item.diaryText ? ' : ' + item.diaryText : '' }}
                                                </td>

                                                <td
                                                    v-if="item.propertyID != '' && user_sub_type != 'PMRO'"
                                                    style="text-align: right"
                                                >
                                                    <v-icon
                                                        :class="
                                                            lease_diary_comment_list[item.diaryID] > 0
                                                                ? 'diary-comment-btn green-diary-comment-btn'
                                                                : 'diary-comment-btn'
                                                        "
                                                        :style="
                                                            lease_diary_comment_list[item.diaryID] > 0
                                                                ? 'color:green'
                                                                : ''
                                                        "
                                                        v-if="item.diaryCommentFlag != ''"
                                                        @click="
                                                            showDiaryCommentModal(
                                                                item.diaryID,
                                                                item.propertyID,
                                                                item.leaseID,
                                                                item.diaryDate,
                                                                diary_type[item.diaryCode],
                                                                item.diaryText,
                                                            )
                                                        "
                                                        >comment
                                                    </v-icon>
                                                    <img
                                                        v-else
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/msg.png'"
                                                        style="cursor: pointer"
                                                    />
                                                </td>
                                            </tr>
                                        </template>
                                    </v-data-table>
                                </div>

                                <v-card
                                    class="section-toolbar"
                                    dark
                                    color="titleHeader"
                                    text
                                    tile
                                    v-if="calendar_overdue.length"
                                >
                                    <v-card-actions>
                                        <h6
                                            class="title font-weight-black"
                                            style="width: 100%"
                                        >
                                            <img
                                                :src="assetDomain + 'assets/images/icons/warning.png'"
                                                style="vertical-align: middle"
                                            />
                                            Overdue Diary Items (50 oldest)

                                            <div
                                                class="text-right"
                                                style="float: right"
                                            >
                                                <cirrus-input
                                                    class="dashboard-search-field dashboard-diary-search"
                                                    inputFormat="search"
                                                    v-model="search_datatable3"
                                                    placeholder="Search"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </div>
                                        </h6>
                                    </v-card-actions>
                                </v-card>
                                <div
                                    class="page-form"
                                    v-if="calendar_overdue.length"
                                >
                                    <table
                                        class="data-grid"
                                        width="100%"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                    >
                                        <tr
                                            v-for="(calendarEvent, index) in calendar_overdue"
                                            :key="index"
                                            :class="calendar_overdue[index].propertyID == '' ? 'highlight' : ''"
                                        >
                                            <td
                                                colspan="7"
                                                v-if="calendarEvent.propertyID == ''"
                                            >
                                                cirrus8 Event: {{ calendarEvent.diaryText }}
                                            </td>
                                        </tr>
                                    </table>

                                    <v-data-table
                                        class="c8-datatable-custom documents-table"
                                        v-show="calendar_overdue.length > 0"
                                        dense
                                        item-key="id"
                                        :headers="user_sub_type != 'PMRO' ? headers : headersPMRO"
                                        :items="calendar_overdue"
                                        :items-per-page="10"
                                        :search="search_datatable3"
                                        :custom-sort="customSort"
                                        :custom-filter="customSearch"
                                    >
                                        <template v-slot:item="{ item }">
                                            <tr
                                                :key="index"
                                                :class="item.propertyID == '' ? 'highlight' : ''"
                                            >
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <v-icon
                                                        color="red"
                                                        title="Delete"
                                                        @click="
                                                            delete_diary(
                                                                item.propertyID,
                                                                item.diaryID,
                                                                calendar_overdue.indexOf(item),
                                                                2,
                                                            )
                                                        "
                                                    >
                                                        close
                                                    </v-icon>
                                                    <!--                              <a @click="delete_diary(item.propertyID,item.diaryID , calendar_three_months.indexOf(item),1)" title="Delete" href="#" >-->
                                                    <!--                                  <img x-small :src="assetDomain + 'assets/images/icons/delete_v2.png'" /></a>-->
                                                </td>
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <img
                                                        v-if="item.diaryCompleted != null && item.diaryCompleted != ''"
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/accept.png'"
                                                    />
                                                    <v-icon
                                                        class="edit-diary"
                                                        small
                                                        @click="
                                                            copy_diary(
                                                                item,
                                                                item.diaryID,
                                                                calendar_overdue.indexOf(item),
                                                                2,
                                                            )
                                                        "
                                                        v-if="item.diaryCompleted == null || item.diaryCompleted == ''"
                                                        title="Edit diary item"
                                                    >
                                                        fas fa-edit
                                                    </v-icon>
                                                </td>
                                                <td v-if="item.propertyID != '' && user_sub_type != 'PMRO'">
                                                    <img
                                                        v-if="item.diaryCompleted != null && item.diaryCompleted != ''"
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/accept.png'"
                                                    />
                                                    <v-icon
                                                        color="green"
                                                        @click="
                                                            complete_diary(
                                                                item.diaryID,
                                                                calendar_overdue.indexOf(item),
                                                                2,
                                                            )
                                                        "
                                                        v-if="item.diaryCompleted == null || item.diaryCompleted == ''"
                                                        title="Close diary item"
                                                    >
                                                        <!--                                  <img class="icon domroll assets/images/icons/add.png" :src="assetDomain + 'assets/images/icons/bullet_go.png'">-->
                                                        check
                                                    </v-icon>
                                                </td>
                                                <td v-if="item.propertyID != ''">
                                                    {{ item.diaryDate }}
                                                </td>

                                                <td v-if="item.propertyID != ''">
                                                    <a
                                                        title="View property details"
                                                        :href="
                                                            user_type == 'C'
                                                                ? '?command=property_summary_page&module=properties&property_code=' +
                                                                  item.propertyID
                                                                : '?command=v2_manage_property_page&module=properties&property_code=' +
                                                                  item.propertyID
                                                        "
                                                        >{{ item.propertyID }}</a
                                                    ><br />{{ item.propertyName }}
                                                </td>
                                                <td v-if="item.propertyID != ''">
                                                    <a
                                                        v-if="item.leaseID != ''"
                                                        title="View lease details"
                                                        :href="
                                                            user_type == 'C'
                                                                ? '?command=lease_summary_page&module=leases&lease_code=' +
                                                                  item.leaseID +
                                                                  '&property_code=' +
                                                                  item.propertyID
                                                                : '?command=lease_page_v2&module=leases&lease_code=' +
                                                                  item.leaseID +
                                                                  '&property_code=' +
                                                                  item.propertyID
                                                        "
                                                        >{{ item.leaseID }}</a
                                                    >
                                                    <br />{{ item.leaseName }}
                                                </td>

                                                <td
                                                    v-if="item.propertyID != ''"
                                                    width=""
                                                >
                                                    <b>{{ diary_type[item.diaryCode] }}</b
                                                    >{{ item.diaryText ? ' : ' + item.diaryText : '' }}
                                                </td>

                                                <td
                                                    v-if="item.propertyID != '' && user_sub_type != 'PMRO'"
                                                    style="text-align: right"
                                                >
                                                    <v-icon
                                                        :class="
                                                            lease_diary_comment_list[item.diaryID] > 0
                                                                ? 'diary-comment-btn green-diary-comment-btn'
                                                                : 'diary-comment-btn'
                                                        "
                                                        :style="
                                                            lease_diary_comment_list[item.diaryID] > 0
                                                                ? 'color:green'
                                                                : ''
                                                        "
                                                        v-if="item.diaryCommentFlag != ''"
                                                        @click="
                                                            showDiaryCommentModal(
                                                                item.diaryID,
                                                                item.propertyID,
                                                                item.leaseID,
                                                                item.diaryDate,
                                                                diary_type[item.diaryCode],
                                                                item.diaryText,
                                                            )
                                                        "
                                                        >comment
                                                    </v-icon>
                                                    <img
                                                        v-else
                                                        class="icon"
                                                        :src="assetDomain + 'assets/images/icons/msg.png'"
                                                        style="cursor: pointer"
                                                    />
                                                </td>
                                            </tr>
                                        </template>
                                    </v-data-table>
                                </div>
                            </v-col>
                        </v-row>
                    </v-tab-item>

                    <v-tab-item :value="'tab-2'">
                        <br />
                        <property-list-component @eventname="updateTabs"></property-list-component>
                    </v-tab-item>

                    <v-tab-item :value="'tab-3'">
                        <br />
                        <lease-list-component></lease-list-component>
                    </v-tab-item>

                    <v-tab-item :value="'tab-4'">
                        <br />
                        <company-list-component></company-list-component>
                    </v-tab-item>

                    <v-tab-item
                        v-if="user_type == 'A'"
                        :value="'tab-5'"
                    >
                        <br />
                        <ledger-list-component></ledger-list-component>
                    </v-tab-item>
                </v-tabs>
            </div>
        </div>

        <!-- UPDATING EXISTING TASK -->
        <v-dialog
            v-model="showUpdateForm"
            content-class="c8-page"
        >
            <task-update-component
                :visible="showUpdateForm"
                :task_id="task_row_id"
                @close="showUpdateForm = false"
            ></task-update-component>
        </v-dialog>
        <!-- END OF ADDING NEW TASK -->

        <!-- COMMENT SECTION -->
        <v-dialog
            v-model="showCommentForm"
            content-class="c8-page"
        >
            <task-comment-component
                :visible="showCommentForm"
                :task_id="task_row_id"
                @close="showCommentForm = false"
            ></task-comment-component>
        </v-dialog>
        <!-- END OF COMMENT SECTION -->

        <v-dialog
            v-model="show_comment_modal"
            max-width="1000"
            content-class="c8-page"
            scrollable
        >
            <v-card>
                <v-card-title class="headline">
                    Diary Comment
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_comment_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div
                        class="page-form"
                        v-if="show_comment_modal"
                    >
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Property:
                            </v-col>
                            <v-col
                                xs="12"
                                sm="4"
                                md="4"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ diary_comment_property }}</strong>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Lease:
                            </v-col>
                            <v-col
                                xs="12"
                                sm="4"
                                md="4"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ diary_comment_leases }}</strong>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Date:
                            </v-col>
                            <v-col
                                xs="12"
                                sm="4"
                                md="4"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ diary_comment_date }}</strong>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Entry Type:
                            </v-col>
                            <v-col
                                xs="12"
                                sm="4"
                                md="4"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ diary_comment_enry_type }}</strong>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Notes:
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ diary_comment_notes }}</strong>
                            </v-col>
                        </v-row>
                        <v-container>
                            <cirrus-server-error :errorMsg2="error_diary_update_comment"></cirrus-server-error>
                            <v-row
                                dense
                                v-for="(
                                    diary_modal_list_temp_data, diary_modal_list_temp_index
                                ) in diary_modal_list_temp"
                                :key="diary_modal_list_temp_index"
                            >
                                <v-col cols="12">
                                    <v-card
                                        color=""
                                        class="diary-comment-item-container"
                                        style="margin-bottom: 6px"
                                    >
                                        <v-card-actions class="pa-3">
                                            <strong>{{ diary_modal_list_temp_index + 1 }}.&nbsp</strong>
                                            <span>
                                                Created at {{ diary_modal_list_temp_data.created_date }} &nbsp Updated
                                                at {{ diary_modal_list_temp_data.updated_date }} &nbsp by
                                                {{ diary_modal_list_temp_data.created_by }}
                                            </span>
                                            <v-spacer></v-spacer>
                                            <v-btn
                                                x-small
                                                @click="updateDiaryComment(diary_modal_list_temp_index)"
                                                >Update
                                            </v-btn>
                                            <v-icon
                                                color="red"
                                                @click="deleteDiaryComment(diary_modal_list_temp_index)"
                                                >close
                                            </v-icon>
                                        </v-card-actions>
                                        <v-divider light></v-divider>
                                        <v-layout>
                                            <v-flex xs12>
                                                <v-textarea
                                                    v-model="diary_modal_list_temp_data.diary_comment"
                                                    auto-grow
                                                    rows="1"
                                                    full-width
                                                    class="noteTextArea"
                                                    maxlength="1800"
                                                ></v-textarea>
                                            </v-flex>
                                        </v-layout>
                                    </v-card>
                                </v-col>
                            </v-row>
                            <cirrus-server-error :errorMsg2="error_diary_new_comment"></cirrus-server-error>
                            <v-row dense>
                                <v-col cols="12">
                                    <v-card
                                        color=""
                                        class="new-comment-modal-container"
                                        style="margin-bottom: 6px"
                                    >
                                        <v-card-actions class="pa-3">
                                            <strong>Add New Comment</strong>
                                            <v-spacer></v-spacer>
                                            <v-icon
                                                color="green"
                                                @click="addDiaryComment()"
                                                >add
                                            </v-icon>
                                        </v-card-actions>
                                        <v-divider light></v-divider>
                                        <v-layout>
                                            <v-flex xs12>
                                                <v-textarea
                                                    v-model="diary_modal_new_comment"
                                                    auto-grow
                                                    rows="1"
                                                    full-width
                                                    class="noteTextArea"
                                                    maxlength="1800"
                                                ></v-textarea>
                                            </v-flex>
                                        </v-layout>
                                    </v-card>
                                </v-col>
                            </v-row>
                        </v-container>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="dialogActivity"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Task Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="dialogActivity = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <task-activity-log-component
                        v-if="dialogActivity"
                        :task_id="activity_task_id"
                    ></task-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="dialogActivity = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--  UPDATE/COPY DIARY      -->
        <v-dialog
            v-model="copy_modal"
            max-width="1000"
            content-class="c8-page"
            class="dialog-edit-diary"
        >
            <v-card>
                <v-card-title class="headline">
                    Diary Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="copy_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Diary Entry Type
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        :id="'copy_diary_type'"
                                        v-model="copy_diary_type"
                                        :options="diary_type_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-800"
                                        :custom-label="nameWithDash"
                                        group-label="language"
                                        placeholder="Please select..."
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                    >
                                        <span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Diary Date
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'copy_diary_date'"
                                        :size="'40'"
                                        v-model="copy_diary_date"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Diary Note
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :id="'copy_diary_note'"
                                        v-model="copy_diary_note"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Copy Comments
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-checkbox
                                        v-model="copy_diary_comment"
                                        label=""
                                        ripple="false"
                                        dense
                                        style="width: auto; margin-right: 26px"
                                    ></v-checkbox>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="updateDiaryItem()"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="copyDiaryItem()"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >content_copy
                        </v-icon>
                        Copy and Close *
                    </v-btn>
                    <v-spacer></v-spacer>
                </v-card-actions>
                <div style="padding: 10px !important">
                    * Close original diary item and Create new diary item with above details.
                </div>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
import { mapState } from 'vuex';
import axios from 'axios';
import PropertiesList from '../Forms/PropertiesList.vue';
import LeaseList from '../Forms/LeaseList.vue';
import CompanyList from '../Forms/CompanyList.vue';
import LedgerList from '../Forms/LedgerList.vue';
import { bus } from '../../../../plugins/bus';
import Vue from 'vue';

const Swal = require('sweetalert2');

Vue.component('task-activity-log-component', require('../../TaskManagement/forms/TaskActivityLog.vue').default);
//Vue.component('task-create-component', require('../../TaskManagement/Forms/TaskCreateForm').default);
Vue.component('task-update-component', require('../../TaskManagement/forms/TaskUpdateForm.vue').default);
Vue.component('task-comment-component', require('../../TaskManagement/forms/TaskCommentForm.vue').default);

export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialLeaseCode: String,
        initialVersionId: String,
    },
    components: {
        'property-list-component': PropertiesList,
        'lease-list-component': LeaseList,
        'company-list-component': CompanyList,
        'ledger-list-component': LedgerList,
    },
    data() {
        return {
            ai_pm_approval: '',
            ai_ta_approval: '',
            user_sub_type: localStorage.getItem('user_sub_type'),
            assetDomain: this.$assetDomain,
            defaultPortfolio: 1,
            hide_process: 1,
            manager_list: [],
            manager_code: { field_key: '', field_value: 'All Portfolios' },
            cur_day: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
            months: [
                'January',
                'February',
                'March',
                'April',
                'May',
                'June',
                'July',
                'August',
                'September',
                'October',
                'November',
                'December',
            ],
            error_diary_new_comment: [],
            error_diary_update_comment: [],
            diary_modal_new_comment: '',
            diary_comment_property: '',
            diary_comment_leases: '',
            diary_comment_date: '',
            diary_comment_enry_type: '',
            diary_comment_notes: '',
            show_comment_modal: false,
            diary_modal_list_temp: [],
            selected_diary: '',
            lease_diary_comment_list: [],
            diary_type: [],
            pass_to_server: '',
            today: '',
            country_code: '',
            trust_account_label: '',
            calendar_current: '',
            calendar_selected: '',
            calendar: [],
            load_tab1: false,
            tasks_assign: [],
            calendar_event: [],
            calendar_three_months: [],
            calendar_overdue: [],
            quick_links: [],
            count_forms: [],

            review_list: [],
            review_list_headers: [
                { text: 'Property', value: 'property' },
                { text: 'Lease', value: 'lease' },
                { text: 'Company', value: 'company' },
                { text: 'Task', value: 'description' },
                { text: 'Subject', value: 'subject' },
            ],

            asset_domain: '',
            property_code: '',
            lease_code: '',
            version_id: '',
            property_list: [],
            lease_list: [],
            lease_main_list: [],
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            template_tab: null,
            loading_setting: false,
            loading_page_setting: false,

            dialogActivity: false,
            activities: [],
            activity_task_id: 0,

            showUpdateForm: false,
            task_data: {},
            showCreateForm: false,
            showCommentForm: false,
            show_lease_beta: '3',
            show_property_beta: '3',
            task_row_id: 0,

            headers: [
                { text: '', value: 'action1', sortable: false, align: 'left', width: '10px' },
                { text: '', value: 'action2', sortable: false, align: 'left', width: '10px' },
                { text: '', value: 'action3', sortable: false, align: 'left', width: '10px' },
                { text: 'Diary Date', value: 'diaryDate', sortable: true, align: 'left' },
                { text: 'Property', value: 'propertyID', sortable: true, align: 'left' },
                { text: 'Lease', value: 'leaseID', sortable: true, align: 'left' },
                { text: 'Diary Item', value: 'diaryText', sortable: true, align: 'left', width: '220px' },
                { text: '', value: 'field5', sortable: false, align: 'left' },
            ],
            headersPMRO: [
                { text: 'Property', value: 'propertyID', sortable: true, align: 'left' },
                { text: 'Lease', value: 'leaseID', sortable: true, align: 'left' },
                { text: 'Diary Date', value: 'diaryDate', sortable: true, align: 'left' },
                { text: 'Diary Item', value: 'diaryText', sortable: true, align: 'left' },
            ],
            search_datatable1: '',
            search_datatable2: '',
            search_datatable3: '',
            copy_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            success_flag: false,
            lease_diary_add_arr: [],
            copy_diary_type: '',
            copy_diary_note: '',
            copy_diary_date: '',
            copy_diary_comment: false,
            diary_type_list: [],
            show_progress: false,
        };
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'clientTimezone']),
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadManagerList();
        this.loadDashboard();
        this.loadDashboardBeta();
    },
    methods: {
        customSearch(value, search, item) {
            return Object.values(item).some(
                (v) => v && v.toString().toLowerCase().includes(search.toString().toLowerCase()),
            );
        },
        customSort: function (items, index, isDesc) {
            items.sort((a, b) => {
                if (index[0] == 'diaryDate') {
                    let dataA = a[index].split('/');
                    let dataB = b[index].split('/');

                    if (!isDesc[0]) {
                        return (
                            new Date(dataB[2] + '-' + dataB[1] + '-' + dataB[0]) -
                            new Date(dataA[2] + '-' + dataA[1] + '-' + dataA[0])
                        );
                    } else {
                        return (
                            new Date(dataA[2] + '-' + dataA[1] + '-' + dataA[0]) -
                            new Date(dataB[2] + '-' + dataB[1] + '-' + dataB[0])
                        );
                    }
                } else {
                    if (typeof a[index] !== 'undefined' && a[index]) {
                        if (b[index] === null) return -1;
                        if (!isDesc[0]) {
                            return a[index].toLowerCase().localeCompare(b[index].toLowerCase());
                        } else {
                            return b[index].toLowerCase().localeCompare(a[index].toLowerCase());
                        }
                    }
                }
            });
            return items;
        },
        showCommentModal: function (data) {
            this.showCommentForm = true;
            this.task_row_id = data.task_row_id;
        },
        showEditModal: function (data) {
            this.showUpdateForm = true;
            this.task_row_id = data.task_row_id;
        },
        showActivityModal: function (task_id) {
            this.activity_task_id = task_id;
            this.dialogActivity = true;
        },
        loadManagerList: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('no_load', true);
            form_data.append('user_type', localStorage.getItem('user_type'));
            form_data.append('show_header', true);
            form_data.append('display_with_prop_only', true);
            this.$api.post(this.cirrus8_api_url + 'api/loadPortfolioManagersList', form_data).then((response) => {
                let head = [{ field_key: '', field_value: 'All Portfolios' }];
                this.manager_list = head.concat(response.data.pm_data);

                if (localStorage.getItem('user_type') != 'A') {
                    head = [{ field_key: 'myTask', field_value: 'My Task Only' }];
                    this.manager_list = this.manager_list.concat(head);
                }
                this.loading_content_setting = false;
            });
        },
        loadDashboardBeta: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('no_load', true);
            form_data.append('user_type', localStorage.getItem('user_type'));
            this.$api.post(this.cirrus8_api_url + 'api/loadDashboardVue', form_data).then((response) => {
                this.show_lease_beta = response.data.show_lease_beta;
                this.show_property_beta = response.data.show_property_beta;
            });
        },
        dateOrdinal: function (d) {
            return (
                d + (31 == d || 21 == d || 1 == d ? 'st' : 22 == d || 2 == d ? 'nd' : 23 == d || 3 == d ? 'rd' : 'th')
            );
        },
        updateTabs(variable) {
            this.template_tab = variable;
        },
        showDiaryCommentModal: function (index, property, leases, date, type, notes) {
            this.diary_modal_new_comment = '';
            this.diary_comment_property = property;
            this.diary_comment_leases = leases;
            this.diary_comment_date = date;
            this.diary_comment_enry_type = type;
            this.diary_comment_notes = notes;
            this.selected_diary = index;

            if (this.lease_diary_comment_list[index] == 0) {
                this.diary_modal_list_temp = [];
                this.show_comment_modal = true;
            } else {
                var form_data = new FormData();
                form_data.append('diaryID', index);
                this.$api.post(this.cirrus8_api_url + 'api/home/<USER>', form_data).then((response) => {
                    this.diary_modal_list_temp = response.data;
                    this.show_comment_modal = true;
                });
            }
        },
        addDiaryComment: function () {
            this.error_diary_new_comment = [];
            let diary_id = this.selected_diary;
            let diary_modal_new_comment = this.diary_modal_new_comment;

            if (diary_modal_new_comment === '') {
                this.error_diary_new_comment.push(['You have not entered a valid diary comment.']);
            }

            if (this.error_diary_new_comment.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('diary_id', diary_id);
                form_data.append('diary_modal_new_comment', diary_modal_new_comment);

                let apiUrl = this.cirrus8_api_url + 'api/lease/create/diary-comment';

                this.$api.post(apiUrl, form_data).then((response) => {
                    this.diary_modal_new_comment = '';
                    let new_diary_comment = response.data.diary_comment;
                    this.diary_modal_list_temp.push(new_diary_comment);
                    this.lease_diary_comment_list[diary_id] += 1;
                });
            }
        },
        updateDiaryComment: function (index) {
            this.error_diary_new_comment = [];
            let diary_modal_list_temp = this.diary_modal_list_temp;
            let diary_id = this.selected_diary;

            let diary_modal_comment = diary_modal_list_temp[index].diary_comment;
            let diary_comment_id = diary_modal_list_temp[index].diary_comment_id;

            if (diary_modal_comment === '') {
                this.error_diary_update_comment.push(['You have not entered a valid diary comment.']);
            }

            if (this.error_diary_new_comment.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('diary_id', diary_id);
                form_data.append('diary_comment_id', diary_comment_id);
                form_data.append('diary_modal_new_comment', diary_modal_comment);

                let apiUrl = this.cirrus8_api_url + 'api/lease/update/diary-comment';
                this.$api.post(apiUrl, form_data).then((response) => {
                    let diary_comment = response.data.diary_comment;
                    this.diary_modal_list_temp[index] = diary_comment;
                });
            }
        },
        deleteDiaryComment: function (index) {
            Swal.fire({
                title: 'Are you sure?',
                text: 'You will not be able to recover your current changes!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, complete it!',
                cancelButtonText: 'No, keep it',
            }).then((result) => {
                if (result.value) {
                    let diary_modal_list_temp = this.diary_modal_list_temp;
                    let diary_id = this.selected_diary;

                    let diary_modal_comment = diary_modal_list_temp[index].diary_comment;
                    let diary_comment_id = diary_modal_list_temp[index].diary_comment_id;

                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('no_load', true);

                    form_data.append('diary_id', diary_id);
                    form_data.append('diary_comment_id', diary_comment_id);
                    form_data.append('diary_modal_new_comment', diary_modal_comment);
                    let apiUrl = this.cirrus8_api_url + 'api/lease/delete/diary-comment';

                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.diary_modal_list_temp.splice(index, 1);
                        this.lease_diary_comment_list[diary_id] -= 1;
                    });
                }
            });
        },
        copy_diary: function (item, primary, index, event_type) {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.copy_modal = true;

            this.copy_diary_type = item.diaryType;
            this.copy_diary_date = item.diaryDate;
            this.copy_diary_note = item.diaryText;
            this.copy_diary_comment = false;
            this.lease_diary_add_arr = item;
        },
        copyDiaryItem: function () {
            let errorArr = [];
            if (this.copy_diary_date === '' || this.copy_diary_date === null) {
                errorArr.push(['You have not entered a valid diary date.']);
            }
            // if (copy_diary_type === '') {
            //     errorArr.push(["You have not entered a valid diary type."])
            // }
            if (this.copy_diary_note === '') {
                errorArr.push(['You have not entered a valid diary entry.']);
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                this.show_progress = true;

                let propertyID = this.lease_diary_add_arr.propertyID;
                let leaseID = this.lease_diary_add_arr.leaseID;
                let diaryID = this.lease_diary_add_arr.diaryID;
                var form_data = new FormData();
                form_data.append('property_code', propertyID);
                form_data.append('lease_code', leaseID);
                form_data.append('diary_id', diaryID);
                form_data.append('diary_type', this.copy_diary_type.value);
                form_data.append('diary_date', this.copy_diary_date);
                form_data.append('diary_note', this.copy_diary_note);
                form_data.append('copy_diary_comment', this.copy_diary_comment);
                form_data.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/home/<USER>';

                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadCalendar(this.calendar_current);
                    this.loading_setting = false;
                    this.show_progress = false;
                    this.success_flag = true;
                    this.copy_modal = false;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        updateDiaryItem: function () {
            let errorArr = [];
            if (this.copy_diary_date === '' || this.copy_diary_date === null) {
                errorArr.push(['You have not entered a valid diary date.']);
            }
            // if (copy_diary_type === '') {
            //     errorArr.push(["You have not entered a valid diary type."])
            // }
            if (this.copy_diary_note === '') {
                errorArr.push(['You have not entered a valid diary entry.']);
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                this.show_progress = true;

                let propertyID = this.lease_diary_add_arr.propertyID;
                let leaseID = this.lease_diary_add_arr.leaseID;
                let diaryID = this.lease_diary_add_arr.diaryID;
                var form_data = new FormData();
                form_data.append('property_code', propertyID);
                form_data.append('lease_code', leaseID);
                form_data.append('diary_id', diaryID);
                form_data.append('diary_type', this.copy_diary_type.value);
                form_data.append('diary_date', this.copy_diary_date);
                form_data.append('diary_note', this.copy_diary_note);
                form_data.append('copy_diary_comment', this.copy_diary_comment);

                form_data.append('no_load', true);

                let apiUrl = this.cirrus8_api_url + 'api/home/<USER>';

                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadCalendar(this.calendar_current);
                    this.loading_setting = false;
                    this.show_progress = false;
                    this.success_flag = true;
                    this.copy_modal = false;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        delete_diary: function (property, primary, index, event_type) {
            let dis = this;
            Swal.fire({
                text: 'Are you sure you want to delete this diary item?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ok',
                cancelButtonText: 'Cancel',
            }).then((result) => {
                if (result.value) {
                    var form_data = new FormData();
                    form_data.append('diary', primary);
                    form_data.append('property', property);

                    this.$api.post(this.cirrus8_api_url + 'api/home/<USER>', form_data).then((response) => {
                        if (event_type == 0) dis.$delete(this.calendar_event[this.pass_to_server]['events'], index);
                        if (event_type == 1) dis.$delete(this.calendar_three_months, index);
                        if (event_type == 2) dis.$delete(this.calendar_overdue, index);
                    });
                }
            });
        },
        complete_diary: function (primary, index, event_type) {
            let dis = this;
            Swal.fire({
                text: 'This will mark the diary item as completed. Are you certain?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ok',
                cancelButtonText: 'Cancel',
            }).then((result) => {
                if (result.value) {
                    var form_data = new FormData();
                    form_data.append('diary', primary);

                    this.$api.post(this.cirrus8_api_url + 'api/home/<USER>', form_data).then((response) => {
                        if (event_type == 0) dis.$delete(this.calendar_event[this.pass_to_server]['events'], index);
                        if (event_type == 1) dis.$delete(this.calendar_three_months, index);
                        if (event_type == 2) dis.$delete(this.calendar_overdue, index);
                    });
                }
            });
        },

        update_calendar: function (action) {
            switch (action) {
                case 'minusYear':
                    this.calendar_current = new Date(
                        this.calendar_current.setFullYear(this.calendar_current.getFullYear() - 1),
                    );
                    break;
                case 'minusMonth':
                    this.calendar_current = new Date(
                        this.calendar_current.setMonth(this.calendar_current.getMonth() - 1),
                    );
                    break;
                case 'plusMonth':
                    this.calendar_current = new Date(
                        this.calendar_current.setMonth(this.calendar_current.getMonth() + 1),
                    );
                    break;
                case 'plusYear':
                    this.calendar_current = new Date(
                        this.calendar_current.setFullYear(this.calendar_current.getFullYear() + 1),
                    );
                    break;
            }

            this.loadCalendar(this.calendar_current);
        },
        loadCalendar: function (curr) {
            var d = new Date(curr.getMonth() + 1 + '/' + curr.getDate() + '/' + curr.getFullYear());
            var e = new Date(curr.getMonth() + 1 + '/' + curr.getDate() + '/' + curr.getFullYear());

            this.calendar_selected = d.toLocaleString('default', { month: 'long' }) + ' ' + d.getFullYear();
            this.calendar_current = new Date(curr.getMonth() + 1 + '/' + curr.getDate() + '/' + curr.getFullYear());

            let container = [];
            if (d.getDate() > 1) {
                let x;
                for (x = 1; x <= d.getDate(); x++) {
                    var current = new Date(d.getMonth() + 1 + '/' + x + '/' + d.getFullYear());
                    container.push(current);
                }

                let last = parseInt(this.lastday(d.getFullYear(), d.getMonth()));
                let y = 1;
                let cur_date = d.getDate();
                for (x = cur_date; x < last; x++) {
                    var current = new Date(d.getMonth() + 1 + '/' + (d.getDate() + y++) + '/' + d.getFullYear());
                    container.push(current);
                }
            } else {
                let x;
                let last = parseInt(this.lastday(d.getFullYear(), d.getMonth()));
                let y = 0;
                let cur_date = d.getDate();
                for (x = cur_date; x <= last; x++) {
                    var current = new Date(d.getMonth() + 1 + '/' + (d.getDate() + y++) + '/' + d.getFullYear());
                    container.push(current);
                }
            }

            let prev = new Date(d.setMonth(d.getMonth() - 1));
            let next = new Date(e.setMonth(e.getMonth() + 1));

            let x;
            let y = 0;
            let index = 0;
            this.calendar = [];
            this.calendar[index] = [];
            for (x = 0; x < container.length; x++) {
                if (x == 0 && container[x].getDay() != 1) {
                    let last = parseInt(this.lastday(prev.getFullYear(), prev.getMonth()));
                    let loop_prev = container[x].getDay() == 0 ? 7 : container[x].getDay();
                    for (y = 1; y < loop_prev; y++) {
                        var current = new Date(
                            prev.getMonth() + 1 + '/' + (last - (loop_prev - y - 1)) + '/' + prev.getFullYear(),
                        );
                        this.calendar[index].push({
                            current: current,
                            date: current.getMonth() + 1 + '/' + current.getDate() + '/' + current.getFullYear(),
                            day: current.getDate(),
                            class: 'inactive',
                        });
                    }
                }

                if (this.calendar[index].length == 7) {
                    index = index + 1;
                    this.calendar[index] = [];
                }

                let selected =
                    curr.getDate() == container[x].getDate()
                        ? 'selected'
                        : container[x].getMonth() +
                                1 +
                                '/' +
                                container[x].getDate() +
                                '/' +
                                container[x].getFullYear() ==
                            this.today
                          ? 'today'
                          : '';
                this.calendar[index].push({
                    current: container[x],
                    date: container[x].getMonth() + 1 + '/' + container[x].getDate() + '/' + container[x].getFullYear(),
                    day: container[x].getDate(),
                    class: selected
                        ? selected
                        : container[x].getDay() == 0 || container[x].getDay() == 6
                          ? 'weekend'
                          : 'active',
                });
            }

            y = 1;
            if (container[container.length - 1].getDay() != 0) {
                for (x = container[container.length - 1].getDay(); x <= 6; x++) {
                    var current = new Date(next.getMonth() + 1 + '/' + y++ + '/' + next.getFullYear());
                    this.calendar[index].push({
                        current: current,
                        date: current.getMonth() + 1 + '/' + current.getDate() + '/' + current.getFullYear(),
                        day: current.getDate(),
                        class: 'inactive',
                    });
                }
            }

            var form_data = new FormData();
            var pass_to_server =
                this.calendar_current.getMonth() +
                1 +
                '/' +
                this.calendar_current.getDate() +
                '/' +
                this.calendar_current.getFullYear();
            this.pass_to_server = pass_to_server;
            form_data.append('selected', pass_to_server);
            form_data.append('user_type', localStorage.getItem('user_type'));
            form_data.append('user_sub_type', localStorage.getItem('user_sub_type'));
            form_data.append('portfolio', this.manager_code.field_key);
            form_data.append('clientTimezone', localStorage.getItem('clientTimezone'));

            this.$api.post(this.cirrus8_api_url + 'api/home/<USER>', form_data).then((response) => {
                this.calendar_three_months = response.data.data.threeMonthsEvents;
                this.calendar_overdue = response.data.data.overdueEvents;
                this.calendar_event = response.data.data.calendar;
                this.lease_diary_comment_list = response.data.data.diary_comment;
                this.today = response.data.today;
                this.country_code = response.data.country_code;
                this.trust_account_label = response.data.trust_account_label;
                this.diary_type = response.data.data.diary_type;
                this.diary_type_list = response.data.data.diary_type_list;
            });
        },

        lastday: function (y, m) {
            return new Date(y, m + 1, 0).getDate();
        },
        nameWithDash({ field_key, field_value }) {
            if (!field_value) {
                return 'Please select...';
            }

            return !field_key || field_key == 'myTask' ? field_value : `${field_key} — ${field_value}`;
        },
        loadDashboard: function () {
            window.dispatchEvent(new Event('resize'));
            var form_data = new FormData();
            form_data.append('portfolio', this.manager_code.field_key);
            form_data.append('user_type', localStorage.getItem('user_type'));

            form_data.append('defaultPortfolio', localStorage.getItem('user_type') != 'A' ? this.defaultPortfolio : 0);
            this.defaultPortfolio = 0;
            this.$api.post(this.cirrus8_api_url + 'api/home/<USER>', form_data).then((response) => {
                if (response.data.defaultPortfolio && localStorage.getItem('user_type') != 'A') {
                    this.manager_code.field_key = response.data.defaultPortfolio;
                    let dis = this;
                    $.each(this.manager_list, function (i, e) {
                        if (e.field_key.trim() == response.data.defaultPortfolio) {
                            dis.manager_code = { field_key: e.field_key.trim(), field_value: e.field_value };
                        }
                    });
                } else this.loadCalendar(new Date());

                this.quick_links = response.data.data.quickReport;
                this.count_forms = response.data.data.forms;
                this.ai_pm_approval = response.data.data.ai_pm_approval;
                this.ai_ta_approval = response.data.data.ai_ta_approval;
                this.load_tab1 = true;
            });
        },
        loadLeaseDefaultManager: function () {
            let dis = this;
            setTimeout(function () {
                bus.$emit('accept_manager', dis.manager_code.field_key, dis.manager_code.field_value);
            }, 200);
        },
        loadPropDefaultManager: function () {
            let dis = this;
            setTimeout(function () {
                bus.$emit('accept_manager_prop', dis.manager_code.field_key, dis.manager_code.field_value);
            }, 200);
        },
        open_location: function (url) {
            window.location = url;
        },
    },
    watch: {
        manager_code: function (value) {
            this.loadDashboard();
            this.loadCalendar(new Date());
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
    },
    created() {},
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>
<style lang="scss" scoped>
small {
    display: block;
}
</style>
<style type="text/css">
/*.section-toolbar .dashboard-diary-search .v-card__actions .v-input:not(.v-textarea)  input{*/
.section-toolbar .v-card__actions .dashboard-diary-search .v-input:not(.v-textarea) input {
    color: #000000 !important;
    caret-color: #3489a1 !important;
}

.v-data-footer__select {
    display: none;
}

body.c8-dark #frame #container table .edit-diary {
    color: #ffffff !important;
}

.v-dialog.c8-page.v-dialog--active {
    overflow-y: unset;
}

body.c8-dark #frame #container div .theme--light.v-icon.mdi-checkbox-marked {
    color: #fff !important;
    z-index: 999;
}

body.c8-dark #frame #container div .v-icon.mdi-checkbox-blank-outline {
    color: #fff !important;
    z-index: 999;
}

body.c8-dark #frame #container .theme--light.v-btn.v-btn--disabled,
body.c8-dark #frame #container .theme--light.v-btn.v-btn--disabled.v-btn_loading,
body.c8-dark #frame #container .theme--light.v-btn.v-btn--disabled .v-icon {
    color: rgba(255, 255, 255, 26) !important;
}

body.c8-dark #frame #container .theme--light .v-btn--outlined .v-btn__content .v-icon,
body.c8-dark #frame #container .theme--light .v-btn--round .v-btn__content .v-icon {
    color: rgba(255, 255, 255, 26) !important;
}

.v-data-table thead th:nth-child(-n + 3):nth-child(n + 1),
.v-data-table tbody td:nth-child(-n + 3):nth-child(n + 1) {
    padding: 0px !important;
}
</style>
