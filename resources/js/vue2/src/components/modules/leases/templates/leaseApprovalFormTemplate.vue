<style>
.shortcut-div > .v-toolbar__content {
    height: 30px;
}

.v-application .overline {
    font-size: 0.625rem;
    font-weight: 400;
    letter-spacing: 0.1666666667em;
    line-height: 1rem;
    text-transform: uppercase;
    font-family: 'Raleway', sans-serif;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <div class="page-form">
            <div class="form-row">
                <cirrus-loader v-if="loading_page_setting"></cirrus-loader>

                <v-toolbar flat>
                    <v-toolbar-title>
                        <cirrus-page-header
                            :title="form_description + ' - Original Form for ' + property_code + '(' + lease_code + ')'"
                        />
                    </v-toolbar-title>
                    <div class="flex-grow-1"></div>
                    <!--            <v-btn x-small @click="startTour()" v-if="created_by_name!==''" class="v-step-final-message" color="secondary">Tour</v-btn>-->
                </v-toolbar>
                <v-subheader v-if="form_description !== '' && process_state === '3'">
                    These details are as per the original new lease form and may have been updated since submission. Go
                    to &nbsp
                    <a
                        target="_blank"
                        :href="
                            '?module=leases&command=lease_page_v2&property_code=' +
                            property_code +
                            '&lease_code=' +
                            lease_code +
                            ''
                        "
                    >
                        Lease Summary
                    </a>
                    &nbsp for up-to date information.
                </v-subheader>
                <cirrus-server-error
                    :error_msg="error_server_msg"
                    :errorMsg2="error_server_msg2"
                ></cirrus-server-error>

                <v-alert
                    text
                    v-if="errorVacant"
                    prominent
                    dense
                    tile
                    type="error"
                    transition="scale-transition"
                    color="red lighten-2"
                >
                    <v-row
                        align="center"
                        no-gutters
                        dense
                    >
                        <v-col
                            class="col-12"
                            style="font-size: 11px"
                        >
                            The unit you are trying to occupy has an existing tenant. Please
                            <a
                                style="font-weight: bold; color: #b71c1c"
                                target="_blank"
                                :href="
                                    '?command=home&module=leases&searchMethod=property&propertyID=' +
                                    vacateProperty +
                                    '&leaseID=' +
                                    vacateLease +
                                    ''
                                "
                                >vacate</a
                            >
                            before continuing.
                        </v-col>
                    </v-row>
                </v-alert>

                <v-container fluid>
                    <v-row>
                        <v-col
                            cols="12"
                            md="6"
                        >
                            <cirrus-content-loader
                                type="article"
                                v-if="created_by_name === ''"
                            ></cirrus-content-loader>
                            <v-card
                                class="mx-auto"
                                color="#1F7087"
                                shaped
                                dark
                                v-if="created_by_name !== ''"
                            >
                                <v-list-item three-line>
                                    <v-list-item-content>
                                        <div class="overline mb-4">
                                            Property Code:
                                            <span class="v-step-property-code"
                                                >{{ property_code }} - {{ lease_code }}</span
                                            >
                                        </div>
                                        <v-list-item-title class="headline mb-1"
                                            ><span class="v-step-created-by"
                                                >{{ created_by_name }} ({{ created_by_email }})</span
                                            >
                                            - <span class="v-step-created-date">{{ created_date }}</span>
                                        </v-list-item-title>
                                        <v-list-item-subtitle class="v-step-comment">
                                            <pre>{{ comments }}</pre>
                                        </v-list-item-subtitle>
                                    </v-list-item-content>
                                </v-list-item>

                                <v-card-actions>
                                    <v-btn
                                        color="primary"
                                        v-if="process_state === '1'"
                                        class="v-step-modify-lease-code-btn"
                                        @click="modify_lease_code_modal = true"
                                        >Modify Lease Code
                                    </v-btn>
                                    <v-btn
                                        color="primary"
                                        v-if="process_state === '1'"
                                        class="v-step-assign-company-btn"
                                        @click="showReassignCompanyModal()"
                                        >Assign Company
                                    </v-btn>
                                    <v-spacer></v-spacer>
                                    <v-btn
                                        text
                                        color="#fff"
                                        small
                                        right
                                        @click="printLease()"
                                    >
                                        <v-icon>print</v-icon>
                                        PRINT
                                    </v-btn>
                                </v-card-actions>
                            </v-card>
                        </v-col>
                        <v-col
                            cols="12"
                            md="6"
                        >
                            <div v-if="process_state === '1'">
                                <v-textarea
                                    class="vuetifyText v-step-feedback"
                                    outlined
                                    label="Your feedback (This response is sent to the client when either approving or rejecting a form)"
                                    v-model="feedback_comment"
                                    value=""
                                    full-width
                                ></v-textarea>

                                <div style="float: right; margin-left: 10px">
                                    <span>
                                        <v-btn
                                            color="error"
                                            dense
                                            small
                                            class="v-step-form-no-btn"
                                            @click="processRejectForm()"
                                            :disabled="form_no_btn"
                                            ><v-icon>clear</v-icon>Reject Lease</v-btn
                                        >
                                    </span>
                                </div>
                                <div style="float: right; margin-left: 10px">
                                    <span>
                                        <v-btn
                                            color="success"
                                            dense
                                            small
                                            class="v-step-form-yes-btn"
                                            @click="processApproveForm"
                                            :disabled="form_yes_btn"
                                            ><v-icon>done</v-icon>Approve Lease</v-btn
                                        >
                                    </span>
                                </div>
                                <div style="float: right; margin-top: 7px">
                                    <span><strong>Would you like to process this lease?</strong></span>
                                    <!--                                    <span><strong>Would you like to mark this lease as processed?</strong></span>-->
                                </div>
                                <div style="float: left !important">
                                    <v-chip
                                        v-if="error_msg.length > 0 && errorData.id === 'feedback_comment'"
                                        v-for="(errorData, index) in error_msg"
                                        :key="index"
                                        outlined
                                        color="error"
                                    >
                                        <v-icon left>error</v-icon>
                                        {{ errorData.message }}
                                    </v-chip>
                                </div>
                            </div>
                            <div v-if="feedback_comment && process_state !== '1'">
                                <v-textarea
                                    class="vuetifyText v-step-feedback"
                                    outlined
                                    v-model="feedback_comment"
                                    value=""
                                    full-width
                                    readonly
                                ></v-textarea>
                            </div>
                        </v-col>
                    </v-row>
                </v-container>
                <!--                <v-card-->
                <!--                        color="grey lighten-4"-->
                <!--                        flat-->
                <!--                        tile-->
                <!--                        v-if="property_code!=='' && lease_code!=='' && form_mode===0"-->
                <!--                >-->
                <!--                    <v-tabs dark background-color="grey darken-1" centered show-arrows style="z-index: 9999 !important" class="v-step-shortcut">-->
                <!--                        <v-tabs-slider color="grey darken-1"></v-tabs-slider>-->
                <!--                        <v-tab @click="goToHref('tab-1','tenantDetailsSection')">Tenant Details</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-1','mailingDetailsSection')">Mailing Details</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-1','leaseDetailsSection')">Details</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-2','unitDetailsSection')">Unit Details</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-2','leaseUnitChargesSection')">Charges</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-2','leaseManagementFeesSection')">Man Fees</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-2','rentReviewsSection')">Rent Review</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-1','leaseDiarySection')">Diary</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-3','leaseNotesSection')">Notes</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-3','leaseInspectionSection')">Inspection</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-3','leaseInsuranceSection')">Insurance</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-3','leaseGuaranteeSection')">Guarantee</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-4','leaseCommunicationHistorySection')">Communication History</v-tab>-->
                <!--                    </v-tabs>-->
                <!--                </v-card>-->
                <v-tabs
                    icons-and-text
                    class="cirrus-tab-theme"
                    v-if="property_code !== '' && lease_code !== '' && form_mode === 0"
                    v-model="template_tab"
                    show-arrows
                >
                    <v-tabs-slider color="orange"></v-tabs-slider>

                    <v-tab
                        href="#tab-1"
                        v-if="responsive_show"
                        class="v-step-form-tab"
                    >
                        Overview
                        <v-icon
                            small
                            dense
                            >business
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-2"
                        v-if="responsive_show"
                    >
                        Charges and Rent Reviews
                        <v-icon
                            small
                            dense
                            >monetization_on
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-3"
                        v-if="responsive_show"
                    >
                        Diary
                        <v-icon
                            small
                            dense
                            >date_range
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-4"
                        v-if="responsive_show"
                    >
                        Notes and Additional Details
                        <v-icon
                            small
                            dense
                            >notes
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-5"
                        v-if="responsive_show"
                    >
                        Communication and Documents
                        <v-icon
                            small
                            dense
                            >attachment
                        </v-icon>
                    </v-tab>

                    <v-tab
                        href="#tab-1"
                        class="primary--text v-step-form-tab"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >business
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-2"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >monetization_on
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-3"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >date_range
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-4"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >notes
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-5"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >attachment
                        </v-icon>
                    </v-tab>
                    <v-tab-item :value="'tab-1'">
                        <lease-main-form-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-main-form-component>
                        <br />
                        <br />
                        <lease-contacts-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-contacts-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-2'">
                        <lease-unit-details-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                            :show_unit_history="false"
                        ></lease-unit-details-component>
                        <lease-man-fees-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-man-fees-component>
                        <lease-rent-review-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                            :viewRecommendation="false"
                        ></lease-rent-review-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-3'">
                        <lease-diary-component
                            id="leaseDiarySection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-diary-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-4'">
                        <lease-notes-component
                            id="leaseNotesSection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :current_db="current_db"
                            :user_type="user_type"
                            :username="username"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-notes-component>
                        <lease-inspection-component
                            id="leaseInspectionSection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :current_db="current_db"
                            :user_type="user_type"
                            :username="username"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-inspection-component>
                        <lease-insurance-component
                            id="leaseInsuranceSection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :current_db="current_db"
                            :user_type="user_type"
                            :username="username"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-insurance-component>
                        <lease-guarantee-component
                            id="leaseGuaranteeSection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :current_db="current_db"
                            :user_type="user_type"
                            :username="username"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-guarantee-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-5'">
                        <lease-documents-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-documents-component>
                        <v-card
                            class="section-toolbar"
                            dark
                            color="titleHeader"
                            text
                            tile
                        >
                            <v-card-actions
                                ><h6 class="title font-weight-black">Communication History</h6></v-card-actions
                            >
                        </v-card>
                        <lease-email-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-email-component>
                        <lease-sms-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-sms-component>
                    </v-tab-item>
                </v-tabs>

                <v-dialog
                    v-model="modify_lease_code_modal"
                    max-width="1000"
                    content-class="c8-page"
                >
                    <v-card>
                        <v-card-title class="headline">
                            Modify Lease Code
                            <a
                                href="#"
                                class="dialog-close"
                                @click.prevent="modify_lease_code_modal = false"
                            >
                                <v-icon>mdi-close</v-icon>
                            </a>
                        </v-card-title>
                        <v-card-text>
                            <cirrus-server-error :errorMsg2="error_server_msg_modify_lease_modal"></cirrus-server-error>

                            <div class="page-form">
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Lease Code
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <cirrus-input
                                            :id="'new_lease_code'"
                                            v-model="new_lease_code"
                                            :edit_form="true"
                                            size="10"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                        <v-btn
                                            color="primary"
                                            x-small
                                            @click="modifyLeaseCode()"
                                            :disabled="modify_lease_code_btn"
                                        >
                                            Modify
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </div>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer />
                            <v-btn
                                tile
                                x-small
                                text
                                @click="modify_lease_code_modal = false"
                                >Close
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
                <v-dialog
                    v-model="assign_company_modal"
                    max-width="1000"
                    content-class="c8-page"
                >
                    <v-card>
                        <v-card-title class="headline">
                            Re-assign Lease Company
                            <a
                                href="#"
                                class="dialog-close"
                                @click.prevent="assign_company_modal = false"
                            >
                                <v-icon>mdi-close</v-icon>
                            </a>
                        </v-card-title>
                        <v-card-text>
                            <cirrus-server-error
                                :errorMsg2="error_server_msg_assign_company_modal"
                            ></cirrus-server-error>
                            <div class="page-form">
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Existing Company
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <multiselect
                                            tabindex="12"
                                            v-model="reassign_company_existing"
                                            :options="dd_company_list"
                                            :allowEmpty="false"
                                            :options-limit="10000"
                                            class="vue-select2 dropdown-left dropdown-400"
                                            :custom-label="nameWithDash"
                                            group-label="language"
                                            placeholder="Select a company"
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="loading_company_details_modal"
                                >
                                    <v-col
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <cirrus-content-loader type="article"></cirrus-content-loader>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="!loading_company_details_modal"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Company Code
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <cirrus-input
                                            :id="'reassign_company_code'"
                                            v-model="reassign_company_code"
                                            :edit_form="true"
                                            size="10"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="!loading_company_details_modal"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Company Name
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <cirrus-input
                                            :id="'reassign_company_name'"
                                            v-model="reassign_company_name"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="!loading_company_details_modal"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Address
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <cirrus-input
                                            :id="'reassign_company_address'"
                                            v-model="reassign_company_address"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="!loading_company_details_modal"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >{{ suburb_label }}
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                        v-if="reassign_company_country.field_key === 'AU'"
                                        id="lease-suburb"
                                    >
                                        <v-combobox
                                            tabindex="14"
                                            v-model="reassign_company_city"
                                            :maxlength="40"
                                            :items="suburb_list_filtered"
                                            item-value="label"
                                            item-text="label"
                                            @change="suburbSelected(reassign_company_city)"
                                            auto-select-first
                                            hide-selected
                                            persistent-hint
                                            append-icon
                                            :search-input.sync="searchLeaseTenantSuburb"
                                            :hide-no-data="!searchLeaseTenantSuburb"
                                            dense
                                            ref="refLeaseTenantSuburb"
                                            flat
                                        >
                                            <template v-slot:no-data>
                                                <v-list-item>
                                                    <v-chip
                                                        v-model="searchLeaseTenantSuburb"
                                                        small
                                                    >
                                                        {{ searchLeaseTenantSuburb }}
                                                    </v-chip>
                                                </v-list-item>
                                            </template>
                                        </v-combobox>
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                        v-else
                                    >
                                        <cirrus-input
                                            :size="'40'"
                                            :id="'lease_tenant_suburb'"
                                            v-model="reassign_company_city"
                                            tabindex="12"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                            :maxlength="40"
                                        ></cirrus-input>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="!loading_company_details_modal && country_defaults.display_state === true"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >State
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <multiselect
                                            tabindex="15"
                                            @input="tenantStateChanged()"
                                            v-model="reassign_company_state"
                                            :options="getDDCountryStates(reassign_company_country.field_key)"
                                            :allowEmpty="false"
                                            class="vue-select2 dropdown-left dropdown-300"
                                            group-label="language"
                                            placeholder="Select a state"
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="!loading_company_details_modal"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Post Code
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                        v-if="reassign_company_country.field_key === 'AU'"
                                        id="lease-postcode"
                                    >
                                        <v-combobox
                                            tabindex="16"
                                            v-model="reassign_company_postcode"
                                            :maxlength="40"
                                            :items="postcode_list_filtered"
                                            item-value="label"
                                            item-text="label"
                                            @change="postcodeSelected(reassign_company_postcode)"
                                            auto-select-first
                                            hide-selected
                                            persistent-hint
                                            append-icon
                                            :search-input.sync="searchLeaseTenantPostcode"
                                            :hide-no-data="!searchLeaseTenantPostcode"
                                            dense
                                            ref="refLeaseTenantSuburb"
                                            flat
                                        >
                                            <template v-slot:no-data>
                                                <v-list-item>
                                                    <v-chip
                                                        v-model="searchLeaseTenantPostcode"
                                                        small
                                                    >
                                                        {{ searchLeaseTenantPostcode }}
                                                    </v-chip>
                                                </v-list-item>
                                            </template>
                                        </v-combobox>
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                        v-else
                                    >
                                        <cirrus-input
                                            :id="'reassign_company_postcode'"
                                            v-model="reassign_company_postcode"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="!loading_company_details_modal"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Country
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <multiselect
                                            tabindex="17"
                                            @input="triggerCountryChange()"
                                            v-model="reassign_company_country"
                                            :options="dd_country_list"
                                            :allowEmpty="false"
                                            class="vue-select2 dropdown-left dropdown-300"
                                            group-label="language"
                                            placeholder="Select a country"
                                            track-by="field_key"
                                            label="field_value"
                                            :optionHeight="50"
                                            openDirection="bottom"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="!loading_company_details_modal"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        >Email
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <cirrus-input
                                            :id="'reassign_company_email'"
                                            v-model="reassign_company_email"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                    </v-col>
                                </v-row>
                            </div>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer />
                            <v-btn
                                tile
                                x-small
                                color="primary"
                                @click="assignOrUpdateCompany()"
                                :disabled="assign_company_btn"
                                >Assign Company
                            </v-btn>
                            <v-btn
                                tile
                                x-small
                                text
                                @click="assign_company_modal = false"
                                >Close
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
            </div>
        </div>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex';
import LeaseMainForm from '../forms/leaseMainForm.vue';
import LeaseDiary from '../forms/leaseDiary.vue';
import LeaseNotes from '../forms/leaseNotes.vue';
import LeaseGuarantee from '../forms/leaseGuarantee.vue';
import LeaseInsurance from '../forms/leaseInsurance.vue';
import LeaseInspection from '../forms/leaseInspection.vue';
import LeaseRentReview from '../forms/LeaseRentReview.vue';
import LeaseUnitDetails from '../forms/leaseUnitDetails.vue';
import LeaseManagementFees from '../forms/LeaseManagementFees.vue';
import LeaseBonds from '../forms/leaseBonds.vue';
import LeaseOutstandingAmounts from '../forms/leaseOutstandingAmounts.vue';
import LeaseContacts from '../forms/leaseContacts.vue';
import LeaseDocuments from '../../../../DocumentDirectory/sections/LeaseDocumentFormV2.vue';
import LeaseEmail from '../forms/LeaseEmail.vue';
import LeaseSMS from '../forms/LeaseSMS.vue';
import LeaseProfile from '../forms/LeaseProfile.vue';
import global_mixins from '../../../../plugins/mixins';
import suburb_list from '../../../../plugins/australianSuburb.json';

// Vue.use(SuiVue);
export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialLeaseCode: String,
        initialVersionId: String,
        initialCountryDefaultSettings: String,
    },
    components: {
        'lease-main-form-component': LeaseMainForm,
        'lease-diary-component': LeaseDiary,
        'lease-notes-component': LeaseNotes,
        'lease-guarantee-component': LeaseGuarantee,
        'lease-insurance-component': LeaseInsurance,
        'lease-inspection-component': LeaseInspection,
        'lease-rent-review-component': LeaseRentReview,
        'lease-unit-details-component': LeaseUnitDetails,
        'lease-man-fees-component': LeaseManagementFees,
        'lease-bonds-component': LeaseBonds,
        'lease-outstanding-amount-component': LeaseOutstandingAmounts,
        'lease-contacts-component': LeaseContacts,
        'lease-documents-component': LeaseDocuments,
        'lease-email-component': LeaseEmail,
        'lease-sms-component': LeaseSMS,
        'lease-profile-component': LeaseProfile,
    },
    data() {
        return {
            suburb_label: 'Suburb',
            property_code: '',
            lease_code: '',
            version_id: '',
            property_list: [],
            lease_list: [],
            lease_main_list: [],
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            read_only: this.initialReadOnly,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            template_tab: null,
            loading_setting: false,
            loading_page_setting: false,
            loading_company_details_modal: false,
            tab: null,
            shortcuts: [
                { icon: 'business', title: 'Go to property', shortcut_code: 'property' },
                { icon: 'table_chart', title: 'Lease Abstract', shortcut_code: 'lease_abstract' },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (current month)',
                    shortcut_code: 'tenant_activity_current_month',
                },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (all dates)',
                    shortcut_code: 'tenant_activity_all_date',
                },
                { icon: 'receipt', title: 'View Tax Invoices', shortcut_code: 'view_tax_invoice' },
                { icon: 'history', title: 'Lease Activity Logs', shortcut_code: 'lease_logs' },
                { icon: 'print', title: 'Print', shortcut_code: 'print' },
            ],
            force_load: false,
            lease_page_title: 'Lease Summary',
            error_server_msg: {},
            error_server_msg2: [],
            error_server_msg_modify_lease_modal: [],
            error_server_msg_assign_company_modal: [],
            lease_existed: true,
            force_load_new_lease: 0,
            lease_commencement_date: '31/12/2999',
            tour_steps_new_lease: [],
            suburb_list_filtered: [],
            postcode_list_filtered: [],
            searchLeaseTenantSuburb: '',
            searchLeaseTenantPostcode: '',
            new_lease_code: '',
            created_by_name: '',
            created_by_email: '',
            created_date: '',
            lease_form_type: '',
            process_state: '',
            form_description: '',
            comments: 'no comments',
            feedback_comment: '',
            tour_steps: [
                { target: '.v-step-property-code', content: 'Property code.' },
                { target: '.v-step-created-by', content: 'User name and email address submitted this form.' },
                { target: '.v-step-created-date', content: 'Date this form submitted.' },
                { target: '.v-step-comment', content: 'User comment when he/she submitted this form.' },
                {
                    target: '.v-step-feedback',
                    content: 'Feedback that sent to the client when either approving or rejecting a form.',
                },
                {
                    target: '.v-step-modify-lease-code-btn',
                    content: 'This is where you can modify the lease code of submitted form.',
                },
                {
                    target: '.v-step-assign-company-btn',
                    content: 'This is where you can re-assign either debtor or supplier of the lease.',
                },
                { target: '.v-step-form-yes-btn', content: 'This is the button for approving the lease form.' },
                { target: '.v-step-form-no-btn', content: 'This is the button for rejecting the lease form.' },
            ],
            modify_lease_code_modal: false,
            assign_company_modal: false,
            reassign_company_code: '',
            reassign_company_name: '',
            reassign_company_address: '',
            reassign_company_city: '',
            reassign_company_postcode: '',
            reassign_company_email: '',
            reassign_company_debtor: false,
            reassign_company_supplier: false,
            reassign_company_existing: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            reassign_company_state: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            reassign_company_country: { field_key: '' },
            modify_lease_code_btn: false,
            assign_company_btn: false,
            form_yes_btn: false,
            form_no_btn: false,
            errorVacant: '',
            vacateProperty: '',
            vacateLease: '',
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
            },
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'http_host',
            'dd_company_list',
            'dd_country_list',
            'doc_active_version',
            'sys_ver_control_list',
        ]),
        ...mapGetters(['getDDCountryStates', 'getLeaseDetails']),
    },
    mounted() {
        // console.log(localStorage.getItem('cirrus8_api_url'));
        this.fetchFormVersionControl();
        this.loadPropertyList();
        this.fetchCountryList();
        this.fetchAccountIncomeGroupedList();
        this.fetchAccountExpenseGroupedList();
        this.fetchBondPropertyList();
        this.fetchParamLeaseTypeList();
        this.fetchParamDivisionList();
        this.fetchParamTenantTypeList();
        this.fetchRetailCategoryList();
        this.fetchRetailSubCategoryList();
        this.fetchRetailFineCategoryList();
        this.fetchTaxRateList();
        this.fetchCompanyList();
        // this.loadCountryDefaults();
        let step_array = [
            {
                target: '.v-step-new-lease-button',
                content: `Create <strong>New</strong> lease <br> Shows the form for new lease`,
            },
            { target: '.v-step-search-type', content: 'Select the form search type' },
            { target: '.v-step-property-select', content: 'Select the lease property you want to view or edit' },
            { target: '.v-step-final-message', content: 'You have finish the page tour.' },
        ];
        this.SET_TOUR_STEPS(step_array);
        let lease_form_read_only = [
            { form_type: 'LEASE', form_section: 'LEASE_MAIN_DETAIL' },
            { form_type: 'LEASE', form_section: 'LEASE_CONTACT' },
            { form_type: 'LEASE', form_section: 'LEASE_DIARY' },
            { form_type: 'LEASE', form_section: 'LEASE_UNIT_DETAIL' },
            { form_type: 'LEASE', form_section: 'LEASE_INSURANCE' },
            { form_type: 'LEASE', form_section: 'LEASE_INSPECTION' },
            { form_type: 'LEASE', form_section: 'LEASE_GUARANTEE' },
            { form_type: 'LEASE', form_section: 'LEASE_NOTE' },
            { form_type: 'LEASE', form_section: 'LEASE_DOCUMENT' },
            { form_type: 'LEASE', form_section: 'LEASE_CHARGE' },
            { form_type: 'LEASE', form_section: 'LEASE_MANAGEMENT' },
            { form_type: 'LEASE', form_section: 'LEASE_RENT_REVIEW' },
        ];
        this.SET_PM_LEASE_FORM_READ_ONLY(lease_form_read_only);
        this.tour_steps_new_lease = [
            {
                target: '.v-step-cancel-lease-button',
                content: `Cancel <strong>New</strong> lease <br> Shows the form for new lease`,
            },
            { target: '.v-step-property-select', content: 'Select the lease property you want to view or edit' },
            { target: '.v-step-new-lease-code', content: 'Enter new lease code' },
            { target: '.v-step-final-message', content: 'You have finish the page tour.' },
        ];
        this.property_code = this.initialPropertyCode;
        this.lease_code = this.initialLeaseCode;
        this.new_lease_code = this.initialLeaseCode;
        this.version_id = this.initialVersionId;
        this.getLeaseClientSubmittedForm();
        // document.onreadystatechange = () => {
        //     if (document.readyState == "complete") {
        //         bus.$emit("loadLeaseMainFormSection","");
        //     }
        // }
    },
    methods: {
        ...mapActions([
            'fetchCountryList',
            'fetchAccountIncomeGroupedList',
            'fetchAccountExpenseGroupedList',
            'fetchBondPropertyList',
            'fetchParamDivisionList',
            'fetchParamLeaseTypeList',
            'fetchParamTenantTypeList',
            'fetchRetailCategoryList',
            'fetchRetailSubCategoryList',
            'fetchRetailFineCategoryList',
            'fetchTaxRateList',
            'fetchCompanyList',
            'fetchFormVersionControl',
        ]),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_LEASE_CODE', 'SET_TOUR_STEPS', 'SET_PM_LEASE_FORM_READ_ONLY']),
        loadPropertyList: function () {
            this.$api.post('load-property-dropdown-list').then((response) => {
                this.property_list = response.data.data;
            });
        },
        loadLeaseList: function () {
            this.loading_setting = true;
            let form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('page_source', 'leaseFormTemplate');
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-lease-list').then((response) => {
                this.lease_list = response.data.data;
                this.loading_setting = false;
            });
        },
        selectLease: function (fieldKey, fieldValue, leaseAddress, fieldGroup) {
            this.lease_code = {
                fieldKey: fieldKey,
                fieldValue: fieldValue,
                leaseAddress: leaseAddress,
                fieldGroup: fieldGroup,
                field_key: fieldKey,
                field_value: fieldValue,
                lease_address: leaseAddress,
                field_group: fieldGroup,
            };
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        goToHref: function (param1, param2) {
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        goToShortcut: function (parameter) {
            switch (parameter) {
                case 'property':
                    window.open(
                        '?module=properties&command=v2_manage_property_page&property_code=' + this.property_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'company':
                    //module=companies&command=company&companyID=7ELEVEN
                    window.open(
                        '?module=companies&command=company&companyID=' + this.$company_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'lease_abstract':
                    break;
                case 'tenant_activity_current_month':
                    break;
                case 'tenant_activity_all_date':
                    break;
                case 'view_tax_invoice':
                    break;
                case 'print':
                    break;
            }
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        showNewLease: function () {
            this.form_mode = 1;
            this.lease_page_title = 'New Lease';
            this.new_lease_code = '';
        },
        cancelNewLease: function () {
            this.lease_page_title = 'Lease Summary';
            this.form_mode = 0;

            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_lease_code = '';
            this.lease_existed = true;
        },
        showLeaseListTable: function () {
            if (this.form_mode === 0) {
                this.form_mode = 0;
                this.lease_code = { fieldKey: '' };
            } else {
                this.error_msg = [];
                this.error_server_msg2 = [];
                this.new_lease_code = '';
                this.lease_existed = true;
            }
        },
        checkLeaseCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            let new_lease_code = this.new_lease_code.toUpperCase().trim();

            this.new_lease_code = new_lease_code;
            if (new_lease_code.length > 10) {
                // this.error_msg.push({id:'new_lease_code', message: 'Lease code allows 10 characters only'});
                this.error_server_msg2.push(['Lease code allows 10 characters only']);
            } else if (new_lease_code === '') {
                // this.error_msg.push({id:'new_lease_code', message: 'Please input a valid lease code'});
                this.error_server_msg2.push(['Please input a valid lease code']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('new_lease_code', new_lease_code);
                form_data.append('no_load', true);

                this.$api
                    .post(this.cirrus8_api_url + 'api/lease/fetch/check-code-if-exist', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        if (this.error_server_msg2.length === 0) {
                            this.lease_existed = response.data.lease_existed;
                            if (!this.lease_existed) {
                                this.force_load_new_lease++;
                                // var child = this.$refs.main_form_new_lease;
                                // child.loadForm();
                            }
                        }
                        this.loading_page_setting = false;
                    });
            }
        },
        getLeaseCode: function (value) {
            let lease_code = value.lease_code;
            let lease_name = value.lease_name;
            this.lease_commencement_date = value.lease_commencement_date;
            let label = lease_code + ' - ' + lease_name;
            this.cancelNewLease();
            // this.loadLeaseList();
            this.lease_code = {
                fieldKey: lease_code,
                field_key: lease_code,
                fieldValue: label,
                field_value: label,
                value: lease_code,
                label: label,
            };
        },
        startTour: function () {
            this.$tours['leaseTour'].start();
        },
        startNewLeaseTour: function () {
            this.$tours['newLeaseTour'].start();
        },
        getLeaseClientSubmittedForm: function () {
            if (this.property_code !== '' && this.lease_code !== '' && this.version_id !== '') {
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/fetch/submitted-form-detail', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        this.loading_page_setting = false;
                        this.created_by_name = response.data.created_by_name;
                        this.created_by_email = response.data.created_by_email;
                        this.created_date = response.data.created_date;
                        this.comments = response.data.comments;
                        this.feedback_comment = response.data.feedback_comment;
                        this.lease_form_type = response.data.lease_form_type;
                        this.process_state = response.data.process_state;
                        this.form_description = response.data.form_description;
                        if (this.comments === '') {
                            this.comments = 'no comments';
                        }
                    });
            }
        },
        processApproveForm: function () {
            this.errorVacant = false;
            this.error_msg = [];

            if (this.property_code !== '' && this.lease_code !== '' && this.version_id !== '') {
                this.form_yes_btn = true;
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('feedback_comment', this.feedback_comment);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/lease/process/approve-for-approval-lease', form_data)
                    .then((response) => {
                        if (response.data.validation_errors) {
                            if (response.data.validation_errors[0] == 'checkVacant') {
                                this.vacateProperty = response.data.vacantPropertyID;
                                this.vacateLease = response.data.vacantLeaseID;
                                this.errorVacant = true;
                            } else {
                                this.error_server_msg2 = response.data.validation_errors;
                            }
                        }
                        this.loading_page_setting = false;
                        this.form_yes_btn = false;
                        if (response.data.status === 'success') {
                            setTimeout(
                                function () {
                                    this.getLeaseClientSubmittedForm();
                                }.bind(this),
                                5000,
                            );
                            this.$noty.success('Successfully Approved.');
                        }
                    });
            }
        },
        processRejectForm: function () {
            this.errorVacant = false;
            this.error_msg = [];
            if (this.feedback_comment === '') {
                this.error_msg.push({ id: 'feedback_comment', message: 'You need to enter your feedback.' });
                this.$noty.error('You need to enter your feedback.');
            }
            if (
                this.property_code !== '' &&
                this.lease_code !== '' &&
                this.version_id !== '' &&
                this.feedback_comment !== ''
            ) {
                this.form_no_btn = true;
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('feedback_comment', this.feedback_comment);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/lease/process/reject-for-approval-lease', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        this.loading_page_setting = false;
                        this.form_no_btn = false;
                        if (!this.error_server_msg2) {
                            setTimeout(this.getLeaseClientSubmittedForm(), 5000);
                            this.$noty.success('Successfully Rejected.');
                        }
                    });
            }
        },
        getValueInList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.field_key === param1);
                if (filtered.length > 0) {
                    return filtered[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        fieldKey: '',
                        fieldValue: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        modifyLeaseCode: function () {
            this.modify_lease_code_btn = true;
            this.error_server_msg_modify_lease_modal = [];
            let new_lease_code = this.new_lease_code.toUpperCase().trim();
            this.new_lease_code = new_lease_code;
            if (new_lease_code.length > 10) {
                // this.error_msg.push({id:'new_lease_code', message: 'Lease code allows 10 characters only'});
                this.error_server_msg_modify_lease_modal.push(['Lease code allows 10 characters only']);
            } else if (new_lease_code === '') {
                // this.error_msg.push({id:'new_lease_code', message: 'Please input a valid lease code'});
                this.error_server_msg_modify_lease_modal.push(['Please input a valid lease code']);
            }
            if (this.error_server_msg_modify_lease_modal.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('new_lease_code', this.new_lease_code);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/lease/update/modify-lease-code', form_data)
                    .then((response) => {
                        this.error_server_msg_modify_lease_modal = response.data.validation_errors;
                        this.loading_page_setting = false;
                        if (this.error_server_msg_modify_lease_modal.length <= 0) {
                            this.lease_code = this.new_lease_code;
                            this.modify_lease_code_modal = false;
                            this.modify_lease_code_btn = false;
                            this.$noty.success('Successfully Modified.');
                            // location.href = this.http_host+'?module=leases&command=lease_approval_page&property_code='+this.property_code+'&lease_code='+new_lease_code+'&version_id'+this.version_id;
                        }
                    });
            } else {
                this.modify_lease_code_btn = false;
            }
        },
        showReassignCompanyModal: function () {
            this.loading_company_details_modal = true;
            this.assign_company_modal = true;
            this.error_server_msg_assign_company_modal = [];
            let form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('new_lease_code', this.new_lease_code);
            form_data.append('no_load', true);
            this.$api.post(this.cirrus8_api_url + 'api/lease/fetch/company-details', form_data).then((response) => {
                this.loading_company_details_modal = false;
                this.reassign_company_code = response.data.company_code;
                this.reassign_company_name = response.data.company_name;
                this.reassign_company_address = response.data.company_address;
                this.reassign_company_city = response.data.company_city;
                this.reassign_company_postcode = response.data.company_postcode;
                this.reassign_company_email = response.data.company_email;
                let reassign_company_state = response.data.company_state;
                let company_country = response.data.company_country;
                this.reassign_company_state = this.getValueInList(
                    reassign_company_state,
                    this.getDDCountryStates(company_country),
                );
                this.reassign_company_existing = this.getValueInList(this.reassign_company_code, this.dd_company_list);
                this.reassign_company_country = this.getValueInList(company_country, this.dd_country_list);
                this.loadCountryDefaults(1, company_country);
            });
        },
        assignOrUpdateCompany: function () {
            this.assign_company_btn = true;
            let reassign_company_code = this.reassign_company_code;
            let reassign_company_name = this.reassign_company_name;
            let reassign_company_address = this.reassign_company_address;
            let reassign_company_city = this.reassign_company_city;
            let reassign_company_postcode = this.reassign_company_postcode;
            let reassign_company_email = this.reassign_company_email;
            let reassign_company_country = this.reassign_company_country.field_key;

            if (reassign_company_code.length > 10) {
                this.error_server_msg_assign_company_modal.push(['Company code allows 10 characters only']);
            } else if (reassign_company_code === '') {
                this.error_server_msg_assign_company_modal.push(['Please input a valid company code']);
            }
            if (reassign_company_name === '') {
                this.error_server_msg_assign_company_modal.push(['Please input a valid company name']);
            }
            if (reassign_company_address === '') {
                this.error_server_msg_assign_company_modal.push(['Please input a valid company address']);
            }
            if (reassign_company_city === '') {
                this.error_server_msg_assign_company_modal.push(['Please input a valid company city']);
            }
            if (reassign_company_postcode === '') {
                this.error_server_msg_assign_company_modal.push(['Please input a valid company postcode']);
            }
            let reassign_company_state = '';
            if (this.country_defaults.display_state === true) {
                reassign_company_state = this.reassign_company_state.field_key;
                if (reassign_company_state === '')
                    this.error_server_msg_assign_company_modal.push(['Please input a valid company state']);
            }

            if (this.error_server_msg_assign_company_modal.length <= 0) {
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('reassign_company_code', reassign_company_code);
                form_data.append('reassign_company_name', reassign_company_name);
                form_data.append('reassign_company_address', reassign_company_address);
                form_data.append('reassign_company_city', reassign_company_city);
                form_data.append('reassign_company_postcode', reassign_company_postcode);
                if (this.country_defaults.display_state === true)
                    form_data.append('reassign_company_state', reassign_company_state);
                form_data.append('reassign_company_country', reassign_company_country);
                form_data.append('reassign_company_email', reassign_company_email);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/update-or-create/temp-company-details', form_data)
                    .then((response) => {
                        this.assign_company_btn = false;
                        this.assign_company_modal = false;
                        bus.$emit('loadLeaseMainFormSection', '');
                        this.$noty.success('Successfully Saved.');
                    });
            } else {
                this.assign_company_btn = false;
            }
        },
        printLease: function () {
            var printWindow = window.open(
                '?module=leases&command=lease_print_page&property_code=' +
                    this.property_code +
                    '&lease_code=' +
                    this.lease_code +
                    '&version_id=' +
                    this.version_id,
                '_blank', // <- This is what makes it open in a new window.
                'toolbar=yes,scrollbars=yes,resizable=no,top=20,left=30,width=900,height=600',
            );
            printWindow.focus();
        },
        loadCountryDefaults: function (type, country) {
            this.loading_page_setting = true;
            if (country === undefined) return;
            var form_data = new FormData();
            form_data.append('country', country);
            form_data.append('no_load', true);

            let api_url = this.cirrus8_api_url + 'admin/country_defaults/load';
            this.$api.post(api_url, form_data).then((response) => {
                this.loading_page_setting = false;
                this.country_defaults = response.data.default;
                this.suburb_label = this.ucwords(this.country_defaults.suburb);
                if (this.reassign_company_country.field_key === '')
                    this.reassign_company_country = this.country_defaults.country_code;
            });
        },
        triggerCountryChange: function () {
            this.loadCountryDefaults(1, this.reassign_company_country.field_key);
            this.resetFormDetails();
        },
        resetFormDetails: function () {
            this.reassign_company_state = null;
            this.reassign_company_city = null;
            this.reassign_company_postcode = null;
            this.searchLeaseTenantSuburb = null;
            this.searchLeaseTenantPostcode = null;
        },
        suburbFilteredList(stateVal) {
            this.suburb_list_filtered = [];
            let filteredItem = [];

            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.suburb_list_filtered = filteredItem;
        },
        postcodeFilteredList(stateVal) {
            this.postcode_list_filtered = [];
            let filteredItem = [];
            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.postcode_list_filtered = filteredItem;
        },
        suburbSelected(data) {
            // lease_tenant_state
            if (this.$refs.refLeaseTenantSuburb.selectedValues.length === 0) {
                if (data) {
                    this.reassign_company_city = data.suburb;
                    this.reassign_company_postcode = data.pcode;
                }
            } else {
                if (data) {
                    this.reassign_company_city = data.suburb;
                    this.reassign_company_postcode = data.pcode;
                }
            }
        },
        postcodeSelected(data) {
            // lease_tenant_state
            if (this.$refs.refLeaseTenantSuburb.selectedValues.length === 0) {
                if (data) {
                    this.reassign_company_city = data.suburb;
                    this.reassign_company_postcode = data.pcode;
                    // let state = data.State;
                    // this.lease_tenant_state = this.getValueInList(state, this.getDDCountryStates(this.lease_tenant_country.field_key));
                }
            } else {
                if (data) {
                    this.reassign_company_city = data.suburb;
                    this.reassign_company_postcode = data.pcode;
                    // let state = data.State;
                    // this.lease_tenant_state = this.getValueInList(state, this.getDDCountryStates(this.lease_tenant_country.field_key));
                }
            }
        },
        tenantStateChanged: function () {
            if (this.reassign_company_state) {
                this.suburbFilteredList(this.reassign_company_state.field_key);
                this.postcodeFilteredList(this.reassign_company_state.field_key);
            } else {
                this.suburb_list_filtered = [];
                this.postcode_list_filtered = [];
            }
        },
    },
    watch: {
        property_code: function () {
            // this.lease_code = {fieldKey:''};
            this.lease_list = [];
            this.SET_PROPERTY_CODE(this.property_code);
            this.loadLeaseList();
        },
        lease_code: function () {
            this.SET_LEASE_CODE(this.lease_code);
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
        reassign_company_existing: function () {
            if (this.reassign_company_existing.field_key !== '') {
                this.error_server_msg_assign_company_modal = [];
                this.loading_company_details_modal = true;
                let form_data = new FormData();
                form_data.append('company_code', this.reassign_company_existing.field_key);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/company/load-company-details', form_data)
                    .then((response) => {
                        this.reassign_company_code = this.reassign_company_existing.field_key;
                        this.reassign_company_name = response.data.tenant_name;
                        this.reassign_company_address = response.data.tenant_address_1 + response.data.tenant_address_2;
                        this.reassign_company_city = response.data.tenant_suburb;
                        this.reassign_company_postcode = response.data.tenant_post_code;
                        this.reassign_company_email = response.data.tenant_email;
                        let reassign_company_state = response.data.tenant_state.field_key;
                        this.reassign_company_existing = this.getValueInList(
                            this.reassign_company_code,
                            this.dd_company_list,
                        );
                        let company_country = response.data.tenant_country_code;
                        this.reassign_company_state = this.getValueInList(
                            reassign_company_state,
                            this.getDDCountryStates(company_country),
                        );
                        this.reassign_company_country = this.getValueInList(company_country, this.dd_country_list);
                        this.loadCountryDefaults(1, company_country);
                        this.loading_company_details_modal = false;
                    });
            }
        },
        reassign_company_city: function (newVal, oldVal) {
            if (newVal === undefined) {
                newVal = this.searchLeaseTenantSuburb;
                this.reassign_company_city = newVal;
            }
        },
        reassign_company_postcode: function (newVal, oldVal) {
            if (newVal === undefined) {
                newVal = this.searchLeaseTenantPostcode;
                this.reassign_company_postcode = newVal;
            }
        },
    },
    created() {},
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
