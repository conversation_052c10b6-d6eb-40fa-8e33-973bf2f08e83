<template>
    <div
        v-resize="onResize"
        class="c8-page lease-forms"
    >
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <v-row class="ma-0">
            <v-col
                xs="12"
                sm="12"
                md="9"
                lg="9"
            >
                <v-toolbar flat>
                    <v-toolbar-title>
                        <cirrus-page-header :title="lease_page_title" />
                    </v-toolbar-title>
                </v-toolbar>

                <div class="page-form">
                    <v-row
                        class="form-row"
                        v-if="form_mode === 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Search Type:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <v-btn-toggle
                                class="form-toggle"
                                v-model="search_type"
                                mandatory
                            >
                                <v-btn
                                    small
                                    tile
                                    text
                                >
                                    Property
                                </v-btn>
                                <v-btn
                                    small
                                    tile
                                    text
                                >
                                    Lease
                                </v-btn>
                                <v-btn
                                    small
                                    tile
                                    text
                                >
                                    {{ property_manager_label }}
                                </v-btn>
                            </v-btn-toggle>
                        </v-col>
                    </v-row>
                    <v-row
                        class="form-row"
                        v-if="search_type === 2 && form_mode === 0"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >{{ property_manager_label }}:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <multiselect
                                v-model="manager_code"
                                :options="manager_list"
                                :allowEmpty="false"
                                :options-limit="10000"
                                class="vue-select2 dropdown-left dropdown-400"
                                :custom-label="nameWithDash"
                                group-label="language"
                                :placeholder="'Select a ' + property_manager_label"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                            <v-btn
                                :loading="manager_list.length <= 0"
                                depressed
                                elevation="0"
                                small
                                color="normal"
                                height="30"
                                class="rounded-l-0"
                                v-on:click="changeSearchType('manager_code')"
                            >
                                <v-icon>arrow_right</v-icon>
                            </v-btn>
                        </v-col>
                    </v-row>
                    <v-row
                        class="form-row"
                        v-if="search_type !== 1 || (search_type === 1 && lease_code.field_key !== '')"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Property:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <multiselect
                                v-model="property_code"
                                :options="property_list"
                                group-values="field_group_values"
                                :groupSelect="false"
                                group-label="field_group_names"
                                :options-limit="10000"
                                :group-select="true"
                                class="vue-select2 dropdown-left dropdown-400"
                                :custom-label="nameWithDash"
                                placeholder="Select a property"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                            <v-btn
                                :loading="property_list.length <= 0"
                                depressed
                                elevation="0"
                                small
                                color="normal"
                                height="30"
                                class="rounded-l-0"
                                v-on:click="changeSearchType('property_code')"
                            >
                                <v-icon>arrow_right</v-icon>
                            </v-btn>
                            <a
                                v-if="property_code.field_key !== ''"
                                v-on:click="goToShortcut('property')"
                                data-tooltip="Go to property"
                            >
                                <v-icon
                                    elevation="0"
                                    icon
                                    small
                                    height="30"
                                    >business
                                </v-icon>
                            </a>
                        </v-col>
                    </v-row>
                    <v-row
                        class="form-row"
                        v-if="form_mode === 0 && (property_code.field_key !== '' || search_type === 1)"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Lease:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <multiselect
                                v-model="lease_code"
                                :options="lease_list"
                                :options-limit="10000"
                                group-values="field_group_values"
                                :groupSelect="false"
                                group-label="field_group_names"
                                :group-select="true"
                                class="vue-select2 dropdown-left dropdown-400"
                                :custom-label="nameWithDash"
                                placeholder="Select a lease"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                            <v-btn
                                :loading="lease_list.length <= 0"
                                depressed
                                elevation="0"
                                small
                                color="normal"
                                height="30"
                                class="rounded-l-0"
                                v-on:click="reloadLeasePage()"
                            >
                                <v-icon>arrow_right</v-icon>
                            </v-btn>
                            <v-tooltip
                                top
                                v-if="
                                    client_new_lease_access &&
                                    form_mode === 0 &&
                                    property_code.field_key !== '' &&
                                    property_code.field_key !== undefined
                                "
                            >
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        depressed
                                        elevation="0"
                                        small
                                        color="primary"
                                        height="30"
                                        class="rounded-a-0"
                                        @click="showNewLease()"
                                        v-bind="attrs"
                                        v-on="on"
                                    >
                                        <v-icon>add</v-icon>
                                    </v-btn>
                                </template>
                                <span>New Lease</span>
                            </v-tooltip>
                        </v-col>
                    </v-row>
                    <v-row
                        class="form-row"
                        v-if="property_code.field_key !== '' && form_mode === 1 && lease_existed === true"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Enter A Lease Code:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <cirrus-input
                                class="v-step-new-lease-code"
                                v-model="new_lease_code"
                                size="10"
                                :id="'new_lease_code'"
                                :edit_form="true"
                                :error_msg="error_msg"
                            ></cirrus-input>
                            <v-btn
                                class="form-text-button mb-0 rounded-l-0"
                                color="primary"
                                depressed
                                elevation="0"
                                small
                                @click="checkLeaseCode()"
                                >Create New
                            </v-btn>
                            <v-tooltip top>
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        depressed
                                        elevation="0"
                                        small
                                        color="black"
                                        height="30"
                                        class="rounded-a-0"
                                        dark
                                        v-if="form_mode === 1"
                                        @click="cancelNewLease()"
                                        v-bind="attrs"
                                        v-on="on"
                                    >
                                        <v-icon color="red">close</v-icon>
                                    </v-btn>
                                </template>
                                <span>Cancel</span>
                            </v-tooltip>
                        </v-col>
                    </v-row>
                    <v-row
                        class="form-row"
                        v-if="property_code.field_key !== '' && form_mode === 1 && lease_existed === false"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Lease Code:
                        </v-col>
                        <input
                            type="hidden"
                            id="lease_code"
                            :value="new_lease_code"
                        />
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <span class="form-input-text v-step-new-lease-code-label">
                                {{ new_lease_code }}
                            </span>
                        </v-col>
                    </v-row>
                </div>
            </v-col>
            <v-col
                class="pt-7"
                xs="12"
                sm="12"
                md="3"
                lg="3"
                v-if="property_code.field_key !== '' && lease_code.field_key !== '' && form_mode === 0"
                v-on:dblclick="doubleClickForm()"
            >
                <label
                    v-if="
                        (!formSectionReadOnly(pm_lease_form_read_only, 'LEASE', 'LEASE_MAIN_DETAIL') &&
                            form_notes.length > 0) ||
                        edit_form
                    "
                    class="font-weight-black"
                    for="main-notes"
                    >Notes:</label
                >
                <textarea
                    id="main-notes"
                    class="noteTextArea font-weight-bold"
                    style="width: 100%"
                    rows="5"
                    v-model="form_notes"
                    v-if="edit_form"
                ></textarea>
                <div
                    class="noteTextArea"
                    v-if="!edit_form"
                    style="max-height: 145px; overflow: auto"
                >
                    <pre
                        class="pre-content font-weight-bold"
                        v-if="form_notes.length > 0"
                        >{{ form_notes }}</pre
                    >
                    <pre
                        class="pre-content font-weight-bold"
                        style="color: grey"
                        v-if="form_notes.length === 0 && is_inactive == 0"
                    >
(double click to add note)</pre
                    >
                </div>
                <v-btn
                    v-if="edit_form"
                    class="float-end mt-1"
                    color="success"
                    dense
                    x-small
                    @click="processSaveFormNotes()"
                >
                    Save
                </v-btn>
            </v-col>
        </v-row>
        <br />
        <v-alert
            dense
            text
            type="error"
            v-if="property_code.is_inactive == 1"
        >
            This property is currently classed as inactive, to make changes to the lease, first make the property
            active.
        </v-alert>
        <sui-table
            v-if="search_type != 1 && property_code.field_key !== '' && lease_code.field_key === '' && form_mode === 0"
            stackable
            selectable
            class="fixed_headers"
        >
            <sui-table-header class="fixed_headers_thead">
                <sui-table-row>
                    <sui-table-header-cell
                        colspan="4"
                        class="fieldDescription headerTr"
                        >Matching Records - click to refine your search
                    </sui-table-header-cell>
                    <sui-table-header-cell
                        textAlign="right"
                        style="color: white"
                    >
                        <sui-checkbox
                            class="inactive-checkbox"
                            v-model="show_inactive_lease"
                            label="Include Inactive Leases"
                        />
                    </sui-table-header-cell>
                </sui-table-row>
                <sui-table-row class="fieldDescription">
                    <sui-table-header-cell class="center fieldDescription headerTr">Property</sui-table-header-cell>
                    <sui-table-header-cell class="fieldDescription headerTr">Lease</sui-table-header-cell>
                    <sui-table-header-cell class="fieldDescription headerTr">Description</sui-table-header-cell>
                    <sui-table-header-cell class="fieldDescription headerTr">Lease Type</sui-table-header-cell>
                    <sui-table-header-cell class="center fieldDescription headerTr">Status</sui-table-header-cell>
                </sui-table-row>
            </sui-table-header>
            <sui-table-body v-if="loading_content_setting">
                <sui-table-row>
                    <sui-table-cell
                        colspan="5"
                        class="center"
                    >
                        <cirrus-content-loader v-if="loading_content_setting"></cirrus-content-loader>
                    </sui-table-cell>
                </sui-table-row>
            </sui-table-body>
            <sui-table-body
                v-for="(lease_list_data, index) in lease_list"
                :key="index"
            >
                <sui-table-row
                    v-if="(!show_inactive_lease && lease_list_data_detail.is_inactive == 0) || show_inactive_lease"
                    v-for="(lease_list_data_detail, index_detail) in lease_list_data.field_group_values"
                    :key="index_detail"
                    @click="
                        selectLease(
                            lease_list_data_detail.field_key,
                            lease_list_data_detail.field_value,
                            lease_list_data_detail.lease_address,
                            lease_list_data_detail.field_group,
                            lease_list_data_detail.is_inactive,
                        )
                    "
                    :class="returnInactiveClass(property_code.is_inactive, lease_list_data_detail.is_inactive)"
                >
                    <sui-table-cell class="center">
                        {{ property_code.field_key }}
                    </sui-table-cell>
                    <sui-table-cell class="">
                        <span v-if="lease_list_data_detail.is_inactive == 1">*</span
                        >{{ lease_list_data_detail.field_key }} -
                        {{ lease_list_data_detail.field_value }}
                    </sui-table-cell>
                    <sui-table-cell class="">
                        {{ lease_list_data_detail.tenancy_location }}
                    </sui-table-cell>
                    <sui-table-cell class="">
                        {{ lease_list_data_detail.lease_type_description }}
                    </sui-table-cell>
                    <sui-table-cell class="center">
                        <v-icon
                            v-if="lease_list_data_detail.field_group === 'C'"
                            color="blue darken-2"
                            >business
                        </v-icon>
                        <v-icon
                            v-if="lease_list_data_detail.field_group !== 'C'"
                            color="orange darken-2"
                            >warning
                        </v-icon>
                        <span v-if="lease_list_data_detail.is_inactive == 1">Inactive</span>
                        <span v-else>{{ lease_list_data_detail.field_group === 'C' ? 'Current' : 'Vacant' }}</span>
                    </sui-table-cell>
                </sui-table-row>
            </sui-table-body>
        </sui-table>
        <v-alert
            dense
            type="error"
            class="mb-0 rounded-b-0"
            v-if="
                lease_details.main_form.lease_tenant_status === 'L' &&
                property_code.field_key !== '' &&
                lease_code.field_key !== '' &&
                form_mode === 0
            "
        >
            <v-row align="center">
                <v-col class="grow">
                    This lease is currently classed as Vacated
                    <span v-if="lease_code.is_inactive == 1">and Inactive</span>
                </v-col>
                <v-col class="shrink">
                    <v-btn
                        x-small
                        color="subPrimary"
                        v-if="property_code.is_inactive != 1 && lease_code.is_inactive == 1"
                        @click="processLeaseInactiveStatus(0)"
                        :loading="loading_inactive_btn"
                        >Set as Active
                    </v-btn>
                    <v-btn
                        x-small
                        color="subPrimary"
                        v-if="property_code.is_inactive != 1 && lease_code.is_inactive != 1"
                        @click="processLeaseInactiveStatus(1)"
                        :loading="loading_inactive_btn"
                        >Set as Inactive
                    </v-btn>
                </v-col>
            </v-row>
        </v-alert>
        <v-tabs
            class="cirrus-tab-theme"
            icons-and-text
            v-show="property_code.field_key !== '' && lease_code.field_key !== '' && form_mode === 0"
            v-model="template_tab"
            show-arrows
        >
            <v-tabs-slider color="white"></v-tabs-slider>

            <v-tab
                href="#tab-1"
                v-if="responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
                class="v-step-form-tab"
            >
                Overview
                <v-icon
                    small
                    dense
                    >business
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-2"
                v-if="responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                Charges and Rent Reviews
                <v-icon
                    small
                    dense
                    >monetization_on
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-3"
                v-if="responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                Diary
                <v-icon
                    small
                    dense
                    >date_range
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-4"
                v-if="responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                Notes and Additional Details
                <v-icon
                    small
                    dense
                    >notes
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-5"
                v-if="responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                Communication and Documents
                <v-icon
                    small
                    dense
                    >attachment
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-6"
                v-if="responsive_show && lease_profile && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                Lease Profile
                <v-icon
                    small
                    dense
                    >dvr
                </v-icon>
            </v-tab>

            <v-tab
                href="#tab-1"
                class="primary--text v-step-form-tab"
                v-if="!responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                <v-icon
                    small
                    dense
                    >business
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-2"
                class="primary--text"
                v-if="!responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                <v-icon
                    small
                    dense
                    >monetization_on
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-3"
                class="primary--text"
                v-if="!responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                <v-icon
                    small
                    dense
                    >date_range
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-4"
                class="primary--text"
                v-if="!responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                <v-icon
                    small
                    dense
                    >notes
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-5"
                class="primary--text"
                v-if="!responsive_show && property_code.field_key !== '' && lease_code.field_key !== ''"
            >
                <v-icon
                    small
                    dense
                    >attachment
                </v-icon>
            </v-tab>
            <v-tab
                href="#tab-6"
                class="primary--text"
                v-if="
                    !responsive_show && lease_profile && property_code.field_key !== '' && lease_code.field_key !== ''
                "
            >
                <v-icon
                    small
                    dense
                    >dvr
                </v-icon>
            </v-tab>
            <v-col
                class="text-right pt-2"
                v-show="property_code.field_key !== '' && lease_code.field_key !== '' && form_mode === 0"
            >
                <v-menu
                    offset-y
                    offset-x
                >
                    <template v-slot:activator="{ on }">
                        <v-btn
                            depressed
                            small
                            color="grey darken-1"
                            dark
                            v-on="on"
                            id="option-button"
                            v-show="property_code.field_key !== '' && lease_code.field_key !== '' && form_mode === 0"
                        >
                            <v-icon
                                small
                                left
                                >settings
                            </v-icon>
                            Options
                        </v-btn>
                    </template>
                    <v-list
                        dense
                        subheader
                        class="menu-option"
                        style="left: -120px"
                    >
                        <v-list-item
                            v-for="(shortcuts_data, shortcuts_index) in shortcuts"
                            :key="shortcuts_index"
                            v-if="
                                shortcuts_data.shortcut_code !== 'sms_send' || (sms_sending_setup && is_inactive != 1)
                            "
                            @click="goToShortcut(shortcuts_data.shortcut_code)"
                        >
                            <v-list-item-avatar>
                                <v-avatar
                                    size="32px"
                                    tile
                                >
                                    <v-icon>{{ shortcuts_data.icon }}</v-icon>
                                </v-avatar>
                            </v-list-item-avatar>
                            <v-list-item-title style="text-align: left">{{ shortcuts_data.title }}</v-list-item-title>
                        </v-list-item>
                    </v-list>
                </v-menu>
            </v-col>
            <v-tab-item :value="'tab-1'">
                <lease-outstanding-amount-component
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                />
                <lease-bonds-component
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :pmro_read_only="pmro_read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :is_inactive="is_inactive"
                />
                <lease-main-form-component
                    v-on:returnLeaseIsExisted="getLeaseCode"
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-main-form-component>
                <br />
                <lease-contacts-component
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-contacts-component>
            </v-tab-item>
            <v-tab-item :value="'tab-2'">
                <lease-unit-details-component
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-unit-details-component>
                <lease-man-fees-component
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-man-fees-component>
                <lease-rent-review-component
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-rent-review-component>

                <lease-unit-history-component
                    id="leaseUnitHistorySection"
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-unit-history-component>
            </v-tab-item>
            <v-tab-item :value="'tab-3'">
                <lease-diary-component
                    id="leaseDiarySection"
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-diary-component>
            </v-tab-item>
            <v-tab-item :value="'tab-4'">
                <lease-notes-component
                    id="leaseNotesSection"
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :current_db="current_db"
                    :user_type="user_type"
                    :username="username"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-notes-component>
                <lease-inspection-component
                    id="leaseInspectionSection"
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :current_db="current_db"
                    :user_type="user_type"
                    :username="username"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-inspection-component>
                <lease-insurance-component
                    id="leaseInsuranceSection"
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :current_db="current_db"
                    :user_type="user_type"
                    :username="username"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-insurance-component>
                <lease-guarantee-component
                    id="leaseGuaranteeSection"
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :current_db="current_db"
                    :user_type="user_type"
                    :username="username"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-guarantee-component>
            </v-tab-item>
            <v-tab-item :value="'tab-5'">
                <lease-documents-v2-component
                    v-if="doc_active_version === 1"
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :current_db="current_db"
                    :user_type="user_type"
                    :username="username"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-documents-v2-component>
                <lease-documents-component
                    v-else
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :current_db="current_db"
                    :user_type="user_type"
                    :username="username"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-documents-component>
                <lease-communication-history-component
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :pmro_read_only="pmro_read_only"
                    :is_inactive="is_inactive"
                ></lease-communication-history-component>
            </v-tab-item>
            <v-tab-item :value="'tab-6'">
                <lease-profile-component
                    :property_code="property_code.field_key"
                    :lease_code="lease_code.field_key"
                    :version_id="version_id"
                    :current_db="current_db"
                    :user_type="user_type"
                    :username="username"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                    :is_inactive="is_inactive"
                ></lease-profile-component>
            </v-tab-item>
        </v-tabs>

        <div
            v-if="property_code.field_key !== '' && new_lease_code !== '' && lease_existed === false && form_mode === 1"
        >
            <lease-main-form-component
                v-on:returnLeaseCode="getLeaseCode"
                :property_code="property_code.field_key"
                :lease_code="new_lease_code"
                :version_id="version_id"
                ref="main_form_new_lease"
                :new_lease="true"
                :page_form_type="page_form_type"
                :country_default_settings="initialCountryDefaultSettings"
            ></lease-main-form-component>
        </div>

        <v-dialog
            v-model="show_tax_invoice_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    View Tax Invoice
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_tax_invoice_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-tax-invoice-component
                        v-if="show_tax_invoice_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :is_inactive="is_inactive"
                    ></lease-tax-invoice-component>
                </v-card-text>
            </v-card>
        </v-dialog>

        <!--  FOR VIEWING THE TASK CREATED UNDER IT  -->
        <v-dialog
            v-model="show_task_management"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    View Task List
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_task_management = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <task-list-view
                        v-if="show_task_management"
                        :property_code="property_code"
                        :lease_code="lease_code"
                    ></task-list-view>
                </v-card-text>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_tenant_activity"
            max-width="1200"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Tenant Activity
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_tenant_activity = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div
                        id="show_tenant_html"
                        v-html="show_tenant_html"
                    ></div>
                </v-card-text>
            </v-card>
        </v-dialog>
        <sms-sending-modal-component
            key="shortcuts"
            v-if="isLeaseFormLive()"
        ></sms-sending-modal-component>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import { mapActions, mapMutations, mapState } from 'vuex';
import LeaseMainForm from '../forms/leaseMainForm.vue';
import LeaseDiary from '../forms/leaseDiary.vue';
import LeaseNotes from '../forms/leaseNotes.vue';
import LeaseGuarantee from '../forms/leaseGuarantee.vue';
import LeaseInsurance from '../forms/leaseInsurance.vue';
import LeaseInspection from '../forms/leaseInspection.vue';
import LeaseRentReview from '../forms/LeaseRentReview.vue';
import LeaseUnitDetails from '../forms/leaseUnitDetails.vue';
import LeaseManagementFees from '../forms/LeaseManagementFees.vue';
import LeaseUnitHistory from '../forms/leaseUnitHistory.vue';
import LeaseBonds from '../forms/leaseBonds.vue';
import LeaseOutstandingAmounts from '../forms/leaseOutstandingAmounts.vue';
import LeaseContacts from '../forms/leaseContacts.vue';
import LeaseDocuments from '../forms/leaseDocuments.vue';
import LeaseDocumentsV2 from '../../../../DocumentDirectory/sections/LeaseDocumentFormV2.vue';
import LeaseEmail from '../forms/LeaseEmail.vue';
import LeaseCommunicationHistory from '../forms/LeaseCommunicationHistory.vue';
import LeaseSMS from '../forms/LeaseSMS.vue';
import LeaseProfile from '../forms/LeaseProfile.vue';
import LeaseTaxInvoice from '../forms/LeaseTaxInvoice.vue';
import TaskListView from '../../TaskManagement/forms/TaskListView.vue';
import axios from 'axios';
import Vue from 'vue';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';

Vue.component('sms-sending-modal-component', require('../../Generic/Forms/SMSSendingModal.vue').default);
export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialLeaseCode: String,
        initialVersionId: String,
        initialCountryDefaultSettings: String,
    },
    components: {
        'lease-main-form-component': LeaseMainForm,
        'lease-diary-component': LeaseDiary,
        'lease-notes-component': LeaseNotes,
        'lease-guarantee-component': LeaseGuarantee,
        'lease-insurance-component': LeaseInsurance,
        'lease-inspection-component': LeaseInspection,
        'lease-rent-review-component': LeaseRentReview,
        'lease-unit-details-component': LeaseUnitDetails,
        'lease-unit-history-component': LeaseUnitHistory,
        'lease-man-fees-component': LeaseManagementFees,
        'lease-bonds-component': LeaseBonds,
        'lease-outstanding-amount-component': LeaseOutstandingAmounts,
        'lease-contacts-component': LeaseContacts,
        'lease-documents-component': LeaseDocuments,
        'lease-documents-v2-component': LeaseDocumentsV2,
        'lease-communication-history-component': LeaseCommunicationHistory,
        'lease-email-component': LeaseEmail,
        'lease-sms-component': LeaseSMS,
        'lease-profile-component': LeaseProfile,
        'lease-tax-invoice-component': LeaseTaxInvoice,
        'task-list-view': TaskListView,
    },
    data() {
        return {
            property_manager_label: 'Property Manager',
            manager_code: { field_key: '' },
            property_code: { field_key: '' },
            lease_code: { field_key: '' },
            new_lease_code: '',
            new_lease_cod2: '',
            version_id: null,
            property_list: [],
            lease_list: [],
            manager_list: [],
            lease_main_list: [],
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            read_only: this.initialReadOnly,
            pmro_read_only: false,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            template_tab: 'tab-1',
            loading_content_setting: false,
            loading_page_setting: false,
            tab: null,
            shortcuts: [
                {
                    icon: 'business',
                    title: 'Go to property',
                    shortcut_code: 'property',
                },
                {
                    icon: 'table_chart',
                    title: 'Lease Abstract',
                    shortcut_code: 'lease_abstract',
                },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (current month)',
                    shortcut_code: 'tenant_activity_current_month',
                },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (all dates)',
                    shortcut_code: 'tenant_activity_all_date',
                },
                {
                    icon: 'receipt',
                    title: 'View Tax Invoices',
                    shortcut_code: 'view_tax_invoice',
                },
                {
                    icon: 'receipt',
                    title: 'Raise Invoice',
                    shortcut_code: 'raise_invoice',
                },
                { icon: 'mail', title: 'Letter', shortcut_code: 'letter' },
                { icon: 'message', title: 'Send SMS', shortcut_code: 'sms_send' },
                // {icon: 'mdi-view-list', title: 'View Related Task', shortcut_code: 'view_task_management'},
                { icon: 'print', title: 'Print', shortcut_code: 'print' },
            ],
            force_load: false,
            lease_page_title: 'Lease Summary',
            error_server_msg: {},
            error_server_msg2: [],
            lease_existed: true,
            force_load_new_lease: 0,
            lease_commencement_date: '31/12/2999',
            tour_steps_new_lease: [],
            show_activity_log_modal: true,
            show_tax_invoice_modal: false,
            show_task_management: false,
            show_tenant_activity: false,
            sms_modal: false,
            show_tenant_html: '',
            show_inactive_property: true,
            show_inactive_lease: false,
            property_index: Math.random(),
            lease_index: Math.random(),
            lease_outstanding_amount_list: [],
            loading_inactive_btn: false,
            is_inactive: 0,
            last_occupied_date_range: '',
            selected_lease: {},
            form_notes: '',
            form_notes_old: '',
            edit_form: false,
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_profile',
            'auto_diarise',
            'sms_sending_setup',
            'client_new_lease_access',
            'doc_active_version',
            'sys_ver_control_list',
            'pm_lease_form_read_only',
        ]),
    },
    mounted() {
        let country_default_settings = JSON.parse(atob(this.initialCountryDefaultSettings));
        this.property_manager_label = this.ucwords(country_default_settings.property_manager);
        this.fetchFormVersionControl();
        this.checkAccess();
        this.loadManagerList();
        this.loadPropertyList();
        // this.loadLeaseList();
        this.fetchCountryList();
        this.fetchAccountIncomeGroupedList();
        this.fetchAccountExpenseGroupedList();
        this.fetchBondPropertyList();
        this.fetchParamLeaseTypeList();
        this.fetchParamDivisionList();
        this.fetchParamTenantTypeList();
        this.fetchRetailCategoryList();
        this.fetchRetailSubCategoryList();
        this.fetchRetailFineCategoryList();
        this.fetchTaxRateList();
        this.fetchParamLeaseProfile();
        this.fetchParamEmailCenSetup();
        this.fetchParamSMSSendingSetup();
        this.fetchAccountList();
        this.fetchPMLeaseFormReadOnly();
        this.fetchDateToday();
        this.fetchPMAutoDiarise();
        this.fetchCompanyList();
        let step_array = [
            {
                target: '.v-step-new-lease-button',
                content: `Create <strong>New</strong> lease <br> Shows the form for new lease`,
            },
            { target: '.v-step-search-type', content: 'Select the form search type' },
            {
                target: '.v-step-property-select',
                content: 'Select the lease property you want to view or edit',
            },
            {
                target: '.v-step-final-message',
                content: 'You have finish the page tour.',
            },
        ];
        this.SET_TOUR_STEPS(step_array);
        this.tour_steps_new_lease = [
            {
                target: '.v-step-cancel-lease-button',
                content: `Cancel <strong>New</strong> lease <br> Shows the form for new lease`,
            },
            {
                target: '.v-step-property-select',
                content: 'Select the lease property you want to view or edit',
            },
            { target: '.v-step-new-lease-code', content: 'Enter new lease code' },
            {
                target: '.v-step-final-message',
                content: 'You have finish the page tour.',
            },
        ];

        if (this.initialPropertyCode) {
            this.version_id = this.initialVersionId;
        }
    },
    methods: {
        ...mapActions([
            'fetchCountryList',
            'fetchAccountIncomeGroupedList',
            'fetchAccountExpenseGroupedList',
            'fetchBondPropertyList',
            'fetchParamDivisionList',
            'fetchParamLeaseTypeList',
            'fetchParamTenantTypeList',
            'fetchRetailCategoryList',
            'fetchRetailSubCategoryList',
            'fetchRetailFineCategoryList',
            'fetchTaxRateList',
            'fetchParamLeaseProfile',
            'fetchParamEmailCenSetup',
            'fetchParamSMSSendingSetup',
            'fetchAccountList',
            'fetchPMLeaseFormReadOnly',
            'fetchDateToday',
            'fetchPMAutoDiarise',
            'fetchCompanyList',
            'fetchFormVersionControl',
        ]),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_LEASE_CODE', 'SET_TOUR_STEPS']),
        checkAccess() {
            if (this.user_sub_type == 'PMRO') {
                this.pmro_read_only = true;

                this.shortcuts = [
                    {
                        icon: 'business',
                        title: 'Go to property',
                        shortcut_code: 'property',
                    },
                    {
                        icon: 'table_chart',
                        title: 'Lease Abstract',
                        shortcut_code: 'lease_abstract',
                    },
                    {
                        icon: 'account_balance',
                        title: 'Tenant Activity (current month)',
                        shortcut_code: 'tenant_activity_current_month',
                    },
                    {
                        icon: 'account_balance',
                        title: 'Tenant Activity (all dates)',
                        shortcut_code: 'tenant_activity_all_date',
                    },
                    {
                        icon: 'receipt',
                        title: 'View Tax Invoices',
                        shortcut_code: 'view_tax_invoice',
                    },
                    { icon: 'print', title: 'Print', shortcut_code: 'print' },
                ];
            }
        },
        nameWithDash({ field_key, field_value, lease_property, is_inactive }) {
            let inactive_char = '';
            switch (this.search_type) {
                case 1:
                    if (!field_value) {
                        return 'Please select...';
                    }
                    if (lease_property) return `${inactive_char}${field_key} — ${field_value} (${lease_property})`;
                    return `${inactive_char}${field_key} — ${field_value}`;
                    break;
                default:
                    if (!field_value) {
                        return 'Please select...';
                    }
                    return `${inactive_char}${field_key} — ${field_value}`;
                    break;
            }
        },
        getValueInList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.field_key === param1);
                if (filtered.length > 0) {
                    return filtered[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        async deleteLease() {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.lease_status_index = 1;
                let form_data = new FormData();
                form_data.append('page_source', 'leaseSummaryFormTemplate');
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('lease_code', this.lease_code.field_key);
                form_data.append('no_load', true);
                this.$api.post('lease/delete/lease-main', form_data).then((response) => {
                    let error_server_msg2 = response.data.error_server_msg;
                    this.error_server_msg2 = error_server_msg2;
                    if (error_server_msg2.length === 0) {
                        this.lease_code = {
                            field_key: '',
                            field_value: 'Please select...',
                        };
                    }
                });
            }
        },
        loadPropertyList: function () {
            let active_only = '1';
            if (this.show_inactive_property) active_only = '0';
            let form_data = new FormData();
            form_data.append('page_source', 'leaseSummaryFormTemplate');
            form_data.append('manager_code', this.manager_code.field_key);
            form_data.append('active_only', active_only);
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-dropdown-list', form_data).then((response) => {
                this.property_list = response.data.group;
                let property_list_ungroup = response.data.data;
                if (this.initialPropertyCode) {
                    this.property_code = this.getValueInList(this.initialPropertyCode, property_list_ungroup);
                }
                this.property_index = Math.random();
            });
        },
        loadLeaseList: function () {
            this.lease_list = [];
            let active_only = '0';
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('page_source', 'leaseSummaryFormTemplate');
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('active_only', active_only);
            if (this.search_type === 1) form_data.append('active_property_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-lease-list', form_data).then((response) => {
                this.lease_list = response.data.group;
                let lease_list_ungroup = response.data.data;
                this.loading_content_setting = false;
                if (this.initialLeaseCode) {
                    this.lease_code = this.getValueInList(this.initialLeaseCode, lease_list_ungroup);
                }
                this.lease_index = Math.random();
            });
        },
        loadManagerList: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('page_source', 'leaseSummaryFormTemplate');
            form_data.append('no_load', true);
            this.$api.post(this.cirrus8_api_url + 'api/loadPortfolioManagersList', form_data).then((response) => {
                this.manager_list = response.data.pm_data;
                this.loading_content_setting = false;
            });
        },
        loadLastDateOccupied: function () {
            // this.loading_content_setting = true;
            if (this.property_code.field_key !== '' && this.lease_code.field_key) {
                let form_data = new FormData();
                form_data.append('page_source', 'leaseSummaryFormTemplate');
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('lease_code', this.lease_code.field_key);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/lease/fetch/last-date-occupied', form_data)
                    .then((response) => {
                        let start_date = response.data.start_date;
                        let end_date = response.data.end_date;
                        if (start_date !== '' && end_date !== '') {
                            this.last_occupied_date_range = '(Last occupied: ' + start_date + ' - ' + end_date + ')';
                        }
                        // this.loading_content_setting = false;
                    });
            }
        },
        selectLease: function (field_key, field_value, lease_address, field_group, is_inactive) {
            this.lease_code = {
                field_key: field_key,
                field_value: field_value,
                lease_address: lease_address,
                field_group: field_group,
                is_inactive: is_inactive,
            };
            this.selected_lease = {
                code: field_key,
                name: field_value,
                is_inactive: is_inactive,
            };
            if (this.property_code.is_inactive == 1) is_inactive = 1;
            bus.$emit('setInactiveStatus', is_inactive);
            this.is_inactive = is_inactive;
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        goToHref: function (param1, param2) {
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        goToShortcut: function (parameter) {
            let url = '';
            var today = new Date();
            var dd = today.getDate();
            var mm = today.getMonth() + 1;
            var yyyy = today.getFullYear();
            var single_date = yyyy + '-' + mm + '-' + dd;

            let from_date = '';
            let to_date = '';
            switch (parameter) {
                case 'property':
                    if (this.user_type !== 'A') {
                        window.open(
                            '?module=properties&command=property_summary_page&property_code=' +
                                this.property_code.field_key,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    } else {
                        window.open(
                            '?module=properties&command=v2_manage_property_page&property_code=' +
                                this.property_code.field_key,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    }

                    break;
                case 'company':
                    //module=companies&command=company&companyID=7ELEVEN
                    if (this.user_type !== 'A') {
                        window.open(
                            '?module=companies&command=companySummary&action=view&companyID=' +
                                this.$company_code +
                                '&propertyID=' +
                                this.property_code.field_key +
                                '&leaseID=' +
                                this.lease_code.field_key,
                        );
                    } else {
                        window.open(
                            '?module=companies&command=company&companyID=' +
                                this.$company_code +
                                '&propertyID=' +
                                this.property_code.field_key +
                                '&leaseID=' +
                                this.lease_code.field_key,
                        );
                    }

                    break;
                case 'lease_abstract':
                    this.loading_page_setting = true;
                    var form_data = new FormData();
                    form_data.append('properties', this.property_code.field_key);
                    form_data.append('leases', this.lease_code.field_key);
                    form_data.append('no_load', true);
                    form_data.append('user_type', localStorage.getItem('user_type'));
                    if (sessionStorage.getItem('sso_key'))
                        form_data.append('app_key', sessionStorage.getItem('sso_key'));
                    form_data.append('report_ids', '21');
                    form_data.append('singleDate', single_date);
                    form_data.append('format', 'pdf');
                    axios
                        .post(this.cirrus8_api_url + 'api/reports/download/pdf', form_data, { responseType: 'blob' })
                        .then((response) => {
                            this.loading_page_setting = false;
                            let blob = new Blob([response.data], { type: 'application/pdf' });
                            let a = document.createElement('a');
                            a.style = 'display: none';
                            document.body.appendChild(a);
                            url = window.URL.createObjectURL(blob);
                            a.href = url;
                            var fileName = 'Lease_Abstract';
                            a.download = fileName + '.pdf';
                            a.click();
                        });
                    break;
                case 'tenant_activity_current_month':
                    this.show_tenant_activity = true;
                    this.loading_page_setting = true;
                    this.show_tenant_html = '';
                    var cmm = today.getMonth() + 1;
                    mm = today.getMonth() + 2;
                    var yyyy2 = today.getFullYear();
                    if (mm > 12) {
                        mm = 1;
                        yyyy2++;
                    }
                    cmm = cmm < 10 ? '0' + cmm : cmm;
                    mm = mm < 10 ? '0' + mm : mm;
                    from_date = '01' + '/' + cmm + '/' + yyyy;
                    to_date = '01' + '/' + mm + '/' + yyyy2;
                    url = '?module=managementReports&command=tenantActivity';
                    var form_data = new FormData();
                    form_data.append('propertyIDList', this.property_code.field_key);
                    form_data.append('propertyID', this.property_code.field_key);
                    form_data.append('tenantID', this.lease_code.field_key);
                    form_data.append('selectBy', 'property');
                    form_data.append('transactionOption', 'allTransactions');
                    form_data.append('accountOption', 'allAccountCodes');
                    form_data.append('format', 'screen');
                    form_data.append('sortOption', 'sortDate');
                    form_data.append('showSubTotal', 'Yes');
                    form_data.append('showZeroReceipts', 'Yes');
                    form_data.append('showCreditAllocations', 'Yes');
                    form_data.append('pageBreak', 'Yes');
                    form_data.append('fromDate', from_date);
                    form_data.append('toDate', to_date);
                    form_data.append('action', 'finalise');
                    form_data.append('from', 'leaseSummary');

                    form_data.append('app_origin', 'lease_page');
                    form_data.append('no_load', true);
                    axios.post(url, form_data).then((response) => {
                        this.loading_page_setting = false;
                        this.show_tenant_html = response.data;
                    });
                    break;
                case 'tenant_activity_all_date':
                    this.show_tenant_activity = true;
                    this.loading_page_setting = true;
                    this.show_tenant_html = '';
                    url = '?module=managementReports&command=tenantActivity';
                    var form_data = new FormData();
                    form_data.append('propertyIDList', this.property_code.field_key);
                    form_data.append('propertyID', this.property_code.field_key);
                    form_data.append('tenantID', this.lease_code.field_key);
                    form_data.append('selectBy', 'property');
                    form_data.append('transactionOption', 'allTransactions');
                    form_data.append('accountOption', 'allAccountCodes');
                    form_data.append('format', 'screen');
                    form_data.append('sortOption', 'sortDate');
                    form_data.append('showSubTotal', 'Yes');
                    form_data.append('showZeroReceipts', 'Yes');
                    form_data.append('showCreditAllocations', 'Yes');
                    form_data.append('pageBreak', 'Yes');
                    form_data.append('fromDate', '01/01/1900');
                    form_data.append('toDate', '31/12/2999');
                    form_data.append('action', 'finalise');
                    form_data.append('from', 'leaseSummary');

                    form_data.append('app_origin', 'lease_page');
                    form_data.append('no_load', true);
                    axios.post(url, form_data).then((response) => {
                        this.loading_page_setting = false;
                        this.show_tenant_html = response.data;
                    });
                    break;
                case 'view_tax_invoice':
                    this.show_tax_invoice_modal = true;
                    break;
                case 'view_task_management':
                    this.show_task_management = true;
                    break;
                case 'generate_arrears_statement_download':
                    this.loading_page_setting = true;
                    url =
                        '?module=ar&command=generateArrearsStatement&action=dlLetterSingle_' +
                        this.property_code.field_key +
                        '~' +
                        this.lease_code.field_key +
                        '';
                    var form_data = new FormData();
                    form_data.append('showCurrentOnly', 'Yes');
                    form_data.append('includeCredits', 'Yes');
                    form_data.append('ccMe', '1');
                    form_data.append('exclude_letter', 'true');
                    form_data.append('property', this.property_code.field_key);
                    form_data.append('tenantID', this.lease_code.field_key);
                    form_data.append('app_origin', 'lease_page');
                    form_data.append('no_load', true);
                    axios.post(url, form_data).then((response) => {
                        this.loading_page_setting = false;
                        if (response.data.substring(0, 5) === 'Error') {
                            this.$noty.error('PDF file cannot find.');
                        } else {
                            document.location.href = 'download.php?fileID=' + response.data;
                        }
                    });
                    break;
                case 'print':
                    var printWindow = window.open(
                        '?module=leases&command=lease_print_page&is_live=1&property_code=' +
                            this.property_code.field_key +
                            '&lease_code=' +
                            this.lease_code.field_key +
                            '&version_id=' +
                            this.version_id,
                        '_blank',
                        'toolbar=yes,scrollbars=yes,resizable=no,top=20,left=30,width=900,height=600',
                    );
                    printWindow.focus();
                    break;
                case 'raise_invoice':
                    window.open(
                        '?module=ar&command=oneOffCharge&selectionMethod=property&propertyID=' +
                            this.property_code.field_key +
                            '&leaseID=' +
                            this.lease_code.field_key,
                        '_blank',
                    );
                    break;
                case 'letter':
                    window.open(
                        `?module=administration&command=letter_v2&searchMethod=Tenant&propertyCode=${this.property_code.field_key}&leaseCode=${this.lease_code.field_key}`,
                        '_blank',
                    );
                    break;
                case 'sms_send':
                    bus.$emit('toggleSMSSendingModal', {
                        property_code: this.property_code.field_key,
                        lease_code: this.lease_code.field_key,
                        company_code: '',
                        contact_detail_id: '',
                        form_section: this.page_form_type,
                    });
                    break;
            }
        },
        showNewLease: function () {
            this.loadPropertyList();

            this.form_mode = 1;
            this.lease_page_title = 'New Lease';
            this.new_lease_code = '';

            this.updatePageTitle(this.lease_page_title);
        },
        cancelNewLease: function (reset_property = true) {
            this.reset_filter(reset_property);

            this.lease_page_title = 'Lease Summary';
            this.form_mode = 0;

            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_lease_code = '';
            this.lease_existed = true;

            if (Object.keys(this.selected_lease).length != 0) {
                this.updatePageTitle(
                    this.selected_lease.code +
                        '/' +
                        this.lease_details.main_form.property_code +
                        ' - ' +
                        this.selected_lease.name,
                );
            } else {
                this.updatePageTitle(this.lease_page_title);
            }
        },
        showLeaseListTable: function () {
            if (this.form_mode === 0) {
                this.form_mode = 0;
                this.lease_code = { field_key: '' };
            } else {
                this.error_msg = [];
                this.error_server_msg2 = [];
                this.new_lease_code = '';
                this.lease_existed = true;
            }
        },
        checkLeaseCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            let new_lease_code = this.new_lease_code.toUpperCase().trim();

            this.new_lease_code = new_lease_code;
            if (new_lease_code.length > 10) {
                // this.error_msg.push({id:'new_lease_code', message: 'Lease code allows 10 characters only'});
                this.error_server_msg2.push(['Lease code allows 10 characters only']);
            } else if (new_lease_code === '') {
                // this.error_msg.push({id:'new_lease_code', message: 'Please input a valid lease code'});
                this.error_server_msg2.push(['Please input a valid lease code']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('page_source', 'leaseSummaryFormTemplate');
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('new_lease_code', new_lease_code);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/lease/fetch/check-code-if-exist', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        if (this.error_server_msg2.length === 0) {
                            this.lease_existed = response.data.lease_existed;
                            if (!this.lease_existed) {
                                this.force_load_new_lease++;
                                // var child = this.$refs.main_form_new_lease;
                                // child.loadForm();
                            }
                        }
                        this.loading_page_setting = false;
                    });
            }
        },
        getLeaseCode: function (value) {
            let lease_code = value.lease_code;
            let lease_name = value.lease_name;
            this.lease_commencement_date = value.lease_commencement_date;
            let label = lease_code + ' - ' + lease_name;
            this.cancelNewLease(false);
            // this.loadLeaseList();
            this.lease_code = {
                field_key: lease_code,
                field_value: label,
                value: lease_code,
                label: label,
            };
        },
        startTour: function () {
            this.$tours['leaseTour'].start();
        },
        startNewLeaseTour: function () {
            this.$tours['newLeaseTour'].start();
        },
        reset_filter: function (reset_property = true) {
            this.lease_list = [];
            if (reset_property) {
                this.lease_code = { field_key: '' };
                this.property_code = { field_key: '' };
                this.property_list = [];
                this.SET_PROPERTY_CODE('');
                this.SET_LEASE_CODE('');
                this.loadManagerList();
                this.loadPropertyList();
                this.manager_code = { field_key: '' };
            }
            this.loadLeaseList();
        },
        reloadLeasePage: function () {
            if (this.lease_code.field_key !== '') {
                bus.$emit('loadLeaseOutstandingSection', '');
                bus.$emit('loadLeaseBondSection', '');
                bus.$emit('loadLeaseMainFormSection', '');
                bus.$emit('loadLeaseContactSection', '');
                bus.$emit('loadLeaseUnitDetailsSection', '');
                bus.$emit('loadLeaseDirectManagementFeeSection', '');
                bus.$emit('loadLeaseDiarySection', '');
                bus.$emit('loadLeaseNoteSection', '');
                bus.$emit('loadLeaseInspectionSection', '');
                bus.$emit('loadLeaseInsuranceSection', '');
                bus.$emit('loadLeaseGuaranteeSection', '');
                bus.$emit('loadLeaseDocumentSection', '');
                let eventData = {
                    property_code: this.property_code.field_key,
                    lease_code: this.lease_code.field_key,
                };
                bus.$emit('loadLeaseEmailSection', eventData);
                bus.$emit('loadLeaseSMSSection', eventData);
            }
        },
        changeSearchType: function (type) {
            switch (type) {
                case 'manager_code':
                    this.lease_code = { field_key: '' };
                    this.lease_list = [];
                    this.property_code = { field_key: '' };
                    this.property_list = [];
                    this.SET_PROPERTY_CODE('');
                    this.SET_LEASE_CODE('');

                    this.loadPropertyList();
                    break;
                case 'property_code':
                    if (this.search_type != 1 && this.form_mode === 0) {
                        this.lease_code = { field_key: '' };
                        this.lease_list = [];
                        this.SET_PROPERTY_CODE(this.property_code.field_key);
                        this.loadLeaseList();
                    }
                    break;
                case 'lease_code':
                    if (this.search_type == 1) {
                        let property_code = this.lease_code.lease_property;
                        let property_name = this.lease_code.lease_property_name;
                        this.property_code = {
                            field_key: property_code,
                            field_value: property_name,
                            value: property_code,
                            label: property_name,
                        };

                        this.property_list = [];
                        this.property_list = [
                            {
                                field_key: property_code,
                                field_value: property_name,
                                value: property_code,
                                label: property_name,
                            },
                        ];

                        this.SET_PROPERTY_CODE(this.lease_code.lease_property);
                    }

                    this.SET_LEASE_CODE(this.lease_code.field_key);
                    break;
            }
        },
        returnInactiveClass: function (property_is_inactive, lease_is_inactive) {
            if (lease_is_inactive == 1) return 'lease-inactive-row';
            return '';
        },
        async processLeaseInactiveStatus(status) {
            let form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('lease_code', this.lease_code.field_key);
            form_data.append('no_load', true);
            this.$api.post('lease/fetch/outstanding-receipts', form_data).then(async (response) => {
                this.lease_outstanding_amount_list = response.data.filter((m) => eval(m.unpaid_amount) !== 0);
                let total_amount = this.unpaidTotal();
                if (total_amount !== 0 && status === 1 && this.is_inactive == 0) {
                    let dialog_prop = {
                        title: 'Outstanding Balance Alert',
                        message:
                            "There's an outstanding balance on your lease. Are you sure you want to set the lease to 'Inactive'?",
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes, set as Inactive', value: 1, color: 'primary' },
                            { label: 'Cancel', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.setLeaseInactiveStatus(status);
                    }
                } else {
                    this.setLeaseInactiveStatus(status);
                }
            });
        },
        unpaidTotal() {
            let total = 0;
            for (let x = 0; x <= this.lease_outstanding_amount_list.length - 1; x++) {
                if (this.outstanding_mode === 1)
                    total =
                        total + eval(this.lease_outstanding_amount_list[x].unpaid_amount.toString().replace(/,/g, ''));
                else {
                    if (
                        typeof this.lease_outstanding_amount_list[x].due_date_raw !== 'undefined' &&
                        this.lease_outstanding_amount_list[x].due_date_raw !== ''
                    )
                        if (this.isBeforeToday(new Date(this.lease_outstanding_amount_list[x].due_date_raw)))
                            total =
                                total +
                                eval(this.lease_outstanding_amount_list[x].unpaid_amount.toString().replace(/,/g, ''));
                }
            }
            return total;
        },
        isBeforeToday: function (date) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return date < today;
        },
        setLeaseInactiveStatus: function (status) {
            this.loading_inactive_btn = true;
            let form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('lease_code', this.lease_code.field_key);
            form_data.append('status', status);
            form_data.append('no_load', true);
            this.$api.post('lease/process/change-lease-inactive-status', form_data).then(async (response) => {
                this.loading_inactive_btn = false;
                this.loadLeaseList();
                this.lease_code.is_inactive = status;
                if (this.property_code.is_inactive == 1) status = 1;
                bus.$emit('setInactiveStatus', status);
                this.is_inactive = status;
            });
        },
        processSaveFormNotes: function () {
            let text_area_data = this.form_notes.trim(); // Remove unnecessary white spaces

            var form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('lease_code', this.lease_code.field_key);
            form_data.append('notes', text_area_data);
            this.$api.post('lease/update/main-notes', form_data).then((response) => {
                this.loadFormNotes();
                bus.$emit('loadLeaseMainFormSection', '');
            });
        },
        loadFormNotes: function () {
            this.form_notes = '';
            if (this.property_code.field_key !== '' && this.lease_code.field_key !== '') {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('lease_code', this.lease_code.field_key);
                form_data.append('no_load', true);
                this.$api.post('lease/fetch/main-notes', form_data).then((response) => {
                    this.form_notes = response.data.form_notes;
                    this.form_notes_old = response.data.form_notes;
                });
            }
        },
        doubleClickForm: function () {
            bus.$emit('triggerDoubleClick');
        },
    },
    created() {
        bus.$on('loadLastDateOccupied', (data) => {
            this.loadLastDateOccupied();
        });
        bus.$on('clearLeaseDropdown', (data) => {
            this.changeSearchType('property_code');
        });
        bus.$on('changeEditMode', (data) => {
            this.edit_form = data;
            this.loadFormNotes();
        });
        bus.$on('processSaveFormNotes', (data) => {
            this.processSaveFormNotes();
        });
    },
    watch: {
        manager_code: function () {
            this.changeSearchType('manager_code');
        },
        property_code: function () {
            this.changeSearchType('property_code');
            this.error_msg = {};
            this.error_server_msg2 = [];
        },
        lease_code: function () {
            this.changeSearchType('lease_code');
            this.loadLastDateOccupied();
            this.error_msg = {};
            this.error_server_msg2 = [];

            this.selected_lease = {
                code: this.lease_code.field_key,
                name: this.lease_code.field_value,
            };
            let is_inactive = this.lease_code.is_inactive;
            if (this.property_code.is_inactive == 1) is_inactive = 1;
            bus.$emit('setInactiveStatus', is_inactive);
            this.is_inactive = is_inactive;
            if (this.lease_code.field_key !== '') this.loadFormNotes();
        },
        show_inactive_property: function () {
            this.loadPropertyList();
        },
        show_inactive_lease: function () {
            this.loadLeaseList();
        },
        search_type: function () {
            this.reset_filter();
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>
<style lang="scss" scoped>
small {
    display: block;
}
</style>
