<template>
    <div class="c8-page">
        <div>
            <div>
                <v-toolbar flat>
                    <v-toolbar-title>
                        <cirrus-page-header :title="lease_page_title" />
                    </v-toolbar-title>
                    <div class="flex-grow-1"></div>
                    <!--            <v-btn x-small @click="startTour()" class="v-step-final-message" color="secondary">Tour</v-btn>-->
                </v-toolbar>

                <cirrus-server-error
                    :error_msg="error_server_msg"
                    :errorMsg2="error_server_msg2"
                ></cirrus-server-error>
                <div class="page-form">
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Property:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <multiselect
                                v-model="property_code"
                                :options="property_list"
                                :allowEmpty="false"
                                class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                :custom-label="nameWithDash"
                                group-label="language"
                                placeholder="Select a property"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                            <v-btn
                                :loading="property_list.length <= 0"
                                depressed
                                elevation="0"
                                tile
                                small
                                color="normal"
                                height="30"
                                v-on:click="showLeaseListTable()"
                            >
                                <v-icon>arrow_right</v-icon>
                            </v-btn>
                        </v-col>
                    </v-row>
                    <v-row
                        class="form-row"
                        v-if="property_code.field_key !== ''"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Enter A Lease Code:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <input
                                type="hidden"
                                id="lease_code"
                                :value="new_lease_code"
                            />
                            <div v-if="!check_lease_flag && !show_new_main_form">
                                <cirrus-input
                                    class="v-step-lease-enter"
                                    v-model="new_lease_code"
                                    size="10"
                                    :id="'new_lease_code'"
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                                <v-btn
                                    color="primary"
                                    small
                                    @click="checkLeaseCode()"
                                    >Create New
                                </v-btn>
                            </div>
                            <div v-if="check_lease_flag && !show_new_main_form">
                                <span class="form-input-text">{{ new_lease_code }}</span>
                            </div>
                            <div v-if="check_lease_flag && show_new_main_form">
                                <span class="form-input-text">{{ new_lease_code }}</span>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row
                        class="form-row"
                        v-if="show_new_main_form"
                    >
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Version Number:
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <span class="form-input-text">{{ new_version_id }}</span>
                        </v-col>
                    </v-row>
                </div>

                <sui-table
                    v-if="property_code.field_key !== '' && lease_list_temp.length > 0 && !show_new_main_form"
                    stackable
                    selectable
                    class="fixed_headers"
                >
                    <sui-table-header class="fixed_headers_thead">
                        <sui-table-row>
                            <sui-table-header-cell
                                colspan="8"
                                class="fieldDescription headerTr"
                                >Lease Records - click to edit the lease version
                            </sui-table-header-cell>
                        </sui-table-row>
                        <sui-table-row class="fieldDescription">
                            <sui-table-header-cell class="center fieldDescription headerTr">Type</sui-table-header-cell>
                            <sui-table-header-cell class="center fieldDescription headerTr"
                                >Property
                            </sui-table-header-cell>
                            <sui-table-header-cell class="fieldDescription headerTr">Lease</sui-table-header-cell>
                            <sui-table-header-cell class="fieldDescription headerTr">Description</sui-table-header-cell>
                            <sui-table-header-cell class="center fieldDescription headerTr"
                                >Version
                            </sui-table-header-cell>
                            <sui-table-header-cell class="center fieldDescription headerTr"
                                >Status
                            </sui-table-header-cell>
                            <sui-table-header-cell class="center fieldDescription headerTr"
                                >Created By
                            </sui-table-header-cell>
                            <sui-table-header-cell class="center fieldDescription headerTr"
                                >Date Created
                            </sui-table-header-cell>
                        </sui-table-row>
                    </sui-table-header>
                    <sui-table-body v-if="loading_setting">
                        <sui-table-row>
                            <sui-table-cell
                                colspan="8"
                                class="center"
                            >
                                <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
                            </sui-table-cell>
                        </sui-table-row>
                    </sui-table-body>
                    <sui-table-body>
                        <sui-table-row
                            v-for="(lease_list_temp_data, lease_list_temp_index) in lease_list_temp"
                            :key="lease_list_temp_index"
                            @click="selectLease(lease_list_temp_data.lease_code, lease_list_temp_data.version_id)"
                        >
                            <sui-table-cell class="center">
                                <span v-if="lease_list_temp_data.form_type === '2'">New Lease</span>
                                <span v-if="lease_list_temp_data.form_type === '3'">Lease Assignment</span>
                                <span v-if="lease_list_temp_data.form_type === '4'">Lease Renewal</span>
                                <span v-if="lease_list_temp_data.form_type === '5'"
                                    >{{ strata_label }} Change of Ownership</span
                                >
                            </sui-table-cell>
                            <sui-table-cell class="center">
                                {{ lease_list_temp_data.property_code }}
                            </sui-table-cell>
                            <sui-table-cell class="">
                                {{ lease_list_temp_data.lease_code }} - {{ lease_list_temp_data.lease_name }}
                            </sui-table-cell>
                            <sui-table-cell class="">
                                {{ lease_list_temp_data.tenant_address }}
                            </sui-table-cell>
                            <sui-table-cell class="center">
                                {{ lease_list_temp_data.version_id }}
                            </sui-table-cell>
                            <sui-table-cell class="center">
                                <span
                                    v-if="
                                        lease_list_temp_data.process_state === '0' ||
                                        lease_list_temp_data.process_state === null
                                    "
                                    >Draft</span
                                >
                                <span v-if="lease_list_temp_data.process_state === '1'">Unprocessed</span>
                                <span v-if="lease_list_temp_data.process_state === '2'">Rejected</span>
                                <span v-if="lease_list_temp_data.process_state === '3'">Processed</span>
                                <span v-if="lease_list_temp_data.process_state === '4'">Archived</span>
                            </sui-table-cell>
                            <sui-table-cell class="center">
                                <span
                                    v-if="
                                        lease_list_temp_data.lease_created_by === '' ||
                                        lease_list_temp_data.lease_created_by === null
                                    "
                                    >No record</span
                                >
                                <span
                                    v-if="
                                        lease_list_temp_data.lease_created_by !== '' &&
                                        lease_list_temp_data.lease_created_by !== null
                                    "
                                    >{{ lease_list_temp_data.user_email }}</span
                                >
                            </sui-table-cell>
                            <sui-table-cell class="center">
                                {{ lease_list_temp_data.lease_created_date }}
                            </sui-table-cell>
                        </sui-table-row>
                    </sui-table-body>
                </sui-table>

                <lease-stepper-form-template-component
                    v-if="show_new_main_form"
                    :new_lease_flag="new_lease_flag"
                    :lease_code="new_lease_code"
                    :property_code="property_code.field_key"
                    :version_id="new_version_id"
                    :current_db="current_db"
                    :user_type="user_type"
                    :username="username"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :country_default_settings="initialCountryDefaultSettings"
                ></lease-stepper-form-template-component>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions, mapMutations, mapState } from 'vuex';

import LeaseMainForm from '../forms/leaseMainForm.vue';
import LeaseDiary from '../forms/leaseDiary.vue';
import LeaseNotes from '../forms/leaseNotes.vue';
import LeaseGuarantee from '../forms/leaseGuarantee.vue';
import LeaseInsurance from '../forms/leaseInsurance.vue';
import LeaseInspection from '../forms/leaseInspection.vue';
import LeaseUnitDetails from '../forms/leaseUnitDetails.vue';
import LeaseBonds from '../forms/leaseBonds.vue';
import LeaseOutstandingAmounts from '../forms/leaseOutstandingAmounts.vue';
import LeaseContacts from '../forms/leaseContacts.vue';
import LeaseDocuments from '../../../../DocumentDirectory/sections/LeaseDocumentFormV2.vue';
import global_mixins from '../../../../plugins/mixins';

// Vue.use(SuiVue);
export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialLeaseCode: String,
        initialVersionId: String,
        initialCountryDefaultSettings: String,
    },
    components: {
        'lease-main-form-component': LeaseMainForm,
        'lease-diary-component': LeaseDiary,
        'lease-notes-component': LeaseNotes,
        'lease-guarantee-component': LeaseGuarantee,
        'lease-insurance-component': LeaseInsurance,
        'lease-inspection-component': LeaseInspection,
        'lease-unit-details-component': LeaseUnitDetails,
        'lease-bonds-component': LeaseBonds,
        'lease-outstanding-amount-component': LeaseOutstandingAmounts,
        'lease-contacts-component': LeaseContacts,
        'lease-documents-component': LeaseDocuments,
    },
    data() {
        return {
            property_code: { field_key: '', field_value: 'Please select...' },
            lease_code: '',
            new_lease_code: '',
            version_id: null,
            property_list: [],
            lease_list: [],
            lease_main_list: [],
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            new_version_id: '1',
            read_only: this.initialReadOnly,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            template_tab: null,
            loading_setting: false,
            loading_page_setting: false,
            step: 0,
            shortcuts: [
                { icon: 'business', title: 'Go to property', shortcut_code: 'property' },
                { icon: 'table_chart', title: 'Lease Abstract', shortcut_code: 'lease_abstract' },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (current month)',
                    shortcut_code: 'tenant_activity_current_month',
                },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (all dates)',
                    shortcut_code: 'tenant_activity_all_date',
                },
                { icon: 'receipt', title: 'View Tax Invoices', shortcut_code: 'view_tax_invoice' },
                { icon: 'history', title: 'Lease Activity Logs', shortcut_code: 'lease_logs' },
                { icon: 'print', title: 'Print', shortcut_code: 'print' },
            ],
            force_load: false,
            lease_page_title: 'New Lease',
            error_server_msg: {},
            error_server_msg2: [],
            lease_list_temp: [],
            lease_existed: true,
            force_load_new_lease: 0,
            show_new_main_form: false,
            check_lease_flag: false,
            new_lease_flag: true,
            strata_label: 'strata',
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'tour_steps',
            'doc_active_version',
            'sys_ver_control_list',
            'auto_diarise',
        ]),
    },
    mounted() {
        // console.log(localStorage.getItem('cirrus8_api_url'));

        let country_default_settings = JSON.parse(atob(this.initialCountryDefaultSettings));
        this.strata_label = this.ucwords(country_default_settings.strata);

        this.fetchFormVersionControl();
        this.loadPropertyList();
        this.fetchCountryList();
        this.fetchAccountIncomeGroupedList();
        this.fetchAccountExpenseGroupedList();
        this.fetchBondPropertyList();
        this.fetchParamLeaseTypeList();
        this.fetchParamDivisionList();
        this.fetchParamTenantTypeList();
        this.fetchRetailCategoryList();
        this.fetchRetailSubCategoryList();
        this.fetchRetailFineCategoryList();
        this.fetchTaxRateList();
        this.fetchPMAutoDiarise();
        let step_array = [
            { target: '.v-step-property-select', content: 'Select the property you want to add a new lease' },
            {
                target: '.v-step-lease-enter',
                content:
                    'This is where you going to enter the new lease code, the system will validate and show you all the draft or pending leases that uses that code.',
            },
            { target: '.v-step-final-message', content: 'You have finish the page tour.' },
        ];
        this.SET_TOUR_STEPS(step_array);
    },
    methods: {
        ...mapActions([
            'fetchCountryList',
            'fetchAccountIncomeGroupedList',
            'fetchAccountExpenseGroupedList',
            'fetchBondPropertyList',
            'fetchParamDivisionList',
            'fetchParamLeaseTypeList',
            'fetchParamTenantTypeList',
            'fetchRetailCategoryList',
            'fetchRetailSubCategoryList',
            'fetchRetailFineCategoryList',
            'fetchTaxRateList',
            'fetchParamTAList',
            'fetchParamPMList',
            'fetchPMAutoDiarise',
            'fetchFormVersionControl',
        ]),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_LEASE_CODE', 'SET_TOUR_STEPS']),
        getValueInList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.field_key === param1);
                if (filtered.length > 0) {
                    return filtered[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        fieldKey: '',
                        fieldValue: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        loadPropertyList: function () {
            let form_data = new FormData();
            form_data.append('page_source', 'leaseFormTemplate');
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-dropdown-list', form_data).then((response) => {
                this.property_list = response.data.data;
                if (this.initialPropertyCode && this.initialLeaseCode && this.initialVersionId) {
                    this.property_code = this.getValueInList(this.initialPropertyCode, this.property_list);
                    this.new_lease_code = this.initialLeaseCode;
                    this.version_id = this.initialVersionId;
                    this.selectLease(this.new_lease_code, this.version_id);
                }
            });
        },
        loadLeaseList: function () {
            this.loading_setting = true;
            let form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-lease-list', form_data).then((response) => {
                this.lease_list = response.data.data;
                this.loading_setting = false;
            });
        },
        selectLease: function (lease_code, version_id) {
            this.new_lease_flag = true;
            this.new_lease_code = lease_code;
            this.new_version_id = version_id;
            this.check_lease_flag = true;
            this.show_new_main_form = true;
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        goToHref: function (param1, param2) {
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        goToShortcut: function (parameter) {
            switch (parameter) {
                case 'property':
                    window.open(
                        '?module=properties&command=v2_manage_property_page&property_code=' +
                            this.property_code.field_key,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'company':
                    //module=companies&command=company&companyID=7ELEVEN
                    window.open(
                        '?module=companies&command=company&companyID=' + this.$company_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'lease_abstract':
                    break;
                case 'tenant_activity_current_month':
                    break;
                case 'tenant_activity_all_date':
                    break;
                case 'view_tax_invoice':
                    break;
                case 'print':
                    break;
            }
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        showNewLease: function () {
            this.form_mode = 1;
            this.lease_page_title = 'New Lease';
            this.new_lease_code = '';
        },
        cancelNewLease: function () {
            this.lease_page_title = 'Lease Summary';
            this.form_mode = 0;

            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_lease_code = '';
            this.lease_existed = true;
        },
        showLeaseListTable: function () {
            this.show_new_main_form = false;
            this.check_lease_flag = false;
            this.lease_list_temp = [];
            this.new_lease_code = '';
            this.loadLeaseList();
            this.loadTempLease();

            this.error_server_msg2 = [];
            this.error_msg = [];
        },
        checkLeaseCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            let new_lease_code = this.new_lease_code.toUpperCase().trim();

            this.new_lease_code = new_lease_code;
            if (new_lease_code.length > 10) {
                // this.error_msg.push({id:'new_lease_code', message: 'Lease code allows 10 characters only'});
                this.error_server_msg2.push(['Lease code allows 10 characters only']);
            } else if (new_lease_code === '') {
                // this.error_msg.push({id:'new_lease_code', message: 'Please input a valid lease code'});
                this.error_server_msg2.push(['Please input a valid lease code']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('new_lease_code', new_lease_code);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/fetch/check-code-if-exist', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        if (this.error_server_msg2.length === 0) {
                            this.lease_list_temp = response.data.lease_list_temp;
                            this.lease_existed = response.data.lease_existed;
                            this.new_version_id = response.data.new_version_id.toString();
                            this.loading_page_setting = false;
                            if (!this.lease_existed) {
                                this.check_lease_flag = true;
                                if (this.lease_list_temp.length === 0) {
                                    this.show_new_main_form = true;
                                    this.lease_code = new_lease_code;
                                }
                            } else {
                            }
                        }
                    });
            }
        },
        createNewLeaseVersion: function () {
            this.show_new_main_form = true;
        },
        loadTempLease: function () {
            let form_data = new FormData();
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('no_load', true);
            this.$api.post(this.cirrus8_api_url + 'api/temp/lease/fetch/lease-list', form_data).then((response) => {
                let lease_list_temp = response.data.lease_list_temp;
                let filtered_list = lease_list_temp.filter((m) => m.form_type === '2');
                this.lease_list_temp = filtered_list;
            });
        },
        getLeaseCode: function (value) {
            let lease_code = value.lease_code;
            let lease_name = value.lease_name;
            let label = lease_code + ' - ' + lease_name;
            this.cancelNewLease();
            this.loadLeaseList();
            this.lease_code = {
                fieldKey: lease_code,
                field_key: lease_code,
                fieldValue: label,
                field_value: label,
                value: lease_code,
                label: label,
            };
        },
        startTour: function () {
            this.$tours['leaseTour'].start();
        },
    },
    watch: {
        property_code: function () {
            this.lease_code = { fieldKey: '' };
            this.lease_list = [];
            this.loadLeaseList();
            this.loadTempLease();
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
