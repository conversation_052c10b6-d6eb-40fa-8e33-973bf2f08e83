<style>
.error-message-div .v-alert {
    margin-bottom: 0px !important;
}

.rent_review_tabs .v-tabs-bar {
    background-color: #3489a1 !important;
}

.data-grid .highlight td {
    font-weight: initial;
}

.multiRow .col {
    padding-top: 0;
    padding: 3px !important;
    vertical-align: middle;
}

.c8-page input {
    min-height: auto !important;
}
</style>
<template>
    <v-app v-cloak>
        <div
            class="c8-page"
            v-if="view_list && !edit_charge_id && loadPage == 1"
        >
            <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
            <v-toolbar flat>
                <v-toolbar-title>
                    <cirrus-page-header title="Charge Review Tasks" />
                </v-toolbar-title>
                <div class="flex-grow-1"></div>
            </v-toolbar>
            <div v-if="loadPage == 1">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This form allows you to view the process status of charge
                review forms in the Cirrus8. To enter a new charge review -
                <a
                    href="#"
                    @click="
                        view_list = 0;
                        approve_notification = 0;
                        email_notification = 0;
                        clearLeaseList();
                        edit_charge_id = '';
                        disable_form = false;
                    "
                    >click here</a
                ><br /><br />
            </div>
            <div
                class="page-noty"
                v-if="email_notification && email_tenant_notification.withEmail"
            >
                <p class="success-box">
                    The rent review letter has been emailed to the following tenant:
                    <strong>{{ email_tenant_notification.withEmail }}</strong>
                </p>
            </div>
            <div
                class="page-noty"
                v-if="approve_notification"
            >
                <p class="success-box">Successfully approved</p>
            </div>
            <div
                v-if="send_on_approval && approve_notification && email_tenant_notification.withEmail"
                class="page-noty"
            >
                <p class="success-box">
                    Tenant invoices were successfully sent to the following:
                    <strong>{{ email_tenant_notification.withEmail }}</strong>
                </p>
            </div>

            <div
                v-if="
                    (send_on_approval || email_notification) &&
                    approve_notification &&
                    email_tenant_notification.withoutEmail
                "
                class="page-noty"
            >
                <p class="warning-box">
                    Email address is invalid or not existing for some tenants. Please check the email settings of the
                    following:
                    <strong>{{ email_tenant_notification.withoutEmail }}</strong>
                </p>
            </div>
            <div
                class="page-noty"
                v-if="rejected_notification"
            >
                <p class="error-box">Successfully rejected</p>
            </div>
            <div class="page-form">
                <v-row class="form-row">
                    <table class="data-grid data-grid-dense tableHive">
                        <tr class="form-subheader">
                            <td class="text-left">From Date</td>
                            <td class="text-left">To Date</td>
                            <td class="text-left">With Status</td>
                            <td></td>
                        </tr>

                        <tr>
                            <td class="text-left">
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    v-model="from_date_filter"
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker
                                >&nbsp;
                            </td>
                            <td class="text-left">
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    v-model="to_date_filter"
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker
                                >&nbsp;
                            </td>
                            <td class="text-left">
                                <multiselect
                                    v-model="status_filter"
                                    :options="status_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Please Select"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </td>
                            <td class="text-left">
                                <v-btn
                                    class=""
                                    @click="loadListCharge()"
                                    color="primary"
                                    dark
                                    small
                                    depressed
                                >
                                    Search
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-row>
            </div>

            <div class="page-form">
                <div class="text-right">
                    <cirrus-input
                        inputFormat="search"
                        v-model="search_table"
                        placeholder="Search"
                        :edit_form="edit_form"
                        style="padding-right: 1em"
                    ></cirrus-input>
                </div>
                <v-row class="form-row">
                    <v-data-table
                        class="c8-datatable-custom data-grid data-grid-dense tableHive"
                        dense
                        item-key="id"
                        :headers="review_list_headers"
                        :items="review_list"
                        :page.sync="page"
                        :search="search_table"
                        :custom-sort="customSort"
                    >
                        <template v-slot:item.charge_id="{ item }">
                            <div class="form-row no-border-line">
                                <a
                                    @click="
                                        loadSelectedCharges(item.charge_id);
                                        approve_notification = 0;
                                        email_notification = 0;
                                        rejected_notification = 0;
                                    "
                                    >View</a
                                >
                            </div>
                        </template>
                    </v-data-table>
                </v-row>
            </div>
        </div>

        <div
            class="c8-page"
            v-if="(!view_list || edit_charge_id) && loadPage == 1"
        >
            <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
            <v-toolbar flat>
                <v-toolbar-title>
                    <cirrus-page-header :title="lease_page_title" />
                </v-toolbar-title>
                <div class="flex-grow-1"></div>
            </v-toolbar>
            <br />
            <div>
                <b
                    >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This form should be used to advise
                    {{ trust_account_label }} of any changes to lease charges. i.e. rent or
                    {{ variable_outgoings_label }}.</b
                >
            </div>
            <div>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;To begin - simply select a lease, the charge type that you
                want to adjust, a date from which the adjustment applies, and the new amount you wish to charge.
            </div>
            <br />

            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_main_form"
                :errorMessage="error_message"
            ></cirrus-server-error>
            <div
                class="page-noty"
                v-if="success_notification"
            >
                <p class="success-box">
                    Your charge review submission has been successfully lodged. You will be notified when it has been
                    processed.
                </p>
            </div>
            <div
                class="page-noty"
                v-if="process_notification"
            >
                <p class="success-box">Your charge has been successfully processed.</p>
            </div>
            <div
                v-if="send_on_approval && process_notification && email_tenant_notification.withEmail"
                class="page-noty"
            >
                <p class="success-box">
                    Tenant invoices were successfully sent to the following:
                    <strong>{{ email_tenant_notification.withEmail }}</strong>
                </p>
            </div>

            <div
                v-if="send_on_approval && process_notification && email_tenant_notification.withoutEmail"
                class="page-noty"
            >
                <p class="warning-box">
                    Email address is invalid or not existing for some tenants. Please check the email settings of the
                    following:
                    <strong>{{ email_tenant_notification.withoutEmail }}</strong>
                </p>
            </div>
            <div
                class="page-noty"
                v-if="lease_charge_notification"
            >
                <p class="error-box">Lease Charge is not existing in Lease form.</p>
            </div>
            <div
                class="infoBox"
                v-for="(downloadData, index) in download_pdf"
                :key="index"
            >
                <a :href="'download.php?fileID=' + downloadData"
                    ><img
                        :src="asset_domain + 'assets/images/icons/pdf.png'"
                        alt="Adobe Logo"
                        class="icon"
                    /><strong>Download in printable format (PDF file)</strong> <u> left click to download or view</u></a
                >
            </div>

            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>

            <v-container
                fluid
                v-if="u_type == 'A' && edit_charge_id && loadPage == 1"
            >
                <v-row>
                    <v-col
                        cols="12"
                        md="6"
                    >
                    </v-col>
                    <v-col
                        cols="12"
                        md="6"
                    >
                        <v-textarea
                            class="vuetifyText v-step-feedback"
                            outlined
                            label="Your feedback (This response is sent to the client when either processing or rejecting a form)"
                            v-model="rejected_comment"
                            value=""
                            full-width
                            :disabled="edit_charge_status == 0 ? false : true"
                        ></v-textarea>
                        <div>
                            <div
                                v-if="edit_charge_status == 0"
                                style="float: right; margin-left: 10px"
                            >
                                <span>
                                    <v-btn
                                        color="error"
                                        dense
                                        small
                                        class="v-step-form-no-btn"
                                        @click="rejectFile()"
                                        ><v-icon>clear</v-icon>Reject</v-btn
                                    >
                                </span>
                            </div>
                            <div
                                v-if="edit_charge_status == 0"
                                style="float: right; margin-left: 10px"
                            >
                                <span>
                                    <v-btn
                                        color="primary"
                                        dense
                                        small
                                        class="v-step-form-yes-btn"
                                        @click="approveFile()"
                                        ><v-icon>done</v-icon>process</v-btn
                                    >
                                </span>
                            </div>
                            <div style="float: right !important; margin-top: 7px">
                                <span
                                    ><strong
                                        >This response is sent to the client when either approving or rejecting a form
                                    </strong></span
                                >
                            </div>
                        </div>
                    </v-col>
                </v-row>
            </v-container>

            <div
                class="page-form"
                v-if="(!view_list || edit_charge_id) && loadPage == 1"
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Date Range:
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-icon-date-picker
                            :size="'40'"
                            v-model="start_date"
                            :edit_form="edit_charge_id ? false : true"
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                        -
                        <cirrus-icon-date-picker
                            :size="'40'"
                            v-model="end_date"
                            :edit_form="edit_charge_id ? false : true"
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                    </v-col>
                </v-row>

                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Rent Review Type :
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            :disabled="disable_form"
                            v-model="rent_review_type"
                            :options="rent_review_list"
                            :allowEmpty="false"
                            class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                            group-label="language"
                            placeholder="Select Rent Review Type"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="edit_charge_id"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Send On TA Approval
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <sui-checkbox
                            v-model="send_on_approval"
                            :disabled="true"
                        />
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="rent_review_type.field_key == 'CPI'"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >CPI Index Name
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            :disabled="disable_form"
                            @input="selectCPI()"
                            v-model="cpi_index_name"
                            :options="cpi_index_list"
                            :allowEmpty="false"
                            class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                            group-label="language"
                            placeholder="please select"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="rent_review_type.field_key == 'CPI'"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Applicable CPI Index
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            :disabled="disable_form"
                            @input="selectApplicable()"
                            v-model="cpi_index_available"
                            :options="applicable_cpi_index_list"
                            :allowEmpty="false"
                            class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                            group-label="language"
                            placeholder="please select"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        &nbsp;&nbsp;
                        <sui-checkbox
                            :disabled="disable_form"
                            value="1"
                            v-model="cpi_manual"
                            style="padding-top: 5px"
                        />
                        Manual Entry
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="rent_review_type.field_key == 'CPI'"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                    >
                        CPI Index (
                        <select
                            v-if="!disable_form"
                            v-model="cpi_ages"
                            @change="selectApplicable()"
                        >
                            <option value="3">3 months ago</option>
                            <option value="6">6 months ago</option>
                            <option value="9">9 months ago</option>
                            <option value="12">12 months ago</option>
                            <option value="15">15 months ago</option>
                            <option value="18">18 months ago</option>
                            <option value="21">21 months ago</option>
                            <option value="24">24 months ago</option></select
                        ><span v-else>{{ cpi_ages }} months ago </span>)
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span
                            class="v-input"
                            v-if="cpi_manual"
                            ><input
                                :disabled="disable_form"
                                type="text"
                                @input="reComputeCPI(index_charge)"
                                class="cirrus-input-form-textbox"
                                v-model="cpi_index_old"
                        /></span>
                        <span
                            style="margin-top: 5px; display: block"
                            v-if="!cpi_manual"
                            >{{ cpi_index_old }}</span
                        >
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="rent_review_type.field_key == 'CPI'"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Current CPI Index
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span
                            class="v-input"
                            v-if="cpi_manual"
                            ><input
                                :disabled="disable_form"
                                type="text"
                                @input="reComputeCPI(index_charge)"
                                class="cirrus-input-form-textbox"
                                v-model="cpi_index_current"
                        /></span>
                        <span
                            style="margin-top: 5px; display: block"
                            v-if="!cpi_manual"
                            >{{ cpi_index_current }}</span
                        >
                        <span
                            style="margin-top: 5px; display: block"
                            v-if="total_increase_cpi && cpi_manual"
                            >= {{ total_increase_cpi }}% increase</span
                        >
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="!edit_charge_id"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn
                            class=""
                            @click="selectCharge()"
                            color="primary"
                            dark
                            small
                            tile
                        >
                            Search
                        </v-btn>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="lease_list2.length > 0"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Select a lease :
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <vue-dual-list-select
                            v-model="selectedLeaseCharge"
                            :options="lease_list2"
                            ref="refLeasesChargeModel"
                            :displayCount="false"
                        >
                        </vue-dual-list-select>
                    </v-col>
                </v-row>

                <v-card
                    v-if="lease_list2.length && !edit_charge_id"
                    class="section-toolbar"
                    dark
                    color="titleHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h6 class="title font-weight-black">Letter Template</h6>
                        &nbsp
                    </v-card-actions>
                </v-card>

                <div
                    v-if="lease_list2.length && !edit_charge_id"
                    class="page-form"
                >
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class=""
                        >
                            Notice: cirrus8 has changed when rent review letters are sent to tenants. Rent review
                            letters are now sent after the lease charge review is approved by
                            {{ trust_account_label }}ing.
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class="form-input"
                        >
                            <multiselect
                                v-show="!edit_charge_id"
                                :disabled="disable_form"
                                v-model="letter_template"
                                :options="letter_template_list"
                                :allowEmpty="true"
                                class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                group-label="language"
                                placeholder="Select Letter template"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="true"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class="form-input"
                        >
                            <sui-checkbox
                                value="1"
                                v-model="email_to_tenant"
                            />
                            Send e-mail to tenants.
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class="form-input"
                        >
                            <sui-checkbox
                                value="1"
                                v-model="email_to_me"
                            />
                            Send me a copy of this message.
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class="form-input"
                        >
                            <sui-checkbox
                                value="1"
                                v-model="include_letterhead"
                            />
                            Include letterhead.
                        </v-col>
                    </v-row>
                </div>

                <v-row
                    class="form-row"
                    v-if="lease_list2.length > 0"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input text-right"
                    >
                        <v-btn
                            class=""
                            @click="selectMultipleLease()"
                            color="primary"
                            dark
                            small
                            tile
                        >
                            Update Lease Selection
                        </v-btn>
                    </v-col>
                </v-row>
            </div>

            <v-card
                v-if="Object.keys(lease_rent_list).length && !edit_charge_id && with_back_charge"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Back Charge Details</h6>
                    &nbsp
                </v-card-actions>
            </v-card>

            <div
                class="page-form"
                v-if="Object.keys(lease_rent_list).length && !edit_charge_id && with_back_charge"
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Invoice Date
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-icon-date-picker
                            id="invoice_date"
                            :size="'40'"
                            v-model="invoice_date"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                    </v-col>
                </v-row>

                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Generate Interim Invoice
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <sui-checkbox
                            value="1"
                            v-model="generate_interim"
                        />
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="generate_interim"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Due Date
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-icon-date-picker
                            id="due_date"
                            :size="'40'"
                            v-model="due_date"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="generate_interim"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Send On TA Approval
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <sui-checkbox
                            value="1"
                            v-model="send_on_approval"
                        />
                    </v-col>
                </v-row>
            </div>

            <v-card
                v-if="Object.keys(lease_rent_list).length && edit_form == true"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">
                        <sui-checkbox
                            v-if="!edit_charge_id"
                            :checked="true"
                            value="1"
                            @change="checkAllReview($event)"
                        />
                        Rent Reviews
                    </h6>
                    &nbsp
                </v-card-actions>
            </v-card>

            <table
                border="1"
                cellpadding="0"
                cellspacing="0"
                class="data-grid"
                width="100%"
                style="border-collapse: collapse"
            >
                <tbody v-for="(leaseData, index) in lease_rent_list">
                    <tr class="subHeader rowToClick">
                        <td>
                            <sui-checkbox
                                v-if="!edit_charge_id"
                                class="check_review"
                                :checked="false"
                                value="1"
                                @change="checkReview($event, index)"
                            />
                            <strong>{{ index }}</strong>
                        </td>
                        <td
                            align="center"
                            nowrap
                        >
                            <strong>Review Type</strong>
                        </td>
                        <td
                            align="center"
                            nowrap
                        >
                            <strong>Rent Review Date</strong>
                        </td>
                        <td
                            align="right"
                            nowrap
                        >
                            <strong>Old Amount</strong>
                        </td>
                        <td align="right"><strong>New Amount</strong></td>
                        <td align="right"><strong>Increase</strong></td>
                        <td align="right"><strong>Back Charge (NET)</strong></td>
                        <td
                            width="100px"
                            align="center"
                            v-if="
                                typeof charge_template_pdf[index] !== 'undefined' && charge_template_pdf[index].length
                            "
                        >
                            <strong>Letter</strong>
                        </td>
                    </tr>

                    <template v-for="(row, key) in leaseData">
                        <tr class="highlight rowToClick">
                            <td>
                                <sui-checkbox
                                    v-if="!edit_charge_id"
                                    value="1"
                                    v-model="row.include"
                                />
                                {{ row.unit_charge_description }}
                                <span
                                    class="toggler"
                                    title="Show / Hide Details"
                                    ><img
                                        src="assets/images/icons/information.png"
                                        class="icon"
                                    />
                                    <a
                                        href="javascript:"
                                        @click="row.show_detail *= -1"
                                        >[ Details ]</a
                                    >
                                </span>
                            </td>
                            <td align="center">{{ row.rent_type }}</td>
                            <td align="center">{{ row.rent_date }}</td>
                            <td align="right">{{ row.old_amount_label }}</td>
                            <td align="right">{{ row.new_amount_label }}</td>
                            <td align="right">{{ row.cpi_increase }} %</td>
                            <td align="right">
                                <a
                                    v-if="row.invoice_pdf"
                                    :href="'download.php?fileID=' + row.invoice_pdf"
                                    >[view invoice]</a
                                >
                                {{ row.back_charge_amount_label }}
                            </td>
                            <td
                                v-if="
                                    typeof charge_template_pdf[index] !== 'undefined' &&
                                    charge_template_pdf[index].length
                                "
                            >
                                <div class="form-input">
                                    <a
                                        v-if="charge_template_pdf[index] && charge_template_pdf[index][key]"
                                        target="_blank"
                                        :href="'download.php?fileID=' + charge_template_pdf[index][key]"
                                        >Download
                                        <img
                                            :src="asset_domain + 'assets/images/icons/pdf.png'"
                                            alt="Adobe Logo"
                                            class="icon"
                                        />
                                    </a>
                                </div>
                            </td>
                        </tr>

                        <tr v-show="row.show_detail == 1">
                            <td :colspan="charge_template_pdf[index] && charge_template_pdf[index][key] ? 8 : 7">
                                <div class="page-form">
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="12"
                                            md="12"
                                            class="form-subheader"
                                            ><strong>Lease Details</strong></v-col
                                        >
                                    </v-row>

                                    <v-row class="form-row multiRow">
                                        <v-col
                                            xs="12"
                                            sm="2"
                                            md="2"
                                            class="form-label"
                                            >Type
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="10"
                                            md="10"
                                            class="form-input text-only text-left"
                                        >
                                            {{ row.unit_charge_type }}
                                        </v-col>
                                    </v-row>

                                    <v-row class="form-row multiRow">
                                        <v-col
                                            xs="12"
                                            sm="2"
                                            md="2"
                                            class="form-label"
                                            >Frequency
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="10"
                                            md="10"
                                            class="form-input text-only text-left"
                                        >
                                            {{ row.unit_charge_frequency_name }}
                                        </v-col>
                                    </v-row>

                                    <v-row class="form-row multiRow">
                                        <v-col
                                            xs="12"
                                            sm="2"
                                            md="2"
                                            class="form-label"
                                            >Charge
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="10"
                                            md="10"
                                            class="form-input text-only text-left"
                                        >
                                            From <strong>{{ row.unit_start_date }}</strong> to
                                            <strong>{{ row.unit_stop_date }}</strong>
                                        </v-col>
                                    </v-row>

                                    <v-row class="form-row multiRow">
                                        <v-col
                                            xs="12"
                                            sm="2"
                                            md="2"
                                            class="form-label"
                                            >Partial Rent Method
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="10"
                                            md="10"
                                            class="form-input text-only text-left"
                                        >
                                            {{ row.unit_partial_rent_desc }}
                                        </v-col>
                                    </v-row>

                                    <v-data-table
                                        class="c8-datatable-custom"
                                        dense
                                        item-key="id"
                                        :headers="headersDesc"
                                        :items="row.unit_charge_history"
                                        :items-per-page="items_per_page"
                                        hide-default-footer
                                        :page.sync="page"
                                        @page-count="page_count = $event"
                                        :search="search_datatable"
                                        :custom-sort="customSort"
                                    >
                                    </v-data-table>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>

            <div
                class="page-form"
                v-if="Object.keys(lease_rent_list).length && !edit_charge_id"
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="text-right"
                    >
                        <v-btn
                            class=""
                            @click="submitLeases()"
                            color="primary"
                            dark
                            small
                            tile
                        >
                            submit
                        </v-btn>
                    </v-col>
                </v-row>
            </div>
        </div>
    </v-app>
</template>

<script>
import { mapState } from 'vuex';
import LeaseMainForm from '../forms/leaseMainForm.vue';
import axios from 'axios';
import Vue from 'vue';

Vue.component('vue-dual-list-select', require('../../../elements/VueDualListSelect.vue').default);

const Swal = require('sweetalert2');

export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialLeaseCode: String,
        initialVersionId: String,
        viewpage: String,
        startDate: String,
        endDate: String,
    },
    components: {
        'lease-main-form-component': LeaseMainForm,
    },
    data() {
        return {
            variable_outgoings_label: 'Variable Outgoings',
            trust_account_label: '',
            multi_charge_id: 0,
            invoice_date: '', //this.endDate,
            charge_template: {},
            charge_editor_path: {},
            charge_editor: {},
            with_back_charge: 0,
            generate_interim: 0,
            send_on_approval: 0,
            due_date: '',
            total_increase_cpi: '',
            cpi_ages: 12,
            cpi_manual: false,
            cpi_index_available: { fieldKey: '', field_key: '', field_value: '', fieldValue: '' },
            cpi_index_old: '',
            cpi_index_current: '',
            cpi_index_increase: '',
            cpi_index_name: { fieldKey: '', field_key: '', field_value: '', fieldValue: '' },
            lease_rent_list: {},
            charge_template_pdf: {},
            headersDesc: [
                { text: 'From', value: 'unit_charge_item_start_date', width: '24%' },
                { text: 'To', value: 'unit_charge_item_end_date', width: '24%' },
                { text: 'Amount Per Year', value: 'unit_charge_item_amount', width: '24%' },
                { text: 'Parking Bays', value: 'unit_charge_item_parking', width: '25%' },
            ],
            headers: [
                { text: 'Property', value: 'prop_code', sortable: false, width: '15%' },
                { text: 'Lease', value: 'lease_code', width: '15%' },
                { text: 'Type', value: 'rent_type', width: '10%' },
                { text: 'Date', value: 'rent_date', width: '10%' },
                { text: 'Old Amount', value: 'old_amount', width: '10%' },
                { text: 'New Amount', value: 'new_amount', width: '10%' },
                { text: 'Back Charge Amount', value: 'back_charge_amount', width: '20%' },
                { text: '', value: 'filename_link', width: '10%' },
            ],
            asset_domain: this.$assetDomain,
            today: '',
            success_notification: 0,
            process_notification: 0,
            approve_notification: 0,
            rejected_notification: 0,
            email_tenant_notification: { withEmail: '', withoutEmail: '' },
            lease_charge_notification: 0,
            email_notification: 0,
            error_main_form: [],
            error_message: '',
            download_file: '',
            download_pdf: '',
            rent_review_account: { fieldKey: '', field_key: '', field_value: '', fieldValue: '' },
            rent_review_description: '',
            rent_review_amount: '',
            unit_description: '',
            unit_id: '',
            unit_area: '',
            start_date: this.startDate,
            end_date: this.endDate,
            next_billing_date: '',

            email_to_tenant: 0,
            filename_link: '',
            email_to_me: 0,
            filename: '',
            letterTemplate: '',
            letterTemplateBody: '',
            include_letterhead: 0,
            letter_template: '',
            letter_template_list: [],
            lease_charge_chosen: [],
            close_checkbox_diary: [],

            rent_review_charge_id: '',
            rent_review_diaries: false,
            rent_review_date: '',
            rent_review_index: '',
            futureRentReviewValidation: '',
            cpi_index_list: [],
            applicable_cpi_index_list: [],
            applicable_cpi_index_copy_list: [],
            expense_account: [],
            has_rent_review_fee: 0,
            show_rent_review_fee: false,
            selectedLeaseCharge: [],
            availableLeaseCharge: [],
            formData: {
                currentDB: localStorage.getItem('currentDB'),
                user_type: localStorage.getItem('user_type'),
                un: localStorage.getItem('un'),
            },

            template_tab: null,
            back_charge: true,
            rent_modal: false,
            preview_modal: false,
            review_list: [],
            review_list_headers: [
                { text: 'Start Date', value: 'start_date' },
                { text: 'End Date', value: 'end_date' },
                { text: 'Total Leases', value: 'total_leases' },
                { text: 'Created', value: 'created' },
                { text: 'Rent Review Type', value: 'rent_type' },
                { text: 'Author', value: 'created_by' },
                { text: 'Status', value: 'status' },
                { text: 'View', value: 'charge_id' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 20,
            search_datatable: '',
            search_table: '',
            to_date_filter: '31/12/2999',
            from_date_filter: this.startDate,
            status_filter: {
                field_key: localStorage.getItem('user_type') == 'A' ? 0 : 'All',
                field_value: localStorage.getItem('user_type') == 'A' ? 'Unprocessed' : 'All',
            },
            status_list: [],

            lease_list: [],
            lease_list2: [],
            rent_review_list: [
                { field_key: 'Fixed', field_value: 'Fixed', fieldKey: 'Fixed', fieldValue: 'Fixed' },
                { field_key: 'CPI', field_value: 'CPI', fieldKey: 'CPI', fieldValue: 'CPI' },
            ],
            rent_review_type: { fieldKey: '', field_key: '', field_value: '', fieldValue: '' },

            error_msg: [],
            search_type: 0,
            editor_source: '',
            source_code: undefined,
            read_only: this.initialReadOnly,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            loading_content_setting: false,
            loading_page_setting: false,
            lease_page_title: 'Quick Rent Review',
            error_server_msg: {},
            error_server_msg2: [],
            edit_form: true,
            disable_form: false,
            view_list: this.viewpage,
            edit_charge_id: '',
            edit_charge_status: '',
            rejected_comment: '',
            u_type: localStorage.getItem('user_type'),
            calculateCount: 0,
            loadPage: 0,
            direct_process: 0,
        };
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'lease_profile']),
    },
    mounted() {
        this.loadCountryDefaults();
        this.loadDefaultDropdown();
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;

                this.variable_outgoings_label = this.country_defaults.variable_outgoings;
            });
        },
        customSort: function (items, index, isDesc) {
            items.sort((a, b) => {
                if (
                    index[0] == 'unit_charge_item_start_date' ||
                    index[0] == 'unit_charge_item_end_date' ||
                    index[0] == 'start_date' ||
                    index[0] == 'end_date' ||
                    index[0] == 'created'
                ) {
                    let dataA = a[index].split('/');
                    let dataB = b[index].split('/');

                    if (isDesc[0]) {
                        return (
                            new Date(dataB[2] + '-' + dataB[1] + '-' + dataB[0]) -
                            new Date(dataA[2] + '-' + dataA[1] + '-' + dataA[0])
                        );
                    } else {
                        return (
                            new Date(dataA[2] + '-' + dataA[1] + '-' + dataA[0]) -
                            new Date(dataB[2] + '-' + dataB[1] + '-' + dataB[0])
                        );
                    }
                } else if (index[0] == 'total_leases') {
                    if (typeof a[index] !== 'undefined' && a[index]) {
                        if (!isDesc[0]) {
                            return a[index[0]] < b[index[0]] ? -1 : 1;
                        } else {
                            return b[index[0]] < a[index[0]] ? -1 : 1;
                        }
                    }
                } else {
                    if (typeof a[index] !== 'undefined' && a[index]) {
                        if (!isDesc[0]) {
                            return a[index].toLowerCase().localeCompare(b[index].toLowerCase());
                        } else {
                            return b[index].toLowerCase().localeCompare(a[index].toLowerCase());
                        }
                    }
                }
            });
            return items;
        },
        checkReview: function (e, key) {
            let dis = this;
            $.each(this.lease_rent_list[key], function (i, val) {
                dis.lease_rent_list[key][i].include = e;
            });
        },
        checkAllReview: function (e) {
            let dis = this;
            $.each(this.lease_rent_list, function (key, val) {
                $.each(dis.lease_rent_list[key], function (i, value) {
                    dis.lease_rent_list[key][i].include = e;
                });
            });

            $('.check_review').children().prop('checked', e);
        },
        loadDefaultDropdown: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            let form_data = new FormData();
            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadDefaultDropdown', form_data)
                .then((response) => {
                    this.trust_account_label = response.data.trust_account_label;
                    this.loadPage = 1;
                    this.direct_process = response.data.direct_process;
                    if (this.direct_process == 1) this.view_list = 0;
                    this.cpi_index_list = response.data.cpi_index_list;
                    this.status_list = response.data.status_list;
                    this.applicable_cpi_index_copy_list = response.data.applicable_cpi_index_list;
                    this.letter_template_list = response.data.letter_template_list;
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    let container = document.getElementById('main_container');
                    container.scrollTop = 0;
                });
        },

        loadListCharge: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.from_date = this.from_date_filter;
            this.formData.to_date = this.to_date_filter;
            this.formData.status =
                typeof this.status_filter.fieldKey != 'undefined'
                    ? this.status_filter.fieldKey
                    : localStorage.getItem('user_type') == 'A'
                      ? 0
                      : 'All';

            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadMultiListChargeReview', this.formData)
                .then((response) => {
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    let container = document.getElementById('main_container');
                    container.scrollTop = 0;
                    this.review_list = response.data.review_list;
                });
        },

        selectCharge: function () {
            this.clearLeaseList();
            this.success_notification = 0;
            this.process_notification = 0;
            this.download_pdf = '';
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.start_date = this.start_date;
            this.formData.end_date = this.end_date;
            this.formData.rent_type = this.rent_review_type;
            this.formData.cpi_index_name = this.cpi_index_name;
            this.formData.cpi_index_available = this.cpi_index_available;
            this.formData.cpi_index_old = this.cpi_index_old;
            this.formData.cpi_index_current = this.cpi_index_current;

            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadMultipleLeasesForCharge', this.formData)
                .then((response) => {
                    if (response.data.data.length > 1) this.lease_list2 = [{ fieldGroupValues: response.data.data }];
                    else this.lease_list2 = [];
                    if (typeof this.$refs.refLeasesChargeModel != 'undefined') {
                        this.$refs.refLeasesChargeModel.option1 = [{ fieldGroupValues: response.data.data }];
                        this.$refs.refLeasesChargeModel.option2 = [];
                        this.$refs.refLeasesChargeModel.finalValue = [];
                    }

                    this.error_main_form = response.data.error_msg;

                    document.getElementById('ngLoader-UI').style.display = 'none';
                    let container = document.getElementById('main_container');
                    container.scrollTop = 0;
                });
        },
        selectMultipleLease: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.selectedLeaseCharge = this.selectedLeaseCharge;
            this.formData.start_date = this.start_date;
            this.formData.end_date = this.end_date;
            this.formData.rent_type = this.rent_review_type;
            this.formData.total_increase_cpi = this.total_increase_cpi;
            this.formData.cpi_index_name = this.cpi_index_name;
            this.formData.cpi_index_available = this.cpi_index_available;
            this.formData.cpi_index_old = this.cpi_index_old;
            this.formData.cpi_index_current = this.cpi_index_current;
            this.send_on_approval = 0;

            this.formData.letter_template =
                this.letter_template && typeof this.letter_template.fieldKey != 'undefined'
                    ? this.letter_template.fieldKey
                    : '';
            this.formData.include_letterhead = this.include_letterhead;
            this.formData.email_to_me = this.email_to_me;
            this.formData.email_to_tenant = this.email_to_tenant;

            this.formData.cpi_index_increase = this.cpi_index_increase;
            this.formData.cpi_ages = this.cpi_ages;

            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadSelectedLeasesForCharge', this.formData)
                .then((response) => {
                    this.lease_rent_list = response.data.data;
                    this.charge_template_pdf = response.data.charge_template;
                    this.charge_editor = response.data.charge_editor;
                    this.charge_editor_path = response.data.charge_editor_path;

                    if (response.data.with_back_charge) this.send_on_approval = response.data.send_on_approval;
                    let dis = this;
                    if (Object.keys(this.charge_editor).length && !response.data.letter_filename) {
                        let countEditor = 0;
                        $.each(this.charge_editor, function (i, editor) {
                            $.each(editor, function (i2, dat) {
                                dis.formData.editorData = response.data.charge_editor[i][i2];
                                dis.formData.filePath = response.data.charge_editor_path[i][i2];
                                dis.formData.forSaving = 1;

                                let url =
                                    dis.asset_domain +
                                    '?action=downloadChargeReview&module=ar&command=generateTaxInvoice';
                                axios
                                    .post(url, dis.formData, { headers: { 'Content-Type': 'application/json' } })
                                    .then((response) => {
                                        countEditor = countEditor + 1;
                                        if (countEditor == Object.keys(dis.charge_editor).length)
                                            document.getElementById('ngLoader-UI').style.display = 'none';
                                    });
                            });
                        });
                    } else {
                        document.getElementById('ngLoader-UI').style.display = 'none';
                    }

                    this.error_main_form = response.data.error_msg;
                    if (response.data.with_back_charge) this.with_back_charge = 1;
                    else this.with_back_charge = 0;

                    this.generate_interim = 0;
                    this.due_date = '';
                    this.invoice_date = response.data.invoice_date;
                });
        },

        submitLeases: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.selectedLeaseCharge = this.selectedLeaseCharge;
            this.formData.start_date = this.start_date;
            this.formData.end_date = this.end_date;
            //   this.formData.charge_template = this.charge_template
            this.formData.rent_type = this.rent_review_type;
            this.formData.total_increase_cpi = this.total_increase_cpi;
            this.formData.lease_rent_list = this.lease_rent_list;
            this.formData.letter_template =
                this.letter_template && typeof this.letter_template.fieldKey != 'undefined'
                    ? this.letter_template.fieldKey
                    : '';
            this.formData.include_letterhead = this.include_letterhead;
            this.formData.cpi_index_name = this.cpi_index_name;
            this.formData.cpi_index_available = this.cpi_index_available;
            this.formData.cpi_index_old = this.cpi_index_old;
            this.formData.cpi_index_current = this.cpi_index_current;
            this.formData.cpi_index_increase = this.cpi_index_increase;
            this.formData.cpi_ages = this.cpi_ages;
            this.formData.cpi_manual = this.cpi_manual;
            this.formData.email_to_me = this.email_to_me;
            this.formData.email_to_tenant = this.email_to_tenant;

            this.formData.invoice_date = this.invoice_date;
            this.formData.generate_interim = this.generate_interim;
            this.formData.due_date = this.due_date;
            this.formData.send_on_approval = this.send_on_approval;

            let dis = this;
            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/fetch/submitSelectedLeasesForCharge', this.formData)
                .then((response) => {
                    this.error_message = response.data.error_msg;
                    if (!this.error_message) {
                        if (dis.direct_process == 1) {
                            if (response.data.email_tenant == 1 || response.data.email_me == 1) {
                                let url =
                                    dis.$assetDomain +
                                    '?action=sendMultipleLeasesChargereview&directProcess=1&module=leases&command=chargeReview_v2&chargeId=' +
                                    response.data.trans_id;
                                this.$api.post(url, this.formData).then((response2) => {
                                    dis.email_notification = response.data.email_tenant
                                        ? response.data.email_tenant
                                        : 0;

                                    if (dis.with_back_charge) {
                                        let url =
                                            dis.$assetDomain +
                                            '?action=generateMultiTempChargeReview&directProcess=1&module=ar&command=generateTaxInvoice&chargeID=' +
                                            response.data.trans_id +
                                            '&generateInvoice=' +
                                            dis.generate_interim +
                                            '&sendOnApproval=' +
                                            dis.send_on_approval;
                                        this.$api.get(url).then((response) => {
                                            if (typeof this.letter_template.fieldKey != 'undefined')
                                                this.letter_template = '';
                                            if (response.data.length) this.download_pdf = response.data;
                                            document.getElementById('ngLoader-UI').style.display = 'none';
                                            let container = document.getElementById('main_container');
                                            container.scrollTop = 0;
                                        });
                                    } else {
                                        document.getElementById('ngLoader-UI').style.display = 'none';
                                        let container = document.getElementById('main_container');
                                        container.scrollTop = 0;
                                    }
                                });
                            } else {
                                if (dis.with_back_charge) {
                                    let url =
                                        dis.$assetDomain +
                                        '?action=generateMultiTempChargeReview&directProcess=1&module=ar&command=generateTaxInvoice&chargeID=' +
                                        response.data.trans_id +
                                        '&generateInvoice=' +
                                        dis.generate_interim +
                                        '&sendOnApproval=' +
                                        dis.send_on_approval;
                                    this.$api.get(url).then((response) => {
                                        if (typeof this.letter_template.fieldKey != 'undefined')
                                            this.letter_template = '';
                                        if (response.data.length) this.download_pdf = response.data;
                                        document.getElementById('ngLoader-UI').style.display = 'none';
                                        let container = document.getElementById('main_container');
                                        container.scrollTop = 0;
                                    });
                                } else {
                                    document.getElementById('ngLoader-UI').style.display = 'none';
                                    let container = document.getElementById('main_container');
                                    container.scrollTop = 0;
                                }
                            }

                            this.process_notification = 1;
                            this.clearLeaseList();
                            dis.email_tenant_notification = response.data.email_checker;
                        } else {
                            if (dis.generate_interim) {
                                let url =
                                    dis.$assetDomain +
                                    '?action=generateMultiTempChargeReview&generateInvoice=1&module=ar&command=generateTaxInvoice&chargeID=' +
                                    response.data.trans_id +
                                    '&sendOnApproval=' +
                                    dis.send_on_approval;
                                this.$api.get(url).then((response) => {
                                    if (typeof this.letter_template.fieldKey != 'undefined') this.letter_template = '';
                                    if (response.data.length) this.download_pdf = response.data;
                                    document.getElementById('ngLoader-UI').style.display = 'none';
                                    let container = document.getElementById('main_container');
                                    container.scrollTop = 0;
                                });
                            } else {
                                document.getElementById('ngLoader-UI').style.display = 'none';
                                let container = document.getElementById('main_container');
                                container.scrollTop = 0;
                            }

                            this.success_notification = 1;
                            this.clearLeaseList();
                        }
                    } else {
                        document.getElementById('ngLoader-UI').style.display = 'none';
                        let container = document.getElementById('main_container');
                        container.scrollTop = 0;
                    }
                });
        },

        selectCPI: function () {
            this.cpi_index_available = { fieldKey: '', field_key: '', field_value: '', fieldValue: '' };
            this.cpi_index_old = '';
            this.cpi_index_current = '';
            this.cpi_index_increase = '';
            let index = this.cpi_index_name.field_key;
            this.applicable_cpi_index_list = this.applicable_cpi_index_copy_list[index];
        },
        selectApplicable: function (index_charge) {
            let index = this.cpi_index_available.field_key;
            this.cpi_index_current = this.cpi_index_available.cpi;
            this.cpi_index_old = '';

            var ages = parseInt(this.cpi_ages) / 3;
            var ages_check = -1;

            for (let x = 0; x < this.applicable_cpi_index_list.length; x++) {
                if (
                    this.applicable_cpi_index_list[x].quarter == this.cpi_index_available.quarter &&
                    parseFloat(this.applicable_cpi_index_list[x].year) == parseFloat(this.cpi_index_available.year)
                ) {
                    ages_check = 0;
                }

                if (ages_check >= 0) {
                    if (ages_check == ages) {
                        this.cpi_index_old = this.applicable_cpi_index_list[x].cpi;
                    }

                    ages_check += 1;
                }
            }

            if (this.cpi_index_old > 0) {
                this.total_increase_cpi = (
                    (parseFloat(this.cpi_index_current) / parseFloat(this.cpi_index_old)) * 100 -
                    100
                ).toFixed(2);
                this.total_cpi = (
                    parseFloat(this.total_increase_cpi) +
                    parseFloat(this.cpi_index_increase ? this.cpi_index_increase : 0)
                ).toFixed(2);
            } else {
                this.total_cpi = '';
                this.total_increase_cpi = '';
            }
        },
        reComputeCPI: function (index_charge) {
            if (this.cpi_index_old > 0) {
                this.total_increase_cpi = (
                    (parseFloat(this.cpi_index_current) / parseFloat(this.cpi_index_old)) * 100 -
                    100
                ).toFixed(2);
                this.total_cpi = (
                    parseFloat(this.total_increase_cpi) +
                    parseFloat(this.cpi_index_increase ? this.cpi_index_increase : 0)
                ).toFixed(2);
            } else {
                this.total_cpi = '';
                this.total_increase_cpi = '';
            }
        },

        clearLeaseList: function () {
            this.lease_list2 = [];
            if (typeof this.$refs.refLeasesChargeModel != 'undefined') {
                this.$refs.refLeasesChargeModel.option1 = [{ fieldGroupValues: [] }];
                this.$refs.refLeasesChargeModel.option2 = [];
                this.$refs.refLeasesChargeModel.finalValue = [];
            }

            this.lease_rent_list = {};
        },

        loadSelectedCharges: function (charge_id) {
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.charge_id = charge_id;
            this.edit_charge_id = charge_id;
            this.disable_form = true;

            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadSelectedMultipleChargeDetails', this.formData)
                .then((response) => {
                    this.send_on_approval = response.data.review_list[0].sendOnApproval;
                    this.start_date = response.data.review_list[0].start_date;
                    this.end_date = response.data.review_list[0].end_date;
                    this.rent_review_type.field_key = response.data.review_list[0].rent_type;
                    this.cpi_index_name.field_key = response.data.review_list[0].cpi_index_name;
                    this.edit_charge_status = response.data.review_list[0].status;
                    this.rejected_comment = response.data.review_list[0].comment;
                    this.cpi_manual = response.data.review_list[0].cpi_manual == '1' ? true : false;
                    this.selectCPI();

                    let dis = this;
                    $.each(this.cpi_index_list, function (i, e) {
                        if (e['field_key'] == dis.cpi_index_name.field_key)
                            dis.cpi_index_name.field_value = e['field_value'];
                    });

                    this.cpi_index_available.field_key = response.data.review_list[0].cpi_index_available;

                    $.each(this.rent_review_list, function (i, e) {
                        if (e['field_key'] == dis.rent_review_type.field_key)
                            dis.rent_review_type.field_value = e['field_value'];
                    });

                    $.each(this.applicable_cpi_index_list, function (i, e) {
                        if (e['field_key'] == dis.cpi_index_available.field_key)
                            dis.cpi_index_available.field_value = e['field_value'];
                    });

                    this.cpi_index_old = response.data.review_list[0].cpi_index_old;
                    this.cpi_index_current = response.data.review_list[0].cpi_index_current;

                    setTimeout(function () {
                        dis.lease_rent_list = response.data.data;
                        dis.charge_template_pdf = response.data.charge_template;
                        //     alert(  dis.charge_template_pdf);console.log(  dis.charge_template_pdf);
                    }, 500);

                    document.getElementById('ngLoader-UI').style.display = 'none';
                    let container = document.getElementById('main_container');
                    container.scrollTop = 0;
                });
        },
        approveFile: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.rejected_comment = this.rejected_comment;
            this.formData.group_charge_id = this.edit_charge_id;

            let dis = this;
            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/update/approveMultiLeasesRentReview', this.formData)
                .then((response) => {
                    this.error_main_form = response.data.error_msg;
                    if (!this.error_main_form.length) {
                        if (response.data.email_tenant == 1 || response.data.email_me == 1) {
                            let url =
                                dis.$assetDomain +
                                '?action=sendMultipleLeasesChargereview&module=leases&command=chargeReview_v2&chargeId=' +
                                dis.edit_charge_id;
                            this.$api.post(url, this.formData).then((response2) => {
                                dis.approve_notification = 1;
                                dis.email_notification = response.data.email_tenant ? response.data.email_tenant : 0;
                                dis.view_list = 1;
                                dis.edit_charge_id = '';
                                document.getElementById('ngLoader-UI').style.display = 'none';
                                dis.loadListCharge();
                            });
                        } else {
                            dis.approve_notification = 1;
                            dis.view_list = 1;
                            dis.edit_charge_id = '';
                            document.getElementById('ngLoader-UI').style.display = 'none';
                            dis.loadListCharge();
                        }
                    } else {
                        document.getElementById('ngLoader-UI').style.display = 'none';
                    }

                    let container = document.getElementById('main_container');
                    container.scrollTop = 0;

                    this.email_tenant_notification = response.data.email_checker;
                });
        },
        rejectFile: function () {
            if (this.rejected_comment) {
                document.getElementById('ngLoader-UI').style.display = 'block';
                let dis = this;
                let url =
                    this.$assetDomain +
                    '?action=rejectMultiple&module=leases&command=chargeReviewProcess&chargeReviewID=' +
                    dis.edit_charge_id +
                    '&rejectComment=' +
                    dis.rejected_comment;
                this.$api.post(url, this.formData).then((response) => {
                    dis.rejected_notification = 1;
                    dis.view_list = 1;
                    dis.edit_charge_id = '';
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    dis.loadListCharge();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'You need to enter your feedback.',
                    html: '',
                });
            }
        },
    },
    watch: {
        rent_review_type: function () {
            this.clearLeaseList();
        },
        start_date: function () {
            this.clearLeaseList();
        },
        end_date: function () {
            this.clearLeaseList();
        },
    },
};
</script>
