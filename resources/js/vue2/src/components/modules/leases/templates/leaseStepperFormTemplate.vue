<style>
.shortcut-div > .v-toolbar__content {
    height: 30px;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <div>
            <div>
                <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
                <v-stepper v-model="step">
                    <v-stepper-header>
                        <v-stepper-step
                            :complete="step > 1"
                            step="1"
                            >Step 1
                        </v-stepper-step>

                        <v-divider></v-divider>

                        <v-stepper-step
                            :complete="step > 2"
                            step="2"
                            >Step 2
                        </v-stepper-step>

                        <v-divider></v-divider>

                        <v-stepper-step
                            :complete="step > 3"
                            step="3"
                            >Step 3
                        </v-stepper-step>

                        <v-divider></v-divider>

                        <v-stepper-step step="4">Submitting</v-stepper-step>
                    </v-stepper-header>

                    <v-stepper-items>
                        <v-stepper-content step="1">
                            <v-alert
                                type="info"
                                dense
                                dark
                                color="primary"
                            >
                                <span>
                                    A lease (draft) will be automatically created after this step.
                                    <br />
                                    <strong>Lease Name</strong> is the name chosen to identify the lease, and usually
                                    corresponds to the tenant name.<br />
                                    <strong>Tenancy location</strong> describes the physical location (unit / building)
                                    of the lease.
                                </span>
                            </v-alert>
                            <lease-main-form-component
                                ref="main_form_new_lease"
                                save_button_label="Continue to step 2"
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                v-on:returnLeaseIsExisted="getLeaseCode"
                                v-on:returnLeaseFormSuccess="nextStepper"
                                :new_lease="new_lease_flag"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :edit_flag="true"
                            ></lease-main-form-component>
                        </v-stepper-content>

                        <v-stepper-content step="2">
                            <cirrus-server-error
                                :error_msg="error_server_msg"
                                :errorMsg2="error_server_msg2"
                            ></cirrus-server-error>
                            <lease-unit-details-component
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :lease_commencement_date="lease_commencement_date"
                                :version_id="version_id"
                                :read_only="read_only"
                                :show_unit_history="false"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-unit-details-component>
                            <lease-man-fees-component
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-man-fees-component>
                            <lease-rent-review-component
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :read_only="read_only"
                                :new_lease="new_lease_flag"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :viewRecommendation="false"
                                :edit_flag="true"
                            ></lease-rent-review-component>
                            <!--              <lease-unit-history-component id="leaseUnitHistorySection"-->
                            <!--                                            :property_code='property_code' :lease_code='lease_code'-->
                            <!--                                            :version_id='version_id'-->
                            <!--                                            :page_form_type="page_form_type" :country_default_settings="country_default_settings"></lease-unit-history-component>-->

                            <br />
                            <br />
                            <v-btn
                                color="success"
                                dark
                                small
                                tile
                                @click="changeStep(1)"
                                >Back
                            </v-btn>
                            <v-btn
                                absolute
                                right
                                color="success"
                                dark
                                small
                                tile
                                @click="changeStep(3)"
                            >
                                Continue to step 3
                            </v-btn>
                            <br />
                            <br />
                        </v-stepper-content>

                        <v-stepper-content step="3">
                            <lease-contacts-component
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-contacts-component>
                            <lease-diary-component
                                id="leaseDiarySection"
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :current_db="current_db"
                                :user_type="user_type"
                                :username="username"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-diary-component>
                            <lease-notes-component
                                id="leaseNotesSection"
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :current_db="current_db"
                                :user_type="user_type"
                                :username="username"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-notes-component>
                            <lease-documents-v2-component
                                v-if="doc_active_version === 1"
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :current_db="current_db"
                                :user_type="user_type"
                                :username="username"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-documents-v2-component>
                            <lease-documents-component
                                v-else
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :current_db="current_db"
                                :user_type="user_type"
                                :username="username"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-documents-component>
                            <lease-inspection-component
                                id="leaseInspectionSection"
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :current_db="current_db"
                                :user_type="user_type"
                                :username="username"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-inspection-component>
                            <lease-insurance-component
                                id="leaseInsuranceSection"
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :current_db="current_db"
                                :user_type="user_type"
                                :username="username"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-insurance-component>
                            <lease-guarantee-component
                                id="leaseGuaranteeSection"
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :version_id="version_id"
                                :current_db="current_db"
                                :user_type="user_type"
                                :username="username"
                                :read_only="read_only"
                                :page_form_type="page_form_type"
                                :country_default_settings="country_default_settings"
                                :new_lease="new_lease_flag"
                                :edit_flag="true"
                            ></lease-guarantee-component>

                            <br />
                            <br />
                            <v-btn
                                absolute
                                right
                                color="success"
                                dark
                                small
                                tile
                                @click="changeStep(4)"
                            >
                                Complete form
                            </v-btn>

                            <v-btn
                                color="success"
                                dark
                                small
                                tile
                                @click="changeStep(2)"
                                >Back
                            </v-btn>

                            <br />
                            <br />
                        </v-stepper-content>
                        <v-stepper-content step="4">
                            <v-alert
                                type="info"
                                dense
                                dark
                                color="primary"
                            >
                                <v-row align="center">
                                    <v-col class="grow">
                                        <span>
                                            Your lease form has been saved as draft.<br />
                                            Select <strong v-if="process_type === 0">'PROCESS'</strong
                                            ><strong v-if="process_type === 1">'RE-ASSIGN'</strong> for processing or
                                            <strong>'NO, KEEP AS DRAFT'</strong> to retain the form in draft format.<br />
                                        </span>
                                    </v-col>
                                    <v-col class="shrink">
                                        <v-btn
                                            color="primary"
                                            small
                                            right
                                            @click="printLease()"
                                        >
                                            <v-icon>print</v-icon>
                                            PRINT
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </v-alert>
                            <v-container fluid>
                                <v-row>
                                    <v-col
                                        cols="12"
                                        md="6"
                                    >
                                        <cirrus-content-loader
                                            v-if="false"
                                            type="article"
                                        ></cirrus-content-loader>
                                        <v-card
                                            class="mx-auto"
                                            color="#1F7087"
                                            shaped
                                            dark
                                        >
                                            <v-card-text>
                                                <div class="overline mb-4">
                                                    Property Code:
                                                    <span class="v-step-property-code"
                                                        >{{ property_code }} - {{ lease_code }}</span
                                                    >
                                                </div>
                                                <table class="data-grid data-grid-dense tableHive">
                                                    <tr>
                                                        <td
                                                            class="title"
                                                            align="right"
                                                            style="color: white"
                                                        >
                                                            Submit to:
                                                        </td>
                                                        <td class="required"></td>
                                                        <td>
                                                            <v-btn-toggle
                                                                class="v-step-search-type form-toggle"
                                                                v-model="process_type"
                                                                mandatory
                                                            >
                                                                <v-btn
                                                                    small
                                                                    tile
                                                                    text
                                                                >
                                                                    {{ trust_accountant_label }}
                                                                </v-btn>
                                                                <v-btn
                                                                    small
                                                                    tile
                                                                    text
                                                                >
                                                                    {{ property_manager_label }}
                                                                </v-btn>
                                                            </v-btn-toggle>
                                                        </td>
                                                    </tr>
                                                    <tr v-if="process_type === 0">
                                                        <td
                                                            class="title"
                                                            align="right"
                                                            style="color: white"
                                                        >
                                                            {{ trust_accountant_label }}:
                                                        </td>
                                                        <td class="required">*</td>
                                                        <td>
                                                            <span style="background-color: white">
                                                                <multiselect
                                                                    v-model="complete_trust_accountant"
                                                                    :options="dd_param_trust_accountant_list"
                                                                    :allowEmpty="false"
                                                                    class="vue-select2 dropdown-left dropdown-400"
                                                                    :custom-label="nameWithDash"
                                                                    group-label="language"
                                                                    placeholder="Please select..."
                                                                    track-by="field_key"
                                                                    label="field_value"
                                                                    :show-labels="false"
                                                                    ><span slot="noResult"
                                                                        >Oops! No elements found. Consider changing the
                                                                        search query.</span
                                                                    ></multiselect
                                                                >
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr v-if="process_type === 1">
                                                        <td
                                                            class="title"
                                                            align="right"
                                                            style="color: white"
                                                        >
                                                            {{ property_manager_label }}:
                                                        </td>
                                                        <td class="required">*</td>
                                                        <td>
                                                            <div>
                                                                <multiselect
                                                                    v-model="complete_property_manager"
                                                                    :options="dd_param_property_manager_list"
                                                                    :allowEmpty="false"
                                                                    class="vue-select2 dropdown-left dropdown-400"
                                                                    :custom-label="nameWithDash"
                                                                    group-label="language"
                                                                    placeholder="Please select..."
                                                                    track-by="field_key"
                                                                    label="field_value"
                                                                    :show-labels="false"
                                                                    ><span slot="noResult"
                                                                        >Oops! No elements found. Consider changing the
                                                                        search query.</span
                                                                    >
                                                                </multiselect>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </v-card-text>

                                            <v-card-actions>
                                                <v-btn
                                                    small
                                                    color="primary"
                                                    class="v-step-modify-lease-code-btn"
                                                    @click="reloadPage"
                                                    >No, keep as draft
                                                </v-btn>
                                                <v-btn
                                                    small
                                                    color="primary"
                                                    class="v-step-modify-lease-code-btn"
                                                    v-if="process_type === 0"
                                                    @click="processNewLease()"
                                                    >Process
                                                </v-btn>
                                                <v-btn
                                                    small
                                                    color="primary"
                                                    class="v-step-modify-lease-code-btn"
                                                    v-if="process_type === 1"
                                                    @click="processNewLease()"
                                                    >Re-assign
                                                </v-btn>
                                            </v-card-actions>
                                        </v-card>
                                    </v-col>
                                    <v-col
                                        cols="12"
                                        md="6"
                                    >
                                        <v-textarea
                                            class="vuetifyText v-step-feedback"
                                            outlined
                                            label="If you have any final comments, add them here."
                                            v-model="final_comment"
                                        ></v-textarea>
                                    </v-col>
                                </v-row>
                            </v-container>

                            <v-btn
                                color="success"
                                dark
                                small
                                tile
                                @click="changeStep(3)"
                                >Back
                            </v-btn>
                        </v-stepper-content>
                    </v-stepper-items>
                </v-stepper>
            </div>
        </div>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import { mapActions, mapMutations, mapState } from 'vuex';
import LeaseMainForm from '../forms/leaseMainForm.vue';
import LeaseDiary from '../forms/leaseDiary.vue';
import LeaseNotes from '../forms/leaseNotes.vue';
import LeaseGuarantee from '../forms/leaseGuarantee.vue';
import LeaseInsurance from '../forms/leaseInsurance.vue';
import LeaseInspection from '../forms/leaseInspection.vue';
import LeaseRentReview from '../forms/LeaseRentReview.vue';
import LeaseUnitDetails from '../forms/leaseUnitDetails.vue';
import LeaseManagementFees from '../forms/LeaseManagementFees.vue';
import LeaseUnitHistory from '../forms/leaseUnitHistory.vue';
import LeaseBonds from '../forms/leaseBonds.vue';
import LeaseOutstandingAmounts from '../forms/leaseOutstandingAmounts.vue';
import LeaseContacts from '../forms/leaseContacts.vue';
import LeaseDocuments from '../forms/leaseDocuments.vue';
import LeaseDocumentsV2 from '../../../../DocumentDirectory/sections/LeaseDocumentFormV2.vue';
import LeaseEmail from '../forms/LeaseEmail.vue';
import LeaseSMS from '../forms/LeaseSMS.vue';
import LeaseProfile from '../forms/LeaseProfile.vue';
import { cirrusDialog } from '../../../../plugins/mixins';
// Vue.use(SuiVue);
export default {
    props: {
        initialReadOnly: Boolean,
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease_flag: { type: Boolean, default: false },
        force_load: { type: Number, default: 0 },
        page_form_type: { type: String, default: '' },
        country_default_settings: { type: String, default: '' },
    },
    components: {
        'lease-main-form-component': LeaseMainForm,
        'lease-diary-component': LeaseDiary,
        'lease-notes-component': LeaseNotes,
        'lease-guarantee-component': LeaseGuarantee,
        'lease-insurance-component': LeaseInsurance,
        'lease-inspection-component': LeaseInspection,
        'lease-rent-review-component': LeaseRentReview,
        'lease-unit-details-component': LeaseUnitDetails,
        'lease-unit-history-component': LeaseUnitHistory,
        'lease-man-fees-component': LeaseManagementFees,
        'lease-bonds-component': LeaseBonds,
        'lease-outstanding-amount-component': LeaseOutstandingAmounts,
        'lease-contacts-component': LeaseContacts,
        'lease-documents-component': LeaseDocuments,
        'lease-documents-v2-component': LeaseDocumentsV2,
        'lease-email-component': LeaseEmail,
        'lease-sms-component': LeaseSMS,
        'lease-profile-component': LeaseProfile,
    },
    data() {
        return {
            property_manager_label: 'Property Manager',
            trust_accountant_label: 'Trust Accountant',
            loading_page_setting: false,
            step: 1,
            show_step_2_button: false,
            show_step_3_button: false,
            lease_commencement_date: '31/12/2999',
            error_server_msg: {},
            error_server_msg2: [],
            process_type: 0,
            complete_trust_accountant: { field_key: '' },
            complete_trust_accountant_list: [],
            complete_property_manager: { field_key: '' },
            complete_property_manager_list: [],
            final_comment: '',
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'tour_steps',
            'lease_details',
            'lease_details_old',
            'dd_param_trust_accountant_list',
            'dd_param_property_manager_list',
            'dd_param_trust_accountant_default',
            'doc_active_version',
            'sys_ver_control_list',
            'auto_diarise',
        ]),
    },
    mounted() {
        let country_default_settings = JSON.parse(atob(this.country_default_settings));
        this.property_manager_label = this.ucwords(country_default_settings.property_manager);
        this.trust_accountant_label = this.ucwords(country_default_settings.trust_accountant);
        this.fetchFormVersionControl();
        this.fetchParamTAList();
        this.fetchParamPMList();
        // console.log(localStorage.getItem('cirrus8_api_url'));
    },
    methods: {
        ...mapActions([
            'fetchCountryList',
            'fetchAccountIncomeGroupedList',
            'fetchAccountExpenseGroupedList',
            'fetchBondPropertyList',
            'fetchParamDivisionList',
            'fetchParamLeaseTypeList',
            'fetchParamTenantTypeList',
            'fetchRetailCategoryList',
            'fetchRetailSubCategoryList',
            'fetchRetailFineCategoryList',
            'fetchTaxRateList',
            'fetchParamTAList',
            'fetchParamPMList',
            'fetchFormVersionControl',
        ]),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_LEASE_CODE', 'SET_TOUR_STEPS']),
        nameWithDash({ field_key, field_value }) {
            if (!field_value) {
                return 'Please select...';
            }
            return `${field_key} — ${field_value}`;
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        getLeaseCode: function (value) {
            let lease_code = value.lease_code;
            let lease_name = value.lease_name;
            this.lease_commencement_date = value.lease_commencement_date;
            this.show_step_2_button = true;
        },
        reloadPage() {
            window.location.reload();
        },
        nextStepper: function (value) {
            this.step = 2;
        },
        changeStep: function (step) {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            switch (step) {
                case 1:
                    if (this.error_server_msg2.length <= 0) {
                        bus.$emit('loadLeaseMainFormSection', '');
                        this.step = step;
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                    }

                    break;
                case 2:
                    if (this.error_server_msg2.length <= 0) {
                        bus.$emit('loadLeaseUnitDetailsSection', '');
                        // bus.$emit("loadLeaseRentReviewSection","");
                        bus.$emit('loadLeaseDirectManagementFeeSection', '');
                        this.step = step;
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                    }
                    break;
                case 3:
                    //lease_details
                    if (this.lease_details.unit_details.lease_status !== 'C') {
                        this.error_server_msg2.push(['The lease is vacated. Please assign a unit.']);
                        let dialog_prop = {
                            title: 'Warning',
                            message: 'There is no unit attached, Please assign a unit.',
                            icon_show: true,
                        };
                        cirrusDialog(dialog_prop);
                    }
                    if (typeof this.lease_details.unit_charge_details !== 'undefined') {
                        if (this.lease_details.unit_charge_details.lease_unit_charges_list.length <= 0) {
                            this.error_server_msg2.push(['Please add at least one lease charge.']);
                            let dialog_prop = {
                                title: 'Warning',
                                message: 'There is no lease charge. Please add at least one lease charge.',
                                icon_show: true,
                            };
                            cirrusDialog(dialog_prop);
                        }
                    }

                    if (this.error_server_msg2.length <= 0) {
                        bus.$emit('loadLeaseContactSection', '');
                        bus.$emit('loadLeaseDiarySection', '');
                        bus.$emit('loadLeaseNoteSection', '');
                        bus.$emit('loadLeaseDocumentSection', '');
                        bus.$emit('loadLeaseInspectionSection', '');
                        bus.$emit('loadLeaseInsuranceSection', '');
                        bus.$emit('loadLeaseGuaranteeSection', '');
                        this.step = step;
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                    }
                    break;
                case 4:
                    if (this.error_server_msg2.length <= 0) {
                        bus.$emit('loadLeaseUnitDetailsSection', '');
                        this.step = step;
                        this.error_server_msg = {};
                        this.error_server_msg2 = [];
                    }
                    break;
                default:
                    break;
            }
        },
        processNewLease: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            if (this.property_code === '') {
                this.error_server_msg2.push(['There is no property found']);
            }
            if (this.lease_code === '') {
                this.error_server_msg2.push(['There is no lease found']);
            }
            if (this.version_id === '') {
                this.error_server_msg2.push(['There is no version number found']);
            }

            if (this.process_type === 0) {
                if (this.complete_trust_accountant.field_key === '') {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Please select a ' + this.trust_accountant_label + '.',
                        icon_show: true,
                    };
                    cirrusDialog(dialog_prop);
                    this.error_server_msg2.push(['Please select a ' + this.trust_accountant_label + '.']);
                }
            } else {
                if (this.complete_property_manager.field_key === '') {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Please select a ' + this.property_manager_label + '.',
                        icon_show: true,
                    };
                    cirrusDialog(dialog_prop);
                    this.error_server_msg2.push(['Please select a ' + property_manager_label + '.']);
                }
            }
            if (this.error_server_msg2.length === 0) {
                let api_url = '';
                if (this.process_type === 0) {
                    api_url = this.cirrus8_api_url + 'api/lease/process/submit-new-lease-form';
                } else {
                    api_url = this.cirrus8_api_url + 'api/lease/process/reassign-new-lease-form';
                }
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('process_type', this.process_type);
                form_data.append('trust_accountant', this.complete_trust_accountant.parameter_description);
                form_data.append('property_manager', this.complete_property_manager.field_value);
                form_data.append('final_comment', this.final_comment);
                form_data.append('no_load', true);
                this.$api.post(api_url, form_data).then((response) => {
                    this.error_server_msg2 = response.data.validation_errors;
                    this.loading_page_setting = false;
                    this.form_no_btn = false;
                    window.open(
                        '?module=leases&command=lease_approval_page&property_code=' +
                            this.property_code +
                            '&lease_code=' +
                            this.lease_code +
                            '&version_id=' +
                            this.version_id,
                        '_parent', // <- This is what makes it open in a new window.
                    );
                });
            }
        },
        printLease: function () {
            window.open(
                '?module=leases&command=lease_print_page&property_code=' +
                    this.property_code +
                    '&lease_code=' +
                    this.lease_code +
                    '&version_id=' +
                    this.version_id,
                '_blank', // <- This is what makes it open in a new window.
            );
        },
    },
    watch: {
        dd_param_trust_accountant_default: function () {
            this.complete_trust_accountant = this.dd_param_trust_accountant_default;
        },
    },
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
