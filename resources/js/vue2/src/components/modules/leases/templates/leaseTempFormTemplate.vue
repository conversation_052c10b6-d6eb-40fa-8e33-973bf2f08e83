<style>
.shortcut-div > .v-toolbar__content {
    height: 30px;
}
</style>
<template>
    <div
        v-resize="onResize"
        class="c8-page"
    >
        <div class="page-form">
            <div class="form-row">
                <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
                <v-toolbar flat>
                    <v-toolbar-title>
                        <cirrus-page-header
                            :title="'New Lease - Original Form for ' + property_code + '(' + lease_code + ')'"
                        />
                    </v-toolbar-title>
                    <br />

                    <div class="flex-grow-1"></div>
                    <v-btn
                        color="primary"
                        tile
                        dark
                        small
                        right
                        @click="printLease()"
                    >
                        <v-icon>print</v-icon>
                        PRINT
                    </v-btn>
                </v-toolbar>
                <v-subheader v-if="process_state === '3'">
                    These details are as per the original new lease form and may have been updated since submission. Go
                    to &nbsp
                    <a
                        target="_blank"
                        :href="
                            '?module=leases&command=lease_summary_page&property_code=' +
                            property_code +
                            '&lease_code=' +
                            lease_code +
                            ''
                        "
                        >Lease Summary</a
                    >
                    &nbsp for up-to date information.
                </v-subheader>
                <!--                <v-card-->
                <!--                        color="grey lighten-4"-->
                <!--                        flat-->
                <!--                        tile-->
                <!--                        v-if="property_code!=='' && lease_code!=='' && form_mode===0"-->
                <!--                >-->
                <!--                    <v-tabs dark background-color="grey darken-1" centered show-arrows style="z-index: 9999 " class="v-step-shortcut">-->
                <!--                        <v-tabs-slider color="grey darken-1"></v-tabs-slider>-->
                <!--                        <v-tab @click="goToHref('tab-1','tenantDetailsSection')">Tenant Details</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-1','mailingDetailsSection')">Mailing Details</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-1','leaseDetailsSection')">Details</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-2','unitDetailsSection')">Unit Details</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-2','leaseUnitChargesSection')">Charges</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-2','leaseManagementFeesSection')">Man Fees</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-2','rentReviewsSection')">Rent Review</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-1','leaseDiarySection')">Diary</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-3','leaseNotesSection')">Notes</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-3','leaseInspectionSection')">Inspection</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-3','leaseInsuranceSection')">Insurance</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-3','leaseGuaranteeSection')">Guarantee</v-tab>-->
                <!--                        <v-tab @click="goToHref('tab-4','leaseCommunicationHistorySection')">Communication History</v-tab>-->
                <!--                    </v-tabs>-->
                <!--                </v-card>-->
                <v-tabs
                    icons-and-text
                    class="cirrus-tab-theme"
                    v-if="property_code !== '' && lease_code !== '' && form_mode === 0"
                    v-model="template_tab"
                    show-arrows
                >
                    <v-tabs-slider color="orange"></v-tabs-slider>

                    <v-tab
                        href="#tab-1"
                        v-if="responsive_show"
                        class="v-step-form-tab"
                    >
                        Overview
                        <v-icon
                            small
                            dense
                            >business
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-2"
                        v-if="responsive_show"
                    >
                        Charges and Rent Reviews
                        <v-icon
                            small
                            dense
                            >monetization_on
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-3"
                        v-if="responsive_show"
                    >
                        Diary
                        <v-icon
                            small
                            dense
                            >date_range
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-4"
                        v-if="responsive_show"
                    >
                        Notes and Additional Details
                        <v-icon
                            small
                            dense
                            >notes
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-5"
                        v-if="responsive_show"
                    >
                        Communication and Documents
                        <v-icon
                            small
                            dense
                            >attachment
                        </v-icon>
                    </v-tab>

                    <v-tab
                        href="#tab-1"
                        class="primary--text v-step-form-tab"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >business
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-2"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >monetization_on
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-3"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >date_range
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-4"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >notes
                        </v-icon>
                    </v-tab>
                    <v-tab
                        href="#tab-5"
                        class="primary--text"
                        v-if="!responsive_show"
                    >
                        <v-icon
                            small
                            dense
                            >attachment
                        </v-icon>
                    </v-tab>
                    <v-tab-item :value="'tab-1'">
                        <lease-main-form-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-main-form-component>
                        <br />
                        <br />
                        <lease-contacts-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-contacts-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-2'">
                        <lease-unit-details-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                            :show_unit_history="false"
                        ></lease-unit-details-component>
                        <lease-man-fees-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-man-fees-component>
                        <lease-rent-review-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                            :viewRecommendation="false"
                        ></lease-rent-review-component>
                        <lease-unit-history-component
                            id="leaseUnitHistorySection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-unit-history-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-3'">
                        <lease-diary-component
                            id="leaseDiarySection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-diary-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-4'">
                        <lease-notes-component
                            id="leaseNotesSection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :current_db="current_db"
                            :user_type="user_type"
                            :username="username"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-notes-component>
                        <lease-inspection-component
                            id="leaseInspectionSection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :current_db="current_db"
                            :user_type="user_type"
                            :username="username"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-inspection-component>
                        <lease-insurance-component
                            id="leaseInsuranceSection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :current_db="current_db"
                            :user_type="user_type"
                            :username="username"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-insurance-component>
                        <lease-guarantee-component
                            id="leaseGuaranteeSection"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :current_db="current_db"
                            :user_type="user_type"
                            :username="username"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-guarantee-component>
                    </v-tab-item>
                    <v-tab-item :value="'tab-5'">
                        <lease-documents-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-documents-component>
                        <v-card
                            class="section-toolbar"
                            dark
                            color="titleHeader"
                            text
                            tile
                        >
                            <v-card-actions
                                ><h6 class="title font-weight-black">Communication History</h6></v-card-actions
                            >
                        </v-card>
                        <lease-email-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-email-component>
                        <lease-sms-component
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :version_id="version_id"
                            :read_only="read_only"
                            :page_form_type="page_form_type"
                            :country_default_settings="initialCountryDefaultSettings"
                        ></lease-sms-component>
                    </v-tab-item>
                </v-tabs>

                <div v-if="property_code !== '' && new_lease_code !== '' && lease_existed === false && form_mode === 1">
                    <lease-main-form-component
                        v-on:returnLeaseCode="getLeaseCode"
                        :property_code="property_code"
                        :lease_code="new_lease_code"
                        :version_id="version_id"
                        ref="main_form_new_lease"
                        :new_lease="true"
                        :page_form_type="page_form_type"
                        :country_default_settings="initialCountryDefaultSettings"
                    ></lease-main-form-component>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import { mapActions, mapMutations, mapState } from 'vuex';
import LeaseMainForm from '../forms/leaseMainForm.vue';
import LeaseDiary from '../forms/leaseDiary.vue';
import LeaseNotes from '../forms/leaseNotes.vue';
import LeaseGuarantee from '../forms/leaseGuarantee.vue';
import LeaseInsurance from '../forms/leaseInsurance.vue';
import LeaseInspection from '../forms/leaseInspection.vue';
import LeaseRentReview from '../forms/LeaseRentReview.vue';
import LeaseUnitDetails from '../forms/leaseUnitDetails.vue';
import LeaseManagementFees from '../forms/LeaseManagementFees.vue';
import LeaseUnitHistory from '../forms/leaseUnitHistory.vue';
import LeaseBonds from '../forms/leaseBonds.vue';
import LeaseOutstandingAmounts from '../forms/leaseOutstandingAmounts.vue';
import LeaseContacts from '../forms/leaseContacts.vue';
import LeaseDocuments from '../../../../DocumentDirectory/sections/LeaseDocumentFormV2.vue';
import LeaseEmail from '../forms/LeaseEmail.vue';
import LeaseSMS from '../forms/LeaseSMS.vue';
import LeaseProfile from '../forms/LeaseProfile.vue';
import global_mixins from '../../../../plugins/mixins';

// Vue.use(SuiVue);
export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialLeaseCode: String,
        initialVersionId: String,
        initialCountryDefaultSettings: String,
    },
    components: {
        'lease-main-form-component': LeaseMainForm,
        'lease-diary-component': LeaseDiary,
        'lease-notes-component': LeaseNotes,
        'lease-guarantee-component': LeaseGuarantee,
        'lease-insurance-component': LeaseInsurance,
        'lease-inspection-component': LeaseInspection,
        'lease-rent-review-component': LeaseRentReview,
        'lease-unit-details-component': LeaseUnitDetails,
        'lease-unit-history-component': LeaseUnitHistory,
        'lease-man-fees-component': LeaseManagementFees,
        'lease-bonds-component': LeaseBonds,
        'lease-outstanding-amount-component': LeaseOutstandingAmounts,
        'lease-contacts-component': LeaseContacts,
        'lease-documents-component': LeaseDocuments,
        'lease-email-component': LeaseEmail,
        'lease-sms-component': LeaseSMS,
        'lease-profile-component': LeaseProfile,
    },
    data() {
        return {
            property_code: '',
            lease_code: '',
            version_id: '',
            property_list: [],
            lease_list: [],
            lease_main_list: [],
            error_msg: [],
            search_type: 0,
            form_mode: 0,
            read_only: this.initialReadOnly,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            template_tab: null,
            loading_setting: false,
            loading_page_setting: false,
            tab: null,
            shortcuts: [
                { icon: 'business', title: 'Go to property', shortcut_code: 'property' },
                { icon: 'table_chart', title: 'Lease Abstract', shortcut_code: 'lease_abstract' },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (current month)',
                    shortcut_code: 'tenant_activity_current_month',
                },
                {
                    icon: 'account_balance',
                    title: 'Tenant Activity (all dates)',
                    shortcut_code: 'tenant_activity_all_date',
                },
                { icon: 'receipt', title: 'View Tax Invoices', shortcut_code: 'view_tax_invoice' },
                { icon: 'history', title: 'Lease Activity Logs', shortcut_code: 'lease_logs' },
                { icon: 'print', title: 'Print', shortcut_code: 'print' },
            ],
            force_load: false,
            lease_page_title: 'Lease Summary',
            error_server_msg: {},
            error_server_msg2: [],
            lease_existed: true,
            force_load_new_lease: 0,
            lease_commencement_date: '31/12/2999',
            tour_steps_new_lease: [],
            new_lease_code: '',
            process_state: '',
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'doc_active_version',
            'sys_ver_control_list',
            'tour_steps',
        ]),
    },
    mounted() {
        // console.log(localStorage.getItem('cirrus8_api_url'));
        this.fetchFormVersionControl();
        this.loadPropertyList();
        this.fetchCountryList();
        this.fetchAccountIncomeGroupedList();
        this.fetchAccountExpenseGroupedList();
        this.fetchBondPropertyList();
        this.fetchParamLeaseTypeList();
        this.fetchParamDivisionList();
        this.fetchParamTenantTypeList();
        this.fetchRetailCategoryList();
        this.fetchRetailSubCategoryList();
        this.fetchRetailFineCategoryList();
        this.fetchTaxRateList();

        let step_array = [
            {
                target: '.v-step-new-lease-button',
                content: `Create <strong>New</strong> lease <br> Shows the form for new lease`,
            },
            { target: '.v-step-search-type', content: 'Select the form search type' },
            { target: '.v-step-property-select', content: 'Select the lease property you want to view or edit' },
            { target: '.v-step-final-message', content: 'You have finish the page tour.' },
        ];
        this.SET_TOUR_STEPS(step_array);
        let lease_form_read_only = [
            { form_type: 'LEASE', form_section: 'LEASE_MAIN_DETAIL' },
            { form_type: 'LEASE', form_section: 'LEASE_CONTACT' },
            { form_type: 'LEASE', form_section: 'LEASE_DIARY' },
            { form_type: 'LEASE', form_section: 'LEASE_UNIT_DETAIL' },
            { form_type: 'LEASE', form_section: 'LEASE_INSURANCE' },
            { form_type: 'LEASE', form_section: 'LEASE_INSPECTION' },
            { form_type: 'LEASE', form_section: 'LEASE_GUARANTEE' },
            { form_type: 'LEASE', form_section: 'LEASE_NOTE' },
            { form_type: 'LEASE', form_section: 'LEASE_DOCUMENT' },
            { form_type: 'LEASE', form_section: 'LEASE_CHARGE' },
            { form_type: 'LEASE', form_section: 'LEASE_MANAGEMENT' },
            { form_type: 'LEASE', form_section: 'LEASE_RENT_REVIEW' },
        ];
        this.SET_PM_LEASE_FORM_READ_ONLY(lease_form_read_only);
        this.tour_steps_new_lease = [
            {
                target: '.v-step-cancel-lease-button',
                content: `Cancel <strong>New</strong> lease <br> Shows the form for new lease`,
            },
            { target: '.v-step-property-select', content: 'Select the lease property you want to view or edit' },
            { target: '.v-step-new-lease-code', content: 'Enter new lease code' },
            { target: '.v-step-final-message', content: 'You have finish the page tour.' },
        ];
        this.property_code = this.initialPropertyCode;
        this.lease_code = this.initialLeaseCode;
        this.version_id = this.initialVersionId;
        this.getLeaseClientSubmittedForm();
        // document.onreadystatechange = () => {
        //     if (document.readyState == "complete") {
        //         bus.$emit("loadLeaseMainFormSection","");
        //     }
        // }
    },
    methods: {
        ...mapActions([
            'fetchCountryList',
            'fetchAccountIncomeGroupedList',
            'fetchAccountExpenseGroupedList',
            'fetchBondPropertyList',
            'fetchParamDivisionList',
            'fetchParamLeaseTypeList',
            'fetchParamTenantTypeList',
            'fetchRetailCategoryList',
            'fetchRetailSubCategoryList',
            'fetchRetailFineCategoryList',
            'fetchTaxRateList',
            'fetchFormVersionControl',
        ]),
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_LEASE_CODE', 'SET_TOUR_STEPS', 'SET_PM_LEASE_FORM_READ_ONLY']),
        loadPropertyList: function () {
            this.$api.post('load-property-dropdown-list').then((response) => {
                this.property_list = response.data.data;
            });
        },
        loadLeaseList: function () {
            this.loading_setting = true;
            let form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('page_source', 'leaseFormTemplate');
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-lease-list', form_data).then((response) => {
                this.lease_list = response.data.data;
                this.loading_setting = false;
            });
        },
        selectLease: function (fieldKey, fieldValue, leaseAddress, fieldGroup) {
            this.lease_code = {
                fieldKey: fieldKey,
                fieldValue: fieldValue,
                leaseAddress: leaseAddress,
                fieldGroup: fieldGroup,
                field_key: fieldKey,
                field_value: fieldValue,
                lease_address: leaseAddress,
                field_group: fieldGroup,
            };
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        goToHref: function (param1, param2) {
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        getLeaseClientSubmittedForm: function () {
            if (this.property_code !== '' && this.lease_code !== '' && this.version_id !== '') {
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/fetch/submitted-form-detail', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        this.loading_page_setting = false;
                        this.created_by_name = response.data.created_by_name;
                        this.created_by_email = response.data.created_by_email;
                        this.created_date = response.data.created_date;
                        this.comments = response.data.comments;
                        this.feedback_comment = response.data.feedback_comment;
                        this.lease_form_type = response.data.lease_form_type;
                        this.process_state = response.data.process_state;
                        this.form_description = response.data.form_description;
                        if (this.comments === '') {
                            this.comments = 'no comments';
                        }
                    });
            }
        },
        goToShortcut: function (parameter) {
            switch (parameter) {
                case 'property':
                    window.open(
                        '?module=properties&command=v2_manage_property_page&property_code=' + this.property_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'company':
                    //module=companies&command=company&companyID=7ELEVEN
                    window.open(
                        '?module=companies&command=company&companyID=' + this.$company_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'lease_abstract':
                    break;
                case 'tenant_activity_current_month':
                    break;
                case 'tenant_activity_all_date':
                    break;
                case 'view_tax_invoice':
                    break;
                case 'print':
                    break;
            }
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
        showNewLease: function () {
            this.form_mode = 1;
            this.lease_page_title = 'New Lease';
            this.new_lease_code = '';
        },
        cancelNewLease: function () {
            this.lease_page_title = 'Lease Summary';
            this.form_mode = 0;

            this.error_msg = [];
            this.error_server_msg2 = [];
            this.new_lease_code = '';
            this.lease_existed = true;
        },
        showLeaseListTable: function () {
            if (this.form_mode === 0) {
                this.form_mode = 0;
                this.lease_code = { fieldKey: '' };
            } else {
                this.error_msg = [];
                this.error_server_msg2 = [];
                this.new_lease_code = '';
                this.lease_existed = true;
            }
        },
        checkLeaseCode: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            let new_lease_code = this.new_lease_code.toUpperCase().trim();

            this.new_lease_code = new_lease_code;
            if (new_lease_code.length > 10) {
                // this.error_msg.push({id:'new_lease_code', message: 'Lease code allows 10 characters only'});
                this.error_server_msg2.push(['Lease code allows 10 characters only']);
            } else if (new_lease_code === '') {
                // this.error_msg.push({id:'new_lease_code', message: 'Please input a valid lease code'});
                this.error_server_msg2.push(['Please input a valid lease code']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('new_lease_code', new_lease_code);
                form_data.append('no_load', true);
                this.$api
                    .post(this.cirrus8_api_url + 'api/lease/fetch/check-code-if-exist', form_data)
                    .then((response) => {
                        this.error_server_msg2 = response.data.validation_errors;
                        if (this.error_server_msg2.length === 0) {
                            this.lease_existed = response.data.lease_existed;
                            if (!this.lease_existed) {
                                this.force_load_new_lease++;
                                // var child = this.$refs.main_form_new_lease;
                                // child.loadForm();
                            }
                        }
                        this.loading_page_setting = false;
                    });
            }
        },
        getLeaseCode: function (value) {
            let lease_code = value.lease_code;
            let lease_name = value.lease_name;
            this.lease_commencement_date = value.lease_commencement_date;
            let label = lease_code + ' - ' + lease_name;
            this.cancelNewLease();
            // this.loadLeaseList();
            this.lease_code = {
                fieldKey: lease_code,
                field_key: lease_code,
                fieldValue: label,
                field_value: label,
                value: lease_code,
                label: label,
            };
        },
        startTour: function () {
            this.$tours['leaseTour'].start();
        },
        startNewLeaseTour: function () {
            this.$tours['newLeaseTour'].start();
        },
        sampleButon: function () {
            bus.$emit('loadLeaseMainFormSection', '');
        },
        printLease: function () {
            window.open(
                '?module=leases&command=lease_print_page&property_code=' +
                    this.property_code +
                    '&lease_code=' +
                    this.lease_code +
                    '&version_id=' +
                    this.version_id,
                '_blank', // <- This is what makes it open in a new window.
            );
        },
    },
    watch: {
        property_code: function () {
            // this.lease_code = {fieldKey:''};
            this.lease_list = [];
            this.SET_PROPERTY_CODE(this.property_code);
            this.loadLeaseList();
        },
        lease_code: function () {
            this.SET_LEASE_CODE(this.lease_code);
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
    },
    created() {},
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
