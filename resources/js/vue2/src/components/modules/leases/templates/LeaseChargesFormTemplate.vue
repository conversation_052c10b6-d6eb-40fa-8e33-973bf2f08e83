<style>
.error-message-div .v-alert {
    margin-bottom: 0px !important;
}

.rent_review_tabs .v-tabs-bar {
    background-color: #3489a1 !important;
}
</style>
<template>
    <v-app v-cloak>
        <div
            v-if="(view_list && u_type != 'A') || (u_type == 'A' && !edit_charge_id)"
            v-resize="onResize"
            class="c8-page"
        >
            <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
            <v-toolbar flat>
                <v-toolbar-title>
                    <cirrus-page-header title="Charge Review Tasks" />
                </v-toolbar-title>
                <div class="flex-grow-1"></div>
            </v-toolbar>
            <div v-if="u_type != 'A'">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This form allows you to view the process status of charge
                review forms in the Cirrus8. To enter a new charge review -
                <a
                    href="#"
                    @click="
                        view_list = 0;
                        approve_notification = 0;
                        email_notification = 0;
                    "
                    >click here</a
                ><br /><br />
            </div>
            <div
                class="page-noty"
                v-if="email_notification && with_email_address"
            >
                <p class="success-box">
                    The rent review letter has been emailed to the tenant. <strong>{{ lease_code.field_key }}</strong>
                </p>
            </div>
            <div
                class="page-noty"
                v-if="approve_notification"
            >
                <p class="success-box">Successfully approved</p>
            </div>
            <div
                v-if="this.send_on_approval && with_email_address && approve_notification"
                class="page-noty"
            >
                <p class="success-box">
                    The invoice has been emailed to the tenant. <strong>{{ lease_code.field_key }}</strong>
                </p>
            </div>
            <div
                class="page-noty"
                v-if="rejected_notification"
            >
                <p class="error-box">Successfully rejected</p>
            </div>
            <div
                class="page-noty"
                v-if="(this.send_on_approval || email_notification) && !with_email_address && approve_notification"
            >
                <p class="warning-box">
                    Email address is invalid or not existing for some tenants. Please check the email settings of the
                    following:
                    <strong>{{ lease_code.field_key }}</strong>
                </p>
            </div>
            <div class="page-form">
                <v-row class="form-row">
                    <table class="data-grid data-grid-dense tableHive">
                        <tr class="form-subheader">
                            <td class="text-left">Select {{ portfolio_manager_label }}</td>
                            <td class="text-left">From Date</td>
                            <td class="text-left">To Date</td>
                            <td class="text-left">With Status</td>
                            <td></td>
                        </tr>

                        <tr>
                            <td class="text-left">
                                <multiselect
                                    v-model="property_manager_filter"
                                    :options="property_manager_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </td>
                            <td class="text-left">
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    v-model="from_date_filter"
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker
                                >&nbsp;
                            </td>
                            <td class="text-left">
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    v-model="to_date_filter"
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker
                                >&nbsp;
                            </td>
                            <td class="text-left">
                                <multiselect
                                    v-model="status_filter"
                                    :options="status_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Please Select"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </td>
                            <td class="text-left">
                                <v-btn
                                    class=""
                                    @click="loadListCharge()"
                                    color="success"
                                    dark
                                    small
                                    depressed
                                >
                                    Search
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-row>
            </div>

            <div class="page-form">
                <div class="text-right">
                    <cirrus-input
                        inputFormat="search"
                        v-model="search_table"
                        placeholder="Search"
                        :edit_form="edit_form"
                        style="padding-right: 1em"
                    ></cirrus-input>
                </div>
                <v-row class="form-row">
                    <v-data-table
                        class="c8-datatable-custom data-grid data-grid-dense tableHive"
                        dense
                        item-key="id"
                        :headers="status_filter.fieldKey != 2 ? review_list_headers : review_list_headers_rejected"
                        :items="review_list"
                        :custom-sort="customSort"
                        :page.sync="page"
                        :search="search_table"
                    >
                        <template v-slot:item.charge_id="{ item }">
                            <div class="form-row no-border-line">
                                <a
                                    @click="
                                        loadSelectedCharges(item.charge_id);
                                        approve_notification = 0;
                                        email_notification = 0;
                                        rejected_notification = 0;
                                    "
                                    >View</a
                                >
                            </div>
                        </template>

                        <template v-slot:item.delete="{ item }">
                            <a
                                @click="delete_review(item.charge_id, item.property_code)"
                                href="#"
                            >
                                <img :src="asset_domain + 'assets/images/icons/delete.png'" />
                            </a>
                        </template>
                    </v-data-table>
                </v-row>
            </div>
        </div>

        <div
            v-if="(!view_list && u_type != 'A') || (u_type == 'A' && edit_charge_id)"
            v-resize="onResize"
            class="c8-page"
        >
            <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
            <v-toolbar flat>
                <v-toolbar-title>
                    <cirrus-page-header :title="lease_page_title" />
                </v-toolbar-title>
                <div class="flex-grow-1"></div>
            </v-toolbar>
            <br />
            <div>
                <b
                    >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This form should be used to advise
                    {{ ucwords(trust_account_label) }} of any changes to lease charges. i.e. rent or
                    {{ variable_outgoings_label }}.</b
                >
            </div>
            <div>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;To begin - simply select a lease, the charge type that you
                want to adjust, a date from which the adjustment applies, and the new amount you wish to charge.
            </div>
            <br />

            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_main_form"
            ></cirrus-server-error>
            <div
                class="page-noty"
                v-if="success_notification"
            >
                <p class="success-box">
                    Your charge review submission has been successfully lodged. You will be notified when it has been
                    processed. If you would like to edit lease notes for this lease
                    <a @click="gotoNotes()">click here</a>
                </p>
            </div>
            <div
                class="page-noty"
                v-if="lease_charge_notification"
            >
                <p class="error-box">Lease Charge is not existing in Lease form.</p>
            </div>
            <div
                class="infoBox"
                v-if="download_pdf"
            >
                <a :href="'download.php?fileID=' + download_pdf"
                    ><img
                        :src="asset_domain + 'assets/images/icons/pdf.png'"
                        alt="Adobe Logo"
                        class="icon"
                    /><strong>Download in printable format (PDF file)</strong> <u> left click to download or view</u></a
                >
            </div>

            <v-container
                fluid
                v-if="u_type == 'A' && edit_charge_status == 0"
            >
                <v-row>
                    <v-col
                        cols="12"
                        md="6"
                    >
                    </v-col>
                    <v-col
                        cols="12"
                        md="6"
                    >
                        <v-textarea
                            class="vuetifyText v-step-feedback"
                            outlined
                            label="Your feedback (This response is sent to the client when either processing or rejecting a form)"
                            v-model="rejected_comment"
                            value=""
                            full-width
                        ></v-textarea>
                        <div>
                            <div style="float: right; margin-left: 10px">
                                <span>
                                    <v-btn
                                        color="error"
                                        dense
                                        small
                                        class="v-step-form-no-btn"
                                        @click="rejectFile()"
                                        ><v-icon>clear</v-icon>Reject</v-btn
                                    >
                                </span>
                            </div>
                            <div style="float: right; margin-left: 10px">
                                <span>
                                    <v-btn
                                        color="success"
                                        dense
                                        small
                                        class="v-step-form-yes-btn"
                                        @click="approveFile()"
                                        ><v-icon>done</v-icon>process</v-btn
                                    >
                                </span>
                            </div>
                            <div style="float: right !important; margin-top: 7px">
                                <span
                                    ><strong
                                        >This response is sent to the client when either approving or rejecting a form
                                    </strong></span
                                >
                            </div>
                        </div>
                    </v-col>
                </v-row>
            </v-container>

            <v-container
                fluid
                v-if="u_type != 'A' && edit_charge_id && edit_charge_status == 2"
            >
                <v-row>
                    <v-col
                        cols="12"
                        md="6"
                    >
                        <br /><br />
                        <div class="text-right"><strong>Rejected Feedback</strong></div>
                        <div class="text-right">This is the reason why trust_accountant_label rejecting a form</div>
                    </v-col>
                    <v-col
                        cols="12"
                        md="6"
                    >
                        <v-textarea
                            class="vuetifyText v-step-feedback"
                            outlined
                            v-model="rejected_comment"
                            value=""
                            full-width
                            :disabled="true"
                        ></v-textarea>
                    </v-col>
                </v-row>
            </v-container>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>
            <div class="page-form">
                <v-row
                    class="form-row"
                    v-if="edit_form == true"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Search Type:
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            class="v-step-search-type form-toggle"
                            v-model="search_type"
                            mandatory
                        >
                            <v-btn
                                tile
                                small
                                text
                            >
                                Property
                            </v-btn>
                            <v-btn
                                tile
                                small
                                text
                            >
                                Lease
                            </v-btn>
                            <v-btn
                                tile
                                small
                                text
                            >
                                {{ property_manager_label }}
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="search_type == 2"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >{{ property_manager_label }}:
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            :disabled="disable_form"
                            v-model="manager_code"
                            :options="manager_list"
                            :allowEmpty="false"
                            class="vue-select2 dropdown-left dropdown-400"
                            :custom-label="nameWithDash"
                            group-label="language"
                            :placeholder="'Select a ' + property_manager_label"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-btn
                            v-if="edit_form == true"
                            :loading="manager_list.length <= 0"
                            depressed
                            elevation="0"
                            small
                            color="normal"
                            height="30"
                            v-on:click="changeSearchType('manager_code')"
                        >
                            <v-icon>arrow_right</v-icon>
                        </v-btn>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="
                        search_type != 1 ||
                        (search_type == 1 && lease_code.field_key !== '' && typeof lease_code.field_key !== 'undefined')
                    "
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Property:
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            :disabled="disable_form"
                            v-model="property_code"
                            :options="property_list"
                            :allowEmpty="false"
                            class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                            :custom-label="nameWithDash"
                            group-label="language"
                            placeholder="Select a property"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-btn
                            v-if="edit_form == true"
                            :loading="property_list.length <= 0"
                            depressed
                            elevation="0"
                            small
                            color="normal"
                            height="30"
                            v-on:click="changeSearchType('property_code')"
                        >
                            <v-icon>arrow_right</v-icon>
                        </v-btn>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="property_code.field_key !== '' || search_type == 1"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Lease:
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            :disabled="disable_form"
                            v-model="lease_code"
                            :options="lease_list"
                            :allowEmpty="false"
                            class="vue-select2 dropdown-left dropdown-400"
                            :custom-label="nameWithDash"
                            group-label="language"
                            placeholder="Select a lease"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-btn
                            v-if="edit_form == true"
                            :loading="lease_list.length <= 0"
                            depressed
                            elevation="0"
                            tile
                            small
                            color="normal"
                            height="30"
                            v-on:click="changeSearchType('lease_code')"
                        >
                            <v-icon>arrow_right</v-icon>
                        </v-btn>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="
                        property_code.field_key !== '' &&
                        lease_code.field_key !== '' &&
                        typeof lease_code.field_key !== 'undefined' &&
                        edit_form == true
                    "
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Select a lease charge:
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <input
                            type="hidden"
                            v-model="unit_id"
                        />
                        <input
                            type="hidden"
                            v-model="unit_area"
                        />
                        <vue-dual-list-select
                            v-model="selectedLeaseCharge"
                            :options="availableLeaseCharge"
                            ref="refChargeModel"
                            :displayCount="false"
                            :withKey="false"
                        >
                        </vue-dual-list-select>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="
                        property_code.field_key !== '' &&
                        lease_code.field_key !== '' &&
                        typeof lease_code.field_key !== 'undefined' &&
                        edit_form == true
                    "
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn
                            class=""
                            @click="selectCharge()"
                            color="success"
                            dark
                            small
                            tile
                        >
                            Select a Lease Charge
                        </v-btn>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="lease_charge_chosen.length && disable_form"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Unit:
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-label text-left"
                    >
                        {{ unit_description }}
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="lease_charge_chosen.length"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Date from which the new charge amount applies:
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-icon-date-picker
                            :size="'40'"
                            v-model="charge_billing_date"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker
                        >&nbsp;(next billing date is {{ next_billing_date }} )
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="
                        lease_charge_chosen.length && has_rent_review_fee && !show_rent_review_fee && edit_form == true
                    "
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn
                            class=""
                            @click="show_rent_review_fee = true"
                            color="success"
                            dark
                            small
                            tile
                        >
                            Add Rent Review Fee
                        </v-btn>
                    </v-col>
                </v-row>
            </div>

            <br />
            <sui-table
                v-if="
                    search_type != 1 &&
                    property_code.field_key != null &&
                    property_code.field_key != '' &&
                    (lease_code.field_key == null || lease_code.field_key == '')
                "
                stackable
                selectable
                class="fixed_headers"
            >
                <sui-table-header class="fixed_headers_thead">
                    <sui-table-row>
                        <sui-table-header-cell
                            colspan="4"
                            class="fieldDescription headerTr"
                            >Matching Records - click to refine your search
                        </sui-table-header-cell>
                    </sui-table-row>
                    <sui-table-row class="fieldDescription">
                        <sui-table-header-cell class="center fieldDescription headerTr">Property</sui-table-header-cell>
                        <sui-table-header-cell class="fieldDescription headerTr">Lease</sui-table-header-cell>
                        <sui-table-header-cell class="fieldDescription headerTr">Description</sui-table-header-cell>
                        <sui-table-header-cell class="center fieldDescription headerTr">Status</sui-table-header-cell>
                    </sui-table-row>
                </sui-table-header>
                <sui-table-body v-if="loading_content_setting">
                    <sui-table-row>
                        <sui-table-cell
                            colspan="4"
                            class="center"
                        >
                            <cirrus-content-loader v-if="loading_content_setting"></cirrus-content-loader>
                        </sui-table-cell>
                    </sui-table-row>
                </sui-table-body>
                <sui-table-body>
                    <sui-table-row
                        v-for="(leaseListData, index) in lease_list"
                        :key="index"
                        @click="
                            selectLease(
                                leaseListData.field_key,
                                leaseListData.field_value,
                                leaseListData.leaseAddress,
                                leaseListData.field_group,
                            )
                        "
                    >
                        <sui-table-cell class="center">
                            {{ property_code.field_key }}
                        </sui-table-cell>
                        <sui-table-cell class="">
                            {{ leaseListData.field_key }} - {{ leaseListData.field_value }}
                        </sui-table-cell>
                        <sui-table-cell class="">
                            {{ leaseListData.leaseAddress }}
                        </sui-table-cell>
                        <sui-table-cell class="center">
                            <v-icon
                                v-if="leaseListData.field_group === 'C'"
                                color="blue darken-2"
                                >business
                            </v-icon>
                            <v-icon
                                v-if="leaseListData.field_group !== 'C'"
                                color="orange darken-2"
                                >warning
                            </v-icon>
                            {{ leaseListData.field_group === 'C' ? 'Current' : 'Vacant' }}
                        </sui-table-cell>
                    </sui-table-row>
                </sui-table-body>
            </sui-table>

            <v-card
                v-if="lease_charge_chosen.length && show_rent_review_fee"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Rent Review Fee Details</h6>
                    &nbsp
                </v-card-actions>
            </v-card>

            <div
                v-if="lease_charge_chosen.length && show_rent_review_fee"
                class="page-form"
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Description
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            :size="'40'"
                            :id="'lease_tenant_name'"
                            v-model="rent_review_description"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </v-col>
                </v-row>

                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Amount ({{ currency_symbol }})
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-input
                            :size="'40'"
                            :id="'lease_tenant_name'"
                            v-model="rent_review_amount"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </v-col>
                </v-row>

                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Expense Account
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            :disabled="disable_form"
                            v-model="rent_review_account"
                            :options="expense_account"
                            group-values="field_group_values"
                            :groupSelect="false"
                            group-label="field_group_names"
                            :group-select="true"
                            class="vue-select2 dropdown-left dropdown-400"
                            :custom-label="nameWithDash"
                            placeholder="Please select ..."
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="show_rent_review_fee && edit_form == true"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn
                            class=""
                            @click="
                                rent_review_account = '';
                                rent_review_description = '';
                                rent_review_amount = '';
                                show_rent_review_fee = false;
                            "
                            color="success"
                            dark
                            small
                            tile
                        >
                            Remove Rent Review Fee
                        </v-btn>
                    </v-col>
                </v-row>
            </div>

            <!--START  OF LEASE CHARGE CHOSEN-->

            <v-tabs
                dark
                icons-and-text
                v-model="template_tab"
                show-arrows
                class="rent_review_tabs"
                v-if="lease_charge_chosen.length"
            >
                <v-tabs-slider color="white"></v-tabs-slider>

                <v-tab
                    v-for="(item_charge, index_charge) in lease_charge_chosen"
                    :key="item_charge.unit_charge_id"
                    :href="'#tab-' + item_charge.unit_charge_id"
                    v-if="responsive_show"
                    class="v-step-form-tab"
                >
                    {{ item_charge.unit_charge_description }}
                </v-tab>

                <v-tab-item
                    v-for="(item_charge, index_charge) in lease_charge_chosen"
                    :key="item_charge.unit_charge_id"
                    :value="'tab-' + item_charge.unit_charge_id"
                >
                    <div
                        class="page-form"
                        v-if="u_type != 'A' && item_charge.unit_rent_review_list.length"
                    >
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                                class="form-subheader"
                                ><strong
                                    >This lease charge has rent review dates stored against it. If you are submitting a
                                    new charge amount based on a completed rent review - select the appropriate rent
                                    review below.</strong
                                ></v-col
                            >
                        </v-row>

                        <v-row
                            class="form-row"
                            v-for="(item_rent_review, index_rent_review) in item_charge.unit_rent_review_list"
                            :key="index_rent_review"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                            ></v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <input
                                    v-if="edit_form == true"
                                    type="radio"
                                    @click="selectRentReview(index_charge, index_rent_review)"
                                    :value="item_rent_review.review_id"
                                    v-model="item_charge.rent_review"
                                />
                                <strong>{{ item_rent_review.review_date }}</strong> - {{ item_rent_review.review_type }}
                                <a
                                    href="#"
                                    v-if="edit_form == true"
                                    @click="closeReview(index_charge, index_rent_review, item_rent_review.review_id)"
                                >
                                    [ close ]
                                </a>
                                &nbsp;&nbsp;<a
                                    v-if="item_charge.rent_review == item_rent_review.review_id && edit_form == true"
                                    @click="item_charge.rent_review = 0"
                                    href="#"
                                >
                                    [ clear ]</a
                                >
                            </v-col>
                        </v-row>
                    </div>

                    <div
                        class="page-form"
                        v-if="u_type != 'A'"
                    >
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                                class="form-subheader"
                                ><strong>Lease Details</strong></v-col
                            >
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Charge Description
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input text-only text-left"
                            >
                                {{ item_charge.unit_charge_description }}
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Type
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input text-only text-left"
                            >
                                {{ item_charge.unit_charge_type }}
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Frequency
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input text-only text-left"
                            >
                                {{ item_charge.unit_charge_frequency_name }}
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Charge
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input text-only text-left"
                            >
                                From <strong>{{ item_charge.unit_start_date }}</strong> to
                                <strong>{{ item_charge.unit_stop_date }}</strong>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Partial Rent Method
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input text-only text-left"
                            >
                                {{ item_charge.unit_partial_rent_desc }}
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="edit_form == true"
                        >
                            <v-col
                                xs="2"
                                sm="2"
                                md="2"
                                class="form-label"
                            ></v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input text-only text-left"
                            >
                                <v-btn
                                    class=""
                                    @click="
                                        rent_review_charge_id = item_charge.unit_charge_serial;
                                        rent_review_index = index_charge;
                                        rent_review_type = '';
                                        rent_modal = true;
                                    "
                                    color="info"
                                    dark
                                    small
                                    tile
                                >
                                    Add a Future Rent Review
                                </v-btn>
                            </v-col>
                        </v-row>

                        <v-data-table
                            class="c8-datatable-custom"
                            dense
                            item-key="id"
                            :headers="headers"
                            :items="item_charge.unit_charge_history"
                            :items-per-page="items_per_page"
                            hide-default-footer
                            :page.sync="page"
                            @page-count="page_count = $event"
                            :search="search_datatable"
                        >
                        </v-data-table>
                    </div>

                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                                class="form-subheader"
                                ><strong>New Charge Details</strong></v-col
                            >
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                                class="subHeader"
                            >
                                NOTE: Enter in the new amount you want to charge below. You may enter in an annual
                                amount, a monthly amount, or an amount per <span v-html="area_unit"></span>. Click on
                                'calculate' in order to preview the new amount being charged below.
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Method To Calculate New Charge:
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <v-btn-toggle
                                    class="v-step-search-type form-toggle"
                                    v-model="item_charge.charge_method"
                                    mandatory
                                >
                                    <v-btn
                                        :disabled="disable_form"
                                        @click="calculate(index_charge)"
                                        tile
                                        small
                                        text
                                    >
                                        per year
                                    </v-btn>
                                    <v-btn
                                        :disabled="disable_form"
                                        @input="calculate(index_charge)"
                                        tile
                                        small
                                        text
                                    >
                                        per month
                                    </v-btn>
                                    <v-btn
                                        :disabled="disable_form"
                                        @input="calculate(index_charge)"
                                        tile
                                        small
                                        text
                                    >
                                        per <span v-html="area_unit"></span>
                                    </v-btn>
                                    <v-btn
                                        :disabled="disable_form"
                                        @input="calculate(index_charge)"
                                        tile
                                        small
                                        text
                                    >
                                        % increase
                                    </v-btn>
                                    <v-btn
                                        :disabled="disable_form"
                                        @input="calculate(index_charge)"
                                        tile
                                        small
                                        text
                                    >
                                        CPI Increase
                                    </v-btn>
                                </v-btn-toggle>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.charge_method != 4"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >New Charge Amount
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <cirrus-input
                                    :size="'40'"
                                    :id="'lease_tenant_name'"
                                    v-model="item_charge.charge_amount"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                                {{ item_charge.charge_method == 3 ? '%' : '' }}
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.charge_method == 4"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >CPI Index Name
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    :disabled="disable_form"
                                    @input="selectCPI(index_charge, true)"
                                    v-model="item_charge.cpi_index_name"
                                    :options="cpi_index_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="please select"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.charge_method == 4"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Applicable CPI Index
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    :disabled="disable_form"
                                    @input="selectApplicable(index_charge)"
                                    v-model="item_charge.cpi_index_available"
                                    :options="item_charge.applicable_cpi_index_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="please select"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                                &nbsp;&nbsp;
                                <sui-checkbox
                                    :disabled="disable_form"
                                    value="1"
                                    v-model="item_charge.cpi_manual"
                                    style="padding-top: 5px"
                                />
                                Manual Entry
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.charge_method == 4"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                            >
                                CPI Index (
                                <select
                                    v-if="!disable_form"
                                    v-model="item_charge.cpi_ages"
                                    @change="selectApplicable(index_charge)"
                                >
                                    <option value="3">3 months ago</option>
                                    <option value="6">6 months ago</option>
                                    <option value="9">9 months ago</option>
                                    <option value="12">12 months ago</option>
                                    <option value="15">15 months ago</option>
                                    <option value="18">18 months ago</option>
                                    <option value="21">21 months ago</option>
                                    <option value="24">24 months ago</option></select
                                ><span v-else>{{ item_charge.cpi_ages }} months ago </span>)
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <span
                                    class="v-input"
                                    v-if="item_charge.cpi_manual"
                                    ><input
                                        :disabled="disable_form"
                                        type="text"
                                        @input="reComputeCPI(index_charge)"
                                        class="cirrus-input-form-textbox"
                                        v-model="item_charge.cpi_index_old"
                                /></span>
                                <span
                                    style="margin-top: 5px; display: block"
                                    v-if="!item_charge.cpi_manual"
                                    >{{ item_charge.cpi_index_old }}</span
                                >
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.charge_method == 4"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Current CPI Index
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <span
                                    class="v-input"
                                    v-if="item_charge.cpi_manual"
                                    ><input
                                        :disabled="disable_form"
                                        type="text"
                                        @input="reComputeCPI(index_charge)"
                                        class="cirrus-input-form-textbox"
                                        v-model="item_charge.cpi_index_current"
                                /></span>
                                <span
                                    style="margin-top: 5px; display: block"
                                    v-if="!item_charge.cpi_manual"
                                    >{{ item_charge.cpi_index_current }}</span
                                >
                                <span
                                    style="margin-top: 5px; display: block"
                                    v-if="item_charge.total_increase_cpi && item_charge.cpi_manual"
                                    >= {{ item_charge.total_increase_cpi }}% increase</span
                                >
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.charge_method == 4"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Additional % Increase
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <span class="v-input"
                                    ><input
                                        :disabled="disable_form"
                                        type="text"
                                        @input="reComputeCPI(index_charge)"
                                        class="cirrus-input-form-textbox"
                                        v-model="item_charge.cpi_index_increase"
                                /></span>
                                <span v-if="item_charge.total_cpi"
                                    ><strong>= {{ item_charge.total_cpi }}% total increase</strong></span
                                >
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="edit_form == true"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                            >
                                <v-btn
                                    class=""
                                    @click="calculate(index_charge)"
                                    color="success"
                                    dark
                                    small
                                    tile
                                >
                                    Calculate new charge
                                </v-btn>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.calculation"
                        >
                            <table class="data-grid data-grid-dense tableHive">
                                <tr class="form-subheader">
                                    <td></td>
                                    <td>Annual Amount</td>
                                    <td>Monthly Amount</td>
                                    <td>Amount per <span v-html="area_unit"></span></td>
                                </tr>

                                <tr>
                                    <td class="text-right">Old Amount</td>
                                    <td>{{ item_charge.charge_old_amount_label }}</td>
                                    <td>{{ item_charge.charge_old_month }}</td>
                                    <td>{{ item_charge.charge_old_area }}</td>
                                </tr>

                                <tr class="subHeader">
                                    <td class="text-right"><strong>New Amount</strong></td>
                                    <td>
                                        <strong>{{ item_charge.charge_new_amount_label }}</strong>
                                    </td>
                                    <td>
                                        <strong>{{ item_charge.charge_new_month }}</strong>
                                    </td>
                                    <td>
                                        <strong>{{ item_charge.charge_new_area }}</strong>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="text-right">Increase</td>
                                    <td>{{ item_charge.charge_increase_amount }}</td>
                                    <td>{{ item_charge.charge_increase_month }}</td>
                                    <td>{{ item_charge.charge_increase_area }}% increase</td>
                                </tr>
                            </table>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.calculation && !item_charge.is_backcharge"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Raise Rent Review Fee
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <sui-checkbox
                                    :disabled="disable_form"
                                    value="1"
                                    v-model="item_charge.raise_rent_fee"
                                />
                                <i>This adds a diary reminder into the system</i>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.calculation && !item_charge.is_backcharge"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Comments
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <v-textarea
                                    v-model="item_charge.comment"
                                    auto-grow
                                    rows="6"
                                    full-width
                                    class="noteTextArea"
                                    :disabled="disable_form"
                                ></v-textarea>
                            </v-col>
                        </v-row>
                    </div>

                    <div class="page-form">
                        <v-row
                            class="form-row"
                            v-if="item_charge.is_backcharge"
                        >
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                                class="form-subheader"
                                ><strong>Back Charge Details</strong></v-col
                            >
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.is_backcharge"
                        >
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                                class="subHeader"
                            >
                                NOTE: The date from which you want the new charge to apply {{ charge_billing_date }} is
                                prior to the next billing date {{ next_billing_date }}, which means a back charge will
                                be created. If you want to generate an Interim Tax Invoice, select the option below -
                                otherwise the back charge will appear on the next invoice.
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.is_backcharge && edit_form"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Invoice Date
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <cirrus-icon-date-picker
                                    :id="'invoice' + item_charge.unit_charge_id"
                                    :size="'40'"
                                    v-model="item_charge.invoice_date"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.is_backcharge && edit_form"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Backcharge Period
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input text-only text-left"
                            >
                                {{ item_charge.interim_from_date }} To {{ item_charge.interim_to_date }}
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.is_backcharge && edit_charge_status != 1"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >{{ !edit_form ? 'System Calculated ' : '' }}Back Charge
                                {{ !edit_form ? '' : 'Amount' }}
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                {{ currency_symbol }}
                                <cirrus-input
                                    :size="'40'"
                                    :id="'lease_tenant_name'"
                                    v-model="item_charge.back_charge_amount"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                                [{{ item_charge.back_charge_tax }} tax] {{ item_charge.back_charge_total }} total
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.is_backcharge && !edit_form"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >User Submitted Back Charge
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                {{ currency_symbol }}
                                <cirrus-input
                                    :size="'40'"
                                    :id="'lease_tenant_name'"
                                    v-model="item_charge.submitted_back_charge_amount"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.is_backcharge && edit_charge_status != 1"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Raise Rent Review Fee
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <sui-checkbox
                                    :disabled="disable_form"
                                    value="1"
                                    v-model="item_charge.raise_rent_fee"
                                />
                                <i>This adds a diary reminder into the system</i>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="item_charge.is_backcharge"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Comments
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <v-textarea
                                    v-model="item_charge.comment"
                                    auto-grow
                                    rows="6"
                                    full-width
                                    class="noteTextArea"
                                    :disabled="disable_form"
                                ></v-textarea>
                            </v-col>
                        </v-row>
                    </div>

                    <div class="page-form">
                        <v-row
                            class="form-row"
                            v-if="isWithBackCharge(item_charge)"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Generate Interim Invoice
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <sui-checkbox
                                    :disabled="disable_form"
                                    value="1"
                                    v-model="generate_interim"
                                />

                                <a
                                    v-if="tax_invoice_pdf"
                                    :href="'download.php?fileID=' + tax_invoice_pdf"
                                    >[view invoice]</a
                                >
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="isWithBackCharge(item_charge) && generate_interim"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Due Date
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <cirrus-icon-date-picker
                                    :id="'due' + item_charge.unit_charge_id"
                                    :size="'40'"
                                    v-model="due_date"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                            </v-col>
                        </v-row>

                        <v-row
                            class="form-row"
                            v-if="isWithBackCharge(item_charge) && generate_interim"
                        >
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Send On TA Approval
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <sui-checkbox
                                    :disabled="disable_form"
                                    value="1"
                                    v-model="send_on_approval"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </v-tab-item>
            </v-tabs>

            <!--END OF LEASE CHARGE CHOSEN-->

            <!--CLOSE DIARY AND LETTER TEMPLATE-->
            <v-card
                v-if="u_type != 'A' && lease_charge_chosen.length && close_diary_list.length"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Close Associated Diary Items</h6>
                    &nbsp
                </v-card-actions>
            </v-card>

            <div
                v-if="u_type != 'A' && lease_charge_chosen.length && close_diary_list.length"
                class="page-form"
            >
                <v-data-table
                    class="c8-datatable-custom"
                    dense
                    item-key="id"
                    :headers="close_headers"
                    :custom-sort="customSort"
                    :items="close_diary_list"
                    :items-per-page="items_per_page"
                    :page.sync="page"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.diary_close="{ item }">
                        <div class="form-row no-border-line">
                            <sui-checkbox
                                :disabled="disable_form"
                                value="item.diary_close"
                                v-model="item.checked"
                            />
                        </div>
                    </template>

                    <template v-slot:item.diary_entry="{ item }">
                        <div class="form-row no-border-line">
                            <strong>{{ item.diary_entry_type }}:</strong> {{ item.diary_entry }}
                        </div>
                    </template>
                </v-data-table>
            </div>

            <v-card
                v-if="edit_form == false && filename_link"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Letter Template</h6>
                    &nbsp
                </v-card-actions>
            </v-card>
            <div
                v-if="edit_form == false && filename_link"
                class="page-form"
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class=""
                    >
                        Notice: cirrus8 has changed when rent review letters are sent to tenants. Rent review letters
                        are now sent after the lease charge review is approved by {{ trust_account_label }}ing.
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="edit_form == false"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <sui-checkbox
                            :disabled="true"
                            value="1"
                            v-model="email_to_tenant"
                        />
                        Send e-mail to tenants.
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="edit_form == false"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <sui-checkbox
                            :disabled="true"
                            value="1"
                            v-model="email_to_me"
                        />
                        Send me a copy of this message.
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="edit_form == false"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <a
                            target="_blank"
                            :href="'download.php?fileID=' + filename_link"
                            >Download
                            <img
                                :src="asset_domain + 'assets/images/icons/pdf.png'"
                                alt="Adobe Logo"
                                class="icon"
                            />
                        </a>
                    </v-col>
                </v-row>
            </div>

            <v-card
                v-if="lease_charge_chosen.length && edit_form == true"
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Letter Template</h6>
                    &nbsp
                </v-card-actions>
            </v-card>
            <div
                v-if="lease_charge_chosen.length && edit_form == true"
                class="page-form"
            >
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class=""
                    >
                        Notice: cirrus8 has changed when rent review letters are sent to tenants. Rent review letters
                        are now sent after the lease charge review is approved by {{ trust_account_label }}ing.
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="edit_form == true"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Choose a Template
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            :disabled="disable_form"
                            @input="chooseTemplate()"
                            v-model="letter_template"
                            :options="letter_template_list"
                            :allowEmpty="false"
                            class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                            group-label="language"
                            placeholder="Select Letter template"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-btn
                            :loading="letter_template_list.length <= 0"
                            depressed
                            elevation="0"
                            tile
                            small
                            color="normal"
                            height="30"
                        >
                            <v-icon>arrow_right</v-icon>
                        </v-btn>
                        <v-btn
                            v-if="edit_form == true && letter_template"
                            depressed
                            elevation="0"
                            small
                            color="normal"
                            height="30"
                            v-on:click="
                                letter_template = '';
                                formData.letter_template = '';
                            "
                        >
                            <v-icon>delete</v-icon>
                        </v-btn>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="letter_template && !download_file"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <textarea
                            name="letterTemplateBody"
                            id="letterTemplateBody"
                        ></textarea>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="letter_template && download_file"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <a
                            target="_blank"
                            :href="'download.php?fileID=' + download_file"
                            >Download
                            <img
                                :src="asset_domain + 'assets/images/icons/docx.png'"
                                alt="Adobe Logo"
                                class="icon"
                            />
                        </a>
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="letter_template"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <sui-checkbox
                            value="1"
                            v-model="email_to_tenant"
                        />
                        Send e-mail to tenants.
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="letter_template"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <sui-checkbox
                            value="1"
                            v-model="email_to_me"
                        />
                        Send me a copy of this message.
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="letter_template && !download_file"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="form-input"
                    >
                        <sui-checkbox
                            value="1"
                            v-model="include_letterhead"
                        />
                        Include letterhead.
                    </v-col>
                </v-row>

                <v-row
                    class="form-row"
                    v-if="edit_form == true"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="text-right"
                    >
                        <v-btn
                            class=""
                            @click="downloadFile()"
                            color="success"
                            dark
                            small
                            tile
                            v-if="letter_template"
                        >
                            download
                        </v-btn>

                        <v-btn
                            class=""
                            @click="submitFile()"
                            color="success"
                            dark
                            small
                            tile
                        >
                            submit
                        </v-btn>
                    </v-col>
                </v-row>
            </div>

            <!--FUTURE RENT REVIEW TEMPLATE-->
            <v-dialog
                v-model="rent_modal"
                max-width="1000"
                content-class="c8-page"
            >
                <v-card height="330">
                    <v-card-title class="headline">
                        Future Rent Review
                        <a
                            href="#"
                            class="dialog-close"
                            @click.prevent="rent_modal = false"
                        >
                            <v-icon>mdi-close</v-icon>
                        </a>
                    </v-card-title>
                    <v-card-text>
                        <cirrus-server-error
                            :error_msg="error_server_msg"
                            :errorMsg2="error_server_msg2"
                        ></cirrus-server-error>
                        <!--Lease add-->
                        <div>
                            <div class="page-form">
                                <v-row
                                    class="form-row error"
                                    v-if="futureRentReviewValidation != ''"
                                >
                                    <v-col
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        class=""
                                        style="color: #ffffff"
                                        >{{ futureRentReviewValidation }}
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label"
                                        >What type of rent review?
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="5"
                                        md="5"
                                        class="form-input"
                                    >
                                        <multiselect
                                            v-model="rent_review_type"
                                            :options="rent_review_type_list"
                                            :allowEmpty="false"
                                            class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                            group-label="language"
                                            placeholder="Select rent review type"
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </v-col>

                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-input"
                                    >
                                        On
                                        <cirrus-icon-date-picker
                                            :size="'40'"
                                            v-model="rent_review_date"
                                            :edit_form="edit_form"
                                            :error_msg="error_msg"
                                        ></cirrus-icon-date-picker>
                                    </v-col>
                                </v-row>

                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label"
                                    >
                                        Diarise
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <sui-checkbox
                                            v-model="rent_review_diaries"
                                            value="1"
                                        />
                                        diarise this review
                                    </v-col>
                                </v-row>
                            </div>
                        </div>
                    </v-card-text>
                    <v-card-actions>
                        <v-spacer />
                        <v-btn
                            class="v-step-save-2-button"
                            color="primary"
                            dark
                            depressed
                            tile
                            small
                            @click="addFutureRentReview()"
                        >
                            apply
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>

            <!--PREVIEW DOCUMENT-->
            <v-dialog
                v-model="preview_modal"
                max-width="1000"
                content-class="c8-page"
            >
                <v-card height="770">
                    <v-card-title class="headline">
                        Preview Rent Review Letter
                        <a
                            href="#"
                            class="dialog-close"
                            @click.prevent="preview_modal = false"
                        >
                            <v-icon>mdi-close</v-icon>
                        </a>
                    </v-card-title>
                    <v-card-text>
                        <cirrus-server-error
                            :error_msg="error_server_msg"
                            :errorMsg2="error_server_msg2"
                        ></cirrus-server-error>
                        <!--Lease add-->
                        <div>
                            <div class="page-form">
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        class=""
                                    >
                                        <div v-html="editorData" />
                                    </v-col>
                                </v-row>
                            </div>
                        </div>
                    </v-card-text>
                </v-card>
            </v-dialog>
        </div>
    </v-app>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import LeaseMainForm from '../forms/leaseMainForm.vue';
import axios from 'axios';
import Vue from 'vue';

Vue.component('vue-dual-list-select', require('../../../elements/VueDualListSelect.vue').default);

const Swal = require('sweetalert2');

export default {
    props: {
        initialReadOnly: Boolean,
        initialPageFormType: String,
        initialPropertyCode: String,
        initialLeaseCode: String,
        initialVersionId: String,
        viewpage: String,
        tinymce: String,
        openai: String,
        tinymceenabled: String,
        tinymceai: String,
    },
    components: {
        'lease-main-form-component': LeaseMainForm,
    },
    data() {
        return {
            variable_outgoings_label: 'Variable Outgoings',
            property_manager_label: 'Property Manager',
            trust_account_label: 'Trust account',
            trust_accountant_label: 'Trust accountant',
            portfolio_manager_label: 'Portfolio Manager',
            country_code: '',
            asset_domain: this.$assetDomain,
            today: '',
            success_notification: 0,
            approve_notification: 0,
            with_email_address: false,
            rejected_notification: 0,
            lease_charge_notification: 0,
            email_notification: 0,
            error_main_form: [],
            download_file: '',
            download_pdf: '',
            tax_invoice_pdf: '',
            editorData: '',
            editorConfig: {
                fontSize: {
                    options: [
                        '8pt',
                        '9pt',
                        '10pt',
                        '11pt',
                        '12pt',
                        '14pt',
                        '16pt',
                        '18pt',
                        '20pt',
                        '22pt',
                        '24pt',
                        '26pt',
                        '28pt',
                        '36pt',
                        '48pt',
                        '72pt',
                    ],
                },
                toolbar: {
                    items: [
                        'heading',
                        '|',
                        'bold',
                        'italic',
                        'link',
                        'bulletedList',
                        'numberedList',
                        '|',
                        'indent',
                        'outdent',
                        '|',
                        'imageUpload',
                        'blockQuote',
                        'insertTable',
                        'undo',
                        'redo',
                        'fontBackgroundColor',
                        'fontColor',
                        'fontSize',
                        'fontFamily',
                        'alignment',
                        'horizontalLine',
                        'highlight',
                        'strikethrough',
                        'subscript',
                        'superscript',
                        'underline',
                    ],
                },
                fontColor: {
                    colors: [
                        {
                            color: '#1abc9c',
                            label: 'Strong Cyan',
                        },
                        {
                            color: '#2ecc71',
                            label: 'Emerald',
                        },
                        {
                            color: '#3498db',
                            label: 'Bright Blue',
                        },
                        {
                            color: '#3498db',
                            label: 'Amethyst',
                        },
                        {
                            color: '#4e5f70',
                            label: 'Grayish Blue',
                        },

                        {
                            color: '#f1c40f',
                            label: 'Vivid yellow',
                        },

                        {
                            color: '#16a085',
                            label: 'Dark Cyan',
                        },

                        {
                            color: '#27ae60',
                            label: 'Dark Emerald',
                        },

                        {
                            color: '#2980b9',
                            label: 'Strong Blue',
                        },

                        {
                            color: '#8e44ad',
                            label: 'Dark Violet',
                        },

                        {
                            color: '#2c3e50',
                            label: 'Desaturated Blue',
                        },

                        {
                            color: '#f39c12',
                            label: 'Orange',
                        },

                        {
                            color: '#e67e22',
                            label: 'Carrot',
                        },

                        {
                            color: '#e74c3c',
                            label: 'Pale Red',
                        },

                        {
                            color: '#ecf0f1',
                            label: 'Bright Silver',
                            hasBorder: true,
                        },

                        {
                            color: '#95a5a6',
                            label: 'Light Grayish Cyan',
                        },

                        {
                            color: '#dddddd',
                            label: 'Light Gray',
                        },

                        {
                            color: '#ffffff',
                            label: 'White',
                            hasBorder: true,
                        },
                        {
                            color: '#d35400',
                            label: 'Pumpkin',
                        },

                        {
                            color: '#c0392b',
                            label: 'Strong Red',
                        },

                        {
                            color: '#bdc3c7',
                            label: 'Silver',
                        },

                        {
                            color: '#7f8c8d',
                            label: 'Grayish Cyan',
                        },

                        {
                            color: '#999999',
                            label: 'Dark Gray',
                        },

                        {
                            color: '#000000',
                            label: 'Black',
                        },
                    ],
                },
                fontBackgroundColor: {
                    colors: [
                        {
                            color: '#1abc9c',
                            label: 'Strong Cyan',
                        },
                        {
                            color: '#2ecc71',
                            label: 'Emerald',
                        },
                        {
                            color: '#3498db',
                            label: 'Bright Blue',
                        },
                        {
                            color: '#3498db',
                            label: 'Amethyst',
                        },
                        {
                            color: '#4e5f70',
                            label: 'Grayish Blue',
                        },

                        {
                            color: '#f1c40f',
                            label: 'Vivid yellow',
                        },

                        {
                            color: '#16a085',
                            label: 'Dark Cyan',
                        },

                        {
                            color: '#27ae60',
                            label: 'Dark Emerald',
                        },

                        {
                            color: '#2980b9',
                            label: 'Strong Blue',
                        },

                        {
                            color: '#8e44ad',
                            label: 'Dark Violet',
                        },

                        {
                            color: '#2c3e50',
                            label: 'Desaturated Blue',
                        },

                        {
                            color: '#f39c12',
                            label: 'Orange',
                        },

                        {
                            color: '#e67e22',
                            label: 'Carrot',
                        },

                        {
                            color: '#e74c3c',
                            label: 'Pale Red',
                        },

                        {
                            color: '#ecf0f1',
                            label: 'Bright Silver',
                            hasBorder: true,
                        },

                        {
                            color: '#95a5a6',
                            label: 'Light Grayish Cyan',
                        },

                        {
                            color: '#dddddd',
                            label: 'Light Gray',
                        },

                        {
                            color: '#ffffff',
                            label: 'White',
                            hasBorder: true,
                        },
                        {
                            color: '#d35400',
                            label: 'Pumpkin',
                        },

                        {
                            color: '#c0392b',
                            label: 'Strong Red',
                        },

                        {
                            color: '#bdc3c7',
                            label: 'Silver',
                        },

                        {
                            color: '#7f8c8d',
                            label: 'Grayish Cyan',
                        },

                        {
                            color: '#999999',
                            label: 'Dark Gray',
                        },

                        {
                            color: '#000000',
                            label: 'Black',
                        },
                    ],
                },
                language: 'en',
                image: {
                    toolbar: ['imageTextAlternative', 'imageStyle:full', 'imageStyle:side'],
                },
                table: {
                    contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells'],
                },
            },
            with_backcharge: 0,
            rent_review_account: { fieldKey: '', field_key: '', field_value: '', fieldValue: '' },
            rent_review_description: '',
            rent_review_amount: '',
            unit_description: '',
            unit_id: '',
            unit_area: '',
            charge_billing_date: '',
            next_billing_date: '',
            generate_interim: 0,
            send_on_approval: 0,
            due_date: '',
            email_to_tenant: 0,
            filename_link: '',
            email_to_me: 1,
            filename: '',
            letterTemplate: '',
            letterTemplateBody: '',
            include_letterhead: 1,
            letter_template: '',
            letter_template_list: [],
            lease_charge_chosen: [],
            close_checkbox_diary: [],

            rent_review_charge_id: '',
            rent_review_diaries: false,
            rent_review_date: '',
            rent_review_type: '',
            rent_review_index: '',
            futureRentReviewValidation: '',
            rent_review_type_list: [],
            cpi_index_list: [],
            applicable_cpi_index_list: [],
            expense_account: [],
            has_rent_review_fee: 0,
            show_rent_review_fee: false,
            selectedLeaseCharge: [],
            availableLeaseCharge: [],
            formData: {
                currentDB: localStorage.getItem('currentDB'),
                user_type: localStorage.getItem('user_type'),
                un: localStorage.getItem('un'),
            },

            template_tab: null,
            back_charge: true,
            rent_modal: false,
            preview_modal: false,
            review_list: [],
            headers: [
                { text: 'From', value: 'unit_charge_item_start_date', sortable: false, width: '24%' },
                { text: 'To', value: 'unit_charge_item_end_date', width: '24%' },
                { text: 'Amount Per Year', value: 'unit_charge_item_amount', width: '24%' },
                { text: 'Parking Bays', value: 'unit_charge_item_parking', width: '25%' },
            ],
            review_list_headers: [
                { text: 'Property Code', value: 'property_code' },
                { text: 'Lease Code', value: 'lease_code' },
                { text: 'Created', value: 'created' },
                { text: 'Updated', value: 'updated' },
                { text: 'Processed', value: 'processed' },
                { text: 'Processed By', value: 'processed_by' },
                { text: 'Author', value: 'author' },
                { text: 'Status', value: 'status' },
                { text: 'View', value: 'charge_id' },
            ],
            review_list_headers_rejected: [
                { text: 'Property Code', value: 'property_code' },
                { text: 'Lease Code', value: 'lease_code' },
                { text: 'Created', value: 'created' },
                { text: 'Updated', value: 'updated' },
                { text: 'Processed', value: 'processed' },
                { text: 'Processed By', value: 'processed_by' },
                { text: 'Author', value: 'author' },
                { text: 'Status', value: 'status' },
                { text: 'View', value: 'charge_id' },
                { text: 'Delete', value: 'delete' },
            ],
            close_diary_list: [],
            close_headers: [
                { text: 'Close', value: 'diary_close', sortable: false, width: '20%' },
                { text: 'Date', value: 'diary_date', width: '20%' },
                { text: 'Diary Entry', value: 'diary_entry', width: '78%' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 20,
            search_datatable: '',
            search_table: '',
            charge_review_id: '',
            to_date_filter: '',
            from_date_filter: '',
            status_filter: {
                field_key: localStorage.getItem('user_type') == 'A' ? 0 : 'All',
                field_value: localStorage.getItem('user_type') == 'A' ? 'Unprocessed' : 'All',
            },
            status_list: [],
            property_manager_list: [],
            property_manager_filter: {
                field_key: '',
                field_value: localStorage.getItem('user_type') == 'A' ? 'All' : 'My Task Only',
            },

            manager_code: { fieldKey: '' },
            property_code: { fieldKey: '', field_key: '', field_value: '', fieldValue: '' },
            lease_code: { fieldKey: '', field_value: '', fieldValue: '' },
            version_id: null,
            property_list: [],
            lease_list: [],
            manager_list: [],
            lease_main_list: [],
            error_msg: [],
            search_type: 0,
            editor_source: '',
            source_code: undefined,
            read_only: this.initialReadOnly,
            page_form_type: this.initialPageFormType,
            window_size: {
                x: 0,
                y: 0,
            },
            responsive_show: true,
            loading_content_setting: false,
            loading_page_setting: false,
            lease_page_title:
                localStorage.getItem('user_type') == 'A' ? 'Charge Review Maintenance' : 'Charge and Rent Review',
            error_server_msg: {},
            error_server_msg2: [],
            edit_form: true,
            disable_form: false,
            view_list: this.viewpage,
            edit_charge_id: '',
            edit_charge_status: '',
            rejected_comment: '',
            u_type: localStorage.getItem('user_type'),
            calculateCount: 0,
            currency_symbol: '$',
            area_unit: 'm&sup2;',
        };
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'lease_profile']),
    },
    mounted() {
        let recaptchaScript2 = document.createElement('script');
        recaptchaScript2.setAttribute('src', 'https://cdn.tiny.cloud/1/' + this.tinymce + '/tinymce/7/tinymce.min.js');
        document.head.appendChild(recaptchaScript2);

        setTimeout(function () {
            tinymce.init({ selector: '#letterTemplateBody' });
        }, 1000);

        this.loadManagerList();
        this.loadPropertyList();
        this.loadLeaseList();
        this.loadListCharge();
        this.loadCountryDefaults();

        if (this.initialPropertyCode) {
            let dis = this;
            let propKey = dis.initialPropertyCode;
            setTimeout(function () {
                dis.property_code = { field_key: propKey };
                $.each(dis.property_list, function (i, e) {
                    if (e['field_key'] == dis.property_code.field_key) dis.property_code.field_value = e['field_value'];
                });
            }, 1500);
            dis.initialPropertyCode = null;
        }

        if (this.initialPropertyCode) {
            this.version_id = this.initialVersionId;
        }
    },
    methods: {
        ...mapMutations(['SET_PROPERTY_CODE', 'SET_LEASE_CODE']),
        delete_review: function (primary, property) {
            let dis = this;
            Swal.fire({
                text: 'Are you sure you want to delete this rent review item?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ok',
                cancelButtonText: 'Cancel',
            }).then((result) => {
                if (result.value) {
                    var form_data = new FormData();
                    form_data.append('charge_id', primary);
                    form_data.append('property_code', property);

                    this.$api
                        .post(this.cirrus8_api_url + 'api/temp/lease/update/deleteRentReview', form_data)
                        .then((response) => {
                            this.loadListCharge();
                        });
                }
            });
        },
        customSort: function (items, index, isDesc) {
            items.sort((a, b) => {
                if (
                    index[0] == 'created' ||
                    index[0] == 'updated' ||
                    index[0] == 'processed' ||
                    index[0] == 'diary_date'
                ) {
                    let dataA = a[index].split('/');
                    let dataB = b[index].split('/');

                    if (!isDesc[0]) {
                        return (
                            new Date(dataB[2] + '-' + dataB[1] + '-' + dataB[0]) -
                            new Date(dataA[2] + '-' + dataA[1] + '-' + dataA[0])
                        );
                    } else {
                        return (
                            new Date(dataA[2] + '-' + dataA[1] + '-' + dataA[0]) -
                            new Date(dataB[2] + '-' + dataB[1] + '-' + dataB[0])
                        );
                    }
                } else {
                    if (typeof a[index] !== 'undefined' && a[index]) {
                        if (!isDesc[0]) {
                            return a[index].toLowerCase().localeCompare(b[index].toLowerCase());
                        } else {
                            return b[index].toLowerCase().localeCompare(a[index].toLowerCase());
                        }
                    }
                }
            });
            return items;
        },

        nameWithDash({ field_key, field_value }) {
            if (!field_value) {
                return 'Please select...';
            }
            return `${field_key} — ${field_value}`;
        },
        getValueInList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((m) => m.value === param1);
                if (filtered.length > 0) {
                    return filtered[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        fieldKey: '',
                        fieldValue: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        loadPropertyList: function () {
            let form_data = new FormData();
            form_data.append('page_source', 'leaseFormTemplate');
            if (typeof this.manager_code.field_key != 'undefined')
                form_data.append('manager_code', this.manager_code.field_key);
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-dropdown-list', form_data).then((response) => {
                this.property_list = response.data.data;
                if (this.initialPropertyCode) {
                    this.property_code = this.getValueInList(this.initialPropertyCode, this.property_list);
                }
            });
        },
        loadLeaseList: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            let form_data = new FormData();
            form_data.append('page_source', 'leaseFormTemplate');
            if (typeof this.property_code.field_key != 'undefined')
                form_data.append('property_code', this.property_code.field_key);
            form_data.append('no_load', true);
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('current_only', true);
            this.$api.post('load-property-lease-list', form_data).then((response) => {
                this.lease_list = response.data.data;
                document.getElementById('ngLoader-UI').style.display = 'none';
                if (this.initialLeaseCode) {
                    this.lease_code = {
                        fieldKey: this.initialLeaseCode,
                        field_key: this.initialLeaseCode,
                        field_value: this.initialLeaseCode,
                        fieldValue: this.initialLeaseCode,
                    };
                }
            });
        },
        loadManagerList: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            let form_data = new FormData();
            form_data.append('page_source', 'leaseFormTemplate');
            form_data.append('no_load', true);
            this.$api.post(this.cirrus8_api_url + 'api/loadPortfolioManagersList', form_data).then((response) => {
                this.manager_list = response.data.pm_data;
                document.getElementById('ngLoader-UI').style.display = 'none';
            });
        },
        selectLease: function (fieldKey, fieldValue, leaseAddress, fieldGroup) {
            this.lease_code = {
                fieldKey: fieldKey,
                fieldValue: fieldValue,
                leaseAddress: leaseAddress,
                fieldGroup: fieldGroup,
                field_key: fieldKey,
                field_value: fieldValue,
                lease_address: leaseAddress,
                field_group: fieldGroup,
            };
        },
        onResize() {
            this.window_size = { x: window.innerWidth, y: window.innerHeight };
        },
        getLeaseCode: function (value) {
            let lease_code = value.lease_code;
            let lease_name = value.lease_name;
            let label = lease_code + ' - ' + lease_name;
            // this.loadLeaseList();
            this.lease_code = {
                fieldKey: lease_code,
                field_key: lease_code,
                fieldValue: label,
                field_value: label,
                value: lease_code,
                label: label,
            };
        },

        reset_filter: function (reset_property = true) {
            this.lease_code = { field_key: '' };
            this.lease_list = [];
            if (reset_property) {
                this.property_code = { field_key: '' };
                this.property_list = [];
                this.SET_PROPERTY_CODE('');
            }
            this.manager_code = { field_key: '' };
            this.SET_LEASE_CODE('');
            this.loadManagerList();
            this.loadPropertyList();
            this.loadLeaseList();
        },
        changeSearchType: function (type) {
            switch (type) {
                case 'manager_code':
                    this.lease_code = { fieldKey: '' };
                    this.lease_list = [];
                    this.property_code = { fieldKey: '' };
                    this.property_list = [];
                    this.SET_PROPERTY_CODE('');
                    this.SET_LEASE_CODE('');

                    this.loadPropertyList();
                    break;
                case 'property_code':
                    if (this.search_type != 1) {
                        this.lease_code = { fieldKey: '' };
                        this.lease_list = [];
                        this.SET_PROPERTY_CODE(this.property_code.field_key);
                        this.loadLeaseList();
                    }
                    break;
                case 'lease_code':
                    if (this.search_type == 1) {
                        let property_code = this.lease_code.lease_property;
                        let property_name = this.lease_code.lease_property_name;
                        this.property_code = {
                            fieldKey: property_code,
                            field_key: property_code,
                            fieldValue: property_name,
                            field_value: property_name,
                            value: property_code,
                            label: property_name,
                        };

                        this.property_list = [];
                        this.property_list = [
                            {
                                fieldKey: property_code,
                                field_key: property_code,
                                fieldValue: property_name,
                                field_value: property_name,
                                value: property_code,
                                label: property_name,
                            },
                        ];

                        this.SET_PROPERTY_CODE(this.lease_code.lease_property);
                    }

                    this.SET_LEASE_CODE(this.lease_code.field_key);
                    break;
            }
        },
        closeReview: function (index, rent_index, review) {
            Swal.fire({
                title: 'Are you sure?',
                text: 'This will permanently close the rent review - are you sure?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ok',
                cancelButtonText: 'Cancel',
            }).then((result) => {
                if (result.value) {
                    document.getElementById('ngLoader-UI').style.display = 'block';
                    let form_data = new FormData();
                    form_data.append('property_code', this.property_code.field_key);
                    form_data.append('lease_code', this.lease_code.field_key);
                    form_data.append('rent_review_id', review);
                    this.$api
                        .post(this.cirrus8_api_url + 'api/temp/lease/update/completeRentReview', form_data)
                        .then((response) => {
                            this.$delete(this.lease_charge_chosen[index].unit_rent_review_list, rent_index);
                            document.getElementById('ngLoader-UI').style.display = 'none';
                        });
                }
            });
        },
        selectRentReview: function (index, rent_index) {
            this.charge_billing_date = this.lease_charge_chosen[index].unit_rent_review_list[rent_index].review_date;
            if (this.lease_charge_chosen[index].unit_rent_review_list[rent_index].review_desc == 'CPI') {
                this.lease_charge_chosen[index].charge_method = 4;
                this.lease_charge_chosen[index].charge_amount = '';
                this.calculate(index);
            }
        },
        resetRentReviewCharge: function () {
            this.lease_charge_chosen = [];
            this.selectedLeaseCharge = [];
            this.rejected_comment = '';

            let dis = this;
            if (this.property_code.field_key && this.lease_code.field_key) {
                document.getElementById('ngLoader-UI').style.display = 'block';
                let form_data = new FormData();
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('lease_code', this.lease_code.field_key);
                this.$api.post(this.cirrus8_api_url + 'api/loadChargeRentReview', form_data).then((response) => {
                    this.unit_id = response.data.unit_id;
                    this.unit_area = response.data.unit_area;
                    this.availableLeaseCharge = [{ fieldGroupValues: response.data.data }];
                    this.$refs.refChargeModel.option1 = [{ fieldGroupValues: response.data.data }];
                    this.$refs.refChargeModel.option2 = [];
                    this.$refs.refChargeModel.finalValue = [];
                    document.getElementById('ngLoader-UI').style.display = 'none';
                });
            }
        },
        selectCharge: function () {
            this.error_main_form = [];
            this.success_notification = 0;
            this.download_pdf = '';
            if (this.selectedLeaseCharge.length && this.selectedLeaseCharge.length < 6) {
                document.getElementById('ngLoader-UI').style.display = 'block';
                this.formData.property_code = this.property_code.field_key;
                this.formData.lease_code = this.lease_code.field_key;
                this.formData.unit_code = this.unit_id;
                this.formData.selected_lease_charge = this.selectedLeaseCharge;

                //axios.post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadChargeDetails', this.formData)
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadChargeDetails', this.formData)
                    .then((response) => {
                        document.getElementById('ngLoader-UI').style.display = 'none';
                        this.lease_charge_chosen = response.data.data;
                        this.charge_billing_date = response.data.charge_billing_date;
                        this.next_billing_date = response.data.next_billing_date;
                        this.expense_account = response.data.expense_account;
                        this.has_rent_review_fee = response.data.has_rent_review_fee;
                        this.rent_review_amount = response.data.rent_rev_amount;
                        this.rent_review_description = response.data.rent_rev_desc;
                        this.rent_review_account.field_key = response.data.rent_rev_exp_account;
                        this.rent_review_type_list = response.data.rent_review_type_list;
                        this.letter_template_list = response.data.letter_template_list;
                        this.rent_review_date = response.data.today;
                        this.close_diary_list = response.data.diary_items;
                        this.cpi_index_list = response.data.cpi_index_list;
                        this.applicable_cpi_index_list = response.data.applicable_cpi_index_list;
                        this.today = response.data.today;

                        let dis = this;
                        if (response.data.expense_account) {
                            $.each(this.expense_account, function (i, e) {
                                $.each(e['field_group_values'], function (i2, e2) {
                                    if (e2['field_key'].trim() == dis.rent_review_account.field_key.trim())
                                        dis.rent_review_account.field_value = e2['field_value'];
                                });
                            });
                        }
                    });
            } else {
                if (this.selectedLeaseCharge.length > 5)
                    Swal.fire({
                        icon: 'error',
                        title: 'You cannot select more than five charges at a time.',
                        html: '',
                    });
                this.lease_charge_chosen = [];
                this.with_backcharge = 0;
            }
        },
        addFutureRentReview: function () {
            this.futureRentReviewValidation = '';

            if (!this.rent_review_type) this.futureRentReviewValidation = 'You have not selected a review type.';
            else if (!this.rent_review_date) this.futureRentReviewValidation = 'You have not entered a valid date.';
            else {
                document.getElementById('ngLoader-UI').style.display = 'block';
                let form_data = new FormData();
                form_data.append('property_code', this.property_code.field_key);
                form_data.append('lease_code', this.lease_code.field_key);
                form_data.append('review_type', this.rent_review_type.field_key);
                form_data.append('review_date', this.rent_review_date);
                form_data.append('review_diarise', this.rent_review_diaries);
                form_data.append('charge_id', this.rent_review_charge_id);
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/update/addRentReview', form_data)
                    .then((response) => {
                        //MUST BE ID OF ADD FUTURE RENT REVIEW
                        this.lease_charge_chosen[this.rent_review_index].unit_rent_review_list.push({
                            review_id: 99,
                            review_type: this.rent_review_type.fieldValue,
                            review_date: this.rent_review_date,
                        });
                        this.rent_modal = false;
                        document.getElementById('ngLoader-UI').style.display = 'none';
                    });
            }
        },
        calculate: function (index_charge) {
            if (this.edit_charge_status == 1) return;

            if (
                (this.lease_charge_chosen[index_charge].charge_amount &&
                    this.lease_charge_chosen[index_charge].charge_method != 4) ||
                (this.lease_charge_chosen[index_charge].cpi_index_current &&
                    this.lease_charge_chosen[index_charge].charge_method == 4)
            ) {
                this.calculateCount += 1;

                document.getElementById('ngLoader-UI').style.display = 'block';
                this.formData.property_code = this.property_code.field_key;
                this.formData.lease_code = this.lease_code.field_key;
                this.formData.unit_code = this.unit_id;
                this.formData.unit_area = this.unit_area;
                this.formData.charge_from_date = this.charge_billing_date;
                this.formData.next_billing_date = this.next_billing_date;
                this.formData.unit_charge = this.lease_charge_chosen[index_charge].unit_charge_serial;
                this.formData.charge_amount = this.lease_charge_chosen[index_charge].charge_amount;
                this.formData.charge_method = this.lease_charge_chosen[index_charge].charge_method;
                this.formData.cpi_old = this.lease_charge_chosen[index_charge].cpi_index_old;
                this.formData.cpi_new = this.lease_charge_chosen[index_charge].cpi_index_current;
                this.formData.cpi_interest = this.lease_charge_chosen[index_charge].cpi_index_increase;
                this.formData.lease_charge = this.lease_charge_chosen[index_charge];

                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadChargeComputation', this.formData)
                    .then((response) => {
                        if (this.calculateCount == 1) document.getElementById('ngLoader-UI').style.display = 'none';

                        this.calculateCount -= 1;

                        this.error_main_form = response.data.error_msg;
                        if (!this.error_main_form.length) {
                            this.lease_charge_chosen[index_charge].charge_old_amount =
                                response.data.data.charge_old_amount;
                            this.lease_charge_chosen[index_charge].charge_old_amount_label =
                                response.data.data.charge_old_amount_label;
                            this.lease_charge_chosen[index_charge].charge_old_area = response.data.data.charge_old_area;
                            this.lease_charge_chosen[index_charge].charge_old_month =
                                response.data.data.charge_old_month;
                            this.lease_charge_chosen[index_charge].charge_new_amount =
                                response.data.data.charge_new_amount;
                            this.lease_charge_chosen[index_charge].charge_new_amount_label =
                                response.data.data.charge_new_amount_label;
                            this.lease_charge_chosen[index_charge].charge_new_month =
                                response.data.data.charge_new_month;
                            this.lease_charge_chosen[index_charge].charge_new_area = response.data.data.charge_new_area;
                            this.lease_charge_chosen[index_charge].charge_increase_area =
                                response.data.data.charge_increase_area;
                            this.lease_charge_chosen[index_charge].charge_increase_amount =
                                response.data.data.charge_increase_amount;
                            this.lease_charge_chosen[index_charge].charge_increase_month =
                                response.data.data.charge_increase_month;

                            this.lease_charge_chosen[index_charge].interim_from_date =
                                response.data.data.interim_from_date;
                            this.lease_charge_chosen[index_charge].interim_to_date = response.data.data.interim_to_date;
                            this.lease_charge_chosen[index_charge].is_backcharge = response.data.data.is_backcharge;

                            this.lease_charge_chosen[index_charge].back_charge_amount =
                                response.data.data.back_charge_amount;
                            this.lease_charge_chosen[index_charge].back_charge_tax = response.data.data.back_charge_tax;
                            this.lease_charge_chosen[index_charge].back_charge_total =
                                response.data.data.back_charge_total;

                            this.lease_charge_chosen[index_charge].calculation = 1;

                            this.with_backcharge = 0;
                            let dis = this.with_backcharge;
                            $.each(this.lease_charge_chosen, function (i, e) {
                                dis = parseFloat(dis) + parseFloat(e.is_backcharge);
                            });
                            this.with_backcharge = dis;
                            if (dis) {
                                this.lease_charge_chosen[index_charge].invoice_date = this.next_billing_date;
                            }
                            this.currency_symbol = response.data.currency_symbol;
                            this.area_unit = response.data.area_unit;

                            if (this.with_backcharge && !this.edit_charge_id)
                                this.send_on_approval = response.data.send_on_approval;
                        }
                    });
            } else {
                this.lease_charge_chosen[index_charge].calculation = 0;
            }
        },
        selectCPI: function (index_charge, remove_val) {
            if (remove_val) this.lease_charge_chosen[index_charge].cpi_index_available = '';
            if (remove_val) this.lease_charge_chosen[index_charge].cpi_index_old = '';
            if (remove_val) this.lease_charge_chosen[index_charge].cpi_index_current = '';
            if (remove_val) this.lease_charge_chosen[index_charge].cpi_index_increase = '';
            let index = this.lease_charge_chosen[index_charge].cpi_index_name.field_key;
            this.lease_charge_chosen[index_charge].applicable_cpi_index_list = this.applicable_cpi_index_list[index];
        },
        selectApplicable: function (index_charge) {
            let index = this.lease_charge_chosen[index_charge].cpi_index_available.field_key;
            this.lease_charge_chosen[index_charge].cpi_index_current =
                this.lease_charge_chosen[index_charge].cpi_index_available.cpi;
            this.lease_charge_chosen[index_charge].cpi_index_old = '';

            var ages = parseInt(this.lease_charge_chosen[index_charge].cpi_ages) / 3;
            var ages_check = -1;

            for (let x = 0; x < this.lease_charge_chosen[index_charge].applicable_cpi_index_list.length; x++) {
                if (
                    this.lease_charge_chosen[index_charge].applicable_cpi_index_list[x].quarter ==
                        this.lease_charge_chosen[index_charge].cpi_index_available.quarter &&
                    parseFloat(this.lease_charge_chosen[index_charge].applicable_cpi_index_list[x].year) ==
                        parseFloat(this.lease_charge_chosen[index_charge].cpi_index_available.year)
                ) {
                    ages_check = 0;
                }

                if (ages_check >= 0) {
                    if (ages_check == ages) {
                        this.lease_charge_chosen[index_charge].cpi_index_old =
                            this.lease_charge_chosen[index_charge].applicable_cpi_index_list[x].cpi;
                    }

                    ages_check += 1;
                }
            }

            if (this.lease_charge_chosen[index_charge].cpi_index_old > 0) {
                this.lease_charge_chosen[index_charge].total_increase_cpi = (
                    (parseFloat(this.lease_charge_chosen[index_charge].cpi_index_current) /
                        parseFloat(this.lease_charge_chosen[index_charge].cpi_index_old)) *
                        100 -
                    100
                ).toFixed(2);
                this.lease_charge_chosen[index_charge].total_cpi = (
                    parseFloat(this.lease_charge_chosen[index_charge].total_increase_cpi) +
                    parseFloat(
                        this.lease_charge_chosen[index_charge].cpi_index_increase
                            ? this.lease_charge_chosen[index_charge].cpi_index_increase
                            : 0,
                    )
                ).toFixed(2);
            } else {
                this.lease_charge_chosen[index_charge].total_cpi = '';
                this.lease_charge_chosen[index_charge].total_increase_cpi = '';
            }
        },
        reComputeCPI: function (index_charge) {
            if (this.lease_charge_chosen[index_charge].cpi_index_old > 0) {
                this.lease_charge_chosen[index_charge].total_increase_cpi = (
                    (parseFloat(this.lease_charge_chosen[index_charge].cpi_index_current) /
                        parseFloat(this.lease_charge_chosen[index_charge].cpi_index_old)) *
                        100 -
                    100
                ).toFixed(2);
                this.lease_charge_chosen[index_charge].total_cpi = (
                    parseFloat(this.lease_charge_chosen[index_charge].total_increase_cpi) +
                    parseFloat(
                        this.lease_charge_chosen[index_charge].cpi_index_increase
                            ? this.lease_charge_chosen[index_charge].cpi_index_increase
                            : 0,
                    )
                ).toFixed(2);
            } else {
                this.lease_charge_chosen[index_charge].total_cpi = '';
                this.lease_charge_chosen[index_charge].total_increase_cpi = '';
            }
        },
        chooseTemplate: function () {
            if (this.letter_template) {
                document.getElementById('ngLoader-UI').style.display = 'block';
                this.formData.letter_template = this.letter_template;
                this.formData.property_code = this.property_code.field_key;
                this.formData.lease_code = this.lease_code.field_key;
                this.formData.charge_from_date = this.charge_billing_date;
                this.formData.lease_charge_chosen = this.lease_charge_chosen;

                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadTemplateRentReview', this.formData)
                    .then((response) => {
                        document.getElementById('ngLoader-UI').style.display = 'none';
                        this.download_file = response.data.letter_filename;
                        this.editorData = response.data.letter_template_body;

                        tinymce.remove('#letterTemplateBody');

                        if (!this.download_file) {
                            document.getElementById('letterTemplateBody').value =
                                String(response.data.letter_template_body) + '';

                            tinymce.init({
                                selector: '#letterTemplateBody',
                                plugins:
                                    (this.tinymceai == '1' ? 'ai' : '') +
                                    ' advcode advlist advtable  anchor autocorrect autolink autosave casechange charmap checklist codesample directionality editimage emoticons footnotes formatpainter image insertdatetime link linkchecker lists mergetags   nonbreaking pagebreak permanentpen powerpaste searchreplace table tableofcontents  tinymcespellchecker typography visualblocks visualchars wordcount',
                                menubar: 'edit insert view format tools',
                                toolbar:
                                    'undo redo spellcheckdialog | aidialog aishortcuts | blocks fontfamily fontsizeinput | bold italic underline forecolor backcolor | link image   | align lineheight checklist bullist numlist | indent outdent  | removeformat typography',
                                editable_root: true,
                                branding: false,
                                ai_request: (request, respondWith) => {
                                    const openAiOptions = {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            Authorization: `Bearer ` + this.openai,
                                        },
                                        body: JSON.stringify({
                                            model: 'gpt-3.5-turbo',
                                            temperature: 0.7,
                                            max_tokens: 800,
                                            messages: [{ role: 'user', content: request.prompt }],
                                        }),
                                    };
                                    respondWith.string((signal) =>
                                        window
                                            .fetch('https://api.openai.com/v1/chat/completions', {
                                                signal,
                                                ...openAiOptions,
                                            })
                                            .then(async (response) => {
                                                if (response) {
                                                    const data = await response.json();
                                                    if (data.error) {
                                                        throw new Error(`${data.error.type}: ${data.error.message}`);
                                                    } else if (response.ok) {
                                                        // Extract the response content from the data returned by the API
                                                        // return data?.choices[0]?.message?.content?.trim();
                                                        return data.choices[0].message.content.trim();
                                                    }
                                                } else {
                                                    throw new Error('Failed to communicate with the ChatGPT API');
                                                }
                                            }),
                                    );
                                },
                                height: 1056,
                                width: '100%', //816
                                toolbar_sticky: false,
                                autosave_restore_when_empty: true,
                                spellchecker_active: true,
                                spellchecker_language: this.country_code == 'AU' ? 'en_AU' : 'en_GB',
                                spellchecker_languages:
                                    'US English=en_US,UK English=en_GB,Danish=da,Dutch=nl,Finnish=fi,French=fr,German=de,Italian=it,Norwegian Bokmål=nb_NO,Norwegian Nynorsk=nn,Brazilian Portuguese=pt_BR,Portuguese=pt,Portuguese (Portugal)=pt_PT,Spanish=es,Swedish=sv,Swedish (Finland)=sv_FI,Afrikaans (South Africa)=af_ZA,English (Australia)=en_AU,English (Canada)=en_CA,English (United Kingdom)=en_GB,English (United States)=en_US,Medical English (US)=en_US-medical,Medical English (UK)=en_GB-medical,Maori (New Zealand)=mi_NZ',
                                typography_langs: ['en-US'],
                                typography_default_lang: 'en-US',
                                mergetags_list: [
                                    {
                                        title: 'Date Formats',
                                        menu: [
                                            { value: 'dateFormat1', title: 'dateFormat1' },
                                            { value: 'dateFormat2', title: 'dateFormat2' },
                                            { value: 'dateFormat3', title: 'dateFormat3' },
                                            { value: 'dateFormat4', title: 'dateFormat4' },
                                        ],
                                    },
                                    {
                                        title: 'Amount',
                                        menu: [
                                            { value: 'rentReviewType', title: 'rentReviewType' },
                                            { value: 'rentReviewType2', title: 'rentReviewType2' },
                                            { value: 'rentReviewType3', title: 'rentReviewType3' },
                                            { value: 'rentReviewType4', title: 'rentReviewType4' },
                                            { value: 'rentReviewType5', title: 'rentReviewType5' },
                                            { value: 'chargeNewAmountMonthly', title: 'chargeNewAmountMonthly' },
                                            { value: 'chargeNewAmountMonthly2', title: 'chargeNewAmountMonthly2' },
                                            { value: 'chargeNewAmountMonthly3', title: 'chargeNewAmountMonthly3' },
                                            { value: 'chargeNewAmountMonthly4', title: 'chargeNewAmountMonthly4' },
                                            { value: 'chargeNewAmountMonthly5', title: 'chargeNewAmountMonthly5' },
                                            { value: 'chargeNewAmountAnnual', title: 'chargeNewAmountAnnual' },
                                            { value: 'chargeNewAmountAnnual2', title: 'chargeNewAmountAnnual2' },
                                            { value: 'chargeNewAmountAnnual3', title: 'chargeNewAmountAnnual3' },
                                            { value: 'chargeNewAmountAnnual4', title: 'chargeNewAmountAnnual4' },
                                            { value: 'chargeNewAmountAnnual5', title: 'chargeNewAmountAnnual5' },
                                            { value: 'chargeOldAmountMonthly', title: 'chargeOldAmountMonthly' },
                                            { value: 'chargeOldAmountMonthly2', title: 'chargeOldAmountMonthly2' },
                                            { value: 'chargeOldAmountMonthly3', title: 'chargeOldAmountMonthly3' },
                                            { value: 'chargeOldAmountMonthly4', title: 'chargeOldAmountMonthly4' },
                                            { value: 'chargeOldAmountMonthly5', title: 'chargeOldAmountMonthly5' },
                                            { value: 'psmpaAmount', title: 'psmpaAmount' },
                                            { value: 'psmpaAmount2', title: 'psmpaAmount2' },
                                            { value: 'psmpaAmount3', title: 'psmpaAmount3' },
                                            { value: 'psmpaAmount4', title: 'psmpaAmount4' },
                                            { value: 'psmpaAmount5', title: 'psmpaAmount5' },
                                            { value: 'oldCPI', title: 'oldCPI' },
                                            { value: 'oldCPI2', title: 'oldCPI2' },
                                            { value: 'oldCPI3', title: 'oldCPI3' },
                                            { value: 'oldCPI4', title: 'oldCPI4' },
                                            { value: 'oldCPI5', title: 'oldCPI5' },
                                            { value: 'newCPI', title: 'newCPI' },
                                            { value: 'newCPI2', title: 'newCPI2' },
                                            { value: 'newCPI3', title: 'newCPI3' },
                                            { value: 'newCPI4', title: 'newCPI4' },
                                            { value: 'newCPI5', title: 'newCPI5' },
                                            { value: 'additionalPercentage', title: 'additionalPercentage' },
                                            { value: 'additionalPercentage2', title: 'additionalPercentage2' },
                                            { value: 'additionalPercentage3', title: 'additionalPercentage3' },
                                            { value: 'additionalPercentage4', title: 'additionalPercentage4' },
                                            { value: 'additionalPercentage5', title: 'additionalPercentage5' },
                                            { value: 'percentIncrease', title: 'percentIncrease' },
                                            { value: 'percentIncrease2', title: 'percentIncrease2' },
                                            { value: 'percentIncrease3', title: 'percentIncrease3' },
                                            { value: 'percentIncrease4', title: 'percentIncrease4' },
                                            { value: 'percentIncrease5', title: 'percentIncrease5' },
                                            { value: 'CPIIncrease', title: 'CPIIncrease' },
                                            { value: 'CPIIncrease2', title: 'CPIIncrease2' },
                                            { value: 'CPIIncrease3', title: 'CPIIncrease3' },
                                            { value: 'CPIIncrease4', title: 'CPIIncrease4' },
                                            { value: 'CPIIncrease5', title: 'CPIIncrease5' },
                                        ],
                                    },
                                    {
                                        title: 'Property Manager',
                                        menu: [
                                            { value: 'propertyManagerID', title: 'propertyManagerID' },
                                            { value: 'propertyManagerName', title: 'propertyManagerName' },
                                            { value: 'propertyManagerTitle', title: 'propertyManagerTitle' },
                                            { value: 'propertyManagerEmail', title: 'propertyManagerEmail' },
                                        ],
                                    },
                                    {
                                        title: 'Property',
                                        menu: [
                                            { value: 'propertyCode', title: 'propertyCode' },
                                            { value: 'propertyName', title: 'propertyName' },
                                            { value: 'propertyStreet', title: 'propertyStreet' },
                                            { value: 'propertyCity', title: 'propertyCity' },
                                            { value: 'propertyState', title: 'propertyState' },
                                            { value: 'propertyPostCode', title: 'propertyPostCode' },
                                            { value: 'principalOwner', title: 'principalOwner' },
                                            { value: 'principalOwnerABN', title: 'principalOwnerABN' },
                                        ],
                                    },
                                    {
                                        title: 'Lease',
                                        menu: [
                                            { value: 'leaseName', title: 'leaseName' },
                                            { value: 'leaseDescription', title: 'leaseDescription' },
                                            { value: 'leaseCode', title: 'leaseCode' },
                                            { value: 'leaseStreet', title: 'leaseStreet' },
                                            { value: 'leaseCity', title: 'leaseCity' },
                                            { value: 'leaseState', title: 'leaseState' },
                                            { value: 'leasePostCode', title: 'leasePostCode' },
                                            { value: 'leaseExpiry', title: 'leaseExpiry' },
                                            { value: 'leaseCommencement', title: 'leaseCommencement' },
                                            { value: 'leaseOption', title: 'leaseOption' },
                                            { value: 'nextTermCommencement', title: 'nextTermCommencement' },
                                            { value: 'CRN', title: 'CRN' },
                                        ],
                                    },
                                    {
                                        title: 'Tenant',
                                        menu: [
                                            { value: 'tenantName', title: 'tenantName' },
                                            { value: 'tenantStreet', title: 'tenantStreet' },
                                            { value: 'tenantCity', title: 'tenantCity' },
                                            { value: 'tenantState', title: 'tenantState' },
                                            { value: 'tenantPostCode', title: 'tenantPostCode' },
                                        ],
                                    },
                                    {
                                        title: 'Miscellaneous',
                                        menu: [
                                            { value: 'chargeFromDate', title: 'chargeFromDate' },
                                            { value: 'contactName', title: 'contactName' },
                                            { value: 'contactSalutation', title: 'contactSalutation' },
                                            { value: 'nextRentReviewDate', title: 'nextRentReviewDate' },
                                            { value: 'chargeDescription', title: 'chargeDescription' },
                                            { value: 'chargeDescription2', title: 'chargeDescription2' },
                                            { value: 'chargeDescription3', title: 'chargeDescription3' },
                                            { value: 'chargeDescription4', title: 'chargeDescription4' },
                                            { value: 'chargeDescription5', title: 'chargeDescription5' },
                                            { value: 'CPIindexName', title: 'CPIindexName' },
                                            { value: 'CPIindexName2', title: 'CPIindexName2' },
                                            { value: 'CPIindexName3', title: 'CPIindexName3' },
                                            { value: 'CPIindexName4', title: 'CPIindexName4' },
                                            { value: 'CPIindexName5', title: 'CPIindexName5' },
                                            { value: 'CPIindexQuarter', title: 'CPIindexQuarter' },
                                            { value: 'CPIindexQuarter2', title: 'CPIindexQuarter2' },
                                            { value: 'CPIindexQuarter3', title: 'CPIindexQuarter3' },
                                            { value: 'CPIindexQuarter4', title: 'CPIindexQuarter4' },
                                            { value: 'CPIindexQuarter5', title: 'CPIindexQuarter5' },
                                            { value: 'nextInsuranceExpiryDate', title: 'nextInsuranceExpiryDate' },
                                            { value: 'nextInsuranceExpiryType', title: 'nextInsuranceExpiryType' },
                                            { value: 'nextInsuranceExpiryNote', title: 'nextInsuranceExpiryNote' },
                                        ],
                                    },
                                ],
                                mergetags_prefix: '%',
                                mergetags_suffix: '%',
                                content_style: `
                    body {
                      background: #fff;
                      font-family: Ariel;
                    }

                    /* Apply page-like styling */
                    @media (min-width: 840px) {
                      html {
                        background: #eceef4;
                        min-height: 100%;
                      }

                      body {
                        background-color: #fff;
                        box-shadow: 0 0 4px rgba(0, 0, 0, .15);
                        box-sizing: border-box;
                        margin: 1rem auto 0;
                        max-width: 830px;
                        min-height: calc(100vh - 1rem);
                        padding: 8rem;
                      }

                      .mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {
                        margin-left: 20px;
                      }
                    }
                  `,
                            });
                        }
                    });
            } else {
            }
        },
        printDownload(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;

            let blob = new Blob([this.printS2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            a.download = name + '.' + format;
            a.click();
        },
        printS2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
        downloadFile: function () {
            tinyMCE.triggerSave();
            if (!this.download_file) this.editorData = $('#letterTemplateBody').val();

            if (this.letter_template) {
                document.getElementById('ngLoader-UI').style.display = 'block';
                this.formData.letter_template = this.letter_template.fieldKey;
                this.formData.editorData = this.editorData;
                this.formData.property_code = this.property_code.field_key;
                this.formData.lease_code = this.lease_code.field_key;
                this.formData.charge_from_date = this.charge_billing_date;
                this.formData.lease_charge_chosen = this.lease_charge_chosen;
                this.formData.include_letterhead = this.include_letterhead;

                let dis = this;
                this.$api
                    .post(this.cirrus8_api_url + 'api/temp/lease/fetch/downloadTemplateRentReview', this.formData)
                    .then((response) => {
                        dis.formData.editor_data = response.data.editor_data;
                        dis.formData.filePath = response.data.path;
                        dis.formData.forSaving = 0;
                        let file_name = response.data.name;
                        if (!this.download_file) {
                            let url =
                                this.$assetDomain +
                                '?action=downloadChargeReview&module=ar&command=generateTaxInvoice&leaseID=' +
                                dis.lease_code.field_key +
                                '&propertyID=' +
                                dis.property_code.field_key +
                                '&dueDate=' +
                                dis.due_date +
                                '';
                            axios
                                .post(url, dis.formData, { headers: { 'Content-Type': 'application/json' } })
                                .then((response) => {
                                    let file_base_64 = response.data;
                                    dis.printDownload(file_base_64, file_name, 'pdf');
                                    document.getElementById('ngLoader-UI').style.display = 'none';
                                });
                        } else {
                            let file_base_64 = response.data.base64;
                            dis.printDownload(file_base_64, file_name, 'pdf');
                            document.getElementById('ngLoader-UI').style.display = 'none';
                        }
                    });
            } else {
            }
        },
        submitFile: function () {
            tinyMCE.triggerSave();
            if (!this.download_file && this.letter_template) this.editorData = $('#letterTemplateBody').val();

            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.rejected_comment = this.rejected_comment;
            this.formData.group_charge_id = this.edit_charge_id;
            this.formData.status = this.edit_charge_status;
            if (this.letter_template && this.letter_template.fieldKey)
                this.formData.letter_template = this.letter_template.fieldKey;
            else this.formData.letter_template = '';
            if (this.letter_template && this.letter_template.fieldKey) this.formData.editorData = this.editorData;
            else this.formData.editorData = '';
            this.formData.property_code = this.property_code.field_key;
            this.formData.lease_code = this.lease_code.field_key;
            this.formData.charge_from_date = this.charge_billing_date;
            this.next_billing_date = this.next_billing_date;
            this.formData.lease_charge_chosen = this.lease_charge_chosen;
            this.formData.close_checkbox_diary = this.close_diary_list;
            this.formData.lease_charge = this.lease_charge_chosen;
            this.formData.unit_code = this.unit_id;
            this.formData.generate_interim = this.generate_interim;
            this.formData.send_on_approval = this.send_on_approval;

            this.formData.rent_review_description = this.rent_review_description;
            this.formData.rent_review_amount = this.rent_review_amount;
            this.formData.rent_review_account = this.rent_review_account.field_key;
            this.formData.due_date = this.due_date;
            this.formData.show_rent_review_fee = this.show_rent_review_fee;
            this.formData.email_to_me = this.email_to_me;
            this.formData.email_to_tenant = this.email_to_tenant;
            this.formData.include_letterhead = this.include_letterhead;

            let dis = this;
            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/update/submitRentReview', this.formData)
                .then((response2) => {
                    this.error_main_form = response2.data.error_msg;
                    if (!this.error_main_form.length) {
                        this.success_notification = 1;
                        if (this.generate_interim) {
                            let url =
                                '?action=generateTempChargeReview&module=ar&command=generateTaxInvoice&leaseID=' +
                                dis.lease_code.field_key +
                                '&propertyID=' +
                                dis.property_code.field_key +
                                '&dueDate=' +
                                dis.due_date +
                                '';
                            axios.get(url).then((response) => {
                                this.download_pdf = response.data;
                                document.getElementById('ngLoader-UI').style.display = 'none';
                            });
                        } else {
                            document.getElementById('ngLoader-UI').style.display = 'none';
                        }

                        if (this.letter_template && !this.download_file) {
                            this.letter_template = '';
                            dis.formData.editor_data = response2.data.editor_data;
                            dis.formData.filePath = response2.data.path;
                            dis.formData.forSaving = 1;
                            let file_name = response2.data.name;
                            let url =
                                this.$assetDomain + '?action=downloadChargeReview&module=ar&command=generateTaxInvoice';
                            axios
                                .post(url, dis.formData, { headers: { 'Content-Type': 'application/json' } })
                                .then((response) => {
                                    document.getElementById('ngLoader-UI').style.display = 'none';
                                });
                        }

                        this.formData.letter_template = '';
                        this.lease_charge_chosen = [];
                        this.edit_charge_id = '';
                    } else {
                        document.getElementById('ngLoader-UI').style.display = 'none';
                    }

                    let container = document.getElementById('main_container');
                    container.scrollTop = 0;
                });
        },
        gotoNotes: function () {
            window.open(
                '?module=leases&command=lease_summary_page&property_code=' +
                    this.property_code.field_key +
                    '&lease_code=' +
                    this.lease_code.field_key,
                '_blank',
            );
        },
        loadListCharge: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.from_date = this.from_date_filter;
            this.formData.to_date = this.to_date_filter;
            this.formData.status =
                typeof this.status_filter.fieldKey != 'undefined'
                    ? this.status_filter.fieldKey
                    : localStorage.getItem('user_type') == 'A'
                      ? 0
                      : 'All';
            this.formData.property_manager_filter = this.property_manager_filter.fieldKey;

            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadListChargeReview', this.formData)
                .then((response) => {
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    this.review_list = response.data.review_list;
                    this.to_date_filter = response.data.to_date_filter;
                    this.from_date_filter = response.data.from_date_filter;
                    this.status_list = response.data.status_list;
                    this.property_manager_list = response.data.property_manager_list;
                });
        },
        loadSelectedCharges: function (charge_id) {
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.charge_id = charge_id;

            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/fetch/loadSelectedChargeDetails', this.formData)
                .then((response) => {
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    this.view_list = 0;
                    this.property_code.field_key = response.data.property_code;
                    this.lease_code.field_key = response.data.lease_code;
                    let dis = this;
                    $.each(this.property_list, function (i, e) {
                        if (e['field_key'] == dis.property_code.field_key)
                            dis.property_code.field_value = e['field_value'];
                    });
                    $.each(this.lease_list, function (i, e) {
                        if (
                            e['field_key'] == dis.lease_code.field_key &&
                            e['lease_property'] == dis.property_code.field_key
                        )
                            dis.lease_code.field_value = e['field_value'];
                    });

                    this.rejected_comment = response.data.rejected_comment;
                    this.edit_charge_status = response.data.status;
                    this.email_to_me = response.data.emailMe;
                    this.email_to_tenant = response.data.emailTenant;
                    this.filename_link = response.data.filename_link;
                    this.filename = response.data.filename;
                    this.letterTemplateBody = response.data.letterTemplateBody;
                    this.letterTemplate = response.data.letterTemplate;

                    this.edit_charge_id = charge_id;
                    if (this.edit_charge_status == 0 || this.edit_charge_status == 1 || this.u_type == 'A') {
                        this.edit_form = false;
                        this.disable_form = true;
                    } else {
                        this.edit_form = true;
                        this.disable_form = false;
                    }

                    this.unit_description = response.data.unit_description;
                    this.unit_id = response.data.unit_id;
                    this.unit_area = response.data.unit_area;
                    this.availableLeaseCharge = [{ fieldGroupValues: response.data.lease_charge_list }];
                    this.selectedLeaseCharge = response.data.lease_charge_list_chosen;

                    if (response.data.no_lease_charge) {
                        this.lease_charge_notification = 1;
                    }

                    this.lease_charge_chosen = response.data.data;
                    this.charge_billing_date = response.data.charge_billing_date;
                    this.next_billing_date = response.data.next_billing_date;
                    this.expense_account = response.data.expense_account;
                    this.has_rent_review_fee = response.data.has_rent_review_fee;
                    this.rent_review_type_list = response.data.rent_review_type_list;
                    this.letter_template_list = response.data.letter_template_list;
                    this.rent_review_date = response.data.today;
                    this.close_diary_list = response.data.diary_items;
                    this.cpi_index_list = response.data.cpi_index_list;
                    this.applicable_cpi_index_list = response.data.applicable_cpi_index_list;

                    this.show_rent_review_fee = response.data.adhoc_desc ? 1 : 0;
                    this.rent_review_description = response.data.adhoc_desc;
                    this.rent_review_amount = response.data.adhoc_amount;
                    this.rent_review_account.field_key = response.data.adhoc_account;

                    this.generate_interim = response.data.generate_interim;
                    this.due_date = response.data.due_date;
                    this.send_on_approval = response.data.send_on_approval;
                    this.tax_invoice_pdf = response.data.tax_invoice_pdf;

                    $.each(this.lease_charge_chosen, function (i, e) {
                        if (dis.lease_charge_chosen[i]['cpi_index_name']['field_key']) dis.selectCPI(i, false);

                        $.each(e['applicable_cpi_index_list'], function (i2, e2) {
                            if (dis.lease_charge_chosen[i]['cpi_index_available'].field_key == e2.field_key)
                                dis.lease_charge_chosen[i]['cpi_index_available'].field_value = e2.field_value;
                        });

                        if (i != 0) dis.calculate(i);
                    });

                    $.each(this.expense_account, function (i, e) {
                        $.each(e['field_group_values'], function (i2, e2) {
                            if (e2['field_key'].trim() == dis.rent_review_account.field_key.trim())
                                dis.rent_review_account.field_value = e2['field_value'];
                        });
                    });
                });
        },

        approveFile: function () {
            document.getElementById('ngLoader-UI').style.display = 'block';
            this.formData.rejected_comment = this.rejected_comment;
            this.formData.group_charge_id = this.edit_charge_id;
            this.formData.property_code = this.property_code.field_key;
            this.formData.lease_code = this.lease_code.field_key;
            this.formData.charge_from_date = this.charge_billing_date;
            this.next_billing_date = this.next_billing_date;
            this.formData.lease_charge_chosen = this.lease_charge_chosen;
            this.formData.unit_code = this.unit_id;

            this.formData.rent_review_description = this.rent_review_description;
            this.formData.rent_review_amount = this.rent_review_amount;
            this.formData.rent_review_account = this.rent_review_account.field_key;
            this.formData.send_on_approval = this.send_on_approval;

            let dis = this;
            this.$api
                .post(this.cirrus8_api_url + 'api/temp/lease/update/approveRentReview', this.formData)
                .then((response) => {
                    this.error_main_form = response.data.error_msg;
                    if (!this.error_main_form.length) {
                        if (dis.email_to_me == 1 || dis.email_to_tenant == 1) {
                            let url =
                                this.$assetDomain +
                                '?action=sendChargereview&module=leases&command=chargeReview_v2&leaseID=' +
                                dis.lease_code.field_key +
                                '&propertyID=' +
                                dis.property_code.field_key +
                                '&chargeId=' +
                                dis.edit_charge_id;
                            axios
                                .post(url, this.formData, { headers: { 'Content-Type': 'application/json' } })
                                .then((response2) => {
                                    dis.approve_notification = 1;
                                    dis.email_notification = response2.data;
                                    dis.view_list = 1;
                                    dis.edit_charge_id = '';
                                    document.getElementById('ngLoader-UI').style.display = 'none';
                                    dis.loadListCharge();
                                });
                        } else {
                            dis.approve_notification = 1;
                            dis.view_list = 1;
                            dis.edit_charge_id = '';
                            document.getElementById('ngLoader-UI').style.display = 'none';
                            dis.loadListCharge();
                        }
                    } else {
                        document.getElementById('ngLoader-UI').style.display = 'none';
                    }

                    let container = document.getElementById('main_container');
                    container.scrollTop = 0;
                    this.with_email_address = response.data.with_email_address;
                });
        },
        rejectFile: function () {
            if (this.rejected_comment) {
                document.getElementById('ngLoader-UI').style.display = 'block';
                let dis = this;
                let url =
                    this.$assetDomain +
                    '?action=reject&module=leases&command=chargeReviewProcess&leaseID=' +
                    dis.lease_code.field_key +
                    '&propertyID=' +
                    dis.property_code.field_key +
                    '&chargeReviewID=' +
                    dis.edit_charge_id +
                    '&rejectComment=' +
                    dis.rejected_comment;
                axios.post(url, this.formData, { headers: { 'Content-Type': 'application/json' } }).then((response) => {
                    dis.rejected_notification = 1;
                    dis.view_list = 1;
                    dis.edit_charge_id = '';
                    document.getElementById('ngLoader-UI').style.display = 'none';
                    dis.loadListCharge();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'You need to enter your feedback.',
                    html: '',
                });
            }
        },
        loadCountryDefaults: function () {
            let form_data = new FormData();
            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                let cdf = response.data.default;
                this.currency_symbol = cdf.currency_symbol;
                this.area_unit = cdf.area_unit;

                this.variable_outgoings_label = cdf.variable_outgoings;
                this.property_manager_label = this.ucwords(cdf.property_manager);
                this.trust_account_label = cdf.trust_account;
                this.trust_accountant_label = this.ucwords(cdf.trust_accountant);
                this.portfolio_manager_label = this.ucwords(cdf.portfolio_manager);
                this.country_code = cdf.country_code;
            });
        },
        isWithBackCharge: function (data) {
            return this.with_backcharge || data.is_backcharge;
        },
    },
    watch: {
        generate_interim: function () {
            let dis = this;
            let dated = this;
            if (this.generate_interim) dated = this.today;
            else dated = this.next_billing_date;

            $.each(this.lease_charge_chosen, function (i, e) {
                dis.lease_charge_chosen[i].invoice_date = dated;
            });
        },
        manager_code: function () {
            this.changeSearchType('manager_code');
        },
        property_code: function () {
            this.letter_template = '';
            this.changeSearchType('property_code');
            //console.log(this.property_code,'prop');
            this.letter_template = '';
            if (this.initialLeaseCode) {
                let dis = this;
                let leaseKey = this.initialLeaseCode;
                setTimeout(function () {
                    dis.lease_code = { field_key: leaseKey };
                    $.each(dis.lease_list, function (i, e) {
                        if (e['field_key'] == dis.lease_code.field_key) {
                            dis.lease_code.field_value = e['field_value'];
                        }
                    });
                }, 2000);
                dis.initialLeaseCode = null;
            }
        },
        lease_code: function () {
            this.letter_template = '';
            this.changeSearchType('lease_code');
            this.resetRentReviewCharge();
            this.letter_template = '';
        },
        search_type: function () {
            this.reset_filter();
        },
        source_code: function () {
            if (this.source_code == 0) this.editor_source = this.editorData;
            else this.editorData = this.editor_source;
        },
        window_size: function () {
            if (this.window_size.x <= 882) {
                this.responsive_show = false;
            } else {
                this.responsive_show = true;
            }
        },
    },
};
</script>
