<template>
    <div class="c8-page">
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <div class="page-form">
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Due Date:</strong>
                            </td>
                            <td class="required">*</td>
                            <td>
                                <cirrus-icon-date-picker
                                    :id="'start_date' + String(Math.random()).replace('.', '')"
                                    v-model="start_date"
                                    :size="'40'"
                                    data-inverted=""
                                    :edit_form="true"
                                ></cirrus-icon-date-picker>
                                to
                                <cirrus-icon-date-picker
                                    :id="'end_date' + String(Math.random()).replace('.', '')"
                                    v-model="end_date"
                                    :size="'40'"
                                    data-inverted=""
                                    :edit_form="true"
                                ></cirrus-icon-date-picker>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Invoice Number:</strong>
                            </td>
                            <td class="required">&nbsp&nbsp</td>
                            <td>
                                <cirrus-input
                                    :id="'tax_inv_number'"
                                    v-model="invoice_number"
                                    :edit_form="true"
                                    size="8"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Show Statement</strong>
                            </td>
                            <td class="required">&nbsp&nbsp</td>
                            <td>
                                <v-btn-toggle
                                    class="form-toggle"
                                    v-model="show_statement"
                                    mandatory
                                >
                                    <v-btn small>Yes</v-btn>
                                    <v-btn small>No</v-btn>
                                </v-btn-toggle>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
        </div>
        <v-divider></v-divider>
        <v-card elevation="0">
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    @click="loadForm()"
                    color="success"
                    depressed
                    small
                    :disabled="loading_btn_setting"
                    :loading="loading_btn_setting"
                >
                    <v-icon>search</v-icon>
                    Search
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-card
            v-show="lease_tax_invoice_list.length > 0 && !loading_setting"
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
            </v-card-actions>
        </v-card>
        <div
            class="page-form"
            v-show="lease_tax_invoice_list.length > 0 && !loading_setting"
        >
            <div class="form-row">
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="lease_tax_invoice_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="lease_tax_invoice_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ lease_tax_invoice_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.invoice_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.invoice_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.last_emailed_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                :data-tooltip="loadToolTip(item)"
                            >
                                {{ item.last_emailed }}
                            </span>
                        </div>
                    </template>
                    <template v-slot:item.download="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                @click="downloadPdf(item.download_path)"
                            >
                                <a
                                    ><img
                                        :src="asset_domain + 'assets/images/icons/pdf.png'"
                                        alt="Adobe Logo"
                                        class="icon"
                                    />&nbsp; Download</a
                                >
                            </span>
                        </div>
                    </template>
                    <template v-slot:item.send_email="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">
                                <v-btn
                                    v-if="lease_email_address !== ''"
                                    text
                                    x-small
                                    @click="sendEmail(item)"
                                    :disabled="is_inactive == 1"
                                >
                                    <v-icon
                                        color="success"
                                        v-if="item.log > 0"
                                        >check</v-icon
                                    >
                                    <v-icon v-if="item.log === 0">email</v-icon>
                                    Email
                                </v-btn>
                            </span>
                        </div>
                    </template>

                    <template v-slot:item.action1="{ item }"> </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="lease_tax_invoice_list.length > 5"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                        :total-visible="5"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>
    </div>
</template>
<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        property_code: { type: Object, default: { field_key: '', field_value: '' } },
        lease_code: { type: Object, default: { field_key: '', field_value: '' } },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            loading: true,
            asset_domain: this.$assetDomain,
            invoice_number: '',
            search_datatable: '',
            start_date: '01/' + ('0' + (new Date().getMonth() + 1)).slice(-2) + '/' + new Date().getFullYear(),
            end_date:
                new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate() +
                '/' +
                ('0' + (new Date().getMonth() + 1)).slice(-2) +
                '/' +
                new Date().getFullYear(),
            show_statement: 1,
            error_server_msg: {},
            error_server_msg2: [],
            headers: [
                { text: '#', value: 'index', width: '40px' },
                { text: 'Due Date', value: 'invoice_date_raw' },
                { text: 'Invoice Number', value: 'invoice_number' },
                { text: 'Emailed', value: 'last_emailed_raw' },
                { text: 'Download', value: 'download' },
                { text: '', value: 'send_email' },
            ],
            lease_tax_invoice_list: [],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            loading_btn_setting: false,
            loading_setting: false,
            lease_email_address: '',
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'server_date_today']),
    },
    methods: {
        loadLeaseTaxInvoice: function () {
            let invoice_number = this.invoice_number;
            let start_date = this.start_date;
            let end_date = this.end_date;
            let show_statement = this.show_statement;
            let property_code = this.property_code.field_key;
            let lease_code = this.lease_code.field_key;

            let error_server_msg2 = [];
            if (start_date === '') error_server_msg2.push(['Please input a valid start date.']);
            if (end_date === '') error_server_msg2.push(['Please input a valid start date.']);
            this.error_server_msg2 = error_server_msg2;
            if (error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', property_code);
                form_data.append('lease_code', lease_code);
                form_data.append('start_date', start_date);
                form_data.append('end_date', end_date);
                form_data.append('invoice_number', invoice_number);
                form_data.append('show_statement', show_statement);
                form_data.append('no_load', true);
                this.$api.post(this.cirrus8_api_url + 'api/ar/fetch/tax-invoice', form_data).then((response) => {
                    this.lease_tax_invoice_list = response.data.tax_invoice_list;
                    this.loading_btn_setting = false;
                    this.loading_setting = false;
                });
            }
        },
        loadForm: function () {
            this.loading_btn_setting = true;
            this.loadLeaseTaxInvoice();
        },
        loadToolTip(item) {
            let tooltip = '';
            if (item.last_emailed !== '' && item.last_emailed !== null) {
                tooltip = 'To: ' + item.recipient;
                if (item.error !== '' && item.error !== null) {
                    tooltip = 'Error: ' + item.error;
                }
            }
            return tooltip;
        },
        downloadPdf: function (encoded_url) {
            document.location.href = 'download.php?fileID=' + encoded_url;
        },
        async sendEmail(item) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure? This invoice will be emailed to the tenant.',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.loading_btn_setting = true;
                let invoice_number = item.invoice_number;
                let file_name = item.file_name;
                let file_name_base_64 = item.file_name_base_64;
                let invoice_date = item.invoice_date;
                let start_date = this.start_date;
                let end_date = this.end_date;
                let show_statement = this.show_statement;
                let property_code = this.property_code.field_key;
                let lease_code = this.lease_code.field_key;
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('reportType', 1);
                form_data.append('property_code', property_code);
                form_data.append('lease_code', lease_code);
                form_data.append('fromDate', start_date);
                form_data.append('toDate', end_date);
                form_data.append('no_load', true);
                let url =
                    '?module=ar&command=viewTaxInvoice&action=email' +
                    '&pID=' +
                    property_code +
                    '&lID=' +
                    lease_code +
                    '&reference=' +
                    invoice_number +
                    '&file=' +
                    file_name_base_64 +
                    '&invoiceDate=' +
                    invoice_date;
                axios.post(url, form_data).then((response) => {
                    this.loading_btn_setting = false;

                    this.loadLeaseTaxInvoice();
                });
            }
        },
        loadLeaseEmailAddress: function () {
            let property_code = this.property_code.field_key;
            let lease_code = this.lease_code.field_key;
            var form_data = new FormData();
            form_data.append('property_code', property_code);
            form_data.append('lease_code', lease_code);
            form_data.append('no_load', true);
            this.$api.post('lease/fetch/email-address', form_data).then((response) => {
                this.lease_email_address = response.data.email_address;
                this.loading_page_setting = false;
            });
        },
    },
    created() {
        bus.$on('loadLeaseTaxInvoice', (data) => {
            this.loadLeaseTaxInvoice();
        });
        this.loadLeaseEmailAddress();
    },
    watch: {
        server_date_today: function () {},
    },
    mixins: [global_mixins],
};
</script>
