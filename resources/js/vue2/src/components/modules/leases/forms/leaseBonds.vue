<template>
    <div>
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
            v-if="leaseBonds.length > 0"
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Related Bond/Deposit Outstanding Amounts</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    v-if="expand"
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <h6
                    class="title font-weight-black"
                    style="padding-right: 10px"
                >
                    Total Unpaid: {{ unpaidTotal }}
                </h6>
                <v-btn
                    color="white"
                    icon
                    x-small
                    class="v-step-gen-final-message"
                    @click="expand = !expand"
                >
                    <v-icon v-if="!expand">expand_more</v-icon>
                    <v-icon v-if="expand">expand_less</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-expand-transition v-if="leaseBonds.length > 0">
            <v-card
                v-show="expand"
                class="mx-auto"
            >
                <v-data-table
                    class="c8-datatable-custom"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="leaseBondsList"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ leaseBondsList.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.transaction_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.transaction_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.property_name="{ item }">
                        {{ item.property_code }}
                    </template>
                    <template v-slot:item.lease_name="{ item }">
                        {{ item.lease_code }}
                    </template>
                    <template v-slot:item.from_to_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.from_date }} - {{ item.to_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.unpaid_amount="{ item }">
                        {{ accountingAmountFormat(numberWithCommas(roundTo(item.unpaid_amount, 2))) }}
                    </template>
                </v-data-table>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[10, 20, 30]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </v-card>
        </v-expand-transition>
        <br v-if="leaseBonds.length > 0" />
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
    },
    data() {
        return {
            footer_props: {
                'items-per-page-options': [],
                'items-per-page-text': null,
                'disable-items-per-page': true,
            },
            loading_setting: true,
            leaseBonds: [],
            leaseBondsList: [],
            search: '',
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Date', value: 'transaction_date_raw' },
                { text: 'Type', value: 'transaction_type' },
                { text: 'Invoice', value: 'invoice_number' },
                { text: 'From-To Date', value: 'from_to_date_raw' },
                { text: 'Account', value: 'account_code' },
                { text: 'Description', value: 'account_name' },
                { text: 'Unpaid Amount', value: 'unpaid_amount', align: 'end' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 10,
            search_datatable: '',
            expand: false,
        };
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'lease_details', 'lease_details_old']),
        unpaidTotal() {
            let total = 0;
            for (let x = 0; x <= this.leaseBondsList.length - 1; x++) {
                total = total + eval(this.leaseBondsList[x].unpaid_amount.toString().replace(/,/g, ''));
            }
            total = this.roundTo(total, 2);
            return this.accountingAmountFormat(this.numberWithCommas(total));
        },
        bonds() {
            if (this.leaseBonds !== [] && this.leaseBonds !== undefined) {
                return this.leaseBonds.oldAmounts;
            } else {
                return [];
            }
        },
    },
    mounted() {
        this.loading_setting = false;
        this.loadLeaseExpandParameters();
    },
    methods: {
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadLeaseBonds();
            }
        },
        loadLeaseBonds() {
            let form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('no_load', true);
            this.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/lease/fetch/outstanding-receipts-bond', form_data)
                .then((response) => {
                    this.leaseBonds = response.data.old_amounts;
                    this.leaseBondsList = response.data.old_amounts.filter((m) => eval(m.unpaid_amount) != 0);
                    this.lease_bonds_amnt_loading = false;
                });
        },
        loadLeaseExpandParameters() {
            let form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('lease/fetch/lease-expand-parameters', form_data).then((response) => {
                this.expand = response.data.expand_bond_section;
            });
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadLeaseBondSection', (data) => {
            this.loadForm();
        });
    },
    mixins: [global_mixins],
};
</script>
