<template>
    <div
        class="page-form"
        v-on:dblclick="doubleClickForm()"
        style="max-width: 100%"
    >
        <div v-if="unit_code !== ''">
            <v-card
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Rent Review</h6>
                    &nbsp&nbsp
                    <v-icon
                        small
                        @click="toggle_tips ? (toggle_tips = false) : (toggle_tips = true)"
                        >info</v-icon
                    >
                    <v-spacer></v-spacer>
                    <cirrus-input
                        inputFormat="search"
                        v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                        v-model="search_datatable"
                        placeholder="Search"
                        :edit_form="true"
                        style="padding-right: 1em"
                    ></cirrus-input>

                    <v-btn
                        x-small
                        v-show="
                            !readonly &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                            !pmro_read_only
                        "
                        icon
                        @click="modalAddData()"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="
                            !readonly &&
                            !new_lease &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                            !pmro_read_only
                        "
                        v-if="!edit_form"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="
                            !readonly &&
                            !new_lease &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                            !pmro_read_only
                        "
                        v-if="edit_form"
                        icon
                        @click="resetForm()"
                    >
                        <v-icon color="red">undo</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="
                            !readonly &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                        "
                        icon
                        @click="loadForm()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        icon
                        x-small
                        v-show="!read_only && !new_lease && isLeaseFormLive()"
                        @click="showLeaseActivityModal()"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
            <v-alert
                border="left"
                dense
                type="info"
                v-if="toggle_tips"
                class="rounded-0 mb-0 c8-info-alert"
            >
                Reviews are dates stipulated in the lease at which a contracted amount being paid under the lease is
                reviewed. Reviews usually apply to base rent, but may apply to other charges on the lease such as
                carparks.<br />
                <ol>
                    <li>
                        <strong>To add a new rent review to the lease</strong>, click on the
                        <v-icon small>edit</v-icon> button to enter into edit mode and click
                        <v-icon small>add</v-icon> button to show the form. Select a date for the review and then select
                        the type of review taking place.
                    </li>
                    <li>
                        <strong>To mark an existing rent review as completed</strong>, click on the link 'Mark as
                        Completed' next to the relevent rent review. Note that once a review has been completed, it
                        cannot be deleted.
                    </li>
                    <li>
                        <strong>To add the rent review to the diary</strong>, click on the
                        <v-icon small>date_range</v-icon> button.
                    </li>
                </ol>
            </v-alert>
            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <v-col
                class="text-center"
                v-if="lease_rent_review_list.length === 0"
                v-show="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
            >
                <v-btn
                    depressed
                    small
                    color="success"
                    @click="modalAddData()"
                    v-if="!pmro_read_only"
                    >Add Rent Review</v-btn
                >
                <div
                    style="margin-top: 10px"
                    v-else
                >
                    No rent reviews at the moment
                </div>
            </v-col>
            <v-data-table
                v-if="!loading_setting"
                class="c8-datatable-custom"
                v-show="lease_rent_review_list.length > 0"
                dense
                item-key="id"
                :headers="headers"
                :items="lease_rent_review_list"
                :items-per-page="items_per_page"
                hide-default-footer
                :page.sync="page"
                :total-visible="7"
                @page-count="page_count = $event"
                :search="search_datatable"
            >
                <template v-slot:item.index="{ item }">
                    {{ lease_rent_review_list.indexOf(item) + 1 }}
                </template>
                <template v-slot:item.item_no="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.item_no }}</span>
                    </div>
                </template>

                <template v-slot:item.review_date_raw="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.review_date }}</span>
                    </div>
                </template>

                <template v-slot:item.charge_unit_desc="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.charge_unit_desc }}</span>
                    </div>
                </template>

                <template v-slot:item.review_type_desc="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.review_type_desc }}</span>
                    </div>
                </template>

                <template
                    v-show="viewRecommendation"
                    v-slot:item.recommendation="{ item }"
                >
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.rent_recommendation.field_value }}</span>
                    </div>
                </template>

                <template v-slot:item.comment="{ item }">
                    <div class="form-row no-border-line">
                        <span class="form-input-text">{{ item.comment }}</span>
                    </div>
                </template>

                <template v-slot:item.completed="{ item }">
                    <v-icon
                        color="green"
                        v-if="
                            item.review_complete === 1 &&
                            (!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) ||
                                page_form_type.includes('print-page'))
                        "
                        >done</v-icon
                    >
                    <v-btn
                        x-small
                        v-if="
                            item.review_complete === 1 &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                            !pmro_read_only
                        "
                        data-tooltip="Re-Open"
                        data-position="left center"
                        @click="openRentReview(lease_rent_review_list.indexOf(item))"
                        >Re-Open</v-btn
                    >

                    <v-btn
                        x-small
                        v-if="
                            item.review_complete === 0 &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                            !pmro_read_only
                        "
                        @click="completedRentReview(lease_rent_review_list.indexOf(item))"
                        >Mark as Completed</v-btn
                    >
                </template>

                <template v-slot:item.diarise="{ item }">
                    <v-icon
                        small
                        v-if="
                            !item.review_complete &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                        "
                        @click="diariseRentReview(lease_rent_review_list.indexOf(item))"
                        :disabled="pmro_read_only ? true : false"
                    >
                        date_range
                    </v-icon>
                </template>

                <template v-slot:item.action1="{ item }">
                    <v-icon
                        small
                        @click="modalOpenAED(lease_rent_review_list.indexOf(item))"
                        v-if="edit_form"
                        >fas fa-edit</v-icon
                    >

                    <v-icon
                        color="red"
                        v-show="!readonly"
                        v-if="edit_form && item.review_complete !== 1"
                        @click="deleteRentReview(lease_rent_review_list.indexOf(item))"
                        >close
                    </v-icon>
                    <v-icon
                        color="red"
                        v-show="!readonly"
                        v-if="edit_form && item.review_complete === 1"
                        :disabled="item.review_complete === 1"
                        >close
                    </v-icon>
                </template>
            </v-data-table>
            <v-row
                class="form-row"
                v-if="
                    lease_rent_review_list.length > 5 &&
                    !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                "
            >
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                >
                    <table class="c8-datatable-custom-footer">
                        <tr>
                            <td class="">Rows per page:</td>
                            <td>
                                <multiselect
                                    v-model="items_per_page"
                                    :options="[5, 10, 15]"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-200"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </td>
                            <td></td>
                            <td>
                                <v-pagination
                                    v-model="page"
                                    :length="page_count"
                                ></v-pagination>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <v-dialog
                v-model="show_activity_log_modal"
                max-width="1000"
                content-class="c8-page"
            >
                <v-card>
                    <v-card-title class="headline">
                        Activity Log
                        <a
                            href="#"
                            class="dialog-close"
                            @click.prevent="show_activity_log_modal = false"
                        >
                            <v-icon>mdi-close</v-icon>
                        </a>
                    </v-card-title>
                    <v-card-text>
                        <lease-activity-log-component
                            v-if="show_activity_log_modal"
                            :property_code="property_code"
                            :lease_code="lease_code"
                            :form_section="form_section"
                        ></lease-activity-log-component>
                    </v-card-text>
                    <v-card-actions>
                        <v-spacer />
                        <v-btn
                            depressed
                            small
                            @click="show_activity_log_modal = false"
                        >
                            <v-icon
                                left
                                dark
                                size="18"
                                >mdi-close</v-icon
                            >
                            Close
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>

            <!--   AED modal      -->
            <v-dialog
                v-model="AED_modal"
                max-width="1000"
                content-class="c8-page"
                @keydown.ctrl.left="modalPrevData()"
                @keydown.ctrl.right="modalNextData()"
                @keydown.ctrl.shift.enter="modalAddData()"
                @keydown.ctrl.enter="modalSubmitData()"
                @keydown.ctrl.delete="deleteRentReview(lease_rent_review_arr.index)"
            >
                <v-card>
                    <v-card-title class="headline">
                        Rent Review Information
                        <a
                            href="#"
                            class="dialog-close"
                            @click.prevent="AED_modal = false"
                        >
                            <v-icon>mdi-close</v-icon>
                        </a>
                    </v-card-title>
                    <v-card-text>
                        <cirrus-server-error
                            :error_msg="error_server_msg"
                            :errorMsg2="error_server_msg2"
                        ></cirrus-server-error>
                        <v-alert
                            type="success"
                            dense
                            tile
                            text
                            v-if="success_flag"
                        >
                            Successfully Saved
                        </v-alert>
                        <!--Lease add-->
                        <div>
                            <div class="page-form">
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label"
                                        >#</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <span class="form-input-text">{{
                                            lease_rent_review_arr.index === 'New'
                                                ? lease_rent_review_arr.index
                                                : lease_rent_review_arr.index + 1
                                        }}</span>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label required"
                                        >Review Date</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <cirrus-icon-date-picker
                                            data-inverted=""
                                            :id="'lease_rent_review_date' + String(Math.random()).replace('.', '')"
                                            v-model="lease_rent_review_arr.review_date"
                                            :size="'40'"
                                            :edit_form="lease_rent_review_arr.index === 'New'"
                                            :error_msg="error_msg"
                                        ></cirrus-icon-date-picker>
                                        <span style="position: absolute; margin-top: 4px">
                                            <v-checkbox
                                                v-model="lease_rent_review_arr.diary_flag"
                                                v-if="lease_rent_review_arr.index === 'New'"
                                                label="Diarise"
                                                ripple="false"
                                                dense
                                            ></v-checkbox>
                                        </span>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label required"
                                        >Review Applies to Lease Change</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <multiselect
                                            v-model="lease_rent_review_arr.charge_unit"
                                            :options="charge_unit_list"
                                            openDirection="bottom"
                                            :allowEmpty="false"
                                            class="vue-select2 dropdown-left dropdown-600"
                                            group-label="language"
                                            placeholder="Please select..."
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label required"
                                        >Review Type</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <multiselect
                                            v-model="lease_rent_review_arr.review_type"
                                            :options="review_type"
                                            :allowEmpty="false"
                                            openDirection="bottom"
                                            class="vue-select2 dropdown-left dropdown-600"
                                            group-label="language"
                                            placeholder="Please select..."
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label"
                                        >Recommendation</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <multiselect
                                            v-model="lease_rent_review_arr.rent_recommendation"
                                            :options="lease_rent_recommendation"
                                            openDirection="bottom"
                                            :allowEmpty="false"
                                            class="vue-select2 dropdown-left dropdown-600"
                                            group-label="language"
                                            placeholder="Please select..."
                                            track-by="field_key"
                                            label="field_value"
                                            :show-labels="false"
                                            ><span slot="noResult"
                                                >Oops! No elements found. Consider changing the search query.</span
                                            >
                                        </multiselect>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label"
                                        >Comment</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <div class="form-row no-border-line">
                                            <cirrus-input
                                                v-model="lease_rent_review_arr.comment"
                                                size=""
                                                data-inverted=""
                                                :edit_form="true"
                                                :error_msg="error_msg"
                                            ></cirrus-input>
                                        </div>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="
                                        lease_rent_review_arr.status !== 'new' &&
                                        !formSectionReadOnly(
                                            pm_lease_form_read_only,
                                            form_type,
                                            form_section,
                                            is_inactive,
                                        )
                                    "
                                >
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label"
                                        >Completed</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <v-icon
                                            color="green"
                                            v-if="lease_rent_review_arr.review_complete === 1"
                                            >done</v-icon
                                        >
                                        <v-btn
                                            x-small
                                            v-if="lease_rent_review_arr.review_complete === 1"
                                            @click="lease_rent_review_arr.review_complete = 0"
                                        >
                                            Open</v-btn
                                        >
                                        <span v-if="!edit_form && lease_rent_review_arr.review_complete === 1"
                                            >Open</span
                                        >
                                        <v-btn
                                            x-small
                                            v-if="lease_rent_review_arr.review_complete === 0"
                                            @click="lease_rent_review_arr.review_complete = 1"
                                            >Mark as Completed</v-btn
                                        >
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-if="lease_rent_review_arr.status !== 'new' && !auto_diarise"
                                >
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label"
                                        >Diarise</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                        <v-icon
                                            v-show="!readonly"
                                            v-if="
                                                edit_form &&
                                                lease_rent_review_arr.review_id &&
                                                !lease_rent_review_arr.review_complete &&
                                                !auto_diarise
                                            "
                                            @click="diariseRentReview(lease_rent_review_arr.index)"
                                            >date_range
                                        </v-icon>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row">
                                    <v-col
                                        xs="12"
                                        sm="2"
                                        md="2"
                                        class="form-label"
                                    ></v-col>
                                    <v-col
                                        xs="12"
                                        sm="10"
                                        md="10"
                                        class="form-input"
                                    >
                                    </v-col>
                                </v-row>
                            </div>
                        </div>
                    </v-card-text>
                    <v-card-actions>
                        <v-btn
                            class="v-step-save-2-button"
                            @click="modalPrevData()"
                            data-tooltip="CTR + LEFT"
                            color="primary"
                            dark
                            depressed
                            small
                        >
                            <v-icon
                                left
                                dark
                                size="18"
                                >skip_previous</v-icon
                            >
                            Previous
                        </v-btn>
                        <v-spacer />
                        <v-btn
                            class="v-step-save-2-button"
                            @click="modalAddData()"
                            data-tooltip="CTR + SHIFT + ENTER"
                            color="primary"
                            dark
                            depressed
                            small
                            v-if="lease_rent_review_arr.status !== 'new'"
                        >
                            <v-icon
                                left
                                dark
                                size="18"
                                >add</v-icon
                            >
                            Add New
                        </v-btn>
                        <v-btn
                            class="v-step-save-2-button"
                            @click="modalSubmitData()"
                            data-tooltip="CTR + ENTER"
                            color="success"
                            dark
                            depressed
                            small
                        >
                            <v-icon
                                left
                                dark
                                size="18"
                                >check</v-icon
                            >
                            Save
                        </v-btn>
                        <v-btn
                            class="v-step-save-2-button"
                            data-tooltip="CTR + DEL"
                            @click="deleteRentReview(lease_rent_review_arr.index)"
                            v-if="lease_rent_review_arr.review_complete == 1 && lease_rent_review_arr.index !== 'New'"
                            color="error"
                            dark
                            depressed
                            small
                        >
                            <v-icon
                                left
                                dark
                                size="18"
                                >mdi-close</v-icon
                            >
                            Delete
                        </v-btn>
                        <v-btn
                            class="v-step-save-2-button"
                            @click="modalAddData()"
                            v-if="lease_rent_review_arr.index === 'New'"
                            data-tooltip="CTR + ENTER"
                            color="warning"
                            dark
                            depressed
                            small
                        >
                            <v-icon
                                left
                                dark
                                size="18"
                                >clear_all</v-icon
                            >
                            Clear
                        </v-btn>
                        <v-btn
                            class="v-step-save-2-button"
                            @click="modalNextData()"
                            data-tooltip="CTR + RIGHT"
                            color="primary"
                            dark
                            depressed
                            small
                        >
                            Next
                            <v-icon
                                left
                                dark
                                size="18"
                                >skip_next</v-icon
                            >
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>
        </div>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        viewRecommendation: { type: Boolean, default: true },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_RENT_REVIEW',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            toggle_tips: false,
            lease_rent_review_arr: [],
            lease_rent_review_list: [],
            lease_rent_review_list_old: [],
            review_type: [],
            unit_code: '',
            lease_rent_recommendation: [],
            charge_unit_list: [],
            readonly: this.read_only,
            show_activity_log_modal: false,
            AED_modal: false,
            success_flag: false,
            modal_current_ctr: 0,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Review Date', value: 'review_date_raw', width: '8%' },
                { text: 'Review Applies to Lease Change', value: 'charge_unit_desc', width: '20%' },
                { text: 'Review Type', value: 'review_type_desc', width: '17%' },
                { text: 'Recommendation', value: 'recommendation', width: '15%' },
                { text: 'Comment', value: 'comment', width: '15%' },
                { text: 'Completed', value: 'completed', width: '10%' },
                { text: 'Diarise', value: 'diarise', sortable: false, width: '5%' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        this.edit_form = this.edit_flag;
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'auto_diarise',
        ]),
    },
    methods: {
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.readonly &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadLeaseRentReview();
                if (this.edit_flag) {
                    this.edit_form = true;
                }
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_rent_review_list = [];
            this.lease_rent_review_list = JSON.parse(JSON.stringify(this.lease_rent_review_list_old));
        },
        modalAddData: function () {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            let lease_rent_review_list = [];
            let review_date = d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear();
            if (this.lease_rent_review_list.length > 0) {
                lease_rent_review_list = this.lease_rent_review_list[this.lease_rent_review_list.length - 1];
                review_date = lease_rent_review_list.review_date;
                review_date = review_date.split('/');
                review_date = review_date[0] + '/' + review_date[1] + '/' + (parseInt(review_date[2]) + 1);
            }

            this.lease_rent_review_arr = {
                index: 'New',
                review_id: '',
                review_type: { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' },
                charge_unit: { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' },
                rent_recommendation: {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                comment: '',
                review_date: review_date,
                diary_flag: this.auto_diarise,
                review_complete: 0,
                old_rent: 0,
                status: 'new',
            };
        },
        loadLeaseRentReview: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('unit_code', this.unit_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';

            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/rent-review';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/rent-review';
            }

            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_rent_review_list = response.data.lease_rent_review_list;
                this.lease_rent_review_list_old = response.data.lease_rent_review_list_old;
                this.review_type = response.data.review_type;
                this.lease_rent_recommendation = response.data.lease_rent_recommendation;
                this.charge_unit_list = response.data.charge_unit_list;
                if (this.formSectionReadOnly(this.pm_lease_form_read_only, this.form_type, this.form_section)) {
                    this.items_per_page = this.lease_rent_review_list.length;
                }
                this.loading_setting = false;
            });
        },
        modalSubmitData: function () {
            let errorArr = [];
            let review_type = this.lease_rent_review_arr.review_type;
            let charge_unit = this.lease_rent_review_arr.charge_unit;
            let review_date = this.lease_rent_review_arr.review_date;
            if (review_date === '') {
                errorArr.push(['You have not entered a valid review date.']);
            }
            if (review_type.value === '') {
                errorArr.push(['You have not selected a valid review type.']);
            }
            if (charge_unit.value === '') {
                errorArr.push(['You have not selected a valid lease charge.']);
            }

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                let save_arr = [];
                save_arr[0] = this.lease_rent_review_arr;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('lease_rent_review_list', JSON.stringify(save_arr));

                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/update-or-create/rent-review';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/update-or-create/rent-review';
                }

                this.$api.post(apiUrl, form_data, {}).then((response) => {
                    this.edit_form = this.edit_flag;
                    if (response.data.diary == 1) {
                        bus.$emit('loadLeaseDiarySection', '');
                    }
                    this.lease_rent_review_list = [];
                    this.loadForm();
                    if (this.lease_rent_review_arr.index === 'New') {
                        this.lease_rent_review_arr.index = this.lease_rent_review_list.length;
                        this.lease_rent_review_arr.status = 'saved';
                        this.lease_rent_review_arr.review_id = response.data.review_id;
                    }
                    // this.edit_form = false;
                    this.loading_setting = false;
                    // this.$noty.success("Successfully Saved");
                    this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        async deleteRentReview(index) {
            if (index === 'New') return;
            let review_complete = this.lease_rent_review_list[index].review_complete;
            if (review_complete === 0) {
                let review_id = this.lease_rent_review_list[index].review_id;

                if (!review_id) {
                    this.lease_rent_review_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('lease_code', this.lease_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('review_id', review_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isLeaseFormLive()) {
                            apiUrl = this.cirrus8_api_url + 'api/lease/delete/rent-review';
                        } else {
                            apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/rent-review';
                        }

                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.lease_rent_review_list.splice(index, 1);
                            this.loading_setting = false;

                            this.lease_rent_review_list_old = JSON.parse(JSON.stringify(this.lease_rent_review_list));
                        });
                    }
                }
            }
        },
        async diariseRentReview(index) {
            if (index === 'New') return;
            let review_complete = this.lease_rent_review_list[index].review_complete;
            if (review_complete === 0) {
                let review_id = this.lease_rent_review_list[index].review_id;
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.loading_setting = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    form_data.append('unit_code', this.unit_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('review_id', review_id);
                    form_data.append('no_load', true);
                    let apiUrl = '';
                    if (this.isLeaseFormLive()) {
                        apiUrl = this.cirrus8_api_url + 'api/lease/create/diarise-rent-review';
                    } else {
                        apiUrl = this.cirrus8_api_url + 'api/temp/lease/create/diarise-rent-review';
                    }

                    this.$api.post(apiUrl, form_data).then((response) => {
                        bus.$emit('loadLeaseDiarySection', '');
                        this.loading_setting = false;
                    });
                }
            }
        },
        completedRentReview: function (index) {
            let review_id = this.lease_rent_review_list[index].review_id;
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('unit_code', this.unit_code);
            form_data.append('version_id', this.version_id);
            form_data.append('review_id', review_id);
            form_data.append('no_load', true);
            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/update/completed-rent-review';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/completed-rent-review';
            }

            this.$api.post(apiUrl, form_data).then((response) => {
                this.loadForm();
                bus.$emit('loadLeaseDiarySection', '');
                this.loading_setting = false;
            });
        },
        openRentReview: function (index) {
            let review_id = this.lease_rent_review_list[index].review_id;
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('unit_code', this.unit_code);
            form_data.append('version_id', this.version_id);
            form_data.append('review_id', review_id);
            form_data.append('no_load', true);
            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/update/open-rent-review';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/open-rent-review';
            }

            this.$api.post(apiUrl, form_data).then((response) => {
                this.loadForm();
                bus.$emit('loadLeaseDiarySection', '');
                this.loading_setting = false;
            });
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        modalPrevData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_rent_review_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.lease_rent_review_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.lease_rent_review_arr = this.lease_rent_review_list[this.modal_current_ctr];
            this.lease_rent_review_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_rent_review_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.lease_rent_review_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.lease_rent_review_arr = this.lease_rent_review_list[this.modal_current_ctr];
            this.lease_rent_review_arr.index = this.modal_current_ctr;
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_rent_review_arr = this.lease_rent_review_list[index];
            this.lease_rent_review_arr.index = index;
            this.modal_current_ctr = index;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadLeaseRentReviewSection', (data) => {
            if (data.unit_code !== '') {
                this.unit_code = data.unit_code;
            }
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
