<template>
    <div class="page-form">
        <v-card
            class="section-toolbar"
            color="titleHeader"
            text
            tile
            v-if="lease_outstanding_amount_all_list.length > 0"
        >
            <v-card-actions
                v-if="!read_only && !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
            >
                <h6 class="title font-weight-black">Lease Outstanding Amount</h6>
                <v-btn-toggle
                    class="form-toggle"
                    v-model="outstanding_mode"
                    mandatory
                    style="margin-left: 5px"
                    v-show="expand"
                >
                    <v-btn
                        x-small
                        text
                    >
                        Due
                    </v-btn>
                    <v-btn
                        x-small
                        text
                    >
                        Outstanding
                    </v-btn>
                </v-btn-toggle>
                <v-spacer></v-spacer>

                <cirrus-input
                    v-if="expand"
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>

                <h6
                    class="title font-weight-black"
                    style="padding-right: 10px"
                >
                    {{ total_label }}: {{ unpaidTotal }}
                </h6>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    color="white"
                    icon
                    x-small
                    class="v-step-gen-final-message"
                    @click="expand = !expand"
                >
                    <v-icon v-if="!expand">expand_more</v-icon>
                    <v-icon v-if="expand">expand_less</v-icon>
                </v-btn>
            </v-card-actions>
            <v-card-actions v-else>
                <h6 class="title font-weight-black">Lease Outstanding Amount</h6>
                <v-spacer></v-spacer>
                <h6
                    class="title font-weight-black"
                    style="padding-right: 10px"
                >
                    {{ total_label }}: {{ unpaidTotal }}
                </h6>
            </v-card-actions>
        </v-card>
        <v-alert
            type="success"
            dense
            tile
            text
            v-if="success_flag"
        >
            <div
                v-for="(suc_data, suc_index) in status_message_arr"
                :key="suc_index"
            >
                {{ suc_data }}
            </div>
        </v-alert>
        <v-alert
            type="error"
            dense
            tile
            text
            v-if="email_errors.length > 0"
        >
            <div
                v-for="(email_errors_data, email_errors_index) in email_errors"
                :key="email_errors_index"
            >
                {{ email_errors_data }}
            </div>
        </v-alert>
        <div
            class="ui fluid menu"
            v-if="
                expand &&
                lease_outstanding_amount_all_list.length > 0 &&
                !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                !pmro_read_only
            "
        >
            <div class="header item pa-2">Generate Arrears Statement</div>
            <div class="right menu">
                <div class="item pa-2">
                    Transaction Due Before: &nbsp;
                    <cirrus-icon-date-picker
                        :id="'transaction_due_before' + String(Math.random()).replace('.', '')"
                        v-model="transaction_due_before"
                        :size="'40'"
                        data-inverted=""
                        :edit_form="true"
                    ></cirrus-icon-date-picker>
                </div>
                <a
                    class="item pa-2"
                    v-if="lease_email_address_list.length > 0"
                >
                    <v-btn
                        @click="loadSendEmailStatementModal()"
                        color="primary"
                        depressed
                        small
                        :loading="button_loading_setting"
                        :disabled="button_loading_setting"
                        v-if="is_inactive != 1"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >email</v-icon
                        >
                        Send Email
                    </v-btn>
                </a>
                <a class="item pa-2">
                    <v-btn
                        @click="downloadArrearsStatement()"
                        color="#eb4d4b"
                        dark
                        depressed
                        small
                        :loading="button_loading_setting"
                        :disabled="button_loading_setting"
                    >
                        <v-icon small>fas fa-file-pdf</v-icon>
                        &nbsp;Download
                    </v-btn>
                </a>
            </div>
        </div>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-expand-transition
            class="mx-auto"
            v-if="expand && lease_outstanding_amount_all_list.length > 0"
        >
            <v-card>
                <v-data-table
                    class="c8-datatable-custom"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="mainList"
                    :loading="lease_outstanding_amnt_loading"
                    loading-text="Loading... Please wait"
                    :items-per-page="items_per_page_out_inv"
                    hide-default-footer
                    :page.sync="page_out_inv"
                    :total-visible="7"
                    @page-count="page_count_out_inv = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.index="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ lease_outstanding_amount_list.indexOf(item) + 1 }}</span>
                        </div>
                    </template>
                    <template v-slot:item.transaction_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.transaction_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.due_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.invoice_number !== '0'"
                                >{{ item.due_date }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.invoice_number="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.invoice_number !== '0' && !item.file_path"
                                >{{ item.invoice_number }}</span
                            >
                            <span
                                class="form-input-text"
                                v-if="item.invoice_number !== '0' && item.file_path"
                            >
                                <a
                                    target="_blank"
                                    :href="'download.php?fileID=' + item.file_path_encode"
                                >
                                    {{ item.invoice_number }}
                                </a>
                            </span>
                        </div>
                    </template>
                    <template v-slot:item.property_name="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.property_code }}</span>
                        </div>
                    </template>
                    <template v-slot:item.lease_name="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.lease_code }}</span>
                        </div>
                    </template>
                    <template v-slot:item.from_to_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.from_date }} - {{ item.to_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.unpaid_amount="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.unpaid_amount != item.amount"
                            >
                                <span
                                    data-position="left center"
                                    :data-tooltip="
                                        'originally ' +
                                        country_defaults.currency_symbol +
                                        accountingAmountFormat(numberWithCommas(roundTo(item.amount, 2)))
                                    "
                                    >*{{ country_defaults.currency_symbol
                                    }}{{
                                        accountingAmountFormat(numberWithCommas(roundTo(item.unpaid_amount, 2)))
                                    }}</span
                                >
                            </span>
                            <span
                                class="form-input-text"
                                v-else
                            >
                                {{ country_defaults.currency_symbol
                                }}{{ accountingAmountFormat(numberWithCommas(roundTo(item.unpaid_amount, 2))) }}
                            </span>
                        </div>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="
                        lease_outstanding_amount_list.length > 10 &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page_out_inv"
                                        :options="[10, 20, 30]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page_out_inv"
                                        :length="page_count_out_inv"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </v-card>
        </v-expand-transition>

        <br v-if="lease_outstanding_amount_all_list.length > 0" />

        <v-card
            dark
            color="titleHeader"
            text
            tile
            v-if="outstanding_amnt_not_invoiced_list.length > 0"
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Amounts not Yet Invoiced</h6>
                <v-spacer></v-spacer>
                <h6
                    class="title font-weight-black"
                    style="padding-right: 10px"
                >
                    Total Unpaid: {{ unpaidNotInvoiceTotal }}
                </h6>
                <v-btn
                    color="white"
                    icon
                    x-small
                    class="v-step-gen-final-message"
                    @click="expand_not_invoice = !expand_not_invoice"
                    v-if="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                >
                    <v-icon v-if="!expand_not_invoice">expand_more</v-icon>
                    <v-icon v-if="expand_not_invoice">expand_less</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-expand-transition
            class="mx-auto"
            v-if="expand_not_invoice && outstanding_amnt_not_invoiced_list.length > 0"
        >
            <v-card v-if="outstanding_amnt_not_invoiced_list.length > 0">
                <v-data-table
                    class="c8-datatable-custom"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="outstanding_amnt_not_invoiced_list"
                    :loading="lease_outstanding_amnt_loading"
                    loading-text="Loading... Please wait"
                    :items-per-page="items_per_page_out_not_inv"
                    hide-default-footer
                    :page.sync="page_out_not_inv"
                    @page-count="page_count_out_not_inv = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.index="{ item }">
                        {{ outstanding_amnt_not_invoiced_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.transaction_date_raw="{ item }">
                        {{ item.transaction_date }}
                    </template>
                    <template v-slot:item.due_date_raw="{ item }">
                        {{ item.due_date }}
                    </template>
                    <template v-slot:item.property_name="{ item }">
                        {{ item.property_code }}
                    </template>
                    <template v-slot:item.lease_name="{ item }">
                        {{ item.lease_code }}
                    </template>
                    <template v-slot:item.from_to_date_raw="{ item }">
                        {{ item.from_date }} - {{ item.to_date }}
                    </template>
                    <template v-slot:item.unpaid_amount="{ item }">
                        <div class="form-row no-border-line">
                            <span
                                class="form-input-text"
                                v-if="item.unpaid_amount != item.amount"
                            >
                                <span
                                    data-position="left center"
                                    :data-tooltip="
                                        'originally ' +
                                        country_defaults.currency_symbol +
                                        accountingAmountFormat(numberWithCommas(roundTo(item.amount, 2)))
                                    "
                                    >*{{ country_defaults.currency_symbol
                                    }}{{
                                        accountingAmountFormat(numberWithCommas(roundTo(item.unpaid_amount, 2)))
                                    }}</span
                                >
                            </span>
                            <span
                                class="form-input-text"
                                v-else
                            >
                                {{ country_defaults.currency_symbol
                                }}{{ accountingAmountFormat(numberWithCommas(roundTo(item.unpaid_amount, 2))) }}
                            </span>
                        </div>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="
                        outstanding_amnt_not_invoiced_list.length > 10 &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page_out_not_inv"
                                        :options="[10, 20, 30]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page_out_not_inv"
                                        :length="page_count_out_not_inv"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </v-card>
        </v-expand-transition>

        <br v-if="outstanding_amnt_not_invoiced_list.length > 0" />

        <v-dialog
            v-model="show_send_email"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="sendEmailArrearsStatement()"
        >
            <v-card>
                <v-card-title class="headline">
                    Sending Email
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_send_email = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <!--Lease add-->
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Send From (Email)</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <multiselect
                                    tabindex="0"
                                    v-model="email_config_additional_default"
                                    :options="email_config_additional_list"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-300"
                                    group-label="language"
                                    placeholder="Select email"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >From Name</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <span class="form-input-text">{{ email_config_additional_default.from_name }}</span>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Reply To (Email)</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <span class="form-input-text">{{ email_config_additional_default.reply_to }}</span>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="sendEmailArrearsStatement()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                        :loading="button_loading_setting"
                        :disabled="button_loading_setting"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Send
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="show_send_email = false"
                    >
                        <v-icon
                            left
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
import { bus } from '../../../../plugins/bus';
import Vue from 'vue';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        force_load: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_OUTSTANDING',
            footer_props: {
                'items-per-page-options': [],
                'items-per-page-text': null,
                'disable-items-per-page': true,
            },
            transaction_due_before: this.server_date_today,
            loading_setting: true,
            button_loading_setting: false,
            show_send_email: false,
            outstanding_mode: 0,
            lease_outstanding_amount_all_list: [],
            lease_outstanding_amount_list: [],
            outstanding_amnt_not_invoiced_list: [],
            email_config_additional_default: {
                field_key: '',
                field_value: 'Please select ...',
                from_name: '',
                reply_to: '',
            },
            email_config_additional_list: [],
            lease_outstanding_amnt_loading: true,
            search: '',
            expand: false,
            expand_not_invoice: false,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Transaction Date', value: 'transaction_date_raw', width: '10%' },
                { text: 'Due Date', value: 'due_date_raw', width: '10%' },
                { text: 'Type', value: 'transaction_type', width: '5%' },
                { text: 'Invoice', value: 'invoice_number', width: '10%' },
                { text: 'From-To Date', value: 'from_to_date_raw', width: '15%' },
                { text: 'Account', value: 'account_code', width: '10%' },
                { text: 'Description', value: 'description', width: '15%' },
                { text: 'Unpaid Amount', value: 'unpaid_amount', align: 'end', width: '10%' },
            ],
            page_out_inv: 1,
            page_count_out_inv: 0,
            items_per_page_out_inv: 10,
            page_out_not_inv: 1,
            page_count_out_not_inv: 0,
            items_per_page_out_not_inv: 10,
            search_datatable: '',
            arrears_generated_flag: false,
            success_flag: false,
            status_message_arr: [],
            email_errors: [],
            lease_email_address: '',
            lease_email_address_list: [],
            total_label: 'Total Due',
            file_timestamp: '',
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
                tax_label: 'GST',
                business_prefix: '',
                currency_symbol: '$',
            },
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'server_date_today',
        ]),
        unpaidTotal() {
            let total = 0;
            for (let x = 0; x <= this.lease_outstanding_amount_list.length - 1; x++) {
                if (this.outstanding_mode === 1)
                    total =
                        total + eval(this.lease_outstanding_amount_list[x].unpaid_amount.toString().replace(/,/g, ''));
                else {
                    if (
                        typeof this.lease_outstanding_amount_list[x].due_date_raw !== 'undefined' &&
                        this.lease_outstanding_amount_list[x].due_date_raw !== ''
                    )
                        if (this.isBeforeToday(new Date(this.lease_outstanding_amount_list[x].due_date_raw)))
                            total =
                                total +
                                eval(this.lease_outstanding_amount_list[x].unpaid_amount.toString().replace(/,/g, ''));
                }
            }
            total = this.roundTo(total, 2);
            return this.accountingAmountFormat(this.numberWithCommas(total));
        },
        unpaidNotInvoiceTotal() {
            let total = 0;
            for (let x = 0; x <= this.outstanding_amnt_not_invoiced_list.length - 1; x++) {
                total =
                    total + eval(this.outstanding_amnt_not_invoiced_list[x].unpaid_amount.toString().replace(/,/g, ''));
            }
            total = this.roundTo(total, 2);
            return this.accountingAmountFormat(this.numberWithCommas(total));
        },
        bonds() {
            if (this.leaseOutstanding !== [] && this.leaseOutstanding !== undefined) {
                return this.lease_outstanding_amount_list.old_amounts;
            } else {
                return [];
            }
        },
        mainList() {
            let lease_outstanding_amount_list = this.lease_outstanding_amount_list;
            if (this.outstanding_mode === 1) return lease_outstanding_amount_list;
            else {
                return lease_outstanding_amount_list.filter((item) =>
                    typeof item.due_date_raw !== 'undefined' && item.due_date_raw !== ''
                        ? this.isBeforeToday(new Date(item.due_date_raw))
                        : false,
                );
            }
        },
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loading_setting = false;
        this.loadLeaseExpandParameters();
    },
    methods: {
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.email_errors = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.force_load) {
                // console.log('load property lease details');
                this.loadCountryDefaults();
                this.loadLeaseOutstandingAmounts();
                this.loadLeaseOutstandingNotYetInvoiceAmounts();
                this.loadLeaseEmailAddress();
            }
        },
        loadLeaseExpandParameters() {
            let form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('lease/fetch/lease-expand-parameters', form_data).then((response) => {
                if (
                    this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.expand = true;
                } else {
                    this.expand = response.data.expand_section;
                    this.expand_not_invoice = response.data.expand_not_invoice_section;
                }
            });
        },
        loadLeaseOutstandingAmounts() {
            let form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('no_load', true);
            this.$api.post('lease/fetch/outstanding-receipts', form_data).then((response) => {
                this.lease_outstanding_amount_all_list = response.data.filter((m) => eval(m.unpaid_amount) !== 0);
                this.lease_outstanding_amount_list = response.data.filter((m) => eval(m.unpaid_amount) !== 0); //&& m.invoice_number !== "0"
                if (
                    this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.items_per_page_out_inv = this.lease_outstanding_amount_all_list.length;
                    this.expand = true;
                }
                this.lease_outstanding_amnt_loading = false;
            });
        },
        loadLeaseOutstandingNotYetInvoiceAmounts() {
            let form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('no_load', true);
            this.$api.post('lease/fetch/outstanding-without-invoice-receipts', form_data).then((response) => {
                this.outstanding_amnt_not_invoiced_list = response.data.filter((m) => eval(m.unpaid_amount) !== 0);
                if (
                    this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.items_per_page_out_not_inv = this.outstanding_amnt_not_invoiced_list.length;
                    this.expand_not_invoice = true;
                }
                this.lease_outstanding_amnt_loading = false;
            });
        },
        extractTimestampValue: function (data) {
            //Extract the timestamp value from the hidden field in the framework HTML response
            var stamp_start = data.search('id="timestamp" ') + 22;
            var stamp_count = 10;
            this.file_timestamp = data.substr(stamp_start, stamp_count);
        },
        generateArrearsStatement: function () {
            let transaction_due_before = this.transaction_due_before;
            if (transaction_due_before === '' || transaction_due_before === null) {
                transaction_due_before = this.server_date_today;
                this.transaction_due_before = this.server_date_today;
            }
            this.button_loading_setting = true;
            let url = '?module=ar&command=generateArrearsStatement&action=finalise1';
            var form_data = new FormData();
            form_data.append('format', 'screen');
            form_data.append('method', 'property');
            form_data.append('toDate', transaction_due_before);
            form_data.append('showCurrentOnly', 'Yes');
            form_data.append('includeCredits', 'Yes');
            form_data.append('exclude_letter', 'true');
            form_data.append('ccMe', '1');
            form_data.append('useLetterhead', 'true');
            form_data.append('property', this.property_code);
            form_data.append('tenantID', this.lease_code);
            form_data.append('app_origin', 'lease_page');

            axios.post(url, form_data).then((response) => {
                this.button_loading_setting = false;
                this.arrears_generated_flag = true;

                this.extractTimestampValue(response.data);
            });
        },
        downloadArrearsStatement: function () {
            this.button_loading_setting = true;

            let transaction_due_before = this.transaction_due_before;
            if (transaction_due_before === '' || transaction_due_before === null) {
                transaction_due_before = this.server_date_today;
                this.transaction_due_before = this.server_date_today;
            }
            this.button_loading_setting = true;
            let url = '?module=ar&command=generateArrearsStatement&action=finalise1';
            var form_data = new FormData();
            form_data.append('format', 'screen');
            form_data.append('method', 'property');
            form_data.append('toDate', transaction_due_before);
            form_data.append('showCurrentOnly', 'Yes');
            form_data.append('includeCredits', 'Yes');
            form_data.append('exclude_letter', 'true');
            form_data.append('ccMe', '1');
            form_data.append('useLetterhead', 'true');
            form_data.append('property', this.property_code);
            form_data.append('tenantID', this.lease_code);
            form_data.append('app_origin', 'lease_page');

            axios.post(url, form_data).then((response) => {
                url =
                    '?module=ar&command=generateArrearsStatement&action=dlLetterSingle_' +
                    this.property_code +
                    '~' +
                    this.lease_code +
                    '';

                this.extractTimestampValue(response.data);

                var form_data = new FormData();
                form_data.append('showCurrentOnly', 'Yes');
                form_data.append('includeCredits', 'Yes');
                form_data.append('ccMe', '1');
                form_data.append('exclude_letter', 'true');
                form_data.append('property', this.property_code);
                form_data.append('tenantID', this.lease_code);
                form_data.append('app_origin', 'lease_page');
                form_data.append('toDate', transaction_due_before);
                form_data.append('timestamp', this.file_timestamp);
                form_data.append('no_load', true);
                axios.post(url, form_data).then((response) => {
                    this.button_loading_setting = false;
                    let position = response.data.toUpperCase().search('ERROR');
                    if (position > -1) this.$noty.warning('No file to download');
                    else document.location.href = 'download.php?fileID=' + response.data;
                });
            });
        },
        loadSendEmailStatementModal: function () {
            this.email_errors = [];

            for (let ctr = 0; ctr <= this.lease_email_address_list.length - 1; ctr++) {
                let email_address = this.lease_email_address_list[ctr].emailAddress;
                let email_list = this.emailArr(email_address);
                let email_ctr = 0;
                if (email_address !== '')
                    for (let email_index = 0; email_index <= email_list.length - 1; email_index++)
                        if (!this.validateEmail(email_list[email_index].trim()))
                            this.email_errors[0] =
                                "The tenant email address is invalid or not present. Edit the tenant's contact details in the company section.";
            }
            if (this.email_errors.length === 0) {
                this.button_loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('no_load', true);
                this.$api.post('fetch/email-config-additional', form_data).then((response) => {
                    this.email_config_additional_default = response.data.email_config_additional_default;
                    this.email_config_additional_list = response.data.email_config_additional_list;
                    this.show_send_email = true;
                    this.button_loading_setting = false;
                });
            }
        },
        sendEmailArrearsStatement: function () {
            this.email_errors = [];
            this.status_message_arr = [];
            this.button_loading_setting = true;
            this.show_send_email = false;
            let transaction_due_before = this.transaction_due_before;
            if (transaction_due_before === '' || transaction_due_before === null) {
                transaction_due_before = this.server_date_today;
                this.transaction_due_before = this.server_date_today;
            }
            this.button_loading_setting = true;
            let url = '?module=ar&command=generateArrearsStatement&action=finalise1';
            var form_data = new FormData();
            form_data.append('format', 'screen');
            form_data.append('method', 'property');
            form_data.append('toDate', transaction_due_before);
            form_data.append('showCurrentOnly', 'Yes');
            form_data.append('includeCredits', 'Yes');
            form_data.append('exclude_letter', 'true');
            form_data.append('ccMe', '1');
            form_data.append('useLetterhead', 'true');
            form_data.append('property', this.property_code);
            form_data.append('tenantID', this.lease_code);
            form_data.append('app_origin', 'lease_page');

            axios.post(url, form_data).then((response) => {
                url =
                    '?module=ar&command=generateArrearsStatement&action=emailSingle_' +
                    this.property_code +
                    '~' +
                    this.lease_code +
                    '';
                this.extractTimestampValue(response.data);
                var form_data = new FormData();
                form_data.append('showCurrentOnly', 'Yes');
                form_data.append('includeCredits', 'Yes');
                form_data.append('toDate', transaction_due_before);
                form_data.append('ccMe', '1');
                form_data.append('exclude_letter', 'true');
                form_data.append('property', this.property_code);
                form_data.append('tenantID', this.lease_code);
                form_data.append('send_from', this.email_config_additional_default.field_key);
                form_data.append('app_origin', 'lease_page');
                form_data.append('app_source', 'API');
                form_data.append('timestamp', this.file_timestamp);
                form_data.append('no_load', true);
                axios.post(url, form_data).then((response) => {
                    this.button_loading_setting = false;
                    this.show_send_email = false;
                    let position =
                        typeof response.data === 'object' && response.data !== null
                            ? -1
                            : response.data.toUpperCase().search('ERROR');
                    if (position > -1) this.$noty.warning('No file to send');
                    else {
                        this.success_flag = true;
                        this.status_message_arr = response.data.status_message_arr;
                        this.email_errors = response.data.email_errors;
                    }

                    setTimeout(
                        function () {
                            this.success_flag = false;
                            this.status_message_arr = [];
                        }.bind(this),
                        2000,
                    );
                });
            });
        },
        loadLeaseEmailAddress: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('no_load', true);
            this.$api.post('lease/fetch/email-address-list', form_data).then((response) => {
                this.lease_email_address_list = response.data.email_address_list;
                this.loading_page_setting = false;
            });
        },
        loadCountryDefaults: function () {
            let form_data = new FormData();
            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
            });
        },
        emailArr: function (param1) {
            return param1.split(';');
        },
        isBeforeToday: function (date) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return date < today;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        server_date_today: function () {
            this.transaction_due_before = this.server_date_today;
        },
        outstanding_mode: function () {
            if (this.outstanding_mode === 0) {
                this.total_label = 'Total Due';
            } else {
                this.total_label = 'Total Unpaid';
            }
        },
    },
    created() {
        bus.$on('loadLeaseOutstandingSection', (data) => {
            this.loadForm();
        });
    },
    mixins: [global_mixins],
};
</script>
<style>
.v-expansion-panel-content > .v-expansion-panel-content__wrap {
    padding: 0px;
}
</style>
