<template>
    <div style="max-width: 100%">
        <div
            class="page-form"
            v-if="formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-subheader"
                    ><strong>Email</strong></v-col
                >
            </v-row>
        </div>
        <cirrus-content-loader
            type="table-tbody"
            v-if="loading_setting || loading_setting_icon"
            :show_skeleton_loader="!hide_skeleton_loader"
        ></cirrus-content-loader>
        <v-data-table
            class="c8-datatable-custom"
            v-show="lease_email_list.length > 0 && !loading_setting"
            dense
            item-key="id"
            :headers="headers"
            :items="lease_email_list"
            :items-per-page="items_per_page"
            hide-default-footer
            :page.sync="page"
            @page-count="page_count = $event"
        >
            <template v-slot:item.index="{ item }">
                <span v-if="!item.email_status_message">{{ lease_email_list.indexOf(item) + 1 }}</span>
                <span v-else>
                    <v-tooltip
                        v-model="item.show_error"
                        top
                    >
                        <template v-slot:activator="{ on, attrs }">
                            <v-icon
                                x-small
                                color="red"
                                v-bind="attrs"
                                v-on="on"
                                >mdi-alert</v-icon
                            >
                        </template>
                        <span>{{ item.email_status_message }}</span>
                    </v-tooltip>
                </span>
            </template>
            <template v-slot:item.category_name="{ item }">
                <span v-if="item.source == 1">Manually Added</span>
                <span v-else>{{ item.category_name }}</span>
            </template>
            <template v-slot:item.filename="{ item }">
                <img
                    v-if="item.attachments_count > 0"
                    :src="asset_domain + 'assets/images/icons/pdf.png'"
                    alt="pdf"
                    class="icon"
                    v-for="(data, index) in item.attachments_content"
                    :key="index"
                    @click="downloadFile(data.file)"
                />
            </template>
            <template v-slot:item.letter_email_subject="{ item }">
                <a
                    v-if="item.source == 1"
                    @click="modalOpenAED(item)"
                >
                    {{ item.letter_email_subject }}
                </a>
                <a
                    v-else
                    @click="showViewEmailModal('log', item.letter_history_id, item.email_log_id)"
                >
                    {{ item.letter_email_subject }}
                </a>
            </template>
            <template v-slot:item.letter_recipient="{ item }">
                <p
                    @click="item.expand_email = !item.expand_email"
                    v-html="item.expand_email ? item.letter_recipient : getShortForm(item.letter_recipient)"
                ></p>
            </template>
            <template v-slot:item.date_created_raw="{ item }">
                <table>
                    <tr>
                        <td>
                            <span
                                class="form-input-text no-wrap"
                                v-if="item.source == 1"
                                >{{ formattedDate(item.date_created) }}</span
                            >
                            <span
                                class="form-input-text no-wrap"
                                v-else
                                >{{ item.date_created }}</span
                            >
                        </td>
                        <td v-if="item.source != 1">
                            <v-menu
                                v-model="item.email_log_history_menu"
                                :close-on-content-click="false"
                                :nudge-width="200"
                                offset-x
                                left
                            >
                                <template v-slot:activator="{ on, attrs }">
                                    <v-icon
                                        v-bind="attrs"
                                        @click="loadSentHistory(item.letter_history_id)"
                                        v-on="on"
                                        small
                                        >mdi-history</v-icon
                                    >
                                </template>

                                <v-card>
                                    <v-list dense>
                                        <v-list-item style="min-height: 10px">
                                            <v-list-item-content class="pa-0">
                                                <v-list-item-title class="text-h8 text-center">
                                                    History
                                                </v-list-item-title>
                                            </v-list-item-content>
                                        </v-list-item>
                                    </v-list>

                                    <v-divider></v-divider>
                                    <span v-if="email_log_history.length === 0">Loading...</span>
                                    <v-simple-table
                                        v-if="email_log_history.length > 0"
                                        dense
                                    >
                                        <template v-slot:default>
                                            <thead>
                                                <tr>
                                                    <th class="text-center">#</th>
                                                    <th class="text-center">Date Sent</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    v-for="(data, index) in email_log_history"
                                                    :key="'history_' + index"
                                                >
                                                    <td class="text-center">
                                                        <span v-if="!data.email_status_message">{{
                                                            email_log_history.indexOf(data) + 1
                                                        }}</span>
                                                        <span v-else>
                                                            <v-tooltip
                                                                v-model="data.show_error"
                                                                top
                                                            >
                                                                <template v-slot:activator="{ on, attrs }">
                                                                    <v-icon
                                                                        x-small
                                                                        color="red"
                                                                        v-bind="attrs"
                                                                        v-on="on"
                                                                        >mdi-alert</v-icon
                                                                    >
                                                                </template>
                                                                <span>{{ data.email_status_message }}</span>
                                                            </v-tooltip>
                                                        </span>
                                                    </td>
                                                    <td class="text-center">{{ data.sent_date }}</td>
                                                </tr>
                                            </tbody>
                                        </template>
                                    </v-simple-table>
                                </v-card>
                            </v-menu>
                        </td>
                    </tr>
                </table>
            </template>
            <template v-slot:item.action1="{ item }">
                <v-icon
                    color="red"
                    v-show="!readonly"
                    @click="deleteHistory(item.letter_history_id)"
                    >close</v-icon
                >
            </template>
        </v-data-table>
        <v-row
            class="form-row"
            v-show="lease_email_list.length > 5"
            v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-col
                xs="12"
                sm="12"
                md="12"
            >
                <table class="c8-datatable-custom-footer">
                    <tr>
                        <td class="">Rows per page:</td>
                        <td>
                            <multiselect
                                v-model="items_per_page"
                                :options="[5, 10, 15, 25]"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-200"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </td>
                        <td></td>
                        <td>
                            <v-pagination
                                v-model="page"
                                :length="page_count"
                                :total-visible="5"
                            ></v-pagination>
                        </td>
                    </tr>
                </table>
            </v-col>
        </v-row>

        <v-dialog
            v-model="show_email_info_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Email Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_email_info_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Date Sent</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-icon-date-picker
                                    :id="'sms_' + String(Math.random()).replace('.', '')"
                                    v-model="information_arr.date_sent"
                                    :size="'40'"
                                    data-inverted=""
                                    :edit_form="true"
                                ></cirrus-icon-date-picker>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Subject</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="information_arr.subject"
                                    maxlength="100"
                                    :id="'email_subject'"
                                    data-inverted=""
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Recipients</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="information_arr.recipients"
                                    size=""
                                    :id="'email_recipients'"
                                    data-inverted=""
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Attachment</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-upload-button2
                                    :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                    v-model="information_arr.attachments"
                                    accept_type="pdf"
                                    :has_saved_file="
                                        information_arr.attachments_old !== '' &&
                                        (typeof information_arr.attachments_old === 'string' ||
                                            information_arr.attachments_old instanceof String)
                                    "
                                    :edit_form="true"
                                    :size_limit="20"
                                ></cirrus-single-upload-button2>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        depressed
                        small
                        @click="show_email_info_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="show_view_email_modal"
            max-width="1000"
            content-class="c8-page view-email-modal"
        >
            <v-card>
                <v-card-title class="headline">
                    Email Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_view_email_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <v-alert
                        v-if="email_details_data.email_status === 'error'"
                        dense
                        text
                        :type="
                            !['info', 'warning', 'error', 'success'].includes(email_details_data.email_status)
                                ? 'warning'
                                : email_details_data.email_status
                        "
                        style="text-align: start; border-radius: revert; margin-top: -6px"
                    >
                        {{ email_details_data.email_status_message }}
                    </v-alert>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            Subject
                                        </td>
                                        <td>{{ email_details_data.subject }}</td>
                                    </tr>
                                    <tr v-if="email_details_data.from_email">
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            From
                                        </td>
                                        <td>
                                            {{ email_details_data.from_name }}
                                            {{
                                                email_details_data.from_name === ''
                                                    ? email_details_data.from_email
                                                    : '<' + email_details_data.from_email + '>'
                                            }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            Recipients
                                        </td>
                                        <td class="alert-td">
                                            <div
                                                v-for="(data, index) in email_details_data.recipient_list"
                                                :key="index"
                                                class="recipient-alert"
                                                @mouseover="data.show_details = true"
                                                @mouseleave="data.show_details = false"
                                            >
                                                <span
                                                    class="recipient-email"
                                                    v-html="createMailToLink(data.recipient_email)"
                                                ></span
                                                ><br />
                                                <div
                                                    class="recipient-status"
                                                    v-show="data.show_details || index_2 === 0"
                                                    v-for="(data_2, index_2) in data.recipient_status_list"
                                                    :key="index_2"
                                                >
                                                    {{ data_2.status }} {{ data_2.timestamp }}
                                                </div>
                                            </div>
                                            <div
                                                v-for="(data, index) in email_details_data.cc_list"
                                                :key="index"
                                                class="cc-alert"
                                                @mouseover="data.show_details = true"
                                                @mouseleave="data.show_details = false"
                                            >
                                                <span
                                                    class="cc-email"
                                                    v-html="createMailToLink(data.recipient_email)"
                                                ></span
                                                ><br />
                                                <div
                                                    class="cc-status"
                                                    v-show="data.show_details || index_2 === 0"
                                                    v-for="(data_2, index_2) in data.cc_status_list"
                                                    :key="index_2"
                                                >
                                                    {{ data_2.status }} {{ data_2.timestamp }}
                                                </div>
                                            </div>
                                            <div
                                                v-for="(data, index) in email_details_data.failed_email_list"
                                                :key="index"
                                                class="failed-alert"
                                                @mouseover="data.show_details = true"
                                                @mouseleave="data.show_details = false"
                                            >
                                                <span
                                                    class="failed-email"
                                                    v-html="createMailToLink(data.recipient_email)"
                                                ></span
                                                ><br />
                                                <div
                                                    class="failed-status"
                                                    v-show="data.show_details || index_2 === 0"
                                                    v-for="(data_2, index_2) in data.status_list"
                                                    :key="index_2"
                                                >
                                                    {{ data_2.status }}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr v-if="email_details_data.attachments_count > 0">
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            Attachment(s)
                                        </td>
                                        <td class="alert-td pa-0">
                                            <div
                                                v-for="(data, index) in email_details_data.attachments_content"
                                                :key="index"
                                                @click="downloadFile(data.file)"
                                            >
                                                <img
                                                    :src="asset_domain + 'assets/images/icons/pdf.png'"
                                                    alt="pdf"
                                                    class="icon"
                                                />
                                                {{ data.file_name }}
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                <div v-if="email_details_data.body_raw">
                                    <p>*Images hosted on unsecured sites might not be displayed properly</p>
                                    <iframe
                                        class="modal-iframe"
                                        :srcdoc="email_details_data.body_raw"
                                    ></iframe>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        :loading="loading_page_setting"
                        @click="resendEmail()"
                        v-if="email_details_data.postmark_log_id"
                        depressed
                        small
                    >
                        Re-send &nbsp;
                        <v-icon small>mdi-send</v-icon>
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        @click="show_view_email_modal = false"
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';
export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        edit_form: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_COM_HISTORY',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            loading_setting_icon: false,
            hide_skeleton_loader: false,
            edit_form: false,
            readonly: this.read_only,
            lease_email_list: [],
            letter_category_list: [],
            headers: [
                { text: '#', value: 'index', sortable: false, width: '4%' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Category', value: 'category_name', width: '10%' },
                { text: 'Subject', value: 'letter_email_subject', width: '26%' },
                { text: 'Recipients', value: 'letter_recipient', width: '26%' },
                { text: 'Date Sent', value: 'date_created_raw' },
                { text: 'Download', value: 'filename', width: '10%', align: 'end' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 25,
            search_datatable: '',
            show_email_info_modal: false,
            information_arr: {
                date_sent: null,
                history_id: '',
                attachments: null,
                attachments_old: null,
                recipients: '',
                subject: '',
            },
            show_view_email_modal: false,
            loading_page_setting: false,
            email_details_data: {
                subject: '',
                from_name: '',
                from_email: '',
                recipient_list: [
                    {
                        recipient_email: '',
                        show_details: false,
                        recipient_status_list: [
                            { status: 'Opened', timestamp: '2024/05/06 07:58:57 AM' },
                            { status: 'Delivered', timestamp: '2024/05/06 07:58:57 AM' },
                            { status: 'Processed', timestamp: '2024/05/06 07:58:57 AM' },
                        ],
                    },
                ],
                cc_list: [
                    {
                        recipient_email: '',
                        show_details: false,
                        cc_status_list: [
                            { status: 'Opened', timestamp: '2024/05/06 07:58:57 AM' },
                            { status: 'Delivered', timestamp: '2024/05/06 07:58:57 AM' },
                            { status: 'Processed', timestamp: '2024/05/06 07:58:57 AM' },
                        ],
                    },
                ],
            },
            body_raw: '',
            email_status: '',
            email_status_message: '',
            asset_domain: this.$assetDomain,
            email_log_history: [],
            limit_by: 50,
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
        ]),
    },
    methods: {
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '' || this.forceLoad) {
                this.loadEmail();
            }
        },
        loadEmail: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('limit_by', this.limit_by);
            form_data.append('no_load', true);
            let apiUrl = '';
            apiUrl = 'lease/fetch/email';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_email_list = response.data.lease_email_list;
                this.letter_category_list = response.data.letter_category_list;
                if (
                    this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.items_per_page = this.lease_email_list.length;
                }
                this.loading_setting = false;
            });
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadEmail_' + id;
        },
        modalSubmitData: function () {
            this.error_server_msg2 = [];
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('history_id', this.information_arr.history_id);
            form_data.append('date_sent', this.information_arr.date_sent);
            form_data.append('subject', this.information_arr.subject);
            form_data.append('recipients', this.information_arr.recipients);
            let attachments = [];
            if (this.information_arr.attachments) attachments = this.information_arr.attachments[0];
            form_data.append('attachments', attachments);
            let apiUrl = '';
            apiUrl = 'with-file-upload/system/update-or-create/lease-email';
            this.$api.post(apiUrl, form_data).then((response) => {
                let error_server_msg2 = response.data.error_server_msg2;
                if (error_server_msg2.length === 0) {
                    this.loadForm();
                    if (this.information_arr.history_id === '') {
                        this.information_arr.history_id = response.data.history_id;
                    }
                } else this.error_server_msg2 = error_server_msg2;
                this.loading_setting = false;
            });
        },
        modalOpenAED: function (item) {
            this.show_email_info_modal = true;
            this.information_arr = {
                history_id: item.letter_history_id,
                recipients: item.letter_recipientRaw,
                subject: item.letter_email_subject,
                date_sent: item.date_created,
                attachments: item.filename,
                attachments_old: item.filename,
            };
        },
        async deleteHistory(id) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                form_data.append('id', id);
                let apiUrl = this.cirrus8_api_url + 'api/system/delete/email-history';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loading_setting = false;
                    this.loadForm();
                });
            }
        },
        showViewEmailModal: function (type, id, email_log_id) {
            this.loading_setting_icon = true;
            this.hide_skeleton_loader = true;
            var form_data = new FormData();
            form_data.append('type', type);
            form_data.append('letter_history_id', id);
            form_data.append('email_log_id', email_log_id);
            form_data.append('no_load', true);
            this.$api.post('administration/email/fetch/view-email-details-by-id', form_data).then((response) => {
                this.email_details_data = response.data.email_details_data;
                this.show_view_email_modal = true;
                this.loading_setting_icon = false;
                this.hide_skeleton_loader = false;
            });
        },
        getShortForm(list) {
            let hasSpace = list.includes('; ');
            let emails;
            if (hasSpace) emails = list.split('; ');
            else emails = list.split(';');

            if (emails.length - 1 > 1) return emails[0] + ' and ' + (emails.length - 2) + ' more...';
            return emails[0];
        },
        downloadFile: function (file) {
            window.open('download.php?fileID=' + file, '_blank');
        },
        createMailToLink(text) {
            // Trim text and replace multiple spaces with single space
            text = text.trim().replace(/\s+/g, ' ');
            // Regular expression for validating an email
            var regex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
            if (regex.test(text)) {
                return `<a href="mailto:${text.trim()}">${text.trim()}</a>`;
            } else {
                return text;
            }
        },
        async resendEmail() {
            let id = this.email_details_data.id;
            let postmark_log_id = this.email_details_data.postmark_log_id;

            let batch_no = this.email_details_data.batch_no;
            let sending_status = 'individual';
            if (batch_no) {
                let dialog_prop = {
                    title: 'info',
                    message:
                        'How would you like us to dispatch the selected emails? Choose from one of the following options:',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Resend by batch no', value: 1, color: 'primary' },
                        { label: 'Resend email', value: 2, color: 'secondary' },
                        { label: 'cancel', value: 3 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) sending_status = 'batch';
                if (result === 3) sending_status = 'cancel';
            }
            if (sending_status !== 'cancel') {
                let dialog_prop = {
                    title: 'warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    var form_data = new FormData();
                    form_data.append('queue_id', id);
                    form_data.append('postmark_log_id', postmark_log_id);
                    form_data.append('sending_status', sending_status);
                    axios
                        .get('?module=administration&command=viewPostmarkEmail&action=resend&id=' + postmark_log_id)
                        .then((response) => {
                            if (response) {
                                this.loading_page_setting = false;
                                let dialog_prop = {
                                    title: 'Success',
                                    message: 'Successfully Sent.',
                                    icon_show: true,
                                };
                                cirrusDialog(dialog_prop);
                            }
                            this.loadForm();
                        });
                }
            }
        },
        loadSentHistory: function (letter_history_id) {
            this.email_log_history = [];
            var form_data = new FormData();
            form_data.append('letter_history_id', letter_history_id);
            form_data.append('no_load', true);
            let apiUrl = '';
            apiUrl = 'ui/fetch/email-sent-history';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.email_log_history = response.data.email_log_history;
            });
        },
        formattedDate(datetime) {
            // Extract and return only the date part
            return datetime.split(' ')[0];
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        edit_form: function () {
            if (this.edit_form) {
                this.headers = [
                    { text: '#', value: 'index', sortable: false, width: '4%' },
                    { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                    { text: 'Category', value: 'category_name', width: '10%' },
                    { text: 'Subject', value: 'letter_email_subject', width: '26%' },
                    { text: 'Recipients', value: 'letter_recipient', width: '26%' },
                    { text: 'Date Sent', value: 'date_created_raw' },
                    { text: 'Download', value: 'filename', align: 'end' },
                    { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
                ];
            } else {
                this.headers = [
                    { text: '#', value: 'index', sortable: false, width: '4%' },
                    { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                    { text: 'Category', value: 'category_name', width: '10%' },
                    { text: 'Subject', value: 'letter_email_subject', width: '26%' },
                    { text: 'Recipients', value: 'letter_recipient', width: '26%' },
                    { text: 'Date Sent', value: 'date_created_raw' },
                    { text: 'Download', value: 'filename', align: 'end' },
                ];
            }
        },
    },
    created() {
        bus.$on('loadLeaseEmailSection', (data) => {
            this.limit_by = data.limit_by;
            if (data.property_code === this.property_code && data.lease_code === this.lease_code) {
                this.loadForm();
            }
        });
        bus.$on('openEMAILAEModal', (data) => {
            this.show_email_info_modal = true;
            this.information_arr = {
                history_id: data.history_id,
                recipients: data.recipients,
                subject: data.subject,
                date_sent: data.date_sent,
                attachments: data.attachments,
                attachments_old: data.attachments,
            };
        });
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>
<style lang="scss" scoped>
small {
    display: block;
}
</style>
<style>
.disabled_style {
    background-color: #f5f5f5 !important;
}
</style>
