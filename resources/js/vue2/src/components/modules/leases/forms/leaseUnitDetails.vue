<style>
.tableRaw td {
    margin: 0px;
    padding: 0px;
    border-bottom: 0px;
}
</style>
<template>
    <div>
        <div v-on:dblclick="doubleClickForm()">
            <v-card
                class="section-toolbar"
                dark
                color="titleHeader"
                text
                tile
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">Unit Details</h6>
                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        v-show="
                            !read_only &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                            !pmro_read_only
                        "
                        v-if="!edit_form"
                        icon
                        @click="edit_form = true"
                    >
                        <v-icon>edit</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        v-show="
                            !read_only &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                        "
                        icon
                        @click="loadForm()"
                    >
                        <v-icon>refresh</v-icon>
                    </v-btn>
                    <v-btn
                        icon
                        x-small
                        v-show="!read_only && !new_lease && isLeaseFormLive()"
                        @click="showLeaseActivityModal()"
                    >
                        <v-icon>history</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>
            <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
            <cirrus-server-error
                :error_msg="error_server_msg"
                :errorMsg2="error_server_msg2"
            ></cirrus-server-error>
            <div
                class="page-form"
                v-if="!loading_setting"
            >
                <v-row
                    class="form-row"
                    v-if="lease_status === 'C' && lease_unit_code.field_key != ''"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        :class="edit_form ? 'form-label required' : 'form-label'"
                        >Unit</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">{{ lease_unit_code.field_key }}</span>
                        <!--                    <multiselect v-if="edit_form" v-model="lease_unit_code" :options="lease_unit_vacant_list" group-values="field_group_values" :groupSelect="false" group-label="field_group_names" :group-select="true" class="vue-select2 dropdown-left dropdown-400" :custom-label="nameWithDash" placeholder="Please select ..." track-by="field_key" label="field_value" :show-labels="false"><span slot="noResult">Oops! No elements found. Consider changing the search query.</span></multiselect>-->
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="lease_status === 'L' && edit_form"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        :class="edit_form ? 'form-label required' : 'form-label'"
                        >Unit</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            v-if="edit_form"
                            v-model="lease_unit_code"
                            :options="lease_unit_vacant_list"
                            group-values="field_group_values"
                            :groupSelect="false"
                            group-label="field_group_names"
                            :group-select="true"
                            class="vue-select2 dropdown-left dropdown-400"
                            :custom-label="nameWithDash"
                            placeholder="Please select ..."
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                        >
                            <span slot="noResult"
                                >Oops! No elements found. Consider changing the search query.</span
                            ></multiselect
                        >
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="lease_unit_code.field_key != '' && (lease_status !== 'L' || edit_form)"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Unit Description</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">{{ lease_unit_code.field_value }}</span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="lease_unit_code.field_key != '' && (lease_status !== 'L' || edit_form)"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Unit Area</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span
                            class="form-input-text"
                            v-if="lease_status === 'C'"
                            >{{ lease_unit_area }}
                            <span v-if="lease_unit_area !== 'N/A'"><span v-html="area_unit"></span></span
                        ></span>
                        <span
                            class="form-input-text"
                            v-if="lease_status !== 'C'"
                            >{{ lease_unit_code.unit_area }}
                            <span v-if="lease_unit_area !== 'N/A'"><span v-html="area_unit"></span></span
                        ></span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="
                        lease_unit_code.field_key != '' &&
                        lease_tenant_type === 'RESI' &&
                        (lease_status !== 'L' || edit_form)
                    "
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Unit Bedroom(s)</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">{{ lease_unit_code.unit_bedrooms }}</span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="
                        lease_unit_code.field_key != '' &&
                        lease_tenant_type === 'RESI' &&
                        (lease_status !== 'L' || edit_form)
                    "
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Unit Bathroom(s)</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">{{ lease_unit_code.unit_bathrooms }}</span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="
                        lease_unit_code.field_key != '' &&
                        lease_tenant_type === 'RESI' &&
                        (lease_status !== 'L' || edit_form)
                    "
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Unit Car Park(s)</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text">{{ lease_unit_code.unit_car_parks }}</span>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="lease_status === 'L'"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Unit Status</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text"
                            ><strong>{{ lease_status_list[lease_status] }}</strong
                            ><span v-if="lease_status === 'L'"> (no unit attached)</span>&nbsp</span
                        >
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="lease_unit_code.field_key !== '' && lease_status === 'C'"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        :class="
                            edit_form &&
                            page_form_type !== 'client-summary-page' &&
                            page_form_type !== 'client-new-page'
                                ? 'form-label required'
                                : 'form-label'
                        "
                        >Occupancy Dates</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-icon-date-picker
                            :size="'40'"
                            :id="'lease_occupancy_start_date' + String(Math.random()).replace('.', '')"
                            v-model="lease_occupancy_start_date"
                            :edit_form="
                                edit_form &&
                                page_form_type !== 'client-summary-page' &&
                                page_form_type !== 'client-new-page'
                            "
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                        &nbsp to &nbsp
                        <cirrus-icon-date-picker
                            :size="'40'"
                            :id="'lease_occupancy_end_date' + String(Math.random()).replace('.', '')"
                            v-model="lease_occupancy_end_date"
                            :edit_form="
                                edit_form &&
                                page_form_type !== 'client-summary-page' &&
                                page_form_type !== 'client-new-page'
                            "
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="lease_status === 'C'"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        :class="
                            edit_form && page_form_type !== 'client-new-page' ? 'form-label required' : 'form-label'
                        "
                        >Unit Status</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text"
                            ><strong>{{ lease_unit_status_list[lease_status] }}</strong
                            ><span v-if="lease_status === 'L'"> (no unit attached)</span>&nbsp</span
                        >
                        <span
                            v-if="edit_form && page_form_type !== 'client-new-page'"
                            class="form-input-text"
                            ><v-btn
                                x-small
                                depressed
                                @click="vacateUnit()"
                                >vacate lease</v-btn
                            ></span
                        >
                        <span
                            v-if="edit_form && page_form_type === 'client-new-page'"
                            class="form-input-text"
                            ><v-btn
                                x-small
                                depressed
                                @click="vacateUnit()"
                                >Detach Unit</v-btn
                            ></span
                        >
                        <cirrus-icon-date-picker
                            v-if="edit_form && page_form_type !== 'client-new-page'"
                            :size="'40'"
                            :id="'lease_last_date_occupied' + String(Math.random()).replace('.', '')"
                            v-model="lease_last_date_occupied"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                        <span
                            v-if="edit_form && page_form_type !== 'client-new-page'"
                            class="form-input-text"
                            >« Select the last date the tenant occupied the unit</span
                        >
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Lease Status</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <span class="form-input-text v-step-new-lease-code-label">
                            {{ lease_status_list[lease_status] }}
                        </span>
                        <v-btn
                            x-small
                            depressed
                            color="normal"
                            @click="deleteLease()"
                            v-if="user_type === 'A'"
                            v-show="edit_form && check_chargers_flag === '0'"
                            >Delete Lease</v-btn
                        >
                        <v-btn
                            x-small
                            depressed
                            color="normal"
                            @click="toggleLeaseStatus()"
                            v-show="edit_form && isLeaseFormLive() && lease_unit_code.field_key === ''"
                            >Change Status</v-btn
                        >
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="lease_status === 'C' && edit_form && page_form_type !== 'client-new-page'"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Close Diary items on vacate</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                        style="padding-top: 9px"
                    >
                        <sui-checkbox
                            v-if="edit_form"
                            v-model="lease_close_item_on_vacate"
                        />
                        <span
                            class="form-input-text"
                            v-if="!edit_form && lease_close_item_on_vacate"
                            >Yes</span
                        >
                        <span
                            class="form-input-text"
                            v-if="!edit_form && !lease_close_item_on_vacate"
                            >No</span
                        >
                    </v-col>
                </v-row>
            </div>
            <v-alert
                text
                prominent
                dense
                type="error"
                v-if="lease_status === 'C' && lease_unit_code.field_key === ''"
            >
                No unit has been found for this lease, even though it is listed as occupied.
            </v-alert>
            <br />
            <v-divider></v-divider>
            <v-card
                elevation="0"
                v-if="edit_form && lease_unit_code.field_key !== ''"
            >
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="attachUnit()"
                        color="success"
                        dark
                        small
                        depressed
                        v-if="lease_status === 'L'"
                    >
                        Attach Unit
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="saveForm()"
                        color="success"
                        dark
                        small
                        depressed
                        v-if="lease_status !== 'L'"
                    >
                        Save Unit Details
                    </v-btn>
                </v-card-actions>
            </v-card>
        </div>
        <v-divider></v-divider>
        <lease-unit-lease-charges-component
            id="leaseUnitChargesSection"
            :occupancy_start_date="lease_occupancy_start_date"
            :property_code="property_code"
            :lease_code="lease_code"
            :lease_unit_code="lease_unit_code.field_key"
            :lease_unit_area="lease_unit_area"
            :lease_status="lease_status"
            :version_id="version_id"
            :current_db="current_db"
            :user_type="user_type"
            :username="username"
            :read_only="read_only"
            :forceLoad="force_load_history"
            :new_lease="new_lease"
            :page_form_type="page_form_type"
            :edit_flag="edit_flag"
            :pmro_read_only="pmro_read_only"
            :is_inactive="is_inactive"
            :country_default_settings="country_default_settings"
        ></lease-unit-lease-charges-component>
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import LeaseUnitHistory from './leaseUnitHistory.vue';
import LeaseUnitLeaseCharges from './leaseUnitLeaseCharges.vue';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        show_unit_history: { type: Boolean, default: true },
        page_form_type: { type: String, default: '' },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
        country_default_settings: { type: String, default: '' },
    },
    components: {
        'lease-unit-history-component': LeaseUnitHistory,
        'lease-unit-lease-charges-component': LeaseUnitLeaseCharges,
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_UNIT_DETAIL',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            lease_last_date_occupied: '31/12/2999',
            lease_last_date_occupied_old: '31/12/2999',
            lease_close_item_on_vacate: false,
            lease_close_item_on_vacate_old: false,
            lease_occupancy_start_date: null,
            lease_occupancy_start_date_old: null,
            lease_occupancy_end_date: null,
            lease_occupancy_end_date_old: null,
            lease_unit_area_id: 0,
            lease_unit_code: {
                value: '',
                label: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            lease_unit_code_old: {
                value: '',
                label: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            lease_unit_description: '',
            lease_unit_description_old: '',
            lease_unit_area: '',
            lease_unit_area_old: '',
            lease_unit_bedrooms: '',
            lease_unit_bedrooms_old: '',
            lease_unit_bathrooms: '',
            lease_unit_bathrooms_old: '',
            lease_unit_car_parks: '',
            lease_unit_car_parks_old: '',
            lease_unit_status: '',
            lease_unit_status_list: [],
            lease_status_list: [],
            force_load_history: false,
            lease_status: '',
            check_chargers_flag: '1',
            lease_tenant_status: '',
            lease_tenant_type: '',
            lease_unit_list: [],
            lease_unit_vacant_list: [],
            show_activity_log_modal: false,
            area_unit: 'm&sup2;',
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
        ]),
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
    },
    methods: {
        ...mapMutations([
            'SET_LEASE_MAIN_DETAILS',
            'SET_LEASE_UNIT_DETAILS',
            'SET_LEASE_CHARGE_DETAILS',
            'SET_LEASE_OLD_MAIN_DETAILS',
            'SET_LEASE_MAIN_DETAILS_TENANT_STATUS',
            'SET_TOUR_STEPS',
        ]),
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadUnitDetails();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_last_date_occupied = this.lease_last_date_occupied_old;
            this.lease_close_item_on_vacate = this.lease_close_item_on_vacate_old;
            this.lease_occupancy_start_date = this.lease_occupancy_start_date_old;
            this.lease_occupancy_end_date = this.lease_occupancy_end_date_old;
            this.lease_unit_area_id = 0;
            this.lease_unit_code = this.lease_unit_code_old;
            this.lease_unit_description = this.lease_unit_description_old;
            this.lease_unit_area = this.lease_unit_area_old;
            this.lease_unit_bedrooms = this.lease_unit_bedrooms_old;
            this.lease_unit_bathrooms = this.lease_unit_bathrooms_old;
            this.lease_unit_car_parks = this.lease_unit_car_parks_old;
        },
        addLine: function () {},
        loadUnitDetails: function () {
            if (this.lease_details.main_form !== undefined) {
                if (Object.keys(this.lease_details.main_form).length > 0) {
                    this.loading_setting = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    // form_data.append('lease_commencement_date', this.lease_details.main_form.lease_commencement_date);
                    form_data.append('version_id', this.version_id);
                    form_data.append('no_load', true);
                    let apiUrl = this.cirrus8_api_url + 'api/lease/fetch/unit-details';
                    if (this.isLeaseFormLive()) {
                        //get data from live
                        apiUrl = this.cirrus8_api_url + 'api/lease/fetch/unit-details';
                    } else {
                        apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/unit-details';
                    }

                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.lease_unit_status = response.data.lease_unit_status;
                        this.lease_unit_status_list = response.data.lease_unit_status_list;
                        this.lease_status_list = response.data.lease_status_list;
                        this.SET_LEASE_MAIN_DETAILS_TENANT_STATUS(response.data.lease_status);
                        this.lease_last_date_occupied = response.data.lease_last_date_occupied;
                        this.lease_close_item_on_vacate = response.data.lease_close_item_on_vacate;
                        this.lease_occupancy_start_date = response.data.lease_occupancy_start_date;
                        this.lease_occupancy_end_date = response.data.lease_occupancy_end_date;
                        this.lease_unit_area_id = response.data.lease_unit_area_id;
                        let lease_unit_code = response.data.lease_unit_code;
                        let lease_unit_details = response.data.lease_unit_details;
                        this.lease_unit_list = response.data.lease_unit_list;
                        this.lease_unit_vacant_list = response.data.lease_unit_vacant_list;
                        this.lease_unit_description = response.data.lease_unit_description;
                        this.lease_unit_area = response.data.lease_unit_area;
                        this.lease_unit_bedrooms = response.data.lease_unit_bedrooms;
                        this.lease_unit_bathrooms = response.data.lease_unit_bathrooms;
                        this.lease_unit_car_parks = response.data.lease_unit_car_parks;
                        if (lease_unit_code !== '')
                            this.lease_unit_code = {
                                field_key: lease_unit_details.unit_code,
                                field_value: lease_unit_details.unit_description,
                            };
                        if (lease_unit_code !== '')
                            this.lease_unit_code_old = {
                                field_key: lease_unit_details.unit_code,
                                field_value: lease_unit_details.unit_description,
                            };
                        this.lease_last_date_occupied_old = response.data.lease_last_date_occupied;
                        this.lease_close_item_on_vacate_old = response.data.lease_close_item_on_vacate;
                        this.lease_occupancy_start_date_old = response.data.lease_occupancy_start_date;
                        this.lease_occupancy_end_date_old = response.data.lease_occupancy_end_date;
                        this.lease_unit_list_old = response.data.lease_unit_list;
                        this.lease_unit_description_old = response.data.lease_unit_description;
                        this.lease_unit_area_old = response.data.lease_unit_area;
                        this.lease_unit_bedrooms_old = response.data.lease_unit_bedrooms;
                        this.lease_unit_bathrooms_old = response.data.lease_unit_bathrooms;
                        this.lease_unit_car_parks_old = response.data.lease_unit_car_parks;

                        this.lease_status = response.data.lease_status;
                        this.check_chargers_flag = response.data.check_chargers;
                        this.lease_tenant_status = response.data.lease_tenant_status;
                        this.lease_tenant_type = response.data.lease_tenant_type;
                        this.SET_LEASE_UNIT_DETAILS(response.data);
                        this.loading_setting = false;

                        this.area_unit = response.data.area_unit;

                        bus.$emit('loadLeaseRentReviewSection', { unit_code: lease_unit_code });
                        bus.$emit('loadLeaseUnitHistorySection', { unit_code: lease_unit_code });
                    });
                }
            }
        },
        saveForm: function () {
            let errorArr = [];
            let lease_occupancy_start_date = this.lease_occupancy_start_date;
            let lease_occupancy_end_date = this.lease_occupancy_end_date;
            let lease_unit_description = this.lease_unit_description;
            let lease_unit_area = this.lease_unit_area;
            let lease_unit_bathrooms = this.lease_unit_bathrooms;
            let lease_unit_bedrooms = this.lease_unit_bedrooms;
            let lease_unit_car_parks = this.lease_unit_car_parks;
            let lease_close_item_on_vacate = this.lease_close_item_on_vacate;
            if (this.user_type === 'A') {
                if (lease_occupancy_end_date === '') {
                    errorArr.push(['You have not entered a valid end date.']);
                }
            } else {
                if (lease_occupancy_start_date === '') {
                    errorArr.push(['You have not entered a valid start date.']);
                }
                if (lease_occupancy_end_date === '') {
                    errorArr.push(['You have not entered a valid end date.']);
                }
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('lease_unit_area_id', this.lease_unit_area_id);
                form_data.append('lease_unit_code', this.lease_unit_code.field_key);
                form_data.append('lease_occupancy_start_date', lease_occupancy_start_date);
                form_data.append('lease_occupancy_end_date', lease_occupancy_end_date);
                form_data.append('lease_unit_description', lease_occupancy_end_date);
                form_data.append('lease_unit_area', this.lease_unit_code.unit_area);
                form_data.append('lease_unit_bathrooms', lease_unit_bathrooms);
                form_data.append('lease_unit_bedrooms', lease_unit_bedrooms);
                form_data.append('lease_unit_car_parks', lease_unit_car_parks);
                form_data.append('lease_close_item_on_vacate', lease_close_item_on_vacate);

                let apiUrl = this.cirrus8_api_url + 'api/lease/update/unit';
                if (this.isLeaseFormLive()) {
                    //get data from live
                    apiUrl = this.cirrus8_api_url + 'api/lease/update/unit';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/unit';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadForm();
                    if (this.edit_flag) {
                        this.edit_form = true;
                    }
                    this.force_load_history = true; //trigger the watch from unit history component
                    this.force_load_history = false; //stop the function from history component (re-render)
                    // this.edit_form = false;
                    this.loading_setting = false;

                    bus.$emit('loadLeaseChargeSection', '');
                });
            }
        },
        getValueInGroupList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((p) => {
                    let field_group_values = p.field_group_values.findIndex((c) => {
                        return c.field_key === param1;
                    });
                    return field_group_values !== -1;
                });
                if (filtered.length > 0) {
                    let field_group_values = filtered[0].field_group_values.filter((m) => m.field_key === param1);
                    return field_group_values[0];
                } else {
                    return {
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        vacateUnitFunc: function () {
            let lease_last_date_occupied = this.lease_last_date_occupied;
            let lease_close_item_on_vacate = this.lease_close_item_on_vacate;
            let errorArr = [];
            if (lease_last_date_occupied === '') {
                errorArr.push(['You have not entered a valid vacant date.']);
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('lease_unit_area_id', this.lease_unit_area_id);
                form_data.append('lease_unit_code', this.lease_unit_code.field_key);
                form_data.append('lease_last_date_occupied', lease_last_date_occupied);
                form_data.append('lease_close_item_on_vacate', lease_close_item_on_vacate);

                let apiUrl = this.cirrus8_api_url + 'api/lease/update/vacate-unit';
                if (this.isLeaseFormLive()) {
                    //get data from live
                    apiUrl = this.cirrus8_api_url + 'api/lease/update/vacate-unit';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/vacate-unit';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadForm();
                    this.force_load_history = true; //trigger the watch from unit history component
                    this.force_load_history = false; //stop the function from history component (re-render)
                    // this.edit_form = false;
                    this.loading_setting = false;
                    bus.$emit('loadLeaseDiarySection', '');
                    bus.$emit('loadLeaseChargeSection', '');
                    bus.$emit('loadLastDateOccupied', '');
                });
            }
        },
        async vacateUnit() {
            if (this.page_form_type === 'client-new-page') {
                this.vacateUnitFunc();
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure you want to vacate this lease? Doing so will also stop all charges.',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes, vacate it', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.vacateUnitFunc();
                }
            }
        },
        async attachUnit() {
            let lease_unit_code = this.lease_unit_code.field_key;

            if (!!!lease_unit_code) {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Please select unit.',
                    icon_show: true,
                };
                const result = await cirrusDialog(dialog_prop);
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure you want to attach this unit?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes, attach it', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('no_load', true);

                    form_data.append('lease_unit_code', this.lease_unit_code.field_key);
                    form_data.append('unit_area_id', this.lease_unit_code.unit_area_id);
                    form_data.append('lease_occupancy_start_date', this.lease_occupancy_start_date);
                    form_data.append('lease_last_date_occupied', this.lease_occupancy_end_date);

                    let apiUrl = this.cirrus8_api_url + 'api/lease/update-or-create/attach-unit';
                    if (this.isLeaseFormLive()) {
                        //get data from live
                        apiUrl = this.cirrus8_api_url + 'api/lease/update-or-create/attach-unit';
                    } else {
                        apiUrl = this.cirrus8_api_url + 'api/temp/lease/update-or-create/attach-unit';
                    }
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.loadForm();
                        this.force_load_history = true; //trigger the watch from unit history component
                        this.force_load_history = false; //stop the function from history component (re-render)
                        // this.edit_form = false;
                        this.loading_setting = false;
                        bus.$emit('loadLeaseChargeSection', '');
                    });
                }
            }
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        async deleteLease() {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.lease_status_index = 1;
                let form_data = new FormData();
                form_data.append('page_source', 'leaseFormTemplate');
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('no_load', true);
                this.$api.post('lease/delete/lease-main', form_data).then((response) => {
                    let error_server_msg2 = response.data.error_server_msg;
                    this.error_server_msg2 = error_server_msg2;
                    if (error_server_msg2.length === 0) {
                        // this.lease_code = { field_key: '', field_value: 'Please select...' }; clearLeaseDropdown
                        bus.$emit('clearLeaseDropdown', '');
                    }
                });
            }
        },
        async toggleLeaseStatus() {
            let dialog_prop = {
                title: 'Warning',
                message:
                    'You need to vacate the tenant at the appropriate date using the vacate lease button under unit details section. Click OK to continue with the change or Cancel to revert back to the original setting.',
                icon_show: true,
                buttons_right: [
                    { label: 'Ok', value: 1, color: 'primary' },
                    { label: 'Cancel', value: 2 },
                ],
            };
            if (this.lease_status === 'L') {
                dialog_prop = {
                    title: 'Warning',
                    message:
                        'You need to ensure the tenant is occupying a unit at the property level before effecting this change. Click OK to continue with the change or Cancel to revert back to the original setting.',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Ok', value: 1, color: 'primary' },
                        { label: 'Cancel', value: 2 },
                    ],
                };
            }
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.lease_status_index = 1;
                let form_data = new FormData();
                form_data.append('page_source', 'leaseFormTemplate');
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('no_load', true);
                this.$api.post('lease/update/toggle-lease-status', form_data).then((response) => {
                    let error_server_msg2 = response.data.error_server_msg;
                    this.error_server_msg2 = error_server_msg2;
                    if (error_server_msg2.length === 0) {
                        // this.lease_code = { field_key: '', field_value: 'Please select...' };
                        this.loadForm();
                    }
                });
            }
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadLeaseUnitDetailsSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
