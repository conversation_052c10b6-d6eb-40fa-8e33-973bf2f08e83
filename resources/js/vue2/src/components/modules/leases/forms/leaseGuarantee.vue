<style>
.icon.amount-field-icon {
    font-size: 12px;
    top: 10px !important;
    left: 0px !important;
    font-weight: 500;
}
</style>
<template>
    <div
        v-on:dblclick="doubleClickForm()"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Lease Guarantee</h6>
                <v-spacer></v-spacer>
                <h6
                    class="title font-weight-black"
                    style="padding-right: 3px"
                    v-if="
                        isLeaseFormLive() &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-show="client_country == 'AU'"
                >
                    Apply for Bank Guarantee in
                </h6>
                <v-btn
                    @click="show_assuro_modal = true"
                    small
                    color="normal"
                    v-if="
                        isLeaseFormLive() &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-show="client_country == 'AU'"
                >
                    <img
                        :src="asset_domain + 'assets/images/assuro_logo.png'"
                        class="icon"
                        width="80"
                        height="15"
                    />
                </v-btn>
                <cirrus-input
                    inputFormat="search"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form && lease_guarantee_list.length > 0"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly && !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-col
                    class="text-center"
                    v-if="lease_guarantee_list.length === 0"
                    v-show="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-btn
                        v-if="!pmro_read_only"
                        depressed
                        small
                        color="success"
                        @click="modalAddData()"
                        >Add Guarantee</v-btn
                    >
                    <div
                        style="margin: 10px 0px"
                        v-else
                    >
                        No lease guarantees at the moment
                    </div>
                </v-col>

                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    :item-class="itemRowBackground"
                    v-show="lease_guarantee_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="lease_guarantee_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.index="{ item }">
                        {{ lease_guarantee_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.guarantee_type_description="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.guarantee_type_description }}</span>
                        </div>
                    </template>
                    <template v-slot:item.guarantee_person_name="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.guarantee_person_name }}</span>
                        </div>
                    </template>
                    <template v-slot:item.guarantee_bank_name="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.guarantee_bank_name }}</span>
                        </div>
                    </template>
                    <template v-slot:item.guarantee_amount="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><span v-if="item.guarantee_amount !== '' && item.guarantee_amount !== null">{{
                                    currency_symbol
                                }}</span
                                >{{ accountingAmountFormat(numberWithCommas(roundTo(item.guarantee_amount, 2))) }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.guarantee_accrued_interest="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><span
                                    v-if="
                                        item.guarantee_accrued_interest !== '' &&
                                        item.guarantee_accrued_interest !== null
                                    "
                                    >{{ currency_symbol }}</span
                                >{{
                                    accountingAmountFormat(
                                        numberWithCommas(roundTo(item.guarantee_accrued_interest, 2)),
                                    )
                                }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.guarantee_expiry_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.guarantee_expiry_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.guarantee_notes="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.guarantee_notes }}</span>
                        </div>
                    </template>
                    <template v-slot:item.guarantee_file="{ item }">
                        <cirrus-single-upload-button2
                            :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                            v-model="item.guarantee_file"
                            accept_type="pdf"
                            :has_saved_file="
                                item.guarantee_file_old !== '' &&
                                (typeof item.guarantee_file_old === 'string' ||
                                    item.guarantee_file_old instanceof String)
                                    ? true
                                    : false
                            "
                            :edit_form="false"
                            :error_msg="error_msg"
                            :size_limit="20"
                        ></cirrus-single-upload-button2>
                    </template>
                    <template v-slot:item.guarantee_diary="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><label>{{ item.guarantee_diarise_id ? '' : 'Not ' }}Diarised</label></span
                            >
                        </div>
                    </template>
                    <template v-slot:item.action0="{ item }">
                        <a
                            v-if="item.assuro_id"
                            :href="item.assuro_link"
                            target="_blank"
                            title="Click this link to go to Assuro"
                            :data-tooltip="item.assuro_status"
                        >
                            <img
                                :src="asset_domain + 'assets/images/assuro_logo_16px.png'"
                                class="icon"
                                width="20"
                                alt="Information Logo"
                            />
                        </a>
                    </template>
                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenAED(lease_guarantee_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit</v-icon
                        >
                        <v-icon
                            color="red"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="deleteGuarantee(lease_guarantee_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="lease_guarantee_list.length > 5"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteGuarantee(lease_guarantee_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Guarantee Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="lease_guarantee_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        lease_guarantee_arr.index === 'New'
                                            ? lease_guarantee_arr.index
                                            : lease_guarantee_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        data-inverted=""
                                        openDirection="bottom"
                                        v-model="lease_guarantee_arr.guarantee_type"
                                        :options="lease_guarantee_type_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-800"
                                        group-label="language"
                                        placeholder="Select a guarantee type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Person</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="lease_guarantee_arr.guarantee_person_name"
                                        size=""
                                        :id="'guarantee_person_name'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Bank Name</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="lease_guarantee_arr.guarantee_bank_name"
                                        size=""
                                        :id="'guarantee_bank_name'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Amount</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <div class="ui left icon input">
                                        <cirrus-input
                                            v-model="lease_guarantee_arr.guarantee_amount"
                                            :inputFormat="'dollar'"
                                            size=""
                                            :id="'guarantee_amount'"
                                            data-inverted=""
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                        <i class="icon amount-field-icon">{{ currency_symbol }}</i>
                                    </div>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Accrued Interest</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <div class="ui left icon input">
                                        <cirrus-input
                                            v-model="lease_guarantee_arr.guarantee_accrued_interest"
                                            :inputFormat="'dollar'"
                                            size=""
                                            :id="'guarantee_accrued_interest'"
                                            data-inverted=""
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                        <i class="icon amount-field-icon">{{ currency_symbol }}</i>
                                    </div>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Expiry Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'lease_guarantee_expiry' + String(Math.random()).replace('.', '')"
                                        v-model="lease_guarantee_arr.guarantee_expiry_date"
                                        data-inverted=""
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                    <span
                                        v-bind:class="lease_guarantee_arr.guarantee_diarise_id ? 'hidden' : ''"
                                        style="position: absolute; margin-top: 4px"
                                    >
                                        <v-checkbox
                                            v-model="lease_guarantee_arr.guarantee_diary"
                                            label="Diarise"
                                            ripple="false"
                                            dense
                                        ></v-checkbox>
                                    </span>
                                    <label
                                        class="form-input-text"
                                        v-bind:class="!lease_guarantee_arr.guarantee_diarise_id ? 'hidden' : ''"
                                        >{{ lease_guarantee_arr.guarantee_diarise_id ? '' : 'Not ' }}Diarised</label
                                    >
                                    <input
                                        type="hidden"
                                        v-model="lease_guarantee_arr.guarantee_diary"
                                        :id="'guarantee_diary'"
                                        :error_msg="error_msg"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Note</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="lease_guarantee_arr.guarantee_notes"
                                        size=""
                                        :id="'guarantee_notes'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >File(s)</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-upload-button2
                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                        v-model="lease_guarantee_arr.guarantee_file"
                                        :edit_form="true"
                                        :has_saved_file="
                                            lease_guarantee_arr.guarantee_file_old !== '' &&
                                            (typeof lease_guarantee_arr.guarantee_file_old === 'string' ||
                                                lease_guarantee_arr.guarantee_file_old instanceof String)
                                                ? true
                                                : false
                                        "
                                        :error_msg="error_msg"
                                        accept_type="pdf"
                                        :size_limit="20"
                                    ></cirrus-single-upload-button2>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="lease_guarantee_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="lease_guarantee_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteGuarantee(lease_guarantee_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="lease_guarantee_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="show_assuro_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <assuro-form-section
                :property_code="property_code"
                :lease_code="lease_code"
            />
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import AssuroForm from '../../Assuro/Forms/AssuroFormModal.vue';

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    components: {
        'assuro-form-section': AssuroForm,
    },
    data() {
        return {
            asset_domain: this.$assetDomain,
            form_type: 'LEASE',
            form_section: 'LEASE_GUARANTEE',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            success_flag: false,
            super_user: '0',
            lease_guarantee_list: [],
            lease_guarantee_list_old: [],
            lease_guarantee_type_list: [],
            readonly: this.read_only,
            show_activity_log_modal: false,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Type', value: 'guarantee_type_description', sortable: false },
                { text: 'Person', value: 'guarantee_person_name', sortable: false, width: '15%' },
                { text: 'Bank Name', value: 'guarantee_bank_name', sortable: false, width: '15%' },
                { text: 'Amount', value: 'guarantee_amount', sortable: false, width: '8%', align: 'end' },
                {
                    text: 'Accrued Interest',
                    value: 'guarantee_accrued_interest',
                    sortable: false,
                    width: '8%',
                    align: 'end',
                },
                { text: 'Exp.Date', value: 'guarantee_expiry_date_raw', sortable: false, width: '8%' },
                { text: 'Note', value: 'guarantee_notes', sortable: false, width: '8%' },
                { text: 'File(s)', value: 'guarantee_file', sortable: false, width: '8%' },
                { text: 'Diarise', value: 'guarantee_diary', sortable: false, width: '10%' },
                { text: '', value: 'action0', align: 'end', sortable: false, width: '39px' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            AED_modal: false,
            lease_guarantee_arr: [],
            modal_current_ctr: 0,
            show_assuro_modal: false,
            currency_symbol: '$',
            client_country: '',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'auto_diarise',
        ]),
    },
    methods: {
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        itemRowBackground: function (item) {
            return item.assuro_id ? 'assuro_style' : '';
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadLeaseGuarantee();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_guarantee_list = [];
            this.lease_guarantee_list = JSON.parse(JSON.stringify(this.lease_guarantee_list_old));
        },
        modalAddData: function () {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.lease_guarantee_arr = {
                index: 'New',
                guarantee_id: '',
                guarantee_type: {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                guarantee_type_description: '',
                guarantee_person_name: '',
                guarantee_bank_name: '',
                guarantee_amount: '',
                guarantee_accrued_interest: '',
                guarantee_expiry_date: d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                guarantee_notes: '',
                guarantee_file: null,
                guarantee_file_old: null,
                guarantee_diarise_id: '',
                status: 'new',
                guarantee_diary: this.auto_diarise,
            };
        },
        loadLeaseGuarantee: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';

            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/guarantee';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/guarantee';
            }
            //   apiUrl = this.cirrus8_api_url + 'api/lease/loadTempLeaseGuarantee';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_guarantee_list = response.data.lease_guarantee_list;
                this.lease_guarantee_list_old = response.data.lease_guarantee_list;
                this.lease_guarantee_type_list = response.data.lease_guarantee_type_list;
                this.super_user = response.data.super_user;
                if (this.formSectionReadOnly(this.pm_lease_form_read_only, this.form_type, this.form_section)) {
                    this.items_per_page = this.lease_guarantee_list.length;
                }

                this.currency_symbol = response.data.currency_symbol;
                this.client_country = response.data.client_country ? response.data.client_country : 'AU';

                this.loading_setting = false;
            });
        },
        modalSubmitData: function () {
            let errorArr = [];
            let guarantee_type = this.lease_guarantee_arr.guarantee_type.value;
            let guaranteePerson = this.lease_guarantee_arr.guarantee_person_name;
            let guaranteeBank = this.lease_guarantee_arr.guarantee_bank_name;
            let guarantee_amount = this.lease_guarantee_arr.guarantee_amount;
            let guarantee_accrued_interest = this.lease_guarantee_arr.guarantee_accrued_interest;
            if (guarantee_type === '') {
                errorArr.push(['You have not entered a valid guarantee type for the guarantee.']);
            }
            if (guaranteePerson === '') {
                errorArr.push(['You have not entered a valid person for the guarantee.']);
            }
            if (guaranteeBank === '') {
                errorArr.push(['You have not entered a valid bank name for the guarantee.']);
            }
            if (guarantee_amount === '') {
                errorArr.push(['You have not used a valid premium amount.']);
            } else {
                if (isNaN(parseFloat(guarantee_amount))) {
                    errorArr.push(['You have not used a valid premium amount.']);
                }
            }
            if (guarantee_accrued_interest === '' || !guarantee_accrued_interest) {
            } else {
                if (isNaN(parseFloat(guarantee_accrued_interest))) {
                    errorArr.push(['You have not used a valid accrued interest amount.']);
                }
            }

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                let save_arr = [];
                save_arr[0] = this.lease_guarantee_arr;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('lease_guarantee_list', JSON.stringify(save_arr));

                let guarantee_file = [];
                if (this.lease_guarantee_arr.guarantee_file)
                    guarantee_file = this.lease_guarantee_arr.guarantee_file[0];
                form_data.append('lease_guarantee_file', guarantee_file);

                let apiUrl = '';
                if (this.isLeaseFormLive())
                    apiUrl = this.cirrus8_api_url + 'api/with-file-upload/lease/update-or-create/guarantee';
                else apiUrl = this.cirrus8_api_url + 'api/with-file-upload/temp/lease/update-or-create/guarantee';

                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        if (response.data.diary == 1) {
                            bus.$emit('loadLeaseDiarySection', '');
                        }
                        this.lease_guarantee_list = [];
                        this.loadForm();
                        this.edit_form = this.edit_flag;
                        this.loading_setting = false;
                        if (!response.data.error) this.success_flag = true;

                        if (this.lease_guarantee_arr.status === 'new') {
                            this.lease_guarantee_arr.index = this.lease_guarantee_list.length;
                            this.lease_guarantee_arr.guarantee_id = response.data.guarantee_id;
                            this.lease_guarantee_arr.guarantee_file = response.data.guarantee_file;
                            this.lease_guarantee_arr.guarantee_file_old = response.data.guarantee_file;
                            this.lease_guarantee_arr.guarantee_diarise_id = response.data.guarantee_diarise_id;
                            this.lease_guarantee_arr.status = 'saved';
                        }

                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    });
            }
        },
        async deleteGuarantee(index) {
            if (index !== 'New') {
                let status = this.lease_guarantee_list[index].status;
                let guarantee_id = this.lease_guarantee_list[index].guarantee_id;

                if (status === 'new') {
                    this.lease_guarantee_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('lease_code', this.lease_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('no_load', true);
                        form_data.append('guarantee_id', guarantee_id);
                        let apiUrl = '';
                        if (this.isLeaseFormLive()) {
                            apiUrl = this.cirrus8_api_url + 'api/lease/delete/guarantee';
                        } else {
                            apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/guarantee';
                        }
                        //  apiUrl = this.cirrus8_api_url + 'api/lease/deleteTempLeaseGuarantee';
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.lease_guarantee_list.splice(index, 1);
                            this.loading_setting = false;

                            this.lease_guarantee_list_old = JSON.parse(JSON.stringify(this.lease_guarantee_list));
                        });
                    }
                }
            }
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadGuarantee_' + id;
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        modalPrevData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_guarantee_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.lease_guarantee_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.lease_guarantee_arr = this.lease_guarantee_list[this.modal_current_ctr];
            this.lease_guarantee_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_guarantee_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.lease_guarantee_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.lease_guarantee_arr = this.lease_guarantee_list[this.modal_current_ctr];
            this.lease_guarantee_arr.index = this.modal_current_ctr;
            // bus.$emit("refreshSingleUploadComponent","");
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_guarantee_arr = this.lease_guarantee_list[index];
            this.lease_guarantee_arr.index = index;
            this.modal_current_ctr = index;
            bus.$emit('refreshSingleUploadComponent', '');
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        show_assuro_modal: function () {
            bus.$emit('loadAssuroForm', '');
        },
    },
    created() {
        bus.$on('loadLeaseGuaranteeSection', (data) => {
            this.loadForm();
        });
        bus.$on('closeGuaranteeAssuroModal', (data) => {
            this.show_assuro_modal = false;
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
<style>
.assuro_style {
    background-color: #f5f5f5;
}
</style>
