<style>
.tableRaw td {
    margin: 0px;
    padding: 0px;
    border-bottom: 0px;
}
</style>
<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Direct Management Fee:</h6>
                &nbsp

                <span
                    class="title"
                    v-show="read_only || !edit_form"
                    v-if="lease_direct_fees === 0"
                    ><strong>Yes</strong></span
                >
                <span
                    class="title"
                    v-show="read_only || !edit_form"
                    v-if="lease_direct_fees === 1"
                    ><strong>No</strong></span
                >
                <v-btn-toggle
                    class="form-toggle"
                    v-show="!read_only && edit_form"
                    v-model="lease_direct_fees"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                        :disabled="!edit_form"
                    >
                        Yes
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                        :disabled="!edit_form"
                    >
                        No
                    </v-btn>
                </v-btn-toggle>

                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>

                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form"
                    icon
                    @click="saveForm()"
                >
                    <v-icon
                        light
                        color="green"
                        >check</v-icon
                    >
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>

        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    :class="lease_direct_fees === 0 && edit_form ? 'form-label required' : 'form-label'"
                    ><strong>Account</strong></v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-on:input="lease_account_desc = lease_account.label"
                        data-inverted=""
                        v-if="edit_form"
                        v-model="lease_account"
                        :options="dd_account_income_grouped_list"
                        group-values="field_group_values"
                        :groupSelect="false"
                        group-label="field_group_names"
                        :group-select="true"
                        class="vue-select2 dropdown-left dropdown-400"
                        placeholder="Please select ..."
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                    <span v-if="!edit_form && lease_account.field_key !== ''">{{ lease_account.field_value }}</span>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    :class="lease_direct_fees === 0 && edit_form ? 'form-label required' : 'form-label'"
                    ><strong>Account Description</strong></v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="lease_account_desc"
                        data-inverted=""
                        :edit_form="edit_form"
                        :error_msg="error_msg"
                    ></cirrus-input>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    :class="lease_direct_fees === 0 && edit_form ? 'form-label required' : 'form-label'"
                    ><strong>Management Percentage</strong></v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="lease_man_percentage"
                        data-inverted=""
                        :edit_form="edit_form"
                        :error_msg="error_msg"
                    ></cirrus-input>
                </v-col>
            </v-row>
        </div>
        <v-divider></v-divider>
        <v-card
            elevation="0"
            v-if="edit_form"
        >
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    class="v-step-save-2-button"
                    @click="saveForm()"
                    color="success"
                    dark
                    small
                    tile
                >
                    Save Management Fees details
                </v-btn>
            </v-card-actions>
        </v-card>
        <br />
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        show_unit_history: { type: Boolean, default: true },
        page_form_type: { type: String, default: '' },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_MANAGEMENT',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,

            lease_direct_fees: 1,
            lease_account: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            lease_account_desc: '',
            lease_man_percentage: '',

            lease_direct_feesOld: 1,
            lease_accountOld: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            lease_account_descOld: '',
            lease_man_percentageOld: '',
            show_activity_log_modal: false,
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
        ]),
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        this.edit_form = this.edit_flag;
    },
    methods: {
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadUnitDetails();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];

            this.lease_direct_fees = this.lease_direct_feesOld;
            this.lease_account = this.lease_accountOld;
            this.lease_man_percentage = this.lease_man_percentageOld;
            this.lease_account_desc = this.lease_account_descOld;
        },
        addLine: function () {},
        loadUnitDetails: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                //get data from live
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/management-fees';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/management-fees';
            }

            this.$api.post(apiUrl, form_data).then((response) => {
                this.dd_account_income_grouped_list = response.data.accountData;

                let lease_account = response.data.leaseManFees.lease_account;
                this.lease_account = this.getValueInGroupList(lease_account, this.dd_account_income_grouped_list);
                this.lease_direct_fees = response.data.leaseManFees.lease_direct_fees;
                this.lease_account_desc = response.data.leaseManFees.lease_account_desc;
                this.lease_man_percentage = response.data.leaseManFees.lease_man_percentage;

                this.lease_accountOld = this.getValueInGroupList(lease_account, this.dd_account_income_grouped_list);
                this.lease_direct_feesOld = response.data.leaseManFees.lease_direct_fees;
                this.lease_account_descOld = response.data.leaseManFees.lease_account_desc;
                this.lease_man_percentageOld = response.data.leaseManFees.lease_man_percentage;

                this.loading_setting = false;
            });
        },
        saveForm: function () {
            let errorArr = [];
            let lease_account = this.lease_account;
            let lease_direct_fees = this.lease_direct_fees;
            let lease_account_desc = this.lease_account_desc;
            let lease_man_percentage = this.lease_man_percentage;
            if (lease_direct_fees === 0) {
                if (lease_account.field_key === '' && lease_direct_fees === 0)
                    errorArr.push(['You have not entered a valid Account.']);
                if (lease_account_desc === '' && lease_direct_fees === 0)
                    errorArr.push(['You have not entered a valid Account Description.']);
                if (lease_man_percentage === '') errorArr.push(['Please input a valid percentage.']);
                else {
                    if (isNaN(lease_man_percentage)) errorArr.push(['Please input a valid percentage.']);
                }
            }

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('lease_account', lease_account.field_key);
                form_data.append('lease_direct_fees', lease_direct_fees);
                form_data.append('lease_account_desc', lease_account_desc);
                form_data.append('lease_man_percentage', lease_man_percentage);

                let apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/management-fees';
                if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/lease/update/management-fees';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.edit_form = this.edit_flag;
                    let error_server_msg2 = response.data.error_server_msg2;
                    if (error_server_msg2.length === 0) {
                        this.loadForm();
                    } else {
                        this.error_server_msg2 = error_server_msg2;
                    }

                    this.loading_setting = false;
                });
            }
        },
        getValueInGroupList: function (param1, paramList) {
            if (param1 !== '' && param1 !== null) {
                let filtered = paramList.filter((p) => {
                    let field_group_values = p.field_group_values.findIndex((c) => {
                        return c.field_key === param1;
                    });
                    return field_group_values !== -1;
                });
                if (filtered.length > 0) {
                    let field_group_values = filtered[0].field_group_values.filter((m) => m.field_key === param1);
                    return field_group_values[0];
                } else {
                    return {
                        value: '',
                        label: 'Please select ...',
                        fieldKey: '',
                        fieldValue: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                }
            } else {
                return {
                    value: '',
                    label: 'Please select ...',
                    fieldKey: '',
                    fieldValue: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                };
            }
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadLeaseDirectManagementFeeSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
