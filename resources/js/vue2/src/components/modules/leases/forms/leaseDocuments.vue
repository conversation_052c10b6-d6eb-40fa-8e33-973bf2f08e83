<style>
.file-description-textbox {
    min-width: 300px !important;
}

.documents-table {
    border: 1px solid rgba(34, 36, 38, 0.15);
}

td.text-start,
td.text-end {
    padding-top: 0.4em !important;
    padding-bottom: 0.4em !important;
}

.documents-table .option {
    font-size: 21px !important;
}

ul.v-pagination button {
    height: 30px !important;
    min-width: 30px !important;
    min-height: 30px !important;
}
</style>
<template>
    <div
        v-on:dblclick="doubleClickForm()"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Documents</h6>
                &nbsp
                <v-btn-toggle
                    v-if="sys_ver_control_list.isFolderSystemOn"
                    class="form-toggle"
                    v-model="doc_active_version"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                        @click="setActiveVersion(0)"
                    >
                        Version 1
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                        @click="setActiveVersion(1)"
                    >
                        Beta
                    </v-btn>
                </v-btn-toggle>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>

                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form && lease_documents_list.length > 0"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form && lease_documents_list.length > 0"
                    icon
                    @click="saveForm()"
                >
                    <v-icon
                        light
                        color="green"
                        >check
                    </v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly && !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-col
                    class="text-center"
                    v-if="lease_documents_list.length === 0"
                    v-show="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-btn
                        v-if="!pmro_read_only"
                        depressed
                        small
                        color="success"
                        @click="modalAddData()"
                        >Add Document
                    </v-btn>
                    <div
                        style="margin: 10px 0px"
                        v-else
                    >
                        No lease documents at the moment
                    </div>
                </v-col>
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom documents-table"
                    v-show="lease_documents_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="lease_documents_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.index="{ item }">
                        {{ lease_documents_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.document_title="{ item }">
                        <cirrus-input
                            custom_class="cirrus-input-table-textbox"
                            v-model="item.document_title"
                            size=""
                            data-inverted=""
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </template>
                    <template v-slot:item.document_description="{ item }">
                        <cirrus-input
                            custom_class="file-description-textbox"
                            v-model="item.document_description"
                            size=""
                            data-inverted=""
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </template>
                    <template v-slot:item.enable_external_link="{ item }">
                        <table v-if="item.is_external != 1 && item.on_s3 != 1">
                            <tbody>
                                <tr v-if="!item.enable_external_link">
                                    <td style="margin: 0; padding: 0">
                                        <cirrus-single-upload-button2
                                            :withLinkUploader="true"
                                            :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                            v-model="item.filename"
                                            :has_saved_file="
                                                item.filename_old !== '' &&
                                                (typeof item.filename_old === 'string' ||
                                                    item.filename_old instanceof String)
                                                    ? true
                                                    : false
                                            "
                                            :edit_form="edit_form"
                                            :error_msg="error_msg"
                                            accept_type="pdf"
                                            :size_limit="20"
                                        ></cirrus-single-upload-button2>
                                    </td>
                                    <td
                                        style="margin: 0; padding: 0"
                                        v-if="edit_form && !item.filename"
                                    >
                                        <span>or</span>
                                    </td>
                                    <td
                                        style="margin: 0; padding: 0"
                                        v-if="edit_form && !item.filename"
                                    >
                                        <a
                                            href="#"
                                            @click="item.enable_external_link = 1"
                                            ><img
                                                :src="asset_domain + 'assets/images/icons/link_icon_blue.png'"
                                                class="icon"
                                                style="width: 19px"
                                        /></a>
                                    </td>
                                </tr>
                                <tr v-if="item.enable_external_link == 1">
                                    <td style="margin: 0; padding: 0">
                                        <cirrus-input
                                            custom_class="cirrus-input-table-textbox"
                                            maxlength="255"
                                            v-model="item.external_url"
                                            size=""
                                            :edit_form="edit_form"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                        <v-btn
                                            :edit_form="edit_form"
                                            x-small
                                            class=""
                                            @click="
                                                item.enable_external_link = 0;
                                                item.external_url = '';
                                            "
                                            >Cancel
                                        </v-btn>
                                        <input
                                            type="hidden"
                                            v-model="item.enable_external_link"
                                            :edit_form="edit_form"
                                            :error_msg="error_msg"
                                        />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <a
                            target="_blank"
                            v-if="item.is_external == 1"
                            :href="item.external_url"
                            ><img
                                :src="asset_domain + 'assets/images/icons/link_icon_blue.png'"
                                alt="Adobe Logo"
                                class="icon"
                                style="width: 20px"
                            />&nbsp; Download</a
                        >
                        <a
                            target="_blank"
                            v-if="item.on_s3 == 1"
                            v-on:click="downloadS3File(item.s3_filepath, item.s3_filename, 'pdf')"
                            ><img
                                :src="asset_domain + 'assets/images/icons/pdf.png'"
                                alt="Adobe Logo"
                                class="icon"
                                style="width: 19px"
                            />&nbsp; Download</a
                        >
                    </template>
                    <template v-slot:item.publish_to_owner="{ item }">
                        <sui-checkbox
                            v-model="item.publish_to_owner"
                            data-inverted=""
                            :data-tooltip="edit_form ? 'Publish to Owner' : false"
                            :disabled="!edit_form"
                        />
                    </template>
                    <template v-slot:item.publish_to_tenant="{ item }">
                        <sui-checkbox
                            v-model="item.publish_to_tenant"
                            data-inverted=""
                            :data-tooltip="edit_form ? 'Publish to Tenant' : false"
                            :disabled="!edit_form"
                        />
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            color="green"
                            class="rotate90 option"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="setFirst('updateSeqFirst', lease_documents_list.indexOf(item))"
                            >fast_rewind
                        </v-icon>
                        <v-icon
                            color="green"
                            class="rotate90 option"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="setOrder('updateSeqUp', lease_documents_list.indexOf(item))"
                            >arrow_left
                        </v-icon>
                        <v-icon
                            color="green"
                            class="rotate90 option"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="setOrder('updateSeqDown', lease_documents_list.indexOf(item))"
                            >arrow_right
                        </v-icon>
                        <v-icon
                            color="green"
                            class="rotate90 option"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="setLast('updateSeqLast', lease_documents_list.indexOf(item))"
                            >fast_forward
                        </v-icon>
                        <v-icon
                            color="red"
                            class="option"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="deleteDocuments(lease_documents_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="lease_documents_list.length > 5"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->

                <!--//v-divider></v-divider//-->
                <v-card
                    elevation="0"
                    v-if="edit_form && lease_documents_list.length > 0"
                >
                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn
                            class="v-step-save-2-button"
                            @click="saveForm()"
                            color="success"
                            dark
                            small
                            depressed
                        >
                            Save Documents details
                        </v-btn>
                    </v-card-actions>
                </v-card>

                <v-dialog
                    v-model="show_activity_log_modal"
                    max-width="1000"
                    content-class="c8-page"
                >
                    <v-card>
                        <v-card-title class="headline">
                            Activity Log
                            <a
                                href="#"
                                class="dialog-close"
                                @click.prevent="show_activity_log_modal = false"
                            >
                                <v-icon>mdi-close</v-icon>
                            </a>
                        </v-card-title>
                        <v-card-text>
                            <lease-activity-log-component
                                v-if="show_activity_log_modal"
                                :property_code="property_code"
                                :lease_code="lease_code"
                                :form_section="form_section"
                            ></lease-activity-log-component>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer />
                            <v-btn
                                color="primary"
                                depressed
                                small
                                @click="show_activity_log_modal = false"
                            >
                                <v-icon
                                    left
                                    dark
                                    size="18"
                                    >mdi-close
                                </v-icon>
                                Close
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
            </div>
        </div>

        <!--   SHOW ADD DIALOG      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitData()"
        >
            <v-card>
                <v-card-title class="headline">
                    Add Document
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="lease_document_add_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        lease_document_add_arr.index === 'New'
                                            ? lease_document_add_arr.index
                                            : lease_document_add_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Document Title
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="lease_document_add_arr.document_title"
                                        size=""
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Description
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="file-description-textbox"
                                        v-model="lease_document_add_arr.document_description"
                                        size=""
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >File
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <table>
                                        <tbody>
                                            <tr>
                                                <td style="margin: 0; padding: 0">
                                                    <cirrus-single-upload-button2
                                                        :withLinkUploader="true"
                                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                                        v-model="lease_document_add_arr.filename"
                                                        :has_saved_file="
                                                            lease_document_add_arr.filename_old !== '' &&
                                                            (typeof lease_document_add_arr.filename_old === 'string' ||
                                                                lease_document_add_arr.filename_old instanceof String)
                                                                ? true
                                                                : false
                                                        "
                                                        :edit_form="true"
                                                        :error_msg="error_msg"
                                                        accept_type="pdf"
                                                        :size_limit="20"
                                                    ></cirrus-single-upload-button2>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!lease_document_add_arr.filename"
                                                >
                                                    <span>or</span>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!lease_document_add_arr.filename"
                                                >
                                                    <a
                                                        href="#"
                                                        @click="lease_document_add_arr.enable_external_link = 1"
                                                        ><img
                                                            :src="
                                                                asset_domain + 'assets/images/icons/link_icon_blue.png'
                                                            "
                                                            class="icon"
                                                            style="width: 19px"
                                                    /></a>
                                                </td>
                                            </tr>
                                            <tr v-if="lease_document_add_arr.enable_external_link == 1">
                                                <td style="margin: 0; padding: 0">
                                                    <cirrus-input
                                                        custom_class="cirrus-input-table-textbox"
                                                        maxlength="255"
                                                        v-model="lease_document_add_arr.external_url"
                                                        size=""
                                                        :edit_form="true"
                                                        :error_msg="error_msg"
                                                    ></cirrus-input>
                                                    <v-btn
                                                        :edit_form="true"
                                                        x-small
                                                        class=""
                                                        @click="
                                                            lease_document_add_arr.enable_external_link = 0;
                                                            lease_document_add_arr.external_url = '';
                                                        "
                                                        >Cancel
                                                    </v-btn>
                                                    <input
                                                        type="hidden"
                                                        v-model="lease_document_add_arr.enable_external_link"
                                                    />
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Publish to Owner
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                    style="padding-top: 12px"
                                >
                                    <sui-checkbox
                                        v-model="lease_document_add_arr.publish_to_owner"
                                        data-inverted=""
                                        :data-tooltip="'Publish to Owner'"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Publish to Tenant
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                    style="padding-top: 12px"
                                >
                                    <sui-checkbox
                                        v-model="lease_document_add_arr.publish_to_tenant"
                                        data-inverted=""
                                        :data-tooltip="'Publish to Tenant'"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        data-position="left center"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="lease_document_add_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        data-position="left center"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--   END ADD DIALOG      -->
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import axios from 'axios';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            asset_domain: this.$assetDomain,
            form_type: 'LEASE',
            form_section: 'LEASE_DOCUMENT',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            success_flag: false,
            AED_modal: false,
            edit_form: false,
            lease_documents_list: [],
            lease_documents_list_old: [],
            lease_document_add_arr: [],
            readonly: this.read_only,
            show_activity_log_modal: false,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Document Title', value: 'document_title', sortable: false },
                { text: 'Description', value: 'document_description', sortable: false },
                { text: 'File', value: 'enable_external_link', sortable: false },
                { text: 'Publish to Owner', value: 'publish_to_owner', sortable: false, align: 'center' },
                { text: 'Publish to Tenant', value: 'publish_to_tenant', sortable: false, align: 'center' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '150px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            filename: '',
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loading_setting = false;
        this.loadForm();

        if (this.edit_flag) {
            this.edit_form = true;
        }
        this.$api
            .post('ui/fetch/param-document-view-number-setup', { form: 'LEASE', no_load: true })
            .then((response) => {
                this.items_per_page = response.data.param_value;
            });
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'doc_active_version',
            'sys_ver_control_list',
        ]),
    },
    methods: {
        ...mapMutations(['SET_DOC_ACTIVE_VERSION']),
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadLeaseDocument();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_documents_list = [];
            this.lease_documents_list = JSON.parse(JSON.stringify(this.lease_documents_list_old));

            let current_documents_list = [];

            $.each(this.lease_documents_list, function (key, value) {
                if (value.status != 'new') {
                    current_documents_list.push(value);
                }
            });

            this.lease_documents_list = current_documents_list;
        },
        modalAddData: function () {
            this.AED_modal = true;
            this.error_server_msg = [];
            this.error_server_msg2 = [];
            // this.edit_form = true;
            // var d = new Date();
            this.lease_document_add_arr = {
                index: 'New',
                document_title: '',
                document_description: '',
                filename: null,
                filename_old: null,
                document_id: '',
                publish_to_owner: false,
                publish_to_tenant: false,
                status: 'new',
                is_external: 0,
                enable_external_link: 0,
                external_url: '',
            };
        },
        loadLeaseDocument: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            let apiUrl = '';

            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/documents';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/documents';
            }

            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_documents_list = response.data.lease_docs_list;
                this.lease_documents_list_old = response.data.lease_docs_list;

                for (let x = 0; x <= this.lease_documents_list.length - 1; x++) {
                    this.lease_documents_list[x].document_seq = x;
                }

                if (this.formSectionReadOnly(this.pm_lease_form_read_only, this.form_type, this.form_section)) {
                    this.items_per_page = this.lease_documents_list.length;
                }
                this.loading_setting = false;
            });
        },
        saveForm: function () {
            let errorArr = [];

            for (let x = 0; x < this.lease_documents_list.length; x++) {
                let document_title = this.lease_documents_list[x].document_title;
                let document_description = this.lease_documents_list[x].document_description;
                let filename = this.lease_documents_list[x].filename || this.lease_documents_list[x].external_url;

                let indexNo = x + 1;
                if (document_title === '') {
                    errorArr.push([indexNo + '. You have not entered a valid title for the document.']);
                }
                if (document_description === '') {
                    errorArr.push([indexNo + '. You have not entered a valid description for the document.']);
                }
                if (filename === '') {
                    errorArr.push([indexNo + '. You have not entered a valid attachment for the document.']);
                }
            }

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                form_data.append('lease_documents_list', JSON.stringify(this.lease_documents_list));

                for (let x = 0; x < this.lease_documents_list.length; x++) {
                    if (this.lease_documents_list[x].filename !== this.lease_documents_list[x].filename_old) {
                        form_data.append('lease_docs_file_' + x, this.lease_documents_list[x].filename[0]);
                    }
                }
                let apiUrl;
                if (this.isLeaseFormLive())
                    apiUrl = this.cirrus8_api_url + 'api/with-file-upload/lease/update-or-create/document';
                else apiUrl = this.cirrus8_api_url + 'api/with-file-upload/temp/lease/update-or-create/document';

                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.lease_documents_list = [];
                        this.loadForm();
                        this.edit_form = this.edit_flag;
                        // this.edit_form = false;
                        this.loading_setting = false;
                    });
            }
        },
        modalSubmitData: function () {
            let errorArr = [];
            let document_title = this.lease_document_add_arr.document_title;
            let document_description = this.lease_document_add_arr.document_description;
            let filename = this.lease_document_add_arr.filename || this.lease_document_add_arr.external_url;

            if (document_title === '') {
                errorArr.push(['You have not entered a valid title for the document.']);
            }
            if (document_description === '') {
                errorArr.push(['You have not entered a valid description for the document.']);
            }
            if (filename === '') {
                errorArr.push(['You have not entered a valid attachment for the document.']);
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                let lease_document_add_arr_sub = [];
                lease_document_add_arr_sub[0] = this.lease_document_add_arr;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                form_data.append('lease_documents_list', JSON.stringify(lease_document_add_arr_sub));
                if (this.lease_document_add_arr.filename !== this.lease_document_add_arr.filename_old) {
                    form_data.append('lease_docs_file_0', this.lease_document_add_arr.filename[0]);
                }
                let apiUrl;
                if (this.isLeaseFormLive())
                    apiUrl = this.cirrus8_api_url + 'api/with-file-upload/lease/update-or-create/document';
                else apiUrl = this.cirrus8_api_url + 'api/with-file-upload/temp/lease/update-or-create/document';

                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.lease_documents_list = [];
                        this.loadForm();
                        if (this.edit_flag) {
                            this.edit_form = true;
                        }
                        this.AED_modal = false;
                        // this.edit_form = false;
                        this.loading_setting = false;
                    });
            }
        },
        async deleteDocuments(index) {
            let status = this.lease_documents_list[index].status;
            let document_id = this.lease_documents_list[index].document_id;
            if (status === 'new') {
                this.lease_documents_list.splice(index, 1);
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.loading_setting = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('document_id', document_id);
                    form_data.append('no_load', true);
                    let apiUrl;
                    if (this.isLeaseFormLive()) {
                        apiUrl = this.cirrus8_api_url + 'api/lease/delete/document';
                    } else {
                        apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/document';
                    }
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.lease_documents_list.splice(index, 1);
                        this.loading_setting = false;
                        this.lease_documents_list_old = JSON.parse(JSON.stringify(this.lease_documents_list));
                    });
                }
            }
        },
        setOrder: function (action, index) {
            let OldIndex = 0;
            let NewIndex = 0;

            if (action == 'updateSeqDown') {
                if (this.lease_documents_list.length == index + 1) return false;

                OldIndex = index;
                NewIndex = index + 1;
            } else if (action == 'updateSeqUp') {
                if (index == 0) return false;

                OldIndex = index;
                NewIndex = index - 1;
            }

            let createdBy = this.lease_documents_list[NewIndex].createdBy;
            let dateCreated = this.lease_documents_list[NewIndex].dateCreated;
            let document_description = this.lease_documents_list[NewIndex].document_description;
            let document_id = this.lease_documents_list[NewIndex].document_id;
            let document_title = this.lease_documents_list[NewIndex].document_title;
            let documentType = this.lease_documents_list[NewIndex].documentType;
            let external_url = this.lease_documents_list[NewIndex].external_url;
            let filename = this.lease_documents_list[NewIndex].filename;
            let is_external = this.lease_documents_list[NewIndex].is_external;
            let publish_to_owner = this.lease_documents_list[NewIndex].publish_to_owner;
            let publish_to_tenant = this.lease_documents_list[NewIndex].publish_to_tenant;
            let status = this.lease_documents_list[NewIndex].status;
            let item_no = this.lease_documents_list[NewIndex].item_no;

            this.lease_documents_list[NewIndex].createdBy = this.lease_documents_list[OldIndex].createdBy;
            this.lease_documents_list[NewIndex].dateCreated = this.lease_documents_list[OldIndex].dateCreated;
            this.lease_documents_list[NewIndex].document_description =
                this.lease_documents_list[OldIndex].document_description;
            this.lease_documents_list[NewIndex].document_id = this.lease_documents_list[OldIndex].document_id;
            this.lease_documents_list[NewIndex].document_title = this.lease_documents_list[OldIndex].document_title;
            this.lease_documents_list[NewIndex].documentType = this.lease_documents_list[OldIndex].documentType;
            this.lease_documents_list[NewIndex].external_url = this.lease_documents_list[OldIndex].external_url;
            this.lease_documents_list[NewIndex].filename = this.lease_documents_list[OldIndex].filename;
            this.lease_documents_list[NewIndex].filename_old = this.lease_documents_list[OldIndex].filename_old;
            this.lease_documents_list[NewIndex].is_external = this.lease_documents_list[OldIndex].is_external;
            this.lease_documents_list[NewIndex].publish_to_owner = this.lease_documents_list[OldIndex].publish_to_owner;
            this.lease_documents_list[NewIndex].publish_to_tenant =
                this.lease_documents_list[OldIndex].publish_to_tenant;
            this.lease_documents_list[NewIndex].status = this.lease_documents_list[OldIndex].status;
            this.lease_documents_list[NewIndex].item_no = this.lease_documents_list[OldIndex].item_no;
            this.lease_documents_list[NewIndex].document_seq = NewIndex;

            this.lease_documents_list[OldIndex].createdBy = createdBy;
            this.lease_documents_list[OldIndex].dateCreated = dateCreated;
            this.lease_documents_list[OldIndex].document_description = document_description;
            this.lease_documents_list[OldIndex].document_id = document_id;
            this.lease_documents_list[OldIndex].document_title = document_title;
            this.lease_documents_list[OldIndex].documentType = documentType;
            this.lease_documents_list[OldIndex].external_url = external_url;
            this.lease_documents_list[OldIndex].filename = filename;
            this.lease_documents_list[OldIndex].filename_old = filename;
            this.lease_documents_list[OldIndex].is_external = is_external;
            this.lease_documents_list[OldIndex].publish_to_owner = publish_to_owner;
            this.lease_documents_list[OldIndex].publish_to_tenant = publish_to_tenant;
            this.lease_documents_list[OldIndex].status = status;
            this.lease_documents_list[OldIndex].item_no = item_no;
            this.lease_documents_list[OldIndex].document_seq = OldIndex;
        },
        setLast: function (action, index) {
            if (this.lease_documents_list.length == index + 1) return false;

            let lastIndex = this.lease_documents_list.length - 1;

            let createdBy = this.lease_documents_list[index].createdBy;
            let dateCreated = this.lease_documents_list[index].dateCreated;
            let document_description = this.lease_documents_list[index].document_description;
            let document_id = this.lease_documents_list[index].document_id;
            let document_title = this.lease_documents_list[index].document_title;
            let documentType = this.lease_documents_list[index].documentType;
            let external_url = this.lease_documents_list[index].external_url;
            let filename = this.lease_documents_list[index].filename;
            let is_external = this.lease_documents_list[index].is_external;
            let publish_to_owner = this.lease_documents_list[index].publish_to_owner;
            let publish_to_tenant = this.lease_documents_list[index].publish_to_tenant;
            let status = this.lease_documents_list[index].status;
            let item_no = this.lease_documents_list[index].item_no;

            for (let x = 0; x < this.lease_documents_list.length; x++) {
                if (x >= index && x != lastIndex) {
                    this.lease_documents_list[x].createdBy = this.lease_documents_list[x + 1].createdBy;
                    this.lease_documents_list[x].dateCreated = this.lease_documents_list[x + 1].dateCreated;
                    this.lease_documents_list[x].document_description =
                        this.lease_documents_list[x + 1].document_description;
                    this.lease_documents_list[x].document_id = this.lease_documents_list[x + 1].document_id;
                    this.lease_documents_list[x].document_title = this.lease_documents_list[x + 1].document_title;
                    this.lease_documents_list[x].documentType = this.lease_documents_list[x + 1].documentType;
                    this.lease_documents_list[x].external_url = this.lease_documents_list[x + 1].external_url;
                    this.lease_documents_list[x].filename = this.lease_documents_list[x + 1].filename;
                    this.lease_documents_list[x].filename_old = this.lease_documents_list[x + 1].filename_old;
                    this.lease_documents_list[x].is_external = this.lease_documents_list[x + 1].is_external;
                    this.lease_documents_list[x].publish_to_owner = this.lease_documents_list[x + 1].publish_to_owner;
                    this.lease_documents_list[x].publish_to_tenant = this.lease_documents_list[x + 1].publish_to_tenant;
                    this.lease_documents_list[x].status = this.lease_documents_list[x + 1].status;
                    this.lease_documents_list[x].item_no = this.lease_documents_list[x + 1].item_no;
                    this.lease_documents_list[x].document_seq = x;
                } else if (x == lastIndex) {
                    this.lease_documents_list[lastIndex].createdBy = createdBy;
                    this.lease_documents_list[lastIndex].dateCreated = dateCreated;
                    this.lease_documents_list[lastIndex].document_description = document_description;
                    this.lease_documents_list[lastIndex].document_id = document_id;
                    this.lease_documents_list[lastIndex].document_title = document_title;
                    this.lease_documents_list[lastIndex].documentType = documentType;
                    this.lease_documents_list[lastIndex].external_url = external_url;
                    this.lease_documents_list[lastIndex].filename = filename;
                    this.lease_documents_list[lastIndex].filename_old = filename;
                    this.lease_documents_list[lastIndex].is_external = is_external;
                    this.lease_documents_list[lastIndex].publish_to_owner = publish_to_owner;
                    this.lease_documents_list[lastIndex].publish_to_tenant = publish_to_tenant;
                    this.lease_documents_list[lastIndex].status = status;
                    this.lease_documents_list[lastIndex].item_no = item_no;
                    this.lease_documents_list[lastIndex].document_seq = lastIndex;
                }
            }
        },
        setFirst: function (action, index) {
            if (0 == index) return false;

            let createdBy = this.lease_documents_list[index].createdBy;
            let dateCreated = this.lease_documents_list[index].dateCreated;
            let document_description = this.lease_documents_list[index].document_description;
            let document_id = this.lease_documents_list[index].document_id;
            let document_title = this.lease_documents_list[index].document_title;
            let documentType = this.lease_documents_list[index].documentType;
            let external_url = this.lease_documents_list[index].external_url;
            let filename = this.lease_documents_list[index].filename;
            let is_external = this.lease_documents_list[index].is_external;
            let publish_to_owner = this.lease_documents_list[index].publish_to_owner;
            let publish_to_tenant = this.lease_documents_list[index].publish_to_tenant;
            let status = this.lease_documents_list[index].status;
            let item_no = this.lease_documents_list[index].item_no;

            for (let x = this.lease_documents_list.length - 1; x >= 0; x--) {
                if (x <= index && x != 0) {
                    this.lease_documents_list[x].createdBy = this.lease_documents_list[x - 1].createdBy;
                    this.lease_documents_list[x].dateCreated = this.lease_documents_list[x - 1].dateCreated;
                    this.lease_documents_list[x].document_description =
                        this.lease_documents_list[x - 1].document_description;
                    this.lease_documents_list[x].document_id = this.lease_documents_list[x - 1].document_id;
                    this.lease_documents_list[x].document_title = this.lease_documents_list[x - 1].document_title;
                    this.lease_documents_list[x].documentType = this.lease_documents_list[x - 1].documentType;
                    this.lease_documents_list[x].external_url = this.lease_documents_list[x - 1].external_url;
                    this.lease_documents_list[x].filename = this.lease_documents_list[x - 1].filename;
                    this.lease_documents_list[x].filename_old = this.lease_documents_list[x - 1].filename_old;
                    this.lease_documents_list[x].is_external = this.lease_documents_list[x - 1].is_external;
                    this.lease_documents_list[x].publish_to_owner = this.lease_documents_list[x - 1].publish_to_owner;
                    this.lease_documents_list[x].publish_to_tenant = this.lease_documents_list[x - 1].publish_to_tenant;
                    this.lease_documents_list[x].status = this.lease_documents_list[x - 1].status;
                    this.lease_documents_list[x].item_no = this.lease_documents_list[x - 1].item_no;
                    this.lease_documents_list[x].document_seq = x;
                } else if (x == 0) {
                    this.lease_documents_list[0].createdBy = createdBy;
                    this.lease_documents_list[0].dateCreated = dateCreated;
                    this.lease_documents_list[0].document_description = document_description;
                    this.lease_documents_list[0].document_id = document_id;
                    this.lease_documents_list[0].document_title = document_title;
                    this.lease_documents_list[0].documentType = documentType;
                    this.lease_documents_list[0].external_url = external_url;
                    this.lease_documents_list[0].filename = filename;
                    this.lease_documents_list[0].filename_old = filename;
                    this.lease_documents_list[0].is_external = is_external;
                    this.lease_documents_list[0].publish_to_owner = publish_to_owner;
                    this.lease_documents_list[0].publish_to_tenant = publish_to_tenant;
                    this.lease_documents_list[0].status = status;
                    this.lease_documents_list[0].item_no = item_no;
                    this.lease_documents_list[0].document_seq = 0;
                }
            }
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadDocs_' + id;
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        downloadS3File: function (this_file, this_file_name, file_type) {
            let params = new FormData();

            params.append('un', this.username);
            params.append('currentDB', this.current_db);
            params.append('user_type', this.user_type);

            params.append('s3path', this_file);

            axios.post(this.cirrus8_api_url + 'api/lease/fetch/download-s3', params).then((response) => {
                if (response.data.data.file) {
                    this.downloadFile(response.data.data.file, this_file_name, file_type);
                }
            });
        },
        downloadFile(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;
            let blob = new Blob([this.s2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            // a.download = name+'.'+format;
            a.download = name;
            a.click();
        },
        s2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
        setActiveVersion: function (status) {
            this.SET_DOC_ACTIVE_VERSION(status);
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadLeaseDocumentSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
