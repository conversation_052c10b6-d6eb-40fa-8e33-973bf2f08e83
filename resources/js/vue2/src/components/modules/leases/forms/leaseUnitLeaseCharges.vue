<style>
.tableRaw {
    width: auto;
}

.tableRaw td {
    margin: 0px !important;
    padding: 0px !important;
    border-bottom: 0px !important;
}

.v-data-table__wrapper {
    overflow-x: hidden;
    overflow-y: hidden;
}
</style>
<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Lease Charges</h6>
                <v-spacer></v-spacer>

                <cirrus-input
                    inputFormat="search"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>

                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        lease_status === 'C' &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form && lease_unit_charges_list.length > 0"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_content_setting"></cirrus-content-loader>
        <cirrus-loader v-if="loading_setting"></cirrus-loader>
        <!--Lease charge list view-->
        <!--datatable start-->
        <v-col
            class="text-center"
            v-show="
                !read_only &&
                lease_status === 'C' &&
                !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
            "
            v-if="lease_unit_charges_list.length === 0"
        >
            <v-btn
                v-if="!pmro_read_only"
                depressed
                small
                color="success"
                @click="modalAddData()"
                >Add Charge</v-btn
            >
            <div
                style="margin-top: 10px"
                v-else
            >
                No lease charges at the moment
            </div>
        </v-col>
        <v-data-table
            class="c8-datatable-custom"
            :item-class="itemRowBackground"
            v-show="lease_unit_charges_list.length > 0"
            v-if="!loading_content_setting"
            dense
            item-key="id"
            :headers="lease_headers"
            :items="lease_unit_charges_list"
            :items-per-page="items_per_page"
            hide-default-footer
            :page.sync="page"
            :total-visible="7"
            @page-count="page_count = $event"
            :search="search_datatable"
            :single-expand="true"
            :expanded.sync="expanded"
            show-expand
            :calculate-widths="true"
        >
            <template v-slot:item.index="{ item }">
                {{ lease_unit_charges_list.indexOf(item) + 1 }}
            </template>
            <template v-slot:item.item_no="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.item_no }}</span>
                </div>
            </template>
            <template v-slot:item.charge_description="{ item }">
                <span class="form-input-text"
                    >{{ item.unit_charge_account }} - <strong>{{ item.unit_charge_description }}</strong>
                </span>
                <span v-if="item.lease_charge_stop_or_no_item === '0' || item.lease_charge_stop_or_no_item === '1'"
                    ><v-chip
                        v-if="generateUnitChargeStatusLabel(item.view_charge_details) !== ''"
                        x-small
                        class="ma-2"
                        :color="generateUnitChargeColor(item.lease_charge_stop_or_no_item, item.stop_date_grater_today)"
                        text-color="white"
                        >{{ generateUnitChargeStatusLabel(item.view_charge_details) }}</v-chip
                    ></span
                >
                <span v-if="item.lease_charge_stop_or_no_item === '2' || item.lease_charge_stop_or_no_item === '3'"
                    ><v-chip
                        x-small
                        class="ma-2"
                        color="red"
                        text-color="white"
                        >NOT CURRENT</v-chip
                    ></span
                >
            </template>
            <template v-slot:item.unit_charge_start_date_raw="{ item }">
                <span class="form-input-text">{{ item.unit_charge_start_date }}</span>
            </template>
            <template v-slot:item.amount="{ item }">
                <span class="form-input-text">{{ item.amount_value }} p.a.</span>
            </template>
            <template v-slot:item.date_flag="{ item }">
                <span
                    class="form-input-text"
                    v-if="item.date_flag"
                    >The charge dates need fixing.</span
                >
            </template>
            <template v-slot:item.action1="{ item }">
                <v-icon
                    color="red"
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    v-if="edit_form && lease_status === 'C'"
                    @click="deleteUnitCharge(lease_unit_charges_list.indexOf(item))"
                    >close
                </v-icon>
            </template>

            <template v-slot:expanded-item="{ headers, item }">
                <td :colspan="headers.length">
                    <div style="float: left">
                        <div class="tree-vertical-line"></div>
                        <div class="tree-horizontal-line"></div>
                    </div>
                    <div style="padding-left: 35px">
                        <v-card
                            class="section-toolbar"
                            color="titleHeader"
                            text
                            tile
                        >
                            <v-card-actions>
                                <h6 class="title font-weight-black">{{ item.unit_charge_description }}</h6>
                                <v-spacer></v-spacer>
                            </v-card-actions>
                        </v-card>
                        <div class="page-form">
                            <cirrus-server-error
                                :error_msg="error_server_charge_msg"
                                :errorMsg2="error_server_charge_msg2"
                            ></cirrus-server-error>
                            <v-alert
                                type="success"
                                dense
                                tile
                                text
                                v-if="success_flag"
                            >
                                Successfully Saved
                            </v-alert>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Account</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-if="edit_form"
                                        v-model="item.unit_charge_account_dropdown"
                                        :options="account_list_income_data_main_list"
                                        group-values="field_group_values"
                                        :groupSelect="false"
                                        group-label="field_group_names"
                                        :group-select="true"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        :custom-label="nameWithDash"
                                        @change="changeUnitChargesAddAccount()"
                                        placeholder="Please select ..."
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <v-btn
                                        depressed
                                        elevation="0"
                                        small
                                        color="normal"
                                        height="30"
                                        class="rounded-l-0"
                                        v-if="edit_form"
                                        v-on:click="changeUnitChargesAddAccount()"
                                    >
                                        <v-icon>arrow_right</v-icon>
                                    </v-btn>
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{ item.unit_charge_account_dropdown.field_key }} -
                                        {{ item.unit_charge_account_dropdown.field_value }}</span
                                    >
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="isMultiplePropertyLedger"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Property Ledger</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-select-v2
                                        v-model="item.propertyLedgerId"
                                        :options="property_ledger_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Please select"
                                        :custom-label="nameWithCodeDash"
                                        v-if="edit_form"
                                    />
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                    >
                                        {{ displayCodeAndLabel(item.propertyLedgerId, property_ledger_list) }}
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Charge Description</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        maxlength="28"
                                        :key="
                                            'item.view_charge_details.lease_charge_description' +
                                            lease_unit_charges_list.indexOf(item)
                                        "
                                        v-model="item.view_charge_details.lease_charge_description"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="show_tax_rate"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Tax Rate</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-select-v2
                                        :hasEmpty="false"
                                        v-if="edit_form"
                                        v-model="item.view_charge_details.tax_rate_code"
                                        :options="dd_param_tax_rate_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Select tax rate"
                                    />
                                    <span
                                        v-if="!edit_form"
                                        class="form-input-text"
                                        >{{
                                            getDropdownName(
                                                item.view_charge_details.tax_rate_code,
                                                dd_param_tax_rate_list,
                                            )
                                        }}</span
                                    >
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Charge Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-model="item.view_charge_details.lease_charge_type"
                                        v-if="edit_form"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            depressed
                                            v-for="(
                                                lease_unit_charge_types_listData, lease_unit_charge_types_listIndex
                                            ) in lease_unit_charge_types_list"
                                            :key="lease_unit_charge_types_listIndex"
                                            text
                                        >
                                            {{ lease_unit_charge_types_listData.label }}
                                        </v-btn>
                                    </v-btn-toggle>
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{
                                            lease_unit_charge_types_list[item.view_charge_details.lease_charge_type]
                                                .label
                                        }}</span
                                    >
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Charge Frequency</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-if="edit_form"
                                        v-model="item.view_charge_details.lease_charge_frequency"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            depressed
                                            v-for="(
                                                lease_unit_charge_frequency_listData,
                                                lease_unit_charge_frequency_listIndex
                                            ) in lease_unit_charge_frequency_list"
                                            :key="lease_unit_charge_frequency_listIndex"
                                            text
                                        >
                                            {{ lease_unit_charge_frequency_listData.label }}
                                        </v-btn>
                                    </v-btn-toggle>
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{
                                            lease_unit_charge_frequency_list[
                                                item.view_charge_details.lease_charge_frequency
                                            ].label
                                        }}</span
                                    >
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Partial Rent Method</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-if="edit_form"
                                        v-model="item.view_charge_details.lease_charge_partial_rent_method"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            depressed
                                            v-for="(
                                                lease_unit_charge_partial_rent_listData,
                                                lease_unit_charge_partial_rent_listIndex
                                            ) in lease_unit_charge_partial_rent_list"
                                            :key="lease_unit_charge_partial_rent_listIndex"
                                            text
                                        >
                                            {{ lease_unit_charge_partial_rent_listData.label }}
                                        </v-btn>
                                    </v-btn-toggle>
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{
                                            lease_unit_charge_partial_rent_list[
                                                item.view_charge_details.lease_charge_partial_rent_method
                                            ].label
                                        }}</span
                                    >
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Charging Status</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{ generateUnitChargeStatusLabel(item.view_charge_details) }}</span
                                    >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        v-if="
                                            edit_form &&
                                            page_form_type !== 'client-temp-page' &&
                                            page_form_type !== 'admin-approval-page'
                                        "
                                        :id="
                                            'item.view_charge_details.lease_charge_charging_status_date' +
                                            lease_unit_charges_list.indexOf(item)
                                        "
                                        v-model="item.view_charge_details.lease_charge_charging_status_date"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                    &nbsp;&nbsp;
                                    <v-btn
                                        depressed
                                        x-small
                                        v-if="
                                            !formSectionReadOnly(
                                                pm_lease_form_read_only,
                                                form_type,
                                                form_section,
                                                is_inactive,
                                            ) &&
                                            edit_form &&
                                            lease_status === 'C' &&
                                            isLeaseFormLive() &&
                                            item.view_charge_details.lease_charge_stop === '1' &&
                                            page_form_type !== 'client-temp-page' &&
                                            page_form_type !== 'admin-approval-page'
                                        "
                                        @click="startCharging(lease_unit_charges_list.indexOf(item))"
                                        >Start charging
                                    </v-btn>
                                    <v-btn
                                        depressed
                                        x-small
                                        v-if="
                                            !formSectionReadOnly(
                                                pm_lease_form_read_only,
                                                form_type,
                                                form_section,
                                                is_inactive,
                                            ) &&
                                            edit_form &&
                                            lease_status === 'C' &&
                                            isLeaseFormLive() &&
                                            item.view_charge_details.lease_charge_stop === '0' &&
                                            page_form_type !== 'client-temp-page' &&
                                            page_form_type !== 'admin-approval-page'
                                        "
                                        @click="showStopModal(lease_unit_charges_list.indexOf(item))"
                                        >Stop charging
                                    </v-btn>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Start Charging Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="
                                            'item.view_charge_details.lease_charge_start_date' +
                                            lease_unit_charges_list.indexOf(item)
                                        "
                                        v-model="item.view_charge_details.lease_charge_start_date"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                    <span class="form-input-text">
                                        <div class="tooltip">
                                            <v-icon small>help</v-icon>
                                            <span
                                                class="tooltiptext"
                                                style="width: 300px !important; text-align: left"
                                            >
                                                Records when tenants lease charges commenced, use either; lease
                                                commencement date (this enables recording of lease charge history on new
                                                managements) or new property management takeover date
                                            </span>
                                        </div>
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    :class="edit_form ? 'form-label required' : 'form-label'"
                                    >Next Charging Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="
                                            'item.view_charge_details.lease_charge_next_date' +
                                            lease_unit_charges_list.indexOf(item)
                                        "
                                        v-model="item.view_charge_details.lease_charge_next_date"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                    <span
                                        class="form-input-text"
                                        v-if="item.view_charge_details.lease_charge_last_date !== ''"
                                    >
                                        <strong>Last date :</strong>
                                        {{ item.view_charge_details.lease_charge_last_date }}
                                    </span>
                                    <span
                                        class="form-input-text"
                                        v-if="item.view_charge_details.lease_charge_last_date === ''"
                                    >
                                        Never billed.
                                    </span>
                                    <span class="form-input-text">
                                        <div class="tooltip">
                                            <v-icon small>help</v-icon>
                                            <span
                                                class="tooltiptext"
                                                style="width: 300px !important; text-align: left"
                                            >
                                                Next date to raise tenant charge on cirrus8
                                            </span>
                                        </div>
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    class="text-right"
                                >
                                    <v-btn
                                        depressed
                                        small
                                        color="success"
                                        v-if="edit_form"
                                        v-show="
                                            !formSectionReadOnly(
                                                pm_lease_form_read_only,
                                                form_type,
                                                form_section,
                                                is_inactive,
                                            )
                                        "
                                        @click="saveForm(false, lease_unit_charges_list.indexOf(item))"
                                    >
                                        Save Charge
                                    </v-btn>
                                </v-col>
                            </v-row>
                        </div>
                        <v-card
                            class="section-toolbar"
                            color="titleHeader"
                            text
                            tile
                        >
                            <v-card-actions>
                                <h6 class="title font-weight-black">
                                    Charge Amounts for {{ item.unit_charge_description }}
                                </h6>
                                <v-spacer></v-spacer>
                                <v-btn
                                    x-small
                                    v-show="
                                        !read_only &&
                                        !formSectionReadOnly(
                                            pm_lease_form_read_only,
                                            form_type,
                                            form_section,
                                            is_inactive,
                                        ) &&
                                        !pmro_read_only
                                    "
                                    v-if="!edit_form && lease_status === 'C'"
                                    icon
                                    @click="edit_form = true"
                                >
                                    <v-icon>edit</v-icon>
                                </v-btn>
                                <v-btn
                                    x-small
                                    depressed
                                    color="normal"
                                    v-show="
                                        !read_only &&
                                        !formSectionReadOnly(
                                            pm_lease_form_read_only,
                                            form_type,
                                            form_section,
                                            is_inactive,
                                        ) &&
                                        !pmro_read_only
                                    "
                                    v-if="edit_form && lease_status === 'C'"
                                    @click="addLeaseChargeLine(lease_unit_charges_list.indexOf(item))"
                                >
                                    Add Charge Period
                                </v-btn>
                            </v-card-actions>
                        </v-card>

                        <cirrus-server-error :errorMsg2="item.charge_error"></cirrus-server-error>
                        <cirrus-server-error
                            :error_msg="error_server_charge_item_msg"
                            :errorMsg2="error_server_charge_item_msg2"
                        ></cirrus-server-error>
                        <sui-table
                            class="vue-data-grid"
                            stackable
                            width="100%"
                            cellpadding="0"
                            cellspacing="0"
                            border="0"
                            compact
                        >
                            <sui-table-header>
                                <sui-table-row class="fieldDescription">
                                    <sui-table-header-cell style="width: 5px !important">#</sui-table-header-cell>
                                    <sui-table-header-cell style="width: 5px !important">ID</sui-table-header-cell>
                                    <sui-table-header-cell style="width: 5px !important">&nbsp</sui-table-header-cell>
                                    <sui-table-header-cell text-align="center">From Date</sui-table-header-cell>
                                    <sui-table-header-cell text-align="center">To Date</sui-table-header-cell>
                                    <sui-table-header-cell text-align="center"
                                        >Amount Per Year (excl. {{ tax_label }})</sui-table-header-cell
                                    >
                                    <sui-table-header-cell text-align="center"
                                        >{{ currency_symbol }}/{{ area_unit }}</sui-table-header-cell
                                    >
                                    <sui-table-header-cell text-align="center"
                                        >Amount Per Month (excl. {{ tax_label }})</sui-table-header-cell
                                    >
                                    <sui-table-header-cell text-align="center">Parking bays</sui-table-header-cell>
                                    <sui-table-header-cell text-align="center">Rent Review</sui-table-header-cell>
                                    <sui-table-header-cell text-align="center"></sui-table-header-cell>
                                    <sui-table-header-cell text-align="center"></sui-table-header-cell>
                                </sui-table-row>
                            </sui-table-header>
                            <sui-table-body
                                class="page-form"
                                v-for="(lease_charge_period_list_data, lease_charge_period_list_index) in item
                                    .view_charge_details.lease_charge_period_list"
                                :key="lease_charge_period_list_index"
                            >
                                <sui-table-row class="form-row">
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                        style="width: 5px !important"
                                    >
                                        {{ lease_charge_period_list_index + 1 }}
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                        style="width: 5px !important"
                                    >
                                        {{ lease_charge_period_list_data.item_no }}
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        style="width: 5px !important"
                                    >
                                        <v-btn
                                            icon
                                            x-small
                                            v-if="lease_charge_period_list_data.continuity_check_failed"
                                        >
                                            <v-icon
                                                dense
                                                color="warning"
                                                >warning</v-icon
                                            >
                                        </v-btn>
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                    >
                                        <cirrus-icon-date-picker
                                            :size="'40'"
                                            :id="
                                                'lease_charge_start_date_' +
                                                lease_unit_charges_list.indexOf(item) +
                                                '_' +
                                                lease_charge_period_list_index
                                            "
                                            v-model="lease_charge_period_list_data.unit_charge_item_start_date"
                                            :edit_form="edit_form && lease_status === 'C'"
                                            :error_msg="error_msg"
                                        ></cirrus-icon-date-picker>
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                    >
                                        <cirrus-icon-date-picker
                                            :size="'40'"
                                            :id="
                                                'lease_charge_end_date_' +
                                                lease_unit_charges_list.indexOf(item) +
                                                '_' +
                                                lease_charge_period_list_index
                                            "
                                            v-model="lease_charge_period_list_data.unit_charge_item_end_date"
                                            :edit_form="edit_form && lease_status === 'C'"
                                            :error_msg="error_msg"
                                        ></cirrus-icon-date-picker>
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                    >
                                        <cirrus-input
                                            inputFormat="dollar"
                                            :size="'10'"
                                            :key="
                                                'lease_charge_item_amount_' +
                                                lease_unit_charges_list.indexOf(item) +
                                                lease_charge_period_list_index
                                            "
                                            v-model="lease_charge_period_list_data.unit_charge_item_amount"
                                            :edit_form="edit_form && lease_status === 'C'"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                    >
                                        <span
                                            v-if="
                                                lease_charge_period_list_data.unit_area !== null &&
                                                lease_charge_period_list_data.unit_area !== '' &&
                                                lease_charge_period_list_data.unit_area !== '0'
                                            "
                                            >{{ currency_symbol
                                            }}{{
                                                accountingAmountFormat(
                                                    numberWithCommas(
                                                        roundTo(
                                                            lease_charge_period_list_data.unit_charge_item_amount /
                                                                divisorClean(lease_charge_period_list_data.unit_area),
                                                            2,
                                                        ),
                                                    ),
                                                )
                                            }}</span
                                        >
                                        <span
                                            v-else-if="
                                                lease_charge_period_list_data.lease_unit_area !== null &&
                                                lease_charge_period_list_data.lease_unit_area !== '' &&
                                                lease_charge_period_list_data.lease_unit_area !== '0'
                                            "
                                            >{{ currency_symbol
                                            }}{{
                                                accountingAmountFormat(
                                                    numberWithCommas(
                                                        roundTo(
                                                            lease_charge_period_list_data.unit_charge_item_amount /
                                                                divisorClean(
                                                                    lease_charge_period_list_data.lease_unit_area,
                                                                ),
                                                            2,
                                                        ),
                                                    ),
                                                )
                                            }}</span
                                        >
                                        <span v-else>{{ currency_symbol }}0.00</span>
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                    >
                                        <span
                                            >{{ currency_symbol
                                            }}{{
                                                accountingAmountFormat(
                                                    numberWithCommas(
                                                        roundTo(
                                                            lease_charge_period_list_data.unit_charge_item_amount / 12,
                                                            2,
                                                        ),
                                                    ),
                                                )
                                            }}</span
                                        >
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                    >
                                        <cirrus-input
                                            :size="'2'"
                                            inputFormat="wholeNumberOnly"
                                            maxlength="6"
                                            :key="
                                                'unit_charge_item_parking_' +
                                                lease_unit_charges_list.indexOf(item) +
                                                '_' +
                                                lease_charge_period_list_index
                                            "
                                            v-model="lease_charge_period_list_data.unit_charge_item_parking"
                                            :edit_form="edit_form && lease_status === 'C'"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        text-align="center"
                                    >
                                        {{ lease_charge_period_list_data.review_type }}
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        style="text-align: right"
                                    >
                                        <v-btn
                                            depressed
                                            x-small
                                            color="success"
                                            v-if="edit_form && lease_status === 'C'"
                                            v-show="
                                                !formSectionReadOnly(
                                                    pm_lease_form_read_only,
                                                    form_type,
                                                    form_section,
                                                    is_inactive,
                                                )
                                            "
                                            @click="
                                                saveLeaseChargeAmount(
                                                    lease_unit_charges_list.indexOf(item),
                                                    lease_charge_period_list_index,
                                                )
                                            "
                                        >
                                            Update
                                        </v-btn>
                                    </sui-table-cell>
                                    <sui-table-cell
                                        class="form-input"
                                        style="text-align: right"
                                    >
                                        <v-icon
                                            color="red"
                                            v-show="
                                                !read_only &&
                                                !formSectionReadOnly(
                                                    pm_lease_form_read_only,
                                                    form_type,
                                                    form_section,
                                                    is_inactive,
                                                )
                                            "
                                            v-if="
                                                edit_form &&
                                                lease_status === 'C' &&
                                                lease_charge_period_list_data.unit_charge_item_id_last_id ===
                                                    lease_charge_period_list_data.unit_charge_item_id
                                            "
                                            @click="
                                                deleteLeaseChargeItem(
                                                    lease_unit_charges_list.indexOf(item),
                                                    lease_charge_period_list_index,
                                                )
                                            "
                                        >
                                            close
                                        </v-icon>
                                    </sui-table-cell>
                                </sui-table-row>
                            </sui-table-body>
                        </sui-table>
                    </div>
                </td>
            </template>
        </v-data-table>
        <v-row
            class="form-row"
            v-show="lease_unit_charges_list.length > 5"
            v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-col
                xs="12"
                sm="12"
                md="12"
            >
                <table class="c8-datatable-custom-footer">
                    <tr>
                        <td class="">Rows per page:</td>
                        <td>
                            <multiselect
                                v-model="items_per_page"
                                :options="[5, 10, 15]"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-200"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </td>
                        <td></td>
                        <td>
                            <v-pagination
                                v-model="page"
                                :length="page_count"
                            ></v-pagination>
                        </td>
                    </tr>
                </table>
            </v-col>
        </v-row>
        <!--datatable end-->
        <!--   add unit      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    New Lease Charge
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_charge_msg"
                        :errorMsg2="error_server_charge_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease charge add-->
                    <div
                        v-if="AED_modal"
                        :key="lease_charge_arr.index"
                    >
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        lease_charge_arr.index === 'New'
                                            ? lease_charge_arr.index
                                            : lease_charge_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Account</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-model="lease_charge_arr.unit_charge_account_dropdown"
                                        :options="account_list_income_data_main_list"
                                        group-values="field_group_values"
                                        :groupSelect="false"
                                        group-label="field_group_names"
                                        :group-select="true"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        :custom-label="nameWithDash"
                                        @change="changeUnitChargesAddAccount()"
                                        placeholder="Please select ..."
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <v-btn
                                        depressed
                                        elevation="0"
                                        small
                                        color="normal"
                                        height="30"
                                        class="rounded-l-0"
                                        v-on:click="changeUnitChargesAddAccount()"
                                    >
                                        <v-icon>arrow_right</v-icon>
                                    </v-btn>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="isMultiplePropertyLedger"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Property Ledger</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-select-v2
                                        v-model="lease_charge_arr.propertyLedgerId"
                                        :options="property_ledger_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Please select"
                                        :custom-label="nameWithCodeDash"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Charge Description</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        maxlength="28"
                                        :key="'unit_charge_description_1_' + lease_charge_arr.index"
                                        v-model="lease_charge_arr.view_charge_details.lease_charge_description"
                                        :error_msg="error_msg"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="show_tax_rate"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Tax Rate</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-select-v2
                                        :hasEmpty="false"
                                        v-model="lease_charge_arr.view_charge_details.tax_rate_code"
                                        :options="dd_param_tax_rate_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Select tax rate"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Charge Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-model="lease_charge_arr.view_charge_details.lease_charge_type"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            depressed
                                            v-for="(
                                                lease_unit_charge_types_listData, lease_unit_charge_types_listIndex
                                            ) in lease_unit_charge_types_list"
                                            :key="lease_unit_charge_types_listIndex"
                                            text
                                        >
                                            {{ lease_unit_charge_types_listData.label }}
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Charge Frequency</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-model="lease_charge_arr.view_charge_details.lease_charge_frequency"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            depressed
                                            v-for="(
                                                lease_unit_charge_frequency_listData,
                                                lease_unit_charge_frequency_listIndex
                                            ) in lease_unit_charge_frequency_list"
                                            :key="lease_unit_charge_frequency_listIndex"
                                            text
                                        >
                                            {{ lease_unit_charge_frequency_listData.label }}
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Partial Rent Method</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-model="lease_charge_arr.view_charge_details.lease_charge_partial_rent_method"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            depressed
                                            v-for="(
                                                lease_unit_charge_partial_rent_listData,
                                                lease_unit_charge_partial_rent_listIndex
                                            ) in lease_unit_charge_partial_rent_list"
                                            :key="lease_unit_charge_partial_rent_listIndex"
                                            text
                                        >
                                            {{ lease_unit_charge_partial_rent_listData.label }}
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="lease_charge_arr.status !== 'new'"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Charging Status</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn
                                        depressed
                                        x-small
                                        tile
                                        v-if="
                                            !formSectionReadOnly(
                                                pm_lease_form_read_only,
                                                form_type,
                                                form_section,
                                                is_inactive,
                                            ) &&
                                            isLeaseFormLive() &&
                                            lease_charge_arr.view_charge_details.lease_charge_stop === '1'
                                        "
                                        @click="startCharging(lease_charge_arr.index)"
                                        >Start charging
                                    </v-btn>
                                    <v-btn
                                        depressed
                                        x-small
                                        tile
                                        v-if="
                                            !formSectionReadOnly(
                                                pm_lease_form_read_only,
                                                form_type,
                                                form_section,
                                                is_inactive,
                                            ) &&
                                            isLeaseFormLive() &&
                                            lease_charge_arr.view_charge_details.lease_charge_stop === '0'
                                        "
                                        @click="showStopModal(lease_charge_arr.index)"
                                        >Stop charging
                                    </v-btn>
                                    <cirrus-icon-date-picker
                                        v-if="lease_charge_arr.view_charge_details.lease_charge_stop === '0'"
                                        :size="'40'"
                                        :id="
                                            'item.view_charge_details.lease_charge_charging_status_date' +
                                            lease_charge_arr.index
                                        "
                                        v-model="lease_charge_arr.view_charge_details.lease_charge_charging_status_date"
                                        :edit_form="false"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                    <span v-if="lease_charge_arr.view_charge_details.lease_charge_stop === '1'">{{
                                        lease_charge_arr.view_charge_details.lease_charge_charging_status_date
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Start Charging Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'lease_charge_start_date' + lease_charge_arr.index"
                                        v-model="lease_charge_arr.view_charge_details.lease_charge_start_date"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                    <span class="form-input-text">
                                        <div class="tooltip">
                                            <v-icon small>help</v-icon>
                                            <span
                                                class="tooltiptext"
                                                style="width: 300px !important; text-align: left"
                                            >
                                                Records when tenants lease charges commenced, use either; lease
                                                commencement date (this enables recording of lease charge history on new
                                                managements) or new property management takeover date
                                            </span>
                                        </div>
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Next Charging Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'lease_charge_next_date' + lease_charge_arr.index"
                                        v-model="lease_charge_arr.view_charge_details.lease_charge_next_date"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                    <span
                                        class="form-input-text"
                                        v-if="
                                            lease_charge_arr.view_charge_details.lease_charge_last_date !== '' &&
                                            lease_charge_arr.index !== 'New'
                                        "
                                    >
                                        <strong>Last date :</strong>
                                        {{ lease_charge_arr.view_charge_details.lease_charge_last_date }}
                                    </span>
                                    <span
                                        class="form-input-text"
                                        v-if="
                                            lease_charge_arr.view_charge_details.lease_charge_last_date === '' &&
                                            lease_charge_arr.index !== 'New'
                                        "
                                    >
                                        Never billed.
                                    </span>
                                    <span class="form-input-text">
                                        <div class="tooltip">
                                            <v-icon small>help</v-icon>
                                            <span
                                                class="tooltiptext"
                                                style="width: 300px !important; text-align: left"
                                            >
                                                Next date to raise tenant charge on cirrus8
                                            </span>
                                        </div>
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="lease_charge_arr.status === 'new'"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Charging Amount (p.a)</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        :size="'10'"
                                        :key="'lease_charge_amount' + lease_charge_arr.index"
                                        v-model="lease_charge_arr.lease_charge_amount"
                                        :error_msg="error_msg"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-show="lease_charge_arr.status === 'new'"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Parking Bays</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'10'"
                                        :key="'lease_charge_parking_bays' + lease_charge_arr.index"
                                        v-model="lease_charge_arr.lease_charge_parking_bays"
                                        :error_msg="error_msg"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="saveAddForm()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                        v-if="lease_charge_arr.status === 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>

                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="lease_charge_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_add_lease_charge_range_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card
                v-for="(
                    lease_charge_add_period_list_data, lease_charge_add_period_list_index
                ) in lease_charge_add_period_list"
                :key="lease_charge_add_period_list_index"
            >
                <v-card-title class="headline">
                    Add Charge Amount
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_add_lease_charge_range_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_charge_item_msg"
                        :errorMsg2="error_server_charge_item_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease charge add range-->
                    <div v-if="show_add_lease_charge_range_modal">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Rent Review</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        v-model="lease_charge_add_period_list_data.lease_charge_item_new_rent_review"
                                        :options="review_type_list"
                                        :allowEmpty="false"
                                        openDirection="bottom"
                                        class="vue-select2 dropdown-left dropdown-300"
                                        group-label="language"
                                        placeholder="Select a review type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >From Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'lease_charge_add_period_list_data.lease_charge_item_new_start_date'"
                                        v-model="lease_charge_add_period_list_data.lease_charge_item_new_start_date"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                    <span style="position: absolute; margin-top: 4px">
                                        <v-checkbox
                                            v-model="lease_charge_add_period_list_data.lease_charge_item_new_diarise"
                                            label="Diarise"
                                            ripple="false"
                                            dense
                                        ></v-checkbox>
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >To Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'lease_charge_add_period_list_data.lease_charge_item_new_end_date'"
                                        v-model="lease_charge_add_period_list_data.lease_charge_item_new_end_date"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Increase Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-model="lease_charge_add_period_list_data.lease_charge_item_new_increase_type"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            tile
                                            v-for="(
                                                increase_type_listData, increase_type_listIndex
                                            ) in increase_type_list"
                                            :key="increase_type_listIndex"
                                            text
                                            :disabled="!edit_form"
                                            @click="changeIncreaseType()"
                                        >
                                            {{ increase_type_listData.label }}
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="lease_charge_add_period_list_data.lease_charge_item_new_increase_type === 0"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Amount Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-model="lease_charge_add_period_list_data.lease_charge_item_new_amount_type"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            tile
                                            v-for="(amount_type_listData, amount_type_listIndex) in amount_type_list"
                                            :key="amount_type_listIndex"
                                            text
                                            :disabled="!edit_form"
                                        >
                                            {{ amount_type_listData.label }}
                                        </v-btn>
                                    </v-btn-toggle>
                                </v-col>
                            </v-row>

                            <v-row
                                class="form-row"
                                v-if="lease_charge_add_period_list_data.lease_charge_item_new_increase_type === 1"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Increase Percentage</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <input
                                        type="text"
                                        maxlength="10"
                                        v-on:keypress="validateInputNumber($event)"
                                        v-model="
                                            lease_charge_add_period_list_data.lease_charge_item_increase_percentage
                                        "
                                        v-on:blur="calNewAmountChargeItem(lease_charge_add_period_list_index)"
                                        style="width: 10em"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >New Amount</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        inputFormat="dollar"
                                        :disabled="
                                            lease_charge_add_period_list_data.lease_charge_item_new_increase_type === 1
                                        "
                                        :size="'10'"
                                        :key="'lease_charge_add_period_list_data.lease_charge_item_new_amount'"
                                        v-model="lease_charge_add_period_list_data.lease_charge_item_new_amount"
                                        :error_msg="error_msg"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Parking Bays</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'2'"
                                        :key="'lease_charge_add_period_list_data.unit_charge_item_parking'"
                                        v-model="lease_charge_add_period_list_data.unit_charge_item_parking"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="saveLeaseChargeItemNew()"
                        color="success"
                        dark
                        small
                    >
                        Add Charge Item
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="show_add_lease_charge_range_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_back_charge_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    You have chosen a from date prior to the next billing date
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_back_charge_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error :errorMsg2="back_charge_error"></cirrus-server-error>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                                class="form-input"
                            >
                                Would you like to generate the following back charges details?
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Amount</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <strong class="form-input-text"
                                    >{{ currency_symbol
                                    }}{{
                                        accountingAmountFormat(numberWithCommas(roundTo(back_charge_total, 2)))
                                    }}</strong
                                >
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >From Period</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ back_charge_from_date }}</strong>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >To Next Billing Date</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <strong class="form-input-text">({{ back_charge_to_date }})</strong>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Transaction Date</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    :id="'back_charge_transaction_date'"
                                    v-model="back_charge_transaction_date"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="processBackCharge()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Yes
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="show_back_charge_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        No
                    </v-btn>
                    <v-spacer />
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_stopped_charge_modal"
            max-width="300"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Stop Charging
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_stopped_charge_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error :errorMsg2="back_charge_error"></cirrus-server-error>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="6"
                                md="6"
                                class="form-label required"
                                >Stop Date</v-col
                            >
                            <v-col
                                xs="12"
                                sm="6"
                                md="6"
                                class="form-input"
                            >
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    :id="'lease_charge_charging_status_date'"
                                    v-model="lease_charge_charging_status_date"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="1"
                                md="1"
                                class="form-input"
                            >
                                <v-icon small>help</v-icon>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="11"
                                md="11"
                                class="form-input"
                            >
                                <span
                                    class="tooltiptext"
                                    style="width: 300px !important; text-align: left"
                                >
                                    Leave the stop charging date as its default of 31/12/2999 to allow the roll over of
                                    lease charges. Charges should be stopped if vacating a lease or the lease charges
                                    have ended.
                                </span>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="stopCharging()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Yes
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="show_stopped_charge_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        No
                    </v-btn>
                    <v-spacer />
                </v-card-actions>
            </v-card>
        </v-dialog>

        <br />
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import { fetchPropertyLedger } from '../../../../modules/Property/lib/ledger';
import isNil from 'lodash/isNil';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        lease_unit_code: { type: String, default: '' },
        lease_unit_area: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        force_load: { type: Boolean, default: false },
        lease_status: { type: String, default: '' },
        occupancy_start_date: { type: String },
        page_form_type: { type: String, default: '' },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
        country_default_settings: { type: String, default: '' },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_CHARGE',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            error_server_charge_item_msg: {},
            error_server_charge_item_msg2: [],
            error_server_charge_msg: {},
            error_server_charge_msg2: [],
            loading_setting: true,
            loading_content_setting: false,
            show_stopped_charge_modal: false,
            stopped_charge_index: null,
            edit_form: false,
            lease_unit_charges_list: [],
            lease_unit_type_list: [{ index: 0, code: '', label: '' }],
            amount_type_list: [{ code: '', label: '' }],
            increase_type_list: [{ code: '', label: '' }],
            lease_unit_charge_types_list: [{ code: '', label: '' }],
            lease_unit_charge_frequency_list: [{ code: '', label: '' }],
            lease_unit_charge_partial_rent_list: [{ code: '', label: '' }],
            account_list_income_data_main_list: [],
            lease_charge_add_period_list: [],
            review_type_list: [],
            lease_charge_add_show: false,
            success_flag: false,
            unit_charge_account_dropdown: {
                value: '',
                label: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            lease_charge_arr: [],
            lease_charge_description: '',
            lease_charge_type: 0,
            lease_charge_frequency: 0,
            lease_charge_partial_rent_method: 0,
            lease_charge_charging_status_date: '31/12/2999',
            back_charge_transaction_date: '31/12/2999',
            commencement_date: '31/12/2999',
            lease_charge_start_date: this.occupancy_start_date,
            lease_charge_next_date: this.occupancy_start_date,
            lease_charge_amount: '0.00',
            lease_charge_parking_bays: '0',
            show_activity_log_modal: false,
            nav_to_new_charges: false,
            AED_modal: false,
            show_add_lease_charge_range_modal: false,
            show_back_charge_modal: false,
            back_charge: null,
            back_charge_total: '0.00',
            back_charge_from_date: '',
            back_charge_to_date: '',
            back_charge_error: [],
            lease_headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: true, width: '50px' },
                { text: 'Charge', value: 'charge_description', width: '30%' },
                { text: 'From', value: 'unit_charge_start_date_raw', width: '10%' },
                { text: 'Amount Per Year (excl. GST)', value: 'amount', align: 'end' },
                { text: '', value: 'dummy1' },
                { text: '', value: 'data-table-expand', class: ['data-table-mini-action'], align: 'end' },
                { text: '', value: 'action1', class: ['data-table-mini-action'], align: 'end', width: '78px' },
            ],
            lease_charge_item_headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: '', value: 'continuity_check_failed' },
                { text: 'From Date', value: 'unit_charge_item_start_date' },
                { text: 'To Date', value: 'unit_charge_item_end_date' },
                { text: 'Amount', value: 'unit_charge_item_amount', align: 'end' },
                { text: '$/m²', value: 'amount', align: 'end' },
                { text: 'Parking Bay', value: 'unit_area_amount_formatted' },
                { text: 'Rent Review', value: 'review_type' },
                { text: '', value: 'action1', class: ['data-table-mini-action'], align: 'end', width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            expanded: [],
            modal_current_ctr: 0,
            show_tax_rate: true,
            tax_label: 'GST',
            currency_symbol: '$',
            area_unit: 'm&sup2;',
            property_ledger_list: [],
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loading_content_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }

        let country_settings = JSON.parse(atob(this.country_default_settings));
        this.area_unit = this.decodeHTMLEntity(country_settings.area_unit);
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'tour_steps',
            'lease_details',
            'pm_lease_form_read_only',
            'auto_diarise',
            'dd_param_tax_rate_list',
            'sys_ver_control_list',
        ]),
    },
    methods: {
        ...mapMutations([
            'SET_LEASE_MAIN_DETAILS',
            'SET_LEASE_UNIT_DETAILS',
            'SET_LEASE_CHARGE_DETAILS',
            'SET_LEASE_OLD_MAIN_DETAILS',
            'SET_TOUR_STEPS',
        ]),
        dateToday: function () {
            let today = new Date();
            let dd = today.getDate();
            let mm = today.getMonth() + 1; //January is 0!
            let yyyy = today.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            today = dd + '/' + mm + '/' + yyyy;
            return new Date().toISOString().substr(0, 10);
        },
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.force_load) {
                this.loadLedgerList();
                this.loadUnitCharges();
            }
        },
        itemRowBackground: function (item) {
            switch (item.lease_charge_stop_or_no_item) {
                case '1':
                    return 'stopped_style';
                case '2':
                    return 'no_item_style';
                case '3':
                    return 'latest_less_today_style';
                default:
                    return '';
            }
        },
        resetForm: function (lease_unit_charges_list_index) {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
        },
        modalAddData: function () {
            this.AED_modal = true;
            this.error_server_charge_msg = {};
            this.error_server_charge_msg2 = [];
            var d = new Date();
            let temp_next_date = this.commencement_date.split('/');
            let day = parseInt(temp_next_date[0]);
            let month = parseInt(temp_next_date[1]);
            let year = parseInt(temp_next_date[2]);
            if (month + 1 >= 12) {
                month = 1;
                year++;
            } else month++;
            if (month < 10) month = '0' + month.toString();
            if (day < 10) day = '0' + day.toString();

            let new_next_date = day + '/' + month + '/' + year;
            this.lease_charge_arr = {
                index: 'New',
                unit_charge_account_dropdown: { field_key: '', field_value: 'Please Select...' },
                propertyLedgerId: null,
                lease_charge_amount: '0.00',
                lease_charge_parking_bays: '0',
                view_charge_details: {
                    lease_charge_description: '',
                    tax_rate_code: 'TAXABLE',
                    tax_rate_desc: '',
                    lease_charge_type: 0,
                    lease_charge_frequency: 0,
                    lease_charge_partial_rent_method: 0,
                    lease_charge_stop: '',
                    lease_charge_charging_status_date: '31/12/2999',
                    lease_charge_start_date: this.commencement_date,
                    lease_charge_next_date: new_next_date,
                },
                status: 'new',
            };
        },
        loadUnitCharges: function () {
            this.loading_content_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('lease_unit_code', this.lease_unit_code);
            form_data.append('lease_unit_area', this.lease_unit_area);
            form_data.append('no_load', true);
            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/unit-charges';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/unit-charges';
            }

            this.$api.post(apiUrl, form_data).then((response) => {
                this.show_tax_rate = response.data.show_tax_rate;
                this.review_type_list = response.data.review_type_list;
                this.amount_type_list = response.data.amount_type_list;
                this.increase_type_list = response.data.increase_type_list;
                this.lease_unit_charges_list = response.data.lease_unit_charges_list;
                this.lease_unit_charge_types_list = response.data.lease_unit_charge_types_list;
                this.lease_unit_charge_frequency_list = response.data.lease_unit_charge_frequency_list;
                this.lease_unit_charge_partial_rent_list = response.data.lease_unit_charge_partial_rent_list;
                this.account_list_income_data_main_list = response.data.account_list_income_data_main_list;
                this.commencement_date = response.data.commencement_date;
                this.SET_LEASE_CHARGE_DETAILS(response.data);
                this.loading_content_setting = false;

                this.tax_label = response.data.tax_label;
                this.currency_symbol = response.data.currency_symbol;
                this.area_unit = this.decodeHTMLEntity(response.data.area_unit);
                this.updateItemHeaders();

                if (this.formSectionReadOnly(this.pm_lease_form_read_only, this.form_type, this.form_section)) {
                    this.items_per_page = this.lease_unit_charges_list.length;
                }
                if (this.isLeaseInPrintView()) this.expanded = response.data.lease_unit_charges_list;

                if (this.nav_to_new_charges) {
                    // let filtered = this.lease_unit_charges_list.filter( m => m.item_no);
                    const filtered = this.lease_unit_charges_list.reduce(function (prev, current) {
                        return prev.item_no > current.item_no ? prev : current;
                    }); //returns object
                    this.expanded.push(filtered);
                }
            });
        },
        saveForm: function (modal, index) {
            let id = this.lease_unit_charges_list[index].id;
            let unit_charge_serial_id = this.lease_unit_charges_list[index].unit_charge_serial_id;
            if (!modal) this.lease_charge_arr = this.lease_unit_charges_list[index];
            // let account_code = this.lease_unit_charges_list[index].unit_charge_account_dropdown.field_key;
            let lease_charge_account_code = this.lease_charge_arr.unit_charge_account_dropdown.field_key;
            let propertyLedgerId = this.lease_charge_arr.propertyLedgerId;
            let lease_charge_description = this.lease_charge_arr.view_charge_details.lease_charge_description;
            let tax_rate_code = this.lease_charge_arr.view_charge_details.tax_rate_code;
            let lease_charge_type =
                this.lease_unit_charge_types_list[this.lease_charge_arr.view_charge_details.lease_charge_type].code;
            let lease_charge_frequency =
                this.lease_unit_charge_frequency_list[this.lease_charge_arr.view_charge_details.lease_charge_frequency]
                    .code;
            let lease_charge_partial_rent_method =
                this.lease_unit_charge_partial_rent_list[
                    this.lease_charge_arr.view_charge_details.lease_charge_partial_rent_method
                ].code;
            let lease_charge_stop = this.lease_charge_arr.view_charge_details.lease_charge_stop;
            let lease_charge_charging_status_date =
                this.lease_charge_arr.view_charge_details.lease_charge_charging_status_date;
            let lease_charge_start_date = this.lease_charge_arr.view_charge_details.lease_charge_start_date;
            let lease_charge_next_date = this.lease_charge_arr.view_charge_details.lease_charge_next_date;
            let lease_charge_amount = this.lease_charge_arr.lease_charge_amount;
            let lease_charge_parking_bays = this.lease_charge_arr.lease_charge_parking_bays;
            //
            this.error_server_charge_msg2 = [];
            if (lease_charge_account_code === '') {
                this.error_server_charge_msg2.push(['Please input a valid account.']);
            }
            if (this.isMultiplePropertyLedger && isNil(propertyLedgerId)) {
                this.error_server_charge_msg2.push(['Please input a valid ledger.']);
            }
            if (lease_charge_description === '') {
                this.error_server_charge_msg2.push(['Please input a valid charge description.']);
            }
            if (lease_charge_charging_status_date === '') {
                this.error_server_charge_msg2.push(['You have not selected a valid stop date.']);
            }
            if (lease_charge_start_date === '') {
                this.error_server_charge_msg2.push(['Please input a valid start date.']);
            }
            if (lease_charge_next_date === '') {
                this.error_server_charge_msg2.push(['Please input a valid next date.']);
            }
            if (lease_charge_amount === '') {
                this.error_server_charge_msg2.push(['Please input a valid charging amount.']);
            } else {
                if (parseFloat(lease_charge_amount) === 0) {
                    this.error_server_charge_msg2.push(['Please input a valid charging amount.']);
                }
            }
            if (modal) {
                if (lease_charge_parking_bays === '') {
                    // this.error_server_charge_msg2.push(['You have not entered a valid # of Parking Bays. Only numeric characters are allowed']);
                } else {
                    if (!$.isNumeric(lease_charge_parking_bays)) {
                        this.error_server_charge_msg2.push([
                            'You have not entered a valid # of Parking Bays. Only numeric characters are allowed',
                        ]);
                    }
                }
            }
            let errorArr = [];
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('unit_id', id);
                form_data.append('lease_unit_code', this.lease_unit_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('unit_charge_serial_id', unit_charge_serial_id);
                form_data.append('lease_charge_account_code', lease_charge_account_code);
                form_data.append('lease_charge_description', lease_charge_description);
                form_data.append('tax_rate_code', tax_rate_code);
                form_data.append('lease_charge_type', lease_charge_type);
                form_data.append('lease_charge_frequency', lease_charge_frequency);
                form_data.append('lease_charge_partial_rent_method', lease_charge_partial_rent_method);
                form_data.append('lease_charge_stop', lease_charge_stop);
                form_data.append('lease_charge_charging_status_date', lease_charge_charging_status_date);
                form_data.append('lease_charge_start_date', lease_charge_start_date);
                form_data.append('lease_charge_next_date', lease_charge_next_date);
                form_data.append('propertyLedgerId', propertyLedgerId);

                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/update/unit-charges-detail';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/unit-charges-detail';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    // this.edit_form = false;
                    this.loading_setting = false;
                    this.edit_form = this.edit_flag;
                    this.error_server_charge_item_msg2 = response.data.error_server_msg2;
                    if (this.error_server_charge_msg2.length === 0) {
                        this.success_flag = true;
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        saveLeaseChargeAmount: function (lease_unit_charges_list_index, lease_charge_period_list_index) {
            let errorArr = [];
            this.error_server_charge_item_msg2 = errorArr;
            let unit_charge_item_id =
                this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details
                    .lease_charge_period_list[lease_charge_period_list_index].unit_charge_item_id;
            let unit_charge_item_start_date =
                this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details
                    .lease_charge_period_list[lease_charge_period_list_index].unit_charge_item_start_date;
            let unit_charge_item_end_date =
                this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details
                    .lease_charge_period_list[lease_charge_period_list_index].unit_charge_item_end_date;
            let unit_charge_item_amount =
                this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details
                    .lease_charge_period_list[lease_charge_period_list_index].unit_charge_item_amount;
            let unit_charge_item_parking =
                this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details
                    .lease_charge_period_list[lease_charge_period_list_index].unit_charge_item_parking;
            let lease_charge_start_date =
                this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details.lease_charge_start_date;

            if (unit_charge_item_start_date === '') {
                this.error_server_charge_item_msg2.push(['Please input a valid start date']);
            }
            if (unit_charge_item_end_date === '') {
                this.error_server_charge_item_msg2.push(['Please input a valid end date']);
            }
            if (unit_charge_item_amount === '') {
                this.error_server_charge_item_msg2.push(['Please input a valid charging amount']);
            } else {
                if (!$.isNumeric(unit_charge_item_amount)) {
                    this.error_server_charge_msg2.push(['Please input a valid charging amount.']);
                }
            }
            if (unit_charge_item_parking === '') {
                // this.error_server_charge_item_msg2.push(['You have not entered a valid # of Parking Bays. Only numeric characters are allowed']);
            } else {
                if (!$.isNumeric(unit_charge_item_parking)) {
                    this.error_server_charge_item_msg2.push([
                        'You have not entered a valid # of Parking Bays. Only numeric characters are allowed',
                    ]);
                }
            }

            if (this.error_server_charge_item_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('lease_unit_code', this.lease_unit_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('unit_charge_item_id', unit_charge_item_id);
                form_data.append('unit_charge_item_start_date', unit_charge_item_start_date);
                form_data.append('unit_charge_item_end_date', unit_charge_item_end_date);
                form_data.append('unit_charge_item_amount', unit_charge_item_amount);
                form_data.append('lease_charge_start_date', lease_charge_start_date);
                form_data.append('unit_charge_item_parking', unit_charge_item_parking);

                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/update/unit-charge-item';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/unit-charge-item';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    // this.edit_form = false;
                    this.loading_setting = false;
                    this.error_server_charge_item_msg2 = response.data.error_server_msg2;
                    if (this.error_server_charge_item_msg2.length === 0) {
                        this.success_flag = true;
                        this.loadForm();
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    }
                });
            }
        },
        toggleView: function (index) {
            if (this.lease_unit_charges_list[index].view_flag) {
                this.lease_unit_charges_list[index].view_flag = false;
            } else {
                this.lease_unit_charges_list[index].view_flag = true;
            }
        },
        changeUnitChargesAccount: function (index) {
            let account_code = this.lease_unit_charges_list[index].unit_charge_account_dropdown.field_key;
            let accountDescription = this.lease_unit_charges_list[index].unit_charge_account_dropdown.field_value;
            if (
                this.lease_unit_charges_list[index].view_charge_details.lease_charge_account_code !==
                this.lease_unit_charges_list[index].view_charge_details.lease_charge_account_codeOld
            ) {
                this.lease_unit_charges_list[index].unit_charge_account = account_code;
                this.lease_unit_charges_list[index].unit_charge_description = accountDescription;
                this.lease_unit_charges_list[index].view_charge_details.lease_charge_account_code = account_code;
                this.lease_unit_charges_list[index].view_charge_details.lease_charge_description = accountDescription;
            }
        },
        changeUnitChargesAddAccount: function () {
            let account_key = this.lease_charge_arr.unit_charge_account_dropdown.field_key;
            let accountDescription = this.lease_charge_arr.unit_charge_account_dropdown.field_value;
            let unit_charge_description = this.lease_charge_arr.view_charge_details.unit_charge_description;
            let lease_charge_description = this.lease_charge_arr.view_charge_details.lease_charge_description;
            if (account_key !== '') {
                this.lease_charge_arr.unit_charge_description = accountDescription;
                this.lease_charge_arr.view_charge_details.unit_charge_description = accountDescription;
                this.lease_charge_arr.view_charge_details.lease_charge_description = accountDescription;
            }
        },
        saveAddForm: function () {
            let lease_charge_account_code = this.lease_charge_arr.unit_charge_account_dropdown.field_key;
            let lease_charge_description = this.lease_charge_arr.view_charge_details.lease_charge_description;
            let tax_rate_code = this.lease_charge_arr.view_charge_details.tax_rate_code;
            let lease_charge_type =
                this.lease_unit_charge_types_list[this.lease_charge_arr.view_charge_details.lease_charge_type].code;
            let lease_charge_frequency =
                this.lease_unit_charge_frequency_list[this.lease_charge_arr.view_charge_details.lease_charge_frequency]
                    .code;
            let lease_charge_partial_rent_method =
                this.lease_unit_charge_partial_rent_list[
                    this.lease_charge_arr.view_charge_details.lease_charge_partial_rent_method
                ].code;
            let lease_charge_stop = this.lease_charge_arr.view_charge_details.lease_charge_stop;
            let lease_charge_charging_status_date =
                this.lease_charge_arr.view_charge_details.lease_charge_charging_status_date;
            let lease_charge_start_date = this.lease_charge_arr.view_charge_details.lease_charge_start_date;
            let lease_charge_next_date = this.lease_charge_arr.view_charge_details.lease_charge_next_date;
            let lease_charge_amount = this.lease_charge_arr.lease_charge_amount;
            let lease_charge_parking_bays = this.lease_charge_arr.lease_charge_parking_bays;
            //
            this.error_server_charge_msg2 = [];
            if (lease_charge_account_code === '') {
                this.error_server_charge_msg2.push(['Please input a valid account']);
            }
            if (lease_charge_description === '') {
                this.error_server_charge_msg2.push(['Please input a valid charge description']);
            }
            if (lease_charge_start_date === '') {
                this.error_server_charge_msg2.push(['Please input a valid start date']);
            }
            if (lease_charge_next_date === '') {
                this.error_server_charge_msg2.push(['Please input a valid next date']);
            }
            if (lease_charge_amount === '') {
                this.error_server_charge_msg2.push(['Please input a valid charging amount']);
            } else {
                if (parseFloat(lease_charge_amount) === 0) {
                    this.error_server_charge_msg2.push(['Please input a valid charging amount']);
                }
            }
            if (lease_charge_parking_bays === '') {
                // this.error_server_charge_msg2.push(['You have not entered a valid # of Parking Bays. Only numeric characters are allowed']);
            } else {
                if (!$.isNumeric(lease_charge_parking_bays)) {
                    this.error_server_charge_msg2.push([
                        'You have not entered a valid # of Parking Bays. Only numeric characters are allowed',
                    ]);
                }
            }
            if (this.error_server_charge_msg2.length === 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('lease_unit_code', this.lease_unit_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('lease_charge_account_code', lease_charge_account_code);
                form_data.append('lease_charge_description', lease_charge_description);
                form_data.append('tax_rate_code', tax_rate_code);
                form_data.append('lease_charge_type', lease_charge_type);
                form_data.append('lease_charge_frequency', lease_charge_frequency);
                form_data.append('lease_charge_partial_rent_method', lease_charge_partial_rent_method);
                form_data.append('lease_charge_stop', lease_charge_stop);
                form_data.append('lease_charge_charging_status_date', lease_charge_charging_status_date);
                form_data.append('lease_charge_start_date', lease_charge_start_date);
                form_data.append('lease_charge_next_date', lease_charge_next_date);
                form_data.append('lease_charge_amount', lease_charge_amount);
                form_data.append('lease_charge_parking_bays', lease_charge_parking_bays);
                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/create/unit-charges-detail';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/create/unit-charges-detail';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    // this.edit_form = false;
                    this.loading_setting = false;

                    this.unit_charge_account_dropdown = {
                        value: '',
                        label: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    };
                    this.lease_charge_description = '';
                    this.lease_charge_type = 0;
                    this.lease_charge_frequency = 0;
                    this.lease_charge_partial_rent_method = 0;
                    this.lease_charge_charging_status_date = '31/12/2999';
                    this.lease_charge_start_date = this.occupancy_start_date;
                    this.lease_charge_next_date = this.occupancy_start_date;
                    this.lease_charge_amount = '0.00';
                    this.lease_charge_parking_bays = '0';
                    this.nav_to_new_charges = true;
                    this.modalAddData();
                    this.loadForm();
                    this.AED_modal = false;
                    this.$noty.success('Successfully added');
                    this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        addLeaseChargeLine: function (lease_unit_charges_list_index) {
            this.error_server_charge_item_msg = {};
            this.error_server_charge_item_msg2 = [];
            this.show_add_lease_charge_range_modal = true;
            let lease_charge_id = this.lease_unit_charges_list[lease_unit_charges_list_index].id;
            let amount = this.lease_unit_charges_list[lease_unit_charges_list_index].amount;
            let lease_charge_period_list =
                this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details
                    .lease_charge_period_list;
            let lease_charge_item_new_start_date = this.lease_charge_start_date;
            if (lease_charge_period_list.length > 0) {
                let last_period_end_date =
                    lease_charge_period_list[lease_charge_period_list.length - 1].unit_charge_item_start_date;
                amount = lease_charge_period_list[lease_charge_period_list.length - 1].unit_charge_item_amount;
                let end_date_split = last_period_end_date.split('/');
                let day = end_date_split[0];
                let month = end_date_split[1];
                let year = parseInt(end_date_split[2]) + 1;
                lease_charge_item_new_start_date = day + '/' + month + '/' + year;
            }
            this.lease_charge_add_period_list = [
                {
                    lease_charge_index: lease_unit_charges_list_index,
                    lease_charge_id: lease_charge_id,
                    lease_charge_item_new_start_date: lease_charge_item_new_start_date,
                    lease_charge_item_new_end_date: '31/12/2999',
                    lease_charge_item_new_increase_type: 0,
                    lease_charge_item_new_amount_type: 0,
                    unit_charge_item_parking: 0,
                    lease_charge_item_new_amount: '0.00',
                    lease_charge_item_old_amount: amount,
                    lease_charge_item_increase_percentage: '0',
                    lease_charge_item_new_rent_review: {
                        value: '',
                        label: 'Please select ...',
                        field_key: '',
                        field_value: 'Please select ...',
                    },
                    lease_charge_item_new_diarise: this.auto_diarise,
                },
            ];
        },
        deleteLeaseChargeItemNew: function (lease_unit_charges_list_index, lease_charge_add_period_list_index) {
            this.lease_unit_charges_list[
                lease_unit_charges_list_index
            ].view_charge_details.lease_charge_add_period_list.splice(lease_charge_add_period_list_index, 1);
        },
        async deleteLeaseChargeItem(lease_unit_charges_list_index, lease_charge_period_list_index) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                let get_id =
                    this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details
                        .lease_charge_period_list[lease_charge_period_list_index].unit_charge_item_id;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                form_data.append('lease_charge_item_id', get_id);

                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/delete/unit-charges-item';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/unit-charges-item';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.lease_unit_charges_list[
                        lease_unit_charges_list_index
                    ].view_charge_details.lease_charge_period_list.splice(lease_charge_period_list_index, 1);
                    this.loadForm();
                    let dialog_prop = {
                        title: 'information',
                        message: 'Lease charge item is deleted.',
                        icon_show: true,
                    };
                    cirrusDialog(dialog_prop);
                });
            }
        },
        async deleteUnitCharge(lease_unit_charges_list_index) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                let lease_unit_charges_list = this.lease_unit_charges_list[lease_unit_charges_list_index];
                let get_id = lease_unit_charges_list.id;
                let unit_charge_serial_id = lease_unit_charges_list.unit_charge_serial_id;

                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('lease_charge_id', get_id);
                form_data.append('no_load', true);
                form_data.append('unit_charge_serial_id', unit_charge_serial_id);
                form_data.append('lease_unit_code', this.lease_unit_code);
                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/delete/unit-charges';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/unit-charges';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadForm();
                    // this.lease_unit_charges_list[lease_unit_charges_list_index].view_charge_details.lease_charge_period_list.splice(lease_charge_period_list_index,1);
                    let dialog_prop = {
                        title: 'information',
                        message: 'Lease unit charge item is deleted.',
                        icon_show: true,
                    };
                    cirrusDialog(dialog_prop);
                });
            }
        },
        saveLeaseChargeItemNew: function () {
            let lease_charge_add_period_list = this.lease_charge_add_period_list;
            let lease_charge_index = lease_charge_add_period_list[0].lease_charge_index;
            let new_from_date = lease_charge_add_period_list[0].lease_charge_item_new_start_date;
            let new_to_date = lease_charge_add_period_list[0].lease_charge_item_new_end_date;
            let new_increase_type = lease_charge_add_period_list[0].lease_charge_item_new_increase_type;
            let new_amount_type = lease_charge_add_period_list[0].lease_charge_item_new_amount_type;
            let new_new_amount = lease_charge_add_period_list[0].lease_charge_item_new_amount;
            let new_increase_percentage = lease_charge_add_period_list[0].lease_charge_item_increase_percentage;
            let new_rent_review = lease_charge_add_period_list[0].lease_charge_item_new_rent_review.field_key;
            let new_diarise = lease_charge_add_period_list[0].lease_charge_item_new_diarise;
            let unit_charge_item_parking = lease_charge_add_period_list[0].unit_charge_item_parking;
            new_diarise = new_diarise ? 1 : 0;
            this.error_server_charge_item_msg = {};
            this.error_server_charge_item_msg2 = [];
            if (new_from_date === '') {
                this.error_server_charge_item_msg2.push(['Please input a valid from date']);
            }
            if (new_to_date === '') {
                this.error_server_charge_item_msg2.push(['Please input a valid to date']);
            }
            if (new_new_amount === '') {
                this.error_server_charge_item_msg2.push(['Please input a valid new amount']);
            } else if (!$.isNumeric(new_new_amount)) {
                this.error_server_charge_msg2.push(['Please input a valid charging amount.']);
            }
            if (unit_charge_item_parking === '') {
                // this.error_server_charge_msg2.push(['You have not entered a valid # of Parking Bays. Only numeric characters are allowed']);
            } else {
                if (!$.isNumeric(unit_charge_item_parking)) {
                    this.error_server_charge_item_msg2.push([
                        'You have not entered a valid # of Parking Bays. Only numeric characters are allowed',
                    ]);
                }
            }
            if (this.error_server_charge_item_msg2.length <= 0) {
                let lease_charge_id = lease_charge_add_period_list[0].lease_charge_id;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('lease_charge_id', lease_charge_id);
                form_data.append('new_from_date', new_from_date);
                form_data.append('new_to_date', new_to_date);
                form_data.append('new_increase_type', new_increase_type);
                form_data.append('new_amount_type', new_amount_type);
                form_data.append('new_new_amount', new_new_amount);
                form_data.append('new_increase_percentage', new_increase_percentage);
                form_data.append('new_rent_review', new_rent_review);
                form_data.append('new_diarise', new_diarise);
                form_data.append('new_parking_bay', unit_charge_item_parking);
                form_data.append('lease_unit_area', this.lease_unit_area);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/create/unit-charges-item';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/create/unit-charges-item';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    let status = response.data.status;
                    if (status === 'Success') {
                        this.error_server_charge_item_msg = {};
                        this.error_server_charge_item_msg2 = [];
                        this.show_add_lease_charge_range_modal = false;

                        let lc_lease_charge_period_list = response.data.lease_charge_period_list;
                        this.lease_unit_charges_list[lease_charge_index].view_charge_details.lease_charge_period_list =
                            lc_lease_charge_period_list;
                        this.lease_unit_charges_list[
                            lease_charge_index
                        ].view_charge_details.lease_charge_add_period_list = [];
                        let back_charge = response.data.back_charge;
                        if (back_charge !== null && back_charge !== undefined) {
                            this.show_back_charge_modal = true;
                            this.back_charge = back_charge;
                            this.back_charge_transaction_date = back_charge.transaction_date;
                            this.back_charge_total = back_charge.total;
                            this.back_charge_from_date = back_charge.from_date;
                            this.back_charge_to_date = back_charge.to_date;
                        }
                        this.loadForm();
                        bus.$emit('loadLeaseRentReviewSection', { unit_code: '' });
                        bus.$emit('loadLeaseDiarySection', '');
                        this.success_flag = true;
                        setTimeout(
                            function () {
                                this.success_flag = false;
                            }.bind(this),
                            2000,
                        );
                    } else {
                        this.error_server_charge_item_msg2 = response.data.validation_errors;
                    }
                });
            }
        },
        changeIncreaseType: function () {
            this.calNewAmountChargeItem(0);
        },
        calNewAmountChargeItem: function (index1) {
            let new_charge_item_list = this.lease_charge_add_period_list[index1];
            let percentage = new_charge_item_list.lease_charge_item_increase_percentage;
            if (percentage === '') {
                percentage = 100;
                // this.lease_unit_charges_list[index1].view_charge_details.lease_charge_add_period_list[index2].lease_charge_item_increase_percentage = percentage;
            }
            percentage = parseFloat(percentage);
            let amount = parseFloat(new_charge_item_list.lease_charge_item_old_amount);
            let percentage_value = percentage / 100;
            let new_amount = amount + amount * percentage_value;
            this.lease_charge_add_period_list[index1].lease_charge_item_new_amount = new_amount;
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        processBackCharge: function () {
            let back_charge = this.back_charge;
            let back_charge_transaction_date = this.back_charge_transaction_date;
            this.back_charge_error = [];
            if (back_charge_transaction_date === '') {
                this.back_charge_error.push(['Please enter a valid transaction date.']);
            }
            if (this.back_charge_error.length === 0) {
                if (back_charge !== null) {
                    let net = back_charge.net;
                    let gst = back_charge.gst;
                    let property_code = back_charge.property_code;
                    let lease_code = back_charge.lease_code;
                    let unit_code = back_charge.unit_code;
                    let account_code = back_charge.account_code;
                    let description = back_charge.description;
                    let tax_code = back_charge.tax_code;
                    let from_date = back_charge.from_date;
                    let to_date = back_charge.to_date;
                    let amount_total = back_charge.amount_total;
                    let charge_amount = back_charge.charge_amount;
                    let tax_rate = back_charge.tax_rate;
                    let total = back_charge.total;

                    let form_data = new FormData();
                    form_data.append('property_code', property_code);
                    form_data.append('lease_code', lease_code);
                    form_data.append('unit_code', unit_code);
                    form_data.append('account_code', account_code);
                    form_data.append('description', description);
                    form_data.append('tax_code', tax_code);
                    form_data.append('from_date', from_date);
                    form_data.append('to_date', to_date);
                    form_data.append('amount_total', amount_total);
                    form_data.append('charge_amount', charge_amount);
                    form_data.append('tax_rate', tax_rate);
                    form_data.append('total', total);
                    form_data.append('gst', gst);
                    form_data.append('net', net);
                    form_data.append('transaction_date', back_charge_transaction_date);
                    let api_url = this.cirrus8_api_url + 'api/lease/process/back-charge';
                    this.$api.post(api_url, form_data).then((response) => {
                        this.back_charge_error = response.data.error_server_msg;
                        if (this.back_charge_error.length === 0) {
                            this.show_back_charge_modal = false;
                            this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        }
                    });
                }
            }
        },
        async showStopModal(index) {
            this.show_stopped_charge_modal = true;
            this.stopped_charge_index = index;
        },
        async stopCharging() {
            this.show_stopped_charge_modal = false;
            let index = this.stopped_charge_index;
            let temp_holder = this.lease_unit_charges_list[index];
            let unit_charge_serial_id = temp_holder.unit_charge_serial_id;
            let unit_charge_stop_date = this.lease_charge_charging_status_date;

            this.loading_page_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('unit_charge_serial_id', unit_charge_serial_id);
            form_data.append('lease_unit_code', this.lease_unit_code);
            form_data.append('unit_charge_stop_date', unit_charge_stop_date);
            form_data.append('app_origin', 'lease_page');
            let api_url = 'lease/process/stop-charging';

            this.$api.post(api_url, form_data).then((response) => {
                this.loading_page_setting = false;
                this.loadForm();
            });
        },
        async startCharging(index) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                let temp_holder = this.lease_unit_charges_list[index];
                let unit_charge_serial_id = temp_holder.unit_charge_serial_id;
                this.loading_page_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('unit_charge_serial_id', unit_charge_serial_id);
                form_data.append('lease_unit_code', this.lease_unit_code);
                form_data.append('app_origin', 'lease_page');
                let api_url = 'lease/process/start-charging';

                this.$api.post(api_url, form_data).then((response) => {
                    this.loading_page_setting = false;
                    this.loadForm();
                });
            }
        },
        modalPrevData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_charge_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.lease_unit_charges_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.lease_charge_arr = this.lease_unit_charges_list[this.modal_current_ctr];
            this.lease_charge_arr.index = this.modal_current_ctr;
            this.lease_charge_arr.status = 'saved';
        },
        modalNextData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_charge_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.lease_unit_charges_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.lease_charge_arr = this.lease_unit_charges_list[this.modal_current_ctr];
            this.lease_charge_arr.index = this.modal_current_ctr;
            this.lease_charge_arr.status = 'saved';
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_charge_arr = this.lease_unit_charges_list[index];
            this.lease_charge_arr.index = index;
            this.lease_charge_arr.status = 'saved';
            this.modal_current_ctr = index;
            bus.$emit('refreshSingleUploadComponent', '');
        },

        formatAsCurrency: function (amt, dec) {
            amt = parseFloat(amt);
            if (amt) {
                dec = dec || 0;
                if (amt < 0) {
                    amt = amt * -1;
                    return '$(' + amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,') + ')';
                } else {
                    return '$' + amt.toFixed(dec).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,');
                }
            } else {
                return '$ 0.00';
            }
        },
        generateUnitChargeStatusLabel: function (item) {
            let label = '';
            let date_lbl = '';
            let charge_stop_flag = item.lease_charge_stop;
            let charge_stop_date = item.lease_charge_charging_status_date;
            if (charge_stop_date) date_lbl = '(' + charge_stop_date + ')';
            if (charge_stop_flag === '1' || charge_stop_date) label = 'STOPPED ' + date_lbl;
            return label;
        },
        generateUnitChargeColor: function (param1, param2) {
            if (param1 === '1') {
                if (param2) return 'orange';
                else return 'red';
            }
            return 'grey';
        },
        updateItemHeaders: function () {
            let taxLabel = this.tax_label;
            let currencySymbol = this.currency_symbol;
            let areaUnit = this.decodeHTMLEntity(this.area_unit);

            this.lease_headers.forEach(function (header) {
                if (header.value == 'amount') {
                    header.text = header.text.replace('GST', taxLabel);
                }
            });
            this.lease_charge_item_headers.forEach(function (header) {
                if (header.value == 'amount') {
                    header.text = currencySymbol + '/' + areaUnit;
                }
            });
        },
        async loadLedgerList() {
            this.property_ledger_list = [];
            try {
                const ledgerList = await fetchPropertyLedger(this.property_code);
                // Normalize possible response shapes, then map to [{ field_key, field_value }]
                const list = Array.isArray(ledgerList) ? ledgerList : [];
                this.property_ledger_list = list.map(({ id, propertyLedgerCode, description }) => ({
                    field_key: id,
                    code: propertyLedgerCode,
                    field_value: description,
                }));
            } catch (err) {}
        },
        isMultiplePropertyLedger() {
            return !!this.sys_ver_control_list?.isMultiplePropertyLedger;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        lease_unit_code: function () {
            this.loadForm();
        },
        'lease_charge_arr.unit_charge_account_dropdown.field_key': function () {
            this.changeUnitChargesAddAccount();
        },
        force_load: function () {
            if (this.force_load) {
                this.loadForm();
            }
        },
        occupancy_start_date: function () {
            this.lease_charge_start_date = this.occupancy_start_date;
            this.lease_charge_next_date = this.occupancy_start_date;
        },
    },
    created() {
        bus.$on('loadLeaseChargeSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
