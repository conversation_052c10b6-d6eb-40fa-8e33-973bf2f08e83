<template>
    <div v-on:dblclick="doubleClickForm()">
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <v-card
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <!-- general details tabindex range 1 - 10-->
                <h6 class="title font-weight-black">General Details</h6>
                &nbsp&nbsp
                <v-icon
                    small
                    @click="toggle_tips ? (toggle_tips = false) : (toggle_tips = true)"
                    class="titleHeader"
                    >info
                </v-icon>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    class="v-step-edit-button"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form"
                    icon
                    @click="
                        loadMainFormDetails();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form"
                    class="v-step-save-1-button"
                    icon
                    @click="saveForm()"
                    :disabled="save_button_enable"
                >
                    <v-icon
                        light
                        color="green"
                        >check
                    </v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    class="v-step-refresh-button"
                    icon
                    @click="loadMainFormDetails()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div v-if="!loading_setting">
            <v-alert
                border="left"
                dense
                type="info"
                v-if="toggle_tips"
                class="rounded-0 mb-0 c8-info-alert"
            >
                * This information will be used to create a debtor company, a debtor company maybe attached to one lease
                or multiple leases, if attached to multiple leases, all correspondence for those leases will go to the
                one debtor company address.<br />
                * Example 1: Lease code: SUBWAY, Debtor code: SUBWAY<br />
                * Example 2: Lease code 1: COLESSYD, Lease code 2: COLESMEL, Lease code 3: COLESBRI, Debtor code: COLES
                – all correspondence for the 3 leases will go to COLES.<br />
            </v-alert>
            <div
                class="page-form"
                v-if="!read_only && !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
            >
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Lease Name:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        :id="'lease_name'"
                                        v-model="lease_name"
                                        :edit_form="edit_form"
                                        tabindex="1"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table
                            class="data-grid data-grid-dense data-grid-no-line tableHive"
                            v-if="isLeaseFormLive()"
                        >
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>CRN:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        inputFormat="wholeNumberOnly"
                                        :id="'lease_tenant_crn'"
                                        v-model="lease_tenant_crn"
                                        :edit_form="edit_form"
                                        maxlength="10"
                                        tabindex="4"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenancy Location:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        :id="'lease_tenant_location'"
                                        v-model="lease_tenant_location"
                                        :edit_form="edit_form"
                                        tabindex="2"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table
                            class="data-grid data-grid-dense data-grid-no-line tableHive"
                            v-if="isLeaseFormLive()"
                        >
                            <tr v-show="!new_lease">
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>{{ portfolio_manager_label }}:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    {{ property_portfolio_desc }}
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Primary Lease:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-show="!read_only && edit_form && is_main_lease_enabled"
                                        v-model="is_main_lease_index"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form"
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                            :disabled="!edit_form"
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                    <div v-if="is_main_lease_enabled">
                                        <span
                                            class="form-input-text"
                                            v-if="is_main_lease_index === 0 && !edit_form"
                                            >Yes</span
                                        >
                                        <span
                                            class="form-input-text"
                                            v-if="is_main_lease_index === 1 && !edit_form"
                                            >No</span
                                        >
                                    </div>
                                    <div v-if="!is_main_lease_enabled">
                                        <span
                                            class="form-input-text"
                                            v-if="is_main_lease_index === 0"
                                            >Yes</span
                                        >
                                        <span
                                            class="form-input-text"
                                            v-if="is_main_lease_index === 1"
                                            >No</span
                                        >
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </div>
            <!-- FOR PRINTING -->
            <div
                class="page-form"
                v-if="formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
            >
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Lease Name:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        :id="'lease_name'"
                                        v-model="lease_name"
                                        :edit_form="edit_form"
                                        tabindex="1"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenancy Location:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        :id="'lease_tenant_location'"
                                        v-model="lease_tenant_location"
                                        :edit_form="edit_form"
                                        tabindex="2"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Primary Lease:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-show="!read_only && edit_form && is_main_lease_enabled"
                                        v-model="is_main_lease_index"
                                        mandatory
                                    >
                                        <v-btn
                                            text
                                            :disabled="!edit_form"
                                        >
                                            Yes
                                        </v-btn>
                                        <v-btn
                                            text
                                            :disabled="!edit_form"
                                        >
                                            No
                                        </v-btn>
                                    </v-btn-toggle>
                                    <div v-if="is_main_lease_enabled">
                                        <span
                                            class="form-input-text"
                                            v-if="is_main_lease_index === 0 && !edit_form"
                                            >Yes</span
                                        >
                                        <span
                                            class="form-input-text"
                                            v-if="is_main_lease_index === 1 && !edit_form"
                                            >No</span
                                        >
                                    </div>
                                    <div v-if="!is_main_lease_enabled">
                                        <span
                                            class="form-input-text"
                                            v-if="is_main_lease_index === 0"
                                            >Yes</span
                                        >
                                        <span
                                            class="form-input-text"
                                            v-if="is_main_lease_index === 1"
                                            >No</span
                                        >
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table
                            class="data-grid data-grid-dense data-grid-no-line tableHive"
                            v-if="isLeaseFormLive()"
                        >
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>CRN:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        inputFormat="wholeNumberOnly"
                                        :id="'lease_tenant_crn'"
                                        v-model="lease_tenant_crn"
                                        :edit_form="edit_form"
                                        maxlength="8"
                                        tabindex="4"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table
                            class="data-grid data-grid-dense data-grid-no-line tableHive"
                            v-if="isLeaseFormLive()"
                        >
                            <tr v-show="!new_lease">
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>{{ portfolio_manager_label }}:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    {{ property_portfolio_desc }}
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </div>
        </div>
        <!-- Tenant Details and Associated Company tabindex range 11 - 20-->
        <v-card
            id="tenantDetailsSection"
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Tenant Details and Associated Company</h6>
            </v-card-actions>
        </v-card>

        <div v-if="!loading_setting">
            <div
                class="page-form"
                v-if="!read_only && !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
            >
                <!--       Company Type: new or existing         -->
                <v-row
                    class="form-row no-gutters"
                    v-if="debtor_in_ar_count === 0 && new_lease && edit_form"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Company Type:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <v-btn-toggle
                                        class="form-toggle"
                                        v-model="company_type"
                                        mandatory
                                    >
                                        <v-btn
                                            small
                                            text
                                        >
                                            New
                                        </v-btn>
                                        <v-btn
                                            small
                                            text
                                        >
                                            Existing
                                        </v-btn>
                                    </v-btn-toggle>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--       Company Code         -->
                <v-row
                    class="form-row no-gutters"
                    v-if="debtor_in_ar_count === 0 && company_type === 0 && edit_form"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                                lg="12"
                                xl="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Company Code:</strong>
                                        </td>
                                        <td
                                            class="required"
                                            v-if="edit_form"
                                        >
                                            *
                                        </td>
                                        <td>
                                            <cirrus-input
                                                v-model="new_company_code"
                                                size="10"
                                                :id="'new_company_code'"
                                                :edit_form="true"
                                                tabindex="11"
                                                :error_msg="error_msg"
                                                :maxlength="10"
                                            ></cirrus-input>
                                            <v-btn
                                                color="primary"
                                                x-small
                                                @click="checkCompanyCode()"
                                                >Create New
                                            </v-btn>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </v-col>
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                        v-if="
                            (company_type === 1 && lease_company.field_key !== '') ||
                            (debtor_in_ar_count === 0 &&
                                !company_existed &&
                                company_type === 0 &&
                                create_new_company_btn)
                        "
                    >
                        <v-row class="form-row no-gutters">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                                lg="12"
                                xl="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong>Tenant {{ tenant_country_defaults.business_label }}:</strong>
                                        </td>
                                        <td
                                            class="required"
                                            v-if="edit_form"
                                        ></td>
                                        <td>
                                            <span
                                                class="form-input-text"
                                                v-if="
                                                    lease_tenant_abn !== '' &&
                                                    company_type === 1 &&
                                                    !company_is_temp_flag
                                                "
                                            >
                                                <a
                                                    v-on:click="goToShortcut('company')"
                                                    data-tooltip="Go to company"
                                                >
                                                    <span v-if="lease_tenant_abn && lease_tenant_abn != ''">{{
                                                        tenant_country_defaults.business_prefix
                                                    }}</span
                                                    >{{ lease_tenant_abn }}
                                                </a>
                                            </span>
                                            <span
                                                class="form-input-text"
                                                v-if="
                                                    lease_tenant_abn !== '' &&
                                                    company_type === 1 &&
                                                    company_is_temp_flag
                                                "
                                                >{{ lease_tenant_abn }}</span
                                            >
                                            <a
                                                v-if="
                                                    lease_tenant_abn === '' &&
                                                    company_type === 1 &&
                                                    page_form_type !== 'client-new-page' &&
                                                    !company_is_temp_flag
                                                "
                                                v-on:click="goToShortcut('company')"
                                                data-tooltip="Go to company"
                                            >
                                                ADD
                                            </a>
                                            <span
                                                class="business-prefix-vue"
                                                v-if="debtor_in_ar_count === 0 && company_type === 0"
                                                v-show="tenant_country_defaults.business_prefix"
                                                >{{ tenant_country_defaults.business_prefix }}</span
                                            >
                                            <v-text-field
                                                v-if="debtor_in_ar_count === 0 && company_type === 0"
                                                :maxlength="tenant_country_defaults.business_length"
                                                tabindex="19"
                                                dense
                                                v-model="lease_tenant_abn"
                                                @change="lookupABN($event)"
                                            />
                                            <div
                                                class="abn-detail-container"
                                                v-if="
                                                    tenant_country_defaults.country_code == 'AU' &&
                                                    debtor_in_ar_count === 0 &&
                                                    company_type === 0
                                                "
                                            >
                                                <v-btn
                                                    text
                                                    depressed
                                                    small
                                                    class="abn-lookup-btn"
                                                    @click="lookupABN($event)"
                                                    :loading="abn_button_loading"
                                                >
                                                    {{ abn_button_label }}
                                                </v-btn>
                                                <span
                                                    class="loading-info-text"
                                                    v-if="abn_button_loading"
                                                    >Looking up ABN...</span
                                                >
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                <v-row
                                    class="form-row no-gutters"
                                    v-if="
                                        (abn_details || abn_message != '') &&
                                        tenant_country_defaults.country_code == 'AU'
                                    "
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    <strong v-if="abn_details"
                                                        >ABN Details
                                                        <v-icon
                                                            style="font-size: 21px; position: relative; top: -1px"
                                                            :title="'Values retrieved from ABN and Company Name Lookup'"
                                                            >info
                                                        </v-icon>
                                                    </strong>
                                                </td>
                                                <td
                                                    class="required"
                                                    v-if="edit_form"
                                                ></td>
                                                <td>
                                                    <table
                                                        v-if="abn_details"
                                                        class="abn-result-table"
                                                    >
                                                        <thead v-if="abn_message != ''">
                                                            <tr>
                                                                <th
                                                                    colspan="2"
                                                                    style="border-bottom: 1px solid #ececec !important"
                                                                    class="abn-message"
                                                                >
                                                                    {{ abn_message }}
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>Company Name</td>
                                                                <td>{{ abn_comp_name }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td>ABN</td>
                                                                <td>
                                                                    {{ abn_value }}
                                                                    <span v-if="abn_status == 'Cancelled'"
                                                                        >({{ abn_status }})</span
                                                                    >
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>ACN</td>
                                                                <td>{{ abn_acn }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td>Company Type</td>
                                                                <td>{{ abn_comp_type }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td>GST Status</td>
                                                                <td>{{ abn_gst_status }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td>State Code</td>
                                                                <td>{{ abn_state_code }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td>Post Code</td>
                                                                <td>{{ abn_post_code }}</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <div
                                                        v-else
                                                        class="abn-message"
                                                    >
                                                        {{ abn_message }}
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                            </v-col>
                        </v-row>
                    </v-col>
                </v-row>
                <!--       Comany and email details         -->
                <v-row
                    class="form-row no-gutters"
                    v-if="
                        debtor_in_ar_count > 0 ||
                        (debtor_in_ar_count === 0 && company_existed && company_type === 0 && create_new_company_btn) ||
                        (debtor_in_ar_count === 0 && company_existed && company_type === 1)
                    "
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Company:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td v-if="debtor_in_ar_count === 0 && company_type === 1 && edit_form">
                                    <multiselect
                                        tabindex="12"
                                        v-model="lease_company"
                                        :options="company_list"
                                        :allowEmpty="false"
                                        :options-limit="10000"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        :custom-label="nameWithDash"
                                        group-label="language"
                                        placeholder="Select a company"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <v-btn
                                        :loading="company_list.length <= 0"
                                        depressed
                                        elevation="0"
                                        small
                                        color="normal"
                                        class="rounded-l-0"
                                        height="30"
                                        v-on:click="loadCompanyDetails()"
                                    >
                                        <v-icon>arrow_right</v-icon>
                                    </v-btn>
                                    <a
                                        v-if="lease_company.field_key !== ''"
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon
                                            elevation="0"
                                            icon
                                            small
                                            height="30"
                                            >business
                                        </v-icon>
                                    </a>
                                </td>
                                <td v-if="debtor_in_ar_count === 0 && company_type === 1 && !edit_form">
                                    <span class="form-input-text"
                                        >{{ lease_company.field_key }} - {{ lease_company.field_value }}</span
                                    >
                                    <a
                                        v-if="lease_company.field_key !== ''"
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon
                                            elevation="0"
                                            icon
                                            small
                                            height="30"
                                            >business
                                        </v-icon>
                                    </a>
                                </td>
                                <td
                                    v-if="
                                        debtor_in_ar_count > 0 || (new_lease && !company_existed && company_type === 0)
                                    "
                                >
                                    <strong v-if="debtor_in_ar_count > 0"
                                        ><span class="form-input-text"
                                            >{{ lease_company_code }} - {{ lease_company_name }}</span
                                        ></strong
                                    >
                                    <a
                                        v-if="debtor_in_ar_count > 0"
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon
                                            v-if="
                                                !formSectionReadOnly(
                                                    pm_lease_form_read_only,
                                                    form_type,
                                                    form_section,
                                                    is_inactive,
                                                )
                                            "
                                            elevation="0"
                                            icon
                                            small
                                            height="30"
                                            >business
                                        </v-icon>
                                    </a>
                                    <cirrus-input
                                        v-if="debtor_in_ar_count === 0"
                                        :id="'company'"
                                        v-model="lease_company_code"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                                <td
                                    v-if="
                                        debtor_in_ar_count > 0 || (!new_lease && !company_existed && company_type === 0)
                                    "
                                >
                                    <cirrus-input
                                        v-if="debtor_in_ar_count === 0"
                                        :id="'company'"
                                        v-model="lease_company_code"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                    <!--          <span>company type: {{company_type}} ||| lease_company:{{lease_company.field_key}} ||| debtor count: {{debtor_in_ar_count}} ||| company_existed: {{company_existed}} ||| company_type: {{company_type}} |||  create_new_company_btn: {{create_new_company_btn}} ||| </span>-->
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                        v-if="
                            (company_type === 1 && lease_company.field_key !== '') ||
                            (debtor_in_ar_count === 0 &&
                                !company_existed &&
                                company_type === 0 &&
                                create_new_company_btn)
                        "
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant {{ tenant_country_defaults.business_label }}:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <span
                                        class="form-input-text"
                                        v-if="lease_tenant_abn !== '' && company_type === 1 && !company_is_temp_flag"
                                    >
                                        <a
                                            v-on:click="goToShortcut('company')"
                                            data-tooltip="Go to company"
                                        >
                                            <span v-if="lease_tenant_abn && lease_tenant_abn != ''">{{
                                                tenant_country_defaults.business_prefix
                                            }}</span
                                            >{{ lease_tenant_abn }}
                                        </a>
                                    </span>
                                    <span
                                        class="form-input-text"
                                        v-if="lease_tenant_abn !== '' && company_type === 1 && company_is_temp_flag"
                                        >{{ lease_tenant_abn }}</span
                                    >
                                    <a
                                        v-if="
                                            lease_tenant_abn === '' &&
                                            company_type === 1 &&
                                            page_form_type !== 'client-new-page' &&
                                            !company_is_temp_flag
                                        "
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        ADD
                                    </a>
                                    <v-text-field
                                        v-if="debtor_in_ar_count === 0 && company_type === 0"
                                        :maxlength="11"
                                        tabindex="19"
                                        dense
                                        v-model="lease_tenant_abn"
                                        @change="lookupABN($event)"
                                    />
                                    <div
                                        class="abn-detail-container"
                                        v-if="
                                            tenant_country_defaults.country_code == 'AU' &&
                                            debtor_in_ar_count === 0 &&
                                            company_type === 0
                                        "
                                    >
                                        <v-btn
                                            text
                                            depressed
                                            small
                                            class="abn-lookup-btn"
                                            @click="lookupABN($event)"
                                            :loading="abn_button_loading"
                                        >
                                            {{ abn_button_label }}
                                        </v-btn>
                                        <span
                                            class="loading-info-text"
                                            v-if="abn_button_loading"
                                            >Looking up ABN...</span
                                        >
                                    </div>
                                </td>
                            </tr>
                        </table>
                        <v-row
                            class="form-row no-gutters"
                            v-if="abn_details || abn_message != ''"
                        >
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                    <tr>
                                        <td
                                            class="title"
                                            align="right"
                                        >
                                            <strong v-if="abn_details"
                                                >ABN Details
                                                <v-icon
                                                    style="font-size: 21px; position: relative; top: -1px"
                                                    title="Values retrieved from ABN and Company Name Lookup"
                                                    >info
                                                </v-icon>
                                            </strong>
                                        </td>
                                        <td
                                            class="required"
                                            v-if="edit_form"
                                        ></td>
                                        <td>
                                            <table
                                                v-if="abn_details"
                                                class="abn-result-table"
                                            >
                                                <thead v-if="abn_message != ''">
                                                    <tr>
                                                        <th
                                                            colspan="2"
                                                            style="border-bottom: 1px solid #ececec !important"
                                                            class="abn-message"
                                                        >
                                                            {{ abn_message }}
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>Company Name</td>
                                                        <td>{{ abn_comp_name }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>ABN</td>
                                                        <td>
                                                            {{ abn_value }}
                                                            <span v-if="abn_status == 'Cancelled'"
                                                                >({{ abn_status }})</span
                                                            >
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>ACN</td>
                                                        <td>{{ abn_acn }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Company Type</td>
                                                        <td>{{ abn_comp_type }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>GST Status</td>
                                                        <td>{{ abn_gst_status }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>State Code</td>
                                                        <td>{{ abn_state_code }}</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Post Code</td>
                                                        <td>{{ abn_post_code }}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <div
                                                v-else
                                                class="abn-message"
                                            >
                                                {{ abn_message }}
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </v-col>
                        </v-row>
                    </v-col>
                </v-row>
                <!--      Tenant Name and Tenant Country          -->
                <v-row
                    class="form-row no-gutters"
                    v-if="
                        debtor_in_ar_count > 0 ||
                        (debtor_in_ar_count === 0 &&
                            !company_existed &&
                            company_type === 0 &&
                            create_new_company_btn) ||
                        (debtor_in_ar_count === 0 && company_existed && company_type === 1)
                    "
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Name:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        :size="'40'"
                                        :id="'lease_tenant_name'"
                                        v-model="lease_tenant_name"
                                        tabindex="12"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                        :maxlength="240"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                        v-if="
                            (company_type === 1 && lease_company.field_key !== '') ||
                            (debtor_in_ar_count === 0 &&
                                !company_existed &&
                                company_type === 0 &&
                                create_new_company_btn)
                        "
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant E-mail:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <span class="form-input-text">
                                        <a
                                            v-if="
                                                lease_tenant_email !== '' && company_type === 1 && !company_is_temp_flag
                                            "
                                            v-for="(emailArrData, emailArrIndex) in emailArr"
                                            :key="emailArrIndex"
                                            :href="'mailto:' + emailArrData"
                                        >
                                            {{ emailArrData }}
                                        </a>
                                    </span>
                                    <cirrus-email-centralisation
                                        v-model="email_centralisation_setting"
                                        :contact_table_name="isLeaseFormLive() ? 'company' : 'temp_company'"
                                        :contact_table_id="lease_company_id"
                                        v-if="
                                            email_cen_setup &&
                                            edit_form &&
                                            lease_tenant_email !== '' &&
                                            company_type === 1 &&
                                            !company_is_temp_flag &&
                                            page_form_type !== 'client-new-page'
                                        "
                                    ></cirrus-email-centralisation>
                                    <span
                                        class="form-input-text"
                                        v-if="lease_tenant_email !== '' && company_type === 1 && company_is_temp_flag"
                                        >{{ lease_tenant_email }}</span
                                    >
                                    <!--                    <a v-if="lease_tenant_email===''" href="../framework/index.php?command=company&module=companies&companyID=">Add Email Address</a>-->
                                    <cirrus-input
                                        :size="'60'"
                                        v-if="debtor_in_ar_count === 0 && company_type === 0"
                                        :id="'lease_tenant_email'"
                                        v-model="lease_tenant_email"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                    <a
                                        v-if="
                                            lease_tenant_email === '' &&
                                            company_type === 1 &&
                                            page_form_type !== 'client-new-page' &&
                                            !company_is_temp_flag
                                        "
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        ADD EMAIL ADDRESS
                                    </a>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--Tenant Address and Tenant State-->
                <v-row
                    class="form-row no-gutters"
                    v-if="
                        (!company_existed && company_type === 0 && create_new_company_btn) ||
                        (company_existed && company_type === 1)
                    "
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Address:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        maxlength="50"
                                        :id="'lease_tenant_address_1'"
                                        v-model="lease_tenant_address_1"
                                        tabindex="13"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                    <br v-if="lease_tenant_address_2 !== '' || edit_form" />
                                    <cirrus-input
                                        v-if="lease_tenant_address_2 !== '' || edit_form"
                                        :rules_length="50"
                                        tabindex="14"
                                        :id="'lease_tenant_address_2'"
                                        v-model="lease_tenant_address_2"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--Tenant Suburb and Tenant Postcode-->

                <v-row
                    class="form-row no-gutters"
                    v-if="
                        (!company_existed && company_type === 0 && create_new_company_btn) ||
                        (company_existed && company_type === 1)
                    "
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant {{ properCase(suburb_label) }}:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td
                                    id="lease-suburb"
                                    v-if="lease_tenant_country.field_key == 'AU'"
                                >
                                    <v-combobox
                                        tabindex="14"
                                        v-if="edit_form"
                                        v-model="lease_tenant_suburb"
                                        :maxlength="40"
                                        :items="suburb_list_filtered"
                                        item-value="label"
                                        item-text="label"
                                        @change="suburbSelected(lease_tenant_suburb)"
                                        auto-select-first
                                        hide-selected
                                        persistent-hint
                                        append-icon
                                        :search-input.sync="searchLeaseTenantSuburb"
                                        :hide-no-data="!searchLeaseTenantSuburb"
                                        dense
                                        ref="refLeaseTenantSuburb"
                                        flat
                                    >
                                        <template v-slot:no-data>
                                            <v-list-item>
                                                <v-chip
                                                    v-model="searchLeaseTenantSuburb"
                                                    small
                                                >
                                                    {{ searchLeaseTenantSuburb }}
                                                </v-chip>
                                            </v-list-item>
                                        </template>
                                    </v-combobox>
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{ lease_tenant_suburb }}</span
                                    >
                                </td>
                                <td
                                    id="lease-suburb-"
                                    v-else
                                >
                                    <cirrus-input
                                        :size="'40'"
                                        :id="'lease_tenant_suburb'"
                                        v-model="lease_tenant_suburb"
                                        tabindex="12"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                        :maxlength="40"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row no-gutters"
                    v-if="
                        ((!company_existed && company_type === 0 && create_new_company_btn) ||
                            (company_existed && company_type === 1)) &&
                        cdf_for_tenant_address.display_state === true
                    "
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant State:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <multiselect
                                        tabindex="15"
                                        @input="tenantStateChanged()"
                                        v-if="edit_form"
                                        v-model="lease_tenant_state"
                                        :options="getDDCountryStates(lease_tenant_country.field_key)"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-300"
                                        group-label="language"
                                        placeholder="Select a state"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <span
                                        v-if="!edit_form"
                                        class="form-input-text"
                                        >{{ lease_tenant_state.field_value }}</span
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row no-gutters"
                    v-if="
                        (!company_existed && company_type === 0 && create_new_company_btn) ||
                        (company_existed && company_type === 1)
                    "
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Postcode:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td
                                    id="lease-postcode"
                                    v-if="lease_tenant_country.field_key === 'AU'"
                                >
                                    <v-combobox
                                        tabindex="16"
                                        v-if="edit_form"
                                        v-model="lease_tenant_post_code"
                                        :maxlength="40"
                                        :items="postcode_list_filtered"
                                        item-value="label"
                                        item-text="label"
                                        @change="postcodeSelected(lease_tenant_post_code)"
                                        auto-select-first
                                        hide-selected
                                        persistent-hint
                                        append-icon
                                        :search-input.sync="searchLeaseTenantPostcode"
                                        :hide-no-data="!searchLeaseTenantPostcode"
                                        dense
                                        ref="refLeaseTenantSuburb"
                                        flat
                                    >
                                        <template v-slot:no-data>
                                            <v-list-item>
                                                <v-chip
                                                    v-model="searchLeaseTenantPostcode"
                                                    small
                                                >
                                                    {{ searchLeaseTenantPostcode }}
                                                </v-chip>
                                            </v-list-item>
                                        </template>
                                    </v-combobox>
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{ lease_tenant_post_code }}</span
                                    >
                                </td>
                                <td
                                    id="lease-postcode-"
                                    v-else
                                >
                                    <cirrus-input
                                        :size="'40'"
                                        :id="'lease_tenant_post_code'"
                                        v-model="lease_tenant_post_code"
                                        tabindex="12"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                        :maxlength="10"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row no-gutters"
                    v-if="
                        (!company_existed && company_type === 0 && create_new_company_btn) ||
                        (company_existed && company_type === 1)
                    "
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Country:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <multiselect
                                        tabindex="17"
                                        @input="triggerCountryChange('tenant')"
                                        v-if="edit_form"
                                        v-model="lease_tenant_country"
                                        :options="dd_country_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-300"
                                        group-label="language"
                                        placeholder="Select a country"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <span
                                        v-if="!edit_form"
                                        class="form-input-text"
                                        >{{ lease_tenant_country.field_value }}</span
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row no-gutters"
                    v-if="edit_form && company_type === 1"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tbody>
                                <tr class="subHeader">
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <b>Save Company Details</b>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <sui-checkbox
                                            v-model="save_company_details_flag"
                                            label="This will update the details for the company as well."
                                            tabindex="19"
                                        />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </v-col>
                </v-row>
            </div>
            <!-- FOR PRINTING -->
            <div
                class="page-form"
                v-if="formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
            >
                <!-- Comany -->
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Company:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td v-if="new_lease && company_type === 1 && edit_form">
                                    <multiselect
                                        tabindex="0"
                                        v-model="lease_company"
                                        :options="company_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        :custom-label="nameWithDash"
                                        group-label="language"
                                        placeholder="Select a company"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <v-btn
                                        :loading="company_list.length <= 0"
                                        depressed
                                        elevation="0"
                                        small
                                        color="normal"
                                        height="30"
                                        v-on:click="loadCompanyDetails()"
                                    >
                                        <v-icon>arrow_right</v-icon>
                                    </v-btn>
                                    <a
                                        v-if="lease_company.field_key !== ''"
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon
                                            elevation="0"
                                            icon
                                            small
                                            height="30"
                                            >business
                                        </v-icon>
                                    </a>
                                </td>
                                <td v-if="new_lease && company_type === 1 && !edit_form">
                                    <span class="form-input-text"
                                        >{{ lease_company.field_key }} - {{ lease_company.field_value }}</span
                                    >
                                    <a
                                        v-if="lease_company.field_key !== ''"
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon
                                            elevation="0"
                                            icon
                                            small
                                            height="30"
                                            >business
                                        </v-icon>
                                    </a>
                                </td>
                                <td v-if="!new_lease || (new_lease && !company_existed && company_type === 0)">
                                    <strong v-if="!new_lease"
                                        ><span class="form-input-text"
                                            >{{ lease_company_code }} - {{ lease_company_name }}</span
                                        ></strong
                                    >
                                    <v-btn
                                        v-if="!new_lease"
                                        depressed
                                        elevation="0"
                                        icon
                                        x-small
                                        v-on:click="goToShortcut('company')"
                                        data-tooltip="Go to company"
                                    >
                                        <v-icon
                                            v-if="
                                                !formSectionReadOnly(
                                                    pm_lease_form_read_only,
                                                    form_type,
                                                    form_section,
                                                    is_inactive,
                                                )
                                            "
                                            >business
                                        </v-icon>
                                    </v-btn>
                                    <cirrus-input
                                        v-if="new_lease"
                                        :id="'company'"
                                        v-model="lease_company_code"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!-- Tenant Name -->
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Name:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        :size="'40'"
                                        :id="'lease_tenant_name'"
                                        v-model="lease_tenant_name"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                        :maxlenght="240"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--Tenant Address -->
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="6"
                        xl="6"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Address:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <cirrus-input
                                        maxlength="37"
                                        :id="'lease_tenant_address_1'"
                                        v-model="lease_tenant_address_1"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                    <br v-if="lease_tenant_address_2 !== '' || edit_form" />
                                    <cirrus-input
                                        v-if="lease_tenant_address_2 !== '' || edit_form"
                                        :rules_length="37"
                                        :maxlength="37"
                                        :id="'lease_tenant_address_2'"
                                        v-model="lease_tenant_address_2"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!-- Tenant Suburb -->
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <!-- update for UK Post Town-->
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant {{ properCase(suburb_label) }}:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <v-combobox
                                        v-if="edit_form"
                                        v-model="lease_tenant_suburb"
                                        :maxlength="40"
                                        :items="suburb_list_filtered"
                                        item-value="label"
                                        item-text="label"
                                        @change="suburbSelected(lease_tenant_suburb)"
                                        auto-select-first
                                        hide-selected
                                        persistent-hint
                                        append-icon
                                        :search-input.sync="searchLeaseTenantSuburb"
                                        :hide-no-data="!searchLeaseTenantSuburb"
                                        dense
                                        ref="refLeaseTenantSuburb"
                                        flat
                                    >
                                        <template v-slot:no-data>
                                            <v-list-item>
                                                <v-chip
                                                    v-model="searchLeaseTenantSuburb"
                                                    small
                                                >
                                                    {{ searchLeaseTenantSuburb }}
                                                </v-chip>
                                            </v-list-item>
                                        </template>
                                    </v-combobox>
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{ lease_tenant_suburb }}</span
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!-- Tenant State -->
                <v-row
                    class="form-row no-gutters"
                    v-if="cdf_for_tenant_address.display_state === true"
                >
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant State:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <span
                                        v-if="!edit_form"
                                        class="form-input-text"
                                        >{{ lease_tenant_state.field_value }}</span
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!-- Tenant Post Code -->
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Postcode:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <v-combobox
                                        v-if="edit_form"
                                        v-model="lease_tenant_post_code"
                                        :maxlength="40"
                                        :items="postcode_list_filtered"
                                        item-value="label"
                                        item-text="label"
                                        @change="postcodeSelected(lease_tenant_post_code)"
                                        auto-select-first
                                        hide-selected
                                        persistent-hint
                                        append-icon
                                        :search-input.sync="searchLeaseTenantPostcode"
                                        :hide-no-data="!searchLeaseTenantPostcode"
                                        dense
                                        ref="refLeaseTenantSuburb"
                                        flat
                                    >
                                        <template v-slot:no-data>
                                            <v-list-item>
                                                <v-chip
                                                    v-model="searchLeaseTenantPostcode"
                                                    small
                                                >
                                                    {{ searchLeaseTenantPostcode }}
                                                </v-chip>
                                            </v-list-item>
                                        </template>
                                    </v-combobox>
                                    <span
                                        class="form-input-text"
                                        v-if="!edit_form"
                                        >{{ lease_tenant_post_code }}</span
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!-- Tenant Country -->
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant Country:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                >
                                    *
                                </td>
                                <td>
                                    <multiselect
                                        tabindex="0"
                                        v-if="edit_form"
                                        v-model="lease_tenant_country"
                                        :options="dd_country_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-300"
                                        group-label="language"
                                        placeholder="Select a country"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <span
                                        v-if="!edit_form"
                                        class="form-input-text"
                                        >{{ lease_tenant_country.field_value }}</span
                                    >
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!-- Tenant E-mail -->
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant E-mail:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <span class="form-input-text"
                                        ><a
                                            v-if="
                                                lease_tenant_email !== '' && company_type === 1 && !company_is_temp_flag
                                            "
                                            :href="
                                                '../framework/index.php?command=companySummary&action=view&module=companies&companyID=' +
                                                lease_company.field_key
                                            "
                                            >{{ lease_tenant_email }}</a
                                        ></span
                                    >
                                    <span
                                        class="form-input-text"
                                        v-if="lease_tenant_email !== '' && company_type === 1 && company_is_temp_flag"
                                        >{{ lease_tenant_email }}</span
                                    >
                                    <cirrus-input
                                        :size="'60'"
                                        v-if="new_lease && company_type === 0"
                                        :id="'lease_tenant_email'"
                                        v-model="lease_tenant_email"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <v-row class="form-row no-gutters">
                    <v-col
                        cols="12"
                        xs="12"
                        sm="12"
                        md="12"
                        lg="12"
                        xl="12"
                    >
                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                            <tr>
                                <td
                                    class="title"
                                    align="right"
                                >
                                    <strong>Tenant {{ tenant_country_defaults.business_label }}:</strong>
                                </td>
                                <td
                                    class="required"
                                    v-if="edit_form"
                                ></td>
                                <td>
                                    <span class="form-input-text"
                                        ><a
                                            v-if="
                                                lease_tenant_abn !== '' && company_type === 1 && !company_is_temp_flag
                                            "
                                            :href="
                                                '../framework/index.php?command=companySummary&action=view&module=companies&companyID=' +
                                                lease_company.field_key
                                            "
                                            ><span v-if="lease_tenant_abn && lease_tenant_abn != ''">{{
                                                tenant_country_defaults.business_prefix
                                            }}</span
                                            >{{ lease_tenant_abn }}</a
                                        ></span
                                    >
                                    <span
                                        class="form-input-text"
                                        v-if="lease_tenant_abn !== '' && company_type === 1 && company_is_temp_flag"
                                        ><span v-if="lease_tenant_abn && lease_tenant_abn != ''">{{
                                            tenant_country_defaults.business_prefix
                                        }}</span
                                        >{{ lease_tenant_abn }}</span
                                    >
                                    <span
                                        class="business-prefix-vue"
                                        v-if="new_lease && company_type === 0"
                                        v-show="tenant_country_defaults.business_prefix"
                                        >{{ tenant_country_defaults.business_prefix }}</span
                                    >
                                    <cirrus-input
                                        :size="'60'"
                                        v-if="new_lease && company_type === 0"
                                        :id="'lease_tenant_abn'"
                                        v-model="lease_tenant_abn"
                                        :edit_form="edit_form"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
            </div>
        </div>
        <!-- Mailing Details tabindex range 21 - 30-->
        <v-card
            id="mailingDetailsSection"
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Mailing Details:</h6>
                &nbsp

                <span
                    class="title form-input-text"
                    v-show="read_only || !edit_form"
                    v-if="lease_mail_setting_address_same_as_above === 0"
                    ><strong>Same as above</strong></span
                >
                <span
                    class="title form-input-text"
                    v-show="read_only || !edit_form"
                    v-if="lease_mail_setting_address_same_as_above === 1"
                    ><strong>Not same as above</strong></span
                >
                <v-btn-toggle
                    class="form-toggle"
                    v-show="!read_only && edit_form"
                    v-model="lease_mail_setting_address_same_as_above"
                    mandatory
                >
                    <v-btn
                        x-small
                        text
                        :disabled="!edit_form"
                    >
                        Yes
                    </v-btn>
                    <v-btn
                        x-small
                        text
                        :disabled="!edit_form"
                    >
                        No
                    </v-btn>
                </v-btn-toggle>
                &nbsp;&nbsp;
                <div class="tooltip">
                    <v-icon
                        small
                        class="titleHeader"
                        >help
                    </v-icon>
                    <span
                        class="tooltiptext"
                        style="width: 300px !important; text-align: left"
                    >
                        If a separating mailing address is entered here all correspondence will be addressed to the
                        mailing address, all emails from cirrus8 will still go to the email address on the tenant
                        company shown in tenant details above.
                    </span>
                </div>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <v-row
                class="form-row"
                v-if="lease_mail_setting_address_same_as_above === 1"
            >
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-subheader"
                    ><strong>Mailing Setting</strong></v-col
                >
            </v-row>
            <!--Atten and country-->
            <v-row
                class="form-row no-gutters"
                v-if="lease_mail_setting_address_same_as_above === 1"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>For attention of:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <cirrus-input
                                    tabindex="21"
                                    :size="'40'"
                                    :id="'lease_mail_setting_for_attention_of'"
                                    v-model="lease_mail_setting_for_attention_of"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Country:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <multiselect
                                    tabindex="25"
                                    @input="triggerCountryChange('mail')"
                                    v-if="edit_form"
                                    v-model="lease_mail_setting_country"
                                    :options="dd_country_list"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-300"
                                    group-label="language"
                                    placeholder="Select a country"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                                <span v-if="!edit_form">{{ lease_mail_setting_country.field_value }}</span>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--Atten and country-->
            <v-row
                class="form-row no-gutters"
                v-if="lease_mail_setting_address_same_as_above === 1"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Lease Name:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <span class="form-input-text">{{ lease_name }}</span>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table
                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                        v-if="mail_country_defaults.display_state === true"
                    >
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Tenant State:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <multiselect
                                    tabindex="26"
                                    @input="mailStateChanged()"
                                    v-if="edit_form"
                                    v-model="lease_mail_setting_state"
                                    :options="getDDCountryStates(lease_mail_setting_country.field_key)"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left dropdown-300"
                                    group-label="language"
                                    placeholder="Select a state"
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                                <span
                                    v-if="!edit_form"
                                    class="form-input-text"
                                    >{{ lease_mail_setting_state.field_value }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--Mailing address and postcode-->
            <v-row
                class="form-row no-gutters"
                v-if="lease_mail_setting_address_same_as_above === 1"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Mailing Address:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <cirrus-input
                                    tabindex="22"
                                    :size="'40'"
                                    :id="'lease_mail_setting_address_1'"
                                    v-model="lease_mail_setting_address_1"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                                <br />
                                <cirrus-input
                                    tabindex="22"
                                    :size="'40'"
                                    :id="'lease_mail_setting_address_2'"
                                    v-model="lease_mail_setting_address_2"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Tenant Postcode:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td
                                id="lease-postcode-mail"
                                v-if="lease_mail_setting_country.field_key == 'AU'"
                            >
                                <v-combobox
                                    tabindex="26"
                                    v-if="edit_form"
                                    v-model="lease_mail_setting_post_code"
                                    :maxlength="40"
                                    :items="postcode_mail_list_filtered"
                                    item-value="label"
                                    item-text="label"
                                    @change="postcodeMailSelected(lease_mail_setting_post_code)"
                                    auto-select-first
                                    hide-selected
                                    persistent-hint
                                    append-icon
                                    :search-input.sync="searchLeaseTenantMailPostcode"
                                    :hide-no-data="!searchLeaseTenantMailPostcode"
                                    dense
                                    ref="refLeaseTenantSuburb"
                                    flat
                                >
                                    <template v-slot:no-data>
                                        <v-list-item>
                                            <v-chip
                                                v-model="searchLeaseTenantMailPostcode"
                                                small
                                            >
                                                {{ searchLeaseTenantMailPostcode }}
                                            </v-chip>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                                <span
                                    class="form-input-text"
                                    v-if="!edit_form"
                                    >{{ lease_mail_setting_post_code }}</span
                                >
                            </td>
                            <td
                                id="lease-postcode-mail-"
                                v-else
                            >
                                <cirrus-input
                                    tabindex="22"
                                    :size="'40'"
                                    :id="'lease_mail_setting_post_code'"
                                    v-model="lease_mail_setting_post_code"
                                    :edit_form="edit_form"
                                    :maxlength="10"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Tenant {{ properCase(suburb_label) }}:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td
                                id="lease-suburb-mail"
                                v-if="lease_mail_setting_country.field_key == 'AU'"
                            >
                                <v-combobox
                                    tabindex="24"
                                    v-if="edit_form"
                                    v-model="lease_mail_setting_suburb"
                                    :maxlength="40"
                                    :items="suburb_mail_list_filtered"
                                    item-value="label"
                                    item-text="label"
                                    @change="suburbMailSelected(lease_mail_setting_suburb)"
                                    auto-select-first
                                    hide-selected
                                    persistent-hint
                                    append-icon
                                    :search-input.sync="searchLeaseTenantMailSuburb"
                                    :hide-no-data="!searchLeaseTenantMailSuburb"
                                    dense
                                    ref="refLeaseTenantMailSuburb"
                                    flat
                                >
                                    <template v-slot:no-data>
                                        <v-list-item>
                                            <v-chip
                                                v-model="searchLeaseTenantMailSuburb"
                                                small
                                            >
                                                {{ searchLeaseTenantMailSuburb }}
                                            </v-chip>
                                        </v-list-item>
                                    </template>
                                </v-combobox>
                                <span
                                    class="form-input-text"
                                    v-if="!edit_form"
                                    >{{ lease_mail_setting_suburb }}</span
                                >
                            </td>
                            <td v-else>
                                <cirrus-input
                                    tabindex="22"
                                    :size="'40'"
                                    :id="'lease_mail_setting_suburb'"
                                    v-model="lease_mail_setting_suburb"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
        </div>
        <!-- Lease Details tabindex range 31 - 40-->
        <div
            class="page-form"
            v-if="
                !loading_setting &&
                !read_only &&
                !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
            "
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-subheader"
                    ><strong>Lease Details</strong></v-col
                >
            </v-row>

            <!--Comm date and bond property-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Commencement Date:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <cirrus-icon-date-picker
                                    tabindex="31"
                                    :size="'40'"
                                    :id="'lease_commencement_date'"
                                    v-model="lease_commencement_date"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                                <v-btn
                                    v-if="leaseTermLogIsOn && isLeaseFormLive() && !new_lease"
                                    x-small
                                    icon
                                    data-tooltip="Lease Term Log"
                                    @click="showLeaseTermLog"
                                >
                                    <v-icon>mdi-history</v-icon>
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                    v-if="!new_lease"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Bond/Deposit Property:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td style="width: 300px">
                                <cirrus-single-select-v2
                                    v-if="edit_form"
                                    v-model="lease_bond_deposit_property"
                                    :options="dd_bond_deposit_property_list"
                                    ref="refPropertyType"
                                    trackBy="field_key"
                                    label="field_key_w_value"
                                    return="field_key"
                                    placeholder="Select a property"
                                />
                                <span
                                    v-if="!edit_form && lease_bond_deposit_property !== ''"
                                    class="form-input-text"
                                    >{{ lease_bond_deposit_property }} -
                                    {{
                                        getDropdownName(lease_bond_deposit_property, dd_bond_deposit_property_list)
                                    }}</span
                                >
                            </td>
                            <td>
                                <a
                                    v-if="
                                        lease_bond_deposit_property !== '' &&
                                        !(
                                            this.user_type !== 'A' &&
                                            getLedgerFlag(
                                                lease_bond_deposit_property,
                                                dd_bond_deposit_property_list,
                                            ) === '1'
                                        )
                                    "
                                    v-on:click="goToShortcut('property', lease_bond_deposit_property)"
                                    data-tooltip="Go to property"
                                >
                                    <v-icon
                                        elevation="0"
                                        icon
                                        small
                                        height="30"
                                        >business
                                    </v-icon>
                                </a>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>

            <!--exp date and bond lease-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Expiry Date:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <cirrus-icon-date-picker
                                    tabindex="32"
                                    style="float: left"
                                    :size="'40'"
                                    :id="'lease_expiry_date'"
                                    v-model="lease_expiry_date"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                                <v-btn
                                    v-if="hasLeaseOptionsAndEditFormAndAutoDiariseDisabled"
                                    class="form-text-button"
                                    text
                                    depressed
                                    elevation="0"
                                    small
                                    @click="diariseExpiryDate()"
                                >
                                    <v-icon x-small>arrow_back_ios</v-icon>
                                    diarise
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                    v-if="!new_lease"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Bond/Deposit Lease:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td style="width: 300px">
                                <div v-if="lease_bond_deposit_property !== ''">
                                    <cirrus-single-select-v2
                                        :key="refresh_index"
                                        v-if="edit_form"
                                        v-model="lease_bond_deposit_lease"
                                        :options="lease_bond_deposit_lease_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_key_w_value"
                                        return="field_key"
                                        placeholder="Select a lease"
                                    />
                                    <span
                                        v-if="!edit_form && lease_bond_deposit_lease !== ''"
                                        class="form-input-text"
                                        >{{ lease_bond_deposit_lease }} -
                                        {{
                                            getDropdownName(lease_bond_deposit_lease, lease_bond_deposit_lease_list)
                                        }}</span
                                    >
                                </div>
                            </td>
                            <td>
                                <div v-if="lease_bond_deposit_property !== ''">
                                    <a
                                        v-if="
                                            typeof lease_bond_deposit_lease === 'string' &&
                                            lease_bond_deposit_lease !== '' &&
                                            lease_bond_deposit_property !== '' &&
                                            !(
                                                this.user_type !== 'A' &&
                                                getLedgerFlag(
                                                    lease_bond_deposit_property,
                                                    dd_bond_deposit_property_list,
                                                ) === '1'
                                            )
                                        "
                                        v-on:click="
                                            goToShortcut('lease', lease_bond_deposit_property, lease_bond_deposit_lease)
                                        "
                                        data-tooltip="Go to lease"
                                    >
                                        <v-icon
                                            elevation="0"
                                            icon
                                            small
                                            height="30"
                                            >business
                                        </v-icon>
                                    </a>
                                    <span
                                        v-if="
                                            edit_form &&
                                            (typeof lease_bond_deposit_lease === 'object' ||
                                                lease_bond_deposit_lease === '') &&
                                            lease_bond_deposit_property !== ''
                                        "
                                        class="form-input-text"
                                        ><v-btn
                                            x-small
                                            class="mt-1"
                                            depressed
                                            @click="showNewBondLease()"
                                            >Add Bond/Deposit Lease</v-btn
                                        ></span
                                    >
                                </div>
                                <div v-if="lease_bond_deposit_property === '' && edit_form">
                                    <span class="form-input-text"
                                        ><i>Please select a Bond/Deposit Property first.</i></span
                                    >
                                </div>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--lease term-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Lease Term:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <span
                                    class="form-input-text"
                                    v-html="lease_term"
                                ></span>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                    v-if="lease_inside_act_show"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Inside the Act:</strong>
                            </td>
                            <td class="required"></td>
                            <td>
                                <span
                                    class="title form-input-text"
                                    v-show="read_only || !edit_form"
                                    v-if="lease_inside_act === 0"
                                    >Inside</span
                                >
                                <span
                                    class="title form-input-text"
                                    v-show="read_only || !edit_form"
                                    v-if="lease_inside_act === 1"
                                    >Outside</span
                                >
                                <v-btn-toggle
                                    class="form-toggle"
                                    v-show="!read_only && edit_form"
                                    v-model="lease_inside_act"
                                    mandatory
                                >
                                    <v-btn
                                        x-small
                                        text
                                        :disabled="!edit_form"
                                    >
                                        Inside
                                    </v-btn>
                                    <v-btn
                                        x-small
                                        text
                                        :disabled="!edit_form"
                                    >
                                        Outside
                                    </v-btn>
                                </v-btn-toggle>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--lease option-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Lease Option:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <cirrus-input
                                    tabindex="33"
                                    :maxlength="'18'"
                                    :id="'lease_option'"
                                    v-model="lease_option"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                                <v-btn
                                    v-if="hasEditModeAndLeaseExpiryHasOptionsAndAutoDiariseDisabled"
                                    class="form-text-button"
                                    text
                                    depressed
                                    elevation="0"
                                    small
                                    @click="diariseLeaseOption()"
                                >
                                    <v-icon x-small>arrow_back_ios</v-icon>
                                    diarise
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Status Description:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <cirrus-text-area
                                    tabindex="37"
                                    :rows="'3'"
                                    :cols="'50'"
                                    :id="'lease_status_description'"
                                    v-model="lease_status_description"
                                    :edit_form="edit_form"
                                    maxlength="100"
                                    :error_msg="error_msg"
                                ></cirrus-text-area>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
        </div>

        <!-- FOR PRINTING -->
        <div
            class="page-form"
            v-if="formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-subheader"
                    ><strong>Lease Details</strong></v-col
                >
            </v-row>

            <!--Comm date-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Commencement Date:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    :id="'lease_commencement_date'"
                                    v-model="lease_commencement_date"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                                <v-btn
                                    v-if="leaseTermLogIsOn && isLeaseFormLive() && !new_lease"
                                    x-small
                                    icon
                                    data-tooltip="Lease Term Log"
                                    @click="showLeaseTermLog"
                                >
                                    <v-icon>mdi-history</v-icon>
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>

            <!--expiry date-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Expiry Date:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <cirrus-icon-date-picker
                                    style="float: left"
                                    :size="'40'"
                                    :id="'lease_expiry_date'"
                                    v-model="lease_expiry_date"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                                <v-btn
                                    v-if="hasLeaseOptionsAndEditFormAndAutoDiariseDisabled"
                                    class="form-text-button"
                                    text
                                    depressed
                                    elevation="0"
                                    small
                                    @click="diariseExpiryDate()"
                                >
                                    <v-icon x-small>arrow_back_ios</v-icon>
                                    diarise
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--lease term-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Lease Term:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <span
                                    class="form-input-text"
                                    v-html="lease_term"
                                ></span>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                    v-if="lease_inside_act_show"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Inside the Act:</strong>
                            </td>
                            <td class="required"></td>
                            <td>
                                <span
                                    class="title form-input-text"
                                    v-show="read_only || !edit_form"
                                    v-if="lease_inside_act === 0"
                                    >Inside</span
                                >
                                <span
                                    class="title form-input-text"
                                    v-show="read_only || !edit_form"
                                    v-if="lease_inside_act === 1"
                                    >Outside</span
                                >
                                <v-btn-toggle
                                    class="form-toggle"
                                    v-show="!read_only && edit_form"
                                    v-model="lease_inside_act"
                                    mandatory
                                >
                                    <v-btn
                                        x-small
                                        text
                                        :disabled="!edit_form"
                                    >
                                        Inside
                                    </v-btn>
                                    <v-btn
                                        x-small
                                        text
                                        :disabled="!edit_form"
                                    >
                                        Outside
                                    </v-btn>
                                </v-btn-toggle>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--lease option-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Lease Option:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <cirrus-input
                                    :size="'40'"
                                    :id="'lease_option'"
                                    v-model="lease_option"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                                <v-btn
                                    v-if="hasEditModeAndLeaseExpiryHasOptionsAndAutoDiariseDisabled"
                                    class="form-text-button"
                                    text
                                    depressed
                                    elevation="0"
                                    small
                                    @click="diariseLeaseOption()"
                                >
                                    <v-icon x-small>arrow_back_ios</v-icon>
                                    diarise
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--status description-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Status Description:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <cirrus-text-area
                                    :rows="'3'"
                                    :cols="'50'"
                                    :id="'lease_status_description'"
                                    v-model="lease_status_description"
                                    :edit_form="edit_form"
                                    maxlength="100"
                                    :error_msg="error_msg"
                                ></cirrus-text-area>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--bond/deposit property-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Bond/Deposit Property:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td style="width: 300px">
                                <cirrus-single-select-v2
                                    v-if="edit_form"
                                    v-model="lease_bond_deposit_property"
                                    :options="dd_bond_deposit_property_list"
                                    ref="refPropertyType"
                                    trackBy="field_key"
                                    label="field_key_w_value"
                                    return="field_key"
                                    placeholder="Select a property"
                                />
                                <span
                                    v-if="!edit_form && lease_bond_deposit_property !== ''"
                                    class="form-input-text"
                                    >{{ lease_bond_deposit_property }} -
                                    {{
                                        getDropdownName(lease_bond_deposit_property, dd_bond_deposit_property_list)
                                    }}</span
                                >
                            </td>
                            <td>
                                <a
                                    v-if="
                                        lease_bond_deposit_property !== '' &&
                                        !(
                                            this.user_type !== 'A' &&
                                            getLedgerFlag(
                                                lease_bond_deposit_property,
                                                dd_bond_deposit_property_list,
                                            ) === '1'
                                        )
                                    "
                                    v-on:click="goToShortcut('property', lease_bond_deposit_property)"
                                    data-tooltip="Go to property"
                                >
                                    <v-icon
                                        elevation="0"
                                        icon
                                        small
                                        height="30"
                                        >business
                                    </v-icon>
                                </a>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--bond/deposit lease-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Bond/Deposit Lease:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td style="width: 300px">
                                <div v-if="lease_bond_deposit_property !== ''">
                                    <cirrus-single-select-v2
                                        :key="refresh_index"
                                        v-if="edit_form"
                                        v-model="lease_bond_deposit_lease"
                                        :options="lease_bond_deposit_lease_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_key_w_value"
                                        return="field_key"
                                        placeholder="Select a lease"
                                    />
                                    <span
                                        v-if="!edit_form && lease_bond_deposit_lease !== ''"
                                        class="form-input-text"
                                        >{{ lease_bond_deposit_lease }} -
                                        {{
                                            getDropdownName(lease_bond_deposit_lease, lease_bond_deposit_lease_list)
                                        }}</span
                                    >
                                </div>
                            </td>
                            <td>
                                <div v-if="lease_bond_deposit_property !== ''">
                                    <a
                                        v-if="
                                            typeof lease_bond_deposit_lease === 'string' &&
                                            lease_bond_deposit_lease !== '' &&
                                            lease_bond_deposit_property !== '' &&
                                            !(
                                                this.user_type !== 'A' &&
                                                getLedgerFlag(
                                                    lease_bond_deposit_property,
                                                    dd_bond_deposit_property_list,
                                                ) === '1'
                                            )
                                        "
                                        v-on:click="
                                            goToShortcut('lease', lease_bond_deposit_property, lease_bond_deposit_lease)
                                        "
                                        data-tooltip="Go to lease"
                                    >
                                        <v-icon
                                            elevation="0"
                                            icon
                                            small
                                            height="30"
                                            >business
                                        </v-icon>
                                    </a>
                                    <span
                                        v-if="
                                            edit_form &&
                                            (typeof lease_bond_deposit_lease === 'object' ||
                                                lease_bond_deposit_lease === '') &&
                                            lease_bond_deposit_property !== ''
                                        "
                                        class="form-input-text"
                                        ><v-btn
                                            x-small
                                            class="mt-1"
                                            depressed
                                            @click="showNewBondLease()"
                                            >Add Bond/Deposit Lease</v-btn
                                        ></span
                                    >
                                </div>
                                <div v-if="lease_bond_deposit_property === '' && edit_form">
                                    <span class="form-input-text"
                                        ><i>Please select a Bond/Deposit Property first.</i></span
                                    >
                                </div>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
        </div>

        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <!-- Interest on Arrears tabindex range 41 - 50-->
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-subheader"
                    ><strong>Interest on Arrears</strong></v-col
                >
            </v-row>

            <!--charge interest-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Charge Interest:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <span
                                    class="form-input-text"
                                    v-show="read_only || !edit_form"
                                    v-if="lease_charge_interest === 0"
                                    ><strong>Yes</strong></span
                                >
                                <span
                                    class="form-input-text"
                                    v-show="read_only || !edit_form"
                                    v-if="lease_charge_interest === 1"
                                    ><strong>No</strong></span
                                >
                                <v-btn-toggle
                                    class="form-toggle"
                                    v-show="!read_only && edit_form"
                                    v-model="lease_charge_interest"
                                    mandatory
                                >
                                    <v-btn
                                        small
                                        text
                                        :disabled="!edit_form"
                                    >
                                        Yes
                                    </v-btn>
                                    <v-btn
                                        small
                                        text
                                        :disabled="!edit_form"
                                    >
                                        No
                                    </v-btn>
                                </v-btn-toggle>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--description and grace period-->
            <v-row
                class="form-row no-gutters"
                v-if="lease_charge_interest === 0"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Description:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <cirrus-input
                                    tabindex="41"
                                    :size="'40'"
                                    :id="'lease_charge_interest_description'"
                                    v-model="lease_charge_interest_description"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Grace Period:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <cirrus-input
                                    tabindex="44"
                                    inputFormat="wholeNumberOnly"
                                    style="float: left"
                                    :size="'6'"
                                    :id="'lease_charge_interest_grace_period'"
                                    v-model="lease_charge_interest_grace_period"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                                &nbsp <span class="form-input-text">days</span>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--interest account and fixed interest rate-->
            <v-row
                class="form-row no-gutters"
                v-if="lease_charge_interest === 0"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Interest Account:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <multiselect
                                    tabindex="42"
                                    v-if="edit_form"
                                    v-model="lease_charge_interest_account"
                                    :options="dd_account_income_grouped_list"
                                    group-values="field_group_values"
                                    :groupSelect="false"
                                    group-label="field_group_names"
                                    :group-select="true"
                                    class="vue-select2 dropdown-left dropdown-400"
                                    :custom-label="nameWithDash"
                                    placeholder="Please select ..."
                                    track-by="field_key"
                                    label="field_value"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                                <span v-if="!edit_form && lease_charge_interest_account.field_key !== ''"
                                    >{{ lease_charge_interest_account.field_key }}
                                    {{ lease_charge_interest_account.field_value }}</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Fixed Interest Rate:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <cirrus-input
                                    tabindex="45"
                                    inputFormat="percentage"
                                    style="float: left"
                                    :size="'6'"
                                    :id="'lease_charge_interest_fixed_interest_rate'"
                                    v-model="lease_charge_interest_fixed_interest_rate"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--select account -->
            <v-row
                class="form-row no-gutters"
                v-if="lease_charge_interest === 0"
            >
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong></strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            >
                                *
                            </td>
                            <td>
                                <v-btn
                                    tabindex="43"
                                    depressed
                                    small
                                    @click="show_interest_on_arrears = true"
                                    >{{ lease_charge_interest_incur_account.length }} Accounts (incur interest) selected
                                </v-btn>
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
        </div>
        <!-- Optional Lease Details tabindex range 51 - 60-->
        <div
            class="page-form"
            v-if="
                !loading_setting &&
                !read_only &&
                !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
            "
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-subheader"
                    ><strong>Optional Lease Details</strong></v-col
                >
            </v-row>

            <!--Bad debt and retail category-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                    v-if="!new_lease"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Bad Debt Provision:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <sui-checkbox
                                    tabindex="51"
                                    v-if="edit_form"
                                    v-model="lease_bad_debt_provision"
                                />
                                <span
                                    class="form-input-text"
                                    v-if="!edit_form && lease_bad_debt_provision"
                                    >Yes</span
                                >
                                <span
                                    class="form-input-text"
                                    v-if="!edit_form && !lease_bad_debt_provision"
                                    >No</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <!--lease type and retail sub category-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Lease Type:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <cirrus-single-select-v2
                                            v-if="edit_form"
                                            v-model="lease_type"
                                            :options="dd_param_lease_type_list"
                                            ref="refPropertyType"
                                            trackBy="field_key"
                                            label="field_value"
                                            return="field_key"
                                            placeholder="Select a lease type"
                                        />
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{ getDropdownName(lease_type, dd_param_lease_type_list) }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Tenant Type:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <cirrus-single-select-v2
                                            v-if="edit_form"
                                            v-model="lease_tenant_type"
                                            :options="dd_param_lease_tenant_type_list"
                                            ref="refPropertyType"
                                            trackBy="field_key"
                                            label="field_value"
                                            return="field_key"
                                            placeholder="Select a tenant type"
                                        />
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{
                                                getDropdownName(lease_tenant_type, dd_param_lease_tenant_type_list)
                                            }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td class="title"><b>Division:</b></td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <cirrus-single-select-v2
                                            v-if="edit_form"
                                            v-model="lease_division"
                                            :options="dd_param_division_list"
                                            ref="refPropertyType"
                                            trackBy="field_key"
                                            label="field_value"
                                            return="field_key"
                                            placeholder="Select a division"
                                        />
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{ getDropdownName(lease_division, dd_param_division_list) }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="6"
                    xl="6"
                >
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Retail Category:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <cirrus-single-select-v2
                                            v-if="edit_form"
                                            v-model="lease_retail_category"
                                            :options="dd_retail_category_list"
                                            ref="refPropertyType"
                                            trackBy="field_key"
                                            label="field_value"
                                            return="field_key"
                                            placeholder="Select a retail category"
                                        />
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{ getDropdownName(lease_retail_category, dd_retail_category_list) }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Retail Sub Category:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <cirrus-single-select-v2
                                            v-if="edit_form"
                                            v-model="lease_retail_sub_category"
                                            :options="loadSubCategoryListBySubCategory"
                                            ref="refPropertyType"
                                            trackBy="field_key"
                                            label="field_value"
                                            return="field_key"
                                            placeholder="Select a sub category"
                                        />
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{
                                                getDropdownName(
                                                    lease_retail_sub_category,
                                                    loadSubCategoryListBySubCategory,
                                                )
                                            }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Retail Fine Category:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <cirrus-single-select-v2
                                            v-if="edit_form"
                                            v-model="lease_retail_fine_category"
                                            :options="loadFineCategoryListBySubCategory"
                                            ref="refPropertyType"
                                            trackBy="field_key"
                                            label="field_value"
                                            return="field_key"
                                            placeholder="Select a fine category"
                                        />
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{
                                                getDropdownName(
                                                    lease_retail_fine_category,
                                                    loadFineCategoryListBySubCategory,
                                                )
                                            }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                </v-col>
            </v-row>
        </div>
        <!-- FOR PRINTING -->
        <div
            class="page-form"
            v-if="formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-subheader"
                    ><strong>Optional Lease Details</strong></v-col
                >
            </v-row>

            <!--bad debt provision-->
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                        <tr>
                            <td
                                class="title"
                                align="right"
                            >
                                <strong>Bad Debt Provision:</strong>
                            </td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td>
                                <sui-checkbox
                                    v-if="edit_form"
                                    v-model="lease_bad_debt_provision"
                                />
                                <span
                                    class="form-input-text"
                                    v-if="!edit_form && lease_bad_debt_provision"
                                    >Yes</span
                                >
                                <span
                                    class="form-input-text"
                                    v-if="!edit_form && !lease_bad_debt_provision"
                                    >No</span
                                >
                            </td>
                        </tr>
                    </table>
                </v-col>
            </v-row>
            <v-row class="form-row no-gutters">
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Lease Type:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{ getDropdownName(lease_type, dd_param_lease_type_list) }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Tenant Type:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{
                                                getDropdownName(lease_tenant_type, dd_param_lease_tenant_type_list)
                                            }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Division:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{ getDropdownName(lease_division, dd_param_division_list) }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                </v-col>
                <v-col
                    cols="12"
                    xs="12"
                    sm="12"
                    md="12"
                    lg="12"
                    xl="12"
                >
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Retail Category:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{ getDropdownName(lease_retail_category, dd_retail_category_list) }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Retail Sub Category:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{
                                                getDropdownName(
                                                    lease_retail_sub_category,
                                                    loadSubCategoryListBySubCategory,
                                                )
                                            }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Retail Fine Category:</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    ></td>
                                    <td>
                                        <span
                                            v-if="!edit_form"
                                            class="form-input-text"
                                            >{{
                                                getDropdownName(
                                                    lease_retail_fine_category,
                                                    loadFineCategoryListBySubCategory,
                                                )
                                            }}</span
                                        >
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                </v-col>
            </v-row>
        </div>
        <v-divider></v-divider>
        <v-card
            elevation="0"
            v-if="edit_form || new_lease"
        >
            <v-card-actions v-if="edit_form">
                <v-spacer></v-spacer>
                <v-btn
                    class="v-step-save-2-button"
                    @click="saveForm()"
                    color="success"
                    :disabled="save_button_enable"
                    small
                >
                    {{ save_button_label }}
                </v-btn>
            </v-card-actions>
        </v-card>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!--    -->
        <v-dialog
            v-model="show_interest_on_arrears"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Interest on Arrears
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_interest_on_arrears = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <v-row
                        class="form-row no-gutters"
                        v-if="lease_charge_interest === 0"
                    >
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                <tr>
                                    <td
                                        class="title"
                                        align="right"
                                    >
                                        <strong>Select Accounts (incur interest):</strong>
                                    </td>
                                    <td
                                        class="required"
                                        v-if="edit_form"
                                    >
                                        *
                                    </td>
                                    <td>
                                        <vue-dual-list-select
                                            v-if="edit_form"
                                            v-model="lease_charge_interest_incur_account"
                                            v-bind:options="dd_account_income_grouped_list"
                                        ></vue-dual-list-select>
                                        <div
                                            v-if="
                                                !edit_form && lease_charge_interest_incur_accountData.field_key !== ''
                                            "
                                            v-for="(
                                                lease_charge_interest_incur_accountData, index
                                            ) in lease_charge_interest_incur_account"
                                            :key="index"
                                        >
                                            <span>
                                                {{ lease_charge_interest_incur_accountData.field_key }} -
                                                {{ lease_charge_interest_incur_accountData.field_value }}</span
                                            >
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="show_interest_on_arrears = false"
                    >
                        <v-icon
                            left
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_bond_lease_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitNewBond()"
        >
            <v-card>
                <v-card-title class="headline">
                    New Bond/Deposit Lease
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_bond_lease_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <!--Lease add-->
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Bond/Deposit Lease Code
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="new_bond_lease_code"
                                    size=""
                                    :id="'new_bond_lease_code'"
                                    data-inverted=""
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Bond/Deposit Lease Name
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="new_bond_lease_name"
                                    size=""
                                    :id="'new_bond_lease_name'"
                                    data-inverted=""
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />

                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitNewBond()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="show_bond_lease_modal = false"
                    >
                        <v-icon
                            left
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="showComActivityLogModal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Lease Term Log
                    <v-spacer></v-spacer>
                    <v-icon
                        color="white"
                        @click="addLeaseTermLogForm"
                        >add
                    </v-icon>
                    <v-icon
                        color="white"
                        v-if="!edit_form"
                        v-show="
                            !read_only &&
                            !new_lease &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                            !pmro_read_only
                        "
                        @click="edit_form = true"
                        >edit
                    </v-icon>
                    <v-icon
                        color="white"
                        @click="showComActivityLogModal = false"
                        >close
                    </v-icon>
                </v-card-title>
                <v-card-text>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                            lg="12"
                            xl="12"
                        >
                            <div class="page-form">
                                <v-row
                                    class="form-row"
                                    v-show="comShowFormFlag"
                                >
                                    <v-col
                                        xs="12"
                                        sm="12"
                                        md="12"
                                    >
                                        <h3>Entry Information</h3>
                                        <cirrus-server-error :errorMsg2="logErrorServerMsg"></cirrus-server-error>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-show="comShowFormFlag"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Commencement Date
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <cirrus-icon-date-picker
                                            :size="'40'"
                                            :id="'lease_commencement_date'"
                                            v-model="logLeaseCommencementDate"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-icon-date-picker>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-show="comShowFormFlag"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label required"
                                        >Expiry Date
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <cirrus-icon-date-picker
                                            :size="'40'"
                                            :id="'lease_commencement_date'"
                                            v-model="logLeaseExpiryDate"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-icon-date-picker>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-show="comShowFormFlag"
                                >
                                    <v-col
                                        xs="12"
                                        sm="3"
                                        md="3"
                                        class="form-label"
                                        >Lease Option
                                    </v-col>
                                    <v-col
                                        xs="12"
                                        sm="9"
                                        md="9"
                                        class="form-input"
                                    >
                                        <cirrus-input
                                            :size="'40'"
                                            :id="'lease_option'"
                                            v-model="logLeaseOption"
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row"
                                    v-show="comShowFormFlag"
                                >
                                    <v-col
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        class="right"
                                    >
                                        <v-btn
                                            color="success"
                                            small
                                            @click="saveLeaseTermLog"
                                            >Submit
                                        </v-btn>
                                        <v-btn
                                            small
                                            @click="cancelLeaseTermLogForm"
                                            >Cancel
                                        </v-btn>
                                    </v-col>
                                </v-row>
                            </div>

                            <v-simple-table dense>
                                <template v-slot:default>
                                    <thead>
                                        <tr class="fieldDescription">
                                            <th class="text-left">ID</th>
                                            <th class="text-left">Commencement Date</th>
                                            <th class="text-left">Expiry Date</th>
                                            <th class="text-left">Lease Option</th>
                                            <th class="text-left"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr
                                            v-for="(data, index) in leaseTermLogList"
                                            :key="index"
                                            :class="data.isSystem ? 'system-row' : ''"
                                        >
                                            <td>{{ data.itemNo }}</td>
                                            <td>{{ data.commencementDate }}</td>
                                            <td>{{ data.expiryDate }}</td>
                                            <td>{{ data.leaseOption }}</td>
                                            <td align="right">
                                                <v-icon
                                                    v-show="edit_form && !data.isSystem"
                                                    small
                                                    @click="showEditLeaseTermLog(data)"
                                                    >edit
                                                </v-icon>
                                                <v-icon
                                                    v-show="edit_form && !data.isSystem"
                                                    color="error"
                                                    small
                                                    @click="deleteLeaseTermLog(data.id)"
                                                    >close
                                                </v-icon>
                                            </td>
                                        </tr>
                                    </tbody>
                                </template>
                            </v-simple-table>
                        </v-col>
                    </v-row>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import Vue from 'vue';
import SuiVue from 'semantic-ui-vue';
import Multiselect from 'vue-multiselect';
import vSelect from 'vue-select';
import { mapGetters, mapMutations, mapState } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import suburb_list from '../../../../plugins/australianSuburb.json';
import axios from 'axios';
import { LEASE_TERM_LOG_IS_ENABLED } from '../../../../constants';
import isEmpty from 'lodash/isEmpty';

const Swal = require('sweetalert2');
Vue.use(SuiVue);
Vue.component('v-select', vSelect);
Vue.component('multiselect', Multiselect);
Vue.component('lease-activity-log-component', require('./LeaseActivityLog.vue').default);

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        force_load: { type: Number, default: 0 },
        save_button_label: { type: String, default: 'Save details' },
        page_form_type: { type: String, default: '' },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
        country_default_settings: { type: String, default: '' },
    },
    data() {
        return {
            date: new Date().toISOString().substr(0, 10),
            menu: false,
            modal: false,
            menu2: false,
            form_type: 'LEASE',
            form_section: 'LEASE_MAIN_DETAIL',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            save_button_enable: false,
            toggle_tips: false,
            edit_form: false,
            lease_name: '',
            lease_name_old: '',
            is_main_lease_index: 1,
            is_main_lease_index_old: 1,
            is_main_lease_enabled: false,
            save_company_details_flag: false,
            lease_tenant_location: '',
            lease_tenant_location_old: '',
            lease_tenant_crn: '',
            lease_tenant_crn_old: '',
            property_portfolio_desc: '',
            property_portfolio_desc_old: '',
            lease_status: '',
            lease_status_index: 1,
            lease_status_index_old: 1,
            lease_status_list: [
                { field_key: 'C', field_value: 'Current' },
                { field_key: 'V', field_value: 'Vacant' },
            ],
            sync_to_fm: false,
            lease_tenant_name: '',
            lease_tenant_address_1: '',
            lease_tenant_address_2: '',
            lease_tenant_suburb: '',
            lease_tenant_country: '',
            lease_tenant_state: '',
            lease_tenant_state_list: [],
            lease_mail_setting_stateList: [],
            email_centralisation_setting: [],
            lease_tenant_post_code: '',
            lease_tenant_abn: '',
            abn_details: false,
            abn_button_loading: false,
            abn_button_label: 'Look up ABN',
            abn_message: '',
            abn_comp_name: '',
            abn_number: '',
            abn_value: '',
            abn_comp_type: '',
            abn_gst: '',
            abn_gst_status: '',
            abn_state_code: '',
            abn_post_code: '',
            abn_status: '',
            abn_detail_display: true,
            abn_details_loading: false,
            lease_tenant_email: '',
            lease_tenant_email_arr: [],
            debtor_in_ar_count: 0,
            save_company_details: false,
            company_old: '',
            sync_to_fm_old: false,
            lease_tenant_name_old: '',
            lease_tenant_address_1_old: '',
            lease_tenant_address_2_old: '',
            lease_tenant_suburb_old: '',
            lease_tenant_country_old: '',
            lease_tenant_state_old: '',
            lease_tenant_state_list_old: [],
            lease_mail_setting_stateList_old: [],
            lease_tenant_post_code_old: '',
            lease_tenant_email_old: '',
            save_company_details_old: false,
            lease_commencement_date: this.dateToday(),
            lease_commencement_date_old: this.dateToday(),
            lease_expiry_date: '31/12/2999',
            lease_expiry_date_old: this.dateToday(),
            lease_term: '',
            lease_term_old: '',
            lease_option: '',
            lease_option_old: '',
            lease_status_description: '',
            lease_status_description_old: '',
            state_list: [
                { value: 'WA', label: 'Western Australia' },
                { value: 'VIC', label: 'Victoria' },
                { value: 'NSW', label: 'New South Wales' },
                { value: 'QLD', label: 'Queensland' },
                { value: 'SA', label: 'South Australia' },
                { value: 'NT', label: 'Northern Territory' },
                { value: 'ACT', label: 'Australian Capital Territory' },
                { value: 'TAS', label: 'Tasmania' },
                { value: 'MALAYSIA', label: 'Malaysia' },
                { value: 'NZ', label: 'New Zealand' },
                { value: 'SNG', label: 'Singapore' },
                { value: 'ENG', label: 'England' },
                { value: 'SCO', label: 'Scotland' },
                { value: 'WAL', label: 'Wales' },
            ],
            lease_bond_deposit_property: '',
            lease_bond_deposit_property_old: '',
            lease_bond_deposit_lease: '',
            lease_bond_deposit_lease_old: '',
            lease_charge_interest: 1,
            refresh_index: 1,
            lease_mail_setting_address_same_as_above: 0,
            lease_mail_setting_address_same_as_above_old: 0,
            lease_inside_act_show: false,
            lease_inside_act: 1,
            lease_charge_interest_old: 1,
            lease_bad_debt_provision: false,
            lease_bad_debt_provision_old: false,
            lease_type: '',
            lease_type_old: '',
            lease_tenant_type: '',
            lease_tenant_type_old: '',
            lease_division: '',
            lease_division_old: '',
            lease_state: '',
            lease_state_old: '',
            lease_retail_category: '',
            lease_retail_category_old: '',
            lease_retail_sub_category: '',
            lease_retail_sub_category_old: '',
            lease_retail_fine_category: '',
            lease_retail_fine_category_old: '',
            lease_charge_interest_description: '',
            lease_charge_interest_description_old: '',
            lease_charge_interest_account: '',
            lease_charge_interest_account_old: '',
            // lease_charge_interest_account_list: [],
            lease_charge_interest_incur_account: [],
            lease_charge_interest_incur_account_old: '',
            lease_charge_interest_grace_period: '',
            lease_charge_interest_grace_period_old: '',
            lease_charge_interest_fixed_interest_rate: '',
            lease_charge_interest_fixed_interest_rate_old: '',
            dropdown_default: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            lease_mail_setting_for_attention_of: '',
            lease_mail_setting_address_1: '',
            lease_mail_setting_address_2: '',
            lease_mail_setting_suburb: '',
            lease_mail_setting_country: '',
            lease_mail_setting_state: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            lease_mail_setting_post_code: '',
            lease_mail_setting_for_attention_of_old: '',
            lease_mail_setting_address_1_old: '',
            lease_mail_setting_address_2_old: '',
            lease_mail_setting_suburb_old: '',
            lease_mail_setting_country_old: '',
            lease_mail_setting_state_old: '',
            lease_mail_setting_post_code_old: '',
            new_company_code: '',
            lease_company_code: '',
            lease_company_id: '',
            lease_company: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            company_code_old: {
                value: '',
                label: 'Please select ...',
                fieldKey: '',
                fieldValue: 'Please select ...',
                field_key: '',
                field_value: 'Please select ...',
            },
            company_type: 0,
            company_existed: false,
            create_new_company_btn: false,
            company_list: [],
            lease_bond_deposit_lease_list: [],
            tour_mailing_details_steps: [],
            tour_lease_details_steps: [],
            show_activity_log_modal: false,
            show_interest_on_arrears: false,
            suburb_list_filtered: [],
            postcode_list_filtered: [],
            suburb_mail_list_filtered: [],
            postcode_mail_list_filtered: [],
            searchLeaseTenantSuburb: '',
            searchLeaseTenantPostcode: '',
            searchLeaseTenantMailSuburb: '',
            searchLeaseTenantMailPostcode: '',
            company_is_temp_flag: false,
            show_bond_lease_modal: false,
            new_bond_lease_code: '',
            new_bond_lease_name: '',

            tenant_country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
            },

            mail_country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
            },

            cdf_for_tenant_address: {
                country_code: 'AU',
                display_state: true,
                post_code_length: '4',
            },
            suburb_label: 'suburb',
            portfolio_manager_label: 'Portfolio Manager',
            showComActivityLogModal: false,
            leaseTermLogId: null,
            logLeaseCommencementDate: this.dateToday(),
            logLeaseExpiryDate: '31/12/2999',
            logLeaseOption: '',
            leaseTermLogList: [],
            leaseTermLogIsOn: LEASE_TERM_LOG_IS_ENABLED,
            logErrorServerMsg: [],
            comShowFormFlag: false,
        };
    },
    mounted() {
        this.loadCompanyList();
        if (this.new_lease) {
            this.edit_form = true;
            this.loadMainFormDetails(false);
            this.loadCountryDefaults('general', '');
        } else {
            this.loadForm();
            this.company_type = 1;
        }
        let country_default_settings = JSON.parse(atob(this.country_default_settings));
        this.suburb_label = country_default_settings.suburb;
        this.portfolio_manager_label = this.ucwords(country_default_settings.portfolio_manager);
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'tour_steps',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'dd_default_value',
            'dd_country_list',
            'dd_account_income_grouped_list',
            'dd_account_income_ungrouped_list',
            'dd_bond_deposit_property_list',
            'dd_param_division_list',
            'dd_param_lease_type_list',
            'dd_param_lease_tenant_type_list',
            'dd_retail_category_list',
            'dd_retail_sub_category_list',
            'dd_retail_fine_category_list',
            'auto_diarise',
            'email_cen_setup',
            'dd_company_list',
        ]),
        ...mapGetters(['getDDCountryStates', 'getLeaseDetails']),
        leaseStatus() {
            let lease_status = this.lease_status;
            if (lease_status === 'C') {
                return 0;
            } else if (lease_status === 'L') {
                return 1;
            }
        },
        emailArr() {
            let lease_tenant_email = this.lease_tenant_email;
            const splitEm = lease_tenant_email.split(';');
            return splitEm;
        },
        loadSubCategoryListBySubCategory() {
            let new_list = [];
            new_list = this.dd_retail_sub_category_list.filter((m) => m.category_id == this.lease_retail_category);
            return new_list;
        },
        loadFineCategoryListBySubCategory() {
            let new_list = [];
            new_list = this.dd_retail_fine_category_list.filter(
                (m) => m.sub_category_id == this.lease_retail_sub_category,
            );
            return new_list;
        },
        hasLeaseOptionsAndEditFormAndAutoDiariseDisabled() {
            return !isEmpty(this.lease_expiry_date) && this.edit_form && !this.auto_diarise;
        },
        hasEditModeAndLeaseExpiryHasOptionsAndAutoDiariseDisabled() {
            return (
                !isEmpty(this.lease_expiry_date) && !isEmpty(this.lease_option) && this.edit_form && !this.auto_diarise
            );
        },
    },
    methods: {
        ...mapMutations(['SET_LEASE_MAIN_DETAILS', 'SET_LEASE_OLD_MAIN_DETAILS', 'SET_TOUR_STEPS']),
        verifyCode: function (inputString) {
            var newStr = inputString.replace(/[^a-zA-Z0-9-]/g, '');
            return newStr.length != inputString.length;
        },
        doubleClickForm() {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        dateToday: function () {
            let today = new Date();
            let dd = today.getDate();
            let mm = today.getMonth() + 1; //January is 0!
            let yyyy = today.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            today = dd + '/' + mm + '/' + yyyy;
            return new Date().toISOString().substr(0, 10);
        },
        resetForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.new_lease) {
                this.edit_form = true;
            } else {
                this.edit_form = false;
            }
            this.lease_name = this.lease_name_old;
            this.is_main_lease_index = this.is_main_lease_index_old;
            this.lease_tenant_location = this.lease_tenant_location_old;
            this.lease_tenant_crn = this.lease_tenant_crn_old;
            this.lease_status_index = this.lease_status_old;
            this.lease_company = this.company_old;
            this.sync_to_fm = this.sync_to_fm_old;
            this.lease_tenant_name = this.lease_tenant_name_old;
            this.lease_tenant_address_1 = this.lease_tenant_address_1_old;
            this.lease_tenant_address_2 = this.lease_tenant_address_2_old;
            this.lease_tenant_suburb = this.lease_tenant_suburb_old;
            this.lease_tenant_country = this.lease_tenant_country_old;
            // this.lease_tenant_country_list = this.lease_tenant_country_list_old;
            // this.lease_mail_setting_country_list = this.lease_mail_setting_country_list_old;
            this.lease_tenant_state = this.lease_tenant_state_old;
            this.suburbFilteredList(this.lease_tenant_state.field_key);
            this.postcodeFilteredList(this.lease_tenant_state.field_key);
            this.lease_mail_setting_state = this.lease_mail_setting_state_old;
            this.lease_tenant_state_list = this.lease_tenant_state_list_old;
            this.lease_tenant_post_code = this.lease_tenant_post_code_old;
            this.lease_tenant_email = this.lease_tenant_email_old;
            this.save_company_details = this.save_company_details_old;

            this.lease_commencement_date = this.lease_commencement_date_old;
            this.lease_expiry_date = this.lease_expiry_date_old;
            this.lease_term = this.lease_term_old;
            this.lease_option = this.lease_option_old;
            this.lease_status_description = this.lease_status_description_old;
            this.lease_bond_deposit_property = this.lease_bond_deposit_property_old;
            this.lease_bond_deposit_lease = this.lease_bond_deposit_lease_old;

            this.lease_charge_interest = this.lease_charge_interest_old;
            this.lease_charge_interest_description = this.lease_charge_interest_description_old;
            this.lease_charge_interest_account = this.lease_charge_interest_account_old;
            this.lease_charge_interest_incur_account = this.lease_charge_interest_incur_account_old;
            this.lease_charge_interest_grace_period = this.lease_charge_interest_grace_period_old;
            this.lease_charge_interest_fixed_interest_rate = this.lease_charge_interest_fixed_interest_rate_old;

            this.lease_mail_setting_address_same_as_above = this.lease_mail_setting_address_same_as_above_old;
            this.lease_mail_setting_for_attention_of = this.lease_mail_setting_for_attention_of_old;
            this.lease_mail_setting_address_1 = this.lease_mail_setting_address_1_old;
            this.lease_mail_setting_address_2 = this.lease_mail_setting_address_2_old;
            this.lease_mail_setting_suburb = this.lease_mail_setting_suburb_old;
            this.lease_mail_setting_country = this.lease_mail_setting_country_old;
            this.lease_mail_setting_state = this.lease_mail_setting_state_old;
            this.lease_mail_setting_post_code = this.lease_mail_setting_post_code_old;
        },
        async saveForm() {
            let new_company_code = this.new_company_code.toUpperCase().trim();
            let company_code = '';
            this.error_msg = [];
            this.error_server_msg2 = [];

            if (!this.lease_charge_interest_account) {
                this.lease_charge_interest_account = this.dropdown_default;
            }

            if (this.lease_name === '') this.error_msg.push(['You have not specified a lease name.']);
            if (this.lease_tenant_location === '')
                this.error_server_msg2.push(['You have not specified a lease location.']);
            if (this.user_type === 'A')
                if (this.lease_tenant_crn === '') this.error_server_msg2.push(['You have not specified a lease CRN.']);
            if (this.lease_tenant_name === '') this.error_server_msg2.push(['You have not specified a tenant name.']);
            if (this.lease_tenant_address_1 === '' && this.lease_tenant_address_2 === '')
                this.error_server_msg2.push(['You have not specified a tenant address.']);
            if (this.lease_tenant_suburb === '')
                this.error_server_msg2.push(['You have not specified a tenant ' + this.suburb_label + '.']);

            if (this.new_lease) {
                if (this.company_type === 0) {
                    this.new_company_code = new_company_code;
                    if (new_company_code.length > 10)
                        this.error_server_msg2.push(['Company code allows 10 characters only']);
                    else if (new_company_code === '')
                        this.error_server_msg2.push(['Please input a valid company details']);

                    company_code = new_company_code;
                } else {
                    this.new_company_code = this.lease_company.field_key;
                    company_code = this.lease_company.field_key;
                }
            } else {
                this.new_company_code = this.lease_company.field_key;
                company_code = this.lease_company.field_key;
            }
            if (Object.keys(this.lease_tenant_country).length > 0) {
                if (this.lease_tenant_country.field_key === '')
                    this.error_server_msg2.push(['You have not specified a tenant country.']);
            } else this.error_server_msg2.push(['You have not specified a tenant country.']);

            if (this.cdf_for_tenant_address.display_state == true) {
                if (!this.lease_tenant_state) this.error_server_msg2.push(['You have not specified a tenant state.']);
                else if (this.lease_tenant_state.field_key === '')
                    this.error_server_msg2.push(['You have not specified a tenant state.']);
            }

            if (this.lease_tenant_post_code === '' || !this.lease_tenant_post_code)
                this.error_server_msg2.push(['You have not specified a tenant post code.']);
            if (
                this.lease_tenant_abn &&
                parseInt(this.tenant_country_defaults.business_length) != this.lease_tenant_abn.length
            ) {
                var abn_error_message =
                    'Tenant ' +
                    this.tenant_country_defaults.business_label +
                    ' must be ' +
                    this.tenant_country_defaults.business_length +
                    ' digits (no spaces).';
                this.error_server_msg2.push([abn_error_message]);
            }
            if (this.lease_commencement_date === '')
                this.error_server_msg2.push(['You have not specified a lease commencement date.']);
            if (this.lease_expiry_date === '')
                this.error_server_msg2.push(['You have not specified a lease expiry date.']);
            if (this.lease_charge_interest === 0) {
                if (Object.keys(this.lease_charge_interest_account).length > 0) {
                    if (this.lease_charge_interest_account.field_key === '')
                        this.error_server_msg2.push(['You have not specified a lease charge interest account.']);
                } else this.error_server_msg2.push(['You have not specified a lease charge interest account.']);
                if (this.lease_charge_interest_incur_account.length <= 0)
                    this.error_server_msg2.push(['You have not specified any lease charge interest incur account.']);
                if (this.lease_charge_interest_fixed_interest_rate === '')
                    this.error_server_msg2.push(['You have not specified any lease charge interest fixed rate.']);
                else {
                    if (isNaN(parseFloat(this.lease_charge_interest_fixed_interest_rate)))
                        this.error_server_msg2.push([
                            'You have not specified a valid fixed interest rate for interest on arrears.',
                        ]);
                    else if (
                        this.lease_charge_interest_fixed_interest_rate < 0 &&
                        this.lease_charge_interest_fixed_interest_rate > 100
                    )
                        this.error_server_msg2.push([
                            'You have not specified a valid fixed interest rate for interest on arrears.',
                        ]);
                }
            }
            if (this.lease_mail_setting_address_same_as_above === 1) {
                if (this.lease_mail_setting_address_1 === '' && this.lease_mail_setting_address_2 === '')
                    this.error_server_msg2.push(['You have not specified a address.']);
                if (this.lease_mail_setting_suburb === '')
                    this.error_server_msg2.push(['You have not specified a ' + this.suburb_label + '.']);
                if (Object.keys(this.lease_mail_setting_country).length > 0) {
                    if (this.lease_mail_setting_country.field_key === '')
                        this.error_server_msg2.push(['You have not specified a country.']);
                } else this.error_server_msg2.push(['You have not specified a country.']);
                if (this.mail_country_defaults.display_state == true) {
                    if (!this.lease_mail_setting_state)
                        this.error_server_msg2.push(['You have not specified a state.']);
                    else if (this.lease_mail_setting_state.field_key === '')
                        this.error_server_msg2.push(['You have not specified a state.']);
                }
                // COUNTRY DEFAULT
                if (this.lease_mail_setting_post_code === '')
                    this.error_server_msg2.push(['You have not specified a post code.']);
            }
            if (this.new_lease && this.company_type === 0 && this.lease_tenant_email !== '') {
                let email_arr = this.emailArr;
                for (let email_index = 0; email_index <= email_arr.length - 1; email_index++)
                    if (!this.validateEmail(email_arr[email_index]))
                        this.error_server_msg2.push(['Email address is invalid.']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('new_lease', this.new_lease);
                form_data.append('company_type', this.company_type);

                //update or create lease details
                form_data.append('lease_name', this.lease_name);
                form_data.append('lease_tenant_location', this.lease_tenant_location);
                form_data.append('lease_tenant_crn', this.lease_tenant_crn);
                form_data.append('lease_status', this.lease_status_index);
                form_data.append('is_main_lease_index', this.is_main_lease_index);
                form_data.append('new_company_code', this.new_company_code);
                form_data.append('company_code', company_code);
                form_data.append('lease_tenant_name', this.lease_tenant_name);
                form_data.append('lease_tenant_address_1', this.lease_tenant_address_1);
                form_data.append('lease_tenant_address_2', this.lease_tenant_address_2);
                form_data.append('lease_tenant_suburb', this.lease_tenant_suburb);
                form_data.append('lease_tenant_country', this.lease_tenant_country.field_key);
                let tenant_state = '';
                if (this.cdf_for_tenant_address.display_state == true) tenant_state = this.lease_tenant_state.field_key;
                form_data.append('lease_tenant_state', tenant_state);
                form_data.append('lease_tenant_post_code', this.lease_tenant_post_code);
                form_data.append('lease_tenant_email', this.lease_tenant_email);
                form_data.append('lease_tenant_abn', this.lease_tenant_abn);
                form_data.append('email_centralisation_setting', JSON.stringify(this.email_centralisation_setting));
                form_data.append('lease_commencement_date', this.lease_commencement_date);
                form_data.append('lease_expiry_date', this.lease_expiry_date);
                form_data.append('lease_term', this.lease_term);
                form_data.append('lease_option', this.lease_option);
                form_data.append('lease_status_description', this.lease_status_description);
                form_data.append('lease_bond_deposit_property', this.lease_bond_deposit_property);
                if (this.lease_bond_deposit_property === '') form_data.append('lease_bond_deposit_lease', '');
                else form_data.append('lease_bond_deposit_lease', this.lease_bond_deposit_lease);

                form_data.append('lease_charge_interest', this.lease_charge_interest);
                form_data.append('lease_charge_interest_description', this.lease_charge_interest_description);
                form_data.append('lease_charge_interest_account', this.lease_charge_interest_account.field_key);
                form_data.append(
                    'lease_charge_interest_incur_account',
                    JSON.stringify(this.lease_charge_interest_incur_account),
                );
                form_data.append('lease_charge_interest_grace_period', this.lease_charge_interest_grace_period);
                form_data.append(
                    'lease_charge_interest_fixed_interest_rate',
                    this.lease_charge_interest_fixed_interest_rate,
                );
                form_data.append('lease_bad_debt_provision', this.lease_bad_debt_provision);
                form_data.append('lease_type', this.lease_type);
                form_data.append('lease_tenant_type', this.lease_tenant_type);
                form_data.append('lease_division', this.lease_division);
                form_data.append('lease_state', this.lease_state.field_key);
                form_data.append('lease_retail_category', this.lease_retail_category);
                form_data.append('lease_retail_sub_category', this.lease_retail_sub_category);
                form_data.append('lease_retail_fine_category', this.lease_retail_fine_category);
                form_data.append('lease_inside_act', this.lease_inside_act);
                form_data.append(
                    'lease_mail_setting_address_same_as_above',
                    this.lease_mail_setting_address_same_as_above,
                );
                form_data.append('lease_mail_setting_for_attention_of', this.lease_mail_setting_for_attention_of);
                form_data.append('lease_mail_setting_address_1', this.lease_mail_setting_address_1);
                form_data.append('lease_mail_setting_address_2', this.lease_mail_setting_address_2);
                form_data.append('lease_mail_setting_suburb', this.lease_mail_setting_suburb);
                form_data.append('lease_mail_setting_country', this.lease_mail_setting_country.field_key);
                let mail_state = '';
                if (this.mail_country_defaults.display_state == true) {
                    mail_state = this.lease_mail_setting_state.field_key;
                }
                form_data.append('lease_mail_setting_state', mail_state);
                form_data.append('lease_mail_setting_post_code', this.lease_mail_setting_post_code);
                form_data.append('save_company_details_flag', this.save_company_details_flag);

                form_data.append('tenant_country_defaults', JSON.stringify(this.tenant_country_defaults));

                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isLeaseFormLive()) apiUrl = 'lease/update-or-create/main-form-details';
                else apiUrl = 'temp/lease/update-or-create/main-form-details';

                if (this.isLeaseFormLive()) {
                    this.loading_page_setting = true;
                    this.save_button_enable = true;
                    var form_data_2 = new FormData();
                    form_data_2.append('property_code', this.property_code);
                    form_data_2.append('lease_code', this.lease_code);
                    form_data_2.append('lease_tenant_crn', this.lease_tenant_crn);
                    form_data_2.append('no_load', true);
                    this.$api.post('lease/fetch/check-crn-if-exist', form_data_2).then(async (response) => {
                        this.loading_page_setting = false;
                        this.save_button_enable = false;
                        if (response.data.existed_lease_code === '') {
                            this.sendSave(apiUrl, form_data);
                            return;
                        }
                        let dialog_prop = {
                            title: 'Warning',
                            message:
                                'This CRN is assigned to ' +
                                this.property_code +
                                '/' +
                                response.data.existed_lease_code +
                                '. Are you sure you want to assign this CRN again?',
                            icon_show: true,
                            buttons_right: [
                                { label: 'Yes', value: 1, color: 'primary' },
                                { label: 'No', value: 2 },
                            ],
                        };
                        const result = await cirrusDialog(dialog_prop);
                        if (result !== 1) this.lease_tenant_crn = '';
                        else this.sendSave(apiUrl, form_data);
                    });
                } else this.sendSave(apiUrl, form_data);
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Invalid input, please check the form.',
                    icon_show: true,
                };
                cirrusDialog(dialog_prop);
            }
        },
        sendSave: function (apiUrl, form_data) {
            this.$api.post(apiUrl, form_data).then((response) => {
                this.error_server_msg = response.data.error_server_msg;
                this.error_server_msg2 = response.data.error_server_msg2;
                if (Object.keys(this.error_server_msg).length <= 0 && this.error_server_msg2.length <= 0) {
                    if (this.auto_diarise) {
                        this.diariseExpiryDate();
                        this.diariseLeaseOption();
                    }
                    this.loadMainFormDetails();
                    if (this.new_lease) {
                        this.edit_form = true;
                    } else {
                        this.edit_form = false;
                    }
                    if (this.new_lease) {
                        if (apiUrl.includes('temp')) {
                            this.updatePageTitle(this.lease_code + '/' + this.property_code + ' - ' + this.lease_name);
                        }

                        if (Object.keys(this.error_server_msg).length === 0) {
                            this.$emit('returnLeaseCode', {
                                property_code: this.property_code,
                                lease_code: this.lease_code,
                                lease_name: this.lease_name,
                            });
                            this.$emit('returnLeaseIsExisted', {
                                property_code: this.property_code,
                                lease_code: this.lease_code,
                                lease_name: this.lease_name,
                            });
                            this.$emit('returnLeaseFormSuccess', {
                                property_code: this.property_code,
                                lease_code: this.lease_code,
                                lease_name: this.lease_name,
                            });
                            if (this.save_button_label === 'Continue to step 2') {
                                bus.$emit('loadLeaseUnitDetailsSection');
                                // bus.$emit("loadLeaseRentReviewSection");
                                bus.$emit('loadLeaseDirectManagementFeeSection');
                            }
                        }
                    }
                    bus.$emit('processSaveFormNotes', '');
                }

                this.loading_setting = false;
            });
        },
        checkCRNIfExist: function () {
            this.loading_page_setting = true;
            this.save_button_enable = true;
            var form_data = new FormData();
            form_data.append('lease_tenant_crn', this.lease_tenant_crn);
            form_data.append('no_load', true);
            this.$api.post('lease/check-crn-if-exist', form_data).then(async (response) => {
                let dialog_prop = {
                    title: 'Warning',
                    message:
                        'This CRN is assigned to $propertyCode/$LeaseCode. Are you sure you want to assign this CRN again?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result !== 1) this.lease_tenant_crn = '';

                this.loading_page_setting = false;
                this.save_button_enable = false;
            });
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.company_existed = false;
            if (this.property_code !== '' && this.lease_code !== '') {
                this.loadMainFormDetails();
                this.loadCountryDefaults('general', '');
            }
        },
        loadMainFormDetails: function (enable_loading = true) {
            this.error_msg = [];
            this.error_server_msg2 = [];
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('new_lease_flag', this.new_lease);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            if (enable_loading) {
                this.loading_setting = true;
            }
            if (this.isLeaseFormLive()) {
                //get data from live
                this.$api.post('lease/fetch/main-form-details', form_data).then((response) => {
                    if (!this.new_lease)
                        this.updatePageTitle(
                            response.data.lease_code +
                                '/' +
                                response.data.property_code +
                                ' - ' +
                                response.data.lease_name,
                        );
                    if (this.new_lease) this.edit_form = true;
                    else this.edit_form = false;
                    this.SET_LEASE_MAIN_DETAILS(response.data);
                    this.SET_LEASE_OLD_MAIN_DETAILS(response.data);
                    // this.SET_LEASE_OLD_MAIN_DETAILS(response.data);
                    this.loadResponseToVariables(response);
                });
            } else {
                let apiUrl = 'temp/lease/fetch/main-form-details';
                this.$api.post(apiUrl, form_data).then((response) => {
                    if (response.data.lease_name !== '') {
                        this.$emit('returnLeaseIsExisted', response.data);
                    }
                    if (!this.new_lease)
                        this.updatePageTitle(
                            response.data.lease_code +
                                '/' +
                                response.data.property_code +
                                ' - ' +
                                response.data.lease_name,
                        );

                    this.SET_LEASE_MAIN_DETAILS(response.data);
                    this.SET_LEASE_OLD_MAIN_DETAILS(response.data);
                    // this.SET_LEASE_OLD_MAIN_DETAILS(response.data);
                    this.loadResponseToVariables(response);
                });
            }
        },
        loadResponseToVariables: function (response) {
            if (!this.new_lease || this.user_type !== 'A') {
                this.lease_name = response.data.lease_name;

                this.lease_name_old = response.data.lease_name;
                this.is_main_lease_index = response.data.is_main_lease_index;
                this.is_main_lease_index_old = response.data.is_main_lease_index;
                this.is_main_lease_enabled = response.data.is_main_lease_enabled;
                this.lease_tenant_location = response.data.lease_tenant_location;
                this.lease_tenant_location_old = response.data.lease_tenant_location;
                this.lease_tenant_crn = response.data.lease_tenant_crn;
                this.lease_tenant_crn_old = response.data.lease_tenant_crn;
                this.property_portfolio_desc = response.data.property_portfolio_desc;
                this.property_portfolio_desc_old = response.data.property_portfolio_desc;
                let tenantStatus = response.data.lease_tenant_status;
                if (tenantStatus === 'C') {
                    this.lease_status_index_old = 0;
                    this.lease_status_index = 0;
                    this.lease_status_old = 0;
                } else if (tenantStatus === 'L') {
                    this.lease_status_index_old = 1;
                    this.lease_status_index = 1;
                    this.lease_status_old = 1;
                }
                this.lease_company_code = response.data.lease_company_code;
                this.lease_company_id = response.data.lease_company_id;
                this.lease_company_name = response.data.lease_company_name;
                this.company_code_old = response.data.lease_company_code;
                Vue.prototype.$company_code = response.data.lease_company_code;
                this.lease_company = response.data.lease_company;
                this.company_old = response.data.lease_company;
                this.sync_to_fm = response.data.sync_to_fm;
                this.sync_to_fm_old = response.data.sync_to_fm;
                this.lease_tenant_name = response.data.lease_tenant_name;
                this.lease_tenant_name_old = response.data.lease_tenant_name;
                this.lease_tenant_address_1 = response.data.lease_tenant_address_1;
                this.lease_tenant_address_1_old = response.data.lease_tenant_address_1;
                this.lease_tenant_address_2 = response.data.lease_tenant_address_2;
                this.lease_tenant_address_2_old = response.data.lease_tenant_address_2;
                this.lease_tenant_suburb = response.data.lease_tenant_suburb;
                this.lease_tenant_suburb_old = response.data.lease_tenant_suburb;
                this.lease_tenant_post_code = response.data.lease_tenant_post_code;
                this.lease_tenant_post_code_old = response.data.lease_tenant_post_code;
                this.lease_tenant_abn = response.data.lease_tenant_abn;
                this.lease_tenant_email = response.data.lease_tenant_email;
                this.email_centralisation_setting = response.data.email_centralisation_setting;
                this.company_is_temp_flag = response.data.company_is_temp_flag;
                this.lease_tenant_email_old = response.data.lease_tenant_email;
                this.save_company_details = response.data.save_company_details;
                this.save_company_details_old = response.data.save_company_details;

                this.lease_inside_act = response.data.lease_inside_act ? 0 : 1;
                this.lease_inside_act_show = response.data.lease_inside_act_show;
                this.lease_mail_setting_address_same_as_above = parseInt(
                    response.data.lease_mail_setting_address_same_as_above,
                );
                this.lease_mail_setting_address_same_as_above_old = parseInt(
                    response.data.lease_mail_setting_address_same_as_above,
                );

                this.lease_mail_setting_for_attention_of = response.data.lease_mail_setting_for_attention_of;
                this.lease_mail_setting_for_attention_of_old = response.data.lease_mail_setting_for_attention_of;
                this.lease_mail_setting_address_1 = response.data.lease_mail_setting_address_1;
                this.lease_mail_setting_address_1_old = response.data.lease_mail_setting_address_1;
                this.lease_mail_setting_address_2 = response.data.lease_mail_setting_address_2;
                this.lease_mail_setting_address_2_old = response.data.lease_mail_setting_address_2;
                this.lease_mail_setting_suburb = response.data.lease_mail_setting_suburb;
                this.lease_mail_setting_post_code = response.data.lease_mail_setting_post_code;
                this.lease_mail_setting_post_code_old = response.data.lease_mail_setting_post_code;
                this.lease_term = response.data.lease_term;
                this.lease_term_old = response.data.lease_term;
                this.lease_option = response.data.lease_option;
                this.lease_option_old = response.data.lease_option;
                this.lease_status_description = response.data.lease_status_description;
                this.lease_status_description_old = response.data.lease_status_description;

                this.lease_charge_interest = parseInt(response.data.lease_charge_interest);
                this.lease_charge_interest_old = parseInt(response.data.lease_charge_interest);
                this.lease_charge_interest_description = response.data.lease_charge_interest_description;
                this.lease_charge_interest_description_old = response.data.lease_charge_interest_description;

                this.lease_charge_interest_grace_period = response.data.lease_charge_interest_grace_period;
                this.lease_charge_interest_grace_period_old = response.data.lease_charge_interest_grace_period;
                this.lease_charge_interest_fixed_interest_rate =
                    response.data.lease_charge_interest_fixed_interest_rate;
                this.lease_charge_interest_fixed_interest_rate_old =
                    response.data.lease_charge_interest_fixed_interest_rate;

                this.lease_bad_debt_provision = response.data.lease_bad_debt_provision;
                this.lease_bad_debt_provision_old = response.data.lease_bad_debt_provision;
            }
            this.lease_tenant_country = response.data.lease_tenant_country;
            this.lease_tenant_country_old = response.data.lease_tenant_country;
            // this.lease_tenant_country_list = response.data.lease_tenant_country_list;
            // this.lease_tenant_country_list_old = response.data.lease_tenant_country_list;
            this.lease_tenant_state = response.data.lease_tenant_state;
            this.suburbFilteredList(this.lease_tenant_state.field_key);
            this.postcodeFilteredList(this.lease_tenant_state.field_key);
            this.lease_tenant_state_old = response.data.lease_tenant_state;
            this.lease_tenant_state_list = response.data.lease_tenant_state_list;
            this.lease_tenant_state_list_old = response.data.lease_tenant_state_list;
            this.lease_mail_setting_country = response.data.lease_mail_setting_country;
            this.lease_mail_setting_country_old = response.data.lease_mail_setting_country;
            // this.lease_mail_setting_country_list = response.data.lease_mail_setting_country_list;
            // this.lease_mail_setting_country_list_old = response.data.lease_mail_setting_country_list;
            this.lease_mail_setting_state = response.data.lease_mail_setting_state;
            // if(this.lease_mail_setting_state === '') this.lease_mail_setting_state = 'WA';
            this.suburbMailFilteredList(this.lease_mail_setting_state.field_key);
            this.postcodeMailFilteredList(this.lease_mail_setting_state.field_key);
            this.lease_mail_setting_state_old = response.data.lease_mail_setting_state;
            this.lease_mail_setting_stateList = response.data.lease_mail_setting_stateList;
            this.lease_mail_setting_stateList_old = response.data.lease_mail_setting_stateList;

            this.debtor_in_ar_count = response.data.debtor_in_ar_count;
            this.lease_tenant_crn = response.data.lease_tenant_crn;
            this.lease_tenant_crn_old = response.data.lease_tenant_crn;

            this.lease_commencement_date = response.data.lease_commencement_date;
            this.lease_commencement_date_old = response.data.lease_commencement_date;
            this.lease_expiry_date = response.data.lease_expiry_date;
            this.lease_expiry_date_old = response.data.lease_expiry_date;

            if (this.lease_company_code !== '') {
                this.company_type = 1;
                this.company_existed = true;
            } else {
                this.company_type = 0;
            }
            // this.company_type = response.data.company_type;
            // this.lease_charge_interest_account_list = response.data.account_income_list_v2;
            // this.lease_type_list = response.data.lease_type_list;
            // this.lease_tenant_type_list = response.data.lease_tenant_type_list;
            // this.lease_division_list = response.data.lease_division_list;
            // this.lease_retail_category_list = response.data.lease_retail_category_list;
            // this.lease_retail_sub_category_list = response.data.lease_retail_sub_category_list;
            // this.lease_retail_fine_category_list = response.data.lease_retail_fine_category_list;
            // this.lease_bond_deposit_property_list = response.data.lease_bond_deposit_property_list;
            this.lease_bond_deposit_lease_list = response.data.lease_bond_deposit_lease_list;

            // this.lease_company = this.getValueInList(response.data.lease_company_code, this.company_list);
            // this.company_code_old = this.getValueInList(response.data.lease_company_code, this.company_list);

            this.lease_type = response.data.lease_type;
            this.lease_type_old = response.data.lease_type;
            this.lease_tenant_type = response.data.lease_tenant_type;
            this.lease_tenant_type_old = response.data.lease_tenant_type;
            this.lease_division = response.data.lease_division;
            this.lease_division_old = response.data.lease_division;
            this.lease_state = this.getValueInList(
                response.data.lease_state,
                this.getDDCountryStates(this.lease_tenant_country.field_key),
            );
            this.lease_state_old = this.getValueInList(
                response.data.lease_state,
                this.getDDCountryStates(this.lease_tenant_country.field_key),
            );
            // this.lease_tenant_state = this.getValueInList(response.data.lease_tenant_state.field_key, this.getDDCountryStates(this.lease_tenant_country.field_key));
            // this.lease_tenant_state_old = this.getValueInList(response.data.lease_state, this.getDDCountryStates(this.lease_tenant_country.field_key));
            this.lease_retail_category = response.data.lease_retail_category;
            this.lease_retail_category_old = response.data.lease_retail_category;
            this.lease_retail_sub_category = response.data.lease_retail_sub_category;
            this.lease_retail_sub_category_old = response.data.lease_retail_sub_category;
            this.lease_retail_fine_category = response.data.lease_retail_fine_category;
            this.lease_retail_fine_category_old = response.data.lease_retail_fine_category;

            this.lease_bond_deposit_property = response.data.lease_bond_deposit_property;
            this.lease_bond_deposit_property_old = response.data.lease_bond_deposit_property;
            this.lease_bond_deposit_lease = response.data.lease_bond_deposit_lease;
            this.lease_bond_deposit_lease_old = response.data.lease_bond_deposit_lease;

            // this.lease_charge_interest_account = response.data.lease_charge_interest_account;
            // this.lease_charge_interest_account_old = response.data.lease_charge_interest_account;

            this.lease_charge_interest_account = this.getValueInList(
                response.data.lease_charge_interest_account,
                this.dd_account_income_ungrouped_list,
            );
            this.lease_charge_interest_account_old = this.lease_charge_interest_account;

            let lease_charge_interest_incur_account = response.data.lease_charge_interest_incur_account;

            if (this.new_lease) {
                let new_code = this.getValueInList($('#lease_code').val(), this.company_list);
                if (new_code.value != '') {
                    this.lease_company = new_code;
                    this.company_type = 1;
                }
            }
            this.lease_charge_interest_incur_account = [];
            for (let z = 0; z < lease_charge_interest_incur_account.length; z++) {
                for (let y = 0; y < this.dd_account_income_ungrouped_list.length; y++) {
                    if (this.dd_account_income_ungrouped_list[y].field_key === lease_charge_interest_incur_account[z]) {
                        let field_key = this.dd_account_income_ungrouped_list[y].field_key;
                        let field_value = this.dd_account_income_ungrouped_list[y].field_value;
                        this.lease_charge_interest_incur_account.push({
                            fieldKey: field_key,
                            fieldValue: field_value,
                            field_key: field_key,
                            field_value: field_value,
                            value: field_key,
                            label: field_value,
                        });
                    }
                }
            }

            this.loading_setting = false;
        },
        loadState: function (country_code) {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('countryCode', country_code);
            form_data.append('no_load', true);
            this.$api.post('loadAPIStatesDropDownList', form_data).then((response) => {
                // this.lease_tenant_state = response.data.defaultData;
                if (!this.edit_form) {
                    this.lease_tenant_state = this.lease_tenant_state_old;
                    this.suburbFilteredList(this.lease_tenant_state);
                    this.postcodeFilteredList(this.lease_tenant_state);
                    this.suburbMailFilteredList(this.lease_tenant_state);
                    this.postcodeMailFilteredList(this.lease_tenant_state);
                    this.lease_tenant_state_list = this.lease_tenant_state_list_old;
                }
                this.lease_tenant_state_list = response.data.stateList;
                // this.loading_setting = false;
            });
        },
        loadStateMail: function (country_code) {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('countryCode', country_code);
            form_data.append('no_load', true);
            this.$api.post('loadAPIStatesDropDownList', form_data).then((response) => {
                // this.lease_mail_setting_state = response.data.defaultData;
                if (!this.edit_form) {
                    // this.lease_mail_setting_state = this.lease_mail_setting_state_old;
                    this.lease_mail_setting_stateList = this.lease_mail_setting_stateList_old;
                }
                this.lease_mail_setting_stateList = response.data.stateList;
                // this.loading_setting = false;
            });
        },
        loadBondLeaseList: function () {
            if (this.lease_bond_deposit_property !== '') {
                var form_data = new FormData();
                form_data.append('property_code', this.lease_bond_deposit_property);
                form_data.append('no_load', true);
                this.$api.post('load-property-lease-list', form_data).then((response) => {
                    this.lease_bond_deposit_lease_list = response.data.data;
                    this.refresh_index = Math.random();
                    if (this.lease_bond_deposit_property_old !== '') {
                        if (this.lease_bond_deposit_property !== this.lease_bond_deposit_property_old) {
                            this.lease_bond_deposit_lease = '';
                        }
                    }
                });
            }
        },
        loadCountryList: function () {
            var form_data = new FormData();
            form_data.append('propertyID', this.lease_bond_deposit_property);
            form_data.append('no_load', true);
            this.$api.post('loadAPICountriesDropDownListRedis', form_data).then((response) => {
                this.lease_tenant_country = response.data.defaultData;
                this.lease_tenant_country_old = response.data.defaultData;

                this.lease_mail_setting_country = response.data.defaultData;
                this.lease_mail_setting_country_old = response.data.defaultData;
            });
        },
        loadCompanyList: function () {
            this.company_list = this.dd_company_list;
        },
        checkCompanyCode: function () {
            this.company_existed = false;
            this.create_new_company_btn = false;
            this.error_msg = [];
            this.error_server_msg2 = [];
            let new_company_code = this.new_company_code.toUpperCase().trim();
            this.new_company_code = new_company_code;
            if (new_company_code.length > 10) {
                this.error_server_msg2.push(['Company code allows 10 characters only']);
            } else if (new_company_code === '') {
                this.error_server_msg2.push(['Please input a valid company code']);
            }
            if (this.error_server_msg2.length <= 0) {
                this.loading_page_setting = true;
                var form_data = new FormData();
                form_data.append('new_company_code', new_company_code);
                form_data.append('no_load', true);
                this.$api.post('company/check-company-code-if-exist', form_data).then((response) => {
                    this.error_server_msg2 = response.data.validation_errors;
                    this.company_existed = response.data.company_existed;
                    this.loading_page_setting = false;
                    this.create_new_company_btn = true;
                });
            }
        },
        loadCompanyDetails: function () {
            this.error_msg = [];
            this.error_server_msg2 = [];
            let lease_company_code = this.lease_company.field_key;
            if (lease_company_code !== '') {
                var form_data = new FormData();
                form_data.append('company_code', lease_company_code);
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('no_load', true);
                this.$api.post('company/load-company-details', form_data).then((response) => {
                    this.new_company_code = response.data.company_code;

                    this.lease_tenant_name = response.data.tenant_name;
                    this.lease_tenant_name_old = response.data.tenant_name;
                    this.lease_tenant_address_1 = response.data.tenant_address_1;
                    this.lease_tenant_address_1_old = response.data.tenant_address_1;
                    this.lease_tenant_address_2 = response.data.tenant_address_2;
                    this.lease_tenant_address_2_old = response.data.tenant_address_2;
                    this.lease_tenant_suburb = response.data.tenant_suburb;
                    this.lease_tenant_suburb_old = response.data.tenant_suburb;
                    this.lease_tenant_post_code = response.data.tenant_post_code;
                    this.lease_tenant_post_code_old = response.data.tenant_post_code;
                    this.lease_tenant_email = response.data.tenant_email;
                    this.lease_tenant_abn = response.data.tenant_gst_no;
                    this.email_centralisation_setting = response.data.email_centralisation_setting;
                    this.lease_tenant_email_old = response.data.tenant_email;

                    this.lease_tenant_country = response.data.tenant_country;
                    this.lease_tenant_state = response.data.tenant_state;
                    this.suburbFilteredList(this.lease_tenant_state.field_key);
                    this.postcodeFilteredList(this.lease_tenant_state.field_key);
                    this.suburbMailFilteredList(this.lease_tenant_state.field_key);
                    this.postcodeMailFilteredList(this.lease_tenant_state.field_key);
                    this.lease_tenant_state_list = response.data.tenant_state_list;

                    let lease_primary_lease_index = response.data.lease_primary_lease_index;
                    let lease_existed = response.data.lease_existed;
                    let is_main_lease = response.data.is_main_lease;
                    if (lease_existed) {
                        this.is_main_lease_enabled = true;
                        this.is_main_lease_index = parseInt(lease_primary_lease_index);
                    } else {
                        if (is_main_lease) {
                            this.is_main_lease_enabled = false;
                            this.is_main_lease_index = 0;
                        } else {
                            this.is_main_lease_enabled = true;
                            this.is_main_lease_index = 1;
                        }
                    }

                    this.loading_page_setting = false;
                });
            }
        },
        showGeneralTour: function () {
            this.$tours['generalDetailTour'].start();
        },
        showTenantTour: function () {
            this.$tours['tenantDetailTour'].start();
        },
        isEditable: function () {
            if (this.new_lease) {
                return (this.edit_form = true);
            } else {
                return !this.read_only &&
                    !this.formSectionReadOnly(this.pm_lease_form_read_only, this.form_type, this.form_section)
                    ? (this.edit_form = true)
                    : (this.edit_form = false);
            }
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        goToShortcut: function (parameter, code1 = '', code2 = '') {
            let property_code = '';
            let lease_code = '';
            let is_ledger_flag = 0;
            switch (parameter) {
                case 'property':
                    property_code = this.property_code.field_key;

                    if (code1 !== '') {
                        property_code = code1;
                        is_ledger_flag = this.getLedgerFlag(property_code, this.dd_bond_deposit_property_list);
                    }
                    if (is_ledger_flag === '1') {
                        if (this.user_type !== 'A')
                            window.open(
                                '?module=salesTrust&command=manage_ledger_v2&ledger_code=' + property_code,
                                '_blank',
                            );
                        else
                            window.open(
                                '?module=salesTrust&command=manage_ledger_v2&ledger_code=' + property_code,
                                '_blank',
                            );
                    } else {
                        if (this.user_type !== 'A')
                            window.open(
                                '?module=properties&command=property_summary_page&property_code=' + property_code,
                                '_blank',
                            );
                        else
                            window.open(
                                '?module=properties&command=v2_manage_property_page&property_code=' + property_code,
                                '_blank',
                            );
                    }
                    break;
                case 'lease':
                    lease_code = this.lease_code;
                    property_code = this.property_code;
                    if (code1 !== '') {
                        property_code = code1;
                        is_ledger_flag = this.getLedgerFlag(property_code, this.dd_bond_deposit_property_list);
                    }
                    if (code2 !== '') lease_code = code2;
                    if (property_code !== '' && lease_code !== '') {
                        // property_code = code1;
                        // lease_code = code2;
                        if (is_ledger_flag == '1') {
                            if (this.user_type === 'A')
                                window.open(
                                    '?module=salesTrust&command=manage_ledger_v2&ledger_code=' +
                                        property_code +
                                        '&sub_ledger_code=' +
                                        lease_code +
                                        '#sub-ledger-section',
                                    '_blank',
                                );
                            else
                                window.open(
                                    '?module=salesTrust&command=manage_ledger_v2&ledger_code=' +
                                        property_code +
                                        '&sub_ledger_code=' +
                                        lease_code +
                                        '#sub-ledger-section',
                                    '_blank',
                                );
                        } else {
                            if (this.user_type === 'A')
                                window.open(
                                    '?module=leases&command=lease_page_v2&property_code=' +
                                        property_code +
                                        '&lease_code=' +
                                        lease_code,
                                    '_blank',
                                );
                            else
                                window.open(
                                    '?module=leases&command=lease_summary_page&property_code=' +
                                        property_code +
                                        '&lease_code=' +
                                        lease_code,
                                    '_blank',
                                );
                        }
                    }
                    break;
                case 'company':
                    window.open(
                        '?module=companies&command=company_v2&companyID=' +
                            this.lease_company.field_key +
                            '&propertyID=' +
                            this.property_code +
                            '&leaseID=' +
                            this.lease_code,
                    );
                    break;
                case 'lease_abstract':
                    this.loading_page_setting = true;
                    var today = new Date();
                    var dd = today.getDate();
                    var mm = today.getMonth() + 1;
                    var yyyy = today.getFullYear();
                    var single_date = yyyy + '-' + mm + '-' + dd;
                    var form_data = new FormData();
                    form_data.append('properties', this.property_code);
                    form_data.append('leases', this.lease_code);
                    form_data.append('no_load', true);
                    form_data.append('user_type', localStorage.getItem('user_type'));
                    if (sessionStorage.getItem('sso_key'))
                        form_data.append('app_key', sessionStorage.getItem('sso_key'));
                    form_data.append('report_ids', '21');
                    form_data.append('singleDate', single_date);
                    form_data.append('format', 'pdf');
                    axios.post(this.cirrus8_api_url + 'api/reports/download/pdf', form_data).then((response) => {
                        this.loading_page_setting = false;
                        let blob = new Blob([response.data], { type: 'application/pdf' });
                        let a = document.createElement('a');
                        a.style = 'display: none';
                        document.body.appendChild(a);
                        let url = window.URL.createObjectURL(blob);
                        a.href = url;
                        var fileName = 'Lease_Abstract';
                        a.download = fileName + '.pdf';
                        a.click();
                    });
                    break;
                case 'tenant_activity_current_month':
                    window.open(
                        '?module=managementReports&command=tenantActivity&propertyID=' +
                            this.property_code +
                            '&tenantID=' +
                            this.lease_code +
                            '',
                        '_blank',
                    );
                    break;
                case 'tenant_activity_all_date':
                    window.open(
                        '?module=managementReports&command=tenantActivity&propertyID=' +
                            this.property_code +
                            '&tenantID=' +
                            this.lease_code +
                            '&api_allDates',
                        '_blank',
                    );
                    break;
                case 'view_tax_invoice':
                    window.open(
                        '?module=ar&command=viewTaxInvoice&propertyID=' +
                            this.property_code +
                            '&leaseID=' +
                            this.lease_code +
                            '',
                        '_blank',
                    );
                    break;
                case 'generate_arrears_statement_download':
                    this.loading_page_setting = true;
                    let url =
                        '?module=ar&command=generateArrearsStatement&action=dlLetterSingle_' +
                        this.property_code +
                        '~' +
                        this.lease_code +
                        '';
                    var form_data = new FormData();
                    form_data.append('showCurrentOnly', 'Yes');
                    form_data.append('includeCredits', 'Yes');
                    form_data.append('ccMe', '1');
                    form_data.append('exclude_letter', 'true');
                    form_data.append('property', this.property_code);
                    form_data.append('tenantID', this.lease_code);
                    form_data.append('app_origin', 'lease_page');
                    this.$api.post(url, form_data).then((response) => {
                        this.loading_page_setting = false;
                        document.location.href = 'download.php?fileID=' + response.data;
                    });
                    break;
                case 'print':
                    window.open(
                        '?module=leases&command=lease_print_page&is_live=1&property_code=' +
                            this.property_code +
                            '&lease_code=' +
                            this.lease_code +
                            '&version_id=' +
                            this.version_id,
                        '_blank',
                    );
                    break;
            }
        },
        diariseExpiryDate: function () {
            if (this.lease_expiry_date === '' && !this.new_lease) return;

            this.loading_page_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('lease_expiry_date', this.lease_expiry_date);
            form_data.append('lease_expiry_date_old', this.lease_expiry_date_old);
            form_data.append('version_id', this.version_id);
            form_data.append('app_origin', 'lease_page');
            let api_url = 'temp/lease/create/diarise-expiry-date';
            if (this.isLeaseFormLive()) {
                api_url = 'lease/create/diarise-expiry-date';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.loading_page_setting = false;
                if (!response.data.no_changes) {
                    bus.$emit('loadLeaseDiarySection');
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Successfully Diarised!',
                        icon_show: true,
                    };
                    cirrusDialog(dialog_prop);
                }
            });
        },
        diariseLeaseOption: function () {
            if (this.lease_option === '') return;

            this.loading_page_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('lease_option', this.lease_option);
            form_data.append('lease_option_old', this.lease_option_old);
            form_data.append('app_origin', 'lease_page');
            let api_url = 'temp/lease/create/diarise-lease-option';
            if (this.isLeaseFormLive()) {
                api_url = 'lease/create/diarise-lease-option';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.loading_page_setting = false;
                if (!response.data.no_changes) {
                    bus.$emit('loadLeaseDiarySection');
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Successfully Diarised!',
                        icon_show: true,
                    };
                    cirrusDialog(dialog_prop);
                }
            });
        },
        suburbSelected(data) {
            // lease_tenant_state
            if (this.$refs.refLeaseTenantSuburb.selectedValues.length == 0) {
                if (data) {
                    this.lease_tenant_suburb = data.suburb;
                    this.lease_tenant_post_code = data.pcode;
                } else {
                    this.lease_tenant_suburb = this.lease_tenant_suburb;
                }
            } else {
                if (data) {
                    this.lease_tenant_suburb = data.suburb;
                    this.lease_tenant_post_code = data.pcode;
                }
            }
        },
        postcodeSelected(data) {
            // lease_tenant_state
            if (this.$refs.refLeaseTenantSuburb.selectedValues.length == 0) {
                if (data) {
                    this.lease_tenant_suburb = data.suburb;
                    this.lease_tenant_post_code = data.pcode;
                    // let state = data.State;
                    // this.lease_tenant_state = this.getValueInList(state, this.getDDCountryStates(this.lease_tenant_country.field_key));
                } else {
                    this.lease_tenant_suburb = this.lease_tenant_suburb;
                }
            } else {
                if (data) {
                    this.lease_tenant_suburb = data.suburb;
                    this.lease_tenant_post_code = data.pcode;
                    // let state = data.State;
                    // this.lease_tenant_state = this.getValueInList(state, this.getDDCountryStates(this.lease_tenant_country.field_key));
                }
            }
        },
        suburbMailSelected(data) {
            // lease_tenant_state
            if (this.$refs.refLeaseTenantMailSuburb.selectedValues.length == 0) {
                if (data) {
                    this.lease_mail_setting_suburb = data.suburb;
                    this.lease_mail_setting_post_code = data.pcode;
                } else {
                    this.lease_mail_setting_suburb = this.lease_mail_setting_suburb;
                }
            } else {
                if (data) {
                    this.lease_mail_setting_suburb = data.suburb;
                    this.lease_mail_setting_post_code = data.pcode;
                }
            }
        },
        postcodeMailSelected(data) {
            // lease_tenant_state
            if (this.$refs.refLeaseTenantMailSuburb.selectedValues.length == 0) {
                if (data) {
                    this.lease_mail_setting_suburb = data.suburb;
                    this.lease_mail_setting_post_code = data.pcode;
                    // let state = data.State;
                    // this.lease_tenant_state = this.getValueInList(state, this.getDDCountryStates(this.lease_tenant_country.field_key));
                } else {
                    this.lease_mail_setting_suburb = this.lease_mail_setting_suburb;
                }
            } else {
                if (data) {
                    this.lease_mail_setting_suburb = data.suburb;
                    this.lease_mail_setting_post_code = data.pcode;
                    // let state = data.State;
                    // this.lease_tenant_state = this.getValueInList(state, this.getDDCountryStates(this.lease_tenant_country.field_key));
                }
            }
        },
        suburbFilteredList(stateVal) {
            this.suburb_list_filtered = [];
            let filteredItem = [];

            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.suburb_list_filtered = filteredItem;
        },
        postcodeFilteredList(stateVal) {
            this.postcode_list_filtered = [];
            let filteredItem = [];
            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.postcode_list_filtered = filteredItem;
        },
        suburbMailFilteredList(stateVal) {
            this.suburb_mail_list_filtered = [];
            let filteredItem = [];
            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.suburb_mail_list_filtered = filteredItem;
        },
        postcodeMailFilteredList(stateVal) {
            this.postcode_mail_list_filtered = [];
            let filteredItem = [];
            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.postcode_mail_list_filtered = filteredItem;
        },
        tenantStateChanged: function () {
            if (this.lease_tenant_state) {
                this.suburbFilteredList(this.lease_tenant_state.field_key);
                this.postcodeFilteredList(this.lease_tenant_state.field_key);
            } else {
                this.suburb_list_filtered = [];
                this.postcode_list_filtered = [];
            }
        },
        mailStateChanged: function () {
            if (this.lease_mail_setting_state && this.lease_mail_setting_state !== null) {
                this.suburbMailFilteredList(this.lease_mail_setting_state.field_key);
                this.postcodeMailFilteredList(this.lease_mail_setting_state.field_key);
            } else {
                this.suburb_mail_list_filtered = [];
                this.postcode_mail_list_filtered = [];
            }
        },
        showNewBondLease: function () {
            //
            this.loading_page_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.lease_bond_deposit_property);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('app_origin', 'lease_page');
            let api_url = 'temp/lease/fetch/bond-new-lease-code';
            if (this.isLeaseFormLive()) {
                api_url = 'lease/fetch/bond-new-lease-code';
            }
            this.$api.post(api_url, form_data).then((response) => {
                this.loading_page_setting = false;

                this.show_bond_lease_modal = true;
                this.new_bond_lease_code = response.data.new_bond_lease_code;
                this.new_bond_lease_name = 'Bond - ' + this.lease_name;
            });
        },
        modalSubmitNewBond: function () {
            this.loading_page_setting = true;
            let validate = true;

            // Validate if Lease/Sub Ledger Code has special character
            if (this.verifyCode(this.new_bond_lease_code)) {
                this.$noty.error('Please use only alphanumeric characters for the bond/deposit lease code.');
                validate = false;
            }

            if (validate) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('bond_property_code', this.lease_bond_deposit_property);
                form_data.append('new_bond_lease_code', this.new_bond_lease_code.toUpperCase().trim());
                form_data.append('new_bond_lease_name', this.new_bond_lease_name);
                form_data.append('version_id', this.version_id);
                form_data.append('app_origin', 'lease_page');
                let api_url = 'temp/lease/create/bond-new-lease-code';
                if (this.isLeaseFormLive()) {
                    api_url = 'lease/create/bond-new-lease-code';
                }
                this.$api.post(api_url, form_data).then((response) => {
                    this.loading_page_setting = false;
                    this.show_bond_lease_modal = false;
                    //
                    let error_server_msg2 = response.data.error_server_msg;
                    this.error_server_msg2 = error_server_msg2;
                    if (error_server_msg2.length === 0) {
                        this.loadBondLeaseList();
                        this.lease_bond_deposit_lease = this.new_bond_lease_code;
                    }
                });
            }
        },
        lookupABN: function (e) {
            if (this.lease_tenant_country.field_key == 'AU') {
                let button = $(e.currentTarget);
                button.attr('disabled', 'disabled');
                this.abn_button_label = '';
                this.abn_button_loading = true;

                let form_data = new FormData();
                form_data.append('company_code', this.lease_company.field_key);
                form_data.append('company_gst_no', this.lease_tenant_abn);
                form_data.append('company_country', this.lease_tenant_country.field_key);
                form_data.append('no_load', true);

                let api_url = 'company/lookup-abn';
                this.abn_detail_display = true;

                //Reset first the ABN Result
                this.abn_message = '';
                this.abn_details = false;
                this.abn_comp_name = '';
                this.abn_number = '';
                this.abn_value = '';
                this.abn_comp_type = '';
                this.abn_gst_status = '';
                this.abn_state_code = '';
                this.abn_post_code = '';

                this.$api.post(api_url, form_data).then((response) => {
                    let abnData = response.data;

                    //Reset Company Name lookup variables
                    this.company_match_list_display = false;
                    this.company_match_list_count = 0;
                    this.company_match_list_message = '';
                    this.dd_company_match_list = [];

                    this.abn_message = abnData.hasOwnProperty('message') ? abnData.message : '';

                    if (abnData.hasOwnProperty('data') && Object.keys(abnData.data).length != 0) {
                        this.abn_details = true;
                        this.abn_comp_name = abnData.data.name;
                        this.abn_value = abnData.data.abn;
                        this.abn_acn = abnData.data.acn;
                        this.abn_comp_type = abnData.data.company_type;
                        this.abn_gst_status = abnData.data.taxStatus;
                        this.abn_gst = abnData.data.gst;
                        this.abn_state_code = abnData.data.state;
                        this.abn_post_code = abnData.data.postal;
                        this.abn_status = abnData.data.abn_status;

                        if (this.abn_status == 'Cancelled') {
                            this.abn_message = 'Selected ABN is cancelled';
                        }
                    }
                    button.removeAttr('disabled');
                    this.abn_button_label = 'Look up ABN';
                    this.abn_button_loading = false;
                });
            }
        },
        loadCountryDefaults: function (type, country) {
            // this.loading_page_setting = true;
            var form_data = new FormData();
            if (type != 'general') form_data.append('country', country);

            let api_url = this.cirrus8_api_url + 'admin/country_defaults/load';
            this.$api.post(api_url, form_data).then((response) => {
                this.error_server_msg2 = response.data.validation_errors;
                // this.loading_page_setting = false;
                if (type == 'tenant') {
                    //this.tenant_country_defaults = response.data.default;
                    this.cdf_for_tenant_address = response.data.default;

                    this.getDDCountryStates(country);
                    this.tenantStateChanged();
                } else if (type == 'mail') {
                    this.mail_country_defaults = response.data.default;
                    this.mailStateChanged();
                } else {
                    this.tenant_country_defaults = response.data.default;
                }
            });
        },
        triggerCountryChange: function (type) {
            if (type == 'tenant') {
                if (
                    this.lease_tenant_state &&
                    this.lease_tenant_country.fieldKey != this.lease_tenant_state.countryCode
                ) {
                    this.resetFormDetails('tenant');
                }
                this.abn_details = false;
                this.abn_message = '';
            } else {
                if (
                    this.lease_mail_setting_state &&
                    this.lease_mail_setting_country.fieldKey != this.lease_mail_setting_state.countryCode
                ) {
                    this.resetFormDetails('mail');
                }
            }
        },
        resetFormDetails: function (type) {
            if (type == 'tenant') {
                this.lease_tenant_state = null;
                this.lease_tenant_suburb = null;
                this.lease_tenant_post_code = null;
                this.searchLeaseTenantSuburb = null;
                this.searchLeaseTenantPostcode = null;
            } else {
                this.lease_mail_setting_state = null;
                this.lease_mail_setting_suburb = '';
                this.lease_mail_setting_post_code = '';
                this.searchLeaseTenantMailSuburb = '';
                this.searchLeaseTenantMailPostcode = '';
            }
        },
        getDropdownName(code, options) {
            let label = code;
            options.forEach((row) => {
                if (row.field_key.trim() === code.trim()) label = row.field_value;
            });
            return label;
        },
        getLedgerFlag(code, options) {
            let is_ledger_flag = '0';
            options.forEach((row) => {
                if (row.field_key.trim() === code.trim()) is_ledger_flag = row.is_ledger_flag;
            });
            return is_ledger_flag;
        },
        showLeaseTermLog: function () {
            this.comShowFormFlag = false;
            this.showComActivityLogModal = true;
            this.clearLeaseTermLogForm();
            this.loadLeaseTermLog();
        },
        showEditLeaseTermLog: function (data) {
            this.comShowFormFlag = true;
            this.leaseTermLogId = data.id;
            this.logLeaseCommencementDate = data.commencementDate;
            this.logLeaseExpiryDate = data.expiryDate;
            this.logLeaseOption = data.leaseOption;
        },
        loadLeaseTermLog: function () {
            this.logErrorServerMsg = [];
            const formData = new FormData();
            formData.append('propertyCode', this.property_code);
            formData.append('leaseCode', this.lease_code);
            formData.append('no_load', true);

            let apiUrl = 'lease-term-log/fetch/list';

            this.$api.post(apiUrl, formData).then((response) => {
                this.sortLeaseTermLog(response.data.leaseTermLogList); // Updated response key
            });
        },
        sortLeaseTermLog: function (list) {
            const systemLeaseTermLog = {
                id: 0,
                itemNo: 'SYSTEM',
                isSystem: true,
                commencementDate: this.lease_commencement_date,
                expiryDate: this.lease_expiry_date,
                leaseOption: this.lease_option,
            };
            this.leaseTermLogList = [...list, systemLeaseTermLog]
                .map((obj) => {
                    return {
                        ...obj,
                        isSystem: obj.isSystem ?? false,
                    };
                })
                .toSorted((a, b) => {
                    const commencementDateA = this.parseDate(a.commencementDate);
                    const commencementDateB = this.parseDate(b.commencementDate);

                    if (commencementDateA - commencementDateB !== 0) {
                        return commencementDateA - commencementDateB;
                    }
                    const expiryDateA = this.parseDate(a.expiryDate);
                    const expiryDateB = this.parseDate(b.expiryDate);
                    return expiryDateA - expiryDateB;
                });
        },
        async deleteLeaseTermLog(id) {
            const dialogProps = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };

            const result = await cirrusDialog(dialogProps);
            if (result === 1) {
                const formData = new FormData();
                formData.append('id', id);
                let apiUrl = 'lease-term-log/delete';
                this.$api.post(apiUrl, formData).then(() => {
                    this.loadLeaseTermLog();
                });
            }
        },
        saveLeaseTermLog: function () {
            this.logErrorServerMsg = [];
            const formData = new FormData();
            formData.append('propertyCode', this.property_code);
            formData.append('leaseCode', this.lease_code);

            if (this.leaseTermLogId) {
                formData.append('id', this.leaseTermLogId);
            }
            formData.append('commencementDate', this.logLeaseCommencementDate);
            formData.append('expiryDate', this.logLeaseExpiryDate);
            formData.append('leaseOption', this.logLeaseOption);

            let apiUrl = 'lease-term-log/create';
            if (this.leaseTermLogId) {
                apiUrl = 'lease-term-log/update';
            }

            this.$api
                .post(apiUrl, formData)
                .then(() => {
                    this.comShowFormFlag = false;
                    this.clearLeaseTermLogForm();
                    this.loadLeaseTermLog();
                })
                .catch((error) => {
                    if (error.response && error.response.status === 422) {
                        const message = error.response.data.message;
                        this.logErrorServerMsg.push([message]);
                    }
                });
        },
        clearLeaseTermLogForm: function () {
            this.leaseTermLogId = null;
            this.logLeaseCommencementDate = this.dateToday();
            this.logLeaseExpiryDate = '31/12/2999';
            this.logLeaseOption = '';
        },
        addLeaseTermLogForm: function () {
            this.comShowFormFlag = true;
            this.clearLeaseTermLogForm();
        },
        cancelLeaseTermLogForm: function () {
            this.comShowFormFlag = false;
            this.clearLeaseTermLogForm();
        },
    },
    watch: {
        date(val) {
            this.dateFormatted = this.formatDate(this.date);
        },
        edit_form: function () {
            if (this.edit_form) {
                let step_array = [
                    {
                        target: '.v-step-option',
                        content: `Create <strong>New</strong> lease <br> Shows the form for new lease`,
                    },
                    {
                        target: '.v-step-shortcut',
                        content: 'Use this to jump between lease sections',
                    },
                    {
                        target: '.v-step-form-tab',
                        content: 'Use this to navigate between lease forms sections',
                    },
                    {
                        target: '.v-step-revert-button',
                        content: 'Use this to revert the form changes to the last saved data',
                    },
                    {
                        target: '.v-step-save-1-button',
                        content: 'Use this save the form',
                    },
                    {
                        target: '.v-step-save-2-button',
                        content: 'You can use this too to save the form',
                    },
                    {
                        target: '.v-step-refresh-button',
                        content: 'Use this to refresh the form section',
                    },
                    {
                        target: '.v-step-final-message',
                        content: 'You have finish the page tour.',
                    },
                ];
                this.SET_TOUR_STEPS(step_array);
            }
            bus.$emit('changeEditMode', this.edit_form);
        },
        lease_company_code: function () {
            // this.loadCompanyDetails();
            this.loadCompanyList();
        },
        lease_company: function () {
            // this.loadCompanyDetails();
            this.loadCompanyList();
        },
        force_load: function () {
            this.loadForm();
        },
        property_code: function () {
            this.loadForm();
            if (this.property_code !== '') {
                let step_array = [
                    {
                        target: '.v-step-option',
                        content: `Create <strong>New</strong> lease <br> Shows the form for new lease`,
                    },
                    {
                        target: '.v-step-shortcut',
                        content: 'Use this to jump between lease sections',
                    },
                    {
                        target: '.v-step-form-tab',
                        content: 'Use this to navigate between lease forms sections',
                    },
                    {
                        target: '.v-step-edit-button',
                        content: 'Use this if you want to edit the form',
                    },
                    {
                        target: '.v-step-final-message',
                        content: 'You have finish the page tour.',
                    },
                ];
                this.SET_TOUR_STEPS(step_array);
            }
        },
        lease_code: function () {
            this.loadForm();
        },
        company_type: function () {
            if (this.company_type === 1) {
                this.company_existed = true;
            }
        },
        lease_status_index: function () {
            if (this.lease_status_index !== this.lease_status_index_old && !this.new_lease) {
                if (this.lease_status_index === 1) {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: 'you need to vacate the tenant at the approriate date using the vacate lease button under unit details section. Click Yes to continue with the change or No to revert back to the original setting.',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Yes, change it!',
                        cancelButtonText: 'No, cancel it',
                    }).then((result) => {
                        if (result.value) {
                            this.lease_status_index = 1;
                        } else {
                            this.lease_status_index = 0;
                        }
                    });
                } else {
                    Swal.fire({
                        title: 'Are you sure?',
                        text: 'you need to ensure the tenant is occupying a unit at the property level before effecting this change. Click Yes to continue with the change or No to revert back to the original setting.',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Yes, change it!',
                        cancelButtonText: 'No, cancel it',
                    }).then((result) => {
                        if (result.value) {
                            this.lease_status_index = 0;
                        } else {
                            this.lease_status_index = 1;
                        }
                    });
                }
            }
        },
        lease_tenant_country: function (newVal, oldVal) {
            // console.log(newVal, oldVal);
            // this.loadState(this.lease_tenant_country.value);
            this.loadCountryDefaults('tenant', this.lease_tenant_country.value);
        },
        lease_mail_setting_country: function () {
            this.loadCountryDefaults('mail', this.lease_mail_setting_country.value);
        },
        lease_bond_deposit_property: function (newVal, oldVal) {
            if (newVal !== oldVal) {
                this.loadBondLeaseList();
            }
        },
        lease_tenant_suburb: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchLeaseTenantSuburb;
                this.lease_tenant_suburb = newVal;
            }
        },
        lease_tenant_post_code: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchLeaseTenantPostcode;
                this.lease_tenant_post_code = newVal;
            }
        },
        lease_mail_setting_suburb: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchLeaseTenantMailSuburb;
                this.lease_mail_setting_suburb = newVal;
            }
        },
        lease_mail_setting_post_code: function (newVal, oldVal) {
            if (newVal == undefined) {
                newVal = this.searchLeaseTenantMailPostcode;
                this.lease_mail_setting_post_code = newVal;
            }
        },
        lease_retail_category: function (newVal, oldVal) {
            if (newVal !== oldVal && this.edit_form) {
                this.lease_retail_sub_category = '';
                this.lease_retail_fine_category = '';
            }
        },
    },
    created() {
        bus.$on('loadLeaseMainFormSection', (data) => {
            this.loadForm();
        });
        bus.$on('triggerDoubleClick', (data) => {
            this.doubleClickForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
