<template>
    <div class="c8-page">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Lease Profile</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-server-error :errorMsg2="error_server_msg2"></cirrus-server-error>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>

        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <v-tabs
                vertical
                v-model="profile_tab"
            >
                <v-tab
                    v-for="(year_data, year_index) in lease_profile_year_list"
                    :key="year_index"
                >
                    <v-icon left> mdi-calendar-text </v-icon>
                    {{ year_data.year_from }} - {{ year_data.year_to }}
                </v-tab>
                <v-tab class="text-success">
                    <v-icon left> mdi-calendar-plus </v-icon>
                    New Profile
                </v-tab>

                <v-tab-item
                    v-for="(year_data2, year_index2) in lease_profile_year_list"
                    :key="year_index2"
                >
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class=""
                        >
                            <div class="ui mini statistic">
                                <div class="label">Year Range</div>
                                <div class="value">{{ year_data2.year_from }} - {{ year_data2.year_to }}</div>
                            </div>
                        </v-col>
                    </v-row>

                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class="form-subheader"
                            ><strong>Management Fee Profile</strong></v-col
                        >
                    </v-row>
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Management fee Allocation type:</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <multiselect
                                v-model="year_data2.man_fee_allocation_type"
                                :options="man_fee_allocation_type_list"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-300"
                                group-label="language"
                                placeholder="Select a management fee allocation type"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </v-col>
                    </v-row>

                    <v-expansion-panels multiple>
                        <v-expansion-panel>
                            <v-expansion-panel-header class="form-expansion-header pa-3"
                                >Management Fee Allocation Breakdown</v-expansion-panel-header
                            >
                            <v-expansion-panel-content>
                                <sui-table
                                    class="vue-data-grid"
                                    stackable
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                    compact
                                >
                                    <sui-table-header>
                                        <sui-table-row class="fieldDescription">
                                            <sui-table-header-cell style="width: 5px">#</sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"> </sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"
                                                >Allocate to Misc</sui-table-header-cell
                                            >
                                            <sui-table-header-cell text-align="center"
                                                >Floor Name</sui-table-header-cell
                                            >
                                            <sui-table-header-cell text-align="center">Unit Code</sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                                        </sui-table-row>
                                    </sui-table-header>
                                    <sui-table-body>
                                        <sui-table-row
                                            v-for="(
                                                lease_list_data, lease_list_index
                                            ) in year_data2.lease_profile_man_fee_list"
                                            :key="lease_list_index"
                                            v-bind:class="{ cirrus_color: lease_list_data.is_lease_selected === true }"
                                        >
                                            <sui-table-cell style="width: 5px">
                                                <strong>{{ lease_list_index + 1 }}.&nbsp</strong>
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                <sui-checkbox v-model="lease_list_data.status" />
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                <sui-checkbox v-model="lease_list_data.allocated_to_misc" />
                                                <cirrus-input
                                                    v-show="lease_list_data.allocated_to_misc"
                                                    :id="'lease_list_data.percentage' + lease_list_index"
                                                    v-model="lease_list_data.misc_percentage"
                                                    :edit_form="true"
                                                    inputFormat="percentage"
                                                ></cirrus-input>
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                {{ lease_list_data.floor_code }}
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                {{ lease_list_data.unit_code }}
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center"> </sui-table-cell>
                                        </sui-table-row>
                                    </sui-table-body>
                                </sui-table>
                            </v-expansion-panel-content>
                        </v-expansion-panel>
                    </v-expansion-panels>

                    <br />
                    <br />
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class="form-subheader"
                            ><strong>Expenses Account Profile</strong></v-col
                        >
                    </v-row>
                    <v-col class="text-right">
                        <v-btn
                            color="success"
                            small
                            depressed
                            @click="addLine(year_index2, year_data2.year_from, year_data2.year_to)"
                            >Add Account</v-btn
                        >
                        <v-btn
                            v-if="lease_profile_year_list.length > 1"
                            color="error"
                            small
                            depressed
                            @click="deleteProfile(year_data2.year_from, year_data2.year_to)"
                            >Delete Profile</v-btn
                        >
                    </v-col>
                    <v-row
                        class="form-row"
                        v-for="(lease_profile_data, lease_profile_index) in year_data2.lease_profile_list"
                        :key="lease_profile_index"
                    >
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class=""
                        >
                            <v-card
                                color=""
                                class=""
                                style="margin-bottom: 6px"
                            >
                                <v-card-actions class="pa-1">
                                    <strong>{{ lease_profile_index + 1 }}.&nbsp&nbsp</strong>
                                    <multiselect
                                        v-model="lease_profile_data.account_details"
                                        :options="dd_account_expenses_ungrouped_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-300"
                                        group-label="language"
                                        :custom-label="nameWithDash"
                                        placeholder="Select an account"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <v-spacer></v-spacer>
                                    <strong>Allocation Type:&nbsp&nbsp</strong>
                                    <multiselect
                                        v-model="lease_profile_data.allocation_type_details"
                                        :options="allocation_type_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        group-label="language"
                                        placeholder="Select a Allocation Type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <v-btn
                                        icon
                                        v-if="!lease_profile_data.toggle_breakdown"
                                        @click="
                                            lease_profile_data.toggle_breakdown = !lease_profile_data.toggle_breakdown
                                        "
                                    >
                                        <v-icon>arrow_drop_down</v-icon>
                                    </v-btn>
                                    <v-btn
                                        icon
                                        v-if="lease_profile_data.toggle_breakdown"
                                        @click="
                                            lease_profile_data.toggle_breakdown = !lease_profile_data.toggle_breakdown
                                        "
                                    >
                                        <v-icon>arrow_drop_up</v-icon>
                                    </v-btn>
                                    <v-btn
                                        icon
                                        @click="deleteAccount(lease_profile_index)"
                                    >
                                        <v-icon color="red">close</v-icon>
                                    </v-btn>
                                </v-card-actions>
                                <v-divider light></v-divider>
                                <sui-table
                                    v-if="lease_profile_data.toggle_breakdown"
                                    class="vue-data-grid"
                                    stackable
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                    compact
                                >
                                    <sui-table-header>
                                        <sui-table-row class="fieldDescription">
                                            <sui-table-header-cell style="width: 5px">#</sui-table-header-cell>
                                            <sui-table-header-cell text-align="center">
                                                <sui-checkbox
                                                    v-model="lease_profile_data.select_all_flag"
                                                    @click="toggleAllCheckbox(lease_profile_index)"
                                                />
                                            </sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"
                                                >Allocate to Misc</sui-table-header-cell
                                            >
                                            <sui-table-header-cell text-align="center"
                                                >Floor Name</sui-table-header-cell
                                            >
                                            <sui-table-header-cell text-align="center">Unit Code</sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                                        </sui-table-row>
                                    </sui-table-header>
                                    <sui-table-body>
                                        <sui-table-row
                                            v-for="(lease_list_data, lease_list_index) in lease_profile_data.lease_list"
                                            :key="lease_list_index"
                                            v-bind:class="{ cirrus_color: lease_list_data.is_lease_selected === true }"
                                        >
                                            <sui-table-cell style="width: 5px">
                                                <strong
                                                    >{{ lease_profile_index + 1 }}.{{
                                                        lease_list_index + 1
                                                    }}.&nbsp</strong
                                                >
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                <sui-checkbox v-model="lease_list_data.status" />
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                <sui-checkbox v-model="lease_list_data.allocated_to_misc" />
                                                <cirrus-input
                                                    v-show="lease_list_data.allocated_to_misc"
                                                    :id="'lease_list_data.percentage' + lease_list_index"
                                                    v-model="lease_list_data.misc_percentage"
                                                    :edit_form="true"
                                                    inputFormat="percentage"
                                                ></cirrus-input>
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                {{ lease_list_data.floor_code }}
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                {{ lease_list_data.unit_code }}
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center"> </sui-table-cell>
                                        </sui-table-row>
                                    </sui-table-body>
                                </sui-table>
                            </v-card>
                        </v-col>
                    </v-row>
                    <v-divider></v-divider>
                    <v-card
                        elevation="0"
                        v-if="year_data2.lease_profile_list.length > 0"
                    >
                        <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn
                                class="v-step-save-2-button"
                                @click="saveForm(year_index2, year_data2.year_from, year_data2.year_to)"
                                color="success"
                                dark
                                small
                                depressed
                            >
                                Save Details
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-tab-item>
                <v-tab-item>
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Financial Year:</v-col
                        >
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <multiselect
                                v-if="!select_year_flag"
                                v-model="financial_year"
                                :options="year_list"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-300"
                                group-label="language"
                                placeholder="Select a financial year"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                            <v-btn
                                v-if="!select_year_flag"
                                color="success"
                                small
                                depressed
                                class="rounded-l-0"
                                @click="toggleCreate(true)"
                                >Select Year</v-btn
                            >
                            <span
                                class="form-input-text"
                                v-if="select_year_flag"
                                >{{ financial_year.field_value }}</span
                            >
                            <v-btn
                                v-if="select_year_flag"
                                color="success"
                                small
                                depressed
                                @click="toggleCreate(false)"
                                >Cancel</v-btn
                            >
                        </v-col>
                    </v-row>
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class="form-subheader"
                            ><strong>Management Fee Profile</strong></v-col
                        >
                    </v-row>
                    <v-row class="form-row">
                        <v-col
                            v-if="select_year_flag"
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                            >Management Fee Allocation type:</v-col
                        >
                        <v-col
                            v-if="select_year_flag"
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <multiselect
                                v-model="man_fee_allocation_type"
                                :options="man_fee_allocation_type_list"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-300"
                                group-label="language"
                                placeholder="Select a financial year"
                                track-by="field_key"
                                label="field_value"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </v-col>
                    </v-row>

                    <v-expansion-panels
                        v-if="select_year_flag"
                        multiple
                    >
                        <v-expansion-panel>
                            <v-expansion-panel-header class="form-expansion-header pa-3"
                                >Management Fee Allocation Breakdown</v-expansion-panel-header
                            >
                            <v-expansion-panel-content>
                                <sui-table
                                    class="vue-data-grid"
                                    stackable
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                    compact
                                >
                                    <sui-table-header>
                                        <sui-table-row class="fieldDescription">
                                            <sui-table-header-cell style="width: 5px">#</sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"> </sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"
                                                >Allocate to Misc</sui-table-header-cell
                                            >
                                            <sui-table-header-cell text-align="center"
                                                >Floor Name</sui-table-header-cell
                                            >
                                            <sui-table-header-cell text-align="center">Unit Code</sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                                        </sui-table-row>
                                    </sui-table-header>
                                    <sui-table-body>
                                        <sui-table-row
                                            v-for="(lease_list_data, lease_list_index) in lease_profile_man_fee_list"
                                            :key="lease_list_index"
                                        >
                                            <sui-table-cell style="width: 5px">
                                                <strong>{{ lease_list_index + 1 }}.&nbsp</strong>
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                <sui-checkbox v-model="lease_list_data.status" />
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                <sui-checkbox v-model="lease_list_data.allocated_to_misc" />
                                                <cirrus-input
                                                    v-show="lease_list_data.allocated_to_misc"
                                                    :id="'lease_list_data.percentage' + lease_list_index"
                                                    v-model="lease_list_data.misc_percentage"
                                                    :edit_form="true"
                                                    inputFormat="percentage"
                                                ></cirrus-input>
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                {{ lease_list_data.floor_code }}
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                {{ lease_list_data.unit_code }}
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center"> </sui-table-cell>
                                        </sui-table-row>
                                    </sui-table-body>
                                </sui-table>
                            </v-expansion-panel-content>
                        </v-expansion-panel>
                    </v-expansion-panels>

                    <br />
                    <br />
                    <v-row
                        v-if="select_year_flag"
                        class="form-row"
                    >
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class="form-subheader"
                            ><strong>Expenses Account Profile</strong></v-col
                        >
                    </v-row>
                    <v-col
                        class="text-right"
                        v-if="select_year_flag"
                    >
                        <v-btn
                            color="success"
                            small
                            depressed
                            @click="addLineNew(financial_year.field_value, 'NEW')"
                            >Add Account</v-btn
                        >
                    </v-col>
                    <v-row
                        v-if="select_year_flag"
                        class="form-row"
                        v-for="(lease_profile_data, lease_profile_index) in lease_profile_list"
                        :key="lease_profile_index"
                    >
                        <v-col
                            xs="12"
                            sm="12"
                            md="12"
                            class=""
                        >
                            <v-card
                                color=""
                                class=""
                                style="margin-bottom: 6px"
                            >
                                <v-card-actions class="pa-1">
                                    <strong>{{ lease_profile_index + 1 }}.&nbsp&nbsp</strong>
                                    <multiselect
                                        v-model="lease_profile_data.account_details"
                                        :options="dd_account_expenses_ungrouped_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-300"
                                        group-label="language"
                                        :custom-label="nameWithDash"
                                        placeholder="Select an account"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <v-spacer></v-spacer>
                                    <strong>Allocation Type:&nbsp&nbsp</strong>
                                    <multiselect
                                        v-model="lease_profile_data.allocation_type_details"
                                        :options="allocation_type_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        group-label="language"
                                        placeholder="Select a Allocation Type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                    <v-btn
                                        icon
                                        v-if="!lease_profile_data.toggle_breakdown"
                                        @click="
                                            lease_profile_data.toggle_breakdown = !lease_profile_data.toggle_breakdown
                                        "
                                    >
                                        <v-icon>arrow_drop_down</v-icon>
                                    </v-btn>
                                    <v-btn
                                        icon
                                        v-if="lease_profile_data.toggle_breakdown"
                                        @click="
                                            lease_profile_data.toggle_breakdown = !lease_profile_data.toggle_breakdown
                                        "
                                    >
                                        <v-icon>arrow_drop_up</v-icon>
                                    </v-btn>
                                    <v-btn
                                        icon
                                        @click="deleteAccount(lease_profile_index)"
                                    >
                                        <v-icon color="red">close</v-icon>
                                    </v-btn>
                                </v-card-actions>
                                <v-divider light></v-divider>
                                <sui-table
                                    v-if="lease_profile_data.toggle_breakdown"
                                    class="vue-data-grid"
                                    stackable
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                    compact
                                >
                                    <sui-table-header>
                                        <sui-table-row class="fieldDescription">
                                            <sui-table-header-cell style="width: 5px">#</sui-table-header-cell>
                                            <sui-table-header-cell text-align="center">
                                                <sui-checkbox
                                                    v-model="lease_profile_data.select_all_flag"
                                                    @click="toggleAllCheckbox(lease_profile_index)"
                                                />
                                            </sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"
                                                >Allocate to Misc</sui-table-header-cell
                                            >
                                            <sui-table-header-cell text-align="center"
                                                >Floor Name</sui-table-header-cell
                                            >
                                            <sui-table-header-cell text-align="center">Unit Code</sui-table-header-cell>
                                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                                        </sui-table-row>
                                    </sui-table-header>
                                    <sui-table-body>
                                        <sui-table-row
                                            v-for="(lease_list_data, lease_list_index) in lease_profile_data.lease_list"
                                            :key="lease_list_index"
                                            v-bind:class="{ cirrus_color: lease_list_data.is_lease_selected === true }"
                                        >
                                            <sui-table-cell style="width: 5px">
                                                <strong
                                                    >{{ lease_profile_index + 1 }}.{{
                                                        lease_list_index + 1
                                                    }}.&nbsp</strong
                                                >
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                <sui-checkbox v-model="lease_list_data.status" />
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                <sui-checkbox v-model="lease_list_data.allocated_to_misc" />
                                                <cirrus-input
                                                    v-show="lease_list_data.allocated_to_misc"
                                                    :id="'lease_list_data.percentage' + lease_list_index"
                                                    v-model="lease_list_data.misc_percentage"
                                                    :edit_form="true"
                                                    inputFormat="percentage"
                                                ></cirrus-input>
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                {{ lease_list_data.floor_code }}
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center">
                                                {{ lease_list_data.unit_code }}
                                            </sui-table-cell>
                                            <sui-table-cell text-align="center"> </sui-table-cell>
                                        </sui-table-row>
                                    </sui-table-body>
                                </sui-table>
                            </v-card>
                        </v-col>
                    </v-row>
                    <v-divider></v-divider>
                    <v-card
                        elevation="0"
                        v-if="lease_profile_list.length > 0"
                    >
                        <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn
                                class="v-step-save-2-button"
                                @click="saveForm('NEW', financial_year.field_value, 'NEW')"
                                color="success"
                                dark
                                small
                                depressed
                                :loading="btn_loading_setting"
                            >
                                Save Details
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-tab-item>
            </v-tabs>
        </div>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        page_form_type: { type: String, default: '' },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_PROFILE',
            loading_setting: false,
            btn_loading_setting: false,
            profile_tab: 0,
            error_server_msg2: [],
            account_list: [],
            lease_profile_list: [],
            default_lease_list: [],
            allocation_type_list: [],
            lease_profile_year_list: [],
            man_fee_allocation_type: [],
            man_fee_allocation_type_list: [],
            lease_profile_man_fee_list: [],
            year_list: [],
            financial_year: [],
            select_year_flag: false,
        };
    },
    mounted() {
        this.loadForm();
    },
    computed: {
        ...mapState([
            'cirrus8_api_url',
            'lease_details',
            'dd_account_expenses_grouped_list',
            'dd_account_expenses_ungrouped_list',
        ]),
        ...mapGetters([]),
    },
    methods: {
        loadForm: function () {
            this.error_server_msg2 = [];
            if (this.property_code !== '' && this.lease_code !== '') {
                this.loadLeaseProfile();
            }
        },
        addLine: function (year_index, year_from, year_to) {
            this.lease_profile_year_list[year_index].lease_profile_list.push({
                account_code: '',
                year_from: year_from,
                year_to: year_to,
                account_details: { field_key: '', field_value: 'Please select...' },
                allocation_type_value: '',
                allocation_type_details: { field_key: '', field_value: 'Please select...' },
                man_fee_allocation_type: { field_key: '', field_value: 'Please select...' },
                lease_list: this.default_lease_list,
                toggle_breakdown: true,
                select_all_flag: true,
            });
        },
        addLineNew: function (year_from, year_to) {
            this.lease_profile_list.push({
                account_code: '',
                year_from: year_from,
                year_to: year_to,
                account_details: { field_key: '', field_value: 'Please select...' },
                allocation_type_value: '',
                allocation_type_details: { field_key: '', field_value: 'Please select...' },
                man_fee_allocation_type: { field_key: '', field_value: 'Please select...' },
                lease_list: this.default_lease_list,
                toggle_breakdown: true,
                select_all_flag: true,
            });
        },
        async deleteProfile(year_from, year_to) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.loading_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('year_from', year_from);
                form_data.append('year_to', year_to);
                form_data.append('version_id', this.version_id);
                form_data.append('form_section', this.form_section);
                form_data.append('no_load', true);

                let apiUrl = '';
                apiUrl = this.cirrus8_api_url + 'api/lease/delete/profile';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadForm();
                });
            }
        },
        saveForm: function (index, year_from, year_to) {
            this.error_server_msg2 = [];
            let lease_profile_list = this.lease_profile_list;
            let lease_profile_man_fee_list = this.lease_profile_man_fee_list;
            if (year_to !== 'NEW') {
                lease_profile_list = this.lease_profile_year_list[index].lease_profile_list;
                lease_profile_man_fee_list = this.lease_profile_year_list[index].lease_profile_man_fee_list;
            }
            let man_fee_allocation_type = '';
            //validate each data
            //check account code duplicate
            for (let x = 0; x <= lease_profile_list.length - 1; x++) {
                let account_code = lease_profile_list[x].account_code;
                let account_details = lease_profile_list[x].account_details;
                let allocation_type_details = lease_profile_list[x].allocation_type_details;
                let lease_list = lease_profile_list[x].lease_list;

                if (account_details.field_key === '') {
                    this.error_server_msg2.push([x + 1 + '. You have not specified an account details.']);
                } else {
                    let check_duplicate_account = lease_profile_list.filter(
                        (m) => m.account_details.field_key === account_details.field_key,
                    );
                    if (check_duplicate_account.length >= 2) {
                        this.error_server_msg2.push([
                            x +
                                1 +
                                '. Account ' +
                                account_details.field_key +
                                ' - ' +
                                account_details.field_value +
                                ' can only be chosen once.',
                        ]);
                    }
                }

                if (allocation_type_details.field_key === '') {
                    this.error_server_msg2.push([x + 1 + '. You have not specified an allocation type details.']);
                } else {
                    // let check_duplicate_account = lease_profile_list.filter(m => m.allocation_type_details.field_key === allocation_type_details.field_key);
                    // if (check_duplicate_account.length >= 2) {
                    //   this.error_server_msg2.push([(x + 1) + '. Allocation type for ' + account_details.field_key + ' - ' + allocation_type_details.field_value + ' is invalid.']);
                    // }
                }
            }
            if (year_to === 'NEW') {
                man_fee_allocation_type = this.man_fee_allocation_type.field_key;
            } else {
                let lease_profile_year_list = this.lease_profile_year_list;
                for (let y = 0; y <= lease_profile_year_list.length - 1; y++) {
                    if (
                        lease_profile_year_list[y].year_from === year_from &&
                        lease_profile_year_list[y].year_to === year_to
                    ) {
                        man_fee_allocation_type = lease_profile_year_list[y].man_fee_allocation_type.field_key;
                    }
                }
            }
            if (man_fee_allocation_type === '') {
                this.error_server_msg2.push([x + 1 + '. You have not specified a management fee allocation type.']);
            }
            //save data
            if (this.error_server_msg2.length === 0) {
                this.btn_loading_setting = true;
                let form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('year_from', year_from);
                form_data.append('year_to', year_to);
                form_data.append('man_fee_allocation_type', man_fee_allocation_type);
                form_data.append('version_id', this.version_id);
                form_data.append('form_section', this.form_section);
                form_data.append('no_load', true);
                form_data.append('lease_profile_list', JSON.stringify(lease_profile_list));
                form_data.append('lease_profile_man_fee_list', JSON.stringify(lease_profile_man_fee_list));

                let apiUrl = '';
                apiUrl = this.cirrus8_api_url + 'api/lease/update-or-create/profile';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.btn_loading_setting = false;
                    this.error_server_msg2 = response.data.error_server_msg2;
                    if (this.error_server_msg2.length === 0) {
                        this.loadForm();
                    }
                });
            }
        },
        loadLeaseProfile: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('form_section', this.form_section);
            form_data.append('no_load', true);
            let apiUrl = '';
            apiUrl = this.cirrus8_api_url + 'api/lease/fetch/profile';
            this.$api.post(apiUrl, form_data).then((response) => {
                let year_list = response.data.year_list;
                let lease_profile_list = response.data.lease_profile_list;
                let default_lease_list = response.data.default_lease_list;
                let allocation_type_list = response.data.allocation_type_list;
                let man_fee_allocation_type_list = response.data.man_fee_allocation_type_list;
                let lease_profile_year_list = response.data.lease_profile_year_list;
                const financial_year_key = response.data.current_period_year;

                let filtered = year_list.filter((m) => m.calendar_year === financial_year_key);
                this.fromDate = filtered[0].calendar_start_date;
                this.toDate = filtered[0].calendar_end_date;
                this.financial_year = {
                    label: response.data.current_period_year,
                    value: response.data.current_period_year,
                    field_key: response.data.current_period_year,
                    field_value: response.data.current_period_year,
                };
                this.year_list = year_list;

                this.default_lease_list = default_lease_list;
                this.allocation_type_list = allocation_type_list;
                this.man_fee_allocation_type_list = man_fee_allocation_type_list;

                for (let z = 0; z <= lease_profile_year_list.length - 1; z++) {
                    for (let x = 0; x <= lease_profile_year_list[z].lease_profile_list.length - 1; x++) {
                        lease_profile_year_list[z].lease_profile_list[x].account_details = this.getValueInList(
                            lease_profile_year_list[z].lease_profile_list[x].account_code,
                            this.dd_account_expenses_ungrouped_list,
                        );
                        lease_profile_year_list[z].lease_profile_list[x].allocation_type_details = this.getValueInList(
                            lease_profile_year_list[z].lease_profile_list[x].allocation_type_value,
                            this.allocation_type_list,
                        );
                        lease_profile_year_list[z].lease_profile_list[x].man_fee_allocation_type_details =
                            this.getValueInList(
                                lease_profile_year_list[z].lease_profile_list[x].man_fee_allocation_type_value,
                                this.man_fee_allocation_type_list,
                            );
                        lease_profile_year_list[z].lease_profile_list[x].toggle_breakdown = false;
                        lease_profile_year_list[z].lease_profile_list[x].split_type = null;
                        if (
                            lease_profile_year_list[z].year_from ===
                                lease_profile_year_list[z].lease_profile_list[x].year_from &&
                            lease_profile_year_list[z].year_to ===
                                lease_profile_year_list[z].lease_profile_list[x].year_to
                        ) {
                            lease_profile_year_list[z].man_fee_allocation_type = this.getValueInList(
                                lease_profile_year_list[z].lease_profile_list[x].man_fee_allocation_type_value,
                                this.man_fee_allocation_type_list,
                            );
                        }
                    }
                }
                this.lease_profile_year_list = lease_profile_year_list;
                this.loading_setting = false;
            });
        },
        computeTotalPercentagePerAccount: function (index) {
            let lease_profile_list = this.lease_profile_list[index];
            let lease_list = lease_profile_list.lease_list;
            let total_percentage = 0;
            for (let x = 0; x <= lease_list.length - 1; x++) {
                let percentage = lease_list[x].percentage;
                let status = lease_list[x].status;
                if (status) {
                    total_percentage = total_percentage + eval(percentage);
                }
            }
            return this.roundTo(total_percentage, 2);
        },
        loadDefaultLease: function (index) {
            let lease_profile_list = this.lease_profile_list[index];
            let default_lease_list = lease_profile_list.default_lease_list;
            let lease_list = lease_profile_list.lease_list;
            for (let x = 0; x <= default_lease_list.length - 1; x++) {
                lease_list.push(default_lease_list[x]);
            }
            this.lease_profile_list[index].lease_list = lease_list;
        },
        computeEqualSplit: function (index) {
            let lease_profile_list = this.lease_profile_list;
            let lease_list = lease_profile_list[index].lease_list;

            let total_lease_active = lease_list.filter((m) => m.status === true || m.status === 1);
            let total_lease_count = total_lease_active.length;

            let each_percentage = 100 / total_lease_count;
            each_percentage = this.roundTo(each_percentage, 2);

            let last_percentage = 0;
            for (let x = 0; x <= lease_profile_list[index].lease_list.length - 1; x++) {
                let new_percentage = eval(each_percentage);
                let lc_status = lease_profile_list[index].lease_list[x].status;
                if (lc_status === true || lc_status === 1) {
                    last_percentage = last_percentage + new_percentage;
                    if (x === lease_profile_list[index].lease_list.length - 1) {
                        last_percentage = this.roundTo(last_percentage, 2);
                        if (last_percentage < 100) {
                            new_percentage = eval(each_percentage) + (100 - eval(last_percentage));
                        } else if (last_percentage > 100) {
                            new_percentage = eval(each_percentage) - (eval(last_percentage) - 100);
                        }
                        new_percentage = this.roundTo(new_percentage, 2);
                    }
                    lease_profile_list[index].lease_list[x].percentage = eval(new_percentage);
                }
            }
            this.lease_profile_list = JSON.parse(JSON.stringify(lease_profile_list));
        },
        toggleCheckbox: function (profile_index, lease_index) {
            // let lease_profile_list = this.lease_profile_list[profile_index].lease_list[lease_index];
            this.lease_profile_list[profile_index].lease_list[lease_index].percentage = '0.00';
        },
        deleteAccount: function (index) {
            this.lease_profile_list.splice(index, 1);
        },
        toggleAllCheckbox: function (index) {
            for (let x = 0; x <= this.lease_profile_list[index].lease_list.length - 1; x++) {
                this.lease_profile_list[index].lease_list[x].status = this.lease_profile_list[index].select_all_flag;
            }
        },
        filter_lease_profile_list(year_to) {
            return this.lease_profile_list.filter((m) => m.year_to === year_to);
        },
        toggleCreate: function (flag) {
            //
            this.error_server_msg2 = [];
            if (flag) {
                // this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('financial_year', this.financial_year.field_key);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                let apiUrl = '';
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/profile/unit-list';
                this.$api.post(apiUrl, form_data).then((response) => {
                    // this.loading_setting = false;
                    this.lease_profile_man_fee_list = response.data.lease_profile_man_fee_list;
                    for (let x = 0; x <= this.lease_profile_list.length - 1; x++) {
                        let year_to = this.lease_profile_list[x].year_to;
                        if (year_to === 'NEW') {
                            this.lease_profile_list.splice(x, 1);
                        }
                    }
                    this.select_year_flag = flag;
                });
            } else {
                this.select_year_flag = flag;
            }
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        profile_tab: function () {
            let pro_length = this.lease_profile_year_list.length;
            if (pro_length > 0) {
                if (this.profile_tab === this.lease_profile_year_list.length) {
                    this.toggleCreate(false);
                    this.lease_profile_list = [];
                    this.addLineNew(this.financial_year.field_value, 'NEW');
                }
            }
        },
    },
    created() {
        bus.$on('loadLeaseProfileSection', (data) => {
            this.loadForm();
        });
    },
    mixins: [global_mixins],
};
</script>
