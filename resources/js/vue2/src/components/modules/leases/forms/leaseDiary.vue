<style>
.theme--light.v-input--is-disabled,
.theme--light.v-input--is-disabled input,
.theme--light.v-input--is-disabled textarea {
    color: rgba(0, 0, 0, 0.55);
}
</style>

<template>
    <div
        class="page-form"
        v-cloak
        v-on:dblclick="doubleClickForm()"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Lease Diary</h6>
                <v-spacer></v-spacer>
                <v-checkbox
                    v-if="
                        !new_lease &&
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    v-model="toggle_show_completed_item"
                    label="Show completed items"
                    ripple="false"
                    dense
                    style="width: auto; margin-right: 26px"
                ></v-checkbox>
                <v-checkbox
                    v-if="
                        !new_lease &&
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    v-model="toggle_show_deleted_item"
                    label="Show deleted items"
                    ripple="false"
                    dense
                    style="width: auto; margin-right: 26px"
                ></v-checkbox>
                <cirrus-input
                    inputFormat="search"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>

                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form && lease_diary_list.length > 0"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader
            type="table-tbody"
            v-if="loading_setting"
        ></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="lease_diary_list.length === 0 && !loading_setting"
            v-show="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-btn
                depressed
                small
                color="success"
                @click="modalAddData()"
                v-if="!pmro_read_only"
                >Add Diary</v-btn
            >
            <div
                style="margin-top: 15px"
                v-else
            >
                No lease diary entries at the moment
            </div>
        </v-col>
        <v-data-table
            class="c8-datatable-custom"
            :item-class="itemRowBackground"
            v-if="!loading_setting"
            v-show="filteredDiaryItem.length > 0"
            dense
            item-key="id"
            :headers="headers"
            :items="filteredDiaryItem"
            :items-per-page="items_per_page"
            hide-default-footer
            :page.sync="page"
            :total-visible="20"
            @page-count="page_count = $event"
            :search="search_datatable"
        >
            <template v-slot:item.index="{ item }">
                {{ filteredDiaryItem.indexOf(item) + 1 }}
            </template>
            <template v-slot:item.item_no="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.item_no }}</span>
                </div>
            </template>
            <template v-slot:item.diary_date_raw="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.diary_date }}</span>
                </div>
            </template>

            <template v-slot:item.diary_entry_type_description="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.diary_entry_type_description }}</span>
                </div>
            </template>

            <template v-slot:item.diary_entry="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.diary_entry }}</span>
                </div>
            </template>

            <template v-slot:item.comments="{ item }">
                <v-icon
                    :color="item.diary_comment_list.length === 0 ? '' : 'success'"
                    @click="showCommentModal(lease_diary_list.indexOf(item))"
                    >comment</v-icon
                >
            </template>

            <template v-slot:item.diary_completed_by="{ item }">
                <span v-if="item.diary_complete !== ''">
                    <v-icon color="green">check</v-icon>
                    <strong>{{ item.diary_complete }}</strong> by
                    {{ item.diary_completed_by }}
                </span>
                <span v-else
                    ><strong
                        v-if="
                            item.status !== 'new' &&
                            !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                            !pmro_read_only
                        "
                        ><v-btn
                            x-small
                            @click="leaseDiaryComplete(lease_diary_list.indexOf(item))"
                            >Mark as Completed</v-btn
                        ></strong
                    ></span
                >
            </template>
            <template v-slot:item.diary_deleted_by="{ item }">
                <span>
                    <strong v-if="item.diary_deleted === '1'">{{ item.deleted_date }}</strong>
                    <span v-if="item.diary_deleted === '1'"> by {{ item.diary_deleted_by }}</span>
                </span>
            </template>

            <template v-slot:item.action1="{ item }">
                <v-icon
                    small
                    @click="modalOpenAED(lease_diary_list.indexOf(item))"
                    v-if="edit_form"
                    >fas fa-edit</v-icon
                >
                <v-icon
                    color="red"
                    @click="deleteDiary(lease_diary_list.indexOf(item))"
                    v-if="item.diary_complete === '' && edit_form"
                    :disabled="item.diary_deleted === '1'"
                    >close
                </v-icon>
                <v-icon
                    color="red"
                    v-if="item.diary_complete !== '' && edit_form"
                    :disabled="item.diary_complete !== ''"
                >
                    close
                </v-icon>
            </template>
        </v-data-table>
        <v-row
            class="form-row"
            v-show="filteredDiaryItem.length > items_per_page"
            v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-col
                xs="12"
                sm="12"
                md="12"
            >
                <table class="c8-datatable-custom-footer">
                    <tr>
                        <td class="">Rows per page:</td>
                        <td>
                            <multiselect
                                v-model="items_per_page"
                                :options="[5, 10, 15]"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-200"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </td>
                        <td></td>
                        <td>
                            <v-pagination
                                v-model="page"
                                :length="page_count"
                            ></v-pagination>
                        </td>
                    </tr>
                </table>
            </v-col>
        </v-row>

        <br />

        <v-divider></v-divider>
        <!--        <v-card elevation="0" v-if="edit_form && lease_diary_list.length>0">-->
        <!--            <v-card-actions v-if="edit_form">-->
        <!--                <v-spacer></v-spacer>-->
        <!--                <v-btn class="v-step-save-2-button" @click="saveForm()"-->
        <!--                       color="success"-->
        <!--                       dark-->
        <!--                       small-->
        <!--                       tile-->
        <!--                >-->
        <!--                    Save Diary details-->
        <!--                </v-btn>-->
        <!--            </v-card-actions>-->
        <!--        </v-card>-->

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_comment_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Diary Comment
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_comment_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div
                        class="page-form"
                        v-if="show_comment_modal"
                    >
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Property:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="4"
                                md="4"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ property_code }}</strong>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Lease:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="4"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ lease_code }}</strong>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Date:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="4"
                                md="4"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ diary_modal_list_temp.diary_date }}</strong>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Entry Type:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="4"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ diary_modal_list_temp.diary_type.label }}</strong>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                                >Notes:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <strong class="form-input-text">{{ diary_modal_list_temp.diary_entry }}</strong>
                            </v-col>
                        </v-row>
                        <v-container>
                            <cirrus-server-error :errorMsg2="error_diary_update_comment"></cirrus-server-error>

                            <div
                                v-if="diary_modal_list_temp.diary_comment_list.length == 0"
                                style="text-align: center; margin: 15px 0px"
                            >
                                No diary comments at the moment
                            </div>

                            <v-row
                                dense
                                v-for="(
                                    diary_modal_list_temp_data, diary_modal_list_temp_index
                                ) in diary_modal_list_temp.diary_comment_list"
                                :key="diary_modal_list_temp_index"
                            >
                                <v-col cols="12">
                                    <v-card
                                        color=""
                                        class="diary-comment-item-container"
                                        style="margin-bottom: 6px"
                                    >
                                        <v-card-actions class="pa-3">
                                            <strong>{{ diary_modal_list_temp_index + 1 }}.&nbsp</strong>
                                            <span>
                                                Created on {{ diary_modal_list_temp_data.created_date }} &nbsp Updated
                                                at {{ diary_modal_list_temp_data.updated_date }} &nbsp by
                                                {{ diary_modal_list_temp_data.created_by }}
                                            </span>
                                            <v-spacer></v-spacer>
                                            <v-btn
                                                x-small
                                                tile
                                                v-show="
                                                    !formSectionReadOnly(
                                                        pm_lease_form_read_only,
                                                        form_type,
                                                        form_section,
                                                        is_inactive,
                                                    ) && !pmro_read_only
                                                "
                                                @click="updateDiaryComment(diary_modal_list_temp_index)"
                                                >Update</v-btn
                                            >
                                            <v-icon
                                                color="red"
                                                v-show="
                                                    !formSectionReadOnly(
                                                        pm_lease_form_read_only,
                                                        form_type,
                                                        form_section,
                                                        is_inactive,
                                                    ) && !pmro_read_only
                                                "
                                                @click="deleteDiaryComment(diary_modal_list_temp_index)"
                                                >close</v-icon
                                            >
                                        </v-card-actions>
                                        <v-divider light></v-divider>
                                        <v-layout>
                                            <v-flex xs12>
                                                <v-textarea
                                                    v-model="diary_modal_list_temp_data.diary_comment"
                                                    auto-grow
                                                    rows="1"
                                                    full-width
                                                    class="noteTextArea"
                                                    maxlength="1800"
                                                    :disabled="pmro_read_only ? true : false"
                                                ></v-textarea>
                                            </v-flex>
                                        </v-layout>
                                    </v-card>
                                </v-col>
                            </v-row>
                            <cirrus-server-error :errorMsg2="error_diary_new_comment"></cirrus-server-error>
                            <v-row
                                dense
                                v-show="
                                    !formSectionReadOnly(
                                        pm_lease_form_read_only,
                                        form_type,
                                        form_section,
                                        is_inactive,
                                    ) && !pmro_read_only
                                "
                            >
                                <v-col cols="12">
                                    <v-card
                                        color=""
                                        class="new-comment-modal-container"
                                        style="margin-bottom: 6px"
                                    >
                                        <v-card-actions class="pa-3">
                                            <strong>Add New Comment</strong>
                                            <v-spacer></v-spacer>
                                            <v-icon
                                                color="green"
                                                @click="addDiaryComment()"
                                                >add</v-icon
                                            >
                                        </v-card-actions>
                                        <v-divider light></v-divider>
                                        <v-layout>
                                            <v-flex xs12>
                                                <v-textarea
                                                    v-model="diary_modal_new_comment"
                                                    auto-grow
                                                    rows="1"
                                                    full-width
                                                    class="noteTextArea"
                                                    maxlength="1800"
                                                ></v-textarea>
                                            </v-flex>
                                        </v-layout>
                                    </v-card>
                                </v-col>
                            </v-row>
                        </v-container>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>
        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteDiary(lease_diary_add_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Diary Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="lease_diary_add_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        lease_diary_add_arr.index === 'New'
                                            ? lease_diary_add_arr.index
                                            : lease_diary_add_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Diary Entry Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        v-model="lease_diary_add_arr.diary_type"
                                        :options="diary_type_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-800"
                                        :custom-label="nameWithDash"
                                        group-label="language"
                                        placeholder="Please select..."
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Diary Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'lease_inspection_sched_date' + lease_diary_add_arr.diary_id"
                                        :size="'40'"
                                        v-model="lease_diary_add_arr.diary_date"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Diary Note</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :id="'diary_entry'"
                                        v-model="lease_diary_add_arr.diary_entry"
                                        :edit_form="lease_diary_add_arr.diary_complete === ''"
                                        :rules_length="100"
                                        :maxlength="100"
                                        :size="100"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="lease_diary_add_arr.status !== 'new'"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Comment</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-icon
                                        :color="lease_diary_add_arr.diary_comment_list.length === 0 ? '' : 'success'"
                                        @click="showCommentModal(lease_diary_add_arr.index)"
                                        >comment</v-icon
                                    >
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="lease_diary_add_arr.status !== 'new'"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Completed</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span
                                        class="form-input-text"
                                        v-if="lease_diary_add_arr.diary_complete !== ''"
                                    >
                                        <v-icon color="green">check</v-icon>
                                        <strong>{{ lease_diary_add_arr.diary_complete }}</strong> by
                                        {{ lease_diary_add_arr.diary_completed_by }}
                                    </span>
                                    <span
                                        class="form-input-text"
                                        v-else
                                        ><strong v-if="lease_diary_add_arr.status !== 'new'"
                                            ><v-btn
                                                x-small
                                                @click="leaseDiaryComplete(lease_diary_add_arr.index)"
                                                >Mark as Completed</v-btn
                                            ></strong
                                        ></span
                                    >
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="lease_diary_add_arr.status !== 'new' && isLeaseFormLive()"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Copy Comments</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <v-checkbox
                                        v-model="copy_diary_comment"
                                        label=""
                                        ripple="false"
                                        dense
                                        style="width: auto; margin-right: 26px"
                                    ></v-checkbox>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="lease_diary_add_arr.diary_complete === ''"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="lease_diary_add_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                        v-if="lease_diary_add_arr.diary_complete === ''"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="copyDiaryItem()"
                        data-tooltip="Close original diary item and Create new diary item with above details."
                        data-position="left center"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="lease_diary_add_arr.status !== 'new' && isLeaseFormLive()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >content_copy</v-icon
                        >
                        Copy and Close
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="lease_diary_add_arr.status !== 'new' && lease_diary_add_arr.diary_complete === ''"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteDiary(lease_diary_add_arr.index)"
                        v-if="
                            lease_diary_add_arr.diary_complete === '' &&
                            lease_diary_add_arr.index !== 'New' &&
                            lease_diary_add_arr.diary_deleted !== '1'
                        "
                        color="error"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="lease_diary_add_arr.diary_complete === ''"
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        page_form_type: { type: String, default: '' },
        edit_flag: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_DIARY',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            error_diary_new_comment: [],
            error_diary_update_comment: [],
            loading_setting: true,
            edit_form: false,
            lease_diary_add_arr: { index: '0', diary_comment_list: [] },
            lease_diary_list: [],
            lease_diary_list_old: [],
            diary_type_list: [],
            show_activity_log_modal: false,
            show_comment_modal: false,
            AED_modal: false,
            success_flag: false,
            show_progress: false,
            diary_modal_list_temp: [],
            diary_modal_new_comment: '',
            modal_current_ctr: 0,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Diary Date', value: 'diary_date_raw', width: '15%' },
                { text: 'Diary Entry Type', value: 'diary_entry_type_description', width: '20%' },
                { text: 'Diary Note', value: 'diary_entry', width: '20%' },
                { text: 'Comments', value: 'comments', sortable: false, width: '5%' },
                { text: 'Completed', value: 'diary_completed_by', sortable: false, width: '30%' },
                { text: 'Deleted', value: 'diary_deleted_by', sortable: false, width: '30%' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 20,
            search_datatable: '',
            toggle_show_deleted_item: false,
            toggle_show_completed_item: false,
            copy_diary_comment: false,
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'clientTimezone',
        ]),
        filteredDiaryItem: function () {
            let lease_diary_list = this.lease_diary_list;
            if (this.toggle_show_deleted_item && this.toggle_show_completed_item) return this.lease_diary_list;
            else if (this.toggle_show_deleted_item && !this.toggle_show_completed_item)
                return lease_diary_list.filter((m) => m.diary_deleted === '1');
            else if (!this.toggle_show_deleted_item && this.toggle_show_completed_item)
                return lease_diary_list.filter((m) => m.diary_deleted !== '1');
            else if (!this.toggle_show_deleted_item && !this.toggle_show_completed_item)
                return lease_diary_list.filter((m) => m.diary_deleted !== '1' && m.diary_completed_by === '');
        },
    },
    mounted() {
        this.loadForm();
        if (this.edit_flag) this.edit_form = true;
    },
    methods: {
        ...mapMutations(['SET_LEASE_DIARY']),
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                )
                    this.edit_form = true;
                else this.edit_form = false;
            }
        },
        itemRowBackground: function (item) {
            return item.diary_deleted === '1' ? 'deleted_diary_style' : '';
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) this.loadLeaseDiary();
            else this.loading_setting = false;
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_diary_list = [];
            this.lease_diary_list = JSON.parse(JSON.stringify(this.lease_diary_list_old));
        },
        modalAddData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.AED_modal = true;
            this.lease_diary_add_arr = {
                index: 'New',
                deleted_by: '',
                deleted_date: '',
                diary_completed_by: '',
                diary_complete: '',
                diary_date: '',
                diary_deleted: '',
                diary_entry: '',
                diary_entry_type: '',
                diary_id: 0,
                diary_user: '',
                diary_type: { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' },
                diary_comment_list: [],
                status: 'new',
            };
        },
        modalOpenAED: function (index) {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.AED_modal = true;
            this.lease_diary_add_arr = this.lease_diary_list[index];
            this.lease_diary_add_arr.index = index;
            this.modal_current_ctr = index;
        },
        loadLeaseDiary: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            form_data.append('client_timezone', this.clientTimezone);
            let apiUrl = '';
            if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/lease/fetch/diary';
            else apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/diary';

            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_diary_list = response.data.lease_diary_list;
                this.lease_diary_list_old = response.data.lease_diary_list;
                this.diary_type_list = response.data.diary_type_list;
                if (this.formSectionReadOnly(this.pm_lease_form_read_only, this.form_type, this.form_section))
                    this.items_per_page = this.lease_diary_list.length;
                this.loading_setting = false;
            });
        },
        async deleteDiary(index) {
            if (index === 'New') return;
            let diary_complete = this.lease_diary_list[index].diary_complete;
            if (diary_complete === '' && index !== 'New') {
                let status = this.lease_diary_list[index].status;
                let diary_id = this.lease_diary_list[index].diary_id;
                if (status === 'new') this.lease_diary_list.splice(index, 1);
                else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.show_progress = true;
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('lease_code', this.lease_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('diary_id', diary_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/lease/delete/diary';
                        else apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/diary';

                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.loading_setting = false;
                            this.loadForm();
                            this.show_progress = false;
                            if (this.AED_modal) this.modalOpenAED(new_index);
                        });
                    }
                }
            }
        },
        async leaseDiaryComplete(index) {
            let status = this.lease_diary_list[index].status;
            let diary_id = this.lease_diary_list[index].diary_id;
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.loading_setting = true;
                this.show_progress = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('diary_id', diary_id);
                form_data.append('no_load', true);
                let apiUrl = '';
                if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/lease/update/complete-diary';
                else apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/complete-diary';

                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loadForm();
                    this.loading_setting = false;
                    this.show_progress = false;
                });
            }
        },
        modalSubmitData: function () {
            let errorArr = [];
            let diary_type = this.lease_diary_add_arr.diary_type.value;
            let diary_entry = this.lease_diary_add_arr.diary_entry;
            let diary_date = this.lease_diary_add_arr.diary_date;

            if (diary_date === '') errorArr.push(['You have not entered a valid diary date.']);
            if (diary_type === '') errorArr.push(['You have not entered a valid diary type.']);
            if (diary_entry === '') errorArr.push(['You have not entered a valid diary entry.']);
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                this.show_progress = true;
                let diary_arr = [];
                diary_arr[0] = this.lease_diary_add_arr;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('lease_diary_list', JSON.stringify(diary_arr));
                form_data.append('no_load', true);

                let apiUrl = '';
                if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/lease/update-or-create/diary';
                else apiUrl = this.cirrus8_api_url + 'api/temp/lease/update-or-create/diary';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.edit_form = this.edit_flag;

                    this.loadForm();

                    if (this.lease_diary_add_arr.index === 'New')
                        this.lease_diary_add_arr.index = this.lease_diary_list.length;
                    this.lease_diary_add_arr.diary_id = response.data.diary_id;
                    this.lease_diary_add_arr.status = 'saved';

                    this.loading_setting = false;
                    this.show_progress = false;
                    this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        showCommentModal: function (index) {
            this.diary_modal_list_temp = this.lease_diary_list[index];
            this.show_comment_modal = true;
        },
        addDiaryComment: function () {
            this.error_diary_new_comment = [];
            let diary_modal_list_temp = this.diary_modal_list_temp;
            let diary_id = diary_modal_list_temp.diary_id;
            let diary_modal_new_comment = this.diary_modal_new_comment;
            if (diary_modal_new_comment === '')
                this.error_diary_new_comment.push(['You have not entered a valid diary comment.']);
            if (this.error_diary_new_comment.length === 0) {
                this.show_progress = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                form_data.append('diary_id', diary_id);
                form_data.append('diary_modal_new_comment', diary_modal_new_comment);
                form_data.append('client_timezone', this.clientTimezone);
                let apiUrl = '';
                if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/lease/create/diary-comment';
                else apiUrl = this.cirrus8_api_url + 'api/temp/lease/create/diary-comment';

                this.$api.post(apiUrl, form_data).then((response) => {
                    this.diary_modal_new_comment = '';
                    let new_diary_comment = response.data.diary_comment;
                    this.diary_modal_list_temp.diary_comment_list.push(new_diary_comment);
                    for (let x = 0; x <= this.lease_diary_list.lenth - 1; x++) {
                        let diary_id = this.lease_diary_list[x].diary_id;
                        if (diary_id === diary_id) this.lease_diary_list[x].diary_comment_list.push(new_diary_comment);
                    }
                    this.show_progress = false;
                });
            }
        },
        updateDiaryComment: function (index) {
            this.error_diary_new_comment = [];
            let diary_modal_list_temp = this.diary_modal_list_temp;
            let diary_id = diary_modal_list_temp.diary_id;
            let diary_modal_comment = diary_modal_list_temp.diary_comment_list[index].diary_comment;
            let diary_comment_id = diary_modal_list_temp.diary_comment_list[index].diary_comment_id;
            if (diary_modal_comment === '')
                this.error_diary_update_comment.push(['You have not entered a valid diary comment.']);

            if (this.error_diary_new_comment.length === 0) {
                this.show_progress = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('diary_id', diary_id);
                form_data.append('diary_comment_id', diary_comment_id);
                form_data.append('diary_modal_new_comment', diary_modal_comment);
                form_data.append('client_timezone', this.clientTimezone);
                let apiUrl = '';
                if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/lease/update/diary-comment';
                else apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/diary-comment';
                this.$api.post(apiUrl, form_data).then((response) => {
                    let diary_comment = response.data.diary_comment;
                    this.diary_modal_list_temp.diary_comment_list[index] = diary_comment;
                    for (let x = 0; x <= this.lease_diary_list.lenth - 1; x++) {
                        let diary_id = this.lease_diary_list[x].diary_id;
                        if (diary_id === diary_id) {
                            for (let y = 0; y <= this.lease_diary_list[x].diary_comment_list; y++)
                                this.lease_diary_list[x].diary_comment_list[y] = diary_comment;
                        }
                    }
                    this.show_progress = false;
                });
            }
        },
        async deleteDiaryComment(index) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                let diary_modal_list_temp = this.diary_modal_list_temp;
                let diary_id = diary_modal_list_temp.diary_id;

                let diary_modal_comment = diary_modal_list_temp.diary_comment_list[index].diary_comment;
                let diary_comment_id = diary_modal_list_temp.diary_comment_list[index].diary_comment_id;
                this.show_progress = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('diary_id', diary_id);
                form_data.append('diary_comment_id', diary_comment_id);
                form_data.append('diary_modal_new_comment', diary_modal_comment);
                let apiUrl = '';
                if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/lease/delete/diary-comment';
                else apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/diary-comment';

                this.$api.post(apiUrl, form_data).then((response) => {
                    this.diary_modal_list_temp.diary_comment_list.splice(index, 1);
                    for (let x = 0; x <= this.lease_diary_list.lenth - 1; x++) {
                        let diary_id = this.lease_diary_list[x].diary_id;
                        if (diary_id === diary_id) this.lease_diary_list[x].diary_comment_list.splice(index, 1);
                    }
                    this.show_progress = false;
                });
            }
        },
        modalPrevData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_diary_add_arr.index;
            if (current_index === 'New') this.modal_current_ctr = 0;
            else {
                current_index = current_index - 1;
                if (current_index === -1) this.modal_current_ctr = this.lease_diary_list.length - 1;
                else this.modal_current_ctr = current_index;
            }
            if (this.lease_diary_list[this.modal_current_ctr].diary_complete !== '') this.modalPrevData();
            this.lease_diary_add_arr = this.lease_diary_list[this.modal_current_ctr];
            this.lease_diary_add_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_diary_add_arr.index;
            if (current_index === 'New') this.modal_current_ctr = 0;
            else {
                current_index = current_index + 1;
                if (current_index > this.lease_diary_list.length - 1) this.modal_current_ctr = 0;
                else this.modal_current_ctr = current_index;
            }
            if (this.lease_diary_list[this.modal_current_ctr].diary_complete !== '') this.modalNextData();
            this.lease_diary_add_arr = this.lease_diary_list[this.modal_current_ctr];
            this.lease_diary_add_arr.index = this.modal_current_ctr;
        },
        copyDiaryItem: function () {
            let errorArr = [];
            let diary_type = this.lease_diary_add_arr.diary_type.value;
            let diary_entry = this.lease_diary_add_arr.diary_entry;
            let diary_date = this.lease_diary_add_arr.diary_date;
            let diary_id = this.lease_diary_add_arr.diary_id;
            if (this.diary_date === '' || this.diary_date === null)
                errorArr.push(['You have not entered a valid diary date.']);
            if (this.diary_entry === '') errorArr.push(['You have not entered a valid diary entry.']);
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                this.show_progress = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('diary_id', diary_id);
                form_data.append('diary_type', diary_type);
                form_data.append('diary_date', diary_date);
                form_data.append('diary_note', diary_entry);
                form_data.append('copy_diary_comment', this.copy_diary_comment);
                form_data.append('no_load', true);
                let apiUrl = 'home/copy-temp-diary';
                if (this.isLeaseFormLive()) apiUrl = 'home/copy-diary';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loading_setting = false;
                    this.AED_modal = false;
                    this.loadForm();
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        changeHeader: function () {
            if (this.toggle_show_completed_item && this.toggle_show_deleted_item) {
                this.headers = [
                    { text: '#', value: 'index', sortable: false, width: '40px' },
                    { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                    { text: 'Diary Date', value: 'diary_date_raw', width: '15%' },
                    { text: 'Diary Entry Type', value: 'diary_entry_type_description', width: '20%' },
                    { text: 'Diary Note', value: 'diary_entry', width: '20%' },
                    { text: 'Comments', value: 'comments', sortable: false, width: '5%' },
                    { text: 'Completed', value: 'diary_completed_by', sortable: false, width: '30%' },
                    { text: 'Deleted', value: 'diary_deleted_by', sortable: false, width: '30%' },
                    { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
                ];
            }
            if (this.toggle_show_completed_item && !this.toggle_show_deleted_item) {
                this.headers = [
                    { text: '#', value: 'index', sortable: false, width: '40px' },
                    { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                    { text: 'Diary Date', value: 'diary_date_raw', width: '15%' },
                    { text: 'Diary Entry Type', value: 'diary_entry_type_description', width: '20%' },
                    { text: 'Diary Note', value: 'diary_entry', width: '20%' },
                    { text: 'Comments', value: 'comments', sortable: false, width: '5%' },
                    { text: 'Completed', value: 'diary_completed_by', sortable: false, width: '30%' },
                    { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
                ];
            }
            if (!this.toggle_show_completed_item && this.toggle_show_deleted_item) {
                this.headers = [
                    { text: '#', value: 'index', sortable: false, width: '40px' },
                    { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                    { text: 'Diary Date', value: 'diary_date_raw', width: '15%' },
                    { text: 'Diary Entry Type', value: 'diary_entry_type_description', width: '20%' },
                    { text: 'Diary Note', value: 'diary_entry', width: '20%' },
                    { text: 'Comments', value: 'comments', sortable: false, width: '5%' },
                    { text: 'Deleted', value: 'diary_deleted_by', sortable: false, width: '30%' },
                    { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
                ];
            }
            if (!this.toggle_show_completed_item && !this.toggle_show_deleted_item) {
                this.headers = [
                    { text: '#', value: 'index', sortable: false, width: '40px' },
                    { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                    { text: 'Diary Date', value: 'diary_date_raw', width: '15%' },
                    { text: 'Diary Entry Type', value: 'diary_entry_type_description', width: '20%' },
                    { text: 'Diary Note', value: 'diary_entry', width: '20%' },
                    { text: 'Comments', value: 'comments', sortable: false, width: '5%' },
                    { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
                ];
            }
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        toggle_show_completed_item: function () {
            this.changeHeader();
        },
        toggle_show_deleted_item: function () {
            this.changeHeader();
        },
    },
    created() {
        bus.$on('loadLeaseDiarySection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
<style>
.deleted_diary_style {
    background: #f7f7f7;
    color: #b2b2b2;
}
.deleted_diary_style .text-start .form-row .form-input-text {
    color: #b2b2b2 !important;
}
</style>
