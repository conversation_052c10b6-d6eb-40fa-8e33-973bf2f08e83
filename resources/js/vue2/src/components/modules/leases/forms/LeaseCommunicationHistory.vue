<template>
    <div
        style="max-width: 100%"
        v-on:dblclick="doubleClickForm()"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Communication History</h6>
                &nbsp
                <v-btn-toggle
                    class="form-toggle"
                    v-model="communication_type"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Email
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        SMS
                    </v-btn>
                </v-btn-toggle>
                <v-spacer></v-spacer>
                <h6
                    class="title"
                    v-show="limit_by === 0"
                >
                    Latest 50 records
                </h6>
                &nbsp
                <v-btn
                    v-show="limit_by === 0"
                    x-small
                    tile
                    text
                    @click="limit_by = 1"
                >
                    Show all
                </v-btn>
                &nbsp &nbsp
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        communication_type === 0
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly && !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    v-if="edit_form"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="show_activity_log_modal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div class="page-form">
            <div
                class="form-row"
                v-show="communication_type === 0"
            >
                <lease-email-component
                    :property_code="property_code"
                    :lease_code="lease_code"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :edit_form="edit_form"
                    :is_inactive="is_inactive"
                ></lease-email-component>
            </div>
            <div
                class="form-row"
                v-show="communication_type === 1"
            >
                <lease-sms-component
                    :property_code="property_code"
                    :lease_code="lease_code"
                    :version_id="version_id"
                    :read_only="read_only"
                    :page_form_type="page_form_type"
                    :edit_form="edit_form"
                    :is_inactive="is_inactive"
                ></lease-sms-component>
            </div>
        </div>

        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import LeaseEmail from './LeaseEmail.vue';
import LeaseSMS from './LeaseSMS.vue';
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    components: {
        'lease-email-component': LeaseEmail,
        'lease-sms-component': LeaseSMS,
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_COM_HISTORY',
            loading_setting: true,
            edit_form: false,
            show_activity_log_modal: false,
            communication_type: 0,
            readonly: this.read_only,
            sms_message: '',
            limit_by: 0,
        };
    },
    mounted() {
        this.loading_setting = false;
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_lease_form_read_only']),
    },
    methods: {
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                )
                    this.edit_form = true;
                else this.edit_form = false;
            }
        },
        loadForm: function () {
            let eventData = {
                limit_by: this.getLimitByValue(),
                property_code: this.property_code,
                lease_code: this.lease_code,
            };
            bus.$emit('loadLeaseEmailSection', eventData);
            bus.$emit('loadLeaseSMSSection', eventData);
        },
        modalAddData: function () {
            var d = new Date();
            bus.$emit('openEMAILAEModal', {
                history_id: '',
                category: '',
                recipients: '',
                subject: '',
                date_sent: d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                attachments: null,
                attachments_old: null,
            });
        },
        resetForm: function () {
            this.edit_form = false;
            this.loadForm();
        },
        handleWatchedProperties() {
            let eventData = {
                limit_by: this.getLimitByValue(),
                property_code: this.property_code,
                lease_code: this.lease_code,
            };

            if (this.communication_type === 0) {
                bus.$emit('loadLeaseEmailSection', eventData);
            } else if (this.communication_type === 1) {
                bus.$emit('loadLeaseSMSSection', eventData);
            }
        },
        getLimitByValue() {
            switch (this.limit_by) {
                case 1:
                    return -1;
                case 0:
                default:
                    return 50;
            }
        },
    },
    watch: {
        communication_type: 'handleWatchedProperties',
        property_code: 'handleWatchedProperties',
        lease_code: 'handleWatchedProperties',
        limit_by: 'handleWatchedProperties',
    },
    created() {
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
