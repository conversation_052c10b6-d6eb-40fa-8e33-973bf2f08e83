<style>
.icon.indemnity-limit-field-icon {
    font-size: 12px;
    top: 10px !important;
    left: 0px !important;
    font-weight: 500;
}
</style>
<template>
    <div
        v-on:dblclick="doubleClickForm()"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Lease Insurance</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form && lease_insurance_list.length > 0"
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly && !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-col
                    class="text-center"
                    v-if="lease_insurance_list.length === 0"
                    v-show="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-btn
                        v-if="!pmro_read_only"
                        depressed
                        small
                        color="success"
                        @click="modalAddData()"
                        >Add Insurance</v-btn
                    >
                    <div
                        style="margin: 10px 0px"
                        v-else
                    >
                        No lease insurances at the moment
                    </div>
                </v-col>
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="lease_insurance_list.length > 0"
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="lease_insurance_list"
                    :items-per-page="items_per_page"
                    hide-default-footer
                    :page.sync="page"
                    :total-visible="7"
                    @page-count="page_count = $event"
                    :search="search_datatable"
                    :calculate-widths="true"
                >
                    <template v-slot:item.index="{ item }">
                        {{ lease_insurance_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.item_no="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.item_no }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_type_description="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_type_description }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_insurer="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_insurer }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_premium_amount="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><span
                                    v-if="
                                        item.insurance_premium_amount !== '' && item.insurance_premium_amount !== null
                                    "
                                    >{{ currency_symbol }}</span
                                >{{
                                    accountingAmountFormat(numberWithCommas(roundTo(item.insurance_premium_amount, 2)))
                                }}</span
                            >
                        </div>
                    </template>
                    <template v-slot:item.insurance_policy_number="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_policy_number }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_start_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_start_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_exp_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_exp_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_paid_date_raw="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_paid_date }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_notes="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text">{{ item.insurance_notes }}</span>
                        </div>
                    </template>
                    <template v-slot:item.insurance_file="{ item }">
                        <cirrus-single-upload-button2
                            :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                            v-model="item.insurance_file"
                            accept_type="pdf"
                            :has_saved_file="
                                item.insurance_file_old !== '' &&
                                (typeof item.insurance_file_old === 'string' ||
                                    item.insurance_file_old instanceof String)
                                    ? true
                                    : false
                            "
                            :edit_form="false"
                            :error_msg="error_msg"
                            :size_limit="20"
                        ></cirrus-single-upload-button2>
                    </template>
                    <template v-slot:item.insurance_diarise="{ item }">
                        <div class="form-row no-border-line">
                            <span class="form-input-text"
                                ><label>{{ item.insurance_diarise_id ? '' : 'Not ' }}Diarised</label></span
                            >
                        </div>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            small
                            @click="modalOpenAED(lease_insurance_list.indexOf(item))"
                            v-if="edit_form"
                            >fas fa-edit
                        </v-icon>
                        <v-icon
                            color="red"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="deleteInsurance(lease_insurance_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="lease_insurance_list.length > 5"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="page"
                                        :length="page_count"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->
            </div>
        </div>
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.left="modalPrevData()"
            @keydown.ctrl.right="modalNextData()"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData()"
            @keydown.ctrl.delete="deleteInsurance(lease_insurance_arr.index)"
        >
            <v-card>
                <v-card-title class="headline">
                    Insurance Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="lease_insurance_arr.index_id">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        lease_insurance_arr.index === 'New'
                                            ? lease_insurance_arr.index
                                            : lease_insurance_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        data-inverted=""
                                        openDirection="bottom"
                                        v-model="lease_insurance_arr.insurance_type"
                                        :options="lease_insurance_type_list"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-800"
                                        group-label="language"
                                        placeholder="Select a insurance type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Insurer</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="lease_insurance_arr.insurance_insurer"
                                        size=""
                                        :id="'insurance_insurer'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Indemnity Limit</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <div class="ui left icon input">
                                        <cirrus-input
                                            v-model="lease_insurance_arr.insurance_premium_amount"
                                            :inputFormat="'dollar'"
                                            size=""
                                            :id="'insurancePolicyAmount'"
                                            data-inverted=""
                                            :edit_form="true"
                                            :error_msg="error_msg"
                                        ></cirrus-input>
                                        <i class="icon indemnity-limit-field-icon">{{ currency_symbol }}</i>
                                    </div>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Policy Number</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="lease_insurance_arr.insurance_policy_number"
                                        size=""
                                        :id="'insurance_policy_number'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Start Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'lease_insurance_start_date' + String(Math.random()).replace('.', '')"
                                        v-model="lease_insurance_arr.insurance_start_date"
                                        :size="'40'"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Expiry Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'lease_insurance_expiry_date' + String(Math.random()).replace('.', '')"
                                        v-model="lease_insurance_arr.insurance_exp_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>

                                    <span
                                        v-bind:class="lease_insurance_arr.insurance_diarise_id ? 'hidden' : ''"
                                        style="position: absolute; margin-top: 4px"
                                    >
                                        <v-checkbox
                                            v-model="lease_insurance_arr.insurance_diarise"
                                            label="Diarise"
                                            ripple="false"
                                            dense
                                        ></v-checkbox>
                                    </span>
                                    <label
                                        class="form-input-text"
                                        v-bind:class="!lease_insurance_arr.insurance_diarise_id ? 'hidden' : ''"
                                        >{{ lease_insurance_arr.insurance_diarise_id ? '' : 'Not ' }}Diarised</label
                                    >
                                    <input
                                        type="hidden"
                                        v-model="lease_insurance_arr.insurance_diarise"
                                        :id="'insurance_diarise'"
                                        :error_msg="error_msg"
                                    />
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Paid Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'lease_insurance_paid_date' + String(Math.random()).replace('.', '')"
                                        v-model="lease_insurance_arr.insurance_paid_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Note</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="lease_insurance_arr.insurance_notes"
                                        size=""
                                        :id="'insurance_notes'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >File(s)</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-upload-button2
                                        :id="'lease_insurance_file_' + lease_insurance_arr.index"
                                        v-model="lease_insurance_arr.insurance_file"
                                        :has_saved_file="
                                            lease_insurance_arr.insurance_file_old !== '' &&
                                            (typeof lease_insurance_arr.insurance_file_old === 'string' ||
                                                lease_insurance_arr.insurance_file_old instanceof String)
                                                ? true
                                                : false
                                        "
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                        :size_limit="20"
                                    ></cirrus-single-upload-button2>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalPrevData()"
                        data-tooltip="CTR + LEFT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_previous</v-icon
                        >
                        Previous
                    </v-btn>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + SHIFT + ENTER"
                        color="primary"
                        dark
                        depressed
                        small
                        v-if="lease_insurance_arr.status !== 'new'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >add</v-icon
                        >
                        Add New
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="lease_insurance_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        data-tooltip="CTR + DEL"
                        @click="deleteInsurance(lease_insurance_arr.index)"
                        color="error"
                        dark
                        depressed
                        small
                        v-if="lease_insurance_arr.index !== 'New'"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Delete
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalNextData()"
                        data-tooltip="CTR + RIGHT"
                        color="primary"
                        dark
                        depressed
                        small
                    >
                        Next
                        <v-icon
                            left
                            dark
                            size="18"
                            >skip_next</v-icon
                        >
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_INSURANCE',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            lease_insurance_list: [],
            lease_insurance_list_old: [],
            lease_insurance_type_list: [],
            readonly: this.read_only,
            show_activity_log_modal: false,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Type', value: 'insurance_type_description', sortable: false, width: '10%' },
                { text: 'Insurer', value: 'insurance_insurer', sortable: false, width: '15%' },
                {
                    text: 'Indemnity Limit',
                    value: 'insurance_premium_amount',
                    align: 'end',
                    sortable: false,
                    width: '10%',
                },
                { text: 'Policy No.', value: 'insurance_policy_number', sortable: false, width: '10%' },
                { text: 'Start Date', value: 'insurance_start_date_raw', width: '8%' },
                { text: 'Exp. Date', value: 'insurance_exp_date_raw', width: '8%' },
                { text: 'Paid Date', value: 'insurance_paid_date_raw', width: '8%' },
                { text: 'Note', value: 'insurance_notes', width: '10%' },
                { text: 'File(s)', value: 'insurance_file', sortable: false, width: '8%' },
                { text: 'Diarise', value: 'insurance_diarise', sortable: false, width: '8%' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            AED_modal: false,
            lease_insurance_arr: [],
            modal_current_ctr: 0,
            success_flag: false,
            currency_symbol: '$',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'auto_diarise',
        ]),
    },
    methods: {
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadLeaseInsurance();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_insurance_list = [];
            this.lease_insurance_list = JSON.parse(JSON.stringify(this.lease_insurance_list_old));
        },
        modalAddData: function () {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.lease_insurance_arr = [];
            let new_object = {
                index_id: Math.random(),
                index: 'New',
                insurance_id: '',
                insurance_type: {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                insurance_insurer: '',
                insurance_premium_amount: '',
                insurance_policy_number: '',
                insurance_start_date: null,
                // "insurance_exp_date": d.getDate() + '/' + ("0" + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                insurance_exp_date: null,
                insurance_paid_date: null,
                insurance_notes: '',
                insurance_file: null,
                insurance_file_old: null,
                insurance_diarise_id: '',
                status: 'new',
                insurance_diarise: this.auto_diarise,
            };
            this.lease_insurance_arr = this.objectClone(new_object);
        },
        loadLeaseInsurance: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';

            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/insurance';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/insurance';
            }
            //  apiUrl = this.cirrus8_api_url + 'api/lease/loadTempLeaseInsurance';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_insurance_list = response.data.lease_insurance_list;
                this.lease_insurance_list_old = response.data.lease_insurance_list;
                this.lease_insurance_type_list = response.data.lease_insurance_type_list;
                if (this.formSectionReadOnly(this.pm_lease_form_read_only, this.form_type, this.form_section)) {
                    this.items_per_page = this.lease_insurance_list.length;
                }
                this.currency_symbol = response.data.currency_symbol;
                this.loading_setting = false;
            });
        },
        modalSubmitData: function () {
            let errorArr = [];
            let insurance_type = this.lease_insurance_arr.insurance_type.value;
            let insurance_insurer = this.lease_insurance_arr.insurance_insurer;
            let insurance_policy_number = this.lease_insurance_arr.insurance_policy_number;
            let insurance_exp_date = this.lease_insurance_arr.insurance_exp_date;
            let insurance_premium_amount = this.lease_insurance_arr.insurance_premium_amount;

            if (insurance_type === '') {
                errorArr.push(['You have not entered a valid policy type for the insurance.']);
            }
            if (insurance_insurer === '') {
                errorArr.push(['You have not entered a valid insurer for the insurance.']);
            }
            if (insurance_policy_number === '') {
                errorArr.push(['You have not entered a valid policy no for the insurance.']);
            }
            if (insurance_exp_date === '' || insurance_exp_date === null) {
                errorArr.push(['You have not entered a valid expiry date for the insurance.']);
            }

            if (insurance_premium_amount === '' || !insurance_premium_amount) {
            } else {
                if (isNaN(parseFloat(insurance_premium_amount))) {
                    errorArr.push(['You have not used a valid Indemnity Limit.']);
                }
            }

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                let save_arr = [];
                save_arr[0] = this.lease_insurance_arr;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('lease_insurance_list', JSON.stringify(save_arr));

                let insurance_file = [];
                if (this.lease_insurance_arr.insurance_file)
                    insurance_file = this.lease_insurance_arr.insurance_file[0];
                form_data.append('lease_insurance_file', insurance_file);

                let apiUrl = '';
                if (this.isLeaseFormLive())
                    apiUrl = this.cirrus8_api_url + 'api/with-file-upload/lease/update-or-create/insurance';
                else apiUrl = this.cirrus8_api_url + 'api/with-file-upload/temp/lease/update-or-create/insurance';
                // apiUrl = this.cirrus8_api_url + 'api/lease/saveTempLeaseInsurance';
                this.$api
                    .post(apiUrl, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.error_server_msg2 = response.data.error_server_msg2;
                        if (this.error_server_msg2.length === 0) {
                            if (response.data.diary == 1) {
                                bus.$emit('loadLeaseDiarySection', '');
                            }
                            this.lease_insurance_list = [];
                            this.loadForm();
                            this.edit_form = this.edit_flag;

                            if (this.lease_insurance_arr.status === 'new') {
                                this.lease_insurance_arr.index = this.lease_insurance_list.length;
                                this.lease_insurance_arr.insurance_id = response.data.insurance_id;
                                this.lease_insurance_arr.insurance_file = response.data.insurance_file;
                                this.lease_insurance_arr.insurance_file_old = response.data.insurance_file;
                                this.lease_insurance_arr.status = 'saved';
                            }

                            this.loading_setting = false;
                            if (!response.data.error) this.success_flag = true;
                            setTimeout(
                                function () {
                                    this.success_flag = false;
                                }.bind(this),
                                2000,
                            );
                        }
                    });
            }
        },
        async deleteInsurance(index) {
            if (index !== 'New') {
                let status = this.lease_insurance_list[index].status;
                let insurance_id = this.lease_insurance_list[index].insurance_id;

                if (status === 'new') {
                    this.lease_insurance_list.splice(index, 1);
                } else {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1) {
                        this.loading_setting = true;
                        var form_data = new FormData();
                        form_data.append('property_code', this.property_code);
                        form_data.append('lease_code', this.lease_code);
                        form_data.append('version_id', this.version_id);
                        form_data.append('insurance_id', insurance_id);
                        form_data.append('no_load', true);
                        let apiUrl = '';
                        if (this.isLeaseFormLive()) {
                            apiUrl = this.cirrus8_api_url + 'api/lease/delete/insurance';
                        } else {
                            apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/insurance';
                        }
                        //  apiUrl = this.cirrus8_api_url + 'api/lease/deleteTempLeaseInsurance';
                        this.$api.post(apiUrl, form_data).then((response) => {
                            this.lease_insurance_list.splice(index, 1);
                            this.loading_setting = false;

                            this.lease_insurance_list_old = JSON.parse(JSON.stringify(this.lease_insurance_list));
                        });
                    }
                }
            }
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadInsurance_' + id;
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        modalPrevData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_insurance_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index - 1;
                if (current_index === -1) {
                    this.modal_current_ctr = this.lease_insurance_list.length - 1;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.lease_insurance_arr = this.objectClone(this.lease_insurance_list[this.modal_current_ctr]);
            this.lease_insurance_arr.index = this.modal_current_ctr;
        },
        modalNextData: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let current_index = this.lease_insurance_arr.index;
            if (current_index === 'New') {
                this.modal_current_ctr = 0;
            } else {
                current_index = current_index + 1;
                if (current_index > this.lease_insurance_list.length - 1) {
                    this.modal_current_ctr = 0;
                } else {
                    this.modal_current_ctr = current_index;
                }
            }
            this.lease_insurance_arr = this.objectClone(this.lease_insurance_list[this.modal_current_ctr]);
            this.lease_insurance_arr.index = this.modal_current_ctr;
        },
        modalOpenAED: function (index) {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_insurance_arr = this.objectClone(this.lease_insurance_list[index]);
            this.lease_insurance_arr.index = index;
            this.modal_current_ctr = index;
            bus.$emit('refreshSingleUploadComponent', '');
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadLeaseInsuranceSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
