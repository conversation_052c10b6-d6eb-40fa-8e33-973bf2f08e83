<style>
.tableRaw td {
    margin: 0px;
    padding: 0px;
    border-bottom: 0px;
}
</style>
<template>
    <div v-on:dblclick="!read_only ? (edit_form = true) : (edit_form = false)">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Unit History</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="!read_only"
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader
            type="table-tbody"
            v-if="loading_setting"
        ></cirrus-content-loader>
        <v-data-table
            class="c8-datatable-custom"
            v-show="lease_unit_history_list.length > 0"
            dense
            item-key="id"
            :headers="headers"
            :items="lease_unit_history_list"
            :items-per-page="items_per_page"
            hide-default-footer
            :page.sync="page"
            :total-visible="7"
            @page-count="page_count = $event"
            :search="search_datatable"
        >
            <template v-slot:item.index="{ item }">
                {{ lease_unit_history_list.indexOf(item) + 1 }}
            </template>

            <template v-slot:item.unit_area_status_description="{ item }">
                {{ item.unit_area_status_description }}
            </template>

            <template v-slot:item.unit_area_start_date_raw="{ item }">
                {{ item.unit_area_start_date === '01/01/1900' ? '--' : item.unit_area_start_date }}
            </template>

            <template v-slot:item.unit_area_end_date_raw="{ item }">
                {{ item.unit_area_end_date === '31/12/2999' ? '--' : item.unit_area_end_date }}
            </template>

            <template v-slot:item.lease_code="{ item }">
                <a v-on:click="goToShortcut('lease', property_code, item.lease_code)">{{ item.lease_code }}</a>
            </template>
        </v-data-table>
        <v-row
            class="form-row"
            v-show="lease_unit_history_list.length > 5"
        >
            <v-col
                xs="12"
                sm="12"
                md="12"
            >
                <table class="c8-datatable-custom-footer">
                    <tr>
                        <td class="">Rows per page:</td>
                        <td>
                            <multiselect
                                v-model="items_per_page"
                                :options="[5, 10, 15]"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-200"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </td>
                        <td></td>
                        <td>
                            <v-pagination
                                v-model="page"
                                :length="page_count"
                            ></v-pagination>
                        </td>
                    </tr>
                </table>
            </v-col>
        </v-row>
    </div>
</template>

<script>
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';
import { mapState } from 'vuex';
import axios from 'axios';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        lease_unit_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        current_db: { type: String, default: '' },
        username: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        page_form_type: { type: String, default: '' },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_UNIT_HISTORY',
            cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            lease_unit_history_list: [],
            lease_unit_status_description_list: [],
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Status', value: 'unit_area_status_description', width: '15%' },
                { text: 'From', value: 'unit_area_start_date_raw', width: '20%' },
                { text: 'To', value: 'unit_area_end_date_raw', width: '20%' },
                { text: 'Lease', value: 'lease_code', width: '40%' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'auto_diarise',
        ]),
    },
    mounted() {
        this.loading_setting = false;
        this.lease_unit_status_description_list['V'] = 'Vacant';
        this.lease_unit_status_description_list['O'] = 'Occuppied';
        this.lease_unit_status_description_list['I'] = 'Inactive';
        // this.loadForm();
    },
    methods: {
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadUnitHistory();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
        },
        addLine: function () {},
        loadUnitHistory: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('lease_unit_code', this.lease_unit_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            // let apiUrl = this.cirrus8_api_url + 'api/lease/fetch/unit-history';
            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/unit-history';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/unit-history';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_unit_history_list = response.data.lease_unit_history_list;
                this.loading_setting = false;
            });
        },
        saveForm: function () {},
        goToShortcut: function (parameter, code1 = '', code2 = '') {
            let property_code = '';
            let lease_code = '';
            switch (parameter) {
                case 'property':
                    property_code = this.property_code;
                    if (code1 !== '') {
                        property_code = code1;
                    }
                    if (this.user_type !== 'A') {
                        window.open(
                            '?module=properties&command=property_summary_page&property_code=' + property_code,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    } else {
                        window.open(
                            '?module=properties&command=v2_manage_property_page&property_code=' + property_code,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    }

                    break;
                case 'lease':
                    lease_code = this.lease_code.fieldKey;
                    property_code = '';
                    if (code1 !== '' && code2 !== '') {
                        property_code = code1;
                        lease_code = code2;
                        if (this.user_type === 'A') {
                            window.open(
                                '?module=leases&command=lease_page_v2&property_code=' +
                                    property_code +
                                    '&lease_code=' +
                                    lease_code,
                                '_blank', // <- This is what makes it open in a new window.
                            );
                        } else {
                            window.open(
                                '?module=leases&command=lease_summary_page&property_code=' +
                                    property_code +
                                    '&lease_code=' +
                                    lease_code,
                                '_blank', // <- This is what makes it open in a new window.
                            );
                        }
                    }
                    break;
                case 'company':
                    //module=companies&command=company&companyID=7ELEVEN
                    if (this.user_type !== 'A') {
                        window.open(
                            '?module=companies&command=companySummary&action=view&companyID=' +
                                this.lease_company.field_key,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    } else {
                        window.open(
                            '?module=companies&command=company&companyID=' + this.lease_company.field_key,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    }

                    break;
                case 'lease_abstract':
                    this.loading_page_setting = true;
                    var today = new Date();
                    var dd = today.getDate();
                    var mm = today.getMonth() + 1;
                    var yyyy = today.getFullYear();
                    var single_date = yyyy + '-' + mm + '-' + dd;
                    var form_data = new FormData();
                    form_data.append('properties', this.property_code);
                    form_data.append('leases', this.lease_code);
                    form_data.append('no_load', true);
                    form_data.append('user_type', localStorage.getItem('user_type'));
                    if (sessionStorage.getItem('sso_key'))
                        form_data.append('app_key', sessionStorage.getItem('sso_key'));
                    form_data.append('report_ids', '21');
                    form_data.append('singleDate', single_date);
                    form_data.append('format', 'pdf');
                    axios.post(this.cirrus8_api_url + 'api/reports/download/pdf', form_data).then((response) => {
                        this.loading_page_setting = false;
                        let blob = new Blob([response.data], { type: 'application/pdf' });
                        let a = document.createElement('a');
                        a.style = 'display: none';
                        document.body.appendChild(a);
                        let url = window.URL.createObjectURL(blob);
                        a.href = url;
                        var fileName = 'Lease_Abstract';
                        a.download = fileName + '.pdf';
                        a.click();
                    });
                    break;
                case 'tenant_activity_current_month':
                    window.open(
                        '?module=managementReports&command=tenantActivity&propertyID=' +
                            this.property_code +
                            '&tenantID=' +
                            this.lease_code +
                            '',
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'tenant_activity_all_date':
                    window.open(
                        '?module=managementReports&command=tenantActivity&propertyID=' +
                            this.property_code +
                            '&tenantID=' +
                            this.lease_code +
                            '&api_allDates',
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'view_tax_invoice':
                    window.open(
                        '?module=ar&command=viewTaxInvoice&propertyID=' +
                            this.property_code +
                            '&leaseID=' +
                            this.lease_code +
                            '',
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'generate_arrears_statement_download':
                    this.loading_page_setting = true;
                    let url =
                        '?module=ar&command=generateArrearsStatement&action=dlLetterSingle_' +
                        this.property_code +
                        '~' +
                        this.lease_code +
                        '';
                    var form_data = new FormData();
                    form_data.append('showCurrentOnly', 'Yes');
                    form_data.append('includeCredits', 'Yes');
                    form_data.append('ccMe', '1');
                    form_data.append('exclude_letter', 'true');
                    form_data.append('property', this.property_code);
                    form_data.append('tenantID', this.lease_code);
                    form_data.append('app_origin', 'lease_page');
                    this.$api.post(url, form_data).then((response) => {
                        this.loading_page_setting = false;
                        let position = response.data.toUpperCase().search('ERROR');
                        if (position > -1) this.$noty.warning('No file to download');
                        else document.location.href = 'download.php?fileID=' + response.data;
                    });
                    break;
                case 'print':
                    window.open(
                        '?module=leases&command=lease_print_page&is_live=1&property_code=' +
                            this.property_code +
                            '&lease_code=' +
                            this.lease_code +
                            '&version_id=' +
                            this.version_id,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
            }
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        lease_unit_code: function () {
            this.loadForm();
        },
        forceLoad: function () {
            if (this.forceLoad) {
                this.loadForm();
            }
        },
    },
    created() {
        bus.$on('loadLeaseUnitHistorySection', (data) => {
            if (data.unit_code !== '') {
                this.lease_unit_code = data.unit_code;
            }
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
