<template>
    <div
        v-on:dblclick="doubleClickForm()"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Lease Inspection</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                    v-model="search_datatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>

                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="
                        edit_form &&
                        (lease_inspection_arr.length > 0 ||
                            lease_inspection_complete_list.length > 0 ||
                            lease_inspection_incomplete_list.length > 0)
                    "
                    icon
                    @click="resetForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !readonly && !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_server_msg2"
        ></cirrus-server-error>
        <div
            class="page-form"
            v-if="!loading_setting"
        >
            <div class="form-row">
                <v-col
                    class="text-center"
                    v-if="lease_inspection_incomplete_list.length === 0 && lease_inspection_complete_list.length === 0"
                    v-show="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
                >
                    <v-btn
                        v-if="!pmro_read_only"
                        depressed
                        small
                        color="success"
                        @click="modalAddData()"
                        >Add Inspection</v-btn
                    >
                    <div
                        style="margin: 10px 0px"
                        v-else
                    >
                        No lease inspections at the moment
                    </div>
                </v-col>
                <table
                    class="data-grid data-grid-dense"
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    v-if="lease_inspection_incomplete_list.length > 0"
                >
                    <tbody>
                        <tr class="subHeader">
                            <td class=""><b>Upcoming Inspection</b></td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="lease_inspection_incomplete_list.length > 0"
                    dense
                    item-key="id"
                    :headers="upcoming_headers"
                    :items="lease_inspection_incomplete_list"
                    :items-per-page="upcoming_items_per_page"
                    hide-default-footer
                    :page.sync="upcoming_page"
                    :total-visible="7"
                    @page-count="upcoming_page_count = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.index="{ item }">
                        {{ lease_inspection_incomplete_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.inspection_date_raw="{ item }">
                        {{ item.inspection_date }}
                    </template>
                    <template v-slot:item.inspection_id="{ item }">
                        {{ item.inspection_id }}
                    </template>
                    <template v-slot:item.inspection_type_value="{ item }">
                        {{ item.inspection_type_value }}
                    </template>
                    <template v-slot:item.inspection_template_value="{ item }">
                        {{ item.inspection_template_value }}
                    </template>
                    <template v-slot:item.inspection_charge="{ item }">
                        {{ currency_symbol
                        }}{{ accountingAmountFormat(numberWithCommas(roundTo(item.inspection_charge, 2))) }}
                    </template>
                    <template v-slot:item.inspection_comment="{ item }">
                        <cirrus-input
                            custom_class="cirrus-input-table-textbox"
                            v-model="item.inspection_comment"
                            size="50"
                            :id="'inspection_comment'"
                            data-inverted=""
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </template>
                    <template v-slot:item.inspection_diary="{ item }">
                        <span v-if="item.inspection_diary !== '1' && edit_form">
                            <sui-checkbox
                                v-model="item.inspection_diary"
                                data-inverted=""
                            />
                        </span>
                        <span v-else>
                            <label>{{ item.inspection_diary === '1' ? '' : 'Not ' }}Diarised</label>
                        </span>
                        <input
                            type="hidden"
                            v-model="item.inspection_diary"
                            :id="'inspection_diary'"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        />
                    </template>

                    <template v-slot:item.action_complete="{ item }">
                        <input
                            type="hidden"
                            v-model="item.inspection_complete"
                            :id="'inspection_complete'"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        />
                        <v-btn
                            x-small
                            class=""
                            v-if="edit_form"
                            @click="completeInspection(item)"
                            >Complete
                        </v-btn>
                    </template>
                    <template v-slot:item.action1="{ item }">
                        <v-icon
                            color="red"
                            v-show="!readonly"
                            v-if="edit_form"
                            @click="deleteInspection(lease_inspection_incomplete_list.indexOf(item))"
                            >close
                        </v-icon>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="lease_inspection_incomplete_list.length > 5"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="upcoming_items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="upcoming_page"
                                        :length="upcoming_page_count"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>
                <!--datatable end-->

                <table
                    class="data-grid data-grid-dense"
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    v-if="lease_inspection_arr.length > 0"
                >
                    <tbody>
                        <tr class="subHeader">
                            <td class="">&nbsp;</td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>

                <table
                    class="data-grid data-grid-dense"
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    v-if="lease_inspection_complete_list.length > 0"
                >
                    <tbody>
                        <tr class="subHeader">
                            <td class=""><b>Completed Inspection</b></td>
                            <td
                                class="required"
                                v-if="edit_form"
                            ></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
                <!--datatable start-->
                <v-data-table
                    class="c8-datatable-custom"
                    v-show="lease_inspection_complete_list.length > 0"
                    dense
                    item-key="id"
                    :headers="completed_headers"
                    :items="lease_inspection_complete_list"
                    :items-per-page="completed_items_per_page"
                    hide-default-footer
                    :page.sync="completed_page"
                    :total-visible="7"
                    @page-count="completed_page_count = $event"
                    :search="search_datatable"
                >
                    <template v-slot:item.index="{ item }">
                        {{ lease_inspection_complete_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.inspection_id="{ item }">
                        {{ item.inspection_id }}
                    </template>
                    <template v-slot:item.inspection_sched_date_raw="{ item }">
                        {{ item.inspection_sched_date }}
                    </template>
                    <template v-slot:item.inspection_complete_date_raw="{ item }">
                        <cirrus-icon-date-picker
                            :id="'lease_inspection_complete_date' + String(Math.random()).replace('.', '')"
                            v-model="item.inspection_complete_date"
                            :size="'40'"
                            data-inverted=""
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                        ></cirrus-icon-date-picker>
                    </template>
                    <template v-slot:item.inspection_complete_by="{ item }">
                        {{ item.inspection_complete_by }}
                    </template>
                    <template v-slot:item.inspection_type_value="{ item }">
                        {{ item.inspection_type_value }}
                    </template>
                    <template v-slot:item.inspection_file="{ item }">
                        <cirrus-single-upload-button2
                            :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                            v-model="item.inspection_file"
                            accept_type="pdf"
                            :has_saved_file="
                                item.inspection_file_old !== '' &&
                                (typeof item.inspection_file_old === 'string' ||
                                    item.inspection_file_old instanceof String)
                                    ? true
                                    : false
                            "
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                            :size_limit="20"
                        ></cirrus-single-upload-button2>
                    </template>

                    <template v-slot:item.action1="{ item }">
                        <div v-if="edit_form">
                            <v-btn
                                v-if="item.inspection_file"
                                x-small
                                class=""
                                @click="item.inspection_publish = item.inspection_publish * -1"
                                :class="[item.inspection_publish == 1 ? 'upload-btn-label' : '']"
                                >Publish
                            </v-btn>
                            <input
                                type="hidden"
                                v-model="item.inspection_publish"
                                :id="'inspection_publish'"
                                :edit_form="edit_form"
                                :error_msg="error_msg"
                            />

                            <v-btn
                                v-if="item.inspection_file_raw"
                                x-small
                                class=""
                                @click="item.inspection_delete_file = item.inspection_delete_file * -1"
                                :class="[item.inspection_delete_file == 1 ? 'upload-btn-label' : '']"
                                >Delete File
                            </v-btn>
                            <input
                                type="hidden"
                                v-model="item.inspection_delete_file"
                                :id="'inspection_delete_file'"
                                :edit_form="edit_form"
                                :error_msg="error_msg"
                            />

                            <v-btn
                                x-small
                                class=""
                                @click="incompleteInspection(item)"
                                >Incomplete</v-btn
                            >
                            <input
                                type="hidden"
                                v-model="item.inspection_incomplete"
                                :id="'inspection_incomplete'"
                                :edit_form="edit_form"
                                :error_msg="error_msg"
                            />
                        </div>
                    </template>
                </v-data-table>
                <v-row
                    class="form-row"
                    v-show="lease_inspection_complete_list.length > 5"
                >
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                    >
                        <table class="c8-datatable-custom-footer">
                            <tr>
                                <td class="">Rows per page:</td>
                                <td>
                                    <multiselect
                                        v-model="completed_items_per_page"
                                        :options="[5, 10, 15]"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        ></multiselect
                                    >
                                </td>
                                <td></td>
                                <td>
                                    <v-pagination
                                        v-model="completed_page"
                                        :length="completed_page_count"
                                    ></v-pagination>
                                </td>
                            </tr>
                        </table>
                    </v-col>
                </v-row>

                <!--datatable end-->

                <br
                    v-if="
                        (lease_inspection_incomplete_list.length > 0 || lease_inspection_complete_list.length > 0) &&
                        edit_form
                    "
                />
                <v-divider
                    v-if="
                        (lease_inspection_incomplete_list.length > 0 || lease_inspection_complete_list.length > 0) &&
                        edit_form
                    "
                ></v-divider>
                <v-card
                    elevation="0"
                    v-if="
                        (lease_inspection_incomplete_list.length > 0 || lease_inspection_complete_list.length > 0) &&
                        edit_form
                    "
                >
                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn
                            class="v-step-save-2-button"
                            @click="updateInspection()"
                            color="success"
                            dark
                            small
                            depressed
                        >
                            Save Inspection details
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </div>
        </div>
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="show_one_off_sundry_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Add One-Off Sundry Charge
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_one_off_sundry_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="one_off_sundry_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Description</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        v-model="one_off_sundry_arr.description"
                                        size=""
                                        :id="'description'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Amount</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :inputFormat="'dollar'"
                                        v-model="one_off_sundry_arr.amount"
                                        size=""
                                        :id="'inspection_comment'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Account</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        v-model="one_off_sundry_arr.account"
                                        :options="account_list"
                                        openDirection="bottom"
                                        group-values="field_group_values"
                                        :groupSelect="false"
                                        group-label="field_group_names"
                                        :group-select="true"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        :custom-label="nameWithDash"
                                        placeholder="Select an account"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>

                            <v-row
                                class="form-row"
                                v-if="isMultiplePropertyLedger"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Property Ledger</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-single-select-v2
                                        v-model="one_off_sundry_arr.propertyLedgerId"
                                        :options="property_ledger_list"
                                        ref="refPropertyType"
                                        trackBy="field_key"
                                        label="field_value"
                                        return="field_key"
                                        placeholder="Please select"
                                        :custom-label="nameWithCodeDash"
                                        v-if="edit_form"
                                    />
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitOneOff()"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        Yes
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="show_one_off_sundry_modal = false"
                        color="normal"
                        depressed
                        small
                    >
                        No
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.shift.enter="modalAddData()"
            @keydown.ctrl.enter="modalSubmitData(1)"
        >
            <v-card>
                <v-card-title class="headline">
                    Inspection Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="lease_inspection_arr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >#</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{
                                        lease_inspection_arr.index === 'New'
                                            ? lease_inspection_arr.index
                                            : lease_inspection_arr.index + 1
                                    }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Scheduled Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :id="'lease_inspection_sched_date' + String(Math.random()).replace('.', '')"
                                        v-model="lease_inspection_arr.inspection_sched_date"
                                        :size="'40'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-icon-date-picker>

                                    <span style="position: absolute; margin-top: 4px">
                                        <v-checkbox
                                            v-model="lease_inspection_arr.inspection_diary"
                                            label="Diarise"
                                            ripple="false"
                                            dense
                                        ></v-checkbox>
                                    </span>
                                    <input
                                        type="hidden"
                                        v-model="lease_inspection_arr.inspection_diary"
                                    />
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Frequency</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        data-inverted=""
                                        openDirection="bottom"
                                        v-model="lease_inspection_arr.inspection_freq"
                                        :options="lease_inspection_freq"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        group-label="language"
                                        placeholder="Select a frequency"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        data-inverted=""
                                        openDirection="bottom"
                                        v-model="lease_inspection_arr.inspection_type"
                                        :options="lease_inspection_type"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        group-label="language"
                                        placeholder="Select a inspection type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Template</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        data-inverted=""
                                        openDirection="bottom"
                                        v-model="lease_inspection_arr.inspection_template"
                                        :options="lease_inspection_template"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        group-label="language"
                                        placeholder="Select a template"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Charge Amount</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :inputFormat="'dollar'"
                                        v-model="lease_inspection_arr.inspection_amount"
                                        size=""
                                        :id="'inspection_comment'"
                                        data-inverted=""
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>

                            <!--              <v-row class="form-row">-->
                            <!--                <v-col xs="12" sm="2" md="2" class="form-label">Diarise</v-col>-->
                            <!--                <v-col xs="12" sm="10" md="10" class="form-input">-->
                            <!--                  <div>-->
                            <!--                    <sui-checkbox v-model="lease_inspection_arr.inspection_diary" data-inverted=""/>-->
                            <!--                  </div>-->
                            <!--                  <input type="hidden" v-model="lease_inspection_arr.inspection_diary"-->
                            <!--                         :error_msg="error_msg">-->

                            <!--                </v-col>-->
                            <!--              </v-row>-->
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />

                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData(1)"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import { fetchPropertyLedger } from '../../../../modules/Property/lib/ledger';
import isNil from 'lodash/isNil';

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_INSPECTION',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            edit_form: false,
            success_flag: false,
            lease_inspection_arr: [],
            lease_inspection_complete_list: [],
            lease_inspection_incomplete_list: [],
            lease_inspection_complete_list_old: [],
            lease_inspection_incomplete_list_old: [],
            lease_inspection_type: [],
            lease_inspection_template: [],
            lease_inspection_freq: [],
            readonly: this.read_only,
            show_activity_log_modal: false,
            upcoming_headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'inspection_id', width: '5%' },
                { text: 'Schedule Date', value: 'inspection_date_raw', width: '10%' },
                { text: 'Type', value: 'inspection_type_value', width: '15%' },
                { text: 'Template', value: 'inspection_template_value', width: '25%' },
                { text: 'Charge Amount', value: 'inspection_charge', width: '10%', align: 'end' },
                { text: 'Comment', value: 'inspection_comment', width: '20%' },
                { text: 'Diarise', value: 'inspection_diary', width: '10%' },
                { text: '', value: 'action_complete', align: 'end' },
                { text: '', value: 'action1', align: 'end', width: '78px' },
            ],
            completed_headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'inspection_id', width: '5%' },
                { text: 'Schedule Date', value: 'inspection_sched_date_raw', width: '10%' },
                { text: 'Completed Date', value: 'inspection_complete_date_raw', width: '10%' },
                { text: 'Completed By', value: 'inspection_complete_by', width: '15%' },
                { text: 'Inspection Type', value: 'inspection_type_value', width: '15%' },
                { text: 'Attach Document', value: 'inspection_file' },
                { text: '', value: 'action1', align: 'end', width: '' },
            ],
            upcoming_page: 1,
            upcoming_page_count: 0,
            upcoming_items_per_page: 5,
            completed_page: 1,
            completed_page_count: 0,
            completed_items_per_page: 5,
            search_datatable: '',
            AED_modal: false,
            modal_current_ctr: 0,
            show_one_off_sundry_modal: false,
            property_inspection_flag: false,
            property_inspection_frequency: [],
            property_inspection_amount: '0.00',
            one_off_sundry_arr: {
                description: '',
                amount: '0.00',
                account: [],
                inspection_id: 0,
                index: 0,
                propertyLedgerId: null,
            },
            account_list: [],
            account_list_ungrouped: [],
            currency_symbol: '$',
            property_ledger_list: [],
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
        var form_data = new FormData();
        form_data.append('property_code', this.property_code);
        form_data.append('version_id', this.version_id);
        form_data.append('no_load', true);
        this.$api.post('ui/fetch/account-exp-lists', form_data).then((response) => {
            this.account_list = response.data.grouped;
            this.account_list_ungrouped = response.data.ungrouped;
        });
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'auto_diarise',
            'sys_ver_control_list',
        ]),
    },
    methods: {
        doubleClickForm: function () {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadLedgerList();
                this.loadLeaseInspection();
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.edit_form = false;
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.lease_inspection_arr = [];
            this.lease_inspection_complete_list = JSON.parse(JSON.stringify(this.lease_inspection_complete_list_old));
            this.lease_inspection_incomplete_list = JSON.parse(
                JSON.stringify(this.lease_inspection_incomplete_list_old),
            );
        },
        modalAddData: function () {
            this.AED_modal = true;
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            var d = new Date();
            this.lease_inspection_arr = {
                index: 'New',
                inspection_id: '',
                inspection_sched_date: d.getDate() + '/' + ('0' + (d.getMonth() + 1)).slice(-2) + '/' + d.getFullYear(),
                inspection_freq: this.property_inspection_frequency,
                inspection_type: {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                inspection_template: {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                inspection_amount: this.property_inspection_amount,
                status: 'new',
                inspection_diary: this.auto_diarise,
            };
        },
        loadLeaseInspection: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';

            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/inspection';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/inspection';
            }

            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_inspection_incomplete_list = response.data.lease_inspection_incomplete_list;
                this.lease_inspection_complete_list = response.data.lease_inspection_complete_list;

                this.lease_inspection_incomplete_list_old = response.data.lease_inspection_incomplete_list_old;
                this.lease_inspection_complete_list_old = response.data.lease_inspection_complete_list_old;

                this.lease_inspection_type = response.data.lease_inspection_type;
                this.lease_inspection_template = response.data.lease_inspection_template;
                this.lease_inspection_freq = response.data.lease_inspection_freq;
                this.property_inspection_flag = response.data.property_inspection_flag;
                this.property_inspection_amount = response.data.property_inspection_amount;
                this.property_inspection_frequency = response.data.property_inspection_frequency;
                this.property_inspection_account = response.data.property_inspection_account;

                this.currency_symbol = response.data.currency_symbol;
                this.loading_setting = false;
            });
        },
        async modalSubmitData(submit_type) {
            let errorArr = [];
            if (Object.keys(this.lease_inspection_arr).length > 0) {
                let inspection_sched_date = this.lease_inspection_arr.inspection_sched_date;
                let inspection_amount = this.lease_inspection_arr.inspection_amount;
                if (inspection_sched_date === '' || inspection_sched_date === null) {
                    errorArr.push(['You have not selected a valid to schedule date.']);
                }

                if (inspection_amount === '' || !inspection_amount) {
                } else {
                    if (isNaN(parseFloat(inspection_amount))) {
                        errorArr.push(['You have not entered a valid amount.']);
                    }
                }
            }

            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                if (this.lease_inspection_arr.index === 'New' && submit_type === 1) {
                    let dialog_prop = {
                        title: 'Warning',
                        message: 'What do you want to do?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Single add', value: 1, color: 'primary' },
                            { label: 'Until expired', value: 2, color: 'primary' },
                            { label: 'Cancel', value: 3 },
                        ],
                    };
                    const result = await cirrusDialog(dialog_prop);
                    if (result === 1 || result === 2) {
                        if (result === 1) {
                            this.saveLease(1);
                        } else {
                            let dialog_prop = {
                                title: 'Warning',
                                message: 'Maximum of 100 inspections will be inserted. Do you want to continue?',
                                icon_show: true,
                                buttons_right: [
                                    { label: 'Yes', value: 1, color: 'primary' },
                                    { label: 'No', value: 2 },
                                ],
                            };
                            const result = await cirrusDialog(dialog_prop);
                            if (result === 1) {
                                this.saveLease(0);
                            }
                        }
                    }
                } else {
                    this.saveLease(0);
                }
            }
        },
        saveLease(single) {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            if (single === 1) {
                form_data.append('single_add', 1);
            }
            let save_arr = [];
            if (Object.keys(this.lease_inspection_arr).length > 0) {
                save_arr[0] = this.lease_inspection_arr;
            }

            form_data.append('lease_inspection_arr', JSON.stringify(save_arr));
            form_data.append('lease_inspection_incomplete_list', JSON.stringify(this.lease_inspection_incomplete_list));
            form_data.append('lease_inspection_complete_list', JSON.stringify(this.lease_inspection_complete_list));

            for (let x = 0; x < this.lease_inspection_complete_list.length; x++) {
                form_data.append(
                    'lease_inspection_file_' + x,
                    this.lease_inspection_complete_list[x].inspection_file[0],
                );
            }

            let apiUrl = '';
            if (this.isLeaseFormLive())
                apiUrl = this.cirrus8_api_url + 'api/with-file-upload/lease/update-or-create/inspection';
            else apiUrl = this.cirrus8_api_url + 'api/with-file-upload/temp/lease/update-or-create/inspection';

            this.$api
                .post(apiUrl, form_data, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                })
                .then((response) => {
                    if (response.data.diary == 1) {
                        bus.$emit('loadLeaseDiarySection', '');
                    }
                    // this.modalAddData();
                    this.lease_inspection_incomplete_list = [];
                    this.lease_inspection_complete_list = [];
                    this.loadForm();
                    this.edit_form = this.edit_flag;
                    // this.edit_form = false;
                    this.loading_setting = false;
                    // this.$noty.success("Successfully Saved");
                    if (!response.data.error) this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
        },
        updateInspection: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            form_data.append('lease_inspection_incomplete_list', JSON.stringify(this.lease_inspection_incomplete_list));
            form_data.append('lease_inspection_complete_list', JSON.stringify(this.lease_inspection_complete_list));

            for (let x = 0; x < this.lease_inspection_complete_list.length; x++) {
                form_data.append(
                    'lease_inspection_file_' + x,
                    this.lease_inspection_complete_list[x].inspection_file[0],
                );
            }

            let apiUrl = '';
            if (this.isLeaseFormLive()) apiUrl = this.cirrus8_api_url + 'api/with-file-upload/lease/update/inspection';
            else apiUrl = this.cirrus8_api_url + 'api/with-file-upload/temp/lease/update/inspection';

            this.$api
                .post(apiUrl, form_data, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                })
                .then((response) => {
                    if (response.data.diary == 1) {
                        bus.$emit('loadLeaseDiarySection', '');
                    }
                    // this.modalAddData();
                    this.lease_inspection_incomplete_list = [];
                    this.lease_inspection_complete_list = [];
                    this.loadForm();
                    // this.edit_form = false;
                    this.loading_setting = false;
                    // this.$noty.success("Successfully Saved");
                    if (!response.data.error) this.success_flag = true;
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
        },
        deleteNewInspection: function (index) {
            this.lease_inspection_arr.splice(index, 1);
        },
        async deleteInspection(index) {
            let inspection_id = this.lease_inspection_incomplete_list[index].inspection_id;
            let inspection_type = this.lease_inspection_incomplete_list[index].inspection_type;
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Single delete', value: 1, color: 'primary' },
                    { label: 'Type delete', value: 2, color: 'primary' },
                    { label: 'Cancel', value: 3 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1 || result === 2) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('inspection_id', inspection_id);
                form_data.append('inspection_type', inspection_type);
                form_data.append('no_load', true);
                if (result === 1) {
                    form_data.append('single_delete', 1);
                }
                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/delete/inspection';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/inspection';
                }

                this.$api.post(apiUrl, form_data).then((response) => {
                    if (result === 1) {
                        this.lease_inspection_incomplete_list.splice(index, 1);
                        this.loading_setting = false;
                        this.lease_inspection_incomplete_list_old = JSON.parse(
                            JSON.stringify(this.lease_inspection_incomplete_list),
                        );
                    } else {
                        // this.modalAddData();
                        this.AED_modal = false;
                        this.lease_inspection_incomplete_list = [];
                        this.lease_inspection_complete_list = [];
                        this.loadForm();
                        // this.edit_form = false;
                        this.loading_setting = false;
                    }
                });
            }
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadInspection_' + id;
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        completeInspection: function (item) {
            let inspection_id = item.inspection_id;
            let inspection_charge = item.inspection_charge;
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('inspection_id', inspection_id);
            form_data.append('no_load', true);
            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/update/inspection-complete';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/inspection-complete';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                // this.modalAddData();
                this.lease_inspection_incomplete_list = [];
                this.lease_inspection_complete_list = [];
                this.loadForm();
                // this.edit_form = false;
                this.loading_setting = false;
                // this.$noty.success("Successfully Saved");
                if (!response.data.error) this.success_flag = true;
                setTimeout(
                    function () {
                        this.success_flag = false;
                    }.bind(this),
                    2000,
                );
                if (this.isLeaseFormLive()) {
                    if (inspection_charge > 0) {
                        this.show_one_off_sundry_modal = true;
                        this.one_off_sundry_arr.index = inspection_id;
                        this.one_off_sundry_arr.inspection_id = inspection_id;
                        this.one_off_sundry_arr.description = 'Lease Inspection';
                        this.one_off_sundry_arr.amount = inspection_charge;
                        this.one_off_sundry_arr.account = this.getValueInList(
                            this.property_inspection_account,
                            this.account_list_ungrouped,
                        );
                    }
                }
            });
        },
        incompleteInspection: function (item) {
            let inspection_id = item.inspection_id;
            let inspection_charge = item.inspection_charge;
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('inspection_id', inspection_id);
            form_data.append('no_load', true);
            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/update/inspection-incomplete';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/inspection-incomplete';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                // this.modalAddData();
                this.lease_inspection_incomplete_list = [];
                this.lease_inspection_complete_list = [];
                this.loadForm();
                // this.edit_form = false;
                this.loading_setting = false;
                // this.$noty.success("Successfully Saved");
                if (!response.data.error) this.success_flag = true;
                setTimeout(
                    function () {
                        this.success_flag = false;
                    }.bind(this),
                    2000,
                );
            });
        },
        modalSubmitOneOff: function () {
            let errorArr = [];
            let inspection_id = this.one_off_sundry_arr.inspection_id;
            let description = this.one_off_sundry_arr.description;
            let amount = this.one_off_sundry_arr.amount;
            let account_code = this.one_off_sundry_arr.account.field_key;
            let propertyLedgerId = this.one_off_sundry_arr.propertyLedgerId;
            if (description === '') errorArr.push(['You have not entered a valid description.']);
            if (account_code === '') errorArr.push(['You have not selected a valid account.']);
            if (this.isMultiplePropertyLedger && isNil(propertyLedgerId))
                errorArr.push(['You have not selected a valid ledger.']);
            if (amount === '' || !amount) {
                errorArr.push(['You have not entered a valid amount.']);
            } else {
                if (isNaN(parseFloat(amount))) {
                    errorArr.push(['You have not entered a valid amount.']);
                }
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('description', description);
                form_data.append('account_code', account_code);
                form_data.append('amount', amount);
                form_data.append('inspection_id', inspection_id);
                form_data.append('propertyLedgerId', propertyLedgerId);
                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/create/one-off-sundry';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/create/one-off-sundry';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    if (!response.data.error) this.success_flag = true;
                    this.show_one_off_sundry_modal = false;
                    // bus.$emit("loadLeaseDiarySection", "");
                    setTimeout(
                        function () {
                            this.success_flag = false;
                        }.bind(this),
                        2000,
                    );
                });
            }
        },
        async loadLedgerList() {
            this.property_ledger_list = [];
            try {
                const ledgerList = await fetchPropertyLedger(this.property_code);
                // Normalize possible response shapes, then map to [{ field_key, field_value }]
                const list = Array.isArray(ledgerList)
                    ? ledgerList
                    : Array.isArray(ledgerList?.data)
                      ? ledgerList.data
                      : Array.isArray(ledgerList?.list)
                        ? ledgerList.list
                        : [];
                this.property_ledger_list = list.map(({ id, propertyLedgerCode, description }) => ({
                    field_key: id,
                    code: propertyLedgerCode,
                    field_value: description,
                }));
            } catch (err) {}
        },
        isMultiplePropertyLedger() {
            return !!this.sys_ver_control_list?.isMultiplePropertyLedger;
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadLeaseInspectionSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
