<style>
.v-dialog .lease-activity-log input {
    padding-left: 0px !important;
    padding-right: 0px !important;
}
</style>
<template>
    <div class="lease-activity-log">
        <v-skeleton-loader
            class="mx-auto"
            type="table"
            :loading="loading"
        >
            <v-btn
                @click="exportToXLSX()"
                v-if="lease_activity_log_list.length > 0"
                depressed
                small
                style="position: absolute; z-index: 99; margin-left: 10px"
            >
                Export to Excel
            </v-btn>
            <v-card elevation="0">
                <v-card-title>
                    <v-spacer></v-spacer>
                    <v-text-field
                        v-model="search"
                        append-icon="search"
                        label="Search"
                        single-line
                        hide-details
                    ></v-text-field>
                </v-card-title>
                <v-data-table
                    dense
                    item-key="id"
                    :headers="headers"
                    :items="lease_activity_log_list"
                    :search="search"
                >
                    <template v-slot:item.index="{ item }">
                        {{ lease_activity_log_list.indexOf(item) + 1 }}
                    </template>
                    <template v-slot:item.old_value="{ item }">
                        <span
                            data-inverted=""
                            :data-tooltip="item.old_value"
                            >{{ truncate(item.old_value, 20) }}</span
                        >
                    </template>
                    <template v-slot:item.new_value="{ item }">
                        <span
                            data-inverted=""
                            :data-tooltip="item.new_value"
                            >{{ truncate(item.new_value, 20) }}</span
                        >
                    </template>
                </v-data-table>
            </v-card>
        </v-skeleton-loader>
        <v-dialog
            v-model="overview_loading"
            hide-overlay
            persistent
            width="300"
        >
            <v-card
                color="primary"
                dark
            >
                <v-card-text>
                    {{ overview_loading_msg }}
                    <v-progress-linear
                        indeterminate
                        color="white"
                        class="mb-0"
                    ></v-progress-linear>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
import moment from 'moment';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        form_section: { type: String, default: '' },
    },
    data() {
        return {
            loading: true,
            search: '',
            headers: [
                {
                    text: '#',
                    value: 'index',
                },
                { text: 'Field Name', value: 'field_name' },
                { text: 'Old Value', value: 'old_value' },
                { text: 'New Value', value: 'new_value' },
                { text: 'User', value: 'username' },
                { text: 'Date', value: 'date_created' },
                { text: 'Status', value: 'status' },
            ],
            lease_activity_log_list: [],
            overview_loading: false,
            overview_loading_msg: 'Please wait...',
        };
    },
    mounted() {
        this.loadActivityLogs();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    methods: {
        loadActivityLogs: function () {
            if (this.property_code !== '' && this.lease_code !== '' && this.form_section !== '') {
                this.loading = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('form_section', this.form_section);
                form_data.append('no_load', true);
                this.$api.post(this.cirrus8_api_url + 'api/lease/fetch/activity-log', form_data).then((response) => {
                    this.lease_activity_log_list = response.data.lease_activity_log_list;
                    this.loading = false;
                });
            }
        },
        exportToXLSX: function () {
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('form_section', this.form_section);
            form_data.append('no_load', true);
            this.overview_loading = true;
            this.overview_loading_msg = 'Generating excel file';
            this.$api.post('lease/process/export-activity-log', form_data).then((response) => {
                let res = response.data;
                this.overview_loading = false;
                this.overview_loading_msg = 'Please wait...';
                if (res.file && res.file.name && res.file.type && res.file.data) {
                    this.printDownload(res.file.data, res.file.name + moment().unix(), res.file.type);
                } else {
                    if (res.error) this.$noty.error(res.error);
                    else this.$noty.error('Generate report failed. Please try again.');
                }
            });
        },
        truncate: function (str, n) {
            return str.length > n ? str.substr(0, n - 1) + '...' : str;
        },
    },
    created() {
        bus.$on('loadLeaseActivityLog', (data) => {
            this.loadActivityLogs();
        });
    },
    mixins: [global_mixins],
};
</script>
