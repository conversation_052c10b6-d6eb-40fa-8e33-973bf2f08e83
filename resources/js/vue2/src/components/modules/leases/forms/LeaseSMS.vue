<template>
    <div style="max-width: 100%">
        <div
            class="page-form"
            v-if="formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-subheader"
                    ><strong>SMS</strong></v-col
                >
            </v-row>
        </div>
        <cirrus-content-loader v-if="loading_setting"></cirrus-content-loader>
        <v-data-table
            class="c8-datatable-custom"
            v-show="lease_sms_list.length > 0 && !loading_setting"
            dense
            item-key="id"
            :headers="headers"
            :items="lease_sms_list"
            :items-per-page="items_per_page"
            hide-default-footer
            :page.sync="page"
            @page-count="page_count = $event"
        >
            <template v-slot:item.index="{ item }">
                {{ lease_sms_list.indexOf(item) + 1 }}
            </template>
            <template v-slot:item.date_sent_raw="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.date_sent }}</span>
                </div>
            </template>
            <template v-slot:item.recepient_no="{ item }">
                <span v-html="item.recepient_no"></span>
            </template>
            <template v-slot:item.action1="{ item }">
                <v-icon
                    color="red"
                    v-show="!readonly"
                    v-if="edit_form"
                    @click="deleteHistory(item.sms_id)"
                    >close
                </v-icon>
            </template>
        </v-data-table>
        <v-row
            class="form-row"
            v-show="lease_sms_list.length > 5"
            v-if="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-col
                xs="12"
                sm="12"
                md="12"
            >
                <table class="c8-datatable-custom-footer">
                    <tr>
                        <td class="">Rows per page:</td>
                        <td>
                            <multiselect
                                v-model="items_per_page"
                                :options="[5, 10, 15]"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-200"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </td>
                        <td></td>
                        <td>
                            <v-pagination
                                v-model="page"
                                :length="page_count"
                            ></v-pagination>
                        </td>
                    </tr>
                </table>
            </v-col>
        </v-row>

        <v-dialog
            v-model="show_sms_info_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    SMS Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_sms_info_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Date Sent</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-icon-date-picker
                                    :id="'sms_' + String(Math.random()).replace('.', '')"
                                    v-model="information_arr.date_sent"
                                    :size="'40'"
                                    data-inverted=""
                                    :edit_form="true"
                                ></cirrus-icon-date-picker>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Recipients</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="information_arr.recipients"
                                    size=""
                                    :id="'sms_recipients'"
                                    data-inverted=""
                                    :edit_form="true"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >SMS Message</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-textarea
                                    v-model="information_arr.sms_message"
                                    auto-grow
                                    rows="7"
                                    full-width
                                    maxlength="300"
                                    class="noteTextArea"
                                ></v-textarea>
                                <br />
                                <div class="text-right">
                                    <span>{{ information_arr.sms_message.length }} / 300</span>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        depressed
                        small
                        @click="show_sms_info_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        edit_form: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_COM_HISTORY',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            loading_setting: true,
            readonly: this.read_only,
            lease_sms_list: [],
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', sortable: false, width: '50px' },
                { text: 'Property', value: 'property_code', width: '15%' },
                { text: 'Lease', value: 'lease_code', width: '15%' },
                { text: 'Date Sent', value: 'date_sent_raw', width: '15%' },
                { text: 'SMS Message', value: 'sms_message', width: '35%' },
                { text: 'Recipients', value: 'recepient_no', width: '15%' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 5,
            search_datatable: '',
            sms_message: '',
            show_sms_info_modal: false,
            information_arr: {
                date_sent: null,
                recipients: '',
                sms_message: '',
            },
            limit_by: 50,
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
        ]),
    },
    methods: {
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadLeaseSMS();
            }
        },
        loadLeaseSMS: function () {
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('limit_by', this.limit_by);
            form_data.append('no_load', true);
            let apiUrl = '';

            apiUrl = this.cirrus8_api_url + 'api/lease/fetch/sms';

            this.$api.post(apiUrl, form_data).then((response) => {
                this.lease_sms_list = response.data.leaseSMSList;
                if (
                    this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.items_per_page = this.lease_sms_list.length;
                }
                this.loading_setting = false;
            });
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadSMS_' + id;
        },
        modalSubmitData: function () {
            this.error_server_msg2 = [];
            this.loading_setting = true;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('sms_id', this.information_arr.sms_id);
            form_data.append('date_sent', this.information_arr.date_sent);
            form_data.append('sms_message', this.information_arr.sms_message);
            form_data.append('recipients', this.information_arr.recipients);
            let apiUrl = 'system/update-or-create/sms';
            this.$api.post(apiUrl, form_data).then((response) => {
                let error_server_msg2 = response.data.error_server_msg2;
                if (error_server_msg2.length === 0) {
                    this.loadForm();
                    this.show_sms_info_modal = false;
                } else this.error_server_msg2 = error_server_msg2;
                this.loading_setting = false;
            });
        },
        modalOpenAED: function (item) {
            this.show_sms_info_modal = true;
            this.information_arr = {
                sms_id: item.sms_id,
                recipients: item.recepient_no_raw,
                sms_message: item.sms_message,
                date_sent: item.date_sent,
            };
        },
        async deleteHistory(id) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.loading_setting = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);
                form_data.append('id', id);
                let apiUrl = this.cirrus8_api_url + 'api/system/delete/sms-history';
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.loading_setting = false;
                    this.loadForm();
                });
            }
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        information_arr: {
            handler: function (val, oldVal) {
                this.information_arr.sms_message = this.information_arr.sms_message.replace(/\n/g, '');
            },
            deep: true,
        },
    },
    created() {
        bus.$on('loadLeaseSMSSection', (data) => {
            this.limit_by = data.limit_by;
            if (data.property_code === this.property_code && data.lease_code === this.lease_code) {
                this.loadForm();
            }
        });
        bus.$on('openSMSAEModal', (data) => {
            this.show_sms_info_modal = true;
            var d = new Date();
            this.information_arr = {
                sms_id: data.history_id,
                recipients: data.recipients,
                sms_message: data.sms_message,
                date_sent: data.date_sent,
            };
        });
    },
    mixins: [global_mixins],
};
</script>
