<style>
.lease-contacts tbody td {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.lease-contacts thead th,
.lease-contacts tbody td {
    padding-left: 16.5px !important;
    padding-right: 16.5px !important;
}

.v-btn--fab.v-size--x-small .v-icon,
.v-btn--icon.v-size--x-small .v-icon {
    font-size: 21px;
}
</style>

<template>
    <div v-on:dblclick="doubleClickForm()">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Contact Details</h6>
                <v-spacer></v-spacer>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    icon
                    @click="modalAddData()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>

                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !new_lease &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive) &&
                        !pmro_read_only
                    "
                    v-if="edit_form && contact_list.length > 0"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="
                        !read_only &&
                        !formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)
                    "
                    icon
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="!read_only && !new_lease && isLeaseFormLive()"
                    @click="showLeaseActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader
            type="table-tbody"
            v-if="loading_setting"
        ></cirrus-content-loader>
        <v-col
            class="text-center"
            v-if="!loading_setting && contact_list.length === 0"
            v-show="!formSectionReadOnly(pm_lease_form_read_only, form_type, form_section, is_inactive)"
        >
            <v-btn
                depressed
                small
                color="success"
                @click="modalAddData()"
                v-if="!pmro_read_only"
                >Add Contact</v-btn
            >
            <div
                style="margin-top: 15px"
                v-else
            >
                No lease contacts at the moment
            </div>
        </v-col>

        <sui-table
            v-if="!loading_setting && contact_list.length > 0"
            class="vue-data-grid lease-contacts"
            stackable
            width="100%"
            cellpadding="0"
            cellspacing="0"
            border="0"
        >
            <sui-table-header>
                <sui-table-row class="fieldDescription">
                    <sui-table-header-cell style="width: 5px">#</sui-table-header-cell>
                    <sui-table-header-cell style="width: 5px; padding: 3px 20px !important">ID</sui-table-header-cell>
                    <sui-table-header-cell style="padding: 3px 15px !important">Name</sui-table-header-cell>
                    <sui-table-header-cell>Type</sui-table-header-cell>
                    <sui-table-header-cell>Details</sui-table-header-cell>
                    <sui-table-header-cell></sui-table-header-cell>
                </sui-table-row>
            </sui-table-header>
            <sui-table-body class="page-form">
                <sui-table-row
                    :class="contact_list_data.contact_primary === '1' ? 'form-row primary-contact' : 'form-row'"
                    v-for="(contact_list_data, index) in contact_list"
                    :key="index"
                    :style="contact_list_data.contact_primary === '1' ? 'background: #f7ffe6 ;' : ''"
                >
                    <sui-table-cell
                        v-if="!edit_form"
                        verticalAlign="top"
                        style="width: 5px"
                    >
                        <span class="form-input-text">{{ index + 1 }}</span>
                    </sui-table-cell>
                    <sui-table-cell
                        v-if="edit_form"
                        verticalAlign="top"
                        style="width: 5px; padding-top: 0.95em !important"
                    >
                        <span class="form-input-text">{{ index + 1 }}</span>
                    </sui-table-cell>
                    <sui-table-cell
                        v-if="!edit_form"
                        verticalAlign="top"
                        style="width: 5px; padding: 3px 20px !important"
                    >
                        <span class="form-input-text">{{ contact_list_data.item_no }}</span>
                    </sui-table-cell>
                    <sui-table-cell
                        v-if="edit_form"
                        verticalAlign="top"
                        style="width: 5px; padding: 0.95em 20px 3px !important"
                    >
                        <span class="form-input-text">{{ contact_list_data.item_no }}</span>
                    </sui-table-cell>
                    <sui-table-cell
                        class="form-input--no-pad-top"
                        verticalAlign="top"
                        style="width: 338px; padding: 4px 15px !important"
                    >
                        <cirrus-input
                            :size="'40'"
                            :maxlength="'40'"
                            :id="'contact_name'"
                            v-model="contact_list_data.contact_name"
                            :edit_form="false"
                            :error_msg="error_msg"
                        ></cirrus-input>
                    </sui-table-cell>
                    <sui-table-cell
                        class="form-input--no-pad-top"
                        verticalAlign="top"
                        style="width: 220px"
                    >
                        <span class="form-input-text">{{ contact_list_data.contact_type.field_value }}</span>
                    </sui-table-cell>
                    <sui-table-cell
                        class="form-input--no-pad-top"
                        verticalAlign="top"
                        style="padding: 1.5px 12.5px !important"
                    >
                        <table class="table-raw data-grid-no-line lease-contact-details">
                            <tr
                                v-for="(contact_details_data, index_detail) in contact_list_data.contact_details"
                                :key="index_detail"
                            >
                                <td style="padding: 0px 2px !important">
                                    <span class="form-input-text">{{ index_detail + 1 + '.' }} &nbsp;</span>
                                </td>
                                <td style="padding: 0px 2px !important">
                                    <span class="form-input-text"
                                        ><strong>{{ contact_details_data.phone_type.field_value }}: </strong></span
                                    >

                                    <cirrus-input
                                        v-if="contact_details_data.phone_type.field_value === 'E-Mail'"
                                        :inputFormat="'emailClickable'"
                                        :size="'30'"
                                        :id="'contact_name'"
                                        v-model="contact_details_data.contact_detail"
                                        :edit_form="false"
                                        :error_msg="error_msg"
                                    ></cirrus-input>

                                    <cirrus-input
                                        v-if="
                                            contact_details_data.phone_type.field_value === 'Mobile Phone' ||
                                            contact_details_data.phone_type.field_value === 'Owner Mobile' ||
                                            contact_details_data.phone_type.field_value === 'Service Mobile' ||
                                            contact_details_data.phone_type.field_value === 'Tenant Mobile'
                                        "
                                        :inputFormat="'phoneClickable'"
                                        :size="'30'"
                                        :id="'contact_name'"
                                        v-model="contact_details_data.contact_detail"
                                        :edit_form="false"
                                        :error_msg="error_msg"
                                    ></cirrus-input>

                                    <cirrus-input
                                        v-if="
                                            contact_details_data.phone_type.field_value !== 'Mobile Phone' &&
                                            contact_details_data.phone_type.field_value !== 'Owner Mobile' &&
                                            contact_details_data.phone_type.field_value !== 'Service Mobile' &&
                                            contact_details_data.phone_type.field_value !== 'Tenant Mobile' &&
                                            contact_details_data.phone_type.field_value !== 'E-Mail'
                                        "
                                        :size="'30'"
                                        :id="'contact_name'"
                                        v-model="contact_details_data.contact_detail"
                                        :edit_form="false"
                                        :error_msg="error_msg"
                                    ></cirrus-input>

                                    <v-btn
                                        text
                                        icon
                                        color="green"
                                        x-small
                                        @click="show_sms_sending_modal(contact_details_data)"
                                        style="position: absolute; margin-top: -3px"
                                        v-if="
                                            sms_sending_setup &&
                                            contact_details_data.phone_type.field_key === 'MOBILE' &&
                                            isLeaseFormLive() &&
                                            is_inactive != 1
                                        "
                                    >
                                        <v-icon x-small>message</v-icon>
                                    </v-btn>
                                </td>
                            </tr>
                            <tr v-if="false">
                                <td colspan="2">
                                    <v-btn
                                        text
                                        icon
                                        color="green"
                                        x-small
                                        @click="addContactDetails(index)"
                                        style="margin-top: 0.3em"
                                    >
                                        <v-icon>add</v-icon>
                                    </v-btn>
                                </td>
                            </tr>
                        </table>
                    </sui-table-cell>
                    <sui-table-cell
                        verticalAlign="top"
                        style="text-align: right"
                    >
                        <div
                            v-if="edit_form"
                            style="padding-top: 0.6em"
                        >
                            <v-btn
                                x-small
                                v-if="contact_list_data.contact_primary === '1'"
                                disabled
                                >Primary</v-btn
                            >
                            <v-btn
                                x-small
                                v-if="contact_list_data.contact_primary === '0' && contact_list_data.status !== 'new'"
                                @click="makePrimaryContact(index)"
                                >Make Primary
                            </v-btn>
                            <v-icon
                                small
                                @click="modalOpenAED(index)"
                                v-if="edit_form"
                                >fas fa-edit</v-icon
                            >
                            <v-btn
                                x-small
                                text
                                icon
                                color="red"
                                @click="deleteContact(index)"
                            >
                                <v-icon>close</v-icon>
                            </v-btn>
                        </div>
                    </sui-table-cell>
                </sui-table-row>
            </sui-table-body>
        </sui-table>
        <br />
        <v-divider></v-divider>
        <v-dialog
            v-model="show_activity_log_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_activity_log_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="show_activity_log_modal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :form_section="form_section"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="show_activity_log_modal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!--   AED modal      -->
        <v-dialog
            v-model="AED_modal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitData()"
        >
            <v-card>
                <v-card-title class="headline">
                    Contact Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AED_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="error_server_msg"
                        :errorMsg2="error_server_msg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="lease_contact_add_arr.item_no">
                        <div class="page-form">
                            <v-row
                                class="form-row"
                                v-if="lease_contact_add_arr.status !== 'new'"
                            >
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >ID</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <span class="form-input-text">{{ lease_contact_add_arr.item_no }}</span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Name</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'40'"
                                        :maxlength="'40'"
                                        :id="'contact_name'"
                                        v-model="lease_contact_add_arr.contact_name"
                                        :edit_form="true"
                                        :error_msg="error_msg"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Type</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        v-model="lease_contact_add_arr.contact_type"
                                        :options="contact_role_list"
                                        openDirection="bottom"
                                        :allowEmpty="false"
                                        class="vue-select2 dropdown-left dropdown-200"
                                        group-label="language"
                                        placeholder="Select a contact type"
                                        track-by="field_key"
                                        label="field_value"
                                        :show-labels="false"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="12"
                                    md="12"
                                >
                                    <v-simple-table
                                        fixed-header
                                        dense
                                        height="200px"
                                        class="c8-datatable-custom"
                                    >
                                        <template v-slot:default>
                                            <thead class="v-data-table-header">
                                                <tr>
                                                    <th
                                                        class="text-left"
                                                        style="width: 40px"
                                                    >
                                                        #
                                                    </th>
                                                    <th class="text-left">Type</th>
                                                    <th class="text-left">Details</th>
                                                    <th class="text-right"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    v-for="(
                                                        contact_details_data, index_detail
                                                    ) in lease_contact_add_arr.contact_details"
                                                    :key="index_detail"
                                                >
                                                    <td class="text-left">
                                                        {{ index_detail + 1 }}
                                                    </td>
                                                    <td class="text-left">
                                                        <multiselect
                                                            v-model="contact_details_data.phone_type"
                                                            openDirection="bottom"
                                                            :options="contact_phone_type_list"
                                                            :allowEmpty="false"
                                                            class="vue-select2 dropdown-left dropdown-300"
                                                            group-label="language"
                                                            placeholder="Select a phone type"
                                                            track-by="field_key"
                                                            label="field_value"
                                                            :show-labels="false"
                                                            ><span slot="noResult"
                                                                >Oops! No elements found. Consider changing the search
                                                                query.</span
                                                            >
                                                        </multiselect>
                                                    </td>
                                                    <td class="text-left">
                                                        <cirrus-input
                                                            v-if="
                                                                contact_details_data.phone_type.field_value === 'E-Mail'
                                                            "
                                                            :inputFormat="'emailClickable'"
                                                            :size="'30'"
                                                            :id="'contact_name'"
                                                            v-model="contact_details_data.contact_detail"
                                                            :edit_form="true"
                                                            :error_msg="error_msg"
                                                        ></cirrus-input>

                                                        <cirrus-input
                                                            v-if="
                                                                contact_details_data.phone_type.field_value ===
                                                                    'Mobile Phone' ||
                                                                contact_details_data.phone_type.field_value ===
                                                                    'Owner Mobile' ||
                                                                contact_details_data.phone_type.field_value ===
                                                                    'Service Mobile' ||
                                                                contact_details_data.phone_type.field_value ===
                                                                    'Tenant Mobile'
                                                            "
                                                            :inputFormat="'phoneClickable'"
                                                            :size="'30'"
                                                            :id="'contact_name'"
                                                            v-model="contact_details_data.contact_detail"
                                                            :edit_form="true"
                                                            :error_msg="error_msg"
                                                        ></cirrus-input>

                                                        <cirrus-input
                                                            v-if="
                                                                contact_details_data.phone_type.field_value !==
                                                                    'Mobile Phone' &&
                                                                contact_details_data.phone_type.field_value !==
                                                                    'Owner Mobile' &&
                                                                contact_details_data.phone_type.field_value !==
                                                                    'Service Mobile' &&
                                                                contact_details_data.phone_type.field_value !==
                                                                    'Tenant Mobile' &&
                                                                contact_details_data.phone_type.field_value !== 'E-Mail'
                                                            "
                                                            :size="'30'"
                                                            :id="'contact_name'"
                                                            v-model="contact_details_data.contact_detail"
                                                            :edit_form="true"
                                                            :error_msg="error_msg"
                                                        ></cirrus-input>

                                                        <cirrus-email-centralisation
                                                            v-model="contact_details_data.email_centralisation_setting"
                                                            :contact_table_name="
                                                                isLeaseFormLive()
                                                                    ? 'lease_contact'
                                                                    : 'temp_lease_contact'
                                                            "
                                                            :contact_table_id="contact_details_data.contact_detail_id"
                                                            v-if="
                                                                email_cen_setup &&
                                                                true &&
                                                                contact_details_data.phone_type.field_key === 'EMAIL' &&
                                                                lease_contact_add_arr.status !== 'new'
                                                            "
                                                            :key="'con-d-' + contact_details_data.contact_detail_id"
                                                            @click="setStatusUnsavedV2()"
                                                        ></cirrus-email-centralisation>
                                                    </td>
                                                    <td class="text-right">
                                                        <v-icon
                                                            v-if="lease_contact_add_arr.contact_details.length > 1"
                                                            color="red"
                                                            size="20"
                                                            @click="deleteContactDetailsV2(index_detail)"
                                                        >
                                                            close
                                                        </v-icon>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </template>
                                    </v-simple-table>
                                </v-col>
                                <v-col class="text-center">
                                    <v-btn
                                        depressed
                                        small
                                        color="success"
                                        @click="modalAddLineData()"
                                        >Add Detail</v-btn
                                    >
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        data-position="left center"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check</v-icon
                        >
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="lease_contact_add_arr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        data-position="left center"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all</v-icon
                        >
                        Clear
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        page_form_type: { type: String, default: '' },
        edit_flag: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
        is_inactive: { default: 0 },
    },
    data() {
        return {
            form_type: 'LEASE',
            form_section: 'LEASE_CONTACT',
            error_msg: [],
            error_server_msg: {},
            error_server_msg2: [],
            lease_contact_add_arr: [],
            loading_setting: true,
            success_flag: false,
            AED_modal: false,
            edit_form: false,
            contact_list: [],
            contact_list_old: [],
            contact_role_list: [],
            contact_phone_type_list: [],
            email_centralise_list: [],
            show_activity_log_modal: false,
            show_progress: false,
            // property_code: this.property_code,
            // lease_code: this.lease_code,
            // version_id: this.version_id,
            // current_db: this.current_db,
            // username: this.username,
            // user_type: this.user_type,
            // read_only: this.read_only,
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'lease_details',
            'lease_details_old',
            'pm_lease_form_read_only',
            'email_cen_setup',
            'sms_sending_setup',
        ]),
    },
    mounted() {
        this.loadForm();
        if (this.edit_flag) {
            this.edit_form = true;
        }
    },
    methods: {
        ...mapMutations(['SET_LEASE_CONTACT_DETAILS', 'SET_LEASE_OLD_CONTACT_DETAILS']),
        doubleClickForm() {
            if (!this.pmro_read_only) {
                if (
                    !this.read_only &&
                    !this.formSectionReadOnly(
                        this.pm_lease_form_read_only,
                        this.form_type,
                        this.form_section,
                        this.is_inactive,
                    )
                ) {
                    this.edit_form = true;
                } else {
                    this.edit_form = false;
                }
            }
        },
        loadContactList: function (show_loading = true) {
            this.loading_setting = show_loading;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/fetch/contact';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/fetch/contact';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.SET_LEASE_CONTACT_DETAILS(response.data.contact_list);
                this.SET_LEASE_OLD_CONTACT_DETAILS(response.data.contact_list);
                this.contact_list = response.data.contact_list;
                this.contact_list_old = response.data.contact_list;
                this.contact_role_list = response.data.contact_role_list;
                this.contact_phone_type_list = response.data.contact_phone_type_list;
                this.email_centralise_list = response.data.email_centralise_list;

                this.loading_setting = false;
            });
        },
        loadForm: function (show_loading = true) {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if ((this.property_code !== '' && this.lease_code !== '') || this.forceLoad) {
                this.loadContactList(show_loading);
            } else {
                this.loading_setting = false;
            }
        },
        getValueInList: function (param1, paramList) {
            let filtered = paramList.filter((m) => m.field_key === param1);
            if (filtered.length > 0) {
                return filtered[0];
            } else {
                return { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' };
            }
        },
        resetForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            this.edit_form = false;
            this.contact_list = this.contact_list_old;
        },
        addContactDetails: function (index) {
            let contact_detail_serial = this.contact_list[index].contact_serial;
            this.contact_list[index].contact_details.push({
                contact_detail_id: '0',
                contact_detail_serial: '0',
                contact_detail_code: '',
                contact_detail_code_desc: '',
                contact_detail: '',
                primary_flag: '0',
                email_centralisation_setting: [],
                phone_type: { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' },
            });
            if (this.contact_list[index].status !== 'new') this.contact_list[index].status = 'unsaved';
        },
        addLine: function () {
            this.edit_form = true;
            this.contact_list.push({
                contact_id: '0',
                contact_serial: '0',
                contact_name: '',
                contact_create_date: '',
                contact_active: '',
                contact_role_desc: '',
                contact_role: '',
                contact_primary: '0',
                contact_details: [],
                contact_type: {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                status: 'new',
            });
        },
        modalAddData: function () {
            this.AED_modal = true;
            this.lease_contact_add_arr = {
                item_no: 'New',
                contact_id: '0',
                contact_serial: '0',
                contact_name: '',
                contact_create_date: '',
                contact_active: '',
                contact_role_desc: '',
                contact_role: '',
                contact_primary: '0',
                contact_details: [
                    {
                        contact_detail_id: '0',
                        contact_detail_serial: '0',
                        contact_detail_code: '',
                        contact_detail_code_desc: '',
                        contact_detail: '',
                        primary_flag: '0',
                        email_centralisation_setting: [],
                        phone_type: {
                            value: '',
                            label: 'Please select ...',
                            field_key: '',
                            field_value: 'Please select ...',
                        },
                    },
                ],
                contact_type: {
                    value: '',
                    label: 'Please select ...',
                    field_key: '',
                    field_value: 'Please select ...',
                },
                status: 'new',
            };
        },
        modalOpenAED: function (index) {
            this.edit_form = true;
            this.AED_modal = true;
            this.lease_contact_add_arr = this.objectClone(this.contact_list[index]);
        },
        modalAddLineData: function () {
            this.AED_modal = true;
            this.lease_contact_add_arr.contact_details.push({
                contact_detail_id: '0',
                contact_detail_serial: '0',
                contact_detail_code: '',
                contact_detail_code_desc: '',
                contact_detail: '',
                primary_flag: '0',
                email_centralisation_setting: [],
                phone_type: { value: '', label: 'Please select ...', field_key: '', field_value: 'Please select ...' },
            });
        },
        async deleteContact(index) {
            if (this.contact_list[index].status === 'new') {
                this.contact_list.splice(index, 1);
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.show_progress = true;
                    // this.loading_setting = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('no_load', true);

                    form_data.append('contact_list', JSON.stringify(this.contact_list[index]));

                    let apiUrl = '';
                    if (this.isLeaseFormLive()) {
                        apiUrl = this.cirrus8_api_url + 'api/lease/delete/contact';
                    } else {
                        apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/contact';
                    }
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.contact_list.splice(index, 1);
                        this.contact_list[index].status = 'saved';
                        this.show_progress = false;
                    });
                }
            }
        },
        async deleteContactDetails(index, index_detail) {
            let contact_detail_id = this.contact_list[index].contact_details[index_detail].contact_detail_id;
            if (contact_detail_id === '0') {
                this.contact_list[index].contact_details.splice(index_detail, 1);
                if (this.contact_list[index].status !== 'new') {
                    this.contact_list[index].status = 'unsaved';
                }
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    // this.loading_setting = true;
                    this.show_progress = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('no_load', true);

                    form_data.append(
                        'contact_details_list',
                        JSON.stringify(this.contact_list[index].contact_details[index_detail]),
                    );

                    let apiUrl = '';
                    if (this.isLeaseFormLive()) {
                        apiUrl = this.cirrus8_api_url + 'api/lease/delete/contact-detail';
                    } else {
                        apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/contact-detail';
                    }
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.contact_list[index].contact_details.splice(index_detail, 1);
                        this.contact_list[index].status = 'saved';
                        this.show_progress = false;
                    });
                }
            }
        },
        async deleteContactDetailsV2(index_detail) {
            let contact_detail_id = this.lease_contact_add_arr.contact_details[index_detail].contact_detail_id;
            if (contact_detail_id === '0') {
                this.lease_contact_add_arr.contact_details.splice(index_detail, 1);
                if (this.lease_contact_add_arr.status !== 'new') {
                    this.lease_contact_add_arr.status = 'unsaved';
                }
            } else {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    // this.loading_setting = true;
                    this.show_progress = true;
                    var form_data = new FormData();
                    form_data.append('property_code', this.property_code);
                    form_data.append('lease_code', this.lease_code);
                    form_data.append('version_id', this.version_id);
                    form_data.append('no_load', true);

                    form_data.append(
                        'contact_details_list',
                        JSON.stringify(this.lease_contact_add_arr.contact_details[index_detail]),
                    );

                    let apiUrl = '';
                    if (this.isLeaseFormLive()) {
                        apiUrl = this.cirrus8_api_url + 'api/lease/delete/contact-detail';
                    } else {
                        apiUrl = this.cirrus8_api_url + 'api/temp/lease/delete/contact-detail';
                    }
                    this.$api.post(apiUrl, form_data).then((response) => {
                        this.lease_contact_add_arr.contact_details.splice(index_detail, 1);
                        this.lease_contact_add_arr.status = 'saved';
                        this.show_progress = false;
                    });
                }
            }
        },
        saveForm: function () {
            let errorArr = [];
            for (let x = 0; x < this.contact_list.length; x++) {
                let contact_name = this.contact_list[x].contact_name;
                let contact_type = this.contact_list[x].contact_type.field_key;
                let contact_details = this.contact_list[x].contact_details;
                let indexNo = x + 1;
                if (contact_name === '') errorArr.push([indexNo + '. You have not entered a valid contact name.']);
                if (contact_type === '') errorArr.push([indexNo + '. You have not entered a valid contact type.']);
                if (contact_details.length > 0) {
                    for (let y = 0; y < contact_details.length; y++) {
                        let phone_type = contact_details[y].phone_type.field_key;
                        let contact_detail = contact_details[y].contact_detail;
                        let indexDetailNo = y + 1;
                        if (phone_type === '')
                            errorArr.push([
                                indexNo + ' - ' + indexDetailNo + '. You have not entered a valid phone type.',
                            ]);
                        if (contact_detail === '')
                            errorArr.push([
                                indexNo + ' - ' + indexDetailNo + '. You have not entered a valid contact detail.',
                            ]);
                    }
                }
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                this.show_progress = true;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('contact_list', JSON.stringify(this.contact_list));

                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/update-or-create/contacts';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/update-or-create/contacts';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.show_progress = false;
                    this.edit_form = this.edit_flag;
                    // this.error_server_msg = response.data.error;
                    // if(Object.keys(this.error_server_msg).length<=0){
                    this.loadForm(false);
                    //     this.edit_form = false;
                    // }
                    this.loading_setting = false;
                });
            }
        },
        modalSubmitData: function () {
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            let errorArr = [];
            let contact_name = this.lease_contact_add_arr.contact_name;
            let contact_type = this.lease_contact_add_arr.contact_type.field_key;
            let contact_details = this.lease_contact_add_arr.contact_details;

            if (contact_name === '') errorArr.push(['You have not entered a valid contact name.']);
            if (contact_type === '') errorArr.push(['You have not entered a valid contact type.']);
            if (contact_details.length > 0) {
                for (let y = 0; y < contact_details.length; y++) {
                    let phone_type = contact_details[y].phone_type.field_key;
                    let contact_detail = contact_details[y].contact_detail;
                    let indexDetailNo = y + 1;
                    if (phone_type === '')
                        errorArr.push([indexDetailNo + '. You have not entered a valid phone type.']);
                    if (contact_detail === '')
                        errorArr.push([indexDetailNo + '. You have not entered a valid contact detail.']);
                    //if(phone_type === 'EMAIL' && !this.validateEmail(contact_detail)) errorArr.push([indexDetailNo + ". You have not entered a valid e-mail."])
                }
            }
            this.error_server_msg2 = errorArr;
            if (this.error_server_msg2.length === 0) {
                this.loading_setting = true;
                this.show_progress = true;
                let lease_contact_add_arr_te = [];
                lease_contact_add_arr_te[0] = this.lease_contact_add_arr;
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('no_load', true);

                form_data.append('contact_list', JSON.stringify(lease_contact_add_arr_te));

                let apiUrl = '';
                if (this.isLeaseFormLive()) {
                    apiUrl = this.cirrus8_api_url + 'api/lease/update-or-create/contacts';
                } else {
                    apiUrl = this.cirrus8_api_url + 'api/temp/lease/update-or-create/contacts';
                }
                this.$api.post(apiUrl, form_data).then((response) => {
                    this.show_progress = false;
                    if (response.data.error_server_msg2) {
                        this.error_server_msg2 = response.data.error_server_msg2;
                    } else {
                        this.lease_contact_add_arr = this.objectClone(response.data.lease_contact_add_arr);
                        this.edit_form = this.edit_flag;
                        bus.$emit('loadLeaseOutstandingSection', '');
                        this.loadForm(false);
                    }
                    this.loading_setting = false;
                    this.show_progress = false;
                });
            }
        },
        setStatusUnsaved: function (index) {
            if (this.contact_list[index].status !== 'new') {
                this.contact_list[index].status = 'unsaved';
            }
        },
        setStatusUnsavedV2: function (index) {
            if (this.lease_contact_add_arr.status !== 'new') {
                this.lease_contact_add_arr.status = 'unsaved';
            }
        },
        makePrimaryContact: function (index) {
            this.loading_setting = true;
            this.show_progress = true;
            let contact_serial = this.contact_list[index].contact_serial;

            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);

            form_data.append('contact_serial', contact_serial);

            let apiUrl = '';
            if (this.isLeaseFormLive()) {
                apiUrl = this.cirrus8_api_url + 'api/lease/update/make-primary-contact';
            } else {
                apiUrl = this.cirrus8_api_url + 'api/temp/lease/update/make-primary-contact';
            }
            this.$api.post(apiUrl, form_data).then((response) => {
                this.loadForm();
                this.loading_setting = false;
                this.show_progress = false;
            });
        },
        showLeaseActivityModal: function () {
            this.show_activity_log_modal = true;
        },
        show_sms_sending_modal: function (contact_details_data) {
            bus.$emit('toggleSMSSendingModal', {
                property_code: this.property_code,
                lease_code: this.lease_code,
                company_code: '',
                contact_detail_id: contact_details_data.contact_detail_id,
                form_section: this.page_form_type,
            });
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadLeaseContactSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            this.is_inactive = data;
            if (data == 1) this.edit_form = false;
        });
    },
    mixins: [global_mixins],
};
</script>
