<template>
    <div>
        <v-card>
            <v-card-title class="headline">
                Assuro Integration Form
                <a
                    href="#"
                    class="dialog-close"
                    @click.prevent="closeAssuro()"
                >
                    <v-icon>mdi-close</v-icon>
                </a>
            </v-card-title>
            <v-card-text v-if="show_form">
                <cirrus-server-error
                    :error_msg="error_server_msg"
                    :errorMsg2="error_server_msg2"
                ></cirrus-server-error>
                <div class="page-form">
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                        >
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                Bank Guarantee Amount:
                                            </td>
                                            <td class="required">*</td>
                                            <td>
                                                <cirrus-input
                                                    inputFormat="numberOnly"
                                                    :id="'bank_guarantee_amount'"
                                                    v-model="bank_guarantee_amount"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                Description:
                                            </td>
                                            <td class="required">*</td>
                                            <td>
                                                <cirrus-input
                                                    :id="'bank_guarantee_description'"
                                                    v-model="bank_guarantee_description"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                        </v-col>
                    </v-row>
                </div>

                <v-card
                    class="section-toolbar"
                    dark
                    color="titleHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h6 class="title font-weight-black">Issuer</h6>
                        <v-spacer></v-spacer>
                    </v-card-actions>
                </v-card>
                <div class="page-form">
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                        >
                            <v-row class="form-row no-gutters highlight">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                Company Name:
                                            </td>
                                            <td class=""></td>
                                            <td>
                                                <cirrus-input
                                                    :id="'issuer_debtor_name'"
                                                    v-model="issuer_debtor_name"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row class="form-row no-gutters highlight">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                ABN:
                                            </td>
                                            <td class=""></td>
                                            <td>
                                                <cirrus-input
                                                    :id="'issuer_debtor_abn'"
                                                    v-model="issuer_debtor_abn"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row class="form-row no-gutters highlight">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                Address:
                                            </td>
                                            <td class=""></td>
                                            <td>
                                                <cirrus-input
                                                    :id="'issuer_debtor_address'"
                                                    v-model="issuer_debtor_address"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                        </v-col>
                    </v-row>
                </div>

                <sui-table
                    v-if="issuer_debtor_contacts_with_email.length > 0"
                    class="vue-data-grid"
                    stackable
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    compact
                >
                    <sui-table-header>
                        <sui-table-row class="fieldDescription">
                            <sui-table-header-cell text-align="center">Include Contact</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">Name</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">E-mail</sui-table-header-cell>
                        </sui-table-row>
                    </sui-table-header>
                    <sui-table-body class="page-form">
                        <sui-table-row
                            class="form-row"
                            v-for="(issuer_data, issuer_index) in issuer_debtor_contacts_with_email"
                            :key="issuer_index"
                        >
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                            >
                                <sui-checkbox
                                    v-model="issuer_data.selected"
                                    v-on:change="issuerContactChck(issuer_index)"
                                    :disabled="issuer_debtor_contacts_with_email.length === 1"
                                />
                            </sui-table-cell>
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                                >{{ issuer_data.contact_name }}</sui-table-cell
                            >
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                                >{{ issuer_data.contact_email }}</sui-table-cell
                            >
                        </sui-table-row>
                    </sui-table-body>
                </sui-table>
                <sui-table
                    class="vue-data-grid"
                    stackable
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    compact
                >
                    <sui-table-header>
                        <sui-table-row class="fieldDescription">
                            <sui-table-header-cell text-align="center">Name</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">E-mail</sui-table-header-cell>
                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                        </sui-table-row>
                    </sui-table-header>
                    <sui-table-body class="page-form">
                        <sui-table-row class="form-row">
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                            >
                                <cirrus-input
                                    :id="'add_issuer_name'"
                                    v-model="add_issuer_name"
                                    :edit_form="true"
                                ></cirrus-input>
                            </sui-table-cell>
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                            >
                                <cirrus-input
                                    :id="'add_issuer_email'"
                                    v-model="add_issuer_email"
                                    :edit_form="true"
                                ></cirrus-input>
                            </sui-table-cell>
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                            >
                                <v-btn
                                    x-small
                                    class=""
                                    @click="addIssuerContact()"
                                    >Add Contact</v-btn
                                >
                            </sui-table-cell>
                        </sui-table-row>
                    </sui-table-body>
                </sui-table>

                <v-card
                    class="section-toolbar"
                    dark
                    color="titleHeader"
                    text
                    tile
                >
                    <v-card-actions>
                        <h6 class="title font-weight-black">Beneficiary</h6>
                        <v-spacer></v-spacer>
                    </v-card-actions>
                </v-card>
                <div class="page-form">
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                        >
                            <v-row class="form-row no-gutters highlight">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                Company Name:
                                            </td>
                                            <td class=""></td>
                                            <td>
                                                <cirrus-input
                                                    :id="'bene_owner_name'"
                                                    v-model="bene_owner_name"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row class="form-row no-gutters highlight">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                ABN:
                                            </td>
                                            <td class=""></td>
                                            <td>
                                                <cirrus-input
                                                    :id="'bene_owner_abn'"
                                                    v-model="bene_owner_abn"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row class="form-row no-gutters highlight">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                        <tr>
                                            <td
                                                class="title"
                                                align="right"
                                            >
                                                Address:
                                            </td>
                                            <td class=""></td>
                                            <td>
                                                <cirrus-input
                                                    :id="'bene_owner_address'"
                                                    v-model="bene_owner_address"
                                                    :edit_form="true"
                                                ></cirrus-input>
                                            </td>
                                        </tr>
                                    </table>
                                </v-col>
                            </v-row>
                        </v-col>
                    </v-row>
                </div>

                <sui-table
                    v-if="bene_owner_contacts_with_email.length > 0"
                    class="vue-data-grid"
                    stackable
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    compact
                >
                    <sui-table-header>
                        <sui-table-row class="fieldDescription">
                            <sui-table-header-cell text-align="center">Include Contact</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">Name</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">E-mail</sui-table-header-cell>
                        </sui-table-row>
                    </sui-table-header>
                    <sui-table-body class="page-form">
                        <sui-table-row
                            class="form-row"
                            v-for="(bene_data, bene_index) in bene_owner_contacts_with_email"
                            :key="bene_index"
                        >
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                            >
                                <sui-checkbox
                                    v-model="bene_data.selected"
                                    v-on:change="beneContactChck(bene_index)"
                                    :disabled="bene_owner_contacts_with_email.length === 1"
                                />
                            </sui-table-cell>
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                                >{{ bene_data.contact_name }}</sui-table-cell
                            >
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                                >{{ bene_data.contact_email }}</sui-table-cell
                            >
                        </sui-table-row>
                    </sui-table-body>
                </sui-table>
                <sui-table
                    class="vue-data-grid"
                    stackable
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    compact
                >
                    <sui-table-header>
                        <sui-table-row class="fieldDescription">
                            <sui-table-header-cell text-align="center">Name</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">E-mail</sui-table-header-cell>
                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                        </sui-table-row>
                    </sui-table-header>
                    <sui-table-body class="page-form">
                        <sui-table-row class="form-row">
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                            >
                                <cirrus-input
                                    :id="'add_bene_name'"
                                    v-model="add_bene_name"
                                    :edit_form="true"
                                ></cirrus-input>
                            </sui-table-cell>
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                            >
                                <cirrus-input
                                    :id="'add_bene_email'"
                                    v-model="add_bene_email"
                                    :edit_form="true"
                                ></cirrus-input>
                            </sui-table-cell>
                            <sui-table-cell
                                class="form-input"
                                text-align="center"
                            >
                                <v-btn
                                    x-small
                                    class=""
                                    @click="addBeneContact()"
                                    >Add Contact</v-btn
                                >
                            </sui-table-cell>
                        </sui-table-row>
                    </sui-table-body>
                </sui-table>
            </v-card-text>
            <v-card-text v-if="!show_form">
                <div
                    class=""
                    style="padding-left: 20px; padding-bottom: 8px"
                >
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                        >
                            <p>Thank you for applying for a new bank guarantee.</p>
                        </v-col>
                    </v-row>
                    <v-row class="form-row no-gutters">
                        <v-col
                            cols="12"
                            xs="12"
                            sm="12"
                            md="12"
                        >
                            <p>
                                You can view your application in
                                <a
                                    :href="assuro_url"
                                    target="_blank"
                                    title="Click this link to go to Assuro"
                                >
                                    <img
                                        :src="asset_domain + 'assets/images/assuro_logo.png'"
                                        alt="Assuro Logo"
                                        class="icon"
                                        width="80"
                                        height="15"
                                        style="border-bottom: none !important; margin-top: 0px" /></a
                                >.
                            </p>
                        </v-col>
                    </v-row>
                </div>
            </v-card-text>
            <v-card-actions v-if="show_form">
                <sui-checkbox v-model="agreement_flag" />
                <p style="margin-bottom: inherit; padding-left: 5px">
                    I confirm that I have read and consent and agree to
                    <a
                        href="#"
                        @click="show_agreement_modal = true"
                        >Assuro Integration Terms</a
                    >
                </p>
                <v-spacer />
                <v-btn
                    color="success"
                    depressed
                    small
                    @click="applyAssuro()"
                    :disabled="!agreement_flag"
                >
                    <v-icon
                        left
                        dark
                        size="18"
                        >mdi-check</v-icon
                    >
                    Apply for Bank Guarantee
                </v-btn>
                <v-btn
                    color="primary"
                    depressed
                    small
                    @click="closeAssuro()"
                >
                    <v-icon
                        left
                        dark
                        size="18"
                        >mdi-close</v-icon
                    >
                    Close
                </v-btn>
            </v-card-actions>
        </v-card>

        <v-dialog
            v-model="show_agreement_modal"
            max-width="500"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Assuro Integration Terms
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_agreement_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <p>
                                    By submitting a bank guarantee request via the Assuro Integration Form, you agree to
                                    comply with all of the terms and conditions in this agreement. <br />
                                    You also agree to comply with the following additional policies: <br />
                                    <br />
                                    1.
                                    <a
                                        href="https://app.assuro.com.au/privacy"
                                        target="_blank"
                                        >Assuro Privacy Policy</a
                                    >
                                    <br />
                                    2.
                                    <a
                                        href="https://app.assuro.com.au/complaintsPolicy"
                                        target="_blank"
                                        >Assuro Complaints Policy</a
                                    >
                                    <br />
                                    3.
                                    <a
                                        href="https://app.assuro.com.au/terms"
                                        target="_blank"
                                        >Assuro Terms and Conditions</a
                                    ><br />
                                    <br />
                                    By requesting a draft bank guarantee you are consenting to all screen data contained
                                    in the Assuro Integration form as well as your name and email (as the requestor)
                                    being digitally transmitted to Billd Holdings Pty Ltd ABN ************** (trading as
                                    Assuro) and any of its subsidiaries. If you do not yet have an Assuro account, you
                                    will need to register an account with Assuro to complete the bank guarantee request.
                                    Assuro will contact you, or the specified requestor (Tenant) by email or other
                                    contact details you provided.
                                    <br />
                                    <br />
                                    <br />
                                    Cir8 Pty Ltd (trading as Cirrus8) receives a commission for bank guarantees
                                    established via the Assuro Integration form.
                                    <br />
                                    <br />
                                    Cirrus8 (Cir8 Pty Ltd) takes no responsibility or liability for any transactions
                                    conducted with Assuro whatsoever. All subscribers using the Assuro platform should
                                    satisfy themselves that the product is appropriate for them and their own clients.
                                </p>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';

export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        show_form: { default: true },
    },
    data() {
        return {
            asset_domain: this.$assetDomain,
            show_assuro_modal: true,
            agreement_flag: false,
            show_agreement_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            bank_guarantee_amount: 25000,
            bank_guarantee_description: '',
            bene_owner_abn: '',
            bene_owner_address: '',
            bene_owner_code: '',
            bene_owner_contacts_with_email: [],
            bene_owner_name: '',
            issuer_debtor_abn: '',
            issuer_debtor_address: '',
            issuer_debtor_code: '',
            issuer_debtor_contacts_with_email: [],
            issuer_debtor_name: '',
            add_issuer_name: '',
            add_issuer_email: '',
            add_bene_name: '',
            add_bene_email: '',
            assuro_url: '',
        };
    },
    mounted() {
        this.loading_setting = false;
        this.loadForm();
    },
    methods: {
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '' && this.lease_code !== '') {
                this.loadAssuroFormDetails();
            }
            this.show_form = true;
        },
        loadAssuroFormDetails: function () {
            this.loading_setting = true;
            this.agreement_flag = false;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = 'assuro/fetch/details';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.bene_owner_abn = response.data.bene_owner_abn;
                this.bene_owner_address = response.data.bene_owner_address;
                this.bene_owner_code = response.data.bene_owner_code;
                let bene_owner_contacts_with_email = response.data.bene_owner_contacts_with_email;
                for (let x = 0; x <= bene_owner_contacts_with_email.length - 1; x++) {
                    if (bene_owner_contacts_with_email.length === 1) bene_owner_contacts_with_email[x].selected = true;
                    else bene_owner_contacts_with_email[x].selected = false;
                }
                if (bene_owner_contacts_with_email.length > 1) bene_owner_contacts_with_email[0].selected = true;
                this.bene_owner_contacts_with_email = bene_owner_contacts_with_email;
                this.bene_owner_name = response.data.bene_owner_name;

                this.issuer_debtor_abn = response.data.issuer_debtor_abn;
                this.issuer_debtor_address = response.data.issuer_debtor_address;
                this.issuer_debtor_code = response.data.issuer_debtor_code;
                let issuer_debtor_contacts_with_email = response.data.issuer_debtor_contacts_with_email;
                for (let x = 0; x <= issuer_debtor_contacts_with_email.length - 1; x++) {
                    if (issuer_debtor_contacts_with_email.length === 1)
                        issuer_debtor_contacts_with_email[x].selected = true;
                    else issuer_debtor_contacts_with_email[x].selected = false;
                }
                if (issuer_debtor_contacts_with_email.length > 1) issuer_debtor_contacts_with_email[0].selected = true;
                this.issuer_debtor_contacts_with_email = issuer_debtor_contacts_with_email;
                this.issuer_debtor_name = response.data.issuer_debtor_name;
                this.loading_setting = false;
            });
        },
        closeAssuro: function () {
            bus.$emit('closeGuaranteeAssuroModal', '');
        },
        applyAssuro: function () {
            let error_arr = [];

            let bank_guarantee_amount = this.bank_guarantee_amount;
            let bank_guarantee_description = this.bank_guarantee_description;
            if (bank_guarantee_amount === '') error_arr.push(['You have not entered a valid amount.']);
            else if (bank_guarantee_amount < 25000) error_arr.push(['The amount you entered should .']);

            if (bank_guarantee_amount === '') {
                error_arr.push(['You have not entered a valid amount.']);
            } else {
                if (isNaN(parseFloat(bank_guarantee_amount))) {
                    error_arr.push(['Bank guarantee amount should be at least 25,000.00']);
                }
            }

            if (bank_guarantee_description === '') error_arr.push(['You have not entered a valid description.']);
            //ISSUER DETAILS
            let issuer_debtor_name = this.issuer_debtor_name;
            let issuer_debtor_address = this.issuer_debtor_address;
            let issuer_debtor_abn = this.issuer_debtor_abn;
            let issuer_debtor_contacts_with_email = this.issuer_debtor_contacts_with_email;

            //BENEFICIARY DETAILS
            let bene_owner_name = this.issuer_debtor_name;
            let bene_owner_address = this.issuer_debtor_address;
            let bene_owner_abn = this.bene_owner_abn;
            let bene_owner_contacts_with_email = this.issuer_debtor_contacts_with_email;

            //Validate Issuer
            let filtered_issuer = this.issuer_debtor_contacts_with_email.filter((m) => m.selected === true);
            if (filtered_issuer.length === 0) error_arr.push(['Please select one issuer contact.']);
            //Validate Beneficiary
            let filtered_bene = this.bene_owner_contacts_with_email.filter((m) => m.selected === true);
            if (filtered_bene.length === 0) error_arr.push(['Please select one beneficiary contact.']);

            this.error_server_msg2 = error_arr;
            if (this.error_server_msg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('bank_guarantee_amount', bank_guarantee_amount);
                form_data.append('bank_guarantee_description', bank_guarantee_description);
                form_data.append('issuer_debtor_name', issuer_debtor_name);
                form_data.append('issuer_debtor_address', issuer_debtor_address);
                form_data.append('issuer_debtor_abn', issuer_debtor_abn);
                form_data.append('bene_owner_name', bene_owner_name);
                form_data.append('bene_owner_abn', bene_owner_abn);
                form_data.append('bene_owner_address', bene_owner_address);
                form_data.append('issuer_debtor_contacts_with_email', JSON.stringify(filtered_issuer));
                form_data.append('bene_owner_contacts_with_email', JSON.stringify(filtered_bene));

                let apiUrl = 'assuro/process/form';
                this.$api.post(apiUrl, form_data).then((response) => {
                    if (response.data.status === 'success') {
                        this.assuro_url = response.data.assuro_url;
                        this.show_form = false;
                    } else {
                        this.error_server_msg2.push([response.data.error]);
                    }
                });
            }
            // bus.$emit("closeGuaranteeAssuroModal", "");
        },
        issuerContactChck: function (index) {
            for (let x = 0; x <= this.issuer_debtor_contacts_with_email.length - 1; x++) {
                if (index !== x) this.issuer_debtor_contacts_with_email[x].selected = false;
            }
        },
        beneContactChck: function (index) {
            for (let x = 0; x <= this.bene_owner_contacts_with_email.length - 1; x++) {
                if (index !== x) this.bene_owner_contacts_with_email[x].selected = false;
            }
        },
        addBeneContact: function () {
            let error_arr = [];
            if (this.add_bene_name === '')
                error_arr.push(['You have not entered a valid contact name for the beneficiary.']);
            if (this.add_bene_email === '') error_arr.push(['You have not entered a valid email for the beneficiary.']);
            else if (!/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(this.add_bene_email))
                error_arr.push(['You have not entered a valid email for the beneficiary.']);
            else if (this.add_bene_email === localStorage.getItem('un'))
                error_arr.push([
                    this.add_bene_email +
                        ' cannot be used since it is the same with the current user email. Please use a different email.',
                ]);
            else {
                let filtered = this.bene_owner_contacts_with_email.filter(
                    (m) => m.contact_email === this.add_bene_email,
                );
                if (filtered.length > 0) {
                    error_arr.push([
                        this.add_bene_email + ' has been used multiple times. Please use a different email.',
                    ]);
                }
            }

            this.error_server_msg2 = error_arr;
            if (this.error_server_msg2.length === 0) {
                this.bene_owner_contacts_with_email.push({
                    contact_name: this.add_bene_name,
                    contact_id: null,
                    contact_role: null,
                    contact_role_desc: null,
                    contact_detail_id: null,
                    contact_email: this.add_bene_email,
                    is_owner: null,
                    selected: this.bene_owner_contacts_with_email.length === 0 ? true : false,
                });
                this.add_bene_name = '';
                this.add_bene_email = '';
            }
        },
        addIssuerContact: function () {
            let error_arr = [];
            if (this.add_issuer_name === '')
                error_arr.push(['You have not entered a valid contact name for the beneficiary.']);
            if (this.add_issuer_email === '')
                error_arr.push(['You have not entered a valid email for the beneficiary.']);
            else if (!/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(this.add_issuer_email))
                error_arr.push(['You have not entered a valid email for the beneficiary.']);
            else if (this.add_issuer_email === localStorage.getItem('un'))
                error_arr.push([
                    this.add_issuer_email +
                        ' cannot be used since it is the same with the current user email. Please use a different email.',
                ]);
            else {
                let filtered = this.issuer_debtor_contacts_with_email.filter(
                    (m) => m.contact_email === this.add_issuer_email,
                );
                if (filtered.length > 0) {
                    error_arr.push([
                        this.add_issuer_email + ' has been used multiple times. Please use a different email.',
                    ]);
                }
            }

            this.error_server_msg2 = error_arr;
            if (this.error_server_msg2.length === 0) {
                this.issuer_debtor_contacts_with_email.push({
                    contact_name: this.add_issuer_name,
                    contact_id: null,
                    contact_role: null,
                    contact_role_desc: null,
                    contact_detail_id: null,
                    contact_email: this.add_issuer_email,
                    is_owner: null,
                    selected: this.issuer_debtor_contacts_with_email.length === 0 ? true : false,
                });
                this.add_issuer_name = '';
                this.add_issuer_email = '';
            }
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
    },
    created() {
        bus.$on('loadAssuroForm', (data) => {
            this.loadForm();
        });
    },
    mixins: [global_mixins],
};
</script>
