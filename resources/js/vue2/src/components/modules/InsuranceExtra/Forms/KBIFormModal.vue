<style>
#KBI-modal .header {
    text-align: center;
    background: #000511;
    margin-top: -6px;
    padding: 10px;
}
#KBI-modal .data-grid .title {
    width: 200px;
    line-height: 17px !important;
}
#KBI-modal .noteTextArea {
    padding-top: 0px;
    margin-top: 0px;
}
#KBI-modal .v-stepper__content {
    padding: 0px;
    margin: 0px;
}
#KBI-modal .v-stepper__header {
    height: 50px;
}
#KBI-modal .v-stepper__step {
    padding: 14px;
}
#KBI-modal .v-stepper {
    box-shadow:
        0 0px 0px 0px rgb(0 0 0 / 20%),
        0 0px 0px 0 rgb(0 0 0 / 14%),
        0 0px 0px 0 rgb(0 0 0 / 12%);
}
</style>
<template>
    <div id="KBI-modal">
        <v-card>
            <v-card-title class="headline">
                Property Insurance Quote
                <a
                    href="#"
                    class="dialog-close"
                    @click.prevent="closeKBI()"
                >
                    <v-icon>mdi-close</v-icon>
                </a>
            </v-card-title>
            <!--      <v-card-text>-->
            <!--        <cirrus-server-error :error_msg="error_server_msg" :errorMsg2="error_server_msg2"></cirrus-server-error>-->
            <!--      </v-card-text>-->
            <v-card-text>
                <div class="header">
                    <img
                        :src="'assets/images/kbi_logo_hq_1890x984.png'"
                        width="100"
                        height="auto"
                    />
                    <br />
                    <span class="kbi-sub-title">SPECIALIST INSURANCE BROKERS</span>
                </div>
                <v-stepper v-model="kbi_stepper">
                    <v-stepper-header>
                        <v-stepper-step
                            :complete="kbi_stepper > 1"
                            step="1"
                        >
                            Basic Information
                            <small>Required</small>
                        </v-stepper-step>

                        <v-divider></v-divider>

                        <v-stepper-step
                            :complete="kbi_stepper > 2"
                            step="2"
                        >
                            Property Details
                            <small>Optional</small>
                        </v-stepper-step>

                        <v-divider></v-divider>

                        <v-stepper-step step="3">
                            Additional Details
                            <small>Optional</small>
                        </v-stepper-step>
                    </v-stepper-header>

                    <v-stepper-items style="height: fit-content">
                        <v-stepper-content step="1">
                            <cirrus-server-error
                                :error_msg="error_server_msg"
                                :errorMsg2="error_server_msg2"
                            ></cirrus-server-error>
                            <div class="page-form">
                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Insured Information</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Full Business Name of Applicant:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-input
                                                        :id="'business_name'"
                                                        v-model="business_name"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Address:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-input
                                                        :id="'property_address'"
                                                        v-model="property_address"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    {{ suburb_label }}:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-combobox
                                                        v-model="property_suburb"
                                                        :maxlength="40"
                                                        :items="suburb_list_filtered"
                                                        item-value="label"
                                                        item-text="label"
                                                        @change="suburbSelected(property_suburb)"
                                                        auto-select-first
                                                        hide-selected
                                                        persistent-hint
                                                        append-icon
                                                        :search-input.sync="search_suburb"
                                                        :hide-no-data="!search_suburb"
                                                        dense
                                                        ref="refSuburb"
                                                        flat
                                                    >
                                                        <template v-slot:no-data>
                                                            <v-list-item>
                                                                <v-chip
                                                                    v-model="search_suburb"
                                                                    small
                                                                >
                                                                    {{ search_suburb }}
                                                                </v-chip>
                                                            </v-list-item>
                                                        </template>
                                                    </v-combobox>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Post Code:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-combobox
                                                        v-model="property_postcode"
                                                        :maxlength="40"
                                                        :items="postcode_list_filtered"
                                                        item-value="label"
                                                        item-text="label"
                                                        @change="postcodeSelected(property_postcode)"
                                                        auto-select-first
                                                        hide-selected
                                                        persistent-hint
                                                        append-icon
                                                        :search-input.sync="searchPostcode"
                                                        :hide-no-data="!searchPostcode"
                                                        dense
                                                        ref="refSuburb"
                                                        flat
                                                    >
                                                        <template v-slot:no-data>
                                                            <v-list-item>
                                                                <v-chip
                                                                    v-model="searchPostcode"
                                                                    small
                                                                >
                                                                    {{ searchPostcode }}
                                                                </v-chip>
                                                            </v-list-item>
                                                        </template>
                                                    </v-combobox>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    State:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-single-select
                                                        v-model="property_state"
                                                        :options="getDDCountryStates('AU')"
                                                        @input="propertyStateChanged()"
                                                    />
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Gross Annual Rental:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-input
                                                        :id="'gross_annual_rental'"
                                                        v-model="gross_annual_rental"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Policy Start Date:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-icon-date-picker
                                                        :id="
                                                            'policy_start_date' + String(Math.random()).replace('.', '')
                                                        "
                                                        v-model="policy_start_date"
                                                        :size="'40'"
                                                        data-inverted=""
                                                        :data-tooltip="'policy start date'"
                                                        :edit_form="true"
                                                    ></cirrus-icon-date-picker>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Schedule & Limits</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>

                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Current Insurer:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <!--                          <cirrus-single-select v-model="current_insurer" :options="current_insurer_list"/>-->
                                                    <multiselect
                                                        :maxHeight="100"
                                                        openDirection="bottom"
                                                        data-inverted=""
                                                        v-model="current_insurer"
                                                        :options="current_insurer_list"
                                                        :allowEmpty="false"
                                                        class="vue-select2 dropdown-left dropdown-400"
                                                        group-label="language"
                                                        placeholder="Select an insurer"
                                                        track-by="field_key"
                                                        label="field_value"
                                                        :edit_form="true"
                                                        :show-labels="false"
                                                        ><span slot="noResult"
                                                            >Oops! No elements found. Consider changing the search
                                                            query.</span
                                                        >
                                                    </multiselect>
                                                    <table
                                                        v-if="current_insurer.field_value === 'Other'"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: 10px"
                                                            >
                                                                Detail:
                                                            </td>
                                                            <td>
                                                                <cirrus-input
                                                                    :size="'45'"
                                                                    :maxlength="40"
                                                                    :id="'current_insurer_other'"
                                                                    v-model="current_insurer_other"
                                                                    :edit_form="true"
                                                                ></cirrus-input>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Building Sum Insured:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-input
                                                        :id="'building_sum_insured'"
                                                        v-model="building_sum_insured"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Public Liability Limit:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="public_liability_limit"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in amounts_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="public_liability_limit === 3"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: 10px"
                                                            >
                                                                Amount:
                                                            </td>
                                                            <td>
                                                                <cirrus-input
                                                                    :size="'45'"
                                                                    :maxlength="40"
                                                                    :id="'acp_cladding_other'"
                                                                    v-model="acp_cladding_other"
                                                                    :edit_form="true"
                                                                ></cirrus-input>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Contact Details</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>

                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Name:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-input
                                                        :id="'requestor_name'"
                                                        v-model="requestor_name"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Email:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-input
                                                        :id="'requestor_email'"
                                                        v-model="requestor_email"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Phone:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-input
                                                        :id="'requestor_phone'"
                                                        v-model="requestor_phone"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>All claims in the last 5 years</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-data-table
                                    class="c8-datatable-custom"
                                    dense
                                    item-key="id"
                                    :headers="claim_headers"
                                    :items="claims"
                                    hide-default-footer
                                    :calculate-widths="true"
                                    v-if="claims.length > 0"
                                >
                                    <template v-slot:item.claim_description="{ item }">
                                        <div class="form-row no-border-line">
                                            <cirrus-input
                                                :size="'45'"
                                                :maxlength="40"
                                                :id="'claim_description'"
                                                v-model="item.claim_description"
                                                :edit_form="true"
                                            ></cirrus-input>
                                        </div>
                                    </template>
                                    <template v-slot:item.claim_date="{ item }">
                                        <div class="form-row no-border-line">
                                            <cirrus-icon-date-picker
                                                :id="'claim_date' + String(Math.random()).replace('.', '')"
                                                v-model="item.claim_date"
                                                :size="'40'"
                                                data-inverted=""
                                                :data-tooltip="'Claim date'"
                                                :edit_form="true"
                                            ></cirrus-icon-date-picker>
                                        </div>
                                    </template>

                                    <template v-slot:item.action="{ item }">
                                        <v-icon
                                            color="red"
                                            @click="deleteClaim(claims.indexOf(item))"
                                            >close</v-icon
                                        >
                                    </template>
                                </v-data-table>
                                <div class="center">
                                    <br />
                                    <v-btn
                                        depressed
                                        x-small
                                        color="normal"
                                        @click="addClaim()"
                                    >
                                        Add Claim
                                    </v-btn>
                                    <br />
                                    <br />
                                </div>
                            </div>
                        </v-stepper-content>

                        <v-stepper-content step="2">
                            <cirrus-server-error
                                :error_msg="error_server_msg"
                                :errorMsg2="error_server_msg2"
                            ></cirrus-server-error>
                            <div class="page-form">
                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Tenants</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-data-table
                                    class="c8-datatable-custom"
                                    dense
                                    item-key="id"
                                    :headers="tenant_headers"
                                    :items="tenants"
                                    hide-default-footer
                                    :calculate-widths="true"
                                >
                                    <template v-slot:item.selected="{ item }">
                                        <div class="form-row no-border-line">
                                            <v-checkbox
                                                v-model="item.selected"
                                                ripple="false"
                                                dense
                                            ></v-checkbox>
                                        </div>
                                    </template>
                                    <template v-slot:item.action1="{ item }">
                                        <div class="form-row no-border-line">
                                            <v-btn
                                                depressed
                                                x-small
                                                >Choose</v-btn
                                            >
                                        </div>
                                    </template>
                                </v-data-table>
                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Current Insurance Policy Schedule and/or Invoice</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-data-table
                                    class="c8-datatable-custom"
                                    dense
                                    item-key="id"
                                    :headers="insurer_headers"
                                    :items="insurer"
                                    hide-default-footer
                                    :calculate-widths="true"
                                >
                                    <template v-slot:item.selected="{ item }">
                                        <div class="form-row no-border-line">
                                            <v-checkbox
                                                v-model="item.selected"
                                                ripple="false"
                                                dense
                                            ></v-checkbox>
                                        </div>
                                    </template>
                                    <template v-slot:item.download_link="{ item }">
                                        <div class="form-row no-border-line">
                                            <cirrus-single-upload-button2
                                                :id="item.download_link"
                                                accept_type="pdf"
                                                v-model="item.download_link"
                                                :size_limit="20"
                                                :has_saved_file="item.download_link === '' ? false : true"
                                                :edit_form="false"
                                            ></cirrus-single-upload-button2>
                                        </div>
                                    </template>
                                </v-data-table>
                                <div
                                    class="p-10"
                                    style="padding: 10px"
                                >
                                    <cirrus-single-upload-button2
                                        id="additional-attachment"
                                        v-model="additional_attachment"
                                        :has_saved_file="false"
                                        :upload_version_2="true"
                                        :edit_form="true"
                                        accept_type="pdf"
                                        :size_limit="20"
                                    ></cirrus-single-upload-button2>

                                    <table
                                        class="data-grid data-grid-dense"
                                        width="100%"
                                        cellpadding="0"
                                        cellspacing="0"
                                        border="0"
                                    >
                                        <tbody>
                                            <tr
                                                v-for="(aaui_data, aaui_index) in additional_attachment_ui_list"
                                                :key="aaui_index"
                                            >
                                                <td class="">
                                                    <b>{{ aaui_data.file_name }}</b>
                                                </td>
                                                <td class="right">
                                                    <v-icon
                                                        color="red"
                                                        @click="deleteAttachedFile(aaui_index)"
                                                        >close</v-icon
                                                    >
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Notes for broker</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                >
                                    <v-textarea
                                        v-model="notes"
                                        auto-grow
                                        rows="2"
                                        full-width
                                        class="noteTextArea"
                                    ></v-textarea>
                                </v-col>
                            </v-row>
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="12"
                                    xl="12"
                                    class="center"
                                >
                                    <span
                                        >A broker will call you to complete the remaining details, or you can speed up
                                        the process by self-completing
                                        <v-btn
                                            depressed
                                            x-small
                                            color="normal"
                                            @click="changePage(3)"
                                        >
                                            Additional Details
                                        </v-btn>
                                    </span>
                                </v-col>
                            </v-row>
                            <br />
                            <br />
                        </v-stepper-content>

                        <v-stepper-content step="3">
                            <cirrus-server-error
                                :error_msg="error_server_msg"
                                :errorMsg2="error_server_msg2"
                            ></cirrus-server-error>
                            <div class="page-form">
                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Construction Details</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Year Built:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <cirrus-input
                                                        :id="'year_built'"
                                                        v-model="year_built"
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Walls:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="walls"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in walls_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="walls === 3"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: 54px"
                                                            >
                                                                Details:
                                                            </td>
                                                            <td>
                                                                <cirrus-input
                                                                    :size="'45'"
                                                                    :maxlength="40"
                                                                    :id="'walls_other'"
                                                                    v-model="walls_other"
                                                                    :edit_form="true"
                                                                ></cirrus-input>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Floors:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="floors"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in floors_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="floors === 2"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: 54px"
                                                            >
                                                                Details:
                                                            </td>
                                                            <td>
                                                                <cirrus-input
                                                                    :size="'45'"
                                                                    :maxlength="40"
                                                                    :id="'floors_other'"
                                                                    v-model="floors_other"
                                                                    :edit_form="true"
                                                                ></cirrus-input>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Roof:
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="roof"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in roof_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="roof === 3"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: 54px"
                                                            >
                                                                Details:
                                                            </td>
                                                            <td>
                                                                <cirrus-input
                                                                    :size="'45'"
                                                                    :maxlength="40"
                                                                    :id="'roof_other'"
                                                                    v-model="roof_other"
                                                                    :edit_form="true"
                                                                ></cirrus-input>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is there any ACP or external cladding?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="acp_cladding"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="acp_cladding === 0"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: fit-content"
                                                            >
                                                                Specification:
                                                            </td>
                                                            <td>
                                                                <cirrus-input
                                                                    :size="'45'"
                                                                    :maxlength="40"
                                                                    :id="'acp_cladding_other'"
                                                                    v-model="acp_cladding_other"
                                                                    :edit_form="true"
                                                                ></cirrus-input>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is there EPS in the building?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="eps"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="eps === 0"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: fit-content"
                                                            >
                                                                Specification:
                                                            </td>
                                                            <td>
                                                                <cirrus-input
                                                                    :size="'45'"
                                                                    :maxlength="40"
                                                                    :id="'eps_other'"
                                                                    v-model="eps_other"
                                                                    :edit_form="true"
                                                                ></cirrus-input>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    In there any Asbestos?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="asbestos"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row no-gutters"
                                    v-if="asbestos === 0"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is there an Asbestos Register in place?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="asbestos_register"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="asbestos_register === 0"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: 10px"
                                                            >
                                                                When:
                                                            </td>
                                                            <td>
                                                                <cirrus-icon-date-picker
                                                                    :id="
                                                                        'asbestos_register_other' +
                                                                        String(Math.random()).replace('.', '')
                                                                    "
                                                                    v-model="asbestos_register_other"
                                                                    :size="'40'"
                                                                    data-inverted=""
                                                                    :edit_form="true"
                                                                ></cirrus-icon-date-picker>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is the property Heritage Listed?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="heritage"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row no-gutters"
                                    v-if="heritage === 0"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Has the property been rewired?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="rewired"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="rewired === 0"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: 10px"
                                                            >
                                                                When:
                                                            </td>
                                                            <td>
                                                                <cirrus-icon-date-picker
                                                                    :id="
                                                                        'rewired_other' +
                                                                        String(Math.random()).replace('.', '')
                                                                    "
                                                                    v-model="rewired_other"
                                                                    :size="'40'"
                                                                    data-inverted=""
                                                                    :edit_form="true"
                                                                ></cirrus-icon-date-picker>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row
                                    class="form-row no-gutters"
                                    v-if="heritage === 0"
                                >
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Has the property been replumbed?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="replumbed"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                    <table
                                                        v-if="replumbed === 0"
                                                        class="data-grid data-grid-dense data-grid-no-line tableHive"
                                                    >
                                                        <tr>
                                                            <td
                                                                class="title"
                                                                style="width: 10px"
                                                            >
                                                                When:
                                                            </td>
                                                            <td>
                                                                <cirrus-icon-date-picker
                                                                    :id="
                                                                        'replumbed_other' +
                                                                        String(Math.random()).replace('.', '')
                                                                    "
                                                                    v-model="replumbed_other"
                                                                    :size="'40'"
                                                                    data-inverted=""
                                                                    :edit_form="true"
                                                                ></cirrus-icon-date-picker>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Fire Protection</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is the property Sprinklered?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="sprinklers"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is there smoke detection?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="smoke_detector"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is it monitored to security or fire brigade?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="monitored_fire"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Are there fire extinguishers/fire hydrants/ and fire hoses on the
                                                    property?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="fire_hoses"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>

                                <table
                                    class="data-grid data-grid-dense"
                                    width="100%"
                                    cellpadding="0"
                                    cellspacing="0"
                                    border="0"
                                >
                                    <tbody>
                                        <tr class="subHeader">
                                            <td class=""><b>Security</b></td>
                                            <td class="required"></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is there a monitored alarm system?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="monitored_alarm"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is there CCTV?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="cctv"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is there any security guards or patrols of the property?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="security_guards"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                                <v-row class="form-row no-gutters">
                                    <v-col
                                        cols="12"
                                        xs="12"
                                        sm="12"
                                        md="12"
                                        lg="12"
                                        xl="12"
                                    >
                                        <table class="data-grid data-grid-dense data-grid-no-line tableHive">
                                            <tr>
                                                <td
                                                    class="title"
                                                    align="right"
                                                >
                                                    Is there Deadlocks and/or window locks?
                                                </td>
                                                <td class="required">*</td>
                                                <td>
                                                    <v-btn-toggle
                                                        class="form-toggle"
                                                        v-model="deadlocks"
                                                    >
                                                        <v-btn
                                                            small
                                                            depressed
                                                            v-for="(data, index) in yes_or_no_arr"
                                                            :key="index"
                                                            text
                                                        >
                                                            {{ data.field_value }}
                                                        </v-btn>
                                                    </v-btn-toggle>
                                                </td>
                                            </tr>
                                        </table>
                                    </v-col>
                                </v-row>
                            </div>
                        </v-stepper-content>
                    </v-stepper-items>
                </v-stepper>
            </v-card-text>
            <v-card-actions>
                <v-spacer />
                <v-btn
                    v-if="kbi_stepper === 1"
                    dark
                    depressed
                    small
                    color="primary"
                    @click="changePage(2)"
                >
                    Next
                </v-btn>
                <v-btn
                    v-if="kbi_stepper === 2"
                    depressed
                    small
                    color="normal"
                    @click="changePage(1)"
                >
                    Back
                </v-btn>
                <v-btn
                    v-if="kbi_stepper === 3"
                    depressed
                    small
                    color="normal"
                    @click="changePage(2)"
                >
                    Back
                </v-btn>
                <v-btn
                    v-if="kbi_stepper === 2 || kbi_stepper === 3"
                    dark
                    depressed
                    small
                    color="primary"
                    @click="applyKBI()"
                >
                    Submit
                </v-btn>
                <v-btn
                    depressed
                    color="normal"
                    small
                    @click.prevent="closeKBI()"
                >
                    Cancel
                </v-btn>
            </v-card-actions>
        </v-card>

        <v-dialog
            v-model="show_agreement_modal"
            max-width="500"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    KBI Integration Terms
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_agreement_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                cols="12"
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <p>
                                    By submitting a bank guarantee request via the KBI Integration Form, you agree to
                                    comply with all of the terms and conditions in this agreement. <br />
                                    You also agree to comply with the following additional policies: <br />
                                    <br />
                                    1.
                                    <a
                                        href="https://app.assuro.com.au/privacy"
                                        target="_blank"
                                        >KBI Privacy Policy</a
                                    >
                                    <br />
                                    2.
                                    <a
                                        href="https://app.assuro.com.au/complaintsPolicy"
                                        target="_blank"
                                        >KBI Complaints Policy</a
                                    >
                                    <br />
                                    3.
                                    <a
                                        href="https://app.assuro.com.au/terms"
                                        target="_blank"
                                        >KBI Terms and Conditions</a
                                    ><br />
                                    <br />
                                    By requesting a draft bank guarantee you are consenting to all screen data contained
                                    in the KBI Integration form as well as your name and email (as the requestor) being
                                    digitally transmitted to Billd Holdings Pty Ltd ABN ************** (trading as KBI)
                                    and any of its subsidiaries. If you do not yet have an KBI account, you will need to
                                    register an account with KBI to complete the bank guarantee request. KBI will
                                    contact you, or the specified requestor (Tenant) by email or other contact details
                                    you provided.
                                    <br />
                                    <br />
                                    <br />
                                    Cir8 Pty Ltd (trading as Cirrus8) receives a commission for bank guarantees
                                    established via the KBI Integration form.
                                    <br />
                                    <br />
                                    Cirrus8 (Cir8 Pty Ltd) takes no responsibility or liability for any transactions
                                    conducted with KBI whatsoever. All subscribers using the KBI platform should satisfy
                                    themselves that the product is appropriate for them and their own clients.
                                </p>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
import { bus } from '../../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
import suburb_list from '../../../../plugins/australianSuburb.json';
import { mapGetters, mapState } from 'vuex';

export default {
    props: {
        property_code: { type: String, default: '' },
        show_form: { default: true },
    },
    data() {
        return {
            suburb_label: 'Suburb',
            asset_domain: this.$assetDomain,
            agreement_flag: false,
            show_agreement_modal: false,
            error_server_msg: {},
            error_server_msg2: [],
            suburb_list_filtered: [],
            postcode_list_filtered: [],
            search_suburb: '',
            searchPostcode: '',
            kbi_stepper: 1,
            business_name: '',
            property_address: '',
            property_suburb: '',
            property_postcode: '',
            property_state: '',
            gross_annual_rental: '',
            policy_start_date: '',
            requestor_name: '',
            requestor_email: '',
            requestor_phone: '',
            current_insurer: { field_key: '', field_value: 'Please select an insurer.' },
            current_insurer_other: '',
            building_sum_insured: '',
            public_liability_limit: null,
            public_liability_limit_other: '',
            notes: '',
            year_built: '',
            walls: null,
            walls_other: '',
            floors: null,
            floors_other: '',
            roof: null,
            roof_other: '',
            acp_cladding: null,
            acp_cladding_other: '',
            eps: null,
            eps_other: '',
            asbestos: null,
            asbestos_register: null,
            asbestos_register_other: '',
            heritage: null,
            rewired: null,
            rewired_other: '',
            replumbed: null,
            replumbed_other: '',
            sprinklers: null,
            smoke_detector: null,
            monitored_fire: null,
            fire_hoses: null,
            monitored_alarm: null,
            cctv: null,
            security_guards: null,
            deadlocks: null,
            claims: [],
            tenants: [],
            insurer: [],
            attachments: [],
            yes_or_no_arr: [{ field_value: 'Yes' }, { field_value: 'No' }],
            amounts_arr: [
                { field_value: '$5 Million' },
                { field_value: '$10 Million' },
                { field_value: '$20 Million' },
                { field_value: 'Other' },
            ],
            walls_arr: [
                { field_value: 'Brick' },
                { field_value: 'Concrete' },
                { field_value: 'Metal' },
                { field_value: 'Other' },
            ],
            floors_arr: [{ field_value: 'Timber' }, { field_value: 'Concrete' }, { field_value: 'Other' }],
            roof_arr: [
                { field_value: 'Tile' },
                { field_value: 'Concrete' },
                { field_value: 'Metal' },
                { field_value: 'Other' },
            ],
            current_insurer_list: [
                { field_key: 'AIG', field_value: 'AIG' },
                { field_key: 'Allianz', field_value: 'Allianz' },
                { field_key: 'CGU', field_value: 'CGU' },
                { field_key: 'CHUBB', field_value: 'CHUBB' },
                { field_key: 'Hollard', field_value: 'Hollard' },
                { field_key: 'Miramar', field_value: 'Miramar' },
                { field_key: 'QBE', field_value: 'QBE' },
                { field_key: 'Vero', field_value: 'Vero' },
                { field_key: 'Zurich', field_value: 'Zurich' },
                { field_key: 'Other', field_value: 'Other' },
            ],
            tenant_headers: [
                { text: '', value: 'selected', sortable: false, width: '50px' },
                { text: 'Unit Code', value: 'unit_code', sortable: false },
                { text: 'Unit Description', value: 'unit_description', sortable: false },
                { text: 'SQM', value: 'unit_area', align: 'end', sortable: false },
                { text: 'Lease Code', value: 'lease_code', sortable: false },
                { text: 'Lease Name', value: 'lease_name', sortable: false },
                { text: 'Unit Status', value: 'unit_status_description', sortable: false },
                { text: 'Tenant Occupation', value: 'action1', align: 'end', sortable: false },
            ],
            insurer_headers: [
                { text: '', value: 'selected', sortable: false, width: '50px' },
                { text: 'Insurer', value: 'insurer', sortable: false },
                { text: 'Policy No.', value: 'policy_no', sortable: false },
                { text: 'File', value: 'download_link', sortable: false },
            ],
            claim_headers: [
                { text: 'Description', value: 'claim_description', sortable: false },
                { text: 'Claim Date', value: 'claim_date', sortable: false },
                { text: '', value: 'action', align: 'end', sortable: false, width: '78px' },
            ],
            additional_attachment: null,
            additional_attachment_ui_list: [],
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loading_setting = false;
        this.loadForm();
        this.loadCountryDefaults();
    },
    methods: {
        loadForm: function () {
            this.error_msg = [];
            this.error_server_msg = {};
            this.error_server_msg2 = [];
            if (this.property_code !== '' && this.lease_code !== '') {
                this.loadKBIFormDetails();
            }
            this.show_form = true;
        },
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.suburb_label = this.ucwords(this.country_defaults.suburb);
            });
        },
        loadKBIFormDetails: function () {
            this.loading_setting = true;
            this.agreement_flag = false;
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('version_id', this.version_id);
            form_data.append('no_load', true);
            let apiUrl = 'kbi/fetch/details';
            this.$api.post(apiUrl, form_data).then((response) => {
                this.business_name = response.data.business_name;
                this.property_address = response.data.property_address;
                this.property_suburb = response.data.property_suburb;
                this.property_state = response.data.property_state;
                this.property_postcode = response.data.property_postcode;
                this.gross_annual_rental = response.data.gross_annual_rental;
                this.requestor_name = response.data.requestor_name;
                this.requestor_email = response.data.requestor_email;
                this.tenants = response.data.tenants;
                this.insurer = response.data.insurer;
                this.loading_setting = false;
            });
        },
        closeKBI: function () {
            bus.$emit('closeKBIModal', '');
        },
        async applyKBI() {
            let dialog_prop = {
                title: 'Warning',
                message:
                    'I/We acknowledge and approve (by my authority as the managing agent) that the information and attachments noted above will be sent to KBI Group Pty Ltd (Insurance Broker—AFSL # 494792) for the purposes to obtain property insurance quotes for the above mentioned property.',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                let property_code = this.property_code;
                let kbi_business_name = this.business_name;
                let property_address = this.property_address;
                let property_suburb = this.property_suburb;
                let property_post_code = this.property_post_code;
                let property_state = this.property_state;
                let kbi_gross_annual_rental = this.gross_annual_rental;
                let kbi_date_commence = this.policy_start_date;
                let kbi_contact_name = this.requestor_name;
                let kbi_contact_email = this.requestor_email;
                let kbi_contact_phone = this.requestor_phone;
                let kbi_current_insurer = this.current_insurer.field_key;
                let kbi_current_insurer_other = this.current_insurer_other;
                let kbi_building_sum_insured = this.building_sum_insured;
                let kbi_liability = this.getRadioValue(this.public_liability_limit, this.amounts_arr);
                let kbi_liability_other = this.public_liability_limit_other;
                let kbi_notes = this.notes;
                let kbi_year_built = this.year_built;
                let kbi_walls = this.getRadioValue(this.walls, this.walls_arr);
                let kbi_walls_other = this.walls_other;
                let kbi_floors = this.getRadioValue(this.floors, this.floors_arr);
                let kbi_floors_other = this.floors_other;
                let kbi_roof = this.getRadioValue(this.roof, this.roof_arr);
                let kbi_roof_other = this.roof_other;
                let kbi_acp = this.getRadioValue(this.acp_cladding, this.yes_or_no_arr);
                let kbi_acp_other = this.acp_cladding_other;
                let kbi_eps = this.getRadioValue(this.eps, this.yes_or_no_arr);
                let kbi_eps_other = this.eps_other;
                let kbi_asbestos = this.getRadioValue(this.asbestos, this.yes_or_no_arr);
                let kbi_asbestosregister = this.getRadioValue(this.asbestos_register, this.yes_or_no_arr);
                let kbi_asbestosregister_other = this.asbestos_register_other;
                let kbi_heritage = this.getRadioValue(this.heritage, this.yes_or_no_arr);
                let kbi_rewired = this.getRadioValue(this.rewired, this.yes_or_no_arr);
                let kbi_rewired_other = this.rewired_other;
                let kbi_replumbed = this.getRadioValue(this.replumbed, this.yes_or_no_arr);
                let kbi_replumbed_other = this.replumbed_other;
                let kbi_sprincklered = this.getRadioValue(this.sprinklers, this.yes_or_no_arr);
                let kbi_smoke_detector = this.getRadioValue(this.smoke_detector, this.yes_or_no_arr);
                let kbi_monitored_fire = this.getRadioValue(this.monitored_fire, this.yes_or_no_arr);
                let kbi_fire_hoses = this.getRadioValue(this.fire_hoses, this.yes_or_no_arr);
                let kbi_monitored_alarm = this.getRadioValue(this.monitored_alarm, this.yes_or_no_arr);
                let kbi_cctv = this.getRadioValue(this.cctv, this.yes_or_no_arr);
                let kbi_security_guards = this.getRadioValue(this.security_guards, this.yes_or_no_arr);
                let kbi_deadlock = this.getRadioValue(this.deadlocks, this.yes_or_no_arr);

                let tenant_arr = [];
                for (let x = 0; x <= this.tenants.length - 1; x++) {
                    let status_checked = this.tenants[x].selected;
                    if (status_checked.checked)
                        tenant_arr.push({
                            unit_id: this.tenants[x].unit_id,
                            kbi_occupation: this.tenants[x].tenant_occupation,
                        });
                }

                let insurer_arr = [];
                for (let x = 0; x <= this.insurer.length - 1; x++) {
                    let status_checked = this.insurer[x].selected;
                    if (status_checked.checked) insurer_arr.push({ insurer_id: this.insurer[x].insurance_id });
                }

                let attachment_arr = [];
                for (let x = 0; x <= this.additional_attachment_ui_list.length - 1; x++) {
                    attachment_arr.push({ attachment_id: this.additional_attachment_ui_list[x].attachment_id });
                }

                var form_data = new FormData();

                form_data.append('property_code', property_code);
                form_data.append('kbi_business_name', kbi_business_name);
                form_data.append('kbi_property_address', property_address);
                form_data.append('kbi_property_suburb', property_suburb);
                form_data.append('kbi_property_postcode', property_post_code);
                form_data.append('kbi_property_state', property_state);
                form_data.append('kbi_gross_annual_rental', kbi_gross_annual_rental);
                form_data.append('kbi_date_commence', kbi_date_commence);
                form_data.append('kbi_contact_name', kbi_contact_name);
                form_data.append('kbi_contact_email', kbi_contact_email);
                form_data.append('kbi_contact_phone', kbi_contact_phone);
                form_data.append('kbi_current_insurer', kbi_current_insurer);
                form_data.append('kbi_current_insurer_other', kbi_current_insurer_other);
                form_data.append('kbi_building_sum_insured', kbi_building_sum_insured);
                form_data.append('kbi_liability', kbi_liability);
                form_data.append('kbi_liability_other', kbi_liability_other);
                form_data.append('kbi_notes', kbi_notes);
                form_data.append('kbi_year_built', kbi_year_built);
                form_data.append('kbi_walls', kbi_walls);
                form_data.append('kbi_walls_other', kbi_walls_other);
                form_data.append('kbi_floors', kbi_floors);
                form_data.append('kbi_floors_other', kbi_floors_other);
                form_data.append('kbi_roof', kbi_roof);
                form_data.append('kbi_roof_other', kbi_roof_other);
                form_data.append('kbi_acp', kbi_acp);
                form_data.append('kbi_acp_other', kbi_acp_other);
                form_data.append('kbi_eps', kbi_eps);
                form_data.append('kbi_eps_other', kbi_eps_other);
                form_data.append('kbi_asbestos', kbi_asbestos);
                form_data.append('kbi_asbestos_register', kbi_asbestosregister);
                form_data.append('kbi_asbestos_register_other', kbi_asbestosregister_other);
                form_data.append('kbi_heritage', kbi_heritage);
                form_data.append('kbi_rewired', kbi_rewired);
                form_data.append('kbi_rewired_other', kbi_rewired_other);
                form_data.append('kbi_replumbed', kbi_replumbed);
                form_data.append('kbi_replumbed_other', kbi_replumbed_other);
                form_data.append('kbi_sprincklered', kbi_sprincklered);
                form_data.append('kbi_smoke_detector', kbi_smoke_detector);
                form_data.append('kbi_monitored_fire', kbi_monitored_fire);
                form_data.append('kbi_fire_hoses', kbi_fire_hoses);
                form_data.append('kbi_monitored_alarm', kbi_monitored_alarm);
                form_data.append('kbi_cctv', kbi_cctv);
                form_data.append('kbi_security_guards', kbi_security_guards);
                form_data.append('kbi_deadlock', kbi_deadlock);

                form_data.append('claim_arr', JSON.stringify(this.claims));
                form_data.append('tenant_arr', JSON.stringify(tenant_arr));
                form_data.append('insurer_arr', JSON.stringify(insurer_arr));
                form_data.append('attachment_arr', JSON.stringify(attachment_arr));

                let apiUrl = 'kbi/process/kbi-integration';
                this.$api.post(apiUrl, form_data).then((response) => {
                    if (response.data.status === 'success') {
                        bus.$emit('closeKBIModal', '');
                    } else {
                        this.error_server_msg2.push([response.data.error]);
                    }
                });
            }
        },
        addClaim: function () {
            this.claims.push({ claim_description: '', claim_date: '' });
        },
        deleteClaim: function (index) {
            this.claims.splice(index, 1);
        },
        deleteAttachedFile: function (index) {
            let url = '?module=properties&command=propertyKBI&action=removeFile';
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('attachment_id', this.additional_attachment_ui_list[index].attachment_id);
            form_data.append('app_origin', 'kbi_section');
            axios.post(url, form_data).then((response) => {
                this.additional_attachment_ui_list.splice(index, 1);
            });
        },
        changePage: function (kbi_stepper) {
            this.error_server_msg2 = [];
            let error_msg_list = {};
            error_msg_list['business_name'] = ['Full Business Name of Applicant is required.'];
            error_msg_list['property_address'] = ['Property to be insured is required.'];
            error_msg_list['gross_annual_rental'] = ['Gross Annual Rental is required.'];
            error_msg_list['policy_start_date'] = ['Date the policy is to commence is required.'];
            error_msg_list['requestor_name'] = ['Contact Name is required.'];
            error_msg_list['requestor_email'] = ['Contact Email is required.'];
            error_msg_list['requestor_phone'] = ['Contact Phone is required.'];
            error_msg_list['current_insurer'] = ['Current Insurer is required.'];
            error_msg_list['building_sum_insured'] = ['Building Sum Insured is required.'];
            error_msg_list['public_liability_limit_other'] = ['Public Liability Limit Amount is required.'];

            switch (kbi_stepper) {
                case 1:
                    this.kbi_stepper = kbi_stepper;
                    break;
                case 2:
                    if (this.business_name === '') this.error_server_msg2.push(error_msg_list['kbi_business_name']);
                    if (this.property_address === '') this.error_server_msg2.push(error_msg_list['property_address']);
                    if (this.gross_annual_rental === '')
                        this.error_server_msg2.push(error_msg_list['gross_annual_rental']);
                    if (this.policy_start_date === '') this.error_server_msg2.push(error_msg_list['policy_start_date']);
                    if (this.requestor_name === '') this.error_server_msg2.push(error_msg_list['requestor_name']);
                    if (this.requestor_email === '') this.error_server_msg2.push(error_msg_list['requestor_email']);
                    if (this.requestor_phone === '') this.error_server_msg2.push(error_msg_list['requestor_phone']);
                    if (this.current_insurer.field_key === '')
                        this.error_server_msg2.push(error_msg_list['current_insurer']);
                    if (this.building_sum_insured === '')
                        this.error_server_msg2.push(error_msg_list['building_sum_insured']);
                    if (this.public_liability_limit === null)
                        this.error_server_msg2.push(error_msg_list['public_liability_limit_other']);
                    if (this.public_liability_limit === 3 && this.public_liability_limit_other === '')
                        this.error_server_msg2.push(error_msg_list['public_liability_limit_other']);

                    for (let x = 0; x <= this.claims.length - 1; x++) {
                        if (this.claims[x].claim_description === '')
                            this.error_server_msg2.push(['Claim descriptions is required.']);
                        if (this.claims[x].claim_date === '') this.error_server_msg2.push(['Claim dates is required.']);
                    }

                    if (this.error_server_msg2.length === 0) this.kbi_stepper = kbi_stepper;
                    break;
                case 3:
                    this.kbi_stepper = kbi_stepper;
                    break;
            }
        },
        getRadioValue: function (index, data_arr) {
            // console.log(index);
            // console.log(data_arr);
            return index === null ? '' : data_arr[index].field_value;
        },
        suburbSelected(data) {
            // property_state
            if (this.$refs.refSuburb.selectedValues.length == 0) {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_postcode = data.pcode;
                    let state = data.State;
                    // this.property_state = this.getValueInList(state, this.getDDCountryStates("AU"));
                } else {
                    this.property_suburb = this.property_suburb;
                }
            } else {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_postcode = data.pcode;
                    let state = data.State;
                    // this.property_state = this.getValueInList(state, this.getDDCountryStates("AU"));
                }
            }
        },
        postcodeSelected(data) {
            // property_state
            if (this.$refs.refSuburb.selectedValues.length == 0) {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_postcode = data.pcode;
                    let state = data.State;
                    // this.property_state = this.getValueInList(state, this.getDDCountryStates("AU"));
                } else {
                    this.property_suburb = this.property_suburb;
                }
            } else {
                if (data) {
                    this.property_suburb = data.suburb;
                    this.property_postcode = data.pcode;
                    let state = data.State;
                    // this.property_state = this.getValueInList(state, this.getDDCountryStates("AU"));
                }
            }
        },
        suburbFilteredList(stateVal) {
            this.suburb_list_filtered = [];
            let filteredItem = [];

            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.suburb_list_filtered = filteredItem;
        },
        postcodeFilteredList(stateVal) {
            this.postcode_list_filtered = [];
            let filteredItem = [];
            $.each(suburb_list, function (item, value) {
                if (value.State === stateVal) {
                    value['label'] = value.suburb + ', ' + value.State + ' ' + value.pcode;
                    value['value'] = { suburb: value.suburb, pcode: value.pcode };
                    filteredItem = filteredItem.concat(value);
                }
            });
            if (filteredItem.length === 0) {
                let temp_value = [];
                temp_value['label'] = '';
                temp_value['value'] = { suburb: '', pcode: '' };
                filteredItem.push(temp_value);
            }
            this.postcode_list_filtered = filteredItem;
        },
        propertyStateChanged: function () {
            this.suburbFilteredList(this.property_state);
            this.postcodeFilteredList(this.property_state);
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        lease_code: function () {
            this.loadForm();
        },
        additional_attachment: function () {
            if (!this.additional_attachment) return;

            let url = '?module=properties&command=propertyKBI&action=upload_file';
            var form_data = new FormData();
            form_data.append('property_code', this.property_code);
            form_data.append('kbiAttachment[]', this.additional_attachment[0]);
            form_data.append('file_name', this.additional_attachment[0].name);
            form_data.append('app_origin', 'kbi_section');
            axios
                .post(url, form_data, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                })
                .then((response) => {
                    this.additional_attachment_ui_list.push({
                        attachment_id: response.data.attachment_id,
                        file_name: this.additional_attachment[0].name,
                    });
                    this.additional_attachment = null;
                });
        },
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'pm_property_form_read_only']),
        ...mapGetters(['getDDCountryStates']),
    },
    created() {
        bus.$on('loadKBIForm', (data) => {
            this.loadForm();
        });
    },
    mixins: [global_mixins],
};
</script>
