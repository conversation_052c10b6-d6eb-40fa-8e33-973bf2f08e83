<template>
    <div class="crs-header">
        <div
            id="shw-sidebar-menu"
            class="header-menu"
        >
            <span class="item"></span>
            <span class="item"></span>
            <span class="item"></span>
            <router-link
                class="item"
                to="/"
                ><img
                    src="http://localhost:8888/cirrus8/framework/assets/images/cirrus8_banner.png"
                    alt="cirrus8"
                    id="logo"
                    style="height: 50px; width: auto"
            /></router-link>
            <strong class="item">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logged in as <PERSON></strong>
            <strong class="item">
                <multiselect
                    v-model="value"
                    :options="options"
                    :canClear="false"
                    :canDeselect="false"
                    :searchable="true"
                    :classes="{ singleLabel: 'multiselect-single-label color-white' }"
                    class="crs-multiselect"
                />
            </strong>
            <strong class="item">on</strong>
            <strong class="item">
                <multiselect
                    v-model="value"
                    :options="options"
                    :canClear="false"
                    :canDeselect="false"
                    :searchable="true"
                    :classes="{ singleLabel: 'multiselect-single-label color-white' }"
                    class="crs-multiselect"
                />
            </strong>
            <div class="item right">
                <span class="item">
                    <div>
                        <div>
                            <div></div>
                            <div>
                                <input
                                    maxlength="60"
                                    id="input-646"
                                    placeholder="Search"
                                    type="text"
                                />
                            </div>
                        </div>
                    </div>
                </span>
                <span class="item">
                    <router-link
                        to="/"
                        accesskey="J"
                        ><span style="font-weight: bold">Dashboard</span></router-link
                    >
                </span>
                <span class="item">
                    <router-link to="/administration"><span>Administration</span></router-link>
                </span>
                <span class="item">
                    <router-link
                        to="/"
                        accesskey="W"
                    >
                        <span>cirrusFM</span>
                    </router-link>
                </span>
                <span class="item"
                    ><a id="logout"
                        ><span><strong>Logout</strong></span></a
                    ></span
                >
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'Header',
    data() {
        return {
            value: ['Client Accountant'],
            options: ['Client Accountant', 'Account Payable', 'Project Manager', 'Owner Portal Plus', 'AI Operator'],
        };
    },
};
</script>
<style scoped lang="scss">
.crs-header {
    background: #00baf2;
    width: auto;
    color: white;
    height: 50px;
    #shw-sidebar-menu {
        box-sizing: initial;
    }
    #shw-sidebar-menu > .item {
        font:
            11px 'Segoe UI',
            Helvetica,
            Arial,
            Tahoma,
            sans-serif !important;
        vertical-align: middle !important;
        line-height: 1 !important;
        text-decoration: none !important;
        -webkit-tap-highlight-color: transparent !important;
        flex: 0 0 auto !important;
        transition:
            background 0.1s ease,
            box-shadow 0.1s ease,
            color 0.1s ease !important;
        align-items: center;
    }
    .header-menu {
        background: none;
        border-radius: 0em;
        border: none;
        box-shadow: none;
        font-size: 1rem;
        display: flex;
        font-family: 'Lato', 'Helvetica Neue', Arial, Helvetica, sans-serif;
        font-weight: normal;
        min-height: 2.85714286em;
        .item {
            font-size: 11px !important;
            color: white;
            padding-top: 0px !important;
            padding-left: 0.2em !important;
            padding-right: 0.2em !important;
            padding-bottom: 0px !important;
            margin: 0em 0.35714286em;
            align-self: center;
            box-shadow: none;
            border: none;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            a {
                background-color: transparent;
                color: white;
                text-decoration: none;
            }
            .crs-multiselect {
                color: #444;
                width: 180px;
                height: 26px;
                min-height: 26px;
                background: #00baf2;
                font:
                    11px 'Segoe UI',
                    Helvetica,
                    Arial,
                    Tahoma,
                    sans-serif !important;
                .multiselect-search {
                    //background: #00BAF2 !important;
                    //font: 11px "Segoe UI", Helvetica, Arial, Tahoma, sans-serif !important;
                }

                .multiselect-dropdown {
                    .multiselect-options {
                        .multiselect-option {
                            //color: #444 !important;
                        }
                    }
                }
            }
            //.crs-multiselect .multiselect-single-label {
            //
            //  //background: #00BAF2 !important;
            //  //color: #ffffff !important;
            //}
            --ms-option-font-size: 11px;
            --ms-option-bg-selected-pointed: #00baf2;
            --ms-option-bg-selected: #00baf2;
            --ms-option-bg-pointed: #00baf2;
            --ms-dropdown-bg: #ffffff;
            --ms-option-color-pointed: #ffffff;
            --ms-bg: #00baf2;
            --ms-radius: 2px;
        }
        .right {
            display: flex;
            margin-left: auto !important;
        }
    }
    .header-menu:last-child {
        margin-bottom: 0rem;
    }
    .header-menu:first-child {
        margin-bottom: 0rem;
    }
}
.color-white {
    color: #ffffff !important;
}
</style>
