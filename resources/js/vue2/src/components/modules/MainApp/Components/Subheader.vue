<template>
    <div class="crs-subheader">
        <nav role="select">
            <ul>
                <li><router-link to="/">Accounts Payable</router-link></li>
                <li><router-link to="/">Accounts Receivable</router-link></li>
                <li><router-link to="/">Accounting Reports</router-link></li>
                <li><router-link to="/">Management Reports</router-link></li>
                <li><router-link to="/">Bank Reconciliation</router-link></li>
                <li><router-link to="/">Properties</router-link></li>
                <li><router-link to="/">Leases</router-link></li>
                <li><router-link to="/">Companies</router-link></li>
                <li><router-link to="/">Sales Trust</router-link></li>
                <li><router-link to="/">General <PERSON></router-link></li>
                <li><router-link to="/">Retail</router-link></li>
                <li><router-link to="/">Business Intelligence</router-link></li>
                <li><router-link to="/">Configuration</router-link></li>
                <li><router-link to="/">Task Management</router-link></li>
            </ul>
        </nav>
    </div>
</template>
<script>
export default {
    name: 'Subheader',
    data() {
        return {
            value: ['Client Accountant'],
            options: ['Client Accountant', 'Account Payable', 'Project Manager', 'Owner Portal Plus', 'AI Operator'],
        };
    },
};
</script>
<style scoped lang="scss">
.crs-subheader {
    padding: 0;
    margin: 0;
    display: table;
    table-layout: fixed;
    width: 100%;
    font:
        11px 'Segoe UI',
        Helvetica,
        Arial,
        Tahoma,
        sans-serif !important;

    ul {
        width: 100%;
        margin: 0 !important;
        padding: 0 !important;
        display: flex;
        flex-direction: row;
        list-style: none;
        -webkit-margin-before: 1em;
        -webkit-margin-after: 1em;
        -webkit-margin-start: 0px;
        -webkit-margin-end: 0px;
        -webkit-padding-start: 40px;
        background: #006581;
        li {
            background: #006581;
            height: 40px;
            text-align: center;
            transition: 0.3s;
            a {
                height: 40px;
                padding: 0 20px;
                display: -ms-flexbox;
                display: -webkit-flex;
                display: flex;
                -ms-flex-align: center;
                -webkit-align-items: center;
                -webkit-box-align: center;
                align-items: center;
                text-align: left;
                line-height: 11px;
                color: #fff;
                text-decoration: none;
                font-weight: bold;
                transition: 0.3s;
            }
        }
        li:hover {
            background: #fff;
            a {
                color: #444 !important;
            }
        }
    }
}
</style>
