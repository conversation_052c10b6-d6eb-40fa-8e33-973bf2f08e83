<template>
    <div class="c8-page page-form">
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header
                    :title="page_title"
                    :subtitle="page_subtitle"
                />
            </v-toolbar-title>
            <v-spacer></v-spacer>
        </v-toolbar>

        <div>
            <div class="page-form">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Delivery Status
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            class="form-toggle"
                            v-model="source_type"
                            mandatory
                        >
                            <v-btn
                                small
                                depressed
                                text
                            >
                                All
                            </v-btn>
                            <v-btn
                                small
                                depressed
                                text
                            >
                                Sent
                            </v-btn>
                            <v-btn
                                small
                                depressed
                                text
                            >
                                Failed
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="has_admin_access"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Database
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            openDirection="bottom"
                            v-model="database_id"
                            :options="database_list"
                            :allowEmpty="true"
                            optionsLimit="10000"
                            class="vue-select2 dropdown-left dropdown-400"
                            placeholder="Select a client"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <!--            <v-btn class="ma-1" x-small :color="database_id.queue_enable ? 'success' : 'warning'" v-if="environment !== 'live'" v-show="database_id.field_value !== 'All' && admin === '1'" @click="toggleQueueSetting()" :loading="para_btn_loading"> {{ database_id.queue_enable ? 'Queue is enabled' : 'Queue is disabled' }}</v-btn>-->
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Recipient Email
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-text-field
                            dense
                            v-model.lazy="recipient_email"
                            @blur="loadForm()"
                            v-on:keyup.enter="loadForm()"
                        ></v-text-field>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Date Range
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <cirrus-icon-date-picker
                            :size="'40'"
                            v-model="start_date"
                            :edit_form="true"
                        ></cirrus-icon-date-picker>
                        -
                        <cirrus-icon-date-picker
                            :size="'40'"
                            v-model="end_date"
                            :edit_form="true"
                        ></cirrus-icon-date-picker>

                        <v-menu>
                            <template v-slot:activator="{ on: menu, attrs }">
                                <v-tooltip bottom>
                                    <template v-slot:activator="{ on: tooltip }">
                                        <v-icon
                                            small
                                            color="primary"
                                            v-bind="attrs"
                                            v-on="{ ...tooltip, ...menu }"
                                        >
                                            mdi-toolbox
                                        </v-icon>
                                    </template>
                                    <span>Date shortcuts</span>
                                </v-tooltip>
                            </template>
                            <v-list>
                                <v-list-item @click="setDates('today')">
                                    <v-list-item-title>Today</v-list-item-title>
                                </v-list-item>
                                <v-list-item @click="setDates('past7days')">
                                    <v-list-item-title>Past 7 days</v-list-item-title>
                                </v-list-item>
                                <v-list-item @click="setDates('thisWeek')">
                                    <v-list-item-title>This Week</v-list-item-title>
                                </v-list-item>
                                <v-list-item @click="setDates('thisMonth')">
                                    <v-list-item-title>This Month</v-list-item-title>
                                </v-list-item>
                                <v-list-item @click="setDates('thisYear')">
                                    <v-list-item-title>This Year</v-list-item-title>
                                </v-list-item>
                            </v-list>
                        </v-menu>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="12"
                        md="12"
                        class="text-right"
                    >
                        <v-btn
                            @click="loadForm()"
                            color="primary"
                            depressed
                            small
                        >
                            Apply Filter
                        </v-btn>
                    </v-col>
                </v-row>
            </div>
            <br />
            <v-tabs
                class="cirrus-tab-theme"
                style="border-radius: 0px"
            >
                <v-tab style="justify-content: left !important">
                    <v-icon> mdi-history</v-icon>
                    Logs
                </v-tab>
                <v-tab style="justify-content: left !important">
                    <v-icon> mdi-email</v-icon>
                    Email Queue
                </v-tab>
                <v-tab style="justify-content: left !important">
                    <v-icon> mdi-email-lock</v-icon>
                    Suppression List
                </v-tab>

                <v-tab-item>
                    <div class="form-row">
                        <v-card
                            class="section-toolbar subHeader"
                            text
                            tile
                            elevation="0"
                        >
                            <v-card-actions>
                                <v-checkbox
                                    v-model="expand_column_log"
                                    v-if="environment !== 'live'"
                                    label="Show more information"
                                    ripple="false"
                                    dense
                                ></v-checkbox>
                                <v-spacer></v-spacer>
                                <cirrus-input
                                    inputFormat="search"
                                    v-model="log_search_datatable"
                                    placeholder="Search"
                                    :edit_form="true"
                                    style="padding-right: 1em"
                                ></cirrus-input>
                                <v-btn
                                    x-small
                                    class="v-step-refresh-button"
                                    icon
                                    @click="loadForm()"
                                >
                                    <v-icon>refresh</v-icon>
                                </v-btn>
                            </v-card-actions>
                        </v-card>
                    </div>
                    <cirrus-content-loader
                        :show_skeleton_loader="false"
                        v-if="loading_setting"
                    ></cirrus-content-loader>
                    <v-data-table
                        class="c8-datatable-custom"
                        dense
                        item-key="id"
                        :search="log_search_datatable"
                        :headers="log_headers"
                        :items="email_log_list"
                        :items-per-page="100"
                        :footer-props="{ 'items-per-page-options': [100, 200, 300, 400, 500, -1] }"
                    >
                        <template v-slot:item.index="{ item }">
                            <span v-if="!item.email_status_message">{{
                                indexCounter(email_log_list.indexOf(item) + 1)
                            }}</span>
                            <span v-else>
                                <v-tooltip
                                    v-model="item.show_error"
                                    top
                                >
                                    <template v-slot:activator="{ on, attrs }">
                                        <v-icon
                                            x-small
                                            color="red"
                                            v-bind="attrs"
                                            v-on="on"
                                        >
                                            mdi-alert
                                        </v-icon>
                                    </template>
                                    <span>{{ item.email_status_message }}</span>
                                </v-tooltip>
                            </span>
                        </template>
                        <template v-slot:item.database_icon="{ item }">
                            <v-tooltip
                                v-model="item.show"
                                top
                            >
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        x-small
                                        icon
                                        v-bind="attrs"
                                        v-on="on"
                                    >
                                        <v-icon color="grey"> mdi-database</v-icon>
                                    </v-btn>
                                </template>
                                <span>{{ item.client_name }}</span>
                            </v-tooltip>
                        </template>
                        <template v-slot:item.attachments_count="{ item }">
                            <img
                                :src="asset_domain + 'assets/images/icons/pdf.png'"
                                alt="pdf"
                                class="icon"
                                v-for="(data, index) in item.attachments_content || []"
                                :key="'att_' + index"
                                @click="downloadFile(data.file)"
                            />
                        </template>
                        <template v-slot:item.recipient_email="{ item }">
                            <p @click="item.expand_email = !item.expand_email">
                                {{ item.expand_email ? item.recipient_email : getShortForm(item.recipient_email) }}
                            </p>
                        </template>
                        <template v-slot:item.subject="{ item }">
                            <a
                                v-if="item.postmark_log_id"
                                @click="
                                    showViewEmailModal(
                                        'log',
                                        item.id,
                                        item.email_log_id,
                                        item.postmark_log_id,
                                        email_log_list.indexOf(item),
                                    )
                                "
                                >{{ item.subject === '' ? '(no subject)' : item.subject }}</a
                            >
                            <span v-else>{{ item.subject === '' ? '(no subject)' : item.subject }}</span>
                        </template>
                        <template v-slot:item.action1="{ item }">
                            <v-btn
                                v-if="item.postmark_log_id"
                                x-small
                                icon
                                :loading="item.loading_setting_icon"
                                @click="
                                    showViewEmailModal(
                                        'log',
                                        item.id,
                                        item.email_log_id,
                                        item.postmark_log_id,
                                        email_log_list.indexOf(item),
                                    )
                                "
                            >
                                <v-icon small>mdi-eye</v-icon>
                            </v-btn>
                        </template>
                    </v-data-table>
                </v-tab-item>
                <v-tab-item>
                    <!--          <v-alert-->
                    <!--              dense-->
                    <!--              type="info"-->
                    <!--              elevation="0"-->
                    <!--              class="ma-0 pa-1"-->
                    <!--              tile-->
                    <!--          >-->
                    <!--            Please note that our system is set to keep email queued logs for up to <strong>{{ queue_storing_days }}</strong> days only.-->
                    <!--          </v-alert>-->
                    <div class="form-row">
                        <v-card
                            class="section-toolbar subHeader"
                            text
                            tile
                            elevation="0"
                        >
                            <v-card-actions>
                                <v-checkbox
                                    v-model="expand_column"
                                    v-if="environment !== 'live'"
                                    label="Show more information"
                                    ripple="false"
                                    dense
                                ></v-checkbox>
                                <v-spacer></v-spacer>
                                <v-btn
                                    v-show="queue_email_list.length > 0"
                                    v-if="selected_q_email_log.length === 0"
                                    x-small
                                    color="primary"
                                    @click="sendAllFailedEmail()"
                                >
                                    <v-icon x-small>mdi-email</v-icon>
                                    Re-send all email
                                </v-btn>
                                <v-btn
                                    v-show="queue_email_list.length > 0"
                                    v-else
                                    x-small
                                    color="warning"
                                    @click="sendSelectedFailedEmail()"
                                >
                                    <v-icon x-small>mdi-email</v-icon>
                                    Re-send selected email
                                </v-btn>
                                <cirrus-input
                                    inputFormat="search"
                                    v-model="search_datatable"
                                    placeholder="Search"
                                    :edit_form="true"
                                    style="padding-right: 1em"
                                ></cirrus-input>
                                <v-btn
                                    x-small
                                    class="v-step-refresh-button"
                                    icon
                                    @click="loadForm()"
                                >
                                    <v-icon>refresh</v-icon>
                                </v-btn>
                            </v-card-actions>
                        </v-card>
                    </div>
                    <cirrus-content-loader
                        :show_skeleton_loader="false"
                        v-if="loading_setting_for_queue_log"
                    ></cirrus-content-loader>
                    <v-data-table
                        class="c8-datatable-custom"
                        dense
                        item-key="id"
                        show-select
                        v-model="selected_q_email_log"
                        :search="search_datatable"
                        :headers="headers"
                        :items="queue_email_list"
                        :items-per-page="-1"
                        hide-default-footer
                    >
                        <template v-slot:item.index="{ item }">
                            <span v-if="!item.recipient_error_message">{{
                                indexCounter(queue_email_list.indexOf(item) + 1)
                            }}</span>
                            <span v-else>
                                <v-tooltip
                                    v-model="item.show_error"
                                    top
                                >
                                    <template v-slot:activator="{ on, attrs }">
                                        <v-icon
                                            x-small
                                            color="red"
                                            v-bind="attrs"
                                            v-on="on"
                                            >mdi-alert</v-icon
                                        >
                                    </template>
                                    <span>{{ item.recipient_error_message }}</span>
                                </v-tooltip>
                            </span>
                        </template>
                        <template v-slot:item.database_icon="{ item }">
                            <v-tooltip
                                v-model="item.show"
                                top
                            >
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        x-small
                                        icon
                                        v-bind="attrs"
                                        v-on="on"
                                    >
                                        <v-icon color="grey"> mdi-database</v-icon>
                                    </v-btn>
                                </template>
                                <span>{{ item.client_name }}</span>
                            </v-tooltip>
                        </template>
                        <template v-slot:item.recipient_email="{ item }">
                            <p @click="item.expand_email = !item.expand_email">
                                {{ item.expand_email ? item.recipient_email : getShortForm(item.recipient_email) }}
                            </p>
                        </template>
                        <template v-slot:item.from_email="{ item }">
                            {{ item.from_email }} <br />
                            ({{ item.from_name }})
                        </template>
                        <template v-slot:item.action1="{ item }">
                            <v-btn
                                x-small
                                icon
                                :loading="item.loading_setting_icon"
                                @click="
                                    showViewEmailModal(
                                        'queue',
                                        item.id,
                                        item.email_log_id,
                                        item.postmark_log_id,
                                        queue_email_list.indexOf(item),
                                    )
                                "
                            >
                                <v-icon small>mdi-eye</v-icon>
                            </v-btn>
                        </template>
                    </v-data-table>
                </v-tab-item>
                <v-tab-item>
                    <div class="form-row">
                        <v-card
                            class="section-toolbar subHeader"
                            text
                            tile
                            elevation="0"
                        >
                            <v-card-actions>
                                <v-spacer></v-spacer>
                                <cirrus-input
                                    inputFormat="search"
                                    v-model="search_suppression_datatable"
                                    placeholder="Search"
                                    :edit_form="true"
                                    style="padding-right: 1em"
                                ></cirrus-input>
                                <v-btn
                                    x-small
                                    class="v-step-refresh-button"
                                    icon
                                    @click="loadSuppressionList()"
                                >
                                    <v-icon>refresh</v-icon>
                                </v-btn>
                            </v-card-actions>
                        </v-card>
                    </div>
                    <cirrus-content-loader
                        :show_skeleton_loader="false"
                        v-if="loading_setting"
                    ></cirrus-content-loader>
                    <v-data-table
                        class="c8-datatable-custom"
                        dense
                        item-key="id"
                        :search="search_suppression_datatable"
                        :headers="headers_suppression"
                        :items="email_suppression_list"
                        :items-per-page="-1"
                        hide-default-footer
                    >
                        <template v-slot:item.index="{ item, index }">
                            {{ index + 1 }}
                        </template>
                        <template v-slot:item.action1="{ item, index }">
                            <v-btn
                                x-small
                                icon
                                :loading="item.loading_setting_icon"
                                @click="deleteEmailFromSuppression(item)"
                            >
                                <v-icon
                                    color="red"
                                    small
                                    >close
                                </v-icon>
                            </v-btn>
                        </template>
                    </v-data-table>
                </v-tab-item>
            </v-tabs>
        </div>

        <v-dialog
            v-model="show_view_email_modal"
            max-width="1000"
            content-class="c8-page view-email-modal"
        >
            <v-card>
                <v-card-title class="headline">
                    Email Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_view_email_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <v-alert
                        v-if="email_details_data.email_status !== ''"
                        dense
                        text
                        :type="
                            !['info', 'warning', 'error', 'success'].includes(email_details_data.email_status)
                                ? 'warning'
                                : email_details_data.email_status
                        "
                        style="text-align: start; border-radius: revert; margin-top: -6px"
                    >
                        {{ email_details_data.email_status_message }}
                    </v-alert>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="12"
                                md="12"
                            >
                                <table>
                                    <tr>
                                        <td>Subject</td>
                                        <td>
                                            {{
                                                email_details_data.subject === ''
                                                    ? '(no subject)'
                                                    : email_details_data.subject
                                            }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>From</td>
                                        <td>
                                            {{ email_details_data.from_name }}
                                            {{
                                                email_details_data.from_name === ''
                                                    ? email_details_data.from_email
                                                    : '<' + email_details_data.from_email + '>'
                                            }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Recipients</td>
                                        <td class="alert-td">
                                            <v-row>
                                                <v-col
                                                    xs="6"
                                                    sm="6"
                                                    md="6"
                                                    v-for="(data, index) in email_details_data.recipient_list || []"
                                                    :key="'reci_' + index"
                                                >
                                                    <v-alert
                                                        class="recipient-alert"
                                                        dense
                                                        border="left"
                                                        @mouseover="data.show_details = true"
                                                        @mouseleave="data.show_details = false"
                                                    >
                                                        <div class="recipient-email">{{ data.recipient_email }}</div>
                                                        <div
                                                            class="recipient-status"
                                                            v-show="data.show_details || index_2 === 0"
                                                            v-for="(data_2, index_2) in data.recipient_status_list"
                                                            :key="'rec_st_' + index_2"
                                                        >
                                                            {{ data_2.status }} {{ data_2.timestamp }}
                                                        </div>
                                                    </v-alert>
                                                </v-col>
                                                <v-col
                                                    xs="6"
                                                    sm="6"
                                                    md="6"
                                                    v-for="(data, index) in email_details_data.cc_list || []"
                                                    :key="'cc_' + index"
                                                >
                                                    <v-alert
                                                        class="cc-alert"
                                                        dense
                                                        border="left"
                                                        @mouseover="data.show_details = true"
                                                        @mouseleave="data.show_details = false"
                                                    >
                                                        <div class="cc-email">{{ data.recipient_email }}</div>
                                                        <div
                                                            class="cc-status"
                                                            v-show="data.show_details || index_2 === 0"
                                                            v-for="(data_2, index_2) in data.cc_status_list || []"
                                                            :key="'cc_sta_' + index_2"
                                                        >
                                                            {{ data_2.status }} {{ data_2.timestamp }}
                                                        </div>
                                                    </v-alert>
                                                </v-col>
                                                <v-col
                                                    xs="6"
                                                    sm="6"
                                                    md="6"
                                                    v-for="(data, index) in email_details_data.failed_email_list || []"
                                                    :key="'fail_' + index"
                                                >
                                                    <v-alert
                                                        class="failed-alert"
                                                        type="error"
                                                        dense
                                                        border="left"
                                                        @mouseover="data.show_details = true"
                                                        @mouseleave="data.show_details = false"
                                                    >
                                                        <div class="failed-email">{{ data.recipient_email }}</div>
                                                        <div
                                                            class="failed-status"
                                                            v-show="data.show_details || index_2 === 0"
                                                            v-for="(data_2, index_2) in data.status_list || []"
                                                            :key="'stat_' + index_2"
                                                        >
                                                            {{ data_2.status }}
                                                        </div>
                                                    </v-alert>
                                                </v-col>
                                            </v-row>
                                        </td>
                                    </tr>
                                    <tr v-if="email_details_data.attachments_count > 0">
                                        <td>Attachment(s)</td>
                                        <td class="alert-td pa-0">
                                            <div
                                                v-for="(data, index) in email_details_data.attachments_content || []"
                                                :key="'att_con' + index"
                                                @click="downloadFile(data.file)"
                                            >
                                                <img
                                                    :src="asset_domain + 'assets/images/icons/pdf.png'"
                                                    alt="pdf"
                                                    class="icon"
                                                />
                                                {{ data.file_name }}
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                <div
                                    v-if="email_details_data.email_status !== 'no_log'"
                                    style="height: 500px"
                                >
                                    <p>*Images hosted on unsecured sites might not be displayed properly</p>
                                    <iframe
                                        class="modal-iframe"
                                        :srcdoc="email_details_data.body_raw"
                                    ></iframe>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        @click="resendEmail()"
                        v-if="email_details_data.email_status !== 'no_log'"
                        depressed
                        small
                    >
                        Re-send &nbsp;
                        <v-icon small>mdi-send</v-icon>
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        @click="show_view_email_modal = false"
                        >Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="show_sending_process_modal"
            max-width="700"
            content-class="c8-page view-email-modal"
        >
            <v-card>
                <v-card-title class="headline">
                    Sending Process
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_sending_process_modal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text class="pa-3">
                    <v-alert
                        dense
                        type="info"
                        elevation="0"
                        class="ma-0 pa-1"
                        tile
                    >
                        Please do not refresh or leave this page while the process is running, as it may interrupt
                        sending
                    </v-alert>
                    <br />
                    <v-progress-linear
                        :color="sending_progress_color"
                        v-model="sending_progress"
                        height="25"
                    >
                        <strong
                            >{{ current_successful_request_num }}/{{ current_successful_request_total }}
                            <strong v-if="cancel_sending_process">(Finished)</strong></strong
                        >
                    </v-progress-linear>
                    <strong>Processed Emails:</strong>
                    <v-simple-table
                        dense
                        fixed-header
                        height="300px"
                    >
                        <template v-slot:default>
                            <thead>
                                <tr>
                                    <th class="text-left">ID</th>
                                    <th class="text-left">Recipient</th>
                                    <th class="text-left">Subject</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    v-for="(item, index) in email_send_log_list || []"
                                    :key="'log_' + index"
                                >
                                    <td>{{ item.item_no }}</td>
                                    <td>{{ item.recipient }}</td>
                                    <td>{{ item.subject }}</td>
                                </tr>
                            </tbody>
                        </template>
                    </v-simple-table>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn
                        color="primary"
                        v-if="cancel_sending_process"
                        text
                        @click="show_sending_process_modal = false"
                        >Close
                    </v-btn>
                    <v-btn
                        color="primary"
                        v-else
                        text
                        @click="cancelEmailSendingProcess()"
                        >Cancel
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
import global_mixins, { cirrusDialog } from '../../../../plugins/mixins';
import axios from 'axios';

export default {
    data() {
        return {
            loading_setting: false,
            loading_setting_for_queue_log: false,
            edit_form: false,
            error_server_msg: {},
            error_server_msg2: [],
            headers: [
                { text: '', value: 'database_icon', sortable: false, width: '40px' },
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', width: '70px' },
                { text: 'Queued At', value: 'queued_at' },
                { text: 'Recipient', value: 'recipient_email' },
                { text: 'Subject', value: 'subject' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '100px' },
            ],
            headers_suppression: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Email', value: 'email_address' },
                { text: 'Reason', value: 'reason' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '100px' },
            ],
            log_headers: [
                { text: '', value: 'database_icon', sortable: false, width: '40px' },
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Recipient', value: 'recipient_email' }, // to email{text: '', value: 'database_icon', sortable: false, width: '40px'},
                { text: 'From', value: 'from_email' }, //date and time email got inserted to queue table
                { text: 'Subject', value: 'subject' }, // subject
                { text: 'Sent', value: 'sent_datetime' }, //data from email_log
                { text: 'Source', value: 'source' }, //data from email_log
                { text: 'Attachment(s)', value: 'attachments_count' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '100px' },
            ],
            search_suppression_datatable: '',
            email_suppression_list: [],
            search_datatable: '',
            selected_q_email_log: [],
            log_search_datatable: '',
            page: 1,
            page_count: 0,
            items_per_page: 50,
            queue_email_list: [],
            email_log_list: [],
            show_view_email_modal: false,
            email_details_data: {
                subject: '',
                from_name: '',
                from_email: '',
                recipient_list: [
                    {
                        recipient_email: '',
                        show_details: false,
                        recipient_status_list: [
                            { status: 'Opened', timestamp: '2024/05/06 07:58:57 AM' },
                            { status: 'Delivered', timestamp: '2024/05/06 07:58:57 AM' },
                            { status: 'Processed', timestamp: '2024/05/06 07:58:57 AM' },
                        ],
                    },
                ],
                cc_list: [
                    {
                        recipient_email: '',
                        show_details: false,
                        cc_status_list: [
                            { status: 'Opened', timestamp: '2024/05/06 07:58:57 AM' },
                            { status: 'Delivered', timestamp: '2024/05/06 07:58:57 AM' },
                            { status: 'Processed', timestamp: '2024/05/06 07:58:57 AM' },
                        ],
                    },
                ],
            },
            body_raw: '',
            email_status: '',
            email_status_message: '',
            server_last_run_timestamp: 'OFF',
            environment: 'local',
            queue_storing_days: '0',
            source_type: 0,
            database_id: { field_key: '', field_value: 'All', queue_enable: null },
            database_list: [],
            start_date: null,
            end_date: null,
            recipient_email: null,
            expand_column: false,
            include_system: false,
            expand_column_log: false,
            loading_setting_icon: false,
            para_btn_loading: false,
            admin: false,
            email_worker_status: false,
            force_run_loading: false,
            page_title: 'Email Logs',
            page_subtitle: 'This section allows you to view the most recent emails sent by the system.',
            page_title_old: 'Email Queue & Logs',
            page_subtitle_old: 'Email Sending, Queue Management, & Logs.',
            asset_domain: this.$assetDomain,
            has_admin_access: false,
            show_sending_process_modal: false,
            cancel_sending_process: false,
            current_successful_request_num: 0,
            current_successful_request_total: 0,
            sending_progress_color: 'primary',
            sending_progress: 0,
            email_send_log_list: [],
        };
    },
    mounted() {
        this.start_date = this.dateToday();
        this.end_date = this.dateToday();
        this.checkWorkersStatus();
        this.modifyTableHeader();
    },
    methods: {
        indexCounter: function (index) {
            let ctr = index;
            if (this.page > 1) {
                ctr = index + (this.page - 1) * this.items_per_page;
            }
            return ctr;
        },
        loadForm: function () {
            this.initialPageLoad();
            this.loadEmailLog();
            this.loadSuppressionList();
        },
        async initialPageLoad() {
            this.queue_email_list = [];
            this.loading_setting_for_queue_log = true;
            let database_id = this.database_id.field_key;
            var form_data = new FormData();
            form_data.append('database_id', database_id);
            form_data.append('source_type', this.source_type);
            form_data.append('recipient_email', this.recipient_email);
            form_data.append('start_date', this.start_date);
            form_data.append('end_date', this.end_date);
            form_data.append('no_load', true);
            await this.$api.post('administration/email/fetch/queue-email-list', form_data).then((response) => {
                if (response.data.queue_email_list.length > 0)
                    this.queue_email_list = response.data.queue_email_list.map((item) => {
                        return { ...item, loading_setting_icon: false };
                    });
                this.loading_setting_for_queue_log = false;
            });
        },
        async loadEmailLog() {
            this.email_log_list = [];
            this.loading_setting = true;
            let database_id = this.database_id.field_key;
            var form_data = new FormData();
            form_data.append('database_id', database_id);
            form_data.append('source_type', this.source_type);
            form_data.append('recipient_email', this.recipient_email);
            form_data.append('start_date', this.start_date);
            form_data.append('end_date', this.end_date);
            form_data.append('include_system', this.include_system);
            form_data.append('no_load', true);
            await this.$api.post('administration/email/fetch/email-log-list', form_data).then((response) => {
                let email_log_list = response.data.email_log_list;
                if (email_log_list.length > 0)
                    this.email_log_list = email_log_list.map((item) => {
                        return { ...item, loading_setting_icon: false };
                    });
                this.loading_setting = false;
            });
        },
        async loadSuppressionList() {
            this.email_suppression_list = [];
            this.loading_setting = true;
            let database_id = this.database_id.field_key;
            var form_data = new FormData();
            form_data.append('database_id', database_id);
            form_data.append('no_load', true);
            await this.$api.post('administration/email/fetch/postmark-suppression-list', form_data).then((response) => {
                this.email_suppression_list = response.data.email_suppression_list;
                this.loading_setting = false;
            });
        },
        async checkWorkersStatus() {
            var form_data = new FormData();
            form_data.append('no_load', true);
            await this.$api.post('administration/email/fetch/workers-status', form_data).then((response) => {
                this.email_worker_status = response.data.email_worker_status;
                this.server_last_run_timestamp = response.data.worker;
                this.environment = response.data.environment;
                this.queue_storing_days = response.data.queue_storing_days;
                this.admin = response.data.admin;
                this.has_admin_access = response.data.has_admin_access;
                // setTimeout(this.checkWorkersStatus, 10000);
            });
        },
        async loadAllowedDatabaseList() {
            var form_data = new FormData();
            form_data.append('no_load', true);
            await this.$api
                .post('administration/email/fetch/load-allowed-database-list', form_data)
                .then((response) => {
                    this.database_list = response.data.allowed_db_list;
                    this.database_id = response.data.default_db;
                });
        },
        showViewEmailModal: function (type, id, email_log_id, postmark_log_id, index) {
            if (type === 'queue') {
                postmark_log_id = '';
                email_log_id = '';
                this.queue_email_list[index].loading_setting_icon = true;
            } else {
                this.email_log_list[index].loading_setting_icon = true;
            }
            var form_data = new FormData();
            form_data.append('type', type);
            form_data.append('queue_id', id);
            form_data.append('email_log_id', email_log_id);
            form_data.append('postmark_log_id', postmark_log_id);
            form_data.append('no_load', true);
            this.$api.post('administration/email/fetch/view-email-details-by-id', form_data).then((response) => {
                this.email_details_data = response.data.email_details_data;
                this.show_view_email_modal = true;
                if (type === 'queue') {
                    this.queue_email_list[index].loading_setting_icon = false;
                } else {
                    this.email_log_list[index].loading_setting_icon = false;
                }
            });
        },
        async resendEmail() {
            let id = this.email_details_data.id;
            let postmark_log_id = this.email_details_data.postmark_log_id;

            let batch_no = this.email_details_data.batch_no;
            let sending_status = 'individual';
            if (batch_no) {
                let dialog_prop = {
                    title: 'info',
                    message:
                        'How would you like us to dispatch the selected emails? Choose from one of the following options:',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Resend by batch no', value: 1, color: 'primary' },
                        { label: 'Resend email', value: 2, color: 'secondary' },
                        { label: 'cancel', value: 3 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) sending_status = 'batch';
                if (result === 3) sending_status = 'cancel';
            }
            if (sending_status !== 'cancel') {
                let dialog_prop = {
                    title: 'warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    var form_data = new FormData();
                    form_data.append('queue_id', id);
                    form_data.append('postmark_log_id', postmark_log_id);
                    form_data.append('sending_status', sending_status);
                    if (this.email_worker_status) {
                        this.$api
                            .post('administration/email/process/resend-queued-email', form_data)
                            .then((response) => {
                                this.show_view_email_modal = false;
                                this.loadForm();
                                let dialog_prop = {
                                    title: 'Success',
                                    message: 'Successfully Queued.',
                                    icon_show: true,
                                };
                                cirrusDialog(dialog_prop);
                            });
                    } else {
                        axios
                            .get('?module=administration&command=viewPostmarkEmail&action=resend&id=' + postmark_log_id)
                            .then((response) => {
                                if (response) {
                                    this.loading_page_setting = false;
                                    let dialog_prop = {
                                        title: 'Success',
                                        message: 'Successfully Sent.',
                                        icon_show: true,
                                    };
                                    cirrusDialog(dialog_prop);
                                }
                                this.loadForm();
                            });
                    }
                }
            }
        },
        dateToday: function () {
            let today = new Date();
            let dd = today.getDate();
            let mm = today.getMonth() + 1; //January is 0!
            let yyyy = today.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            today = dd + '/' + mm + '/' + yyyy;
            return today;
        },
        dateFormat: function (today) {
            let dd = today.getDate();
            let mm = today.getMonth() + 1; //January is 0!
            let yyyy = today.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            today = dd + '/' + mm + '/' + yyyy;
            return today;
        },
        getShortForm(list) {
            let hasSpace = list.includes('; ');
            let emails;
            if (hasSpace) emails = list.split('; ');
            else emails = list.split(';');

            if (emails.length - 1 > 1) return emails[0] + ' and ' + (emails.length - 2) + ' more...';
            return emails[0];
        },
        setDates(interval) {
            let start, end;
            switch (interval) {
                case 'today':
                    end = start = new Date();
                    break;

                case 'past7days':
                    end = new Date();
                    start = new Date();
                    start.setDate(start.getDate() - 7);
                    break;

                case 'thisWeek':
                    end = new Date();
                    start = new Date();
                    start.setDate(end.getDate() - end.getDay());
                    break;

                case 'thisMonth':
                    end = new Date();
                    start = new Date(end.getFullYear(), end.getMonth(), 1);
                    break;

                case 'thisYear':
                    end = new Date();
                    start = new Date(end.getFullYear(), 0, 1);
                    break;

                default:
                    end = start = new Date();
            }
            this.start_date = this.dateFormat(start);
            this.end_date = this.dateFormat(end);
        },
        downloadFile: function (file) {
            window.open('download.php?fileID=' + file, '_blank');
        },
        async runScheduler() {
            if (this.server_last_run_timestamp === 'OFF') {
                this.force_run_loading = true;
                let dialog_prop = {
                    title: 'warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    var form_data = new FormData();
                    this.$api.post('administration/email/process/force-run-worker', form_data).then((response) => {
                        this.force_run_loading = false;
                    });
                } else {
                    this.force_run_loading = false;
                }
            }
        },
        async toggleQueueSetting() {
            this.para_btn_loading = true;
            let dialog_prop = {
                title: 'warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                var form_data = new FormData();
                form_data.append('database_id', this.database_id.field_key);
                this.$api
                    .post('administration/email/process/toggle-queue-setting-by-client-id', form_data)
                    .then((response) => {
                        this.para_btn_loading = false;
                        this.database_id.queue_enable = !this.database_id.queue_enable;
                    });
            } else {
                this.para_btn_loading = false;
            }
        },
        modifyTableHeader: function () {
            const common_headers = [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'item_no', width: '70px' },
                { text: 'Recipient', value: 'recipient_email' },
                { text: 'Subject', value: 'subject' },
                { text: 'Sent', value: 'sent_datetime' },
                { text: 'Source', value: 'source' },
                { text: 'Attachment(s)', value: 'attachments_count' },
            ];

            const admin_headers = [{ text: '', value: 'database_icon', sortable: false, width: '40px' }];
            const expanded_headers = [
                { text: 'From', value: 'from_email' },
                { text: 'Original Recipient', value: 'overwritten_recipient_email' },
            ];
            const action_headers = [{ text: '', value: 'action1', align: 'end', sortable: false, width: '100px' }];

            if (this.expand_column_log) {
                if (this.has_admin_access) {
                    this.log_headers = admin_headers.concat(common_headers, expanded_headers, action_headers);
                } else {
                    this.log_headers = common_headers.concat(expanded_headers, action_headers);
                }
            } else {
                if (this.has_admin_access) {
                    this.log_headers = admin_headers.concat(common_headers, action_headers);
                } else {
                    this.log_headers = common_headers.concat(action_headers);
                }
            }
        },
        async sendAllFailedEmail() {
            this.email_send_log_list = [];
            let dialog_prop = {
                title: 'Warning: Sending All Failed Emails',
                message:
                    'Are you sure you want to resend all failed emails in the system? This action will attempt to send any previously failed emails and could result in duplicate messages being delivered. Please do not refresh while the process is running, as it may interrupt the sending. Proceed with caution.',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.cancel_sending_process = false;
                this.current_successful_request_num = 0;
                this.current_successful_request_total = this.getFailedEmailCount();
                this.show_sending_process_modal = true;
                let list_of_failed_emails = this.getListOfFailedEmails();
                for (let i = 0; i < list_of_failed_emails.length; i++) {
                    if (!this.cancel_sending_process) {
                        await this.resendFailedEmailByQueueId(list_of_failed_emails[i].id, 'individual');
                        this.email_send_log_list.push({
                            recipient: list_of_failed_emails[i].recipient_email,
                            subject: list_of_failed_emails[i].subject,
                            id: list_of_failed_emails[i].id,
                            item_no: list_of_failed_emails[i].item_no,
                        });
                    }
                }
                this.cancel_sending_process = true;
                this.initialPageLoad();
                let dialog_prop = {
                    title: 'Success',
                    message: 'Successfully Sent.',
                    icon_show: true,
                };
                cirrusDialog(dialog_prop);
            }
        },
        async sendSelectedFailedEmail() {
            this.email_send_log_list = [];
            let dialog_prop = {
                title: 'Warning: Sending All Failed Emails',
                message:
                    'Are you sure you want to resend all selected failed emails in the system? This action will attempt to send any previously failed emails and could result in duplicate messages being delivered. Please do not refresh while the process is running, as it may interrupt the sending. Proceed with caution.',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.cancel_sending_process = false;
                this.current_successful_request_num = 0;
                this.current_successful_request_total = this.getFailedSelectedEmailCount();
                this.show_sending_process_modal = true;

                let list_of_failed_emails = this.getListOfSelectedFailedEmails();
                for (let i = 0; i < list_of_failed_emails.length; i++) {
                    if (!this.cancel_sending_process) {
                        await this.resendFailedEmailByQueueId(list_of_failed_emails[i].id, 'individual');
                        this.email_send_log_list.push({
                            recipient: list_of_failed_emails[i].recipient_email,
                            subject: list_of_failed_emails[i].subject,
                            id: list_of_failed_emails[i].id,
                            item_no: list_of_failed_emails[i].item_no,
                        });
                    }
                }
                this.cancel_sending_process = true;
                this.initialPageLoad();
                let dialog_prop = {
                    title: 'Success',
                    message: 'Successfully Sent.',
                    icon_show: true,
                };
                cirrusDialog(dialog_prop);
            }
        },
        async cancelEmailSendingProcess() {
            this.cancel_sending_process = true;
            this.sending_progress_color = 'error';
        },
        getListOfFailedEmails() {
            return this.queue_email_list;
        },
        getListOfSelectedFailedEmails() {
            return this.selected_q_email_log;
        },
        getFailedEmailCount() {
            return this.getListOfFailedEmails().length;
        },
        getFailedSelectedEmailCount() {
            return this.getListOfSelectedFailedEmails().length;
        },
        async resendFailedEmailByQueueId(id, sending_type) {
            var form_data = new FormData();
            form_data.append('queue_id', id);
            form_data.append('sending_status', sending_type);
            form_data.append('no_load', true);
            form_data.append('no_notif', true);
            await this.$api.post('administration/email/process/resend-queued-email', form_data).then((response) => {
                this.current_successful_request_num++;
                this.sending_progress = Math.ceil(
                    (this.current_successful_request_num / this.current_successful_request_total) * 100,
                );
            });
        },
        async deleteEmailFromSuppression(item) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure you?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                var form_data = new FormData();
                form_data.append('database_id', this.database_id.field_key);
                form_data.append('email_address', item.email_address);
                await this.$api
                    .post('administration/email/delete/delete-email-from-suppression', form_data)
                    .then((response) => {
                        this.loadSuppressionList();
                    });
            }
        },
    },
    watch: {
        email_worker_status: function () {
            if (this.email_worker_status) {
                this.page_title = this.page_title_old;
                this.page_subtitle = this.page_subtitle_old;
            }
        },
        has_admin_access: function () {
            if (this.has_admin_access) {
                this.loadAllowedDatabaseList();
                this.modifyTableHeader();
            }
        },
        expand_column: function () {
            if (this.expand_column) {
                this.headers = [
                    { text: '', value: 'database_icon', sortable: false, width: '40px' },
                    { text: '#', value: 'index', sortable: false, width: '40px' },
                    { text: 'ID', value: 'item_no', width: '70px' },
                    { text: 'Queued At', value: 'queued_at' },
                    { text: 'Batch No', value: 'batch_no', align: 'center' },
                    { text: 'From', value: 'from_email' },
                    { text: 'Recipient', value: 'recipient_email' },
                    { text: 'Subject', value: 'subject' },
                    { text: '', value: 'action1', align: 'end', sortable: false, width: '100px' },
                ];
            } else {
                this.headers = [
                    { text: '', value: 'database_icon', sortable: false, width: '40px' },
                    { text: '#', value: 'index', sortable: false, width: '40px' },
                    { text: 'ID', value: 'item_no', width: '70px' },
                    { text: 'Queued At', value: 'queued_at' },
                    { text: 'Recipient', value: 'recipient_email' },
                    { text: 'Subject', value: 'subject' },
                    { text: '', value: 'action1', align: 'end', sortable: false, width: '100px' },
                ];
            }
        },
        expand_column_log: function () {
            this.modifyTableHeader();
        },
        database_id: function () {
            if (this.database_id === null) {
                this.database_id = { field_key: '', field_value: 'All' };
            }
        },
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>
<style lang="scss" scoped>
small {
    display: block;
}
</style>
<style>
.disabled_style {
    background-color: #f5f5f5 !important;
}
</style>
