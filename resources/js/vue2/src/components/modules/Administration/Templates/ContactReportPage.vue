<template>
    <div class="c8-page page-form">
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header title="Contact Report" />
            </v-toolbar-title>
        </v-toolbar>
        <span class="caption">This form allows you to view all contacts from all forms.</span>

        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Parameters</h6>
                <v-spacer></v-spacer>

                <v-btn
                    x-small
                    icon
                    @click="reset()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader
            type="table-tbody"
            v-if="loading_setting"
        ></cirrus-content-loader>
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Source</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        class="form-toggle"
                        v-model="source_type"
                        mandatory
                    >
                        <v-btn
                            small
                            depressed
                            text
                        >
                            All
                        </v-btn>
                        <v-btn
                            small
                            depressed
                            text
                        >
                            Property
                        </v-btn>
                        <v-btn
                            small
                            depressed
                            text
                        >
                            Lease
                        </v-btn>
                        <v-btn
                            small
                            depressed
                            text
                        >
                            Company
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="source_type === 1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Property</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        openDirection="bottom"
                        v-model="property_code"
                        :options="property_list"
                        :allowEmpty="false"
                        group-values="fieldGroupValues"
                        :groupSelect="false"
                        group-label="fieldGroupNames"
                        optionsLimit="10000"
                        :group-select="true"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        placeholder="Select a property"
                        track-by="fieldKey"
                        label="fieldValue"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="source_type === 2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Lease</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        v-model="lease_code"
                        :options="lease_list"
                        :allowEmpty="false"
                        group-values="field_group_values"
                        :groupSelect="false"
                        group-label="field_group_names"
                        optionsLimit="10000"
                        :group-select="true"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        placeholder="Select a lease"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="source_type === 3"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Company Type</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                    style="padding-top: 10px"
                >
                    <sui-checkbox
                        v-model="debtor_flag"
                        label="Debtor"
                    />
                    <sui-checkbox
                        v-model="owner_flag"
                        label="Owner"
                    />
                    <sui-checkbox
                        v-model="supplier_flag"
                        label="Supplier"
                    />
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="source_type === 3"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Company</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        openDirection="bottom"
                        v-model="company_code"
                        :options="company_list"
                        :allowEmpty="false"
                        group-values="fieldGroupValues"
                        :groupSelect="false"
                        group-label="fieldGroupNames"
                        optionsLimit="10000"
                        :group-select="true"
                        class="vue-select2 dropdown-left dropdown-400"
                        :custom-label="nameWithDash"
                        placeholder="Select a company"
                        track-by="fieldKey"
                        label="fieldValue"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Type</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <multiselect
                        openDirection="bottom"
                        v-model="contact_role"
                        :options="contact_role_list"
                        :custom-label="nameWithDash"
                        :allowEmpty="false"
                        class="vue-select2 dropdown-left dropdown-300"
                        group-label="language"
                        placeholder="Select a contact type"
                        track-by="field_key"
                        label="field_value"
                        :show-labels="false"
                        ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                    </multiselect>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Search Name</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-text-field
                        dense
                        v-model.lazy="search_contact"
                        @blur="initialPageLoad()"
                        v-on:keyup.enter="initialPageLoad()"
                        :maxlength="30"
                    ></v-text-field>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="text-right"
                >
                    <v-btn
                        @click="generateReport()"
                        color="primary"
                        depressed
                        small
                    >
                        Generate Report
                    </v-btn>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="contact_list.length > 0"
            >
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="text-right"
                >
                    <v-btn
                        @click="exportToXLSX()"
                        depressed
                        small
                    >
                        Export to Excel
                    </v-btn>
                </v-col>
            </v-row>
        </div>
        <v-data-table
            class="c8-datatable-custom"
            dense
            :item-class="itemRowBackground"
            item-key="id"
            :search="search_datatable"
            :headers="headers"
            :items="contact_list"
            :items-per-page="items_per_page"
            hide-default-footer
            v-show="!overview_loading"
        >
            <template v-slot:item.index="{ item }">
                {{ indexCounter(contact_list.indexOf(item) + 1) }}
            </template>
            <template v-slot:item.form_code="{ item }">
                {{ item.form_code }}
            </template>
            <template v-slot:item.contact_type_desc="{ item }">
                {{ replaceDesc(item.contact_type.field_key, item.contact_type.field_value) }}
            </template>
            <template v-slot:item.contact_details="{ item }">
                <table class="table-raw data-grid-no-line lease-contact-details">
                    <tr
                        v-for="(contact_details_data, index_detail) in item.contact_details"
                        :key="index_detail"
                    >
                        <td style="padding: 0px 2px !important">
                            <span class="form-input-text">{{ index_detail + 1 + '.' }} &nbsp;</span>
                        </td>
                        <td style="padding: 0px 2px !important">
                            <span class="form-input-text"
                                ><strong>{{ contact_details_data.phone_type.field_value }}: </strong></span
                            >

                            <cirrus-input
                                v-if="contact_details_data.phone_type.field_value === 'E-Mail'"
                                :inputFormat="'emailClickable'"
                                :size="'30'"
                                :id="'contact_name'"
                                v-model="contact_details_data.contact_detail"
                                :edit_form="false"
                                :error_msg="error_msg"
                            ></cirrus-input>

                            <cirrus-input
                                v-if="
                                    contact_details_data.phone_type.field_value === 'Mobile Phone' ||
                                    contact_details_data.phone_type.field_value === 'Owner Mobile' ||
                                    contact_details_data.phone_type.field_value === 'Service Mobile' ||
                                    contact_details_data.phone_type.field_value === 'Tenant Mobile'
                                "
                                :inputFormat="'phoneClickable'"
                                :size="'30'"
                                :id="'contact_name'"
                                v-model="contact_details_data.contact_detail"
                                :edit_form="false"
                                :error_msg="error_msg"
                            ></cirrus-input>

                            <cirrus-input
                                v-if="
                                    contact_details_data.phone_type.field_value !== 'Mobile Phone' &&
                                    contact_details_data.phone_type.field_value !== 'Owner Mobile' &&
                                    contact_details_data.phone_type.field_value !== 'Service Mobile' &&
                                    contact_details_data.phone_type.field_value !== 'Tenant Mobile' &&
                                    contact_details_data.phone_type.field_value !== 'E-Mail'
                                "
                                :size="'30'"
                                :id="'contact_name'"
                                v-model="contact_details_data.contact_detail"
                                :edit_form="false"
                                :error_msg="error_msg"
                            ></cirrus-input>
                        </td>
                    </tr>
                </table>
            </template>
        </v-data-table>
        <v-row
            class="form-row"
            v-if="contact_list_page_count > 0 && !overview_loading"
        >
            <v-col
                xs="12"
                sm="12"
                md="12"
            >
                <table class="c8-datatable-custom-footer">
                    <tr>
                        <td class="">Rows per page:</td>
                        <td>
                            <multiselect
                                v-model="items_per_page"
                                :options="[5, 10, 15, 25, 50, 100]"
                                :allowEmpty="false"
                                class="vue-select2 dropdown-left dropdown-200"
                                :show-labels="false"
                                ><span slot="noResult"
                                    >Oops! No elements found. Consider changing the search query.</span
                                >
                            </multiselect>
                        </td>
                        <td></td>
                        <td>
                            <v-pagination
                                v-model="page"
                                :length="Math.ceil(contact_list_page_count / items_per_page)"
                                :total-visible="10"
                            ></v-pagination>
                        </td>
                    </tr>
                </table>
            </v-col>
        </v-row>

        <cirrus-content-loader v-if="overview_loading"></cirrus-content-loader>
    </div>
</template>
<script>
import { bus } from '../../../../plugins/bus';
import global_mixins from '../../../../plugins/mixins';
import { mapActions, mapState } from 'vuex';
import moment from 'moment';
export default {
    data() {
        return {
            loading_setting: false,
            edit_form: false,
            error_server_msg: {},
            error_server_msg2: [],
            modal_current_ctr: 0,
            contact_list: [],
            property_code: { field_key: '', field_value: 'Please select...' },
            lease_code: { field_key: '', field_value: 'Please select...' },
            company_code: { field_key: '', field_value: 'Please select...' },
            financial_year: { field_key: new Date().getFullYear(), field_value: new Date().getFullYear() },
            man_prop_manager: { field_key: '', field_value: 'Please select...' },
            modified_by: { field_key: '', field_value: 'Please select...' },
            contact_role: { field_key: '', field_value: 'Please select...' },
            contact_phone_type: { field_key: '', field_value: 'Please select...' },
            property_list: [],
            lease_list: [],
            company_list: [],
            contact_role_list: [],
            contact_phone_type_list: [],
            modified_by_list: [],
            source_type: 0,
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'Source', value: 'source', width: '100px' },
                { text: 'Code', value: 'form_code', align: 'center', sortable: false },
                { text: 'Name', value: 'contact_name' },
                { text: 'Type', value: 'contact_role_desc' },
                { text: 'Details', value: 'contact_details' },
            ],
            page: 1,
            page_count: 0,
            items_per_page: 50,
            search_contact: '',
            search_datatable: '',
            contact_list_page_count: 0,
            overview_loading: false,
            debtor_flag: false,
            owner_flag: false,
            supplier_flag: false,
            overview_loading_msg: 'Please wait...',
        };
    },
    mounted() {
        // this.initialPageLoad();
        this.loadContactReportParamList();
        this.loadPropertyList();
        this.loadCompanyList();
        this.loadLeaseList();
    },
    methods: {
        indexCounter: function (index) {
            let ctr = index;
            if (this.page > 1) {
                ctr = index + (this.page - 1) * this.items_per_page;
            }
            return ctr;
        },
        itemRowBackground: function (item) {
            return item.budget_input_type === '' ? 'disabled_style' : '';
        },
        initialPageLoad: function () {
            this.overview_loading = true;
            var form_data = new FormData();
            // form_data.append('device_id', "UID00000001");
            form_data.append('source_type', this.source_type);
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('lease_code', this.lease_code.field_key);
            form_data.append('company_code', this.company_code.field_key);
            form_data.append('contact_role', this.contact_role.field_key);
            form_data.append('contact_phone_type', this.contact_phone_type.field_key);
            form_data.append('search_contact', this.search_contact);
            form_data.append('debtor_flag', this.debtor_flag);
            form_data.append('owner_flag', this.owner_flag);
            form_data.append('supplier_flag', this.supplier_flag);
            form_data.append('page', this.page);
            form_data.append('limit', this.items_per_page);
            form_data.append('no_load', true);
            this.$api.post('administration/contact-report/fetch/contact-list', form_data).then((response) => {
                this.contact_list = response.data.contact_list;
                this.contact_list_page_count = response.data.contact_list_page_count;
                this.overview_loading = false;
            });
        },
        loadContactReportParamList: function () {
            this.budgetSettingDimmer = true;
            let form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('administration/contact-report/fetch/contact-param', form_data).then((response) => {
                this.contact_role_list = response.data.contact_role_list;
                this.contact_phone_type_list = response.data.contact_phone_type_list;
            });
        },
        loadPropertyList: function () {
            this.budgetSettingDimmer = true;
            let form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('load-property-active-list-redis', form_data).then((response) => {
                this.property_list = response.data.data;
            });
        },
        loadLeaseList: function () {
            this.loading_content_setting = true;
            let form_data = new FormData();
            form_data.append('active_only', '1');
            form_data.append('ledger_option', '1');
            form_data.append('no_load', true);
            this.$api.post('load-property-lease-list', form_data).then((response) => {
                this.lease_list = response.data.group;
                let lease_list_ungroup = response.data.data;
                this.loading_content_setting = false;
                if (this.initialLeaseCode) {
                    this.lease_code = this.getValueInList(this.initialLeaseCode, lease_list_ungroup);
                }
            });
        },
        loadCompanyList: function () {
            this.budgetSettingDimmer = true;
            let form_data = new FormData();
            form_data.append('no_load', true);
            form_data.append('debtor_flag', this.debtor_flag);
            form_data.append('owner_flag', this.owner_flag);
            form_data.append('supplier_flag', this.supplier_flag);
            this.$api.post('company-group-active-list', form_data).then((response) => {
                this.company_list = response.data.data;
            });
        },
        loadManagementDetailsLists: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('property/fetch/management-details-lists', form_data).then((response) => {
                this.property_manager_list = response.data.property_manager_list;
            });
        },
        loadModifiedByList: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);
            this.$api.post('property/budget/load-modified-by-list', form_data).then((response) => {
                this.modified_by_list = response.data.modified_by_list;
            });
        },
        replaceDesc: function (key, desc) {
            if (desc === '') return key;
            return desc;
        },
        goToShortcut: function (parameter, param = '') {
            let param_split = param.split(':');
            let code1 = param_split[0].trim();
            let code2 = param_split[1] === undefined ? '' : param_split[1].trim();
            switch (parameter) {
                case 'PROPERTY':
                    if (localStorage.getItem('user_type') !== 'A') {
                        window.open(
                            '?module=properties&command=property_summary_page&property_code=' + code1,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    } else {
                        window.open(
                            '?module=properties&command=v2_manage_property_page&property_code=' + code1,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    }

                    break;
                case 'LEASE':
                    if (localStorage.getItem('user_type') === 'A') {
                        window.open(
                            '?module=leases&command=lease_page_v2&property_code=' + code1 + '&lease_code=' + code2,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    } else {
                        window.open(
                            '?module=leases&command=lease_summary_page&property_code=' + code1 + '&lease_code=' + code2,
                            '_blank', // <- This is what makes it open in a new window.
                        );
                    }
                    break;
                case 'COMPANY':
                    window.open('?module=companies&command=company_v2&companyID=' + code1);

                    break;
            }
        },
        reset: function () {
            this.source_type = 0;
            this.property_code = { field_key: '', field_value: 'Please select...' };
            this.lease_code = { field_key: '', field_value: 'Please select...' };
            this.company_code = { field_key: '', field_value: 'Please select...' };
            this.contact_role = { field_key: '', field_value: 'Please select...' };
            this.initialPageLoad();
        },
        exportToXLSX: function () {
            var form_data = new FormData();
            form_data.append('source_type', this.source_type);
            form_data.append('property_code', this.property_code.field_key);
            form_data.append('lease_code', this.lease_code.field_key);
            form_data.append('company_code', this.company_code.field_key);
            form_data.append('contact_role', this.contact_role.field_key);
            form_data.append('contact_phone_type', this.contact_phone_type.field_key);
            form_data.append('search_contact', this.search_contact);
            form_data.append('page', this.page);
            form_data.append('limit', this.items_per_page);
            form_data.append('no_load', true);
            this.overview_loading = true;
            this.overview_loading_msg = 'Generating excel file';
            this.$api.post('contact-report/process/export-contact-report', form_data).then((response) => {
                let res = response.data;
                this.overview_loading = false;
                this.overview_loading_msg = 'Please wait...';
                if (res.file && res.file.name && res.file.type && res.file.data) {
                    this.printDownload(res.file.data, res.file.name + moment().unix(), res.file.type);
                } else {
                    if (res.error) this.$noty.error(res.error);
                    else this.$noty.error('Generate report failed. Please try again.');
                }
            });
        },
        generateReport: function () {
            this.initialPageLoad();
        },
    },
    watch: {
        debtor_flag: function () {
            this.loadCompanyList();
        },
        owner_flag: function () {
            this.loadCompanyList();
        },
        supplier_flag: function () {
            this.loadCompanyList();
        },
        source_type: function () {
            this.property_code = { field_key: '', field_value: 'Please select...' };
            this.lease_code = { field_key: '', field_value: 'Please select...' };
            this.company_code = { field_key: '', field_value: 'Please select...' };
            this.contact_role = { field_key: '', field_value: 'Please select...' };
        },
        page: function () {
            this.initialPageLoad();
        },
        items_per_page: function () {
            this.initialPageLoad();
        },
    },
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>
<style lang="scss" scoped>
small {
    display: block;
}
</style>
<style>
.disabled_style {
    background-color: #f5f5f5 !important;
}
</style>
