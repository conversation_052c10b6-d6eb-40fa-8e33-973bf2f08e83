<template>
    <div class="page-form">
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <h1
            class="ui header"
            style="color: #7f8c8d; font-size: 2vw; font-weight: normal; letter-spacing: -2px"
        >
            <div class="content">Import Outgoings Adjustments</div>
        </h1>
        <strong
            >This section allows you to import outgoings, please save your import file as CSV before importing.</strong
        >
        <cirrus-server-error
            :error_msg="error_server_msg"
            :errorMsg2="error_main_form"
        ></cirrus-server-error>
        <br />
        <strong id="system-message">
            <div
                id="success_email_div"
                class="variable-outgoings-alert variable-outgoings-alert-success"
                style="display: none"
            >
                <p id="success_email_msg_div"></p>
            </div>

            <div
                id="invalid_email_div"
                class="variable-outgoings-alert variable-outgoings-alert-warning"
                style="display: none"
            >
                <p id="invalid_email_msg_div"></p>
            </div>
        </strong>

        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Property
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <div>
                        <multiselect
                            v-model="property_code"
                            :options="property_list"
                            :allowEmpty="false"
                            class="vue-select2 dropdown-left dropdown-800"
                            :custom-label="nameWithDash"
                            group-label="language"
                            placeholder="Select a property"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-chip
                            v-if="error_msg.length > 0 && error_data.id === 'property-code'"
                            v-for="(error_data, index) in error_msg"
                            :key="index"
                            outlined
                            color="error"
                        >
                            <v-icon left>error</v-icon>
                            {{ error_data.message }}
                        </v-chip>
                    </div>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Due Date
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-icon-date-picker
                        :size="'40'"
                        :id="'due-date'"
                        v-model="due_date"
                        :edit_form="edit_form"
                        :error_msg="error_msg"
                    ></cirrus-icon-date-picker>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Invoice Date
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-icon-date-picker
                        :size="'40'"
                        :id="'invoice-date'"
                        v-model="invoice_date"
                        :edit_form="edit_form"
                        :error_msg="error_msg"
                    ></cirrus-icon-date-picker>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Adjustment Account
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <div>
                        <multiselect
                            v-model="account_code"
                            :options="account_list"
                            :allowEmpty="false"
                            class="vue-select2 dropdown-left dropdown-800"
                            :custom-label="nameWithDash"
                            group-label="language"
                            placeholder="Select an account"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-chip
                            v-if="error_msg.length > 0 && error_data.id === 'account-code'"
                            v-for="(error_data, index) in error_msg"
                            :key="index"
                            outlined
                            color="error"
                        >
                            <v-icon left>error</v-icon>
                            {{ error_data.message }}
                        </v-chip>
                    </div>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Description
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        :id="'account_desc'"
                        v-model="account_desc"
                        :edit_form="edit_form"
                        :error_msg="error_msg"
                    ></cirrus-input>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Import Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="import_type"
                        mandatory
                    >
                        <v-btn small>All</v-btn>
                        <v-btn small>{{ variable_outgoings_abr_label }} Adjustment Only</v-btn>
                        <v-btn small>DR Adjustment Only</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Email Attachment Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="email_attachment_type"
                        mandatory
                    >
                        <v-btn small>Both</v-btn>
                        <v-btn small>Letter Only</v-btn>
                        <v-btn small>Schedule Only</v-btn>
                        <v-btn small>None</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="email_attachment_type === 0 || email_attachment_type === 1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Letter Template
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <div>
                        <multiselect
                            v-model="letter_template"
                            :options="letter_list"
                            :allowEmpty="false"
                            class="vue-select2 dropdown-left dropdown-800"
                            group-label="language"
                            placeholder="Select a template"
                            track-by="field_key"
                            label="field_value"
                            :show-labels="false"
                        >
                            <span slot="noResult"
                                >Oops! No elements found. Consider changing the search query.</span
                            ></multiselect
                        >
                        <v-chip
                            v-if="error_msg.length > 0 && error_data.id === 'letter-template'"
                            v-for="(error_data, index) in error_msg"
                            :key="index"
                            outlined
                            color="error"
                        >
                            <v-icon left>error</v-icon>
                            {{ error_data.message }}
                        </v-chip>
                    </div>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >{{ variable_outgoings_abr_label }} Reconciliation Excel
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <div>
                        <cirrus-single-upload-button2
                            :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                            v-model="vo_rec_excel_file"
                            :edit_form="edit_form"
                            :error_msg="error_msg"
                            accept_type="csv"
                            :size_limit="20"
                        ></cirrus-single-upload-button2>
                        <v-chip
                            v-if="error_msg.length > 0 && error_data.id === 'uploaded-file'"
                            v-for="(error_data, index) in error_msg"
                            :key="index"
                            outlined
                            color="error"
                        >
                            <v-icon left>error</v-icon>
                            {{ error_data.message }}
                        </v-chip>
                    </div>
                </v-col>
            </v-row>
            <div class="form-row">
                <br />
                <v-btn
                    tile
                    color="primary"
                    absolute
                    right
                    @click="generateAdjustments()"
                    >Generate Adjustments
                </v-btn>
                <br />
                <br />
                <br />
                <sui-table
                    class="vue-data-grid"
                    compact
                    stackable
                    width="100%"
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    v-if="imported_data.length > 0"
                >
                    <sui-table-header>
                        <sui-table-row class="fieldDescription">
                            <sui-table-header-cell style="width: 5px !important">#</sui-table-header-cell>
                            <sui-table-header-cell>Lease</sui-table-header-cell>
                            <sui-table-header-cell></sui-table-header-cell>
                            <sui-table-header-cell text-align="center">From Date</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">To Date</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">Net</sui-table-header-cell>
                            <sui-table-header-cell text-align="center">{{ tax_label }}</sui-table-header-cell>
                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                            <sui-table-header-cell text-align="center"></sui-table-header-cell>
                        </sui-table-row>
                    </sui-table-header>
                    <sui-table-body>
                        <sui-table-row
                            v-for="(imported_data_data, imported_data_index) in imported_data"
                            :key="imported_data_index"
                        >
                            <sui-table-cell style="width: 5px !important">
                                {{ imported_data_index + 1 }}
                            </sui-table-cell>
                            <sui-table-cell text-align="center">
                                <multiselect
                                    v-model="imported_data_data.lease_code_value"
                                    :options="lease_list"
                                    :allowEmpty="false"
                                    class="vue-select2 dropdown-left auto"
                                    :custom-label="nameWithDash"
                                    group-label="language"
                                    placeholder="Select a lease"
                                    track-by="fieldKey"
                                    label="fieldValue"
                                    :show-labels="false"
                                >
                                    <span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    >
                                </multiselect>
                            </sui-table-cell>
                            <sui-table-cell style="width: 5px !important">
                                <v-icon
                                    color="red"
                                    small
                                    v-if="imported_data_data.lease_code_value.field_key === ''"
                                    >warning
                                </v-icon>
                            </sui-table-cell>
                            <sui-table-cell
                                text-align="center"
                                style="width: 160px !important"
                            >
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    :id="'imported_data_data.from_date'"
                                    v-model="imported_data_data.from_date"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                            </sui-table-cell>
                            <sui-table-cell
                                text-align="center"
                                style="width: 160px !important"
                            >
                                <cirrus-icon-date-picker
                                    :size="'40'"
                                    :id="'imported_data_data.to_date'"
                                    v-model="imported_data_data.to_date"
                                    :edit_form="edit_form"
                                    :error_msg="error_msg"
                                ></cirrus-icon-date-picker>
                            </sui-table-cell>
                            <sui-table-cell
                                text-align="center"
                                style="width: 110px !important"
                            >
                                <cirrus-input
                                    inputFormat="dollar"
                                    width="1"
                                    :size="'42'"
                                    v-model="imported_data_data.net_amount"
                                    :id="'imported_data_data.net_amount'"
                                    :edit_form="false"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </sui-table-cell>
                            <sui-table-cell
                                text-align="center"
                                style="width: 110px !important"
                            >
                                <cirrus-input
                                    inputFormat="dollar"
                                    :size="'42'"
                                    v-model="imported_data_data.gst_amount"
                                    :id="'imported_data_data.net_amount'"
                                    :edit_form="false"
                                    :error_msg="error_msg"
                                ></cirrus-input>
                            </sui-table-cell>
                            <sui-table-cell
                                text-align="center"
                                style="width: 300px !important"
                            >
                                <v-btn
                                    tile
                                    x-small
                                    v-if="email_attachment_type !== 3"
                                    @click="downloadLetterPDF(imported_data_index)"
                                >
                                    Download
                                </v-btn>
                            </sui-table-cell>
                            <sui-table-cell
                                text-align="center"
                                style="width: 300px !important"
                            >
                                <cirrus-multiple-upload-button
                                    :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                    v-model="imported_data_data.additional_attachments"
                                    :edit_form="edit_form"
                                    accept_type="pdf"
                                    :size="'5242880'"
                                    :error_msg="error_msg"
                                ></cirrus-multiple-upload-button>
                            </sui-table-cell>
                            <sui-table-cell
                                text-align="center"
                                style="text-align: right; width: 15px !important"
                            >
                                <v-icon
                                    color="red"
                                    @click="deleteGeneratedAdjustement(imported_data_index)"
                                    >close
                                </v-icon>
                            </sui-table-cell>
                        </sui-table-row>
                    </sui-table-body>
                </sui-table>

                <sui-table
                    border="1"
                    size="small"
                    compact
                    v-if="imported_data.length > 0"
                >
                    <sui-table-body>
                        <sui-table-row>
                            <sui-table-cell
                                colspan="8"
                                vertical-align="middle"
                                text-align="right"
                                style="text-align: right; vertical-align: middle; width: 90%"
                            >
                                <sui-checkbox
                                    v-model="portfolio_manager_cc_flag"
                                    :label="'CC to ' + portfolio_manager"
                                />
                            </sui-table-cell>
                            <sui-table-cell>
                                <v-btn
                                    tile
                                    color="primary"
                                    class="right"
                                    @click="processVariableOutgoings()"
                                    >Process Interim Invoices & Email
                                </v-btn>
                            </sui-table-cell>
                        </sui-table-row>
                    </sui-table-body>
                </sui-table>
                <div style="display: none">
                    <v-chip
                        @click="downloadGeneratedPDFInvoice(download_link_arr_data)"
                        color="primary"
                        v-for="(download_link_arr_data, download_link_arr_index) in download_link_arr"
                        :key="download_link_arr_index"
                    >
                        <img
                            :src="asset_domain + 'assets/images/icons/pdf.png'"
                            alt="Adobe Logo"
                            class="icon"
                        />
                        &nbsp
                        {{ download_link_arr_data.leaseID }} Invoice PDF
                    </v-chip>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios';
import { cirrusDialog } from '../../../../plugins/mixins';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {},
    data() {
        return {
            variable_outgoings_abr_label: 'VO',
            portfolio_manager: 'Portfolio Manager',
            asset_domain: this.$assetDomain,
            property_code: { field_key: '' },
            error_main_form: [],
            error_server_msg: {},
            property_list: [],
            lease_list: [],
            read_only: this.initialReadOnly,
            windowSize: {
                x: 0,
                y: 0,
            },
            responsiveShow: true,
            loadingSetting: false,
            invoice_date: null,
            due_date: null,
            edit_form: true,
            error_msg: [],
            account_code: { label: '', value: '', fieldKey: '', fieldValue: '', field_key: '', field_value: '' },
            account_desc: '',
            account_code_parameter: '',
            account_list: [],
            loading_page_setting: false,
            vo_rec_excel_file: '',
            letter_list: [],
            letter_template: { label: '', value: '', fieldKey: '', fieldValue: '', field_key: '', field_value: '' },
            imported_data: [],
            imported_group_data: [],
            imported_dr_data: [],
            portfolio_manager_cc_flag: false,
            inlude_letter_to_email: true,
            download_link_arr: [],
            import_type: 0,
            email_attachment_type: 0,
            tax_label: 'GST',
        };
    },
    computed: {},
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadInitialParameters();
        this.loadCountryDefaults();
    },
    methods: {
        loadCountryDefaults: function () {
            var form_data = new FormData();
            form_data.append('no_load', true);

            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.variable_outgoings_abr_label = this.country_defaults.vo.toUpperCase();
                this.portfolio_manager = this.ucwords(this.country_defaults.portfolio_manager);
            });
        },
        dateToday: function () {
            let today = new Date();
            let dd = today.getDate();
            let mm = today.getMonth() + 1; //January is 0!
            let yyyy = today.getFullYear();
            if (dd < 10) {
                dd = '0' + dd;
            }
            if (mm < 10) {
                mm = '0' + mm;
            }
            today = dd + '/' + mm + '/' + yyyy;
            return new Date().toISOString().substr(0, 10);
        },
        nameWithDash({ field_key, field_value }) {
            if (!field_value) {
                return 'Please select...';
            }
            return `${field_key} — ${field_value}`;
        },
        loadInitialParameters: function () {
            this.loading_page_setting = true;
            var form_data = new FormData();
            form_data.append('parameter_type', 'VOADJ');
            form_data.append('parameter_code', 'ACCTCODE');

            this.$api.post('ar/variable-outgoings/load-initial-parameters', form_data).then((response) => {
                this.property_list = response.data.property_list;
                this.account_list = response.data.account_list;
                this.account_code_parameter = response.data.parameter_description;
                this.letter_list = response.data.letter_list;
                let account_code_parameter = this.account_code_parameter;
                let filter_arr = this.account_list.filter((m) => m.field_key === account_code_parameter);
                if (filter_arr.length > 0) {
                    this.account_code = filter_arr[0];
                }
                this.tax_label = response.data.tax_label;
                this.loading_page_setting = false;
            });
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadInsurance_' + id;
        },
        generateAdjustments: function () {
            $('#preloader_dl_div').fadeOut('slow');
            $('#dl_link_div').hide();
            $('#invalid_email_div').hide();
            $('#success_email_div').hide();
            this.error_main_form = [];

            let property_code = this.property_code.field_key;
            let uploaded_file = null;
            if (this.vo_rec_excel_file !== null) {
                uploaded_file = this.vo_rec_excel_file[0];
            }
            let account_code = this.account_code.field_key;
            let account_desc = this.account_desc;
            let letter_template = this.letter_template.field_key;
            let invoice_date = this.invoice_date;
            let due_date = this.due_date;
            this.error_msg = [];
            if (property_code === '') {
                this.error_msg.push({ id: 'property-code', message: 'You have not chosen a valid property.' });
            }
            if (uploaded_file === undefined || uploaded_file === null) {
                this.error_msg.push({ id: 'uploaded-file', message: 'You have not chosen a valid file.' });
            }
            if (account_code === '') {
                this.error_msg.push({ id: 'account-code', message: 'You have not chosen a valid account.' });
            }
            if (account_desc === '') {
                this.error_msg.push({
                    id: 'account-desc',
                    message: 'You have not entered a valid account description.',
                });
            }

            if (this.email_attachment_type === 0 || this.email_attachment_type === 1) {
                if (letter_template === '') {
                    this.error_msg.push({
                        id: 'letter-template',
                        message: 'You have not chosen a valid letter template.',
                    });
                }
            }

            if (invoice_date === '' || invoice_date === null) {
                this.error_msg.push({ id: 'invoice-date', message: 'You have not chosen a valid invoice date.' });
            }
            if (due_date === '' || due_date === null) {
                this.error_msg.push({ id: 'due-date', message: 'You have not chosen a valid due date.' });
            }

            if (invoice_date !== '' && invoice_date !== null && due_date !== '' && due_date !== null) {
                let split_inv_date = invoice_date.split('/');
                let split_due_date = due_date.split('/');

                let date1 = new Date(split_inv_date[1] + '/' + split_inv_date[0] + '/' + split_inv_date[2]).getTime(),
                    date2 = new Date(split_due_date[1] + '/' + split_due_date[0] + '/' + split_due_date[2]).getTime();

                if (date2 < date1) {
                    this.error_msg.push({ id: 'due-date', message: 'Due date must be on or after Invoice date.' });
                }
            }

            if (this.error_msg.length === 0) {
                this.loading_page_setting = true;
                var formData = new FormData();
                formData.append('property_code', property_code);
                formData.append('parameter_type', 'VOADJ');
                formData.append('parameter_code', 'ACCTCODE');
                formData.append('vo_rec_excel_file', uploaded_file);
                formData.append('import_type', this.import_type);

                this.$api
                    .post('with-file-upload/ar/variable-outgoings/generate-variable-outgoings', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.loading_page_setting = false;
                        if (response.data.error) return;
                        this.lease_list = response.data.lease_list;
                        this.imported_data = response.data.imported_data;
                        this.imported_group_data = response.data.imported_group_data;
                        this.imported_dr_data = response.data.imported_dr_data;
                        this.error_main_form = response.data.error_msg;
                    });
            }
        },
        async processVariableOutgoings() {
            $('#preloader_dl_div').fadeOut('slow');
            $('#dl_link_div').hide();
            $('#invalid_email_div').hide();
            $('#success_email_div').hide();
            this.error_main_form = [];
            let property_code = this.property_code.field_key;
            let uploaded_file = null;
            if (this.vo_rec_excel_file !== null) {
                uploaded_file = this.vo_rec_excel_file[0];
            }
            let account_code = this.account_code.field_key;
            let account_desc = this.account_desc;
            let letter_template = this.letter_template.field_key;
            let invoice_date = this.invoice_date;
            let due_date = this.due_date;
            this.error_msg = [];
            if (property_code === '') {
                this.error_msg.push({ id: 'property-code', message: 'You have not chosen a valid property.' });
            }
            if (uploaded_file === undefined || uploaded_file === null) {
                this.error_msg.push({ id: 'uploaded-file', message: 'You have not chosen a valid file.' });
            }
            if (account_code === '') {
                this.error_msg.push({ id: 'account-code', message: 'You have not chosen a valid account.' });
            }
            if (account_desc === '') {
                this.error_msg.push({
                    id: 'account-desc',
                    message: 'You have not entered a valid account description.',
                });
            }
            if (this.email_attachment_type === 0 || this.email_attachment_type === 1) {
                if (letter_template === '') {
                    this.error_msg.push({
                        id: 'letter-template',
                        message: 'You have not chosen a valid letter template.',
                    });
                }
            }

            if (invoice_date === '' || invoice_date === null) {
                this.error_msg.push({ id: 'invoice-date', message: 'You have not chosen a valid invoice date.' });
            }
            if (due_date === '' || due_date === null) {
                this.error_msg.push({ id: 'due-date', message: 'You have not chosen a valid due date.' });
            }

            if (invoice_date !== '' && invoice_date !== null && due_date !== '' && due_date !== null) {
                let split_inv_date = invoice_date.split('/');
                let split_due_date = due_date.split('/');

                let date1 = new Date(split_inv_date[1] + '/' + split_inv_date[0] + '/' + split_inv_date[2]).getTime(),
                    date2 = new Date(split_due_date[1] + '/' + split_due_date[0] + '/' + split_due_date[2]).getTime();

                if (date2 < date1) {
                    this.error_msg.push({ id: 'due-date', message: 'Due date must be on or after Invoice date.' });
                }
            }

            if (this.error_msg.length === 0) {
                let dialog_prop = {
                    title: 'Warning',
                    message: 'Are you sure?',
                    icon_show: true,
                    buttons_right: [
                        { label: 'Yes', value: 1, color: 'primary' },
                        { label: 'No', value: 2 },
                    ],
                };
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.loading_page_setting = true;
                    var formData = new FormData();
                    formData.append('property_code', this.property_code.field_key);
                    formData.append('account_code', this.account_code.field_key);
                    formData.append('account_desc', this.account_desc);
                    formData.append('letter_template', this.letter_template.field_key);
                    formData.append('invoice_date', this.invoice_date);
                    formData.append('due_date', this.due_date);
                    formData.append('import_type', this.import_type);

                    formData.append('portfolio_manager_cc_flag', this.portfolio_manager_cc_flag);
                    formData.append('email_attachment_type', this.email_attachment_type);
                    formData.append('imported_data', JSON.stringify(this.imported_data));
                    formData.append('imported_group_data', JSON.stringify(this.imported_group_data));
                    formData.append('imported_dr_data', JSON.stringify(this.imported_dr_data));

                    for (let loop_x = 0; loop_x <= this.imported_data.length - 1; loop_x++) {
                        let lc_property_code = this.imported_data[loop_x].property_code;
                        let lc_lease_code = this.imported_data[loop_x].lease_code;
                        let counter = 1;
                        for (
                            let loop_y = 0;
                            loop_y <= this.imported_data[loop_x].additional_attachments.length - 1;
                            loop_y++
                        ) {
                            let form_data_key =
                                lc_property_code + '_' + lc_lease_code + '_additional_attachments_' + counter;
                            formData.append(form_data_key, this.imported_data[loop_x].additional_attachments[loop_y]);
                            counter++;
                        }
                    }

                    this.$api
                        .post('with-file-upload/ar/variable-outgoings/process-variable-outgoings', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data',
                            },
                        })
                        .then((response) => {
                            this.loading_page_setting = false;
                            if (response.data.error) return;
                            let document_ids = response.data.document_ids;
                            let batch_number = response.data.batch_number;
                            this.error_main_form = response.data.validation_errors;
                            let status = response.data.status;
                            let send_cc = response.data.send_cc;
                            if (status !== 'error') {
                                if (send_cc === 'true') {
                                    send_cc = 1;
                                } else {
                                    send_cc = 0;
                                }
                                var formData = new FormData();
                                formData.append('batchNumber', batch_number);
                                formData.append('sendEmail', '1');
                                formData.append('sendCc', send_cc);
                                formData.append('documentAPTag', '0');
                                formData.append('documentIDs', document_ids);
                                formData.append('energytec', '1');
                                let url = '?action=generateInterimInvoice&module=ar&command=generateTaxInvoice';
                                axios.post(url, formData).then((response) => {
                                    let object = response.data;
                                    if (typeof object === 'string') {
                                        $('#invalid_email_div').show();
                                        document.getElementById('invalid_email_msg_div').innerHTML =
                                            'Nothing was processed';
                                    } else {
                                        let str_txt = '';
                                        let ii;
                                        this.download_link_arr = object[0];
                                        this.imported_data = [];
                                        this.imported_group_data = [];
                                        this.imported_dr_data = [];
                                        (this.property_code = {
                                            label: '',
                                            value: '',
                                            fieldKey: '',
                                            fieldValue: '',
                                            field_key: '',
                                            field_value: '',
                                        }),
                                            // this.account_code = {label: '', value: '',fieldKey:'',fieldValue:'',field_key:'',field_value:''},
                                            (this.import_type = 0);
                                        this.vo_rec_excel_file = null;
                                        let obj = response.data;
                                        let i;
                                        for (i = 0; i < obj[0].length; i++) {
                                            str_txt +=
                                                '<a href="download.php?fileID=' +
                                                obj[0][i].fileID +
                                                '"><img src="' +
                                                this.asset_domain +
                                                'assets/images/icons/pdf.png" alt="Adobe Logo" class="icon" />     ' +
                                                obj[0][i].propertyID +
                                                ' - ' +
                                                obj[0][i].leaseID +
                                                '</a><br>';
                                        }

                                        //For email msg
                                        $('#preloader_dl_div').fadeOut('slow');
                                        $('#dl_link_div').hide();

                                        if (obj[1]) {
                                            $('#invalid_email_div').show();
                                        }
                                        if (obj[2]) {
                                            $('#success_email_div').show();
                                        }

                                        document.getElementById('invalid_email_msg_div').innerHTML = obj[1];
                                        document.getElementById('success_email_msg_div').innerHTML = obj[2];
                                    }
                                    this.loading_page_setting = false;
                                    location.href = '#system-message';
                                });
                            } else {
                                this.loading_page_setting = false;
                            }
                        });
                }
            }
        },
        downloadGeneratedPDFInvoice: function (download_link_arr_data) {
            window.location = 'download.php?fileID=' + download_link_arr_data.fileID;
        },
        async deleteGeneratedAdjustement(index) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.imported_data.splice(index, 1);
            }
        },
        downloadLetterPDF: function (index) {
            this.loading_page_setting = true;
            var formData = new FormData();

            formData.append('property_code', this.property_code.field_key);
            formData.append('property_name', this.property_code.field_value);
            formData.append('account_code', this.account_code.field_key);
            formData.append('account_desc', this.account_desc);
            formData.append('letter_template', this.letter_template.field_key);
            formData.append('invoice_date', this.invoice_date);
            formData.append('due_date', this.due_date);
            formData.append('import_type', this.import_type);
            formData.append('email_attachment_type', this.email_attachment_type);

            formData.append('portfolio_manager_cc_flag', this.portfolio_manager_cc_flag);
            formData.append('imported_data', JSON.stringify(this.imported_data[index]));
            formData.append('imported_group_data', JSON.stringify(this.imported_group_data));
            formData.append('imported_dr_data', JSON.stringify(this.imported_dr_data));

            this.$api.post('ar/variable-outgoings/download-vo-letter', formData).then((response) => {
                let file_base_64 = response.data.file.base64;
                let file_name = response.data.file.name;
                this.printDownload(file_base_64, file_name, 'pdf');
                this.loading_page_setting = false;
            });
        },
        printDownload(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;

            let blob = new Blob([this.printS2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            a.download = name + '.' + format;
            a.click();
        },
        printS2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
    },
    watch: {
        account_code: function () {
            this.account_desc = this.account_code.field_value;
        },
    },
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>
<style lang="scss" scoped>
small {
    display: block;
}

.variable-outgoings-alert {
    padding-left: 10px;
    /*margin-bottom: 20px;*/
    margin-top: 10px;
    margin-bottom: 10px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.variable-outgoings-alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
    padding: 12px;
}

.variable-outgoings-alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
    padding: 12px;
}

.variable-outgoings-alert-primary {
    color: #004085;
    background-color: #cce5ff;
    border-color: #b8daff;
    padding: 12px;
}
</style>
