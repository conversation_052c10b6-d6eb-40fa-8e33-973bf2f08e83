<template>
    <div class="c8-page lease-forms">
        <div class="page-form">
            <div class="form-row">
                <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
                <v-tabs
                    class="cirrus-tab-theme"
                    icons-and-text
                    v-model="template_tab"
                    show-arrows
                >
                    <v-tabs-slider color="white"></v-tabs-slider>

                    <v-tab href="#tab-2"
                        ><div style="margin-bottom: 5px">
                            <span
                                style="font-size: 15px"
                                class=" "
                            ></span
                            >&nbsp;<span
                                class=""
                                style="vertical-align: text-bottom; line-height: 10px"
                                >Category</span
                            >
                        </div></v-tab
                    >

                    <v-tab href="#tab-3"
                        ><div style="margin-bottom: 1px">
                            <span
                                style="font-size: 15px"
                                class=" "
                            ></span>
                            <span class="">Sub-Category</span>
                        </div></v-tab
                    >

                    <v-tab href="#tab-5"
                        ><div style="margin-bottom: 1px">
                            <span
                                style="font-size: 15px"
                                class=""
                            ></span>
                            <span class="">Fine Category</span>
                        </div></v-tab
                    >

                    <v-tab-item :value="'tab-2'">
                        <br />
                        <category-list-component
                            :currentList="currentList"
                            :currentYear="currentYear"
                            @eventname="updateTabs"
                        ></category-list-component>
                    </v-tab-item>

                    <v-tab-item :value="'tab-3'">
                        <br />
                        <subCategory-list-component
                            :currentList="currentList"
                            :currentYear="currentYear"
                        ></subCategory-list-component>
                    </v-tab-item>

                    <v-tab-item :value="'tab-5'">
                        <br />
                        <fine-list-component
                            :currentList="currentList"
                            :currentYear="currentYear"
                        ></fine-list-component>
                    </v-tab-item>
                </v-tabs>
            </div>
        </div>
    </div>
</template>
<script>
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex';
import axios from 'axios';
axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

import CategoryList from '../Forms/CategoryList.vue';
import SubCategoryList from '../Forms/SubCategoryList.vue';
import FineCategoryList from '../Forms/FineCategoryList.vue';
import { bus } from '../../../../plugins/bus';
const Swal = require('sweetalert2');
import Vue from 'vue';
Vue.component('task-activity-log-component', require('../../TaskManagement/forms/TaskActivityLog.vue').default);
Vue.component('task-update-component', require('../../TaskManagement/forms/TaskUpdateForm.vue').default);
Vue.component('task-comment-component', require('../../TaskManagement/forms/TaskCommentForm.vue').default);

export default {
    props: {
        initialCurrentYear: String,
        //  initialCurrentYearList: [],
    },
    components: {
        'category-list-component': CategoryList,
        'subCategory-list-component': SubCategoryList,
        'fine-list-component': FineCategoryList,
    },
    data() {
        return {
            currentYear: this.initialCurrentYear,
            currentListYear: [],
            currentList: [],
        };
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url', 'clientTimezone']),
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        //alert(this.currentListYear);
        // //console.log(this.currentListYear);
        //             for(let x=10; x>0; x--){
        //                 this.currentList.push({fieldKey: this.currentYear - x ,fieldValue: this.currentYear - x }) ;
        //             }
        //             for(let x=0; x<10; x++){
        //                 this.currentList.push({fieldKey: parseInt(this.currentYear) + x ,fieldValue: parseInt(this.currentYear) + x }) ;
        //             }

        this.loadYearList();
    },
    methods: {
        loadYearList: function () {
            let form_data = new FormData();
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailCalendar', form_data).then((response) => {
                this.currentList = response.data.data;
            });
        },
    },
    watch: {
        template_tab: function (value) {
            switch (value) {
                case 'tab-1':
                    break;
                case 'tab-2':
                    break;
                case 'tab-3':
                    break;
                case 'tab-4':
                    break;
            }
        },
    },
    created() {},
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>

<style lang="scss" scoped>
small {
    display: block;
}
</style>
<style type="text/css">
/*.section-toolbar .dashboard-diary-search .v-card__actions .v-input:not(.v-textarea)  input{*/
.section-toolbar .v-card__actions .dashboard-diary-search .v-input:not(.v-textarea) input {
    color: #000000 !important;
    caret-color: #3489a1 !important;
}
.v-data-footer__select {
    display: none;
}
</style>
