<template>
    <div class="c8-page">
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <div
            class="page-form"
            style="min-height: 350px"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="1"
                    md="1"
                    class="form-label required"
                    >Category:</v-col
                >
                <v-col
                    xs="12"
                    sm="11"
                    md="11"
                    class="form-input"
                >
                    <multiselect
                        v-model="category_code"
                        :options="category_list"
                        :allowEmpty="false"
                        class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a category"
                        track-by="fieldKey"
                        label="fieldValue"
                        :show-labels="false"
                        ><span slot="noResult"
                            >Oops! No elements found. Consider changing the search query.</span
                        ></multiselect
                    >
                </v-col>
            </v-row>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="1"
                    md="1"
                    class="form-label required"
                    >Sub Category:</v-col
                >
                <v-col
                    xs="12"
                    sm="11"
                    md="11"
                    class="form-input"
                >
                    <multiselect
                        v-model="subCategory_code"
                        :options="subCategory_list"
                        :allowEmpty="false"
                        class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a sub category"
                        track-by="fieldKey"
                        label="fieldValue"
                        :show-labels="false"
                        ><span slot="noResult"
                            >Oops! No elements found. Consider changing the search query.</span
                        ></multiselect
                    >
                </v-col>
            </v-row>

            <v-card
                class="section-toolbar"
                dark
                text
                tile
                :class="pending_item ? 'edited-form' : 'titleHeader'"
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">FINE CATEGORIES</h6>
                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        title="Add"
                        icon
                        @click="
                            addCategory();
                            pending_item = 1;
                        "
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        title="Save"
                        icon
                        @click="saveCategory()"
                    >
                        <v-icon>done</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <v-data-table
                class="c8-datatable-custom documents-table"
                v-show="fineCategory_list.length > 0"
                dense
                item-key="id"
                :headers="headers_category"
                :items="fineCategory_list"
                :items-per-page="5000"
                hide-default-footer
                :total-visible="5000"
            >
                <template v-slot:item.fieldValue="{ item }">
                    <input
                        v-model="item.fieldValue"
                        @keypress="pending_item = 1"
                        placeholder="Fine Category Name"
                    />
                </template>

                <template v-slot:item.benchMark="{ item }">
                    <a
                        v-if="item.fieldKey"
                        @click="
                            year_code = { fieldKey: currentYear, fieldValue: currentYear };
                            show_benchmark_modal = true;
                            fineCategory_code = item.fieldKey;
                            fineCategory_name = item.fieldValue;
                            loadFineCategoryBenchmark();
                        "
                        href="#"
                        ><v-icon color="blue darken-2">list</v-icon></a
                    >
                </template>

                <template v-slot:item.action1="{ item }">
                    <v-icon
                        color="green"
                        class="rotate90 option"
                        @click="setFirst('updateSeqFirst', fineCategory_list.indexOf(item))"
                        >fast_rewind
                    </v-icon>
                    <v-icon
                        color="green"
                        class="rotate90 option"
                        @click="setOrder('updateSeqUp', fineCategory_list.indexOf(item))"
                        >arrow_left
                    </v-icon>
                    <v-icon
                        color="green"
                        class="rotate90 option"
                        @click="setOrder('updateSeqDown', fineCategory_list.indexOf(item))"
                        >arrow_right
                    </v-icon>
                    <v-icon
                        color="green"
                        class="rotate90 option"
                        @click="setLast('updateSeqLast', fineCategory_list.indexOf(item))"
                        >fast_forward
                    </v-icon>
                    <v-icon
                        color="red"
                        class="option"
                        @click="
                            fineCategory_from_transfer = item.fieldValue;
                            deleteDatatableRow(fineCategory_list.indexOf(item));
                        "
                        >close
                    </v-icon>
                </template>
            </v-data-table>
        </div>

        <v-dialog
            v-model="show_benchmark_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card height="60vh">
                <v-card-title class="headline">
                    Benchmark
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_benchmark_modal = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Fine Category:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="fineCategory_name"
                                    :edit_form="false"
                                ></cirrus-input>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Year:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="year_code"
                                    :options="yearList"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Select a year"
                                    track-by="fieldKey"
                                    label="fieldValue"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Occupancy Cost %:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <input
                                    v-model="occCost"
                                    @keypress="amountOnly($event)"
                                    :maxlength="10"
                                />
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label Z"
                            >
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <v-btn
                                    color="success"
                                    small
                                    tile
                                    @click="saveFineCategoryBenchmark()"
                                >
                                    Save
                                </v-btn>

                                <v-btn
                                    color="primary"
                                    small
                                    tile
                                    @click="show_benchmark_modal = false"
                                >
                                    Close
                                </v-btn>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_fine_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card height="60vh">
                <v-card-title class="headline">
                    Transfer Leases to selected Fine Category before deleting
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_fine_modal = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                            ></v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                Transfer all Leases with Fine Category {{ fineCategory_from_transfer }} to
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Category:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="category_code_to_transfer"
                                    :options="category_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Select a category"
                                    track-by="fieldKey"
                                    label="fieldValue"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Sub Category:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="subCategory_code_to_transfer"
                                    :options="subCategory_list_to_transfer"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Select a sub category"
                                    track-by="fieldKey"
                                    label="fieldValue"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Fine Category:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="fineCategory_to_transfer"
                                    :options="allFineCategory_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Select a fine category"
                                    track-by="fieldKey"
                                    label="fieldValue"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label Z"
                            >
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <v-btn
                                    color="success"
                                    small
                                    tile
                                    @click="deleteAndTransferDatatableRow()"
                                >
                                    Transfer and Delete
                                </v-btn>

                                <v-btn
                                    color="primary"
                                    small
                                    tile
                                    @click="show_fine_modal = false"
                                >
                                    Cancel
                                </v-btn>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
const Swal = require('sweetalert2');
export default {
    props: {
        currentList: Object,
        currentYear: String,
    },
    data() {
        return {
            currentYear: this.currentYear,
            loading_page_setting: false,
            pending_item: 0,
            datatable_row_id: 0,
            category_code_to_transfer: { fieldKey: '', fieldValue: 'Please select...' },
            subCategory_code_to_transfer: { fieldKey: '', fieldValue: 'Please select...' },
            category_list_to_transfer: [],
            subCategory_list_to_transfer: [],
            fineCategory_to_transfer: { fieldKey: '', fieldValue: 'Please select...' },
            fineCategory_from_transfer: '',
            show_fine_modal: false,
            fineCategory_code: '',
            fineCategory_name: '',
            show_benchmark_modal: false,
            yearList: this.currentList,
            year_code: { fieldKey: this.currentYear, fieldValue: this.currentYear },

            fineCategory_list: [],
            allFineCategory_list: [],
            allCopyFineCategory_list: [],
            category_code: { fieldKey: '', fieldValue: 'Please select...' },
            category_list: [],
            subCategory_code: { fieldKey: '', fieldValue: 'Please select...' },
            subCategory_list: [],

            headers_category: [
                { text: 'Name', value: 'fieldValue', sortable: true, align: 'left' },
                { text: 'Benchmark', value: 'benchMark', sortable: true, align: 'left' },
                { text: 'Action', value: 'action1', align: 'end', sortable: false, align: 'center' },
            ],
            occCost: '',
            formData: {
                user_type: localStorage.getItem('user_type'),
            },
        };
    },
    mounted() {
        this.loadCategoryList();
        //  this.loadallFineCategoryList();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    created() {
        bus.$on('FineListloadCategoryList', () => {
            this.category_code = { fieldKey: '', fieldValue: 'Please select...' };
            this.loadCategoryList();
        });

        bus.$on('FineListloadSubCategoryList', () => {
            this.subCategory_code = { fieldKey: '', fieldValue: 'Please select...' };
            this.loadSubCategoryList();
        });
    },
    methods: {
        setOrder: function (action, index) {
            let OldIndex = 0;
            let NewIndex = 0;

            if (action == 'updateSeqDown') {
                if (this.fineCategory_list.length == index + 1) return false;

                OldIndex = index;
                NewIndex = index + 1;
            } else if (action == 'updateSeqUp') {
                if (index == 0) return false;

                OldIndex = index;
                NewIndex = index - 1;
            }

            this.pending_item = 1;

            let fieldKey = this.fineCategory_list[NewIndex].fieldKey;
            let fieldValue = this.fineCategory_list[NewIndex].fieldValue;
            let totalInUse = this.fineCategory_list[NewIndex].totalInUse;

            this.fineCategory_list[NewIndex].fieldKey = this.fineCategory_list[OldIndex].fieldKey;
            this.fineCategory_list[NewIndex].fieldValue = this.fineCategory_list[OldIndex].fieldValue;
            this.fineCategory_list[NewIndex].totalInUse = this.fineCategory_list[OldIndex].totalInUse;

            this.fineCategory_list[OldIndex].fieldKey = fieldKey;
            this.fineCategory_list[OldIndex].fieldValue = fieldValue;
            this.fineCategory_list[OldIndex].totalInUse = totalInUse;
        },
        setLast: function (action, index) {
            if (this.fineCategory_list.length == index + 1) return false;

            this.pending_item = 1;

            let lastIndex = this.fineCategory_list.length - 1;

            let fieldKey = this.fineCategory_list[index].fieldKey;
            let fieldValue = this.fineCategory_list[index].fieldValue;
            let totalInUse = this.fineCategory_list[index].totalInUse;

            for (let x = 0; x < this.fineCategory_list.length; x++) {
                if (x >= index && x != lastIndex) {
                    this.fineCategory_list[x].fieldKey = this.fineCategory_list[x + 1].fieldKey;
                    this.fineCategory_list[x].fieldValue = this.fineCategory_list[x + 1].fieldValue;
                    this.fineCategory_list[x].totalInUse = this.fineCategory_list[x + 1].totalInUse;
                } else if (x == lastIndex) {
                    this.fineCategory_list[lastIndex].fieldKey = fieldKey;
                    this.fineCategory_list[lastIndex].fieldValue = fieldValue;
                    this.fineCategory_list[lastIndex].totalInUse = totalInUse;
                }
            }
        },

        setFirst: function (action, index) {
            if (0 == index) return false;

            this.pending_item = 1;

            let fieldKey = this.fineCategory_list[index].fieldKey;
            let fieldValue = this.fineCategory_list[index].fieldValue;
            let totalInUse = this.fineCategory_list[index].totalInUse;

            for (let x = this.fineCategory_list.length - 1; x >= 0; x--) {
                if (x <= index && x != 0) {
                    this.fineCategory_list[x].fieldKey = this.fineCategory_list[x - 1].fieldKey;
                    this.fineCategory_list[x].fieldValue = this.fineCategory_list[x - 1].fieldValue;
                    this.fineCategory_list[x].totalInUse = this.fineCategory_list[x - 1].totalInUse;
                } else if (x == 0) {
                    this.fineCategory_list[0].fieldKey = fieldKey;
                    this.fineCategory_list[0].fieldValue = fieldValue;
                    this.fineCategory_list[0].totalInUse = totalInUse;
                }
            }
        },

        loadFineCategoryList: function () {
            let form_data = new FormData();
            form_data.append('subCategory_code', this.subCategory_code.fieldKey);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailFineCategory', form_data).then((response) => {
                this.fineCategory_list = response.data.data;
            });
        },

        loadFineCategoryList_to_transfer: function () {
            let form_data = new FormData();
            form_data.append('subCategory_code', this.subCategory_code_to_transfer.fieldKey);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailFineCategory', form_data).then((response) => {
                this.allCopyFineCategory_list = response.data.data;

                this.allFineCategory_list = [];
                for (let x = 0; x <= this.allCopyFineCategory_list.length - 1; x++) {
                    if (
                        this.fineCategory_list[this.datatable_row_id].fieldKey !=
                        this.allCopyFineCategory_list[x].fieldKey
                    ) {
                        this.allFineCategory_list[x] = this.allCopyFineCategory_list[x];
                    }
                }

                this.fineCategory_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
            });
        },

        loadallFineCategoryList: function () {
            let form_data = new FormData();
            //  form_data.append('subCategory_code', this.subCategory_code.fieldKey);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailFineCategory', form_data).then((response) => {
                this.allFineCategory_list = response.data.data;
                this.allCopyFineCategory_list = response.data.data;
            });
        },

        loadSubCategoryList: function () {
            let form_data = new FormData();
            form_data.append('category_code', this.category_code.fieldKey);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailSubCategory', form_data).then((response) => {
                this.subCategory_list = response.data.data;
            });
        },

        loadSubCategoryList_to_transfer: function () {
            let form_data = new FormData();
            form_data.append('category_code', this.category_code_to_transfer.fieldKey);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailSubCategory', form_data).then((response) => {
                this.subCategory_list_to_transfer = response.data.data;
            });
        },

        loadCategoryList: function () {
            let form_data = new FormData();
            form_data.append('active', 1);
            this.$api.post(this.cirrus8_api_url + 'api/vue/category', form_data).then((response) => {
                this.category_list = response.data.data;
            });
        },

        loadFineCategoryBenchmark: function () {
            let form_data = new FormData();
            form_data.append('year', this.year_code.fieldKey);
            form_data.append('category', this.fineCategory_code);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailFineCategoryBenchmark', form_data).then((response) => {
                this.occCost = response.data.occupancy;
            });
        },

        saveFineCategoryBenchmark: function () {
            if (!this.occCost) {
                this.$noty.error('You have not entered an Occupancy Cost percentage.');
                return;
            }

            let form_data = new FormData();
            form_data.append('year', this.year_code.fieldKey);
            form_data.append('category', this.fineCategory_code);
            form_data.append('cost', this.occCost);
            this.$api
                .post(this.cirrus8_api_url + 'api/vue/saveRetailFineCategoryBenchmark', form_data)
                .then((response) => {
                    this.$noty.success('Benchmark Successfully Updated.');
                });
        },

        amountOnly: function (evt) {
            //---numbers and decimal only
            evt = evt ? evt : window.event;
            var charCode = evt.which ? evt.which : evt.keyCode;
            if (charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57)) {
                evt.preventDefault();
            } else {
                return true;
            }
        },
        addCategory: function () {
            this.fineCategory_list.push({ fieldKey: '', fieldValue: '' });
        },
        deleteDatatableRow: function (datatable_row_id) {
            if (this.fineCategory_list[datatable_row_id].fieldKey) {
                if (this.fineCategory_list[datatable_row_id].totalInUse > 0) {
                    this.subCategory_code_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
                    this.category_code_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
                    this.fineCategory_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
                    this.show_fine_modal = true;
                    this.datatable_row_id = datatable_row_id;
                } else {
                    Swal.fire({
                        text: 'Are you sure you want to delete this fine category?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Ok',
                        cancelButtonText: 'Cancel',
                    }).then((result) => {
                        if (result.value) {
                            this.loading_page_setting = true;
                            this.formData.subCategory = this.subCategory_code.fieldKey;
                            this.formData.fineCategory = this.fineCategory_list[datatable_row_id].fieldKey;
                            this.$api
                                .post(this.cirrus8_api_url + 'api/vue/deleteAPIFineCategory', this.formData)
                                .then((response) => {
                                    this.loading_page_setting = false;
                                    this.allCopyFineCategory_list = response.data.data;
                                    this.$delete(this.fineCategory_list, datatable_row_id);
                                    this.$noty.success('Successfully Deleted.');
                                });
                        }
                    });
                }
            } else {
                this.pending_item = 1;
                this.$delete(this.fineCategory_list, datatable_row_id);
            }
        },

        deleteAndTransferDatatableRow: function () {
            if (typeof this.fineCategory_to_transfer.fieldKey == 'undefined') return;

            if (!this.fineCategory_to_transfer.fieldKey) {
                this.$noty.error('You have not selected a fine category.');
                return;
            }
            Swal.fire({
                text: 'Are you sure you want to delete this fine category?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ok',
                cancelButtonText: 'Cancel',
            }).then((result) => {
                if (result.value) {
                    this.loading_page_setting = true;
                    this.formData.transferCode = this.fineCategory_to_transfer.fieldKey;
                    this.formData.subCategory = this.subCategory_code.fieldKey;
                    this.formData.fineCategory = this.fineCategory_list[this.datatable_row_id].fieldKey;

                    this.$api
                        .post(this.cirrus8_api_url + 'api/vue/deleteandTransferAPIFineCategory', this.formData)
                        .then((response) => {
                            this.loading_page_setting = false;
                            this.fineCategory_list = response.data.data;
                            //  this.$delete(this.fineCategory_list , this.datatable_row_id);
                            this.$noty.success('Successfully Transferred and Deleted.');
                            this.show_fine_modal = false;
                        });
                }
            });
        },

        saveCategory: function () {
            if (this.category_code.fieldKey == 'null' || !this.category_code.fieldKey) {
                this.$noty.error('You have not selected a category');
                return;
            }

            if (this.subCategory_code.fieldKey == 'null' || !this.subCategory_code.fieldKey) {
                this.$noty.error('You have not selected a sub category');
                return;
            }

            for (let x = 0; x < this.fineCategory_list.length; x++) {
                if (this.fineCategory_list[x].fieldValue == 'null' || !this.fineCategory_list[x].fieldValue) {
                    this.$noty.error('You have not entered a fine category name.');
                    return;
                } else {
                    for (let y = 0; y < this.fineCategory_list.length; y++) {
                        if (x != y) {
                            if (this.fineCategory_list[x].fieldValue == this.fineCategory_list[y].fieldValue) {
                                this.$noty.error('Fine Category Name is already existing.');
                                return;
                            }
                        }
                    }
                }
            }

            this.loading_page_setting = true;
            this.formData.subCategory = this.subCategory_code.fieldKey;
            this.formData.fineCategory = this.fineCategory_list;
            this.$api.post(this.cirrus8_api_url + 'api/vue/saveFineCategory', this.formData).then((response) => {
                this.loading_page_setting = false;
                this.pending_item = 0;
                this.fineCategory_list = response.data.data;
                this.$noty.success('Successfully Updated.');
            });
        },
    },
    watch: {
        category_code: function (value) {
            if (value.fieldKey) {
                this.subCategory_code = { fieldKey: '', fieldValue: 'Please select...' };
                this.loadSubCategoryList();
            } else {
                this.subCategory_list = [];
            }

            this.fineCategory_list = [];
        },

        subCategory_code: function (value) {
            if (value.fieldKey) {
                this.loadFineCategoryList();
            } else {
                this.fineCategory_list = [];
            }
        },
        category_code_to_transfer: function (value) {
            if (value.fieldKey) {
                this.subCategory_code_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
                this.loadSubCategoryList_to_transfer();
            } else {
                this.subCategory_list_to_transfer = [];
            }

            this.allCopyFineCategory_list = [];
        },

        subCategory_code_to_transfer: function (value) {
            if (value.fieldKey) {
                this.loadFineCategoryList_to_transfer();
            } else {
                this.allCopyFineCategory_list = [];
            }
        },
        year_code: function (value) {
            if (value.fieldKey && this.fineCategory_code) {
                this.loadFineCategoryBenchmark();
            }
        },
    },
    mixins: [global_mixins],
};
</script>
