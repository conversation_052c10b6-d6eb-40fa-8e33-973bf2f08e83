<template>
    <div class="c8-page">
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <div
            class="page-form"
            style="min-height: 350px"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="1"
                    md="1"
                    class="form-label required"
                    >Category:</v-col
                >
                <v-col
                    xs="12"
                    sm="11"
                    md="11"
                    class="form-input"
                >
                    <multiselect
                        v-model="category_code"
                        :options="category_list"
                        :allowEmpty="false"
                        class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                        group-label="language"
                        placeholder="Select a category"
                        track-by="fieldKey"
                        label="fieldValue"
                        :show-labels="false"
                        ><span slot="noResult"
                            >Oops! No elements found. Consider changing the search query.</span
                        ></multiselect
                    >
                </v-col>
            </v-row>

            <v-card
                class="section-toolbar"
                dark
                text
                tile
                :class="pending_item ? 'edited-form' : 'titleHeader'"
            >
                <v-card-actions>
                    <h6 class="title font-weight-black">SUB CATEGORIES</h6>
                    <v-spacer></v-spacer>
                    <v-btn
                        x-small
                        title="Add"
                        icon
                        @click="
                            addCategory();
                            pending_item = 1;
                        "
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-btn
                        x-small
                        title="Save"
                        icon
                        @click="saveCategory()"
                    >
                        <v-icon>done</v-icon>
                    </v-btn>
                </v-card-actions>
            </v-card>

            <v-data-table
                class="c8-datatable-custom documents-table"
                v-show="subCategory_list.length > 0"
                dense
                item-key="id"
                :headers="headers_category"
                :items="subCategory_list"
                :items-per-page="5000"
                hide-default-footer
                :total-visible="5000"
                :search="search_datatable"
            >
                <template v-slot:item.fieldValue="{ item }">
                    <input
                        v-model="item.fieldValue"
                        @keypress="pending_item = 1"
                        placeholder="Sub Category Name"
                    />
                </template>

                <template v-slot:item.benchMark="{ item }">
                    <a
                        v-if="item.fieldKey"
                        @click="
                            year_code = { fieldKey: currentYear, fieldValue: currentYear };
                            show_benchmark_modal = true;
                            subCategory_code = item.fieldKey;
                            subCategory_name = item.fieldValue;
                            loadSubCategoryBenchmark();
                        "
                        href="#"
                        ><v-icon color="blue darken-2">list</v-icon></a
                    >
                </template>

                <template v-slot:item.action1="{ item }">
                    <v-icon
                        color="green"
                        class="rotate90 option"
                        @click="setFirst('updateSeqFirst', subCategory_list.indexOf(item))"
                        >fast_rewind
                    </v-icon>
                    <v-icon
                        color="green"
                        class="rotate90 option"
                        @click="setOrder('updateSeqUp', subCategory_list.indexOf(item))"
                        >arrow_left
                    </v-icon>
                    <v-icon
                        color="green"
                        class="rotate90 option"
                        @click="setOrder('updateSeqDown', subCategory_list.indexOf(item))"
                        >arrow_right
                    </v-icon>
                    <v-icon
                        color="green"
                        class="rotate90 option"
                        @click="setLast('updateSeqLast', subCategory_list.indexOf(item))"
                        >fast_forward
                    </v-icon>
                    <v-icon
                        v-if="item.totalInTable > 0"
                        color="#607d8b"
                        class="option"
                        title="Delete all associated fine category before deleting"
                        >close
                    </v-icon>
                    <v-icon
                        v-else
                        color="red"
                        class="option"
                        @click="
                            subCategory_from_transfer = item.fieldValue;
                            deleteDatatableRow(subCategory_list.indexOf(item));
                        "
                        >close
                    </v-icon>
                </template>
            </v-data-table>
        </div>

        <v-dialog
            v-model="show_benchmark_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card height="60vh">
                <v-card-title class="headline">
                    Benchmark
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_benchmark_modal = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Sub Category:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="subCategory_name"
                                    :edit_form="false"
                                ></cirrus-input>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Year:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="year_code"
                                    :options="yearList"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Select a year"
                                    track-by="fieldKey"
                                    label="fieldValue"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Occupancy Cost %:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <input
                                    v-model="occCost"
                                    @keypress="amountOnly($event)"
                                    :maxlength="10"
                                />
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label Z"
                            >
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <v-btn
                                    color="success"
                                    small
                                    tile
                                    @click="saveSubCategoryBenchmark()"
                                >
                                    Save
                                </v-btn>

                                <v-btn
                                    color="primary"
                                    small
                                    tile
                                    @click="
                                        year_code = { fieldKey: currentYear, fieldValue: currentYear };
                                        show_benchmark_modal = false;
                                    "
                                >
                                    Close
                                </v-btn>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="show_sub_modal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card height="60vh">
                <v-card-title class="headline">
                    Transfer Leases to selected Sub Category before deleting
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="show_sub_modal = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label"
                            ></v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                Transfer all Leases with Sub Category {{ subCategory_from_transfer }} to
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Category:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="category_code_to_transfer"
                                    :options="category_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Select a category"
                                    track-by="fieldKey"
                                    label="fieldValue"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label required"
                                >Sub Category:</v-col
                            >
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <multiselect
                                    v-model="SubCategory_to_transfer"
                                    :options="allSubCategory_list"
                                    :allowEmpty="false"
                                    class="v-step-property-select vue-select2 dropdown-left dropdown-400"
                                    group-label="language"
                                    placeholder="Select a sub category"
                                    track-by="fieldKey"
                                    label="fieldValue"
                                    :show-labels="false"
                                    ><span slot="noResult"
                                        >Oops! No elements found. Consider changing the search query.</span
                                    ></multiselect
                                >
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="2"
                                md="2"
                                class="form-label Z"
                            >
                            </v-col>
                            <v-col
                                xs="12"
                                sm="10"
                                md="10"
                                class="form-input"
                            >
                                <v-btn
                                    color="success"
                                    small
                                    tile
                                    @click="deleteAndTransferDatatableRow()"
                                >
                                    Transfer and Delete
                                </v-btn>

                                <v-btn
                                    color="primary"
                                    small
                                    tile
                                    @click="show_sub_modal = false"
                                >
                                    Cancel
                                </v-btn>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import global_mixins from '../../../../plugins/mixins';
import { bus } from '../../../../plugins/bus';
const Swal = require('sweetalert2');
export default {
    props: {
        currentList: Object,
        currentYear: String,
    },
    data() {
        return {
            category_code_to_transfer: [],
            loading_page_setting: false,
            pending_item: 0,
            datatable_row_id: 0,
            SubCategory_to_transfer: '',
            show_sub_modal: false,
            subCategory_code: '',
            subCategory_name: '',
            show_benchmark_modal: false,
            yearList: this.currentList,
            year_code: { fieldKey: this.currentYear, fieldValue: this.currentYear },

            subCategory_from_transfer: '',
            subCategory_list: [],
            allCopySubCategory_list: [],
            allSubCategory_list: [],
            category_code: { fieldKey: '', fieldValue: 'Please select...' },
            category_list: [],

            headers_category: [
                { text: 'Name', value: 'fieldValue', sortable: true, align: 'left' },
                { text: 'Benchmark', value: 'benchMark', sortable: true, align: 'left' },
                { text: 'Action', value: 'action1', align: 'end', sortable: false, align: 'center' },
            ],
            occCost: '',
            formData: {
                user_type: localStorage.getItem('user_type'),
            },
        };
    },
    mounted() {
        this.loadCategoryList();
    },
    computed: {
        ...mapState(['current_db', 'user_type', 'username', 'cirrus8_api_url']),
    },
    created() {
        bus.$on('SubListloadCategoryList', () => {
            this.category_code = { fieldKey: '', fieldValue: 'Please select...' };
            this.loadCategoryList();
        });
    },
    methods: {
        setOrder: function (action, index) {
            let OldIndex = 0;
            let NewIndex = 0;

            if (action == 'updateSeqDown') {
                if (this.subCategory_list.length == index + 1) return false;

                OldIndex = index;
                NewIndex = index + 1;
            } else if (action == 'updateSeqUp') {
                if (index == 0) return false;

                OldIndex = index;
                NewIndex = index - 1;
            }

            this.pending_item = 1;

            let fieldKey = this.subCategory_list[NewIndex].fieldKey;
            let fieldValue = this.subCategory_list[NewIndex].fieldValue;
            let totalInUse = this.subCategory_list[NewIndex].totalInUse;
            let totalInTable = this.subCategory_list[NewIndex].totalInTable;

            this.subCategory_list[NewIndex].fieldKey = this.subCategory_list[OldIndex].fieldKey;
            this.subCategory_list[NewIndex].fieldValue = this.subCategory_list[OldIndex].fieldValue;
            this.subCategory_list[NewIndex].totalInUse = this.subCategory_list[OldIndex].totalInUse;
            this.subCategory_list[NewIndex].totalInTable = this.subCategory_list[OldIndex].totalInTable;

            this.subCategory_list[OldIndex].fieldKey = fieldKey;
            this.subCategory_list[OldIndex].fieldValue = fieldValue;
            this.subCategory_list[OldIndex].totalInUse = totalInUse;
            this.subCategory_list[OldIndex].totalInTable = totalInTable;
        },
        setLast: function (action, index) {
            if (this.subCategory_list.length == index + 1) return false;

            this.pending_item = 1;

            let lastIndex = this.subCategory_list.length - 1;

            let fieldKey = this.subCategory_list[index].fieldKey;
            let fieldValue = this.subCategory_list[index].fieldValue;
            let totalInUse = this.subCategory_list[index].totalInUse;
            let totalInTable = this.subCategory_list[index].totalInTable;

            for (let x = 0; x < this.subCategory_list.length; x++) {
                if (x >= index && x != lastIndex) {
                    this.subCategory_list[x].fieldKey = this.subCategory_list[x + 1].fieldKey;
                    this.subCategory_list[x].fieldValue = this.subCategory_list[x + 1].fieldValue;
                    this.subCategory_list[x].totalInUse = this.subCategory_list[x + 1].totalInUse;
                    this.subCategory_list[x].totalInTable = this.subCategory_list[x + 1].totalInTable;
                } else if (x == lastIndex) {
                    this.subCategory_list[lastIndex].fieldKey = fieldKey;
                    this.subCategory_list[lastIndex].fieldValue = fieldValue;
                    this.subCategory_list[lastIndex].totalInUse = totalInUse;
                    this.subCategory_list[lastIndex].totalInTable = totalInTable;
                }
            }
        },

        setFirst: function (action, index) {
            if (0 == index) return false;

            this.pending_item = 1;

            let fieldKey = this.subCategory_list[index].fieldKey;
            let fieldValue = this.subCategory_list[index].fieldValue;
            let totalInUse = this.subCategory_list[index].totalInUse;
            let totalInTable = this.subCategory_list[index].totalInTable;

            for (let x = this.subCategory_list.length - 1; x >= 0; x--) {
                if (x <= index && x != 0) {
                    this.subCategory_list[x].fieldKey = this.subCategory_list[x - 1].fieldKey;
                    this.subCategory_list[x].fieldValue = this.subCategory_list[x - 1].fieldValue;
                    this.subCategory_list[x].totalInUse = this.subCategory_list[x - 1].totalInUse;
                    this.subCategory_list[x].totalInTable = this.subCategory_list[x - 1].totalInTable;
                } else if (x == 0) {
                    this.subCategory_list[0].fieldKey = fieldKey;
                    this.subCategory_list[0].fieldValue = fieldValue;
                    this.subCategory_list[0].totalInUse = totalInUse;
                    this.subCategory_list[0].totalInTable = totalInTable;
                }
            }
        },

        loadSubCategoryList: function () {
            let form_data = new FormData();
            form_data.append('category_code', this.category_code.fieldKey);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailSubCategory', form_data).then((response) => {
                this.subCategory_list = response.data.data;
            });
        },

        loadCategoryList: function () {
            let form_data = new FormData();
            this.$api.post(this.cirrus8_api_url + 'api/vue/category', form_data).then((response) => {
                this.category_list = response.data.data;
            });
        },

        loadSubCategoryBenchmark: function () {
            let form_data = new FormData();
            form_data.append('year', this.year_code.fieldKey);
            form_data.append('category', this.subCategory_code);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailSubCategoryBenchmark', form_data).then((response) => {
                this.occCost = response.data.occupancy;
            });
        },

        saveSubCategoryBenchmark: function () {
            if (!this.occCost) {
                this.$noty.error('You have not entered an Occupancy Cost percentage.');
                return;
            }

            let form_data = new FormData();
            form_data.append('year', this.year_code.fieldKey);
            form_data.append('category', this.subCategory_code);
            form_data.append('cost', this.occCost);
            this.$api
                .post(this.cirrus8_api_url + 'api/vue/saveRetailSubCategoryBenchmark', form_data)
                .then((response) => {
                    this.$noty.success('Benchmark Successfully Updated.');
                });
        },

        amountOnly: function (evt) {
            //---numbers and decimal only
            evt = evt ? evt : window.event;
            var charCode = evt.which ? evt.which : evt.keyCode;
            if (charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57)) {
                evt.preventDefault();
            } else {
                return true;
            }
        },
        addCategory: function () {
            this.subCategory_list.push({ fieldKey: '', fieldValue: '', categoryID: '' });
        },

        deleteDatatableRow: function (datatable_row_id) {
            if (this.subCategory_list[datatable_row_id].fieldKey) {
                if (this.subCategory_list[datatable_row_id].totalInTable > 0) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Item cannot be deleted',
                    });
                } else if (this.subCategory_list[datatable_row_id].totalInUse > 0) {
                    this.show_sub_modal = true;
                    this.datatable_row_id = datatable_row_id;
                    this.SubCategory_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
                    this.category_code_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
                } else {
                    Swal.fire({
                        text: 'Are you sure you want to delete this sub category?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Ok',
                        cancelButtonText: 'Cancel',
                    }).then((result) => {
                        if (result.value) {
                            this.loading_page_setting = true;
                            this.formData.category = this.category_code.fieldKey;
                            this.formData.subCategory = this.subCategory_list[datatable_row_id].fieldKey;
                            this.$api
                                .post(this.cirrus8_api_url + 'api/vue/deleteAPISubCategory', this.formData)
                                .then((response) => {
                                    this.loading_page_setting = false;
                                    this.allCopySubCategory_list = response.data.data;
                                    this.$delete(this.subCategory_list, datatable_row_id);
                                    this.$noty.success('Successfully Deleted.');
                                });
                        }
                    });
                }
            } else {
                this.pending_item = 1;
                this.$delete(this.subCategory_list, datatable_row_id);
            }
        },

        deleteAndTransferDatatableRow: function () {
            if (typeof this.SubCategory_to_transfer.fieldKey == 'undefined') return;

            if (!this.SubCategory_to_transfer.fieldKey) {
                this.$noty.error('You have not selected a sub category.');
                return;
            }

            Swal.fire({
                text: 'Are you sure you want to delete this sub category?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ok',
                cancelButtonText: 'Cancel',
            }).then((result) => {
                if (result.value) {
                    this.loading_page_setting = true;
                    this.formData.transferCode = this.SubCategory_to_transfer.fieldKey;
                    this.formData.category = this.category_code.fieldKey;
                    this.formData.SubCategory = this.subCategory_list[this.datatable_row_id].fieldKey;
                    this.$api
                        .post(this.cirrus8_api_url + 'api/vue/deleteandTransferAPISubCategory', this.formData)
                        .then((response) => {
                            this.loading_page_setting = false;
                            this.subCategory_list = response.data.data;
                            this.$noty.success('Successfully Transferred and Deleted.');
                            this.show_sub_modal = false;
                        });
                }
            });
        },

        saveCategory: function () {
            if (this.category_code.fieldKey == 'null' || !this.category_code.fieldKey) {
                this.$noty.error('You have not selected a category');
                return;
            }

            for (let x = 0; x < this.subCategory_list.length; x++) {
                if (this.subCategory_list[x].fieldValue == 'null' || !this.subCategory_list[x].fieldValue) {
                    this.$noty.error('You have not entered a sub category name.');
                    return;
                } else {
                    for (let y = 0; y < this.subCategory_list.length; y++) {
                        if (x != y) {
                            if (this.subCategory_list[x].fieldValue == this.subCategory_list[y].fieldValue) {
                                this.$noty.error('Sub Category Name is already existing.');
                                return;
                            }
                        }
                    }
                }
            }

            this.loading_page_setting = true;
            this.formData.category_code = this.category_code.fieldKey;
            this.formData.subCategory = this.subCategory_list;
            this.$api.post(this.cirrus8_api_url + 'api/vue/saveSubCategory', this.formData).then((response) => {
                bus.$emit('FineListloadSubCategoryList');
                this.loading_page_setting = false;
                this.pending_item = 0;
                this.subCategory_list = response.data.data;
                this.$noty.success('Successfully Updated.');
            });
        },

        loadSubCategoryList_to_transfer: function () {
            let form_data = new FormData();
            form_data.append('category_code', this.category_code_to_transfer.fieldKey);
            this.$api.post(this.cirrus8_api_url + 'api/vue/retailSubCategory', form_data).then((response) => {
                this.allCopySubCategory_list = response.data.data;

                this.allSubCategory_list = [];
                for (let x = 0; x <= this.allCopySubCategory_list.length - 1; x++) {
                    if (
                        this.subCategory_list[this.datatable_row_id].fieldKey !=
                        this.allCopySubCategory_list[x].fieldKey
                    ) {
                        this.allSubCategory_list[x] = this.allCopySubCategory_list[x];
                    }

                    if (this.allCopySubCategory_list.length - 1 == x) {
                        this.SubCategory_to_transfer = [];
                    }
                }
            });
        },
    },
    watch: {
        category_code_to_transfer: function (value) {
            if (value.fieldKey) {
                this.SubCategory_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
                this.loadSubCategoryList_to_transfer();
            } else {
                this.SubCategory_to_transfer = { fieldKey: '', fieldValue: 'Please select...' };
            }
        },
        category_code: function (value) {
            if (value.fieldKey) this.loadSubCategoryList();
            else this.subCategory_list = [];
        },
        year_code: function (value) {
            if (value.fieldKey && this.subCategory_code) {
                this.loadSubCategoryBenchmark();
            }
        },
    },
    mixins: [global_mixins],
};
</script>
