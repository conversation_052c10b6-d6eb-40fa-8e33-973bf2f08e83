<template>
    <v-container
        fluid
        class="c8-page"
    >
        <cirrus-page-header title="Bank Statement" />
        <!-- FORM -->
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Bank Account</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select
                        placeholder="Select Bank Account"
                        :has-empty="false"
                        v-model="bank"
                        :options="bank_list"
                        @input="checkBank()"
                    />
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Bank Statement Range</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-date-picker
                        v-model="date_from"
                        id="date-from"
                        ref="date_from"
                    />
                    &nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;&nbsp;
                    <cirrus-date-picker
                        v-model="date_to"
                        id="date-to"
                        ref="date_to"
                    />
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                >
                    Print To
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="printTo('screen')"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-monitor</v-icon
                        >
                        Screen</v-btn
                    >&nbsp;
                    <v-btn
                        color="error"
                        tile
                        small
                        @click="printTo('pdf')"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-file-pdf</v-icon
                        >
                        PDF</v-btn
                    >&nbsp;
                    <v-btn
                        color="success"
                        tile
                        small
                        @click="printTo('excel')"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-file-excel</v-icon
                        >
                        Excel</v-btn
                    >
                    <div style="float: right; padding-left: 10px">
                        <!-- <bank-statement-csv :bank="bank" v-if="bank" @import="printTo('screen')" /> -->
                        <bank-statement-file
                            :date="date_to"
                            :file-bank="bank"
                            :has-download="has_download"
                            @import="printTo('screen')"
                            v-if="bank"
                        />
                    </div>
                </v-col>
            </v-row>
        </div>
        <!-- TABLE LIST -->
        <div class="page-list">
            <div
                class="c8-page-table"
                id="c8-float-header-container"
                style="position: relative"
            >
                <table
                    class="c8-float-header"
                    id="header-fixed"
                >
                    <tbody>
                        <tr>
                            <!-- <td colspan="3" style="background-color:#FFFFFF;text-align: left">
              <v-select
                 v-model="list.filters.statuses"
                 :items="status_opts"
                 item-text="label"
                 item-value="value"
                 persistent-hint
                 multiple
              />
            </td> -->
                            <td
                                colspan="5"
                                style="background-color: #ffffff; text-align: left"
                            >
                                <span
                                    v-for="(ch, ind) in status_opts"
                                    :key="'filter-ch-' + ind"
                                    style="display: inline-block; margin-right: 5px"
                                >
                                    <v-checkbox
                                        color="primary"
                                        v-model="list.filters.statuses"
                                        :label="ch.label"
                                        :value="ch.value"
                                    />
                                </span>
                            </td>
                            <td
                                colspan="3"
                                style="background-color: #ffffff; text-align: right"
                            >
                                <div style="width: 250px; float: right; text-align: right">
                                    <div class="page-form no-line">
                                        <v-row class="form-row">
                                            <v-col
                                                xs="12"
                                                sm="4"
                                                md="4"
                                                class="form-label"
                                                >Find Amount</v-col
                                            >
                                            <v-col
                                                xs="12"
                                                sm="8"
                                                md="8"
                                                class="form-input"
                                            >
                                                <v-text-field
                                                    dense
                                                    color="primary"
                                                    v-model="list.filters.amount"
                                                    :append-icon="'mdi-download'"
                                                    @click:append="goToNext()"
                                                />
                                            </v-col>
                                        </v-row>
                                    </div>
                                </div>
                            </td>
                            <td
                                class="text-right"
                                style="background-color: #ffffff"
                            >
                                Difference
                            </td>
                            <td
                                class="text-right"
                                style="background-color: #ffffff"
                            >
                                {{ currency_symbol
                                }}{{
                                    Math.abs(last_statement_amount - (last_closing.balance ? last_closing.balance : 0))
                                        | num2AmtStr
                                }}
                            </td>
                        </tr>
                        <tr class="c8-page-table-row-header">
                            <td
                                v-for="(hdr, index) in list.headers"
                                :key="'hdr-' + index"
                                :class="hdr.add_class"
                                ref="'th-col'+hdr.field"
                            >
                                <span v-html="hdr.label"></span>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table id="main-table">
                    <tbody>
                        <tr class="c8-page-table-footer">
                            <td
                                :colspan="list.headers.length - 2"
                                class="text-right"
                            >
                                <span v-if="last_opening.date">Opening Balance</span>
                                <span v-else>No last opening balance found</span>
                            </td>
                            <td class="text-right">
                                <span v-if="last_opening.date">
                                    <v-btn
                                        color="primary"
                                        text
                                        x-small
                                        icon
                                        @click="forceToReconciled()"
                                        v-if="bank"
                                        style="margin-top: -3px"
                                        data-tooltip="Force Update all to Reconciled"
                                        data-position="bottom right"
                                        ><v-icon>mdi-refresh</v-icon></v-btn
                                    >
                                    {{ currency_symbol }}{{ last_opening.balance | num2AmtStr }}
                                </span>
                                <span v-else>
                                    <v-btn
                                        color="primary"
                                        text
                                        x-small
                                        icon
                                        @click="forceToReconciled()"
                                        v-if="bank"
                                        style="margin-top: -3px"
                                        data-tooltip="Force Update all to Reconciled"
                                        data-position="bottom right"
                                        ><v-icon>mdi-refresh</v-icon></v-btn
                                    >
                                    {{ currency_symbol }}0.00
                                </span>
                            </td>
                            <td class="text-right"></td>
                        </tr>
                        <tr class="c8-page-table-footer">
                            <!-- {{Object.keys(statements)[Object.keys(statements).length -1] | sql2Date}} -  -->
                            <td
                                :colspan="list.headers.length - 2"
                                class="text-right"
                            >
                                Calculated Statement Closing Balance
                            </td>
                            <td class="text-right">{{ currency_symbol }}{{ last_statement_amount | num2AmtStr }}</td>
                            <td class="text-right"></td>
                        </tr>
                        <tr class="c8-page-table-footer">
                            <!-- {{Object.keys(statements)[Object.keys(statements).length -1] | sql2Date}} -  -->
                            <td
                                :colspan="list.headers.length - 2"
                                class="text-right"
                            >
                                <span v-if="last_closing.date">Closing Bank Statement Balance at {{ date_to }}</span>
                                <!-- <span v-if="last_closing.date">Closing Bank Statement Balance at {{last_closing.date | sql2Date}}</span> -->
                                <span v-else>No closing balance found.</span>
                            </td>
                            <td
                                class="text-right"
                                v-if="last_closing.balance"
                            >
                                <v-btn
                                    color="primary"
                                    text
                                    x-small
                                    icon
                                    @click="updateClosingBalance()"
                                    v-if="bank"
                                    style="margin-top: -3px"
                                    data-tooltip="Update closing balance"
                                    data-position="bottom right"
                                    ><v-icon>mdi-square-edit-outline</v-icon></v-btn
                                >
                                {{ currency_symbol }}{{ last_closing.balance | num2AmtStr }}
                            </td>
                            <td
                                class="text-right"
                                v-else
                            >
                                <v-btn
                                    color="primary"
                                    text
                                    x-small
                                    icon
                                    @click="updateClosingBalance()"
                                    v-if="bank"
                                    style="margin-top: -3px"
                                    data-tooltip="Update closing balance"
                                    data-position="bottom right"
                                    ><v-icon>mdi-square-edit-outline</v-icon></v-btn
                                >
                                {{ currency_symbol }}0.00
                            </td>
                            <td class="text-right"></td>
                        </tr>
                        <tr class="c8-page-table-footer">
                            <td
                                :colspan="list.headers.length - 2"
                                class="text-right"
                            >
                                Difference
                            </td>
                            <td class="text-right">
                                {{ currency_symbol
                                }}{{
                                    Math.abs(last_statement_amount - (last_closing.balance ? last_closing.balance : 0))
                                        | num2AmtStr
                                }}
                            </td>
                            <td class="text-right"></td>
                        </tr>
                        <tr v-show="Object.keys(statements).length > 0">
                            <td
                                colspan="5"
                                style="background-color: #ffffff; text-align: left"
                            >
                                <span
                                    v-for="(ch, ind) in status_opts"
                                    :key="'filter-ch-' + ind"
                                    style="display: inline-block; margin-right: 5px"
                                >
                                    <v-checkbox
                                        color="primary"
                                        v-model="list.filters.statuses"
                                        :label="ch.label"
                                        :value="ch.value"
                                    />
                                </span>
                            </td>
                            <td
                                colspan="3"
                                style="background-color: #ffffff"
                            >
                                <div style="width: 250px; float: right; text-align: right">
                                    <div class="page-form no-line">
                                        <v-row class="form-row">
                                            <v-col
                                                xs="12"
                                                sm="4"
                                                md="4"
                                                class="form-label"
                                                >Find Amount</v-col
                                            >
                                            <v-col
                                                xs="12"
                                                sm="8"
                                                md="8"
                                                class="form-input"
                                            >
                                                <v-text-field
                                                    dense
                                                    color="primary"
                                                    v-model="list.filters.amount"
                                                    :append-icon="'mdi-download'"
                                                    @click:append="goToNext()"
                                                />
                                            </v-col>
                                        </v-row>
                                    </div>
                                </div>
                            </td>
                            <td
                                class="text-right"
                                style="background-color: #ffffff"
                            ></td>
                            <td
                                class="text-right"
                                style="background-color: #ffffff"
                            ></td>
                        </tr>
                        <tr
                            class="c8-page-table-row-header"
                            id="to-float-header"
                        >
                            <td
                                v-for="(hdr, index) in list.headers"
                                :key="'hdr-' + index"
                                :class="hdr.add_class"
                                ref="'th-col'+hdr.field"
                            >
                                <span v-html="hdr.label"></span>
                            </td>
                        </tr>
                        <template v-for="(trans, trans_date) in statements">
                            <template
                                v-for="(row, row_index) in trans.rows"
                                v-if="trans.rows && trans.rows.length > 0"
                            >
                                <!-- <tr v-show="rowFilter(row)" :class="{'bg-error' : !!+row.is_deleted}"> -->
                                <tr
                                    v-show="row.show"
                                    :class="{ 'bg-error': !!+row.is_deleted }"
                                >
                                    <template v-for="(hdr_row, hdr_row_index) in list.headers">
                                        <td
                                            v-if="
                                                row.hasOwnProperty(hdr_row.field) &&
                                                list.headers_excl.indexOf(hdr_row.field) === -1
                                            "
                                            :class="[hdr_row.add_class]"
                                        >
                                            <span v-if="hdr_row.hasOwnProperty('format') && hdr_row.format == 'date'">{{
                                                row[hdr_row.field] | sql2Date
                                            }}</span>
                                            <span
                                                v-else-if="
                                                    hdr_row.hasOwnProperty('format') && hdr_row.format == 'amount'
                                                "
                                                >{{ row[hdr_row.field] | num2AmtStr }}</span
                                            >
                                            <span
                                                v-else-if="
                                                    hdr_row.hasOwnProperty('format') && hdr_row.format == 'uppercase'
                                                "
                                                >{{ row[hdr_row.field] | strtoupper }}</span
                                            >
                                            <span
                                                v-else-if="
                                                    hdr_row.hasOwnProperty('format') && hdr_row.format == 'ucfirst'
                                                "
                                                >{{ row[hdr_row.field] | ucfirst }}</span
                                            >
                                            <span v-else>{{ row[hdr_row.field] }}</span>
                                        </td>
                                        <td
                                            v-else-if="hdr_row.field == 'balance'"
                                            :class="[hdr_row.add_class]"
                                        >
                                            <span v-if="!!!+row.is_deleted">{{ row[hdr_row.field] | num2AmtStr }}</span>
                                        </td>
                                        <td
                                            v-else-if="hdr_row.field == 'trans_amount'"
                                            :class="[hdr_row.add_class]"
                                            v-html="setAmount(row[hdr_row.field])"
                                        ></td>
                                        <td
                                            v-else-if="hdr_row.field == 'status'"
                                            :class="[hdr_row.add_class]"
                                        >
                                            {{ status_txt[row[hdr_row.field]] ? status_txt[row[hdr_row.field]] : '' }}
                                        </td>
                                        <td
                                            v-else-if="hdr_row.field == 'actions'"
                                            :class="[hdr_row.add_class]"
                                            style="min-width: 80px"
                                        >
                                            <v-btn
                                                text
                                                icon
                                                color="error"
                                                x-small
                                                style="margin: 0 2.5px"
                                                v-if="row['status'] != 'reconciled'"
                                                @click="askDeleteStatement(row)"
                                                data-tooltip="Delete bank statement line imported"
                                                data-position="bottom right"
                                            >
                                                <v-icon>mdi-close</v-icon>
                                            </v-btn>
                                            <v-btn
                                                text
                                                icon
                                                color="primary"
                                                x-small
                                                style="margin: 0 2.5px"
                                                v-if="!row.is_reconciled && !row.adjustment_id != null"
                                                @click="transactStatement(row)"
                                                :data-tooltip="row.amount < 0 ? 'Process Payment' : 'Process Receipt'"
                                                data-position="bottom right"
                                            >
                                                <v-icon>mdi-file-move</v-icon>
                                            </v-btn>
                                        </td>
                                        <td
                                            v-else
                                            :class="[hdr_row.add_class]"
                                        >
                                            &nbsp;
                                        </td>
                                    </template>
                                </tr>
                            </template>
                            <!-- <template v-if="!trans.rows || trans.rows.length == 0">
								<tr>
									<td>
										{{trans_date | sql2Date}}
									</td>
									<td :colspan="list.headers.length - 1 ">
										No statements found.
										<bank-statement-file :date="$filters.sql2Date(trans_date)" :btn-links="true" :bank="bank" @import="download" @download="download"/>
									</td>
								</tr>
							</template> -->
                        </template>
                        <tr class="c8-page-table-footer">
                            <!-- {{Object.keys(statements)[Object.keys(statements).length -1] | sql2Date}} -  -->
                            <td
                                :colspan="list.headers.length - 2"
                                class="text-right"
                            >
                                Calculated Statement Closing Balance
                            </td>
                            <td class="text-right">{{ currency_symbol }}{{ last_statement_amount | num2AmtStr }}</td>
                            <td class="text-right"></td>
                        </tr>
                        <tr class="c8-page-table-footer">
                            <!-- {{Object.keys(statements)[Object.keys(statements).length -1] | sql2Date}} -  -->
                            <td
                                :colspan="list.headers.length - 2"
                                class="text-right"
                            >
                                <span v-if="last_closing.date">Closing Bank Statement Balance at {{ date_to }}</span>
                                <!-- <span v-if="last_closing.date">Closing Bank Statement Balance at {{last_closing.date | sql2Date}}</span> -->
                                <span v-else>No closing balance found.</span>
                            </td>
                            <td
                                class="text-right"
                                v-if="last_closing.balance"
                            >
                                {{ currency_symbol }}{{ last_closing.balance | num2AmtStr }}
                            </td>
                            <td
                                class="text-right"
                                v-else
                            >
                                {{ currency_symbol }}0.00
                            </td>
                            <td class="text-right"></td>
                        </tr>
                        <tr class="c8-page-table-footer">
                            <td
                                :colspan="list.headers.length - 2"
                                class="text-right"
                            >
                                Difference
                            </td>
                            <td class="text-right">
                                {{ currency_symbol
                                }}{{
                                    Math.abs(last_statement_amount - (last_closing.balance ? last_closing.balance : 0))
                                        | num2AmtStr
                                }}
                            </td>
                            <td class="text-right"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <v-dialog
            v-model="ask_delete_mdl.show"
            persistent
            :max-width="ask_delete_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    &nbsp;
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="ask_delete_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <p style="padding: 1rem">Are you sure you want to delete this bank statement line?</p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="error"
                        depressed
                        tile
                        small
                        @click="deleteStatement(ask_delete_mdl.line)"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-check</v-icon
                        >Delete</v-btn
                    >
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="ask_delete_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="update_closing_bal_mdl.show"
            persistent
            :max-width="update_closing_bal_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Update Bank Statement Closing Balance
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="update_closing_bal_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-noty">
                        <div class="warning-box">
                            <p>
                                Warning! This will update the closing balance permanently and may affect the bank
                                statement balance.
                            </p>
                        </div>
                    </div>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Trans Date</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-date-picker
                                    v-model="update_closing_bal_mdl.trans_date"
                                    id="edit-closing-bal-date"
                                    ref="edit_closing_bal_date"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Closing Balance</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    dense
                                    v-model="update_closing_bal_mdl.closing_bal"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="success"
                        tile
                        small
                        @click="submitUpdateClosingBal()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-check</v-icon
                        >Save</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="update_closing_bal_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="force_reconciled_mdl.show"
            persistent
            :max-width="force_reconciled_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Force Reconcile
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="force_reconciled_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-noty">
                        <div class="warning-box">
                            <p>
                                Warning! This will update the bank statements permanently and may affect the bank
                                statement balance.
                            </p>
                        </div>
                    </div>
                    <div class="page-form">
                        <v-row
                            class="form-row"
                            v-if="force_reconciled_mdl.show"
                        >
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Force Reconcile Until</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-date-picker
                                    v-model="force_reconciled_mdl.trans_date"
                                    id="force-reconcile-date"
                                    ref="force_reconcile_date"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="success"
                        tile
                        small
                        @click="submitForceToReconciled()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-check</v-icon
                        >Update</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="force_reconciled_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-container>
</template>
<script>
import moment from 'moment';
import { mapState, mapActions, mapMutations } from 'vuex';
import CirrusBankStatementFile from '../../elements/CirrusBankStatementFile.vue';
import CirrusBankStatementCsvUpload from '../../elements/CirrusBankStatementCsvUpload.vue';

export default {
    name: 'BankStatements',
    components: {
        'bank-statement-file': CirrusBankStatementFile,
        'bank-statement-csv': CirrusBankStatementCsvUpload,
    },
    data() {
        return {
            has_download: false,
            bank: null,
            date_from: null,
            date_to: null,
            last_opening: {},
            last_closing: {},
            last_statement_amount: 0,
            last_closing_balance: 0,
            status_opts: [
                { label: 'Unreconciled', value: 'pending' },
                { label: 'Reconciled', value: 'reconciled' },
                { label: 'Adjustment', value: 'adjustment' },
                { label: 'Deleted', value: 'deleted' },
            ],
            list: {
                headers: [
                    { label: 'Trans Date', field: 'trans_date', add_class: 'text-left', format: 'date' },
                    // {label: 'Reference', field: 'ref_no', 'add_class': 'text-left'},
                    // {label: 'Payee', field: 'payee', 'add_class': 'text-left'},
                    { label: 'Description', field: 'description', add_class: 'text-left' },
                    { label: 'Trans Type', field: 'trans_type_txt', add_class: 'text-left' },
                    { label: 'Method', field: 'method_txt', add_class: 'text-left' },
                    { label: 'Import Date', field: 'created_at', add_class: 'text-left', format: 'date' },
                    { label: 'Imported By', field: 'create_user', add_class: 'text-left' },
                    { label: 'Status', field: 'status', add_class: 'text-left' },
                    // {label: 'Reconciled Date', field: 'recon_date', 'add_class': 'text-left', 'format': 'date'},
                    { label: 'Trans Amount ($)', field: 'trans_amount', add_class: 'text-right', format: 'amount' },
                    { label: 'Statement Balance  ($)', field: 'balance', add_class: 'text-right', format: 'amount' },
                    // {label: 'System Balance', field: 'calc_balance', 'add_class': 'sliced text-right', 'format': 'amount'},
                    { label: ' ', field: 'actions', add_class: 'text-right', format: 'amount' },
                ],
                headers_excl: ['status', 'actions', 'balance', 'trans_amount'],
                lines: [],
                filters: {
                    statuses: ['pending', 'reconciled', 'adjustment'],
                    show_deleted: false,
                    show_has_list_only: false,
                    amount: null,
                },
            },
            missing: [],
            ask_delete_mdl: {
                show: false,
                width: 370,
                line: {},
            },
            update_closing_bal_mdl: {
                show: false,
                width: 600,
                closing_bal: null,
                trans_date: null,
            },
            force_reconciled_mdl: {
                show: false,
                width: 600,
                trans_date: null,
            },
            status_txt: {
                pending: 'Unreconciled',
                approved: 'Matched',
                reconciled: 'Reconciled',
                adjustment: 'Adjustment',
                deleted: 'Deleted',
            },
            currency_symbol: '$',
        };
    },
    computed: {
        ...mapState('banks', { bank_list: (state) => state.list }),
        statements() {
            let data = {};
            let statements = {};
            this.last_statement_amount = 0;
            let last_opening_bal = this.last_opening.date ? parseFloat(this.last_opening.balance) : 0;

            this.last_statement_amount += last_opening_bal;

            if (this.list.hasOwnProperty('lines')) {
                for (var type in this.list.lines) {
                    this.sortLines(type);
                }

                for (var type in this.list.lines) {
                    let line = this.list.lines[type];
                    if (line.header && line.rows.length > 0) {
                        for (var i = 0; i < line.rows.length; i++) {
                            if (!!!+line.rows[i].is_deleted) {
                                last_opening_bal += parseFloat(line.rows[i].amount);
                                line.rows[i].balance = last_opening_bal;
                                this.last_statement_amount += parseFloat(line.rows[i].amount);
                            }
                        }
                        this.last_closing_balance = parseFloat(line.header.closing_bal);
                    }
                }

                data = this.list.lines;
                Object.keys(data)
                    .sort((a, b) => {
                        a = moment(a, 'YYYYMMDD').valueOf();
                        b = moment(b, 'YYYYMMDD').valueOf();
                        return a - b;
                    })
                    .forEach((key) => {
                        let add = true;
                        statements[key] = data[key];
                    });

                // FITLER STATEMENTS
                for (var st in statements) {
                    let rows = statements[st]['rows'];
                    for (var i = 0; i < rows.length; i++) {
                        let row = rows[i];

                        let show = true;
                        if (
                            this.list.filters.statuses.length > 0 &&
                            this.list.filters.statuses.indexOf(row.status) === -1
                        ) {
                            show = false;
                        }

                        row.show = show;
                    }
                }
            }
            return statements;
        },
        bank_statement_opening_bal() {
            let open_bal = 0;
            if (this.statements && Object.keys(this.statements).length > 0) {
                let first_date = Object.keys(this.statements)[0];
                let statement = this.statements[first_date];
                if (statement.header && statement.header.opening_bal) {
                    open_bal = parseFloat(statement.header.opening_bal);
                }
            }
            return open_bal;
        },
    },
    methods: {
        ...mapActions('banks', ['setList']),
        // 	getCalcClosingBal(trans_date){
        // let line = this.list.lines[trans_date]
        // let total = parseFloat(line.header.calc_opening_bal);
        // if(line.header){
        // 	for (var i = 0; i < line.rows.length; i++) {
        // 		total += parseFloat(line.rows[i].amount)
        // 	}
        // }
        // return total
        // 	},
        checkBank() {
            let dl = false;
            let bk = this.bank_list.find((row) => {
                return row.bankID == this.bank;
            });

            if (bk.username && bk.client_no) {
                dl = true;
            }

            this.has_download = dl;
        },
        submitForceToReconciled() {
            let request = {
                bank_code: this.bank,
                trans_date: this.force_reconciled_mdl.trans_date,
            };
            this.$api.post('bank-statements/force_reconcile', this.req(request)).then((response) => {
                if (!response.data.error) {
                    this.force_reconciled_mdl.show = false;
                }
            });
        },
        forceToReconciled() {
            this.force_reconciled_mdl.show = true;
            this.force_reconciled_mdl.trans_date = null;
        },
        submitUpdateClosingBal() {
            let request = {
                bank_code: this.bank,
                trans_date: this.update_closing_bal_mdl.trans_date,
                closing_bal: this.update_closing_bal_mdl.closing_bal,
            };
            this.$api.post('bank-statements/closing/update', this.req(request)).then((response) => {
                if (!response.data.error) {
                    this.update_closing_bal_mdl.show = false;
                }
            });
        },
        updateClosingBalance() {
            this.update_closing_bal_mdl.show = true;
            this.$refs.edit_closing_bal_date.changeDate(null);
            this.update_closing_bal_mdl.trans_date = null;
            this.update_closing_bal_mdl.closing_bal = null;
            if (this.last_closing.balance) {
                this.update_closing_bal_mdl.closing_bal = this.last_closing.balance;
            }
        },
        printTo(type) {
            this.last_opening = {};
            this.last_statement_amount = 0;
            this.last_closing_balance = 0;

            let dt_fr_m = moment(this.$filters.date2Sql(this.date_from));
            let dt_to_m = moment(this.$filters.date2Sql(this.date_to));

            if (dt_fr_m.isAfter(dt_to_m)) {
                this.$noty.error('Invalid date range, from date must be before the to date');
                return false;
            }

            if (this.bank) {
                let request = {
                    bank: this.bank,
                    from: this.date_from,
                    to: this.date_to,
                    print_to: type,
                };

                this.$api.post('bank-statements/page/list', this.req(request)).then((response) => {
                    let list = [];
                    if (response.data.list) {
                        list = response.data.list;
                    }
                    this.list.lines = list;
                    if (response.data.last_opening) {
                        this.last_opening = response.data.last_opening;
                    }
                    if (response.data.last_closing) {
                        this.last_closing = response.data.last_closing;
                    }

                    if (response.data.pdf) {
                        this.printDownload(response.data.pdf.file, response.data.pdf.filename, 'pdf');
                    } else if (response.data.excel) {
                        this.printDownload(response.data.excel.file, response.data.excel.filename, 'xlsx');
                    }
                });
            }
        },
        download(dl) {
            if (dl) {
                this.printTo('pdf');
            }
        },
        deleteStatement(row) {
            this.ask_delete_mdl.show = false;
            let request = {
                bank: this.bank,
            };

            if (!row.group_list) {
                request.trans_id = row.id;
            } else {
                let multi_trans_id = row.group_list.map((row) => {
                    return row.id;
                });
                request.multi_trans_id = JSON.stringify(multi_trans_id);
            }
            this.$api.post('auto-bank-recon/page/delete', this.req(request)).then((response) => {
                for (var trans_date in this.list.lines) {
                    let rows = this.list.lines[trans_date].rows;
                    for (var i = rows.length - 1; i >= 0; i--) {
                        if (row.id == rows[i].id) {
                            this.list.lines[trans_date].rows.splice(i, 1);
                            break;
                        }
                    }
                }
                this.ask_delete_mdl.line = {};
            });
        },
        askDeleteStatement(row) {
            this.ask_delete_mdl.show = true;
            this.ask_delete_mdl.line = row;
        },
        transactStatement(row) {
            let url = '?module=ar&command=receipting&method=file&fid=' + row.id;
            if (parseFloat(row.amount) < 0) {
                url = '?module=ap&command=payments';
            } else if (row.status == 'adjustment') {
                url = '?module=ar&command=receipting&useAmt=' + row.amount;
            }
            var win = window.open(url, '_blank');
            win.focus();
        },
        isNotWeekend(trans_date) {
            let not = true;
            let date = moment(trans_date);
            if (date.isoWeekday() == 6 || date.isoWeekday() == 7) {
                not = false;
            }
            return not;
        },
        rowFilter(row) {
            let show = true;

            if (this.list.filters.statuses.length > 0 && this.list.filters.statuses.indexOf(row.status) === -1) {
                show = false;
            }

            if (show && !this.list.filters.show_deleted && row.status == 'deleted') {
                show = false;
            }
            // if(show && (this.list.filters.amount != '' && this.list.filters.amount != null) ){
            //   show = this.matchRegex(row,['trans_amount'],this.list.filters.amount.toString());
            // }
            return show;
        },
        sortLines(type) {
            this.list.lines[type].rows.sort(
                this.sort_by(
                    { name: 'is_deleted', reverse: false },
                    { name: 'trans_amount', primer: parseFloat, reverse: true },
                ),
            );
        },
        cloneTableHeader() {
            let cells = document.getElementById('to-float-header').cells;
            let fixed_rows = document.getElementById('header-fixed').getElementsByTagName('tbody')[0].rows[1];
            for (var i = 0; i < cells.length; i++) {
                let cell = cells[i];
                fixed_rows.cells[i].style.width = cell.getBoundingClientRect().width + 'px';
            }
        },
        handleScroll(event) {
            //
            setTimeout(this.cloneTableHeader(), 800);
            // Any code to be executed when the window is scrolled
            let main_tbl = document.getElementById('main-table');
            let fixed_tbl = document.getElementById('header-fixed');
            let offset = document.getElementById('main_container').scrollTop;
            let pos = this.findPos(main_tbl);
            // fixed_tbl.style.width = (main_tbl.offsetWidth + 204) + "px"
            let fixed_tbl_style = window.getComputedStyle(fixed_tbl);
            let offtop = pos.top + 650;

            if (offset >= offtop && fixed_tbl_style.display === 'none') {
                fixed_tbl.style.display = 'block';
            } else if (offset < offtop) {
                fixed_tbl.style.display = 'none';
            }
        },
        findPos(element) {
            var top = 0,
                left = 0;
            do {
                top += element.offsetTop - element.scrollTop || 0;
                left += element.offsetLeft - element.scrollLeft || 0;
                element = element.offsetParent;
            } while (element);
            return {
                top: top,
                left: left,
            };
        },
        findPosOffsetTop(element) {
            var top = 0;
            do {
                top += element.offsetTop || 0;
                element = element.offsetParent;
            } while (element);
            return top;
        },
        goToNext() {
            let container = document.getElementById('main_container');
            let max_scroll = container.scrollHeight - container.clientHeight;
            let elem = document.getElementsByClassName('highlight');
            if (elem && elem.length > 0) {
                for (var i = 0; i < elem.length; i++) {
                    let el = elem[i];
                    let pos = this.findPosOffsetTop(el);
                    if (max_scroll > pos) {
                        if (pos - 150 > container.scrollTop) {
                            container.scrollTop = pos - 150;
                            break;
                        }
                    } else {
                        if (max_scroll == container.scrollTop) container.scrollTop = 0;
                        else container.scrollTop = max_scroll;
                        break;
                    }
                }
            }
        },
        setAmount(amount) {
            let filter_amt = this.list.filters.amount;

            if (filter_amt != null && filter_amt != '') {
                filter_amt = parseFloat(filter_amt.replace(/,/g, ''));
            }

            let txt = this.$filters.highlight(amount, filter_amt);
            if (!this.list.filters.amount || !isNaN(parseFloat(txt))) {
                return this.$filters.num2AmtStr(amount);
            }
            return txt;
        },
        updateHeaderLabels() {
            let currency = this.currency_symbol;
            this.list.headers.forEach(function (header) {
                if (header.field == 'trans_amount' || header.field == 'balance') {
                    header.label = header.label.replace('$', currency);
                }
            });
        },
    },
    mounted() {
        // this.$refs.date_from.changeDate("26/02/2021")
        // this.$refs.date_to.changeDate("26/02/2021")
        // this.$api.post('bank-statements/page/load')
        this.$refs.date_from.changeDate(moment().subtract(1, 'days').format('DD/MM/YYYY'));
        this.$refs.date_to.changeDate(moment().subtract(1, 'days').format('DD/MM/YYYY'));
        this.$api.post('bank-statements/page/load').then((response) => {
            if (response.data.bank_list) {
                this.setList(response.data.bank_list);
                if (this.bank_list && this.bank_list.length == 1) {
                    this.bank = this.bank_list[0].value;
                    this.printTo('screen');
                }
            }
            if (response.data.currency_symbol) {
                this.currency_symbol = response.data.currency_symbol;
                this.updateHeaderLabels();
            }
        });
        this.cloneTableHeader();
    },
    created() {
        document.getElementById('main_container').addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
        document.getElementById('main_container').removeEventListener('scroll', this.handleScroll);
    },
};
</script>
<style lang="scss">
@import '../../../../sass/variables';
.highlight {
    background-color: $c8-secondary !important;
    color: #ffffff;
}
</style>
