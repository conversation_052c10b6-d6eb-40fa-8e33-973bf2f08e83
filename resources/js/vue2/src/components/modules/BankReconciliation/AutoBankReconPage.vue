<template>
    <v-container
        fluid
        class="c8-page"
    >
        <cirrus-page-header title="Automated Bank Reconciliation" />
        <!-- FORM -->
        <div
            class="page-form"
            v-show="!show_results"
        >
            <v-chip
                v-if="no_bank_date"
                class="text-warning c8-full-width"
                style="margin-bottom: 15px"
                outlined
                >No Bank Statements found on this date</v-chip
            >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Bank Account</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select
                        placeholder="Select Bank Account"
                        :has-empty="false"
                        v-model="bank"
                        :options="bank_list"
                        @input="load()"
                    />
                    <div
                        v-show="bank"
                        style="float: right; width: 440px; margin-top: -30px; text-align: right"
                    >
                        <v-btn
                            @click="viewReport()"
                            color="success"
                            tile
                            small
                            ><v-icon
                                left
                                dark
                                size="18"
                                >mdi-file</v-icon
                            >
                            View Report</v-btn
                        >&nbsp;&nbsp;
                        <bank-statement-file
                            :date="bank_date"
                            :file-bank="bank"
                            :has-download="has_download"
                            @import="download"
                            @download="download"
                        />&nbsp;
                    </div>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Bank Statement Date</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-date-picker
                        v-model="bank_date"
                        id="date-from"
                        ref="bank_date"
                        @input="load()"
                    />
                    <div
                        style="display: inline-block; font-weight: 600; margin-left: 10px"
                        v-show="!show_results && Object.keys(list.lines).length > 0"
                    >
                        Last Reconciled: {{ last_recon_date }}
                        <v-btn
                            text
                            icon
                            style="margin-top: -3px"
                            color="primary"
                            x-small
                            @click="
                                edit_last_recon_mdl.show = true;
                                edit_last_recon_mdl.action = 'edit';
                            "
                            data-tooltip="Change last recon date"
                            ><v-icon>mdi-square-edit-outline</v-icon></v-btn
                        >
                    </div>
                </v-col>
            </v-row>
            <!-- <v-row class="form-row">
		  		<v-col xs="12" sm="2" md="2" class="form-label">&nbsp;</v-col>
		  		<v-col xs="12" sm="10" md="10" class="form-input">
		  			<v-checkbox
		  			  color="primary"
		  			  v-model="on_after_recon_only"
		  			  label="Only show on or after Last Reconciled Date"
		  			  @change="load()"
		  			/>
		  		</v-col>
		  	</v-row> -->
            <div v-show="!show_results && Object.keys(list.lines).length > 0">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Bank Statement Balance ({{ country_defaults.currency_symbol }})</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <!-- <cirrus-input inputFormat="dollar" :size="'42'" v-model="closing_bal" :edit_form="false" :error_msg="error_msg"></cirrus-input> -->
                        <v-text-field
                            dense
                            v-model="closing_bal_formatted"
                            @blur="formatClosingBal()"
                            style="max-width: 300px"
                        />
                        <!-- <cirrus-amount-input v-model="closing_bal" /> -->
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Calculated BS Balance ({{ country_defaults.currency_symbol }})</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                        ><span class="form-input-text">{{ calculated_bal | num2AmtStr }}</span></v-col
                    >
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Out Of Balance ({{ country_defaults.currency_symbol }}))</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                        ><span class="form-input-text">{{ out_of_bal | num2AmtStr }}</span></v-col
                    >
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Amount to Reconcile ({{ country_defaults.currency_symbol }})</v-col
                    >
                    <!-- <v-col xs="12" sm="10" md="10" class="form-input"><span class="form-input-text">{{bank_matches_total - bank_statements_total | num2AmtStr}}</span></v-col> -->
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                        ><span class="form-input-text">{{
                            bank_statements_unreconciled_total | num2AmtStr
                        }}</span></v-col
                    >
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                        >Amount to Present ({{ country_defaults.currency_symbol }})</v-col
                    >
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                        ><span class="form-input-text">{{ unpresented_total | num2AmtStr }}</span></v-col
                    >
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label"
                    ></v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input text-right"
                    >
                        <v-btn
                            color="warning"
                            tile
                            small
                            @click="addAdjustment()"
                        >
                            <v-icon
                                center
                                dark
                                size="18"
                                >mdi-plus</v-icon
                            >
                            Add Adjustment
                        </v-btn>
                        <div style="float: right; padding-left: 10px">
                            <v-btn
                                color="primary"
                                tile
                                small
                                @click="load()"
                                >Reset All</v-btn
                            >&nbsp;&nbsp;
                            <v-btn
                                color="success"
                                tile
                                small
                                @click="saveRecon()"
                                >Save Changes</v-btn
                            >&nbsp;&nbsp;
                        </div>
                    </v-col>
                </v-row>
            </div>
        </div>
        <!-- TABLE -->
        <div
            class="page-list"
            v-show="!show_results && Object.keys(list.lines).length > 0"
        >
            <!-- style="padding-right:138px" -->
            <div class="c8-page-table">
                <table
                    class="c8-float-header"
                    id="header-fixed"
                >
                    <tbody>
                        <!-- FILTERS -->
                        <tr>
                            <td
                                colspan="6"
                                style="background-color: #ffffff"
                            >
                                <div class="page-form no-line">
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="form-label required"
                                            >Bank Statement Balance ({{ country_defaults.currency_symbol }})</v-col
                                        >
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="form-input"
                                        >
                                            <v-text-field
                                                dense
                                                v-model="closing_bal_formatted"
                                                @blur="formatClosingBal()"
                                                style="max-width: 300px"
                                            />
                                        </v-col>
                                    </v-row>
                                </div>
                            </td>
                            <td
                                colspan="3"
                                class="sliced"
                                style="background-color: #ffffff; padding-left: 10px"
                            >
                                Calculated Balances
                            </td>
                            <td
                                colspan="2"
                                style="background-color: #ffffff"
                            >
                                {{ country_defaults.currency_symbol }}{{ calculated_bal | num2AmtStr }}
                            </td>
                            <td
                                colspan="1"
                                style="background-color: #ffffff"
                            >
                                Out Of Balance
                            </td>
                            <td
                                colspan="2"
                                style="background-color: #ffffff"
                            >
                                {{ country_defaults.currency_symbol }}{{ out_of_bal | num2AmtStr }}
                            </td>
                        </tr>
                        <tr id="to-float-header-clone-2">
                            <td
                                colspan="6"
                                style="background-color: #ffffff"
                            >
                                <div class="page-form no-line">
                                    <v-row class="form-row">
                                        <!-- <v-col xs="12" sm="2" md="2" class="form-label">Filter:</v-col> -->
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="text-left"
                                        >
                                            <div style="float: left; width: 60px; text-align: left">
                                                <v-btn
                                                    color="error"
                                                    tile
                                                    small
                                                    :disabled="Math.abs(parseFloat(statements_selected_total)) <= 0"
                                                    @click="deleteMultipleStatements()"
                                                    data-tooltip="Delete the checked Bank Statement(s)"
                                                    data-position="right center"
                                                >
                                                    <v-icon
                                                        center
                                                        dark
                                                        size="15"
                                                        >mdi-close</v-icon
                                                    >
                                                </v-btn>
                                            </div>
                                            <div style="display: inline-block; padding-left: 15px; margin-top: -2px">
                                                <v-row>
                                                    <v-col
                                                        xs="12"
                                                        sm="12"
                                                        md="12"
                                                        style="text-align: center"
                                                    >
                                                        <div
                                                            style="display: inline-block; min-width: 100px"
                                                            v-for="(st, st_index) in status_opts"
                                                            :key="'ch-fl-' + st_index"
                                                        >
                                                            <v-checkbox
                                                                v-model="list.filters.statuses"
                                                                color="primary"
                                                                :label="st.label"
                                                                :value="st.value"
                                                            />
                                                        </div>
                                                    </v-col>
                                                    <!-- <v-col xs="12" sm="6" md="6" style="padding:0 !important;">
					  					  			<div class="page-form no-line">
					  					  				<v-row class="form-row">
					  					  				  <v-col xs="12" sm="4" md="4" class="form-label">Amount</v-col>
					  					  				  <v-col xs="12" sm="8" md="8" class="form-input">
					  					  				  	<v-text-field dense v-model="list.filters.amount" style="max-width:100px;"/>
					  					  				  </v-col>
					  					  				</v-row>
					  					  			</div>
					  			  				</v-col> -->
                                                </v-row>
                                            </div>
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="text-right"
                                        >
                                            <div style="padding-top: 7px">
                                                Total Selected :
                                                <span
                                                    style="font-weight: 600"
                                                    :class="{
                                                        'green--text':
                                                            parseFloat(statements_selected_total) != 0 &&
                                                            parseFloat(statements_selected_total) ==
                                                                parseFloat(to_match_selected_total),
                                                    }"
                                                >
                                                    {{ parseFloat(statements_selected_total) | num2AmtStr }}
                                                </span>
                                            </div>
                                        </v-col>
                                    </v-row>
                                </div>
                            </td>
                            <td
                                colspan="8"
                                class="sliced"
                                valign="top"
                                style="background-color: #ffffff"
                            >
                                <div class="page-form no-line">
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="text-left"
                                        >
                                            <div style="padding-top: 7px; padding-left: 12px">
                                                <span
                                                    style="font-weight: 600"
                                                    :class="{
                                                        'green--text':
                                                            parseFloat(statements_selected_total) != 0 &&
                                                            parseFloat(statements_selected_total) ==
                                                                parseFloat(to_match_selected_total),
                                                    }"
                                                    >{{ parseFloat(to_match_selected_total) | num2AmtStr }}</span
                                                >
                                                <span v-if="Math.abs(parseFloat(statement_match_total_diff)) > 0">
                                                    &nbsp; |&nbsp; Difference: &nbsp;
                                                    <span
                                                        class="red--text text--lighten-1"
                                                        style="font-weight: 600"
                                                        >{{ statement_match_total_diff | num2AmtStr }}</span
                                                    >
                                                </span>
                                            </div>
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="text-right"
                                        >
                                            <v-btn
                                                color="success"
                                                tile
                                                small
                                                style="display: inline-block; margin-left: 5px; margin-right: 5px"
                                                :disabled="
                                                    statements_selected_total == 0 ||
                                                    to_match_selected_total == 0 ||
                                                    parseFloat(statements_selected_total) !=
                                                        parseFloat(to_match_selected_total)
                                                "
                                                @click="saveMatch()"
                                            >
                                                <span> Accept Match </span>
                                            </v-btn>
                                            <v-btn
                                                color="error"
                                                tile
                                                small
                                                style="display: inline-block; margin-left: 5px; margin-right: 5px"
                                                :disabled="!statements_selected || statements_selected.length == 0"
                                                @click="deselectRows()"
                                            >
                                                Cancel
                                            </v-btn>
                                        </v-col>
                                    </v-row>
                                </div>
                            </td>
                        </tr>
                        <!-- COLUMN HEADERS -->
                        <tr
                            class="c8-page-table-row-header"
                            id="to-float-header-clone-3"
                        >
                            <td
                                v-for="(hdr, index) in list.headers"
                                :key="'hdr-' + index"
                                :class="hdr.add_class"
                            >
                                <span>{{ hdr.label }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table id="main-table">
                    <tbody>
                        <!-- HEADER -->
                        <tr
                            class="c8-page-table-header"
                            id="to-float-header-1"
                        >
                            <td
                                colspan="6"
                                class="text-center"
                                id="main-table-statements-col"
                            >
                                Bank Statement Transactions
                            </td>
                            <td
                                colspan="8"
                                class="text-center"
                                id="main-table-suggesteds-col"
                            >
                                Cash Book Unreconciled Transactions
                            </td>
                        </tr>
                        <!-- FILTERS -->
                        <tr id="to-float-header-2">
                            <td colspan="6">
                                <div class="page-form no-line">
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="9"
                                            md="9"
                                            class="text-left"
                                        >
                                            <div style="float: left; width: 60px; text-align: left">
                                                <v-btn
                                                    color="error"
                                                    tile
                                                    small
                                                    :disabled="Math.abs(parseFloat(statements_selected_total)) <= 0"
                                                    @click="deleteMultipleStatements()"
                                                    data-tooltip="Delete the checked Bank Statement(s)"
                                                    data-position="right center"
                                                >
                                                    <v-icon
                                                        center
                                                        dark
                                                        size="15"
                                                        >mdi-close</v-icon
                                                    >
                                                </v-btn>
                                            </div>
                                            <div style="display: inline-block; padding-left: 15px; margin-top: -2px">
                                                <v-row>
                                                    <v-col
                                                        xs="12"
                                                        sm="12"
                                                        md="12"
                                                        style="text-align: center"
                                                    >
                                                        <div
                                                            style="display: inline-block; min-width: 100px"
                                                            v-for="(st, st_index) in status_opts"
                                                            :key="'ch-fl-' + st_index"
                                                        >
                                                            <v-checkbox
                                                                v-model="list.filters.statuses"
                                                                color="primary"
                                                                :label="st.label"
                                                                :value="st.value"
                                                            />
                                                        </div>
                                                    </v-col>
                                                    <!-- <v-col xs="12" sm="6" md="6" style="padding:0 !important;">
											  			<div class="page-form no-line">
											  				<v-row class="form-row">
											  				  <v-col xs="12" sm="4" md="4" class="form-label">Amount</v-col>
											  				  <v-col xs="12" sm="8" md="8" class="form-input">
											  				  	<v-text-field dense v-model="list.filters.amount" style="max-width:100px;"/>
											  				  </v-col>
											  				</v-row>
											  			</div>
									  				</v-col> -->
                                                </v-row>
                                            </div>
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="3"
                                            md="3"
                                            class="text-right"
                                        >
                                            <div style="padding-top: 7px">
                                                Total Selected :
                                                <span
                                                    style="font-weight: 600"
                                                    :class="{
                                                        'green--text':
                                                            parseFloat(statements_selected_total) != 0 &&
                                                            parseFloat(statements_selected_total) ==
                                                                parseFloat(to_match_selected_total),
                                                    }"
                                                >
                                                    {{ parseFloat(statements_selected_total) | num2AmtStr }}
                                                </span>
                                            </div>
                                        </v-col>
                                    </v-row>
                                </div>
                            </td>
                            <td
                                colspan="8"
                                class="sliced"
                                valign="top"
                            >
                                <div class="page-form no-line">
                                    <v-row class="form-row">
                                        <v-col
                                            xs="12"
                                            sm="4"
                                            md="4"
                                            class="text-left"
                                        >
                                            <div style="padding-top: 7px; padding-left: 12px">
                                                <span
                                                    style="font-weight: 600"
                                                    :class="{
                                                        'green--text':
                                                            parseFloat(statements_selected_total) != 0 &&
                                                            parseFloat(statements_selected_total) ==
                                                                parseFloat(to_match_selected_total),
                                                    }"
                                                    >{{ parseFloat(to_match_selected_total) | num2AmtStr }}</span
                                                >
                                                <span v-if="Math.abs(parseFloat(statement_match_total_diff)) > 0">
                                                    &nbsp; |&nbsp; Difference: &nbsp;
                                                    <span
                                                        class="red--text text--lighten-1"
                                                        style="font-weight: 600"
                                                        >{{ statement_match_total_diff | num2AmtStr }}</span
                                                    >
                                                </span>
                                            </div>
                                        </v-col>
                                        <v-col
                                            xs="12"
                                            sm="8"
                                            md="8"
                                            class="text-right"
                                        >
                                            <v-btn
                                                color="success"
                                                tile
                                                small
                                                style="display: inline-block; margin-left: 5px; margin-right: 5px"
                                                :disabled="
                                                    statements_selected_total == 0 ||
                                                    to_match_selected_total == 0 ||
                                                    parseFloat(statements_selected_total) !=
                                                        parseFloat(to_match_selected_total)
                                                "
                                                @click="saveMatch()"
                                            >
                                                <span> Accept Match </span>
                                            </v-btn>
                                            <v-btn
                                                color="error"
                                                tile
                                                small
                                                style="display: inline-block; margin-left: 5px; margin-right: 5px"
                                                :disabled="!statements_selected || statements_selected.length == 0"
                                                @click="deselectRows()"
                                            >
                                                Cancel
                                            </v-btn>
                                        </v-col>
                                    </v-row>
                                </div>
                            </td>
                        </tr>
                        <!-- COLUMN HEADERS -->
                        <tr
                            class="c8-page-table-row-header"
                            id="to-float-header-3"
                        >
                            <td
                                v-for="(hdr, index) in list.headers"
                                :key="'hdr-' + index"
                                :class="hdr.add_class"
                            >
                                <span>{{ hdr.label }}</span>
                            </td>
                        </tr>
                        <!-- MATCH LIST -->
                        <template v-for="(trans, trans_date) in recons">
                            <!-- RECON LIST LINES -->
                            <template v-for="(row, row_index) in trans.rows">
                                <!-- RECON LIST ROWS -->
                                <tr
                                    :class="[setRowColor(row)]"
                                    v-show="rowFilter(row)"
                                >
                                    <template v-for="(hdr_row, hdr_row_index) in list.headers">
                                        <td
                                            v-if="
                                                row.hasOwnProperty(hdr_row.field) &&
                                                list.headers_excl.indexOf(hdr_row.field) === -1
                                            "
                                            :class="[hdr_row.add_class]"
                                        >
                                            <!-- STATEMENT CHECKBOX -->
                                            <div v-if="hdr_row.field == 'statement_selected'">
                                                <v-checkbox
                                                    v-model="row[hdr_row.field]"
                                                    color="primary"
                                                    v-if="
                                                        row.status != 'reconciled' &&
                                                        row.method == 'import' &&
                                                        !row.is_matched &&
                                                        row.statement_amount != null &&
                                                        row.statement_details &&
                                                        !row.statement_details.remove_date &&
                                                        checkIfSelectable(row.statement_amount)
                                                    "
                                                />
                                            </div>
                                            <span
                                                v-else-if="hdr_row.field == 'statement_date'"
                                                :class="{ 'statement-sub-row': row.is_sub }"
                                            >
                                                {{ row[hdr_row.field] | sql2Date }}
                                            </span>
                                            <span
                                                v-else-if="hdr_row.field == 'statement_desc'"
                                                :class="{ 'statement-sub-row': row.is_sub }"
                                            >
                                                <!-- joinby-{{row.temp_join_by}}<br><br>is-matched{{row.is_matched}}<br><br>{{row.is_matched && row.temp_join_by == 'mtc_multi_sta'}}<br><br> -->
                                                <template v-if="row.is_matched && row.temp_join_by == 'mtc_multi_sta'"
                                                    ><strong>{{ 'Multiple' }}</strong></template
                                                >
                                                <template v-else>{{ row[hdr_row.field] }}</template>
                                            </span>
                                            <span
                                                v-else-if="hdr_row.field == 'statement_amount'"
                                                :class="{ 'statement-sub-row': row.is_sub }"
                                            >
                                                <!-- {{row[hdr_row.field] | num2AmtStr}} -->
                                                <template v-if="row.is_matched && row.temp_join_by == 'mtc_multi_sta'"
                                                    ><strong>{{
                                                        getMatchTotalAmount(row) | num2AmtStr
                                                    }}</strong></template
                                                >
                                                <template v-else>
                                                    <span :class="setStatementAmountColor(row)">{{
                                                        row[hdr_row.field] | num2AmtStr
                                                    }}</span>
                                                </template>
                                                <v-btn
                                                    text
                                                    icon
                                                    color="primary"
                                                    x-small
                                                    data-tooltip="View Breakdown"
                                                    @click="viewTotalBreakdown(row)"
                                                    style="margin-left: 5px; margin-top: -3px"
                                                    v-if="row.statement_details.group_list"
                                                >
                                                    <v-icon
                                                        center
                                                        dark
                                                        size="18"
                                                        >mdi-open-in-new</v-icon
                                                    >
                                                </v-btn>
                                            </span>
                                            <span
                                                v-else-if="hdr_row.field == 'method'"
                                                :class="{ 'statement-sub-row': row.is_sub }"
                                                >{{ row[hdr_row.field] | strtoupper }}</span
                                            >
                                            <!-- CIRRUS8 SUGGESTED CHECKBOX -->
                                            <div
                                                v-else-if="hdr_row.field == 'match_selected'"
                                                style="min-width: 34px"
                                            >
                                                <div class="match-link-btn">
                                                    <v-btn
                                                        text
                                                        icon
                                                        color="success"
                                                        x-small
                                                        data-tooltip="View Match Details"
                                                        @click="viewMatchDetails(row)"
                                                        v-if="
                                                            row.is_matched &&
                                                            (row.status == 'approved' || row.status == 'reconciled')
                                                        "
                                                    >
                                                        <v-icon
                                                            center
                                                            dark
                                                            size="24"
                                                            >mdi-link-box</v-icon
                                                        >
                                                    </v-btn>
                                                </div>
                                                <v-checkbox
                                                    v-model="row[hdr_row.field]"
                                                    color="primary"
                                                    v-if="
                                                        row.status != 'reconciled' &&
                                                        !row.is_matched &&
                                                        row.match_amount != null &&
                                                        statements_selected &&
                                                        statements_selected.length > 0 &&
                                                        row.match_props &&
                                                        (row.match_props.presented_date == '' ||
                                                            row.match_props.presented_date == 'present') &&
                                                        checkIfSelectable(row.match_amount)
                                                    "
                                                />
                                            </div>
                                            <span v-else-if="hdr_row.field == 'match_trans_type'">
                                                <template
                                                    v-if="row.match_details && row.match_details.trans_type_name"
                                                    >{{ row.match_details.trans_type_name }}</template
                                                >
                                                <!-- <template v-else-if="row.match_props && row.match_props.trans_type_name">{{row.match_props.trans_type_name}}</template> -->
                                            </span>
                                            <span v-else-if="hdr_row.field == 'match_date'">
                                                <template
                                                    v-if="row.is_matched && row.temp_join_by == 'sta_multi_mtc'"
                                                    >{{ ' ' }}</template
                                                >
                                                <template v-else>{{ row[hdr_row.field] | sql2Date }}</template>
                                            </span>
                                            <span v-else-if="hdr_row.field == 'match_ref'">
                                                <template
                                                    v-if="row.is_matched && row.temp_join_by == 'sta_multi_mtc'"
                                                    >{{ ' ' }}</template
                                                >
                                                <template v-else>{{ row[hdr_row.field] }}</template>
                                            </span>
                                            <span v-else-if="hdr_row.field == 'match_payee'">
                                                <template v-if="row.is_matched && row.temp_join_by == 'sta_multi_mtc'">
                                                    <strong>{{ 'Multiple' }}</strong>
                                                    <v-btn
                                                        text
                                                        icon
                                                        color="primary"
                                                        x-small
                                                        data-tooltip="View Matched Amounts"
                                                        @click="viewMatchDetails(row)"
                                                        style="margin-left: 5px; margin-top: -3px"
                                                    >
                                                        <v-icon
                                                            center
                                                            dark
                                                            size="18"
                                                            >mdi-open-in-new</v-icon
                                                        >
                                                    </v-btn>
                                                </template>
                                                <template v-else>{{ row[hdr_row.field] }}</template>
                                            </span>
                                            <span v-else-if="hdr_row.field == 'match_amount'">
                                                <template v-if="row.is_matched && row.temp_join_by == 'sta_multi_mtc'"
                                                    ><strong>{{
                                                        getMatchTotalAmount(row) | num2AmtStr
                                                    }}</strong></template
                                                >
                                                <template v-else>{{ row[hdr_row.field] | num2AmtStr }}</template>

                                                <v-btn
                                                    text
                                                    icon
                                                    color="primary"
                                                    x-small
                                                    data-tooltip="View Breakdown"
                                                    @click="viewApBreakdown(row)"
                                                    style="margin-left: 5px; margin-top: -3px"
                                                    v-if="
                                                        row.match_ref &&
                                                        !row.is_matched &&
                                                        !row.temp_join_by &&
                                                        row.match_props &&
                                                        row.match_props.pmbg_ref
                                                    "
                                                >
                                                    <v-icon
                                                        center
                                                        dark
                                                        size="18"
                                                        >mdi-open-in-new</v-icon
                                                    >
                                                </v-btn>
                                            </span>
                                            <span v-else>{{ row[hdr_row.field] }}</span>
                                        </td>
                                        <td
                                            v-else-if="hdr_row.field == 'status'"
                                            :class="[hdr_row.add_class]"
                                            style="min-width: 80px"
                                        >
                                            <template v-if="row.statement_amount != null">
                                                <span :class="{ 'statement-sub-row': row.is_sub }">{{
                                                    status_txt[row.status]
                                                }}</span>
                                            </template>
                                        </td>
                                        <td
                                            v-else-if="hdr_row.field == 'statement_links'"
                                            :class="[hdr_row.add_class]"
                                            style="min-width: 165px"
                                        >
                                            <template
                                                v-if="
                                                    !row.is_matched &&
                                                    row.statement_amount &&
                                                    row.orig_status != 'adjustment'
                                                "
                                            >
                                                <v-btn
                                                    text
                                                    icon
                                                    color="error"
                                                    x-small
                                                    data-tooltip="Delete Statement"
                                                    @click="askDeleteStatement(row)"
                                                    style="margin: 0 2.5px"
                                                    ><v-icon>mdi-close</v-icon></v-btn
                                                >
                                            </template>
                                            <template v-if="!row.is_matched">
                                                <template
                                                    v-if="
                                                        row.statement_amount != null && row.orig_status == 'adjustment'
                                                    "
                                                >
                                                    <div
                                                        class="page-form no-line"
                                                        style="display: inline-block; width: 165px"
                                                    >
                                                        <v-row class="form-row c8-no-padding">
                                                            <v-col
                                                                xs="12"
                                                                sm="12"
                                                                class="form-input c8-no-padding"
                                                            >
                                                                <!-- <template v-if="row.method == 'import'">
							                                      <v-btn text icon color="success" x-small @click="removeAsAdjustment(trans_date,row_index,row)"><v-icon>mdi-reload</v-icon></v-btn>
							                                    </template> -->
                                                                <v-btn
                                                                    text
                                                                    icon
                                                                    color="primary"
                                                                    x-small
                                                                    :data-tooltip="
                                                                        row.statement_amount < 0
                                                                            ? 'Process Payment'
                                                                            : 'Process Receipt'
                                                                    "
                                                                    @click="transactStatement(row, true)"
                                                                    ><v-icon>mdi-file-move</v-icon></v-btn
                                                                >
                                                                <v-btn
                                                                    text
                                                                    icon
                                                                    color="error"
                                                                    x-small
                                                                    data-tooltip="Delete Adjustment"
                                                                    v-if="checkIfDeletable(row.statement_details)"
                                                                    @click="
                                                                        completeDeleteAdjustmentAsk(
                                                                            trans_date,
                                                                            row_index,
                                                                            row,
                                                                        )
                                                                    "
                                                                >
                                                                    <v-icon>mdi-file-document-box-remove</v-icon>
                                                                </v-btn>

                                                                <!-- <div style="display: inline-block;">
							                                      <v-text-field dense v-model="row.statement_details.remove_date"/>
							                                    </div>

							                                    <cirrus-date-picker
							                                      :id="'remove-date-'+(trans_date+'-'+row_index)"
							                                      :ref="'remove-date-'+(trans_date+'-'+row_index)"
							                                      :icon-only="true"
							                                      :do-not-set="true"
							                                      @input="setAdjustmentRemoveDate(row.statement_details,$event)"
							                                    /> -->
                                                                <cirrus-date-picker
                                                                    v-if="row.adj_serial_no"
                                                                    v-model="row.statement_details.remove_date"
                                                                    :id="
                                                                        'remove-date-' +
                                                                        (trans_date +
                                                                            '-' +
                                                                            row_index +
                                                                            row.adj_serial_no)
                                                                    "
                                                                    :ref="
                                                                        'remove-date-' +
                                                                        (trans_date +
                                                                            '-' +
                                                                            row_index +
                                                                            row.adj_serial_no)
                                                                    "
                                                                    :do-not-set="false"
                                                                    @input="
                                                                        setAdjustmentRemoveDate(
                                                                            row.statement_details,
                                                                            $event,
                                                                        )
                                                                    "
                                                                />
                                                            </v-col>
                                                        </v-row>
                                                    </div>
                                                </template>
                                                <template
                                                    v-else-if="
                                                        row.statement_amount != null &&
                                                        row.orig_status == 'pending' &&
                                                        row.status == 'adjustment'
                                                    "
                                                >
                                                    <v-btn
                                                        text
                                                        icon
                                                        color="error"
                                                        x-small
                                                        data-tooltip="Remove as Adjustment"
                                                        @click="removeAsAdjustment(trans_date, row_index, row)"
                                                        style="margin: 0 2.5px"
                                                        ><v-icon>mdi-file-document-box-remove</v-icon></v-btn
                                                    >
                                                    <v-btn
                                                        text
                                                        icon
                                                        color="primary"
                                                        x-small
                                                        :data-tooltip="
                                                            row.statement_amount < 0
                                                                ? 'Process Payment'
                                                                : 'Process Receipt'
                                                        "
                                                        @click="transactStatement(row)"
                                                        style="margin: 0 2.5px"
                                                        ><v-icon>mdi-file-move</v-icon></v-btn
                                                    >
                                                </template>
                                                <template
                                                    v-else-if="row.statement_amount != null && row.status == 'pending'"
                                                >
                                                    <v-btn
                                                        text
                                                        icon
                                                        data-tooltip="Add as Adjustment"
                                                        color="warning"
                                                        x-small
                                                        @click="addAsAdjustment(trans_date, row_index, row)"
                                                        style="margin: 0 2.5px"
                                                        ><v-icon>mdi-file-document-box-plus</v-icon></v-btn
                                                    >
                                                    <v-btn
                                                        text
                                                        icon
                                                        :data-tooltip="
                                                            row.statement_amount < 0
                                                                ? 'Process Payment'
                                                                : 'Process Receipt'
                                                        "
                                                        color="primary"
                                                        x-small
                                                        @click="transactStatement(row)"
                                                        style="margin: 0 2.5px"
                                                        ><v-icon>mdi-file-move</v-icon></v-btn
                                                    >
                                                </template>
                                                <template v-if="row.status == 'reconciled'">
                                                    <v-btn
                                                        text
                                                        icon
                                                        data-tooltip="Unreconcile Statement"
                                                        color="warning"
                                                        x-small
                                                        @click="setMatchStatus(row, 'unreconcile')"
                                                        style="margin: 0 10px"
                                                        ><v-icon>mdi-reload</v-icon></v-btn
                                                    >
                                                </template>
                                            </template>
                                            <template v-else>
                                                <template v-if="row.status == 'reconciled'">
                                                    <v-btn
                                                        text
                                                        icon
                                                        data-tooltip="Unreconcile Statement"
                                                        color="warning"
                                                        x-small
                                                        @click="setMatchStatus(row, 'unreconcile')"
                                                        style="margin: 0 10px"
                                                        ><v-icon>mdi-reload</v-icon></v-btn
                                                    >
                                                </template>
                                            </template>
                                        </td>
                                        <td
                                            v-else-if="hdr_row.field == 'actions'"
                                            :class="[hdr_row.add_class]"
                                            style="min-width: 80px"
                                        >
                                            <template
                                                v-if="
                                                    row.match_amount != null &&
                                                    row.is_matched &&
                                                    row.status == 'pending'
                                                "
                                            >
                                                <v-btn
                                                    text
                                                    icon
                                                    color="success"
                                                    x-small
                                                    @click="setMatchStatus(row, 'approved')"
                                                    data-tooltip="Accept Match"
                                                    ><v-icon>mdi-check</v-icon></v-btn
                                                >
                                                <v-btn
                                                    text
                                                    icon
                                                    color="error"
                                                    x-small
                                                    @click="setMatchStatus(row, 'declined')"
                                                    data-tooltip="Remove Match"
                                                    ><v-icon>mdi-close</v-icon></v-btn
                                                >
                                            </template>
                                            <template
                                                v-if="
                                                    row.match_amount != null &&
                                                    row.is_matched &&
                                                    row.status == 'approved'
                                                "
                                            >
                                                <v-btn
                                                    text
                                                    icon
                                                    color="error"
                                                    x-small
                                                    @click="setMatchStatus(row, 'declined')"
                                                    data-tooltip="Remove Match"
                                                    ><v-icon>mdi-close</v-icon></v-btn
                                                >
                                            </template>
                                        </td>
                                        <!-- SUGGESTED MATCH COLUMNS -->
                                        <td
                                            v-else-if="hdr_row.field == 'match_presented_date'"
                                            :class="[hdr_row.add_class]"
                                            style="min-width: 165px"
                                        >
                                            <div
                                                class="page-form no-line"
                                                style="width: 130px; float: right"
                                                v-if="row.match_amount != null"
                                                :id="'presented-date-' + (trans_date + '-' + row_index) + '-con'"
                                            >
                                                <v-row class="form-row c8-no-padding">
                                                    <v-col
                                                        xs="12"
                                                        sm="12"
                                                        class="form-input c8-no-padding"
                                                    >
                                                        <template v-if="row.match_details">
                                                            <v-btn
                                                                text
                                                                icon
                                                                color="error"
                                                                x-small
                                                                v-if="
                                                                    !row.match_details.already_presented &&
                                                                    row.match_details.presented_date != '' &&
                                                                    row.match_details.presented_date != 'present' &&
                                                                    row.status != 'approved'
                                                                "
                                                                @click="setPresentDate(row.match_details, 'present')"
                                                            >
                                                                <v-icon>mdi-close</v-icon>
                                                            </v-btn>
                                                            <div style="display: inline-block">
                                                                <v-text-field
                                                                    dense
                                                                    v-model="row.match_details.presented_date"
                                                                    :disabled="
                                                                        row.match_details.already_presented ||
                                                                        row.status == 'approved'
                                                                    "
                                                                    @click="setPresentDate(row.match_details)"
                                                                />
                                                            </div>
                                                            <cirrus-date-picker
                                                                :id="'presented-date-' + (trans_date + '-' + row_index)"
                                                                :ref="
                                                                    'presented-date-' + (trans_date + '-' + row_index)
                                                                "
                                                                :icon-only="true"
                                                                :do-not-set="true"
                                                                :disabled="
                                                                    row.match_details.already_presented ||
                                                                    row.status == 'approved'
                                                                "
                                                                @input="setPresentDate(row.match_details, $event)"
                                                            />
                                                        </template>
                                                        <template v-else>
                                                            <v-btn
                                                                text
                                                                icon
                                                                color="error"
                                                                x-small
                                                                data-tooltip="Unpresent"
                                                                v-if="
                                                                    !row.match_props.already_presented &&
                                                                    row.match_props.presented_date != '' &&
                                                                    row.match_props.presented_date != 'present'
                                                                "
                                                                @blur="setPresentDate(row.match_props, 'present')"
                                                            >
                                                                <v-icon>mdi-close</v-icon>
                                                            </v-btn>
                                                            <v-btn
                                                                text
                                                                icon
                                                                color="success"
                                                                x-small
                                                                data-tooltip="Unpresent"
                                                                v-else-if="
                                                                    row.match_props.already_presented &&
                                                                    row.match_props.presented_date != ''
                                                                "
                                                                @click="setPresentDate(row.match_props, 'unpresent')"
                                                            >
                                                                <v-icon>mdi-reload</v-icon>
                                                            </v-btn>

                                                            <div style="display: inline-block">
                                                                <v-text-field
                                                                    dense
                                                                    v-model="row.match_props.presented_date"
                                                                    :disabled="row.match_props.already_presented"
                                                                    @click="setPresentDate(row.match_props)"
                                                                />
                                                            </div>
                                                            <cirrus-date-picker
                                                                :id="'presented-date-' + (trans_date + '-' + row_index)"
                                                                :ref="
                                                                    'presented-date-' + (trans_date + '-' + row_index)
                                                                "
                                                                :icon-only="true"
                                                                :do-not-set="true"
                                                                :disabled="row.match_props.already_presented"
                                                                @input="setPresentDate(row.match_props, $event)"
                                                            />
                                                        </template>
                                                    </v-col>
                                                </v-row>
                                            </div>
                                        </td>
                                        <td
                                            v-else
                                            :class="[hdr_row.add_class]"
                                        >
                                            &nbsp;
                                        </td>
                                    </template>
                                </tr>
                            </template>
                        </template>
                        <!-- RECON LIST TOTALS -->
                        <tr class="c8-page-table-footer">
                            <td
                                colspan="4"
                                class="text-right"
                            >
                                Total Amount
                            </td>
                            <td class="text-right">
                                {{ country_defaults.currency_symbol }}{{ bank_statements_shown_total | num2AmtStr }}
                            </td>
                            <td></td>
                            <td class="sliced"></td>
                            <td
                                colspan="4"
                                class="text-right"
                            >
                                Total Amount
                            </td>
                            <td class="text-right">
                                {{ country_defaults.currency_symbol }}{{ bank_matches_total | num2AmtStr }}
                            </td>
                            <td
                                colspan="2"
                                class="text-right"
                            ></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div
            class="page-form"
            v-show="!show_results && Object.keys(list.lines).length > 0"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                ></v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input text-right"
                >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="load()"
                        >Reset All</v-btn
                    >&nbsp;&nbsp;
                    <v-btn
                        color="success"
                        tile
                        small
                        @click="saveRecon()"
                        >Save Changes</v-btn
                    >&nbsp;&nbsp;
                </v-col>
            </v-row>
        </div>
        <!-- RESULTS VIEW -->
        <div
            class="page-form"
            v-show="show_results"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Bank Account</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input text-only"
                >
                    {{ bank_name }}
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Bank Statement Date</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input text-only"
                >
                    {{ bank_date }}
                </v-col>
            </v-row>

            <div class="page-list">
                <div class="c8-page-table">
                    <table id="main-table">
                        <tbody>
                            <!-- HEADER -->
                            <tr class="c8-page-table-header">
                                <td
                                    colspan="2"
                                    id="main-table-statements-col"
                                >
                                    Presentation Details
                                </td>
                            </tr>
                            <tr
                                v-if="recon_results.length > 0"
                                v-for="(res, index) in recon_results"
                                :key="'results-ln-' + index"
                            >
                                <td>
                                    <span v-html="res.message"></span>
                                </td>
                            </tr>
                            <tr v-if="recon_results.length == 0">
                                <td>No changes found.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                ></v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input text-right"
                >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="resetRecon()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-reply</v-icon
                        >
                        Return</v-btn
                    >&nbsp;&nbsp;
                </v-col>
            </v-row>
        </div>
        <!-- MODALS     -->
        <v-dialog
            v-model="ask_delete_mdl.show"
            persistent
            :max-width="ask_delete_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    &nbsp;
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="ask_delete_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <p
                        class="c8-padding-1"
                        v-if="!ask_delete_mdl.multiple"
                    >
                        Are you sure you want to delete this bank statement line?
                    </p>
                    <p
                        class="c8-padding-1"
                        v-else
                    >
                        Are you sure you want to delete the checked bank statement(s)?
                    </p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="error"
                        tile
                        small
                        @click="deleteStatement(ask_delete_mdl.line)"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-check</v-icon
                        >Delete</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="ask_delete_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="edit_last_recon_mdl.show"
            persistent
            :max-width="edit_last_recon_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Change Last Recon Date
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="edit_last_recon_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <template v-if="edit_last_recon_mdl.action == 'edit'">
                        <p style="padding: 0.5rem 1rem">
                            <v-icon size="14">mdi-alert</v-icon> Warning: Only proceed with the change if you need to
                            backdate changes and are authorised to do so by your company’s license.
                        </p>
                        <p style="padding: 0.5rem 1rem">
                            If you continue and open prior bank reconciliation periods then you need to balance the bank
                            reconciliation again, print all supporting reports and update the reconciliation.
                        </p>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="4"
                                    md="4"
                                    class="form-label required"
                                    >Last Recon Date</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="8"
                                    md="8"
                                    class="form-input"
                                >
                                    <cirrus-date-picker
                                        v-model="edit_last_recon_mdl.recon_date"
                                        id="edit-last-recon-modal-recon-date"
                                        ref="edit_last_recon_modal_recon_date"
                                    />
                                </v-col>
                            </v-row>
                        </div>
                    </template>
                    <template v-else-if="edit_last_recon_mdl.action == 'save'">
                        <p style="padding: 1rem 1rem">
                            <v-icon size="14">mdi-alert</v-icon> Warning: You will not be able to process transactions
                            before <strong>{{ edit_last_recon_mdl.save_date_txt }}</strong> after you have updated the
                            bank reconciliation. Click Save to continue.
                        </p>
                    </template>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="success"
                        tile
                        small
                        @click="saveNewLastReconDate()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-content-save</v-icon
                        >Save</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="edit_last_recon_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="view_report_mdl.show"
            persistent
            :max-width="view_report_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    {{ bank_name }}
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="view_report_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-list">
                        <div class="c8-page-table">
                            <table id="main-table">
                                <tbody>
                                    <tr>
                                        <td>Last Reconciled</td>
                                        <td>{{ last_recon_date }}</td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td>This Reconcile</td>
                                        <td class="text-right">{{ bank_date }}</td>
                                    </tr>
                                    <tr>
                                        <td>Bank Statement Balance</td>
                                        <td>{{ closing_bal ? closing_bal : 0 | num2AmtStr }}</td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td>Cash Book Opening Balance</td>
                                        <td class="text-right">
                                            {{ view_report_mdl.cash_book_opening_bal | num2AmtStr }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>+ Outstanding Deposits</td>
                                        <td>{{ view_report_mdl.totals.DIR | num2AmtStr }}</td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td>+ Receipt this Period</td>
                                        <td class="text-right">{{ view_report_mdl.receipts | num2AmtStr }}</td>
                                    </tr>
                                    <tr>
                                        <td>- Unpresented Cheques</td>
                                        <td>{{ (view_report_mdl.totals.CHQ * -1) | num2AmtStr }}</td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td>+ Payment this Period</td>
                                        <td class="text-right">{{ view_report_mdl.payments | num2AmtStr }}</td>
                                    </tr>
                                    <tr>
                                        <td>- Unpresented EFts</td>
                                        <td>{{ (view_report_mdl.totals.BPA * -1) | num2AmtStr }}</td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td></td>
                                        <td class="text-right"></td>
                                    </tr>
                                    <tr>
                                        <td>- Reconciliation Adjustments</td>
                                        <td>{{ (view_report_mdl.totals.ADJ * -1) | num2AmtStr }}</td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td></td>
                                        <td class="text-right"></td>
                                    </tr>
                                    <tr>
                                        <td>Adjusted Balance</td>
                                        <td>{{ view_report_mdl.adjusted | num2AmtStr }}</td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td>Cash Book Closing Balance</td>
                                        <td class="text-right">
                                            {{
                                                (view_report_mdl.cash_book_opening_bal +
                                                    view_report_mdl.receipts -
                                                    view_report_mdl.payments * -1)
                                                    | num2AmtStr
                                            }}
                                        </td>
                                    </tr>
                                    <tr class="c8-page-table-row-header">
                                        <td></td>
                                        <td></td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td>Out By</td>
                                        <td class="text-right">{{ view_report_mdl.out_by | num2AmtStr }}</td>
                                    </tr>
                                    <tr>
                                        <td>Trust Ledger Closing Balance</td>
                                        <td>
                                            {{
                                                (view_report_mdl.cash_book_opening_bal +
                                                    view_report_mdl.receipts -
                                                    view_report_mdl.payments * -1)
                                                    | num2AmtStr
                                            }}
                                        </td>
                                        <td style="min-width: 100px">&nbsp;</td>
                                        <td>Cash Book Closing Balance</td>
                                        <td class="text-right">
                                            {{
                                                (view_report_mdl.cash_book_opening_bal +
                                                    view_report_mdl.receipts -
                                                    view_report_mdl.payments * -1)
                                                    | num2AmtStr
                                            }}
                                        </td>
                                    </tr>
                                    <tr class="c8-page-table-row-header">
                                        <td
                                            colspan="5"
                                            class="text-center"
                                        >
                                            Trust Ledger Balances with Cash Book
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="success"
                        tile
                        small
                        @click="updateBankRecon()"
                        v-if="
                            (!isNaN(view_report_mdl.out_by) && view_report_mdl.out_by == 0 && closing_bal >= 0) ||
                            !is_bank_barred
                        "
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-content-save</v-icon
                        >Update Bank Reconciliation
                    </v-btn>
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="downloadReportPDF()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-download</v-icon
                        >Download as PDF</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="view_report_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="view_match_mdl.show"
            persistent
            :max-width="view_match_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Match Details
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="view_match_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div style="padding: 0 30px">
                        <v-row>
                            <v-col
                                xs="12"
                                sm="12"
                            >
                                <div class="page-list">
                                    <div class="c8-page-table">
                                        <table>
                                            <tbody>
                                                <tr class="c8-page-table-header">
                                                    <td
                                                        colspan="100%"
                                                        class="text-left"
                                                    >
                                                        Bank Statements
                                                    </td>
                                                </tr>
                                                <tr class="c8-page-table-row-header">
                                                    <td
                                                        v-for="(hdr, index) in view_match_mdl.sta_list.headers"
                                                        :key="'vhdr-' + index"
                                                        :class="hdr.add_class"
                                                    >
                                                        <span>{{ hdr.label }}</span>
                                                    </td>
                                                </tr>
                                                <tr v-for="(row, row_index) in view_match_mdl.sta_list.lines">
                                                    <td
                                                        v-for="(hdr_row, hdr_row_index) in view_match_mdl.sta_list
                                                            .headers"
                                                        :class="[hdr_row.add_class]"
                                                    >
                                                        <span
                                                            v-if="
                                                                hdr_row.field == 'trans_date' ||
                                                                hdr_row.field == 'created_at'
                                                            "
                                                            >{{ row[hdr_row.field] | sql2Date }}</span
                                                        >
                                                        <span v-else-if="hdr_row.field == 'amount'">{{
                                                            row[hdr_row.field] | num2AmtStr
                                                        }}</span>
                                                        <span v-else>{{ row[hdr_row.field] }}</span>
                                                    </td>
                                                </tr>
                                                <tr class="c8-page-table-footer">
                                                    <td
                                                        colspan="6"
                                                        class="text-right"
                                                    ></td>
                                                    <td class="text-right">
                                                        Total Amount
                                                        <span style="display: inline-block; width: 100px">{{
                                                            view_match_sta_total | num2AmtStr
                                                        }}</span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <table v-if="view_match_mdl.adj_list.lines.length > 0">
                                            <tbody>
                                                <tr class="c8-page-table-header">
                                                    <td
                                                        colspan="100%"
                                                        class="text-left"
                                                    >
                                                        Remainder to Adjustments
                                                    </td>
                                                </tr>
                                                <tr class="c8-page-table-row-header">
                                                    <td
                                                        v-for="(hdr, index) in view_match_mdl.adj_list.headers"
                                                        :key="'vhdr-' + index"
                                                        :class="hdr.add_class"
                                                        :colspan="hdr.field == 'amount' ? 3 : 1"
                                                    >
                                                        <span>{{ hdr.label }}</span>
                                                    </td>
                                                </tr>
                                                <tr v-for="(row, row_index) in view_match_mdl.adj_list.lines">
                                                    <td
                                                        v-for="(hdr_row, hdr_row_index) in view_match_mdl.adj_list
                                                            .headers"
                                                        :class="[hdr_row.add_class]"
                                                        :colspan="hdr_row.field == 'amount' ? 3 : 1"
                                                    >
                                                        <span
                                                            v-if="
                                                                hdr_row.field == 'trans_date' ||
                                                                hdr_row.field == 'created_at' ||
                                                                hdr_row.field == 'remove_date'
                                                            "
                                                            >{{ row[hdr_row.field] | sql2Date }}</span
                                                        >
                                                        <span v-else-if="hdr_row.field == 'amount'">{{
                                                            row[hdr_row.field] | num2AmtStr
                                                        }}</span>
                                                        <span v-else>{{ row[hdr_row.field] }}</span>
                                                    </td>
                                                </tr>
                                                <tr class="c8-page-table-footer">
                                                    <td
                                                        colspan="6"
                                                        class="text-right"
                                                    ></td>
                                                    <td class="text-right">
                                                        Total Amount
                                                        <span style="display: inline-block; width: 100px">{{
                                                            view_match_adj_total | num2AmtStr
                                                        }}</span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <div
                                            style="width: 100%; text-align: center"
                                            class="c8-padding-1"
                                        >
                                            <v-icon
                                                center
                                                size="24"
                                                color="success"
                                                >mdi-link-variant</v-icon
                                            >
                                        </div>
                                        <table>
                                            <tbody>
                                                <tr class="c8-page-table-header">
                                                    <td
                                                        colspan="100%"
                                                        class="text-left"
                                                    >
                                                        Cirrus8 Charnges
                                                    </td>
                                                </tr>
                                                <tr class="c8-page-table-row-header">
                                                    <td
                                                        v-for="(hdr, index) in view_match_mdl.mtc_list.headers"
                                                        :key="'vhdr-' + index"
                                                        :class="hdr.add_class"
                                                    >
                                                        <span>{{ hdr.label }}</span>
                                                    </td>
                                                </tr>
                                                <tr v-for="(row, row_index) in view_match_mdl.mtc_list.lines">
                                                    <td
                                                        v-for="(hdr_row, hdr_row_index) in view_match_mdl.mtc_list
                                                            .headers"
                                                        :class="[hdr_row.add_class]"
                                                    >
                                                        <span
                                                            v-if="
                                                                hdr_row.field == 'trans_date' ||
                                                                hdr_row.field == 'created_at'
                                                            "
                                                            >{{ row[hdr_row.field] | sql2Date }}</span
                                                        >
                                                        <span v-else-if="hdr_row.field == 'amount'">{{
                                                            row[hdr_row.field] | num2AmtStr
                                                        }}</span>
                                                        <span v-else>{{ row[hdr_row.field] }}</span>
                                                    </td>
                                                </tr>
                                                <tr class="c8-page-table-footer">
                                                    <td
                                                        colspan="4"
                                                        class="text-right"
                                                    ></td>
                                                    <td class="text-right">
                                                        Total Amount
                                                        <span style="display: inline-block; width: 100px">{{
                                                            view_match_mtc_total | num2AmtStr
                                                        }}</span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        tile
                        color="error"
                        small
                        v-if="view_match_mdl.row.status == 'approved'"
                        @click="removeMatchOnView()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Remove Match</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="view_match_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="view_breakdown_mdl.show"
            persistent
            :max-width="view_breakdown_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    BPAY Breakdown per Bank Statement
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="view_breakdown_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-list">
                        <div class="c8-page-table">
                            <table>
                                <tbody>
                                    <tr class="c8-page-table-row-header">
                                        <td
                                            v-for="(hdr, index) in view_breakdown_mdl.list.headers"
                                            :key="'vhdr-' + index"
                                            :class="hdr.add_class"
                                        >
                                            <span>{{ hdr.label }}</span>
                                        </td>
                                    </tr>
                                    <tr v-for="(row, row_index) in view_breakdown_mdl.list.lines">
                                        <td
                                            v-for="(hdr_row, hdr_row_index) in view_breakdown_mdl.list.headers"
                                            :class="[hdr_row.add_class]"
                                        >
                                            <span
                                                v-if="hdr_row.field == 'trans_date' || hdr_row.field == 'created_at'"
                                                >{{ row[hdr_row.field] | sql2Date }}</span
                                            >
                                            <span v-else-if="hdr_row.field == 'amount'">{{
                                                row[hdr_row.field] | num2AmtStr
                                            }}</span>
                                            <span v-else>{{ row[hdr_row.field] }}</span>
                                        </td>
                                    </tr>
                                    <tr class="c8-page-table-footer">
                                        <td
                                            colspan="6"
                                            class="text-right"
                                        >
                                            Total Amount
                                        </td>
                                        <td class="text-right">
                                            <span style="display: inline-block; width: 100px">{{
                                                view_breakdown_mdl.total | num2AmtStr
                                            }}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="view_ap_breakdown_mdl.show"
            persistent
            :max-width="view_ap_breakdown_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Total Breakdown
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="view_ap_breakdown_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-list">
                        <div class="c8-page-table">
                            <table style="width: 100%">
                                <tbody>
                                    <tr class="c8-page-table-row-header">
                                        <td
                                            v-for="(hdr, index) in view_ap_breakdown_mdl.list.headers"
                                            :key="'vhdr-' + index"
                                            :class="hdr.add_class"
                                        >
                                            <span>{{ hdr.label }}</span>
                                        </td>
                                    </tr>
                                    <tr v-for="(row, row_index) in view_ap_breakdown_mdl.list.lines">
                                        <td
                                            v-for="(hdr_row, hdr_row_index) in view_ap_breakdown_mdl.list.headers"
                                            :class="[hdr_row.add_class]"
                                        >
                                            <span
                                                v-if="
                                                    hdr_row.field == 'trans_date' || hdr_row.field == 'aptr_create_date'
                                                "
                                                >{{ row[hdr_row.field] | sql2Date }}</span
                                            >
                                            <span v-else-if="hdr_row.field == 'amount'">{{
                                                row[hdr_row.field] | num2AmtStr
                                            }}</span>
                                            <span v-else>{{ row[hdr_row.field] }}</span>
                                        </td>
                                    </tr>
                                    <tr class="c8-page-table-footer">
                                        <td
                                            colspan="7"
                                            class="text-right"
                                        >
                                            Total Amount
                                        </td>
                                        <td class="text-right">
                                            <span style="display: inline-block; width: 100px">{{
                                                view_ap_breakdown_mdl.total | num2AmtStr
                                            }}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="ask_delete_adj_mdl.show"
            persistent
            :max-width="ask_delete_adj_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    &nbsp;
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="ask_delete_adj_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <p class="c8-padding-1">Are you sure you want to completely this adjustment?</p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="error"
                        tile
                        small
                        @click="completeDeleteAdjustment(ask_delete_adj_mdl.line)"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-check</v-icon
                        >Delete</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="ask_delete_adj_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <v-dialog
            v-model="add_adjustment_mdl.show"
            persistent
            :max-width="add_adjustment_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    New Adjustment
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="add_adjustment_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Trans/Issue Date</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-date-picker
                                    v-model="add_adjustment_mdl.form.issue_date"
                                    id="add-new-bank-adj-date"
                                    ref="add_new_bank_adj_date"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Reference</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    dense
                                    v-model="add_adjustment_mdl.form.reference"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Amount</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    dense
                                    v-model="add_adjustment_mdl.form.amount"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Description</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <v-text-field
                                    dense
                                    v-model="add_adjustment_mdl.form.description"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="success"
                        tile
                        small
                        @click="saveNewAdjustment()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-check</v-icon
                        >Add</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="add_adjustment_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-container>
</template>
<script>
import CirrusBankStatementFile from '../../elements/CirrusBankStatementFile.vue';
import moment from 'moment';
import { mapState, mapActions, mapMutations } from 'vuex';

export default {
    name: 'bankRecons',
    components: {
        'bank-statement-file': CirrusBankStatementFile,
    },
    data() {
        return {
            has_download: false,
            bank: null,
            bank_name: null,
            on_after_recon_only: false,
            bank_header: {},
            bank_date: null,
            closing_bal: 0,
            closing_bal_formatted: null,
            opening_bal: null,
            no_bank_date: false,
            is_bank_barred: false,
            last_recon_date: null,
            list: {
                headers: [
                    { label: ' ', field: 'statement_selected', add_class: 'text-left', col_type: 'statment' },
                    {
                        label: 'Trans/ Issue Date',
                        field: 'statement_date',
                        add_class: 'text-left',
                        col_type: 'statment',
                    },
                    // {label: 'Method', field: 'method', 'add_class': 'text-left'},
                    {
                        label: 'Reference/ Description',
                        field: 'statement_desc',
                        add_class: 'text-left',
                        col_type: 'statment',
                    },
                    { label: 'Status', field: 'status', add_class: 'text-left', col_type: 'statment' },
                    { label: 'Amount ($)', field: 'statement_amount', add_class: 'text-right', col_type: 'statment' },
                    {
                        label: 'Actions/ Remove as Of',
                        field: 'statement_links',
                        add_class: 'text-right',
                        col_type: 'statment',
                    },
                    { label: ' ', field: 'match_selected', add_class: 'sliced text-left', col_type: 'match' },
                    { label: 'Date', field: 'match_date', add_class: 'text-left', col_type: 'match' },
                    { label: 'Trans Type', field: 'match_trans_type', add_class: 'text-left' },
                    { label: 'Reference', field: 'match_ref', add_class: 'text-left', col_type: 'match' },
                    { label: 'Payee', field: 'match_payee', add_class: 'text-left', col_type: 'match' },
                    { label: 'Amount ($)', field: 'match_amount', add_class: 'text-right', col_type: 'match' },
                    { label: 'Accept Match', field: 'actions', add_class: 'text-right', col_type: 'match' },
                    {
                        label: 'Presentation Date',
                        field: 'match_presented_date',
                        add_class: 'text-right',
                        col_type: 'match',
                    },
                    // {label: ' ', field: 'match_links', 'add_class': 'text-center'},
                ],
                headers_excl: ['actions', 'match_presented_date', 'statement_links', 'status', 'match_links'],
                lines: {},
                filters: { statuses: ['pending', 'approved', 'adjustment'], amount: null, amount_formatted: null },
            },
            status_opts: [
                { label: 'Unreconciled', value: 'pending' },
                { label: 'Suggested', value: 'approved' },
                { label: 'Reconciled', value: 'reconciled' },
                { label: 'Adjustment', value: 'adjustment' },
            ],
            status_txt: {
                pending: 'Unreconciled',
                approved: 'Suggested',
                reconciled: 'Reconciled',
                adjustment: 'Adjustment',
            },
            adjustments: {
                form: {
                    issue_date: null,
                    ref_no: null,
                    description: null,
                    amount: null,
                },
                lines: [],
            },
            default_match_list: [],
            table_width: 0,
            show_results: false,
            recon_results: [],
            ask_delete_mdl: {
                show: false,
                width: 390,
                multiple: false,
                line: {},
            },
            ask_delete_adj_mdl: {
                show: false,
                width: 390,
                line: {},
                line_key: null,
                trans_date: null,
            },
            edit_last_recon_mdl: {
                show: false,
                width: 460,
                action: 'edit',
                save_date_txt: null,
            },
            view_report_mdl: {
                show: false,
                width: 750,
                totals: { DIR: 0, BPA: 0, CHQ: 0, ADJ: 0 },
                adjusted: 0,
                receipts: 0,
                payments: 0,
                cash_book_opening_bal: 0,
                out_by: 0,
            },
            view_match_mdl: {
                row: {},
                show: false,
                width: 1050,
                sta_list: {
                    headers: [
                        { label: 'Trans Date', field: 'trans_date', add_class: 'text-left' },
                        { label: 'Trans Type', field: 'trans_type_name', add_class: 'text-left' },
                        { label: 'Description', field: 'description', add_class: 'text-left' },
                        { label: 'Method', field: 'method', add_class: 'text-left' },
                        { label: 'Import/Download Date', field: 'created_at', add_class: 'text-left' },
                        { label: 'Import/Download By', field: 'create_user', add_class: 'text-left' },
                        { label: 'Amount', field: 'amount', add_class: 'text-right' },
                    ],
                    lines: [],
                },
                mtc_list: {
                    headers: [
                        { label: 'Trans Date', field: 'trans_date', add_class: 'text-left' },
                        { label: 'Trans Type', field: 'trans_type_name', add_class: 'text-left' },
                        { label: 'Reference', field: 'ref', add_class: 'text-left' },
                        { label: 'Payee', field: 'payee', add_class: 'text-left' },
                        { label: 'Amount', field: 'amount', add_class: 'text-right' },
                    ],
                    lines: [],
                },
                adj_list: {
                    headers: [
                        { label: 'Issue Date', field: 'trans_date', add_class: 'text-left' },
                        { label: 'Trans Type', field: 'trans_type_name', add_class: 'text-left' },
                        { label: 'Reference', field: 'reference', add_class: 'text-left' },
                        { label: 'Description', field: 'description', add_class: 'text-left' },
                        { label: 'Remove Date', field: 'remove_date', add_class: 'text-left' },
                        { label: 'Amount', field: 'amount', add_class: 'text-right' },
                    ],
                    lines: [],
                },
            },
            view_breakdown_mdl: {
                show: false,
                width: 950,
                list: {
                    headers: [
                        { label: 'Trans Date', field: 'trans_date', add_class: 'text-left' },
                        { label: 'Trans Type', field: 'trans_type_name', add_class: 'text-left' },
                        { label: 'Description', field: 'description', add_class: 'text-left' },
                        { label: 'Method', field: 'method', add_class: 'text-left' },
                        { label: 'Import/Download Date', field: 'created_at', add_class: 'text-left' },
                        { label: 'Import/Download By', field: 'create_user', add_class: 'text-left' },
                        { label: 'Amount', field: 'amount', add_class: 'text-right' },
                    ],
                    lines: [],
                    total: 0,
                },
            },
            view_ap_breakdown_mdl: {
                show: false,
                width: 950,
                list: {
                    headers: [
                        { label: 'Ref #', field: 'ref_1', add_class: 'text-left' },
                        { label: 'Property Code', field: 'ref_2', add_class: 'text-left' },
                        { label: 'Trans Date', field: 'trans_date', add_class: 'text-left' },
                        { label: 'Description', field: 'description', add_class: 'text-left' },
                        { label: 'Supplier', field: 'creditor_code', add_class: 'text-left' },
                        { label: 'Created By', field: 'aptr_mod_user', add_class: 'text-left' },
                        { label: 'Create Date', field: 'aptr_create_date', add_class: 'text-left' },
                        { label: 'Amount', field: 'trans_amt', add_class: 'text-right' },
                    ],
                    lines: [],
                    total: 0,
                },
            },
            add_adjustment_mdl: {
                show: false,
                width: 600,
                form: {
                    issue_date: null,
                    reference: null,
                    amount: null,
                    description: null,
                },
            },
            country_defaults: {
                country_code: 'AU',
                business_label: 'ABN',
                business_length: '11',
                display_bsb: true,
                display_state: true,
                post_code_length: '4',
                bank_account_length: '9',
                tax_label: 'GST',
                business_prefix: '',
                currency_symbol: '$',
            },
        };
    },
    computed: {
        ...mapState('banks', { bank_list: (state) => state.list }),
        recons() {
            let data = {};
            let recons = {};

            if (this.list.lines) {
                for (var type in this.list.lines) {
                    this.sortLines(type);
                }
                data = this.list.lines;
                Object.keys(data)
                    .sort((a, b) => {
                        a = moment(a, 'YYYYMMDD').valueOf();
                        b = moment(b, 'YYYYMMDD').valueOf();
                        return a - b;
                    })
                    .forEach((key) => {
                        // let list = data[key]
                        // let rows = list.rows.filter(row => {
                        // 	let add = true
                        // 	console.log(this.list.filters.statuses.indexOf,row.status,)
                        // 	if(statuses.indexOf(row.status) === -1)
                        // 		add = false

                        // 	return add
                        // })
                        // list.rows =
                        recons[key] = data[key];
                    });
            }
            // console.log(recons)
            return recons;
        },
        bank_statements_total() {
            let amount = 0;
            if (this.list.lines) {
                for (var date in this.list.lines) {
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];

                            if (row.statement_amount) amount += parseFloat(row.statement_amount);
                        }
                }
            }
            return amount;
        },
        bank_statements_shown_total() {
            let amount = 0;
            if (this.list.lines) {
                for (var date in this.list.lines) {
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];
                            if (this.rowFilter(row) && row.statement_amount) amount += parseFloat(row.statement_amount);
                        }
                }
            }
            return amount;
        },
        bank_statements_unreconciled_total() {
            let amount = 0;
            if (this.list.lines) {
                for (var date in this.list.lines) {
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];
                            if (row.status == 'pending' && row.statement_amount)
                                amount += parseFloat(row.statement_amount);
                        }
                }
            }
            return amount;
        },
        bank_matches_total() {
            let amount = 0;
            if (this.list.lines) {
                for (var date in this.list.lines) {
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];
                            if (row.match_amount) amount += parseFloat(row.match_amount);
                        }
                }
            }
            return amount;
        },
        unpresented_total() {
            let amount = 0;
            if (this.list.lines) {
                for (var date in this.list.lines) {
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];
                            if (row.match_amount && (row.match_details || row.match_props)) {
                                if (row.match_details) {
                                    if (
                                        !row.match_details.presented_date ||
                                        row.match_details.presented_date == 'present' ||
                                        row.match_details.presented_date == 'unpresent'
                                    ) {
                                        amount += parseFloat(row.match_amount);
                                    }
                                } else if (row.match_props) {
                                    if (
                                        !row.match_props.presented_date ||
                                        row.match_props.presented_date == 'present' ||
                                        row.match_props.presented_date == 'unpresent'
                                    ) {
                                        amount += parseFloat(row.match_amount);
                                    }
                                }
                            }
                        }
                }
            }
            return amount;
        },
        statements_selected() {
            let selecteds = [];
            if (this.list.lines) {
                for (var date in this.list.lines) {
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];
                            if (row.statement_selected) {
                                row.sel_date = date;
                                row.sel_key = i;
                                selecteds.push(row);
                            }
                        }
                }
                if (selecteds.length == 0) {
                    for (var date in this.list.lines) {
                        if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                            for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                                this.list.lines[date].rows[i].match_selected = false;
                            }
                    }
                }
            }
            return selecteds;
        },
        statements_selected_total() {
            let amount = 0;
            if (this.statements_selected && this.statements_selected.length > 0) {
                for (var i = 0; i < this.statements_selected.length; i++) {
                    amount += parseFloat(this.statements_selected[i].statement_amount);
                }
            }
            return amount.toFixed(2);
        },
        to_match_selected() {
            let selecteds = [];
            if (this.list.lines) {
                for (var date in this.list.lines) {
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];
                            if (row.match_selected) {
                                row.sel_date = date;
                                row.sel_key = i;
                                selecteds.push(row);
                            }
                        }
                }
            }
            return selecteds;
        },
        to_match_selected_total() {
            let amount = 0;
            if (this.to_match_selected && this.to_match_selected.length > 0) {
                for (var i = 0; i < this.to_match_selected.length; i++) {
                    amount += parseFloat(this.to_match_selected[i].match_amount);
                }
            }
            return amount.toFixed(2);
        },
        calculated_bal() {
            let calculated_bal = 0;
            let unpresented_total = 0;
            let presented_total = 0;
            let adjustments_total = 0;
            let opening_bal = 0;

            if (this.list.lines) {
                for (var date in this.list.lines) {
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0)
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];
                            // if(row.match_amount && (row.match_presented_date == 'present' || row.match_presented_date == 'unpresent'))
                            //   unpresented_total += parseFloat(row.match_amount)
                            if (row.status == 'adjustment') {
                                if (
                                    !row.statement_details.remove_date ||
                                    moment(this.bank_date, 'DD/MM/YYYY') <
                                        moment(row.statement_details.remove_date, 'DD/MM/YYYY')
                                ) {
                                    adjustments_total += parseFloat(row.statement_amount);
                                }
                            } else {
                                if (row.match_amount && (row.match_details || row.match_props)) {
                                    if (row.match_details) {
                                        if (
                                            !row.match_details.presented_date ||
                                            row.match_details.presented_date == 'present' ||
                                            row.match_details.presented_date == 'unpresent'
                                        ) {
                                            unpresented_total += parseFloat(row.match_amount);
                                        } else if (
                                            moment(this.bank_date, 'DD/MM/YYYY') <
                                            moment(row.match_details.presented_date, 'DD/MM/YYYY')
                                        ) {
                                            presented_total += parseFloat(row.match_amount);
                                        }
                                    } else if (row.match_props) {
                                        if (
                                            !row.match_props.presented_date ||
                                            row.match_props.presented_date == 'present' ||
                                            row.match_props.presented_date == 'unpresent'
                                        ) {
                                            unpresented_total += parseFloat(row.match_amount);
                                        } else if (
                                            moment(this.bank_date, 'DD/MM/YYYY') <
                                            moment(row.match_props.presented_date, 'DD/MM/YYYY')
                                        ) {
                                            presented_total += parseFloat(row.match_amount);
                                        }
                                    }
                                }
                            }
                            // if status end
                        }
                }
            }

            if (this.opening_bal) opening_bal = parseFloat(this.opening_bal);

            calculated_bal =
                parseFloat(opening_bal) +
                (parseFloat(unpresented_total * -1) - parseFloat(presented_total)) +
                parseFloat(adjustments_total);
            return calculated_bal;
        },
        out_of_bal() {
            let bank_statement_bal = 0;
            if (this.closing_bal) {
                bank_statement_bal = parseFloat(this.closing_bal);
            }
            return bank_statement_bal - parseFloat(this.calculated_bal).toFixed(2);
        },
        statement_match_total_diff() {
            return parseFloat(this.statements_selected_total) - parseFloat(this.to_match_selected_total);
        },
        statement_match_total_color() {
            let color = '';
            // let color = 'c8-color-';
            if (parseFloat(this.statements_selected_total) > parseFloat(this.to_match_selected_total)) {
                color = 'orange darken-1';
            } else if (parseFloat(this.statements_selected_total) < parseFloat(this.to_match_selected_total)) {
                color = 'red darken-1';
            } else if (
                parseFloat(this.statements_selected_total) > 0 &&
                parseFloat(this.statements_selected_total) == parseFloat(this.to_match_selected_total)
            ) {
                color = 'green darken-1';
            } else color = '';
            return color;
        },
        view_match_sta_total() {
            let total = 0;
            if (this.view_match_mdl.sta_list.lines.length > 0) {
                for (var i = 0; i < this.view_match_mdl.sta_list.lines.length; i++) {
                    let ln = this.view_match_mdl.sta_list.lines[i];
                    if (ln.amount) {
                        total += parseFloat(ln.amount);
                    }
                }
            }
            return total;
        },
        view_match_mtc_total() {
            let total = 0;
            if (this.view_match_mdl.sta_list.lines.length > 0) {
                for (var i = 0; i < this.view_match_mdl.mtc_list.lines.length; i++) {
                    let ln = this.view_match_mdl.mtc_list.lines[i];
                    if (ln.amount) {
                        total += parseFloat(ln.amount);
                    }
                }
            }
            return total;
        },
        view_match_adj_total() {
            let total = 0;
            if (this.view_match_mdl.adj_list.lines.length > 0) {
                for (var i = 0; i < this.view_match_mdl.adj_list.lines.length; i++) {
                    let ln = this.view_match_mdl.adj_list.lines[i];
                    if (ln.amount) {
                        total += parseFloat(ln.amount);
                    }
                }
            }
            return total;
        },
    },
    methods: {
        ...mapActions('banks', ['setList']),
        saveNewAdjustment() {
            let request = {
                bank: this.bank,
                details: JSON.stringify(this.add_adjustment_mdl.form),
            };
            this.$api.post('auto-bank-recon/page/adjustment/add', this.req(request)).then((response) => {
                if (!response.data.error) {
                    this.add_adjustment_mdl.show = false;
                    this.resetAddAdjustmentForm();
                    this.load();
                }
            });
        },
        addAdjustment() {
            this.add_adjustment_mdl.show = true;
            this.resetAddAdjustmentForm();
        },
        resetAddAdjustmentForm() {
            this.add_adjustment_mdl.form.issue_date = this.bank_date;
            this.add_adjustment_mdl.form.reference = null;
            this.add_adjustment_mdl.form.amount = null;
            this.add_adjustment_mdl.form.description = null;
        },
        checkIfSelectable(amount) {
            if (this.statements_selected_total == 0) return true;
            else if (this.statements_selected_total < 0 && parseFloat(amount) < 0) return true;
            else if (this.statements_selected_total > 0 && parseFloat(amount) >= 0) return true;
            else return false;
        },
        completeDeleteAdjustment() {
            this.ask_delete_adj_mdl.show = false;
            let request = {};

            if (this.ask_delete_adj_mdl.line) {
                let serial_no = null;
                if (this.ask_delete_adj_mdl.line.statement_details.serial_no) {
                    serial_no = this.ask_delete_adj_mdl.line.statement_details.serial_no;
                } else if (this.ask_delete_adj_mdl.line.statement_details.adjustment_details) {
                    serial_no = this.ask_delete_adj_mdl.line.statement_details.adjustment_details.pmbd_serial;
                }
                if (serial_no) {
                    request.bank = this.bank;
                    request.serial_no = serial_no;

                    if (
                        this.ask_delete_adj_mdl.line.statement_details.file_code &&
                        this.ask_delete_adj_mdl.line.statement_details.id
                    ) {
                        request.bank_statement_id = this.ask_delete_adj_mdl.line.statement_details.id;
                    }

                    this.$api.post('auto-bank-recon/page/adjustment/delete', this.req(request)).then((response) => {
                        if (!response.data.error) {
                            this.list.lines[this.ask_delete_adj_mdl.trans_date].rows.splice(
                                this.ask_delete_adj_mdl.line_key,
                                1,
                            );
                            this.ask_delete_adj_mdl.show = false;
                            this.ask_delete_adj_mdl.line = null;
                            this.ask_delete_adj_mdl.line_key = null;
                            this.ask_delete_adj_mdl.trans_date = null;
                            this.load();
                        }
                    });
                } else {
                    this.$noty.error("Can't find Adjustment serial number");
                }
            }
        },
        completeDeleteAdjustmentAsk(trans_date, index, row) {
            this.ask_delete_adj_mdl.show = true;
            this.ask_delete_adj_mdl.line = row;
            this.ask_delete_adj_mdl.line_key = index;
            this.ask_delete_adj_mdl.trans_date = trans_date;
            // this.list.lines[trans_date].rows.splice(index,1)
        },
        checkIfDeletable(details) {
            let del = false;
            let issueDate = null;
            if (details.issue_date) {
                issueDate = moment(details.issue_date, 'DD/MM/YYYY');
            } else if (details.trans_date) {
                issueDate = moment(details.trans_date, 'YYYY-MM-DD');
            }
            if (details.remove_date && issueDate) {
                var removeDate = moment(details.remove_date, 'DD/MM/YYYY');
                if (issueDate.isSame(removeDate)) {
                    del = true;
                }
            }
            return del;
        },
        formatClosingBal() {
            if (this.closing_bal_formatted != null && this.closing_bal_formatted != '') {
                let value = parseFloat(this.closing_bal_formatted.replace(/[^0-9\.\-]+/g, ''));

                if (this.is_bank_barred && value < 0) {
                    this.$noty.error('Bank Statement Balance cannot be a negative amount for this barred bank account');
                    this.closing_bal = 0;
                    this.closing_bal_formatted = null;
                } else {
                    this.closing_bal = value;
                    this.closing_bal_formatted = value
                        .toFixed(2)
                        .toString()
                        .replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,');
                }
            } else {
                this.closing_bal = 0;
                this.closing_bal_formatted = null;
            }
        },
        formatFilterAmount() {
            if (this.list.filters.amount_formatted != null && this.list.filters.amount_formatted != '') {
                let value = parseFloat(this.list.filters.amount_formatted.replace(/[^0-9\.\-]+/g, ''));
                this.list.filters.amount = value;
                this.list.filters.amount_formatted = value.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,');
            } else {
                this.list.filters.amount = 0;
                this.list.filters.amount_formatted = null;
            }
        },
        saveRecon() {
            let request = {
                bank: this.bank,
                bank_date: this.bank_date,
                list: JSON.stringify(this.list.lines),
                match_list: JSON.stringify(this.default_match_list),
            };
            // console.log(this.list.lines);
            // return false;
            this.$api.post('auto-bank-recon/page/save', this.req(request)).then((response) => {
                let results = [];
                // console.log(response.data)
                if (response.data.hasOwnProperty('results') && response.data.results) {
                    results = response.data.results;
                }
                this.recon_results = results;
                this.show_results = true;
            });
        },
        nextCharge() {
            let container = document.getElementById('main_container');
            let max_scroll = container.scrollHeight - container.clientHeight;
            if (this.recons) {
                for (var trans_date in this.recons) {
                    let scrolled = false;
                    for (var i = 0; i < this.recons[trans_date].rows.length; i++) {
                        let row = this.recons[trans_date].rows[i];
                        let elem = document.getElementById('presented-date-' + (trans_date + '-' + i) + '-con');
                        if (elem) {
                            let pos = this.findPosOffsetTop(elem);
                            if (max_scroll > pos) {
                                if (pos - 150 == container.scrollTop) {
                                    break;
                                } else if (pos > container.scrollTop) {
                                    container.scrollTop = pos - 150;
                                    scrolled = true;
                                    break;
                                }
                            } else {
                                if (max_scroll == container.scrollTop) container.scrollTop = 0;
                                else container.scrollTop = max_scroll;
                                scrolled = true;
                            }
                        }
                    }
                    if (scrolled) {
                        break;
                    }
                }
            }
        },
        addAsAdjustment(trans_date, index, row) {
            if (row.is_matched) {
                this.setMatchStatus(row, 'declined');
            }
            row.status = 'adjustment';
            this.$noty.success('Bank statement line updated as adjustment');

            // this.adjustments.form.issue_date =  moment(row.statement_date, 'YYYYMMDD').format("DD/MM/YYYY")
            // this.adjustments.form.ref_no =  row.statement_trans_name
            // this.adjustments.form.description =  row.statement_desc
            // this.adjustments.form.amount =  row.statement_amount
            // this.adjustments.form.statement_uid =  row.uid
            // this.addNewAdjustment()
            //
        },
        setAdjustmentRemoveDate(statement_details, set_date) {
            if (set_date) {
                let issue_date = null;
                if (statement_details.issue_date) issue_date = moment(statement_details.issue_date, 'DD/MM/YYYY');
                else issue_date = moment(statement_details.trans_date, 'YYYY-MM-DD');

                let last_recon_date = moment(this.last_recon_date, 'DD/MM/YYYY');
                let remove_date = moment(set_date, 'DD/MM/YYYY');

                if (remove_date.isValid()) {
                    let valid = true;
                    if (issue_date > remove_date) {
                        valid = false;
                        this.$noty.error('Entered adjustment remove date must not be less than the adjustment date.');
                    } else if (last_recon_date >= remove_date) {
                        valid = false;
                        this.$noty.error('Entered adjustment remove adate must be after the last reconciliation date.');
                    }
                    if (valid) {
                        statement_details.remove_date = set_date;
                    }
                }
            } else {
                statement_details.remove_date = null;
            }

            if (statement_details.remove_date_orig != statement_details.remove_date) {
                statement_details.is_modified = true;
            }
        },
        removeAsAdjustment(trans_date, index, row) {
            // for (var i = 0; i < this.adjustments.lines.length; i++) {
            //   let adj = this.adjustments.lines[i]
            //   if(adj.statement_uid == row.uid){
            //     this.adjustments.lines.splice(i,1);
            //     break;
            //   }
            // }
            row.status = row.orig_status;
            this.$noty.success('Bank statement line removed as adjustment');
        },
        setMatchStatus(row, set_status) {
            switch (set_status) {
                case 'approved':
                    if (row.temp_join_id && row.temp_join_id != '') {
                        let temp_join_id = row.temp_join_id;
                        for (var trans_date in this.list.lines) {
                            let rows = this.list.lines[trans_date].rows;
                            for (var i = rows.length - 1; i >= 0; i--) {
                                let row = rows[i];
                                if (row.temp_join_id && row.temp_join_id == temp_join_id) {
                                    row.status = 'approved';
                                    row.match_details.presented_date = moment(row.statement_date, 'YYYYMMDD').format(
                                        'DD/MM/YYYY',
                                    );
                                    row.match_details.is_presented = true;
                                }
                            }
                        }
                    } else {
                        row.status = 'approved';
                        row.match_details.presented_date = moment(row.statement_date, 'YYYYMMDD').format('DD/MM/YYYY');
                        row.match_details.is_presented = true;
                    }
                    break;
                case 'declined':
                    if (row.orig_status == 'adjustment') row.status = row.orig_status;
                    else row.status = 'pending';
                    this.removeMatch(row);
                    break;
                case 'unreconcile':
                    row.status = 'pending';
                    this.removeMatch(row, true);
                    break;
                case 'pending':
                    row.status = set_status;
                    break;
            }
        },
        removeMatch(row, force_unpresent) {
            let temp_join_id = null;
            if (row.temp_join_id && row.temp_join_id != '') {
                temp_join_id = row.temp_join_id;
            } else {
                let mtch = JSON.parse(JSON.stringify(row.match_details));
                // let matches = JSON.parse(JSON.stringify(row.match_details))
                this.clearRowMatch(row);
                this.addNewRowMatch(mtch, force_unpresent);
            }

            let added_uids = [];
            let cleared_uids = [];
            let sub_row_splice_uids = [];
            for (var trans_date in this.list.lines) {
                let rows = this.list.lines[trans_date].rows;
                for (var i = rows.length - 1; i >= 0; i--) {
                    let row = rows[i];
                    if (row.temp_join_id && row.temp_join_id == temp_join_id) {
                        let mtch = JSON.parse(JSON.stringify(row.match_details));
                        if (added_uids.indexOf(mtch.uid) === -1) {
                            this.addNewRowMatch(mtch, force_unpresent);
                            added_uids.push(mtch.uid);
                        }

                        // CHECK SUB ROW HAS DUPLICATE AND SET FOR SPLICING
                        if (cleared_uids.indexOf(row.uid) != -1) {
                            sub_row_splice_uids.push(row.uid);
                        }

                        if (row.orig_status == 'adjustment') row.status = row.orig_status;
                        else row.status = 'pending';

                        if (!row.is_sub) this.clearRowMatch(row);

                        cleared_uids.push(row.uid);
                    }
                }
                this.setLinesSortNo(rows);
            }
            // CLEAR SUB ROWS
            if (sub_row_splice_uids.length > 0) {
                //#
                for (var trans_date in this.list.lines) {
                    let rows = this.list.lines[trans_date].rows;
                    for (var d = 0; d < sub_row_splice_uids.length; d++) {
                        let to_splice_uid = sub_row_splice_uids[d];
                        for (var i = rows.length - 1; i >= 0; i--) {
                            let row = rows[i];
                            if (row.temp_join_id && row.temp_join_id == temp_join_id) {
                                if (row.is_sub && row.uid == to_splice_uid) {
                                    rows.splice(i, 1);
                                    break;
                                } else {
                                    if (row.orig_status == 'adjustment') row.status = row.orig_status;
                                    else row.status = 'pending';
                                    this.clearRowMatch(row);
                                }
                            }
                        }
                    }
                }
                //#
            } else {
                for (var trans_date in this.list.lines) {
                    let rows = this.list.lines[trans_date].rows;
                    for (var i = rows.length - 1; i >= 0; i--) {
                        let row = rows[i];
                        if (row.temp_join_id && row.temp_join_id == temp_join_id) {
                            if (row.orig_status == 'adjustment') row.status = row.orig_status;
                            else row.status = 'pending';
                            this.clearRowMatch(row);
                        }
                    }
                }
            }
        },
        clearRowMatch(row) {
            row.match_date = null;
            row.match_trans_type = null;
            row.match_ref = null;
            row.match_payee = null;
            row.match_amount = null;
            row.temp_join_id = null;
            row.is_matched = false;
            row.is_sub = false;
            row.match_selected = false;
            row.sort_no = row.orig_sort_no;
            row.match_details = [];
            row.to_adj_details = {};
        },
        addNewRowMatch(mtch, force_unpresent) {
            let details = JSON.parse(JSON.stringify(this.getMatchFirstValue(mtch.uid)));

            if (force_unpresent) {
                details.presented_date = 'present';
                details.already_presented = false;
                details.is_reset = true;
            }

            let new_row = {
                uid: mtch.uid,
                match_date: mtch.trans_date,
                match_ref: mtch.ref,
                match_trans_type: mtch.trans_type,
                match_payee: mtch.payee,
                match_amount: mtch.amount,
                match_presented_date: mtch.presented_date,
                match_is_presented: mtch.is_presented,
                match_props: details,
                is_matched: false,
                match_selected: false,
                status: 'pending',
            };
            if (this.list.lines[mtch.trans_date] && this.list.lines[mtch.trans_date].rows) {
                this.list.lines[mtch.trans_date].rows.push(new_row);
            } else {
                this.list.lines[mtch.trans_date] = { rows: [new_row] };
            }
        },
        saveMatch(hasDiff) {
            if (this.statements_selected && this.statements_selected.length > 1) {
                // MATCH STATEMENT TO RIGHT
                if (this.to_match_selected && this.to_match_selected.length > 1) {
                    this.$noty.error(
                        'If there are multiple selected bank statments lines, You can only select 1 Cash Book Unreconciled Transaction',
                    );
                    return false;
                } else {
                    let ctr = 1;
                    let use_sort_no = 0;
                    let join_by = null;
                    let temp_join_id = null;
                    if (this.to_match_selected.length == 1 && this.statements_selected.length > 1) {
                        join_by = 'mtc_multi_sta';
                        temp_join_id = (new Date().getTime() / 1000) | 0;
                    }
                    for (var trans_date in this.list.lines) {
                        let rows = this.list.lines[trans_date].rows;
                        for (var i = 0; i < rows.length; i++) {
                            let row = rows[i];
                            if (row.statement_selected) {
                                let mtch = this.to_match_selected[0];
                                row.match_details = mtch.match_props;
                                row.temp_join_id = temp_join_id;
                                row.temp_join_by = join_by;
                                if (ctr == 1) use_sort_no = row.sort_no;
                                else row.is_sub = true;
                                row.sort_no = use_sort_no;
                                this.setRowMatch(row, mtch, ctr == 1);
                                this.setMatchStatus(row, 'approved');
                                ctr++;
                            }
                        }
                    }
                }
            } else if (this.statements_selected && this.statements_selected.length == 1) {
                // MATCH STATEMENT TO LEFT
                let stmn = this.statements_selected[0];
                let ctr = 1;
                // let matches_props = []
                let join_by = null;
                let temp_join_id = null;
                if (this.to_match_selected.length > 1) {
                    join_by = 'sta_multi_mtc';
                    temp_join_id = (new Date().getTime() / 1000) | 0;
                }
                // for (var i = 0; i < this.to_match_selected.length; i++) {
                // 	 matches_props.push(JSON.parse(JSON.stringify(this.to_match_selected[i].match_props) ) )
                // }
                let total_amount = 0;
                for (var trans_date in this.list.lines) {
                    let rows = this.list.lines[trans_date].rows;
                    for (var i = 0; i < rows.length; i++) {
                        let row = rows[i];
                        if (stmn.sel_date == trans_date && stmn.sel_key == i) {
                            let mtch = JSON.parse(JSON.stringify(this.to_match_selected[0]));
                            this.setRowMatch(row, mtch, true);
                            row.temp_join_id = temp_join_id;
                            row.temp_join_by = join_by;
                            row.match_details = mtch.match_props;
                            row.is_sub = false;
                            this.setMatchStatus(row, 'approved');
                            total_amount += parseFloat(mtch.match_props.amount);
                            break;
                        }
                    }
                }
                // add additional rows
                if (this.to_match_selected.length > 1) {
                    ctr = 2;
                    for (var m = 1; m < this.to_match_selected.length; m++) {
                        let mtch = JSON.parse(JSON.stringify(this.to_match_selected[m]));
                        if (this.list.lines[stmn.sel_date].rows) {
                            let row = JSON.parse(JSON.stringify(this.list.lines[stmn.sel_date].rows[stmn.sel_key]));
                            this.setRowMatch(row, mtch, true);
                            row.temp_join_id = temp_join_id;
                            row.temp_join_by = join_by;
                            row.match_details = mtch.match_props;
                            row.is_sub = true;
                            row.sort_no = stmn.sort_no;
                            this.list.lines[stmn.sel_date].rows.splice(stmn.sel_key, 0, row);
                            this.setMatchStatus(row, 'approved');
                            total_amount += parseFloat(mtch.match_props.amount);
                            ctr++;
                        }
                    }
                }

                let remainder = parseFloat(stmn.statement_details.amount).toFixed(2) - total_amount.toFixed(2);
                if (remainder > 0) {
                    stmn.to_adj_details = {
                        trans_type: stmn.statement_details.trans_type,
                        reference: stmn.statement_details.trans_type_name,
                        description: stmn.statement_details.description,
                        amount: remainder,
                        trans_date: stmn.statement_details.trans_date,
                    };
                }
                //
            }
            this.deleteMatchRows();
        },
        getMatchFirstValue(uid) {
            let find = this.default_match_list.filter((row) => {
                return row.uid == uid;
            });
            if (find) {
                return find[0];
            } else return {};
        },
        setRowMatch(row, mtch, main) {
            if (main) {
                row.match_date = mtch.match_date;
                row.match_trans_type = mtch.match_trans_type;
                row.match_payee = mtch.match_payee;
                row.match_ref = mtch.match_ref;
                row.match_amount = mtch.match_amount;
                row.match_presented_date = mtch.match_presented_date;
            }
            row.match_is_presented = mtch.is_presented;
            row.match_props = mtch.match_props;
            row.is_matched = true;
            row.statement_selected = false;
            row.match_selected = false;
            // row.match_details = [mtch.match_props]
            row.status = 'pending';
            return row;
        },
        deleteMatchRows() {
            for (var trans_date in this.list.lines) {
                let rows = this.list.lines[trans_date].rows;
                for (var i = rows.length - 1; i >= 0; i--) {
                    let row = rows[i];
                    if (row.match_selected) {
                        rows.splice(i, 1);
                    }
                }
            }
            this.deselectRows();
        },
        deselectRows() {
            for (var trans_date in this.list.lines) {
                let rows = this.list.lines[trans_date].rows;
                for (var i = rows.length - 1; i >= 0; i--) {
                    let row = rows[i];
                    row.statement_selected = false;
                    row.match_selected = false;
                }
            }
        },
        getMatchTotalAmount(row, type) {
            if (row.temp_join_id && row.temp_join_id != '') {
                let temp_join_id = row.temp_join_id;
                let total = 0;
                let uids = [];
                for (var trans_date in this.list.lines) {
                    let rows = this.list.lines[trans_date].rows;
                    for (var i = rows.length - 1; i >= 0; i--) {
                        let row = rows[i];
                        if (row.temp_join_id && row.temp_join_id == temp_join_id) {
                            if (row.temp_join_by == 'mtc_multi_sta' && row.statement_details.amount) {
                                total += parseFloat(row.statement_details.amount);
                            } else if (
                                row.temp_join_by == 'sta_multi_mtc' &&
                                row.match_details.amount &&
                                uids.indexOf(row.match_details.uid) === -1
                            ) {
                                total += parseFloat(row.match_details.amount);
                                uids.push(row.match_details.uid);
                            }
                        }
                    }
                }
                return total;
            } else {
                if (type && type == 'match') return parseFloat(row.match_details.amount);
                else return parseFloat(row.statement_details.amount);
            }
        },
        removeMatchOnView() {
            this.setMatchStatus(this.view_match_mdl.row, 'declined');
            this.view_match_mdl.show = false;
        },
        viewMatchDetails(line) {
            this.view_match_mdl.show = true;
            this.view_match_mdl.row = line;
            this.view_match_mdl.sta_list.lines = [];
            this.view_match_mdl.mtc_list.lines = [];
            this.view_match_mdl.adj_list.lines = [];
            let temp_join_id = line.temp_join_id;

            if (temp_join_id) {
                if (line.temp_join_by == 'mtc_multi_sta') {
                    this.view_match_mdl.mtc_list.lines.push(line.match_details);
                } else if (line.temp_join_by == 'sta_multi_mtc') {
                    this.view_match_mdl.sta_list.lines.push(line.statement_details);
                }

                let uids = [];
                for (var trans_date in this.list.lines) {
                    let rows = this.list.lines[trans_date].rows;
                    for (var i = rows.length - 1; i >= 0; i--) {
                        let row = rows[i];
                        if (row.temp_join_id && row.temp_join_id == temp_join_id) {
                            if (row.temp_join_by == 'mtc_multi_sta') {
                                this.view_match_mdl.sta_list.lines.push(row.statement_details);
                            } else if (
                                row.temp_join_by == 'sta_multi_mtc' &&
                                uids.indexOf(row.match_details.uid) === -1
                            ) {
                                this.view_match_mdl.mtc_list.lines.push(row.match_details);
                                uids.push(row.match_details.uid);
                            }
                        }
                    }
                }
            } else {
                this.view_match_mdl.sta_list.lines.push(line.statement_details);
                this.view_match_mdl.mtc_list.lines.push(line.match_details);
            }
            if (line.status == 'approved' && line.to_adj_details && line.to_adj_details.amount) {
                this.view_match_mdl.adj_list.lines.push(line.to_adj_details);
            } else if (
                line.status == 'reconciled' &&
                line.statement_details &&
                line.statement_details.adjustment_details &&
                line.statement_details.adjustment_details.pmbd_serial
            ) {
                let adj = line.statement_details.adjustment_details;
                this.view_match_mdl.adj_list.lines.push({
                    trans_type_name: line.statement_details.trans_type_name,
                    reference: adj.pmbd_ref,
                    description: adj.pmbd_desc,
                    amount: adj.pmbd_val,
                    trans_date: adj.pmbd_date,
                    remove_date: adj.pmbd_remove_date,
                });
            }
        },
        viewTotalBreakdown(row) {
            let ids = row.statement_details.group_list.map((row) => {
                return row.id;
            });
            let request = { bank: this.bank, line_ids: JSON.stringify(ids) };
            this.$api.post('bank-statements/page/view/line', this.req(request)).then((response) => {
                if (response.data.statements) {
                    this.view_breakdown_mdl.list.lines = response.data.statements;
                    this.view_breakdown_mdl.total = this.view_breakdown_mdl.list.lines.reduce((sum, row) => {
                        return sum + parseFloat(row.amount);
                    }, 0);
                    this.view_breakdown_mdl.show = true;
                }
            });
        },
        viewApBreakdown(row) {
            // let details = row.match_props
            if (row.match_props && row.match_props.pmbg_ref) {
                // let request = {bank : this.bank, ref : row.match_ref.trim()}
                let request = { bank: this.bank, ref: row.match_props.pmbg_ref.trim() };
                this.$api.post('auto-bank-recon/page/view/ap', this.req(request)).then((response) => {
                    if (response.data.lines) {
                        this.view_ap_breakdown_mdl.list.lines = response.data.lines;
                        this.view_ap_breakdown_mdl.total = this.view_ap_breakdown_mdl.list.lines.reduce((sum, row) => {
                            return sum + parseFloat(row.trans_amt);
                        }, 0);
                        this.view_ap_breakdown_mdl.show = true;
                    }
                });
            }
        },
        rowFilter(row) {
            let show = true;
            if (row.is_sub) {
                show = false;
            } else if (this.list.filters.statuses.indexOf(row.status) === -1) {
                show = false;
            }

            if (show && this.list.filters.amount != '' && this.list.filters.amount != null) {
                show = this.matchRegex(row, ['statement_amount'], this.list.filters.amount.toString());
            }
            return show;
        },
        setStatementAmountColor(row) {
            let color = '';

            if (row.status == 'reconciled') {
                if (parseFloat(row.statement_details.adjustment_amount) < this.getMatchTotalAmount(row)) {
                    color = 'c8-weight-bold c8-color-warning';
                }
            } else if (row.status == 'approved') {
                if (parseFloat(row.statement_details.amount).toFixed(2) > this.getMatchTotalAmount(row, 'match')) {
                    color = 'c8-weight-bold c8-color-warning';
                }
            }
            return color;
        },
        setRowColor(row) {
            let color = '';
            if (row.is_matched && row.status == 'pending') {
                color += 'bg-primary ';
            } else if (row.status == 'approved') {
                color += 'bg-success ';
            }
            return color;
        },
        setLinesSortNo(line_rows) {
            line_rows.sort(
                this.sort_by(
                    { name: 'statement_amount', primer: parseFloat, reverse: true },
                    { name: 'is_sub', reverse: false },
                    { name: 'match_amount', primer: parseFloat, reverse: true },
                ),
            );
            let sort_ctr = 1;
            line_rows.map((row) => {
                row.orig_sort_no = sort_ctr;
                row.sort_no = sort_ctr;
                sort_ctr++;
                return row;
            });
        },
        sortLines(type) {
            this.list.lines[type].rows.sort(
                this.sort_by(
                    { name: 'status', reverse: true },
                    { name: 'sort_no', reverse: false },
                    // {name:'statement_amount', primer: parseFloat, reverse: true},
                    { name: 'is_sub', reverse: false },
                    // {name:'match_amount', primer: parseFloat, reverse: true},
                    // {name:'mtch_date', primer: 'date', format: "YYYYMMDD", reverse: false},
                    // {name:'mtch_uid',  reverse: false},
                ),
            );
        },
        setPresentDate(row, set_date) {
            if (set_date) {
                row.presented_date = set_date;
                if (set_date == 'unpresent') {
                    row.presented_date = 'present';
                    row.already_presented = false;
                    row.is_reset = true;
                } else {
                    let is_valid = this.validDate(row, set_date);
                    if (is_valid) {
                        row.presented_date = set_date;
                    } else {
                        row.presented_date = 'present';
                    }
                }
            } else if (!set_date && (row.presented_date == 'present' || row.presented_date == 'unpresent'))
                row.presented_date = this.bank_date;
        },
        validDate(row, set_date) {
            let valid = true;
            if (set_date == 'present') return true;
            let last_recon_date = moment(this.last_recon_date, 'DD/MM/YYYY');
            let bank_date = moment(this.bank_date, 'DD/MM/YYYY');
            let issue_date = moment(row.trans_date, 'YYYY-MM-DD');
            let presented_date = moment(set_date, 'DD/MM/YYYY');
            let msg = '';

            if (last_recon_date.isValid() && presented_date < last_recon_date) {
                valid = false;
                msg = 'The date you entered must be after the last reconciled date (' + this.last_recon_date + ').';
            } else if (issue_date.isValid() && presented_date < issue_date) {
                valid = false;
                msg = 'The date you entered must be after the issue date (' + issue_date.format('DD/MM/YYYY') + ').';
            } else if (presented_date > bank_date) {
                valid = false;
                msg = 'The date you entered must be on or before the bank statement date (' + this.bank_date + ').';
            }

            if (msg != '') {
                this.$noty.error(msg);
            }

            return valid;
        },
        addNewAdjustment() {
            let details = JSON.parse(JSON.stringify(this.adjustments.form));
            details.remove_date = null;
            details.is_new = true;
            this.adjustments.lines.push(details);
            this.resetAdjustmentForm();
        },
        removeNewAdjustment(row, index) {
            this.adjustments.lines.splice(index, 1);
        },
        resetAdjustmentForm() {
            let issue_date = moment().subtract(1, 'days').format('DD/MM/YYYY');
            this.adjustments.form.issue_date = issue_date;
            this.adjustments.form.ref_no = null;
            this.adjustments.form.description = null;
            this.adjustments.form.amount = null;
        },
        resetRecon() {
            this.no_bank_date = false;
            this.bank_header = null;
            this.opening_bal = null;
            this.closing_bal = null;
            this.is_bank_barred = false;
            this.last_recon_date = false;
            this.default_match_list = {};
            this.list.lines = {};
            this.show_results = false;
            this.load();
        },
        transactStatement(row, is_adjustment) {
            let url = '?module=ar&command=receipting&method=file&fid=' + row.statement_details.id;
            if (row.statement_amount < 0) url = '?module=ap&command=payments';
            else if (is_adjustment) {
                url = '?module=ar&command=receipting&useAmt=' + row.statement_details.amount;
            }
            var win = window.open(url, '_blank');
            win.focus();
        },
        deleteMultipleStatements() {
            this.askDeleteStatement();
        },
        askDeleteStatement(row) {
            this.ask_delete_mdl.show = true;
            if (row) {
                this.ask_delete_mdl.line = row;
                this.ask_delete_mdl.multiple = false;
            } else {
                this.ask_delete_mdl.line = {};
                this.ask_delete_mdl.multiple = true;
            }
        },
        deleteStatement(row) {
            this.ask_delete_mdl.show = false;
            let request = {};
            request.bank = this.bank;
            let multiple = false;
            if (row && row.statement_details) {
                if (row.is_matched) {
                    this.setMatchStatus(row, 'unreconcile');
                }
                request.trans_id = row.statement_details.id;
            } else {
                let ids = [];
                multiple = true;
                if (this.statements_selected.length > 0) {
                    for (var i = 0; i < this.statements_selected.length; i++) {
                        let row = this.statements_selected[i];
                        ids.push(row.statement_details.id);
                        if (row.is_matched) this.setMatchStatus(row, 'Unreconcile');
                    }
                }
                request.multi_trans_id = JSON.stringify(ids);
            }
            if (request) {
                this.$api.post('auto-bank-recon/page/delete', this.req(request)).then((response) => {
                    if (!multiple) {
                        for (var trans_date in this.list.lines) {
                            let rows = this.list.lines[trans_date].rows;
                            for (var i = rows.length - 1; i >= 0; i--) {
                                if (row.uid == rows[i].uid) {
                                    this.list.lines[trans_date].rows.splice(i, 1);
                                    break;
                                }
                            }
                        }
                    } else {
                        for (var trans_date in this.list.lines) {
                            let rows = this.list.lines[trans_date].rows;
                            for (var i = rows.length - 1; i >= 0; i--) {
                                for (var s = 0; s < this.statements_selected.length; s++) {
                                    let row = this.statements_selected[s];
                                    if (row.uid == rows[i].uid) {
                                        this.list.lines[trans_date].rows.splice(i, 1);
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    this.ask_delete_mdl.line = {};
                });
            }
        },
        saveNewLastReconDate() {
            var valid = true;
            var currentDate = moment();
            var lastRecDate = moment(this.last_recon_date, 'DD/MM/YYYY');
            var newLastRecDate = null;
            if (this.edit_last_recon_mdl.action == 'save') {
                newLastRecDate = moment(this.bank_date, 'DD/MM/YYYY');
            } else {
                newLastRecDate = moment(this.edit_last_recon_mdl.recon_date, 'DD/MM/YYYY');
            }

            if (this.edit_last_recon_mdl.action == 'edit') {
                if (newLastRecDate > lastRecDate) {
                    valid = false;
                    this.$noty.error(
                        'Entered last reconciled date must not be greater than the current last reconciled date.',
                    );
                } else if (newLastRecDate > currentDate) {
                    valid = false;
                    this.$noty.error('Entered last reconciled date must not be greater than the current date.');
                }
            }

            if (valid && newLastRecDate.isValid()) {
                let request = { bank: this.bank, recon_date: newLastRecDate.format('DD/MM/YYYY') };
                this.$api.post('auto-bank-recon/page/update/recon_date', this.req(request)).then((response) => {
                    this.edit_last_recon_mdl.show = false;
                    this.edit_last_recon_mdl.action = 'edit';
                    this.view_report_mdl.show = false;
                    this.load();
                });
            }
        },
        updateBankRecon() {
            this.edit_last_recon_mdl.show = true;
            this.edit_last_recon_mdl.action = 'save';
            this.edit_last_recon_mdl.save_date_txt = moment(this.bank_date, 'DD/MM/YYYY')
                .add(1, 'days')
                .format('DD/MM/YYYY');
        },
        viewReport() {
            this.view_report_mdl.show = true;
            let opening_bal = 0;
            let closing_bal = 0;

            this.view_report_mdl.totals = { DIR: 0, BPA: 0, CHQ: 0, ADJ: 0 };
            this.view_report_mdl.adjusted = 0;
            this.view_report_mdl.receipts = 0;
            this.view_report_mdl.payments = 0;
            this.view_report_mdl.cash_book_opening_bal = 0;
            this.view_report_mdl.out_by = 0;

            // CALCULATE TOTAL AMOUNTS
            if (this.list.lines) {
                for (var date in this.list.lines) {
                    //#
                    if (this.list.lines[date].rows && this.list.lines[date].rows.length > 0) {
                        for (var i = 0; i < this.list.lines[date].rows.length; i++) {
                            let row = this.list.lines[date].rows[i];
                            // if(row.match_amount && (row.match_presented_date == 'present' || row.match_presented_date == 'unpresent'))
                            //   unpresented_total += parseFloat(row.match_amount)
                            if (row.status == 'adjustment') {
                                if (
                                    !row.statement_details.remove_date ||
                                    moment(this.bank_date, 'DD/MM/YYYY') <
                                        moment(row.statement_details.remove_date, 'DD/MM/YYYY')
                                ) {
                                    this.view_report_mdl.totals['ADJ'] += parseFloat(row.statement_amount);
                                }
                            } else {
                                if (row.match_amount && (row.match_details || row.match_props)) {
                                    if (row.match_details) {
                                        if (this.view_report_mdl.totals.hasOwnProperty(row.match_details.trans_type)) {
                                            if (
                                                !row.match_details.presented_date ||
                                                row.match_details.presented_date == 'present' ||
                                                row.match_details.presented_date == 'unpresent'
                                            ) {
                                                this.view_report_mdl.totals[row.match_details.trans_type] += Math.abs(
                                                    parseFloat(row.match_amount),
                                                );
                                            } else if (
                                                moment(this.bank_date, 'DD/MM/YYYY') <
                                                moment(row.match_details.presented_date, 'DD/MM/YYYY')
                                            ) {
                                                this.view_report_mdl.totals[row.match_details.trans_type] += Math.abs(
                                                    parseFloat(row.match_amount),
                                                );
                                            }
                                        }
                                    } else if (row.match_props) {
                                        if (this.view_report_mdl.totals.hasOwnProperty(row.match_props.trans_type)) {
                                            if (
                                                !row.match_props.presented_date ||
                                                row.match_props.presented_date == 'present' ||
                                                row.match_props.presented_date == 'unpresent'
                                            ) {
                                                this.view_report_mdl.totals[row.match_props.trans_type] += Math.abs(
                                                    parseFloat(row.match_amount),
                                                );
                                            } else if (
                                                moment(this.bank_date, 'DD/MM/YYYY') <
                                                moment(row.match_props.presented_date, 'DD/MM/YYYY')
                                            ) {
                                                this.view_report_mdl.totals[row.match_props.trans_type] += Math.abs(
                                                    parseFloat(row.match_amount),
                                                );
                                            }
                                        }
                                    }
                                }
                            }
                            // if status end
                        }
                    }
                    //#
                }
            }

            if (this.opening_bal) opening_bal = parseFloat(this.opening_bal);

            if (this.closing_bal) closing_bal = parseFloat(this.closing_bal);

            this.view_report_mdl.adjusted =
                parseFloat(closing_bal) +
                parseFloat(this.view_report_mdl.totals.DIR) -
                parseFloat(this.view_report_mdl.totals.BPA) -
                parseFloat(this.view_report_mdl.totals.CHQ) -
                parseFloat(this.view_report_mdl.totals.ADJ);

            // console.log(this.view_report_mdl)
            // GET RECEIPTS AND PAYMENT TOTALS
            let request = { bank: this.bank, from_date: this.last_recon_date, to_date: this.bank_date };
            this.$api.post('auto-bank-recon/page/load/receipts_payments', this.req(request)).then((response) => {
                let receipts = 0;
                if (response.data.hasOwnProperty('receipts') && response.data.receipts) {
                    receipts = response.data.receipts;
                }
                this.view_report_mdl.receipts = receipts;
                let payments = 0;
                if (response.data.hasOwnProperty('payments') && response.data.payments) {
                    payments = response.data.payments;
                }
                this.view_report_mdl.payments = payments;
                let cash_book_opening_bal = 0;
                if (response.data.hasOwnProperty('cash_book_opening_bal') && response.data.cash_book_opening_bal) {
                    cash_book_opening_bal = response.data.cash_book_opening_bal;
                }
                this.view_report_mdl.cash_book_opening_bal = cash_book_opening_bal;

                let differences =
                    parseFloat(closing_bal) +
                    parseFloat(this.view_report_mdl.totals.DIR) -
                    parseFloat(this.view_report_mdl.totals.CHQ) -
                    parseFloat(this.view_report_mdl.totals.BPA) -
                    parseFloat(this.view_report_mdl.totals.ADJ) -
                    (parseFloat(cash_book_opening_bal) + parseFloat(receipts) - parseFloat(payments) * -1);
                let out_by = 0;
                if (differences != 0) {
                    out_by = differences;
                    if (differences < 0) out_by = differences * -1;
                }
                if (isNaN(out_by)) out_by = 0;

                this.view_report_mdl.out_by = parseFloat(out_by).toFixed(2);
            });
        },
        downloadReportPDF() {
            var params = {
                lastReconDate: this.last_recon_date,
                currReconDate: this.bank_date,
                bankID: this.bank,
                bankIDName: this.bank_name,
                bankStatementBal: this.$filters.num2AmtInt(this.closing_bal),
                depositsTotal: this.$filters.num2AmtInt(this.view_report_mdl.totals.DIR),
                eftbpaysTotal: this.$filters.num2AmtInt(this.view_report_mdl.totals.BPA * -1),
                chequesTotal: this.$filters.num2AmtInt(this.view_report_mdl.totals.CHQ * -1),
                adjustmentsTotal: this.$filters.num2AmtInt(this.view_report_mdl.totals.ADJ * -1),
                paymentsPeriod: this.$filters.num2AmtInt(this.view_report_mdl.payments),
                receiptsPeriod: this.$filters.num2AmtInt(this.view_report_mdl.receipts * -1),
                outBy: this.$filters.num2AmtInt(this.view_report_mdl.out_by),
                cashBookCloseBal: this.$filters.num2AmtInt(
                    this.view_report_mdl.cash_book_opening_bal +
                        this.view_report_mdl.receipts -
                        this.view_report_mdl.payments * -1,
                ),
                cashBookOpenBal: this.$filters.num2AmtInt(this.view_report_mdl.cash_book_opening_bal),
                adjustedBal: this.$filters.num2AmtInt(this.view_report_mdl.adjusted),
            };

            this.$api.post('auto-bank-recon/page/generate/pdf', this.req(params)).then((response) => {
                // let fileFormat = 'pdf';
                // let blob = new Blob([window.atob(response.data.pdf)], {type: 'application/'+fileFormat});
                // let a    = document.createElement("a");
                // a.style  = "display: none";
                // document.body.appendChild(a);
                // let url    = window.URL.createObjectURL(blob);
                // a.href     = url;
                // a.download = fileName+'.'+fileFormat;
                // a.click();
                var fileName =
                    'BRR_10099_' +
                    moment().format('DDMMYYYY') +
                    '_balance_' +
                    this.$filters.num2AmtInt(this.view_report_mdl.out_by) +
                    '_' +
                    this.bank +
                    '.pdf';
                this.downloadFile(response.data.pdf, fileName, 'pdf');
            });
        },
        downloadFile(data, name, format) {
            var type = '';
            if (format == 'xslx') type = 'vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64';
            else type = 'data:application/' + format + ';base64,' + data;
            let blob = new Blob([this.s2ab(atob(data))], { type: 'application/' + type });
            let a = document.createElement('a');
            a.style = 'display: none';
            document.body.appendChild(a);
            let url = window.URL.createObjectURL(blob);
            a.href = url;
            // a.download = name+'.'+format;
            a.download = name;
            a.click();
        },
        s2ab(s) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
            return buf;
        },
        load() {
            if (this.bank && this.bank_date) {
                let bank_details = this.bank_list.filter((row) => {
                    return row.value == this.bank;
                });
                if (bank_details && bank_details.length > 0) {
                    this.bank_name = bank_details[0].label;
                }

                let dl = false;
                let bk = this.bank_list.find((row) => {
                    return row.bankID == this.bank;
                });
                if (bk.username && bk.client_no) {
                    dl = true;
                }
                this.has_download = dl;

                let request = {
                    bank: this.bank,
                    bank_date: this.bank_date,
                    on_after_recon_only: this.on_after_recon_only ? 1 : 0,
                };
                this.$api.post('auto-bank-recon/page/list', this.req(request)).then((response) => {
                    // console.log(response.data)
                    this.no_bank_date = false;
                    if (response.data.hasOwnProperty('no_bank_date') && response.data.no_bank_date) {
                        this.no_bank_date = !!+response.data.no_bank_date;
                    }
                    if (response.data.hasOwnProperty('opening_bal') && response.data.opening_bal) {
                        this.opening_bal = response.data.opening_bal;
                    }
                    if (response.data.hasOwnProperty('is_bank_barred')) {
                        this.is_bank_barred = response.data.is_bank_barred;
                    }
                    if (response.data.hasOwnProperty('last_recon_date')) {
                        this.last_recon_date = response.data.last_recon_date;
                    }
                    if (response.data.hasOwnProperty('header') && response.data.header) {
                        this.bank_header = response.data.header;
                        if (response.data.header.closing_bal) {
                            let close_bal_val = parseFloat(response.data.header.closing_bal);
                            this.closing_bal = close_bal_val;
                            this.closing_bal_formatted = close_bal_val
                                .toFixed(2)
                                .toString()
                                .replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,');
                        }
                    }
                    if (response.data.match_list) {
                        this.default_match_list = response.data.match_list;
                    }
                    if (response.data.recon_list) {
                        let list = response.data.recon_list;
                        for (var type in list) {
                            list[type].rows.sort(
                                this.sort_by(
                                    { name: 'statement_amount', primer: parseFloat, reverse: true },
                                    { name: 'is_sub', reverse: false },
                                    { name: 'match_amount', primer: parseFloat, reverse: true },
                                ),
                            );
                            let sort_ctr = 1;
                            let join_ids = [];
                            list[type].rows.map((row, row_index) => {
                                // let has_join = false;
                                // if(row.temp_join_id && join_ids.indexOf(row.temp_join_id) !== -1){
                                //   has_join = true
                                //   console.log(row)
                                // }
                                if (row.match_details) {
                                    let match = row.match_details;
                                    row.match_date = match.trans_date;
                                    row.match_trans_type = match.trans_type;
                                    row.match_ref = match.ref;
                                    row.match_payee = match.payee;
                                    row.match_amount = match.amount;
                                    row.match_presented_date = match.presented_date;
                                    row.match_is_presented = match.is_presented;
                                }
                                if (row.status == 'adjustment') {
                                    row.statement_details.is_modified = false;
                                }
                                row.statement_selected = false;
                                row.match_selected = false;
                                row.orig_status = row.status;
                                row.orig_sort_no = sort_ctr;
                                row.sort_no = sort_ctr;

                                if (!row.temp_join_id) row.temp_join_id = 0;
                                else join_ids.push(row.temp_join_id);

                                sort_ctr++;
                                return row;
                            });
                        }
                        // console.log(list)
                        this.list.lines = list;
                    }
                    this.show_results = false;
                });

                this.list.filters.statuses = ['pending', 'adjustment', 'approved'];
            }
        },
        download(dl) {
            if (dl) {
                this.load();
            }
        },
        match_container_style() {
            this.table_width = document.querySelectorAll('.c8-page')[0].clientWidth + 10;
        },
        cloneTableHeader() {
            // let cells1 = document.getElementById("to-float-header-1").cells
            // let fixed_rows1 = document.getElementById('to-float-header-clone-1');
            // for (var i = 0; i < cells1.length; i++) {
            //   let cell = cells1[i]
            //   fixed_rows1.cells[i].style.width = cell.getBoundingClientRect().width +'px';
            // }
            let cells2 = document.getElementById('to-float-header-2').cells;
            let fixed_rows2 = document.getElementById('to-float-header-clone-2');
            for (var i = 0; i < cells2.length; i++) {
                let cell = cells2[i];
                fixed_rows2.cells[i].style.width = cell.getBoundingClientRect().width + 'px';
            }
            let cells3 = document.getElementById('to-float-header-3').cells;
            let fixed_rows3 = document.getElementById('to-float-header-clone-3');
            for (var i = 0; i < cells3.length; i++) {
                let cell = cells3[i];
                fixed_rows3.cells[i].style.width = cell.getBoundingClientRect().width + 'px';
            }

            // let cells = document.getElementById("to-float-header").cells
            // let fixed_rows = document.getElementById('header-fixed').getElementsByTagName('tbody')[0].rows[0];
            // for (var i = 0; i < cells.length; i++) {
            //   let cell = cells[i]
            //   fixed_rows.cells[i].style.width = cell.getBoundingClientRect().width +'px';
            // }

            // console.log(fixed_rows);
            // let fixed_rows = document.getElementById('header-fixed').getElementsByTagName('tbody')[0].rows[0];
        },
        handleScroll(event) {
            this.cloneTableHeader();
            let offset = document.getElementById('main_container').scrollTop;
            // Any code to be executed when the window is scrolled
            let main_tbl = document.getElementById('main-table');
            let fixed_tbl = document.getElementById('header-fixed');
            let pos = this.findPos(main_tbl);
            // fixed_tbl.style.width = (main_tbl.offsetWidth + 204) + "px"
            let fixed_tbl_style = window.getComputedStyle(fixed_tbl);
            let offtop = pos.top + 550;

            if (offset >= offtop && fixed_tbl_style.display === 'none') {
                fixed_tbl.style.display = 'block';
            } else if (offset < offtop) {
                fixed_tbl.style.display = 'none';
            }
        },
        findPos(element) {
            var top = 0,
                left = 0;
            do {
                top += element.offsetTop - element.scrollTop || 0;
                left += element.offsetLeft - element.scrollLeft || 0;
                element = element.offsetParent;
            } while (element);
            return {
                top: top,
                left: left,
            };
        },
        findPosOffsetTop(element) {
            var top = 0;
            do {
                top += element.offsetTop || 0;
                element = element.offsetParent;
            } while (element);
            return top;
        },
        loadCountryDefaults: function () {
            let form_data = new FormData();
            let api_url = 'country_defaults/load';
            this.$admin.post(api_url, form_data).then((response) => {
                this.country_defaults = response.data.default;
                this.updateHeaders();
            });
        },
        updateHeaders: function () {
            let currency = this.country_defaults.currency_symbol;
            this.list.headers.forEach(function (header) {
                if (header.label.includes('$')) {
                    header.label = header.label.replace('$', currency);
                }
            });
        },
    },
    mounted() {
        let today = moment().subtract(1, 'days');
        while ([0, 6].indexOf(today.day()) !== -1) {
            today = today.subtract(1, 'days');
        }
        let use_date = today.format('DD/MM/YYYY');
        this.$refs.bank_date.changeDate(use_date);

        // this.$refs.bank_date.changeDate(moment().subtract(1,"days").format("DD/MM/YYYY"))
        // this.$refs['add-adj-issue-date'].changeDate(moment().subtract(1,"days").format("DD/MM/YYYY"))

        this.$api.post('auto-bank-recon/page/load').then((response) => {
            if (response.data.bank_list) {
                this.setList(response.data.bank_list);
                if (this.bank_list && this.bank_list.length == 1) {
                    this.bank = this.bank_list[0].value;
                    this.load();
                }
            }
        });
        this.match_container_style();
        this.loadCountryDefaults();
    },
    created() {
        document.getElementById('main_container').addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
        document.getElementById('main_container').removeEventListener('scroll', this.handleScroll);
    },
};
</script>
<style lang="scss">
@import '../../../../sass/variables';
.statement-sub-row {
    display: none;
}
.match-container {
    background-color: #ffffff;
    position: fixed;
    bottom: 0px;
    padding: 0 1.5rem;
    -webkit-box-shadow: 0px -2px 3px rgba(50, 50, 50, 0.1);
    -moz-box-shadow: 0px -2px 3px rgba(50, 50, 50, 0.1);
    box-shadow: 0px -2px 3px rgba(50, 50, 50, 0.1);
    border: 1px solid $c8-border-color;
}
.match-link-btn {
    margin-left: -16px;
}
</style>
