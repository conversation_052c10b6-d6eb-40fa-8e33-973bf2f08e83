<template>
    <v-container
        fluid
        class="c8-page"
    >
        <cirrus-page-header
            title="Workorder Report"
            subtitle="This report can be run for selected properties and displays either open or completed workorders."
        />
        <div class="page-form">
            <div class="form-title-row">Report Options</div>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Report Type</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="report_type"
                        class="form-toggle"
                        @change="resetScreen()"
                        mandatory
                    >
                        <v-btn small>Completed</v-btn>
                        <v-btn small>Open</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="report_type == 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Date Range</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-date-picker
                        v-model="from_date"
                        id="date-from"
                        ref="from_date"
                    />
                    <span style="disply: inline-block; margin: 0 5px"> - </span>
                    <cirrus-date-picker
                        v-model="to_date"
                        id="date-to"
                        ref="to_date"
                        @input="updateReportFor()"
                    />
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="report_type == 1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >As of Date</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-date-picker
                        v-model="to_date"
                        id="date-to"
                        ref="to_date"
                        @input="updateReportFor()"
                    />
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Select Property</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="properties"
                        v-bind:options="properties_select"
                        v-if="properties_select.length > 0"
                    />
                </v-col>
            </v-row>
            <div class="form-title-row">Advanced Options</div>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Sort by</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="sort_by"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn small>{{
                            report_type_opt[report_type] == 'open' ? 'Order Date' : 'Completion Date'
                        }}</v-btn>
                        <v-btn small>Reference #</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Format</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="format"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn small>PDF</v-btn>
                        <v-btn small>Excel Spreadsheet</v-btn>
                        <v-btn small>Print to Screen</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="format != 2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Report For</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-text-field v-model="report_for" />
                    <span class="form-helper-text">will appear on your report header</span>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="format != 2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >File Name Description</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-text-field v-model="filename" />
                    <span class="form-helper-text">will apper in your file name</span>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Include Logo</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="has_logo"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn small>Yes</v-btn>
                        <v-btn small>No</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                ></v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input text-right"
                >
                    <v-btn
                        class="c8-btn"
                        depressed
                        small
                        @click="generate()"
                        >Generate Report</v-btn
                    >
                </v-col>
            </v-row>
        </div>
        <div
            class="page-list"
            v-if="on_screen && this.formats[this.format] == 'screen'"
        >
            <div class="c8-page-table">
                <div v-for="(property, property_code) in on_screen_rows">
                    <div class="form-title-row">{{ property.property_code }} - {{ property.property_name }}</div>
                    <table
                        id="main-table"
                        v-if="Object.keys(property.complete).length > 0 || Object.keys(property.open).length > 0"
                    >
                        <tbody>
                            <template v-if="Object.keys(property.complete).length > 0">
                                <tr class="c8-page-table-sub-header">
                                    <td colspan="7">
                                        <strong>Completed Workorders for {{ from_date }} to {{ to_date }}</strong>
                                    </td>
                                </tr>
                                <template v-for="(workorder, ref_no) in property.complete">
                                    <tr class="c8-page-table-sub-header">
                                        <td>Workorder #</td>
                                        <td>Supplier Code</td>
                                        <td>Supplier Name</td>
                                        <td colspan="2">Completion Date</td>
                                        <td>Tenant Code</td>
                                        <td>Tenant Name</td>
                                    </tr>
                                    <tr>
                                        <td>{{ workorder.ref_no }}</td>
                                        <td>{{ workorder.supplier_code }}</td>
                                        <td>{{ workorder.supplier_name }}</td>
                                        <td colspan="2">{{ workorder.complete_date }}</td>
                                        <td>{{ workorder.tenant_code }}</td>
                                        <td>{{ workorder.tenant_name }}</td>
                                    </tr>
                                    <tr class="c8-page-table-row-header">
                                        <td>Line #</td>
                                        <td>Service Category</td>
                                        <td>Service</td>
                                        <td>Description</td>
                                        <td>Required By Date</td>
                                        <td>Account Code</td>
                                        <td style="text-align: right">Amount {{ currency_symbol }}</td>
                                    </tr>
                                    <tr v-for="(line, key) in workorder.lines">
                                        <td>{{ key + 1 }}</td>
                                        <td>{{ line.service_category }}</td>
                                        <td>{{ line.service_name }}</td>
                                        <td>{{ line.description }}</td>
                                        <td>{{ line.require_date }}</td>
                                        <td>{{ line.account_desc }}</td>
                                        <td style="text-align: right">{{ line.amount | num2AmtStr }}</td>
                                    </tr>
                                </template>
                            </template>
                            <template v-if="Object.keys(property.open).length > 0">
                                <tr class="c8-page-table-sub-header">
                                    <td colspan="7">
                                        <strong>Open Workorders as of {{ to_date }}</strong>
                                    </td>
                                </tr>
                                <template v-for="(workorder, ref_no) in property.open">
                                    <tr class="c8-page-table-sub-header">
                                        <td>Workorder #</td>
                                        <td>Supplier Code</td>
                                        <td>Supplier Name</td>
                                        <td colspan="2">Order Date</td>
                                        <td>Tenant Code</td>
                                        <td>Tenant Name</td>
                                    </tr>
                                    <tr>
                                        <td>{{ workorder.ref_no }}</td>
                                        <td>{{ workorder.supplier_code }}</td>
                                        <td>{{ workorder.supplier_name }}</td>
                                        <td colspan="2">{{ workorder.order_date }}</td>
                                        <td>{{ workorder.tenant_code }}</td>
                                        <td>{{ workorder.tenant_name }}</td>
                                    </tr>
                                    <tr class="c8-page-table-row-header">
                                        <td>Line #</td>
                                        <td>Service Category</td>
                                        <td>Service</td>
                                        <td>Description</td>
                                        <td>Required By Date</td>
                                        <td>Account Code</td>
                                        <td style="text-align: right">Amount {{ currency_symbol }}</td>
                                    </tr>
                                    <tr v-for="(line, key) in workorder.lines">
                                        <td>{{ key + 1 }}</td>
                                        <td>{{ line.service_category }}</td>
                                        <td>{{ line.service_name }}</td>
                                        <td>{{ line.description | strtruncate }}</td>
                                        <td>{{ line.require_date }}</td>
                                        <td>{{ line.account_desc }}</td>
                                        <td style="text-align: right">{{ line.amount | num2AmtStr }}</td>
                                    </tr>
                                </template>
                            </template>
                        </tbody>
                    </table>
                    <table
                        id="main-table"
                        v-else
                    >
                        <tbody>
                            <tr>
                                <td>No {{ report_type_opt[report_type] }} workorders found.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </v-container>
</template>
<script>
import moment from 'moment';
import VueDualListSelect from '../../elements/VueDualListSelect.vue';

export default {
    name: 'assetExpenditureReport',
    components: {
        'vue-dual-list-select': VueDualListSelect,
    },
    data() {
        return {
            on_screen: false,
            on_screen_rows: [],
            from_date: null,
            to_date: null,
            filename: null,
            report_for: null,
            format: 2,
            has_logo: 0,
            report_type: 0,
            sort_by: 0,
            sorts: ['date', 'reference'],
            formats: ['pdf', 'excel', 'screen'],
            has_logo_opt: ['yes', 'no'],
            report_type_opt: ['completed', 'open'],
            properties: [],
            property_list: [],
            currency_symbol: '$',
        };
    },
    computed: {
        curr_report_type() {
            return this.report_type_opt[this.report_type];
        },
        properties_select() {
            let data = [];
            if (this.property_list.length > 0) {
                let groups = {};
                for (var i = 0; i < this.property_list.length; i++) {
                    let row = this.property_list[i];
                    if (typeof groups[row.fieldGroup] === 'undefined') {
                        groups[row.fieldGroup] = {
                            fieldGroupNames: row.fieldGroup,
                            fieldGroupValues: [row],
                        };
                    } else {
                        groups[row.fieldGroup].fieldGroupValues.push(row);
                    }
                }
                for (var grp in groups) {
                    data.push(groups[grp]);
                }
            }
            return data;
        },
    },
    methods: {
        screenPrint(rows) {
            // console.log(rows)
            this.on_screen = true;
            this.on_screen_rows = rows;
        },
        resetScreen() {
            this.on_screen_rows = [];
            this.on_screen = false;
        },
        generate() {
            this.on_screen_rows = [];
            this.on_screen = false;

            if (!this.from_date || !this.to_date || this.properties.length == 0) {
                this.$noty.error('Please complete the required fields');
                return false;
            } else if ((this.formats[this.format] == 'pdf' || this.formats[this.format] == 'excel') && !this.filename) {
                this.$noty.error('Please add the Report for and File Name Description');
                return false;
            }
            if (this.report_for.length > 55) {
                this.$noty.error('Report for field can only have a maximum of 55 characters');
                return false;
            }

            let params = {
                client_db: localStorage.getItem('currentDB'),
                trans_type: 'workorder',
                owner_template: 1,
                use_client_logo: 1,
                has_logo: this.has_logo_opt[this.has_logo] == 'yes' ? 1 : 0,
            };
            let properties = this.properties.map((row) => {
                return row.value;
            });
            params.filters = JSON.stringify({
                property_code: properties,
                target_date_enabled: 0,
                service_id: null,
                created_at_enabled: 1,
                owner_workorder_type: this.report_type_opt[this.report_type] == 'open' ? 'open' : 'complete',
                ordered_at: {
                    from: moment(this.from_date, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                    to: moment(this.to_date, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                    only_to_date: this.report_type_opt[this.report_type] == 'open' ? 1 : 0,
                },
            });

            let sort_by = '';
            if (this.sorts[this.sort_by] == 'date') {
                if (this.report_type_opt[this.report_type] == 'open') {
                    sort_by = 'order_date';
                } else sort_by = 'complete_date';
            } else sort_by = this.sorts[this.sort_by];

            params.options = JSON.stringify({
                format: this.formats[this.format],
                filename: this.filename,
                report_for: this.report_for,
                group_by: null,
                sort_by: sort_by,
                sort_type: 'asc',
            });

            this.$fm.post('reports/generate', this.req(params)).then((response) => {
                let res = response.data;

                if (res.file && res.file.name && res.file.type && res.file.data) {
                    this.printDownload(res.file.data, res.file.name + moment().unix(), res.file.type);
                } else if (res.plot && res.plot.rows) {
                    this.screenPrint(res.plot.rows);
                } else {
                    if (res.error) this.$noty.error(res.error);
                    else this.$noty.error('Generate report failed. Please try again.');
                }
            });
        },
        loadPage() {
            this.$refs.from_date.changeDate(moment().format('01/MM/YYYY'));
            // this.$refs.from_date.changeDate(moment().subtract(30,"days").format("01/MM/YYYY"))
            this.$refs.to_date.changeDate(moment().format('DD/MM/YYYY'));
            this.format = 2;
            this.has_logo = 0;
            this.on_screen = false;
            this.filename = 'Workorder_Report';
            this.report_for = this.from_date + ' to ' + this.to_date;
            // ----
            this.$api.post('management-report/workorder-report/page/load').then((response) => {
                if (response.data.property_list) {
                    this.property_list = response.data.property_list;
                }
                if (response.data.currency_symbol) {
                    this.currency_symbol = response.data.currency_symbol;
                }
            });
        },
        updateReportFor() {
            if (this.to_date) {
                this.report_for = 'As of ' + this.to_date;
            } else {
                this.report_for = '';
            }
        },
    },
    mounted() {
        this.loadPage();
    },
};
</script>
