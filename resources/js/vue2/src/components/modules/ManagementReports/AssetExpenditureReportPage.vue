<template>
    <v-container
        fluid
        class="c8-page"
    >
        <cirrus-page-header
            title="Asset Expenditure Report"
            subtitle="This form allows you generate report about the assets on cirrus FM."
        />
        <div class="page-form">
            <div class="form-title-row">Report Options</div>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Date Range</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-date-picker
                        v-model="from_date"
                        id="date-from"
                        ref="from_date"
                    />
                    <span style="disply: inline-block; margin: 0 5px"> - </span>
                    <cirrus-date-picker
                        v-model="to_date"
                        id="date-to"
                        ref="to_date"
                    />
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Select Property</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="properties"
                        v-bind:options="properties_select"
                        v-if="properties_select.length > 0"
                    />
                </v-col>
            </v-row>
            <div class="form-title-row">Advance Options</div>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Type</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="opt_type"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn small>Cash</v-btn>
                        <v-btn small>Accruals</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Sort by</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="sort_by"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn small>Date</v-btn>
                        <v-btn small>Account No.</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Format</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="format"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn small>PDF</v-btn>
                        <v-btn small>Excel Spreadsheet</v-btn>
                        <v-btn small>Print to Screen</v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="format != 2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Report For</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-text-field v-model="report_for" />
                    <span class="form-helper-text">will appear on your report header</span>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="format != 2"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >File Name Description</v-col
                >
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-text-field v-model="filename" />
                    <span class="form-helper-text">will apper in your file name</span>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                ></v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input text-right"
                >
                    <v-btn
                        depressed
                        small
                        class="c8-btn"
                        @click="generate()"
                        >Generate Report</v-btn
                    >
                </v-col>
            </v-row>
        </div>
        <div
            class="page-list"
            v-if="on_screen"
        >
            <div class="c8-page-table">
                <div v-for="(property, property_code) in on_screen_rows">
                    <div class="form-title-row">{{ property.details.code + ' - ' + property.details.name }}</div>
                    <table id="main-table">
                        <tbody>
                            <tr class="c8-page-table-row-header">
                                <td>Account Name</td>
                                <td>Payee</td>
                                <td>Invoice #</td>
                                <td>Workorder #</td>
                                <td>Asset Code</td>
                                <td>Asset Name</td>
                                <td style="text-align: right">Net {{ currency_symbol }}</td>
                                <td style="text-align: right">{{ tax_label }} {{ currency_symbol }}</td>
                                <td style="text-align: right">Gross {{ currency_symbol }}</td>
                            </tr>
                            <template v-for="(group, group_code) in property.operating_exp.rows">
                                <template v-if="group.subgroups && !Array.isArray(group.subgroups)">
                                    <tr class="c8-page-table-sub-header">
                                        <td colspan="9">
                                            <strong>{{ group.title }}</strong>
                                        </td>
                                    </tr>
                                    <template v-for="(subgroup, subgroup_code) in group.subgroups">
                                        <tr>
                                            <td colspan="9">
                                                <strong>{{ subgroup.title }}</strong>
                                            </td>
                                        </tr>
                                        <template v-for="(row, index) in subgroup.rows">
                                            <tr>
                                                <td>{{ row['account_desc'] }}</td>
                                                <td>{{ row['payee'] }}</td>
                                                <td>{{ row['invoice_no'] }}</td>
                                                <td>{{ row['workorder_no'] }}</td>
                                                <td>{{ row['asset_code'] }}</td>
                                                <td>{{ row['asset_name'] | strtruncate }}</td>
                                                <td style="text-align: right">{{ row['net'] | num2AmtStr }}</td>
                                                <td style="text-align: right">{{ row['gst'] | num2AmtStr }}</td>
                                                <td style="text-align: right">{{ row['gross'] | num2AmtStr }}</td>
                                            </tr>
                                        </template>
                                        <tr class="c8-page-table-footer">
                                            <td colspan="6">Total {{ subgroup['title'] }}</td>
                                            <td style="text-align: right">
                                                {{ subgroup['totals']['net'] | num2AmtStr }}
                                            </td>
                                            <td style="text-align: right">
                                                {{ subgroup['totals']['gst'] | num2AmtStr }}
                                            </td>
                                            <td style="text-align: right">
                                                {{ subgroup['totals']['gross'] | num2AmtStr }}
                                            </td>
                                        </tr>
                                    </template>
                                    <tr class="c8-page-table-footer">
                                        <td colspan="6">Total {{ group['title'] }}</td>
                                        <td style="text-align: right">{{ group['totals']['net'] | num2AmtStr }}</td>
                                        <td style="text-align: right">{{ group['totals']['gst'] | num2AmtStr }}</td>
                                        <td style="text-align: right">{{ group['totals']['gross'] | num2AmtStr }}</td>
                                    </tr>
                                    <tr class="c8-page-table-footer">
                                        <td colspan="6">Total Operating Expenditure</td>
                                        <td style="text-align: right">
                                            {{ property['operating_exp']['totals']['net'] | num2AmtStr }}
                                        </td>
                                        <td style="text-align: right">
                                            {{ property['operating_exp']['totals']['gst'] | num2AmtStr }}
                                        </td>
                                        <td style="text-align: right">
                                            {{ property['operating_exp']['totals']['gross'] | num2AmtStr }}
                                        </td>
                                    </tr>
                                </template>
                            </template>
                            <template v-for="(group, group_code) in property.others_exp.rows">
                                <template v-if="group.subgroups && !Array.isArray(group.subgroups)">
                                    <tr class="c8-page-table-sub-header">
                                        <td colspan="9">{{ group.title }}</td>
                                    </tr>
                                    <template v-for="(subgroup, subgroup_code) in group.subgroups">
                                        <tr>
                                            <td colspan="9">
                                                <strong>{{ subgroup.title }}</strong>
                                            </td>
                                        </tr>
                                        <template v-for="(row, index) in subgroup.rows">
                                            <tr>
                                                <td>{{ row['account_desc'] }}</td>
                                                <td>{{ row['payee'] }}</td>
                                                <td>{{ row['invoice_no'] }}</td>
                                                <td>{{ row['workorder_no'] }}</td>
                                                <td>{{ row['asset_code'] }}</td>
                                                <td>{{ row['asset_name'] | strtruncate }}</td>
                                                <td style="text-align: right">{{ row['net'] | num2AmtStr }}</td>
                                                <td style="text-align: right">{{ row['gst'] | num2AmtStr }}</td>
                                                <td style="text-align: right">{{ row['gross'] | num2AmtStr }}</td>
                                            </tr>
                                        </template>
                                        <tr class="c8-page-table-footer">
                                            <td colspan="6">Total {{ subgroup['title'] }}</td>
                                            <td style="text-align: right">
                                                {{ subgroup['totals']['net'] | num2AmtStr }}
                                            </td>
                                            <td style="text-align: right">
                                                {{ subgroup['totals']['gst'] | num2AmtStr }}
                                            </td>
                                            <td style="text-align: right">
                                                {{ subgroup['totals']['gross'] | num2AmtStr }}
                                            </td>
                                        </tr>
                                    </template>
                                </template>
                            </template>
                            <tr class="c8-page-table-footer">
                                <td colspan="6">Total Expenditure</td>
                                <td style="text-align: right">{{ property['totals']['net'] | num2AmtStr }}</td>
                                <td style="text-align: right">{{ property['totals']['gst'] | num2AmtStr }}</td>
                                <td style="text-align: right">{{ property['totals']['gross'] | num2AmtStr }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </v-container>
</template>
<script>
import moment from 'moment';
import VueDualListSelect from '../../elements/VueDualListSelect.vue';

export default {
    name: 'assetExpenditureReport',
    components: {
        'vue-dual-list-select': VueDualListSelect,
    },
    data() {
        return {
            on_screen: false,
            on_screen_rows: [],
            from_date: null,
            to_date: null,
            filename: null,
            report_for: null,
            format: 2,
            sort_by: 0,
            opt_type: 0,
            opt_types: ['cash', 'accruals'],
            sorts: ['pmxc_ap_alloc.pmxc_alloc_dt', 'pmca_chart.pmca_code'],
            formats: ['pdf', 'excel', 'screen'],
            properties: [],
            property_list: [],
            tax_label: 'GST',
            currency_symbol: '$',
        };
    },
    computed: {
        properties_select() {
            let data = [];

            if (this.property_list.length > 0) {
                let groups = {};
                for (var i = 0; i < this.property_list.length; i++) {
                    let row = this.property_list[i];
                    if (typeof groups[row.fieldGroup] === 'undefined') {
                        groups[row.fieldGroup] = {
                            fieldGroupNames: row.fieldGroup,
                            fieldGroupValues: [row],
                        };
                    } else {
                        groups[row.fieldGroup].fieldGroupValues.push(row);
                    }
                }
                for (var grp in groups) {
                    data.push(groups[grp]);
                }
            }

            return data;
        },
    },
    methods: {
        screenPrint(rows) {
            console.log(rows);
            this.on_screen = true;
            this.on_screen_rows = rows;
        },
        generate() {
            if (!this.from_date || !this.to_date || this.properties.length == 0) {
                this.$noty.error('Please complete the required fields');
                return false;
            } else if (
                (this.formats[this.format] == 'pdf' || this.formats[this.format] == 'excel') &&
                (!this.report_for || !this.filename)
            ) {
                this.$noty.error('Please add the Report for and File Name Description');
                return false;
            }

            let params = {
                client_db: localStorage.getItem('currentDB'),
                trans_type: 'assetExpenditure',
                report_template: 'assetExpenditure',
                report_template_title: 'Asset Expenditure Report',
                owner_template: 1,
            };
            let properties = this.properties.map((row) => {
                return row.value;
            });
            params.filters = JSON.stringify({
                property_code: properties,
                target_date_enabled: 0,
                service_id: null,
                created_at_enabled: 1,
                created_at: {
                    from: moment(this.from_date, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                    to: moment(this.to_date, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                },
            });
            params.options = JSON.stringify({
                format: this.formats[this.format],
                filename: this.filename,
                report_for: this.report_for,
                group_by: null,
                sort_by: this.sorts[this.sort_by],
                line_type: this.opt_types[this.opt_type],
                sort_type: 'asc',
            });
            this.$fm.post('reports/generate', this.req(params)).then((response) => {
                let res = response.data;
                if (res.file && res.file.name && res.file.type && res.file.data) {
                    this.printDownload(res.file.data, res.file.name + moment().unix(), res.file.type);
                } else if (res.plot && res.plot.rows) {
                    this.screenPrint(res.plot.rows);
                } else {
                    if (res.error) this.$noty.error(res.error);
                    else this.$noty.error('Generate report failed. Please try again.');
                }
            });
        },
        loadPage() {
            this.$refs.from_date.changeDate(moment().format('01/MM/YYYY'));
            // this.$refs.from_date.changeDate(moment().subtract(4,"years").format("01/MM/YYYY"))
            this.$refs.to_date.changeDate(moment().format('DD/MM/YYYY'));
            this.format = 2;
            this.on_screen = false;
            this.filename = 'Asset_Expenditure_Report';
            this.sort_by = 0;
            this.line_type = 0;
            this.report_for = this.from_date + ' to ' + this.to_date;
            // ----
            this.$api.post('management-report/asset-expenditure-report/page/load').then((response) => {
                if (response.data.property_list) {
                    this.property_list = response.data.property_list;
                    this.tax_label = response.data.tax_label;
                    this.currency_symbol = response.data.currency_symbol;
                }
            });
        },
    },
    mounted() {
        this.loadPage();
    },
};
</script>
