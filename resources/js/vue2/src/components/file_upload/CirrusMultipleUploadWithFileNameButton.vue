<template>
    <div>
        <div class="upload-btn-wrapper">
            <label
                :for="id"
                class="upload-btn-label right v-btn v-btn--contained theme--light v-size--default primary v-btn--small ml-0"
            >
                <v-icon dark>cloud_upload</v-icon>
                &nbsp
                {{ fileUploadButtonName }}
            </label>
            <label
                v-if="hasAttachedFile"
                @click="removeAttachedFile()"
                class="v-btn v-btn--flat v-btn--icon v-btn--small theme--light ml-0"
            >
                <v-icon color="red">close</v-icon>
            </label>
            <input
                :id="id"
                type="file"
                v-on:change="fileSelected()"
                ref="myFiles"
                v-on:input="updateValue($event.target.value)"
                :accept="acceptTypeList[acceptType]"
                multiple
            />
        </div>
        <div
            v-for="(data, index) in attachedFileList"
            :key="id + '-' + index"
        >
            {{ data.name }}
            <v-icon
                color="red"
                @click="removeIndividualAttachedFile(data.name)"
                >close
            </v-icon>
        </div>
    </div>
</template>
<script>
import {
    ACCEPTED_FILE_TYPES,
    ALL_FILE_TYPES,
    FILE_UPLOAD_BUTTON_NAME,
    MEGABYTES_UNIT_SYMBOL,
    TWENTY_MB_IN_BYTES_LIMIT,
    VALIDATION_MESSAGE_FOR_SIZE_LIMIT,
} from './constants';
import isEmpty from 'lodash/isEmpty';

export default {
    props: {
        value: {},
        sizeInBytes: { type: String, default: TWENTY_MB_IN_BYTES_LIMIT },
        id: { type: String, default: '' },
        acceptType: { type: String, default: ALL_FILE_TYPES },
    },
    data() {
        return {
            id: this.id,
            attachedFileList: [],
            fileUploadButtonName: FILE_UPLOAD_BUTTON_NAME,
            acceptTypeList: ACCEPTED_FILE_TYPES,
        };
    },
    computed: {
        hasAttachedFile: function () {
            return !isEmpty(this.attachedFileList);
        },
    },
    methods: {
        getSizeLimitValidationMsg(limit) {
            return `${VALIDATION_MESSAGE_FOR_SIZE_LIMIT} ${limit} ${MEGABYTES_UNIT_SYMBOL}`;
        },
        updateValue(value) {
            this.$emit('input', value);
        },
        fileSelected: function () {
            const files = Array.from(this.$refs.myFiles.files);
            for (const newFile of files) {
                if (newFile.size > this.sizeInBytes) {
                    alert(this.getSizeLimitValidationMsg(Math.ceil(this.sizeInBytes / (1024 * 1024))));
                    return;
                }
                const isDuplicate = this.attachedFileList.some((existingFile) => existingFile.name === newFile.name);
                if (!isDuplicate) {
                    this.attachedFileList.push(newFile);
                }
            }
            this.$refs.myFiles.value = null;
            this.$emit('input', this.attachedFileList);
        },
        removeAttachedFile: function () {
            this.attachedFileList = [];
            this.$refs.myFiles.value = null;
            this.$emit('input', this.attachedFileList);
        },
        removeIndividualAttachedFile: function (name) {
            this.attachedFileList = this.attachedFileList.filter((file) => file.name !== name);
            this.$emit('input', this.attachedFileList);
        },
    },
    watch: {
        value: function (value) {
            if (isEmpty(value)) {
                this.attachedFileList = [];
                this.$refs.myFiles.value = null;
            }
        },
    },
};
</script>
<style>
.upload-btn-wrapper input[type='file'] {
    display: none;
}
</style>
