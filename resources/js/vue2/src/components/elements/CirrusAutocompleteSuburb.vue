<template>
    <div>
        <multiselect
            :placeholder="placeholder"
            :deselectLabel="deselectLabel"
            :selectLabel="selectLabel"
            v-model="select_val"
            track-by="suburb"
            label="label"
            :selectedValue="selectedValue"
            :options="options"
            @input="changed()"
            :show-labels="hasEmpty"
            :allow-empty="hasEmpty"
        >
            <template
                slot="singleLabel"
                slot-scope="{ option }"
            >
                {{ option.suburb }}
            </template>
        </multiselect>
    </div>
</template>
<script>
export default {
    name: 'Cirrus8Autcomplete',
    props: {
        options: { default: [] },
        selectedValue: { default: [] },
        hasEmpty: { default: true },
        placeholder: { default: 'Select an Item' },
        deselectLabel: { default: 'Select to remove' },
        selectLabel: { default: '' },
        return: { default: 'value' },
    },
    data() {
        return {
            suburb: null,
            select_val: null,
        };
    },
    watch: {
        suburb(newVal, oldVal) {
            if (newVal != oldVal) {
                if (newVal && newVal != null) {
                    let find_val = this.options.filter((row) => {
                        return row.suburb == newVal;
                    });
                    if (find_val && find_val.length > 0) this.select_val = find_val[0];
                }
            }
        },
    },
    methods: {
        get_selected() {
            let postal_code = this.selectedValue['pcode'];
            let city = this.selectedValue['suburb'];
            let find_val = this.options.filter((row) => {
                if (row.suburb == city && row.pcode == postal_code) {
                    this.suburb = row.suburb;
                }
            });
        },
        changed() {
            if (this.return == 'all') {
                this.$emit('input', this.select_val);
            } else {
                if (this.select_val && this.select_val[this.return]) this.$emit('input', this.select_val[this.return]);
                else this.$emit('input', null);
            }
        },
    },
    mounted() {
        this.get_selected();
        if (this.suburb && this.suburb != null) {
            let find_val = this.options.filter((row) => {
                return row.suburb == this.suburb;
            });
            if (find_val && find_val.length > 0) this.select_val = find_val[0];
        }
    },
};
</script>
