<template>
    <span>
        <div v-if="edit_form">
            <div v-if="has_saved_file === false">
                <div id="app">
                    <div
                        class="p-12 bg-gray-100 upload-btn-wrapper"
                        @dragover="dragover"
                        @dragleave="dragleave"
                        @drop="drop"
                    >
                        <input
                            type="file"
                            :id="id"
                            @change="fileSelected"
                            v-on:input="updateValue($event.target.value)"
                            ref="myFiles"
                            :accept="accept_type_list[accept_type]"
                        />
                        <label
                            :for="id"
                            class="block cursor-pointer upload-btn-label right v-btn v-btn--contained theme--light v-size--default primary v-btn--small"
                            style="margin-right: 0px"
                        >
                            <v-icon dark>cloud_upload</v-icon> &nbsp
                            {{ file_upload_button_name }}
                        </label>
                        <label
                            v-if="file_upload_button_name !== 'Attachment'"
                            @click="removeAttachedFile()"
                            class="v-btn v-btn--flat v-btn--icon v-btn--small theme--light"
                            style="margin-left: 0px"
                        >
                            <v-icon color="red">close</v-icon>
                        </label>
                    </div>
                </div>
                <v-chip
                    v-if="error_msg.length > 0 && errorData.id === id"
                    v-for="(errorData, index) in error_msg"
                    :key="index"
                    outlined
                    color="error"
                >
                    <v-icon left>error</v-icon>{{ errorData.message }}
                </v-chip>
            </div>
        </div>
        <span v-if="!edit_form && has_saved_file === false">
            <span class="form-input-text">No Attachment</span>
        </span>

        <span v-if="has_saved_file === true">
            <span class="form-input-text">
                <a
                    :href="href"
                    target="_blank"
                >
                    <img
                        v-if="accept_type === '*'"
                        :src="this.$assetDomain + 'assets/images/icons/flat_notes_green_20.png'"
                        alt="attachement"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'csv'"
                        :src="this.$assetDomain + 'assets/images/icons/csv.png'"
                        alt="csv"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'excel'"
                        :src="this.$assetDomain + 'assets/images/icons/xls.png'"
                        alt="excel"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'text'"
                        :src="this.$assetDomain + 'assets/images/icons/text.png'"
                        alt="text"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'image'"
                        :src="this.$assetDomain + 'assets/images/icons/img.png'"
                        alt="image"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'pdf'"
                        :src="this.$assetDomain + 'assets/images/icons/pdf.png'"
                        alt="pdf"
                        class="icon"
                    />Download
                </a>
            </span>
        </span>
    </span>
</template>
<script>
export default {
    props: {
        value: {},
        placeholder: { type: String, default: '' },
        has_saved_file: { type: Boolean, default: false },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        size: { type: String, default: '60' },
        max_length: { type: String, default: '60' },
        id: { type: String, default: '' },
        with_key: { type: Boolean, default: true },
        edit_form: { type: Boolean, default: false },
        accept_type: { type: String, default: '*' },
    },
    data() {
        return {
            cirrus_input: this.value,
            file_holder: null,
            file_upload_button_name: 'Attachment',
            href: 'download.php?fileID=' + this.value,
            accept_type_list: {
                csv: '.csv',
                excel: 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                text: 'text/plain',
                image: 'image/*',
                pdf: '.pdf',
            },
            lc_has_saved_file: this.has_saved_file,
            filelist: [],
        };
    },
    methods: {
        updateValue(value) {
            this.$emit('input', value);
        },
        fileSelected: function () {
            this.file_holder = this.$refs.myFiles.files;

            this.$emit('input', this.file_holder);
            let fileList = this.$refs.myFiles.files;
            if (fileList.length === 1) {
                let fileDetails = fileList[0];
                let fileName = fileDetails.name;
                let fileType = fileDetails.type;
                fileType = fileType.split('/');
                fileType = fileType[1];
                let fileExtension = fileName.split('.');
                let fileNameFirstPart = fileExtension[0];
                fileExtension = fileExtension[fileExtension.length - 1];
                let file_upload_button_name =
                    fileNameFirstPart.substr(0, 5) + '...' + fileNameFirstPart.substr(-3) + '.' + fileExtension;
                this.file_upload_button_name = file_upload_button_name;
            }
        },
        removeAttachedFile: function () {
            this.file_upload_button_name = 'Attachment';
            this.file_holder = null;
            this.$refs.myFiles.value = null;
            this.$emit('input', this.file_holder);
        },
        onChange() {
            this.filelist = [...this.$refs.myFiles.files];
        },
        remove(i) {
            this.filelist.splice(i, 1);
        },
        dragover(event) {
            event.preventDefault();
            // Add some visual fluff to show the user can drop its files
            if (!event.currentTarget.classList.contains('bg-green-300')) {
                event.currentTarget.classList.remove('bg-gray-100');
                event.currentTarget.classList.add('bg-green-300');
            }
        },
        dragleave(event) {
            // Clean up
            event.currentTarget.classList.add('bg-gray-100');
            event.currentTarget.classList.remove('bg-green-300');
        },
        drop(event) {
            event.preventDefault();
            this.$refs.myFiles.files = event.dataTransfer.files;
            this.fileSelected(); // Trigger the onChange event manually
            // Clean up
            event.currentTarget.classList.add('bg-gray-100');
            event.currentTarget.classList.remove('bg-green-300');
        },
    },
    watch: {
        cirrus_input: function () {
            if (this.file_holder === null) {
                this.removeAttachedFile();
            }
            this.$emit('input', this.file_holder);
        },
        value: function () {
            if (this.value === null) {
                this.removeAttachedFile();
            }
        },
    },
};
</script>
<style>
.upload-btn-label {
    background: #939496 !important;
    color: white !important;
    font-weight: bolder;
}

.upload-btn-wrapper input[type='file'] {
    display: none;
}
</style>
