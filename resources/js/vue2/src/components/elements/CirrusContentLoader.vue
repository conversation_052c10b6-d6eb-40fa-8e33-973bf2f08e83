<template>
    <div>
        <v-progress-linear
            v-show="error_code === ''"
            indeterminate
            color="cyan"
        ></v-progress-linear>
        <v-skeleton-loader
            v-if="show_skeleton_loader"
            v-show="error_code === ''"
            class="mx-auto"
            :type="type"
        ></v-skeleton-loader>
        <div
            class="text-center"
            v-show="error_code !== ''"
        >
            <v-alert
                text
                class="my-0"
                prominent
                dense
                tile
                transition="scale-transition"
                color="red lighten-2"
            >
                <v-row
                    align="center"
                    no-gutters
                    dense
                >
                    <v-col
                        class="col-12"
                        style="font-size: 11px"
                    >
                        <div style="padding: 2px">
                            Please send the error code: {{ error_code }} to cirrus8 <NAME_EMAIL>
                        </div>
                    </v-col>
                </v-row>
            </v-alert>
        </div>
    </div>
</template>
<script>
import { bus } from '../../plugins/bus';

export default {
    props: {
        type: { type: String, default: 'list-item-two-line' },
        show_skeleton_loader: { default: true },
    },
    data() {
        return { error_code: '' };
    },
    created() {
        bus.$on('setErrorCode', (data) => {
            this.error_code = data;
        });
    },
};
// import { VclTable } from 'vue-content-loading';
// export default {
//     components: {
//         VclTable
//     },
// }
</script>
