<template>
    <div class="error-message-div">
        <v-alert
            text
            class="my-0"
            prominent
            dense
            tile
            type="error"
            v-if="errorMessage"
            transition="scale-transition"
            color="red lighten-2"
        >
            <v-row
                align="center"
                no-gutters
                dense
            >
                <v-col
                    class="col-12"
                    style="font-size: 11px"
                >
                    {{ errorMessage }}
                </v-col>
            </v-row>
        </v-alert>
        <v-alert
            text
            class="my-0"
            prominent
            dense
            tile
            type="error"
            v-if="error_msg.length > 0"
            transition="scale-transition"
            color="red lighten-2"
        >
            <v-row
                align="center"
                no-gutters
                dense
            >
                <v-col
                    class="col-12"
                    style="font-size: 11px"
                >
                    <div
                        v-for="(value, index) in error_msg"
                        :key="index"
                        style="padding: 2px; text-align: left"
                    >
                        {{ value[0] }}
                        <v-divider
                            class="info"
                            style="opacity: 0.22"
                            v-if="error_msg.length > 1"
                        ></v-divider>
                    </div>
                </v-col>
            </v-row>
        </v-alert>
        <v-alert
            text
            class="my-0"
            prominent
            dense
            tile
            type="error"
            v-if="errorMsg2.length > 0"
            transition="scale-transition"
            color="red lighten-2"
        >
            <v-row
                align="center"
                no-gutters
                dense
            >
                <v-col
                    class="col-12"
                    style="font-size: 11px"
                >
                    <div
                        v-for="(value, index) in errorMsg2"
                        :key="index"
                        style="padding: 2px"
                    >
                        {{ value[0] }}
                        <v-divider
                            class="info"
                            style="opacity: 0.22"
                            v-if="errorMsg2.length > 1"
                        ></v-divider>
                    </div>
                </v-col>
            </v-row>
        </v-alert>
    </div>
</template>
<script>
export default {
    props: {
        value: { type: String, default: '' },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Object,
            default: function () {
                return {};
            },
        },
        errorMsg2: {
            type: Array,
            default: function () {
                return [];
            },
        },
        errorMessage: {
            type: String,
            default: function () {
                return '';
            },
        },
        debug: { type: Boolean, default: false },
        size: { type: String, default: '60' },
        id: { type: String, default: '60' },
        withKey: { type: Boolean, default: true },
        edit_form: { type: Boolean, default: false },
    },
    data() {
        return {};
    },
    methods: {},
    watch: {},
};
</script>
