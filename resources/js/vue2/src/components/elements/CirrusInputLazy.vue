<template>
    <div style="display: inline-block">
        <span v-if="inputFormat === ''">
            <v-text-field
                dense
                v-model.lazy="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                :class="custom_class"
                :placeholder="placeholder"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <!--            <input  type="text" :value="value" :size="size" :maxlength="maxlength" :disabled="disabled" v-if="edit_form" v-on:input="updateValue($event.target.value)">-->
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
            <v-chip
                v-if="error_msg.length > 0 && errorData.id === id"
                v-for="(errorData, index) in error_msg"
                :key="index"
                outlined
                color="error"
            >
                <v-icon left>error</v-icon>{{ errorData.message }}
            </v-chip>
        </span>
        <span v-if="inputFormat === 'search'">
            <v-text-field
                dense
                v-model.lazy="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                :class="custom_class"
                :placeholder="placeholder"
                prepend-inner-icon="search"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <!--            <input  type="text" :value="value" :size="size" :maxlength="maxlength" :disabled="disabled" v-if="edit_form" v-on:input="updateValue($event.target.value)">-->
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
            <v-chip
                v-if="error_msg.length > 0 && errorData.id === id"
                v-for="(errorData, index) in error_msg"
                :key="index"
                outlined
                color="error"
            >
                <v-icon left>error</v-icon>{{ errorData.message }}
            </v-chip>
        </span>
        <span v-if="inputFormat === 'dollar'">
            <v-text-field
                dense
                v-model.lazy="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                v-on:blur="fixDecimalFormat($event)"
                v-on:keypress="validateInputNumber($event)"
                class="cirrus-input-dollar"
                style="max-width: 100px !important"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                >${{ accountingAmountFormat(numberWithCommas(roundTo(value, 2))) }}</span
            >
        </span>
        <span v-if="inputFormat === 'percentage'">
            <v-text-field
                dense
                v-model.lazy="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                v-on:blur="fixDecimalFormat($event)"
                v-on:keypress="validateInputNumber($event)"
                class="cirrus-input-percentage"
                style="max-width: 80px !important"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
            <span class="form-input-text">%</span>
        </span>
        <span v-if="inputFormat === 'numberOnly'">
            <v-text-field
                dense
                v-model.lazy="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                v-on:blur="fixDecimalFormat($event)"
                v-on:keypress="validateInputNumber($event)"
                class="cirrus-input-dollar"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
        </span>
        <span v-if="inputFormat === 'wholeNumberOnly'">
            <v-text-field
                dense
                v-model.lazy="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                v-on:blur="fixIntFormat($event)"
                v-on:keypress="validateInputWholeNumber($event)"
                class="cirrus-input-dollar"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
        </span>
        <span v-if="inputFormat === 'emailClickable'">
            <v-text-field
                dense
                v-model.lazy="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                :class="custom_class"
                :placeholder="placeholder"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                ><a :href="'mailto:' + value">{{ value }}</a></span
            >
            <v-chip
                v-if="error_msg.length > 0 && errorData.id === id"
                v-for="(errorData, index) in error_msg"
                :key="index"
                outlined
                color="error"
            >
                <v-icon left>error</v-icon>{{ errorData.message }}
            </v-chip>
        </span>
        <span v-if="inputFormat === 'phoneClickable'">
            <v-text-field
                dense
                v-model.lazy="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                :class="custom_class"
                :placeholder="placeholder"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                ><a :href="'tel:' + value.replace(/[\s\/]/g, '')">{{ value }}</a></span
            >
            <v-chip
                v-if="error_msg.length > 0 && errorData.id === id"
                v-for="(errorData, index) in error_msg"
                :key="index"
                outlined
                color="error"
            >
                <v-icon left>error</v-icon>{{ errorData.message }}
            </v-chip>
        </span>
    </div>
</template>
<script>
import global_mixins from '../../plugins/mixins';

export default {
    props: {
        inputFormat: { type: String, default: '' },
        tabindex: { type: String, default: '0' },
        value: { default: '' },
        custom_class: { default: 'cirrus-input-form-textbox' },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        size: { type: String, default: '60' },
        maxlength: { type: String, default: '60' },
        id: { type: String, default: '60' },
        withKey: { type: Boolean, default: true },
        edit_form: { type: Boolean, default: false },
        disabled: { type: Boolean, default: false },
        tooltip: { type: String, default: '' },
        rules_length: { type: String, default: 60 },
        rules: [(v) => v.length <= this.rules_length || 'Max ' + this.rules_length + ' characters'],
    },
    data() {
        return {
            cirrusInput: null,
        };
    },
    mounted() {
        this.cirrusInput = this.value;
    },
    methods: {
        updateValue(value) {
            this.$emit('input', this.cirrusInput);
        },
        validateInputNumber: function (event) {
            // let charKeyCode = e.which || e.keyCode;
            // evt = (event) ? event.keyCode : window.event.keyCode;

            event = event ? event : window.event;
            let charCode = event.which ? event.which : event.keyCode;

            if (
                $.inArray(charCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
                // Allow: Ctrl/cmd+A
                (charCode == 65 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: Ctrl/cmd+C
                (charCode == 67 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: Ctrl/cmd+X
                (charCode == 88 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: + - ( ) * /
                charCode == 43 ||
                charCode == 45 ||
                charCode == 40 ||
                charCode == 41 ||
                charCode == 47 ||
                charCode == 42 ||
                charCode == 99 ||
                charCode == 118 ||
                // Allow: home, end, left, right
                (charCode >= 38 && charCode <= 39)
            ) {
                // let it happen, don't do anything
                if (charCode == 13) {
                    // //(compo);
                    // compo = eval(compo);
                    // this.budgetExpensesArr[compo].value = eval(this.budgetExpensesArr[compo].value);
                    // $(compo).val(eval(e.target.defaultValue));
                    event.target.value = this.roundTo(eval(event.target.value), 2);
                }
                return;
            }

            if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
                event.preventDefault();
            }
        },
        validateInputWholeNumber: function (event) {
            // let charKeyCode = e.which || e.keyCode;
            // evt = (event) ? event.keyCode : window.event.keyCode;

            event = event ? event : window.event;
            let charCode = event.which ? event.which : event.keyCode;
            if (
                $.inArray(charCode, [8, 9, 27, 13, 190]) !== -1 ||
                // Allow: Ctrl/cmd+A
                (charCode == 65 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: Ctrl/cmd+C
                (charCode == 67 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: Ctrl/cmd+X
                (charCode == 88 && (event.ctrlKey === true || event.metaKey === true))
            ) {
                // let it happen, don't do anything
                if (charCode == 13) {
                    // //(compo);
                    // compo = eval(compo);
                    // this.budgetExpensesArr[compo].value = eval(this.budgetExpensesArr[compo].value);
                    // $(compo).val(eval(e.target.defaultValue));
                    event.target.value = this.roundTo(eval(event.target.value), 2);
                }
                return;
            }

            if (charCode > 31 && (charCode < 48 || charCode > 57)) {
                event.preventDefault();
            }
        },
        fixDecimalFormat: function (event) {
            if (event.target.value) event.target.value = this.roundTo(eval(event.target.value), 2);
        },
        fixIntFormat: function (event) {
            if (event.target.value) event.target.value = this.roundTo(Math.floor(eval(event.target.value)), 0);
        },
        roundTo: function (n, digits) {
            if (digits === undefined) {
                digits = 0;
            }
            let multiplicator = Math.pow(10, digits);
            n = parseFloat((n * multiplicator).toFixed(11));
            let test = Math.round(n) / multiplicator;
            return test.toFixed(digits);
        },
    },
    watch: {
        cirrusInput: function () {
            this.$emit('input', this.value);
        },
        value(newVal, oldVal) {
            if (newVal != oldVal) {
                //  if (newVal && newVal != null) {
                this.cirrusInput = newVal;
                //  }
            }
        },
    },
    mixins: [global_mixins],
};
</script>
