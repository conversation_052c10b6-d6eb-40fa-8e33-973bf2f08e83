<template>
    <div>
        <div
            :id="id"
            class="position-relative"
        >
            <v-menu
                ref="menu"
                :close-on-content-click="false"
                v-model="menu"
                :nudge-right="40"
                transition="scale-transition"
                offset-y
                width="78px"
                :attach="'#' + id + '> div > div > div'"
            >
                <template v-slot:activator="{ on }">
                    <v-text-field
                        class="date-picker-input-field"
                        v-model="dateFormatted"
                        persistent-hint
                        append-icon="event"
                        @keypress="removeInvalidCharacters()"
                        @blur="date = parseDate(dateFormatted)"
                        v-bind:autocomplete="'off'"
                        @click:append="
                            menu = true;
                            on;
                            date = parseDate(dateFormatted);
                        "
                        style="width: 106px"
                    >
                    </v-text-field>
                </template>
                <v-date-picker
                    v-model="date"
                    no-title
                    scrollable
                    @input="menu = false"
                ></v-date-picker>
            </v-menu>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        value: { type: String, default: '' },
        id: { type: String, default: '60' },
        clear: { type: Boolean, default: false },
        reload: { type: Boolean, default: false },
        render: { type: Boolean, default: false },
    },
    data() {
        return {
            display: this.type,
            date: null,
            dateFormatted: this.formatInitialDate(),
            menu: false,
        };
    },
    methods: {
        updateValue(value) {
            this.$emit('updateValue', value);
        },
        formatDate(value) {
            if (!value) {
                return null;
            } else {
                const [year, month, day] = value.split('-');
                let newDate = `${day}/${month}/${year}`;
                return newDate;
            }
        },
        removeInvalidCharacters: function () {
            let event = event ? event : window.event;
            let character = event.which ? event.which : event.keyCode;

            if (character >= 45 && character <= 57) {
                return true;
            } else {
                event.preventDefault();
            }
        },
        parseDate(value) {
            if (!value) return null;

            let date = null;
            let complete = this.dayMonthYear(value);
            let dayOnly = this.dayOnlyDate(value);
            let dayMonthOnly = this.dayMonthDate(value);

            if (complete) {
                date = complete;
            }
            if (dayOnly) {
                date = dayOnly;
            }
            if (dayMonthOnly) {
                date = dayMonthOnly;
            }

            let convertedDate = this.prepareDate(date);

            if (convertedDate.toLowerCase() != 'invalid date') {
                date = convertedDate;
            }

            if (this.checkDate(date) === true && date) {
                const [day, month, year] = date.split('/');

                let value = `${year}-${month}-${day}`;
                this.updateDate(value);

                return value;
            } else {
                this.date = null;
                this.dateFormatted = null;
                return null;
            }
        },
        formatInitialDate: function () {
            if (this.value === '' || this.value === null) {
                this.$emit('input', null);
                return this.formatDate(null);
            } else {
                this.date = this.value;

                if (moment(this.value, 'DD/MM/YYYY', true).isValid()) {
                    this.dateFormatted = this.value;
                    this.updateValue(this.value);
                } else {
                    this.updateDate(this.value);
                }
                return this.dateFormatted;
            }
        },
        checkDate: function (value) {
            if (!value) {
                alert('Please enter dates in this format:\n\n dd/mm/yyyy.');
                return false;
            }

            if (!value || /^\s+$/.test(value)) {
                alert('Please enter dates in this format:\n\n dd/mm/yyyy.');
                return false;
            }
            if (!/^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
                alert('Please enter dates in this format:\n\n dd/mm/yyyy.');
                return false;
            }
            let strSeparator = '/';
            if (value) {
                let arrayDate = value.split(strSeparator);
                let arrayLookup = {
                    '01': 31,
                    '03': 31,
                    '04': 30,
                    '05': 31,
                    '06': 30,
                    '07': 31,
                    '08': 31,
                    '09': 30,
                    10: 31,
                    11: 30,
                    12: 31,
                };
                let intDay = arrayDate[0];
                if (arrayLookup[arrayDate[1]] != null) {
                    if (intDay <= arrayLookup[arrayDate[1]] && intDay != 0) {
                        return true;
                    } else {
                        alert('Please enter dates in this format:\n\n dd/mm/yyyy.');
                        return false;
                    }
                } else if (arrayDate[1] == '02') {
                    let intYear = parseInt(arrayDate[2]);
                    if (((intYear % 4 == 0 && intDay <= 29) || (intYear % 4 != 0 && intDay <= 28)) && intDay != 0) {
                        return true;
                    } else {
                        alert('Please enter dates in this format:\n\n dd/mm/yyyy.');
                        return false;
                    }
                }
            }

            alert('This is not a valid date.\n\nPlease re-enter in the format dd/mm/yyyy.');
            return false;
        },
        dayMonthYear: function (value) {
            let entered_date = value;
            let non_number_counter = 0;
            let first_location = 0;
            let second_location = 0;
            let day_value = 0;
            let month_value = 0;
            let year_value = 0;
            for (let x = 0; x < entered_date.length; x++) {
                if (
                    entered_date.charAt(x) != '0' &&
                    entered_date.charAt(x) != '1' &&
                    entered_date.charAt(x) != '2' &&
                    entered_date.charAt(x) != '3' &&
                    entered_date.charAt(x) != '4' &&
                    entered_date.charAt(x) != '5' &&
                    entered_date.charAt(x) != '6' &&
                    entered_date.charAt(x) != '7' &&
                    entered_date.charAt(x) != '8' &&
                    entered_date.charAt(x) != '9'
                ) {
                    non_number_counter++;
                    if (first_location == 0) {
                        first_location = x;
                    } else {
                        second_location = x;
                    }
                }
            }
            if (non_number_counter == 2 && entered_date.length > 4) {
                day_value = entered_date.substring(0, first_location);
                first_location++;
                month_value = entered_date.substring(first_location, second_location);
                second_location++;
                let last_location = entered_date.length;
                year_value = entered_date.substring(second_location, last_location);

                if (day_value.length == 1) {
                    day_value = '0' + day_value;
                }
                if (month_value.length == 1) {
                    month_value = '0' + month_value;
                }
                if (year_value.length == 1) {
                    year_value = '0' + year_value;
                }
                if (parseInt(year_value) > 90 && year_value.length == 2) {
                    year_value = '19' + year_value;
                }
                if (parseInt(year_value) <= 90 && year_value.length == 2) {
                    year_value = '20' + year_value;
                }

                if (day_value.length == 2 && month_value.length == 2 && year_value.length == 4) {
                    return day_value + '/' + month_value + '/' + year_value;
                }
            }
        },
        dayOnlyDate: function (value) {
            value = value.replace(' ', '/');
            value = value.replace('.', '/');

            let now = new Date();
            let this_month = now.getMonth() + 1;
            let this_year = now.getFullYear();

            //This is for start and end entires
            if (value.charAt(0) == 'S' || value.charAt(0) == 's') {
                value = '01';
            }
            if (value.charAt(0) == 'E' || value.charAt(0) == 'e') {
                if (
                    this_month == 1 ||
                    this_month == 3 ||
                    this_month == 5 ||
                    this_month == 7 ||
                    this_month == 8 ||
                    this_month == 10 ||
                    this_month == 12
                ) {
                    value = '31';
                } else if (this_month == 4 || this_month == 6 || this_month == 9 || this_month == 11) {
                    value = '30';
                } else {
                    value = '28';
                }
            }

            if (value.length == 1 || value.length == 2) {
                if (value * 1 == value) {
                    if (value.length == 1) {
                        value = '0' + value;
                    }
                    if (
                        ((this_month == 1 ||
                            this_month == 3 ||
                            this_month == 5 ||
                            this_month == 7 ||
                            this_month == 8 ||
                            this_month == 10 ||
                            this_month == 12) &&
                            value <= 31) ||
                        ((this_month == 4 || this_month == 6 || this_month == 9 || this_month == 11) && value <= 30) ||
                        (this_month == 2 && value <= 28)
                    ) {
                        if (this_month < 10) {
                            return (value = value + '/0' + this_month + '/' + this_year);
                        } else {
                            return (value = value + '/' + this_month + '/' + this_year);
                        }
                    } else {
                        return (value = '0' + value.charAt(0) + '/0' + value.charAt(1) + '/' + this_year);
                    }
                }
            }
        },
        dayMonthDate: function (value) {
            let now = new Date();
            let this_year = now.getFullYear();
            if (value.length >= 3 || value.length <= 5) {
                if (value.length == 3) {
                    //If the user only enter a 3 digit date
                    if (
                        value.charAt(0) * 1 == value.charAt(0) &&
                        value.charAt(1) * 1 != value.charAt(1) &&
                        value.charAt(2) * 1 == value.charAt(2)
                    ) {
                        //Cant be just a number
                        return '0' + value.charAt(0) + '/0' + value.charAt(2) + '/' + this_year;
                    }
                    if (value * 1 == value) {
                        let day = value.substring(0, 2);
                        let month = value.substring(2, 3);
                        if (
                            (month == 1 ||
                                month == 3 ||
                                month == 5 ||
                                month == 7 ||
                                month == 8 ||
                                month == 10 ||
                                month == 12) &&
                            day <= 31
                        ) {
                            return day + '/0' + month + '/' + this_year;
                        } else if ((month == 4 || month == 6 || month == 9 || month == 11) && day <= 30) {
                            return day + '/0' + month + '/' + this_year;
                        } else if (month == 2 && day <= 28) {
                            return day + '/0' + month + '/' + this_year;
                        } //it is not a valid ddm format, must be dmm format
                        else {
                            let day = value.substring(0, 1);
                            let month = value.substring(1, 3);
                            if (
                                (month == 1 ||
                                    month == 3 ||
                                    month == 5 ||
                                    month == 7 ||
                                    month == 8 ||
                                    month == 10 ||
                                    month == 12) &&
                                day <= 31
                            ) {
                                return '0' + day + '/' + month + '/' + this_year;
                            } else if ((month == 4 || month == 6 || month == 9 || month == 11) && day <= 30) {
                                return '0' + day + '/' + month + '/' + this_year;
                            } else if (month == 2 && day <= 28) {
                                return '0' + day + '/' + month + '/' + this_year;
                            }
                        }
                    }
                } else if (value.length == 4) {
                    //If the user only enter a 4 digit date
                    if (value * 1 == value) {
                        //ddmm format
                        if (value.charAt(1) * 1 != value.charAt(1)) {
                            return (
                                '0' + value.charAt(0) + '/' + value.charAt(2) + '' + value.charAt(3) + '/' + this_year
                            );
                        } else if (value.charAt(2) * 1 != value.charAt(2)) {
                            return value.charAt(0) + '' + value.charAt(1) + '/0' + value.charAt(3) + '/' + this_year;
                        } else {
                            return (
                                value.charAt(0) +
                                '' +
                                value.charAt(1) +
                                '/' +
                                value.charAt(2) +
                                '' +
                                value.charAt(3) +
                                '/' +
                                this_year
                            );
                        }
                    }
                    //dxmm format
                    else if (
                        value.charAt(0) * 1 == value.charAt(0) &&
                        value.charAt(1) * 1 != value.charAt(1) &&
                        value.charAt(2) * 1 == value.charAt(2) &&
                        value.charAt(3) * 1 == value.charAt(3)
                    ) {
                        return '0' + value.charAt(0) + '/' + value.charAt(2) + '' + value.charAt(3) + '/' + this_year;
                    }
                    //ddxm format
                    else if (
                        value.charAt(0) * 1 == value.charAt(0) &&
                        value.charAt(1) * 1 == value.charAt(1) &&
                        value.charAt(2) * 1 != value.charAt(2) &&
                        value.charAt(3) * 1 == value.charAt(3)
                    ) {
                        return value.charAt(0) + '' + value.charAt(1) + '/0' + value.charAt(3) + '/' + this_year;
                    }
                } else if (value.length == 5) {
                    //If the user only enter a 5 digit date
                    if (
                        value.charAt(0) * 1 == value.charAt(0) &&
                        value.charAt(1) * 1 == value.charAt(1) &&
                        value.charAt(2) * 1 != value.charAt(2) &&
                        value.charAt(3) * 1 == value.charAt(3) &&
                        value.charAt(4) * 1 == value.charAt(4)
                    ) {
                        //ddxmm format
                        return (
                            value.charAt(0) +
                            '' +
                            value.charAt(1) +
                            '/' +
                            value.charAt(3) +
                            '' +
                            value.charAt(4) +
                            '/' +
                            this_year
                        );
                    }
                }
            }
        },
        updateDate: function (value) {
            if (value) {
                this.dateFormatted = this.formatDate(this.date);
                this.updateValue(this.dateFormatted);
            } else {
                this.dateFormatted = null;
            }
        },
    },
    watch: {
        dateFormatted() {
            this.$emit('input', this.dateFormatted);
        },
        date(val) {
            this.updateDate(val);
        },
        clear() {
            if (this.clear === true) {
                this.dateFormatted = null;
                this.date = null;
            }
        },
        reload() {
            if (this.reload === true) {
                this.dateFormatted = this.value;
                this.updateValue(this.value);
            }
        },
        render() {
            this.dateFormatted = this.value;
            this.updateValue(this.value);
            // this.$forceUpdate();
        },
    },
};
</script>
