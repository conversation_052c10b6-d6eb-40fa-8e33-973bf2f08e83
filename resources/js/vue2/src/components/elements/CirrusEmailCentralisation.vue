<template>
    <span>
        <v-menu
            v-model="menu"
            :close-on-content-click="false"
            :nudge-width="200"
            offset-x
        >
            <template v-slot:activator="{ on, attrs }">
                <v-icon
                    color="blue"
                    size="20"
                    v-bind="attrs"
                    v-on="on"
                    v-if="value.length > 0"
                    >contact_mail</v-icon
                >
            </template>
            <v-card>
                <v-card-title>
                    <span>Email Address Book</span>
                </v-card-title>
                <v-card-text>
                    <div
                        v-for="(email_centralise_list_data, index) in cirrusInput"
                        :key="index"
                    >
                        <h6>{{ email_centralise_list_data.main_title }}</h6>
                        <div
                            v-for="(mainDetailsData, indexDetail) in email_centralise_list_data.main_details"
                            :key="indexDetail"
                        >
                            <sui-checkbox
                                v-model="mainDetailsData.field_flag"
                                :label="mainDetailsData.field_value"
                            />
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>

                    <v-btn
                        text
                        @click="menu = false"
                    >
                        Cancel
                    </v-btn>
                    <v-btn
                        color="primary"
                        text
                        @click="saveEmailCentralisation()"
                        :loading="saveBtnLoading"
                        :disabled="saveBtnLoading"
                        :dark="isDarkMode()"
                    >
                        Save
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-menu>
    </span>
</template>
<script>
import { isDarkMode } from '../../utils/sharedFunctions';

export default {
    props: {
        value: { type: Array, default: [] },
        placeholder: { type: String, default: '' },
        contact_table_name: { type: String, default: '' },
        contact_table_id: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        size: { type: String, default: '60' },
        id: { type: String, default: '60' },
        withKey: { type: Boolean, default: true },
        edit_form: { type: Boolean, default: false },
    },
    data() {
        return {
            cirrusInput: structuredClone(this.value),
            open_modal: false,
            menu: false,
            snapshot: structuredClone(this.value),
            saveBtnLoading: false,
        };
    },
    methods: {
        isDarkMode,
        updateValue(value) {
            this.$emit('input', value);
        },
        saveEmailCentralisation: function () {
            var form_data = new FormData();
            form_data.append('contact_table_name', this.contact_table_name);
            form_data.append('contact_table_id', this.contact_table_id);
            form_data.append('no_load', true);
            form_data.append('email_centralisation_values', JSON.stringify(this.cirrusInput));
            this.saveBtnLoading = true;

            this.$api
                .post('email/process/save-email-centralisation', form_data)
                .then((response) => {
                    this.$emit('input', this.cirrusInput);
                    this.value = structuredClone(this.cirrusInput); // Update the value after saving
                    this.snapshot = structuredClone(this.value); // Update the snapshot after saving
                    this.saveBtnLoading = false;
                    this.menu = false;

                    const processStatus = response.data.status;
                    if (processStatus === 'success') {
                        this.$noty.success('Email Address Book data has been saved.');
                    }

                    if (processStatus === 'warning') {
                        this.$noty.warning(response.data.errorMessage);
                    }

                    if (processStatus === 'error') {
                        this.$noty.error('There was a problem saving the data: ' + response.data.errorMessage);
                    }
                })
                .catch((errorResponse) => {
                    this.$noty.error(errorResponse.message);
                });
        },
        cancel() {
            this.menu = false;
            this.value = structuredClone(this.snapshot); //reset to original values
        },
    },
    watch: {
        menu: function () {
            if (!this.menu) {
                this.cirrusInput = structuredClone(this.snapshot); //reset to original values
            }
        },
        value: function () {
            this.cirrusInput = structuredClone(this.value);
            this.snapshot = structuredClone(this.value);
        },
    },
    mounted() {},
};
</script>
