<template>
    <div style="display: inline-block">
        <div
            :id="id"
            v-show="edit_form"
        >
            <v-menu
                class="date-picker-component"
                v-show="edit_form"
                ref="menu1"
                :close-on-content-click="false"
                v-model="menu1"
                :nudge-right="40"
                transition="scale-transition"
                offset-y
                width="78px"
                :attach="'#' + id + '> div > div > div'"
            >
                <template v-slot:activator="{ on }">
                    <v-text-field
                        class="date-picker-input-field"
                        v-on="on"
                        v-model="dateFormatted"
                        persistent-hint
                        append-icon="event"
                        @blur="date = parseDate(dateFormatted)"
                        style="width: 106px"
                    ></v-text-field>
                </template>
                <v-date-picker
                    v-model="date"
                    no-title
                    @input="menu1 = false"
                ></v-date-picker>
            </v-menu>
        </div>
        <!--<textarea :id="id" :value="value" :rows="rows" :cols="cols" v-if="edit_form" v-on:input="updateValue($event.target.value)"></textarea>-->
        <span
            class="form-input-text"
            v-if="!edit_form"
            >{{ computedDateFormatted }}</span
        >
        <v-chip
            v-if="error_msg.length > 0 && errorData.id === id"
            v-for="(errorData, index) in error_msg"
            :key="index"
            outlined
            color="error"
        >
            <v-icon left>error</v-icon>{{ errorData.message }}
        </v-chip>
    </div>
</template>
<script>
export default {
    props: {
        value: { type: String, default: '' },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        size: { type: String, default: '60' },
        id: { type: String, default: '60' },
        edit_form: { type: Boolean, default: false },
    },
    data() {
        return {
            cirrusInput: this.value,
            date: null,
            dateFormatted: this.formatInitialDate(),
            menu1: false,
            menu2: false,
        };
    },
    computed: {
        computedDateFormatted() {
            return this.value;
        },
    },
    methods: {
        updateValue(value) {
            this.$emit('input', value);
        },
        formatDate(date) {
            if (!date) return null;

            const [year, month, day] = date.split('-');
            return `${day}/${month}/${year}`;
        },
        parseDate(date) {
            if (!date) return null;

            const [day, month, year] = date.split('/');

            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        },
        formatInitialDate: function () {
            if (this.value === '' || this.value === null) {
                this.$emit('input', this.formatDate(new Date().toISOString().substr(0, 10)));
                return this.formatDate(new Date().toISOString().substr(0, 10));
            } else {
                let dateSplit = this.value.split('/');
                // let renderDate = new Date(dateSplit[2],dateSplit[1],dateSplit[0]);
                // console.log(dateSplit,renderDate);
                this.date = dateSplit[2] + '-' + dateSplit[1] + '-' + dateSplit[0];
                return this.value;
            }
        },
    },
    watch: {
        cirrusInput: function () {
            this.$emit('input', this.value);
        },
        date(val) {
            this.dateFormatted = this.formatDate(this.date);
            // this.dateFormatted = this.formatInitialDate();
            this.updateValue(this.dateFormatted);
        },
        value() {
            if (this.value !== '' && this.value !== null) {
                let dateSplit = this.value.split('/');
                // let renderDate = new Date(dateSplit[2],dateSplit[1],dateSplit[0]);
                // console.log(dateSplit,renderDate);
                this.date = dateSplit[2] + '-' + dateSplit[1] + '-' + dateSplit[0];
                return this.value;
                // return dateSplit[2]+'-'+dateSplit[1]+'-'+dateSplit[0];
            } else {
                return this.formatDate(new Date().toISOString().substr(0, 10));
            }
        },
    },
};
</script>
