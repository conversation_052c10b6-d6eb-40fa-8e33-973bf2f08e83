<template>
    <div>
        <div
            class="uix-multiselect ui-widget"
            style="width: 800px; height: 246px"
        >
            <div
                class="multiselect-selected-list"
                style="width: 400px; left: 400px; height: 247px"
            >
                <div class="ui-widget-header ui-corner-tr">
                    <button
                        v-on:click="deSelectAllOption()"
                        type="button"
                        class="uix-control-right ui-button ui-widget ui-state-default ui-corner-all ui-button-icon-only"
                        data-localekey="deselectAll"
                        title="Deselect All"
                        role="button"
                        aria-disabled="false"
                        style="display: inline-block"
                    >
                        <span class="ui-button-icon-primary ui-icon ui-icon-arrowthickstop-1-w"></span
                        ><span class="ui-button-text"></span>
                    </button>
                    <div class="header-text">{{ finalValue.length }} options selected</div>
                </div>
                <div
                    class="uix-list-container ui-widget-content ui-droppable"
                    id="multiselect2_selListContent"
                    style="height: 204px"
                >
                    <div>
                        <div
                            class="multiselect-element-wrapper"
                            v-for="(group, name) in option2"
                            :label="name"
                        >
                            <div style="display: block">
                                <div
                                    class="ui-widget-header ui-priority-secondary group-element group-element-collapsable"
                                    v-if="displayCount"
                                >
                                    <span class="label">
                                        {{ group.fieldGroupNames }} ({{ group.fieldGroupValues.length }})</span
                                    >
                                </div>
                            </div>
                            <div style="display: block">
                                <div
                                    v-on:click="deselectDiv(option.fieldKey)"
                                    class="ui-state-default option-element"
                                    unselectable="on"
                                    v-for="option in group.fieldGroupValues"
                                >
                                    <div>{{ displayKey ? option.fieldKey + ' - ' : '' }}{{ option.fieldValue }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="multiselect-available-list"
                style="width: 400px; left: 0px; height: 247px"
            >
                <div
                    id="mbAddAccountCode_multiSelectDiv"
                    class="ui-widget-header ui-corner-tl"
                >
                    <button
                        v-on:click="selectAllOption()"
                        type="button"
                        class="uix-control-right ui-button ui-widget ui-state-default ui-corner-all ui-button-icon-only"
                        data-localekey="selectAll"
                        title="Select All"
                        role="button"
                        aria-disabled="false"
                        style="display: inline-block"
                    >
                        <span class="ui-button-icon-primary ui-icon ui-icon-arrowthickstop-1-e"></span
                        ><span class="ui-button-text"></span>
                    </button>
                    <button
                        v-on:click="showSearchText()"
                        type="button"
                        class="uix-control-right ui-button ui-widget ui-state-default ui-corner-all ui-button-icon-only"
                        data-localekey="search"
                        title="Search Options"
                        role="button"
                        aria-disabled="false"
                    >
                        <span class="ui-button-icon-primary ui-icon ui-icon-search"></span
                        ><span class="ui-button-text"></span>
                    </button>
                    <input
                        v-on:keyup="searchArray()"
                        ref="refSearchInput"
                        placeholder="Search..."
                        v-model="searchInput"
                        type="text"
                        class="mutiSelectInput uix-search ui-widget-content ui-corner-left"
                        style="width: 363px; height: 24px; font-size: 100%"
                        v-show="showSearch"
                    />
                    <div class="header-text">{{ countOptions() }} options available</div>
                </div>
                <div
                    class="uix-list-container ui-widget-content ui-droppable"
                    id="multiselect2_avListContent2"
                    style="height: 204px"
                >
                    <div>
                        <div class="multiselect-element-wrapper">
                            <div></div>
                        </div>
                        <div
                            class="multiselect-element-wrapper"
                            v-for="(group, name) in option1"
                            :label="name"
                        >
                            <div style="display: block">
                                <div
                                    class="ui-widget-header ui-priority-secondary group-element group-element-collapsable"
                                    v-if="displayCount"
                                >
                                    <span class="label"
                                        >{{ group.fieldGroupNames }} ({{ group.fieldGroupValues.length }})</span
                                    >
                                </div>
                            </div>
                            <div style="display: block">
                                <div
                                    v-on:click="selectDiv(option.fieldKey)"
                                    class="ui-state-default option-element"
                                    unselectable="on"
                                    v-for="option in group.fieldGroupValues"
                                >
                                    <div>{{ displayKey ? option.fieldKey + ' - ' : '' }}{{ option.fieldValue }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        value: {
            type: Array,
            default: function () {
                return [];
            },
        },
        options: { type: Array },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        debug: { type: Boolean, default: false },
        width: { type: String, default: '400px' },
        withKey: { type: Boolean, default: true },
        displayCount: { type: Boolean, default: true },
    },
    data() {
        return {
            option1: [],
            option2: [],
            finalValue: this.value,
            showSearch: false,
            searchInput: '',
            displayKey: this.withKey,
        };
    },
    mounted() {
        this.option1 = JSON.parse(JSON.stringify(this.options));
        for (let a = 0; a <= this.value.length - 1; a++) {
            let key = this.value[a].fieldKey;
            let valueHolder = [];
            for (var x = 0; x <= this.option1.length - 1; x++) {
                let groupName = this.option1[x].fieldGroupNames;
                let groupValues = this.option1[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    let fieldKey = groupValues[y].fieldKey;
                    let fieldValue = groupValues[y].fieldValue;
                    if (fieldKey == key) {
                        let opt2Value = {
                            fieldGroupNames: groupName,
                            fieldGroupValues: [
                                {
                                    fieldKey: fieldKey,
                                    fieldValue: fieldValue,
                                    field_key: fieldKey,
                                    field_value: fieldValue,
                                    value: fieldKey,
                                    label: fieldValue,
                                },
                            ],
                        };
                        valueHolder = {
                            fieldKey: fieldKey,
                            fieldValue: fieldValue,
                            field_key: fieldKey,
                            field_value: fieldValue,
                            value: fieldKey,
                            label: fieldValue,
                        };
                        let flag = false;
                        for (let x2 = 0; x2 <= this.option2.length - 1; x2++) {
                            let groupName2 = this.option2[x2].fieldGroupNames;
                            let groupValues2 = this.option2[x2].fieldGroupValues;
                            if (groupName == groupName2) {
                                flag = true;
                            }
                            if (flag) {
                                this.option2[x2].fieldGroupValues.push(valueHolder);
                                x2 = this.option2.length;
                            }
                        }
                        if (!flag) {
                            this.option2.push(opt2Value);
                        }
                        this.option1[x].fieldGroupValues.splice(y, 1);
                        if (this.option1[x].fieldGroupValues.length <= 0) {
                            this.option1.splice(x, 1);
                        }
                        x = this.option1.length;
                        y = groupValues.length;
                    }
                }
            }
        }
    },
    methods: {
        selectAllOption: function () {
            let valueHolder = [];
            for (var x = 0; x <= this.option1.length - 1; x++) {
                let groupName = this.option1[x].fieldGroupNames;
                let groupValues = this.option1[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    let fieldKey = groupValues[y].fieldKey;
                    let fieldValue = groupValues[y].fieldValue;
                    let opt2Value = {
                        fieldGroupNames: groupName,
                        fieldGroupValues: [
                            {
                                fieldKey: fieldKey,
                                fieldValue: fieldValue,
                                field_key: fieldKey,
                                field_value: fieldValue,
                                value: fieldKey,
                                label: fieldValue,
                            },
                        ],
                    };
                    valueHolder = {
                        fieldKey: fieldKey,
                        fieldValue: fieldValue,
                        field_key: fieldKey,
                        field_value: fieldValue,
                        value: fieldKey,
                        label: fieldValue,
                    };
                    this.finalValue.push(valueHolder);
                    let flag = false;
                    for (let x2 = 0; x2 <= this.option2.length - 1; x2++) {
                        let groupName2 = this.option2[x2].fieldGroupNames;
                        let groupValues2 = this.option2[x2].fieldGroupValues;
                        if (groupName == groupName2) {
                            flag = true;
                        }
                        if (flag) {
                            this.option2[x2].fieldGroupValues.push(valueHolder);
                            x2 = this.option2.length;
                        }
                    }
                    if (!flag) {
                        this.option2.push(opt2Value);
                    }
                }
            }
            this.option1 = [];
            this.option2[0].fieldGroupValues.sort(function (a, b) {
                if (a.fieldKey == b.fieldKey) return parseFloat(b.after) - parseFloat(a.after);
                return a.fieldKey > b.fieldKey ? 1 : -1;
            });
            this.updateValue(this.finalValue);
        },
        deSelectAllOption: function () {
            let valueHolder = [];
            for (var x = 0; x <= this.option2.length - 1; x++) {
                let groupName = this.option2[x].fieldGroupNames;
                let groupValues = this.option2[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    let fieldKey = groupValues[y].fieldKey;
                    let fieldValue = groupValues[y].fieldValue;
                    let opt2Value = {
                        fieldGroupNames: groupName,
                        fieldGroupValues: [
                            {
                                fieldKey: fieldKey,
                                fieldValue: fieldValue,
                                field_key: fieldKey,
                                field_value: fieldValue,
                                value: fieldKey,
                                label: fieldValue,
                            },
                        ],
                    };
                    valueHolder = {
                        fieldKey: fieldKey,
                        fieldValue: fieldValue,
                        field_key: fieldKey,
                        field_value: fieldValue,
                        value: fieldKey,
                        label: fieldValue,
                    };

                    for (let a = 0; a <= this.finalValue.length - 1; a++) {
                        let fieldKeyFinal = this.finalValue[a].fieldKey;
                        if (fieldKey == fieldKeyFinal) {
                            this.finalValue.splice(a, 1);
                        }
                    }
                    let flag = false;
                    for (let x2 = 0; x2 <= this.option1.length - 1; x2++) {
                        let groupName2 = this.option1[x2].fieldGroupNames;
                        let groupValues2 = this.option1[x2].fieldGroupValues;
                        if (groupName == groupName2) {
                            flag = true;
                        }
                        if (flag) {
                            this.option1[x2].fieldGroupValues.push(valueHolder);
                            x2 = this.option1.length;
                        }
                    }
                    if (!flag) {
                        this.option1.push(opt2Value);
                    }
                }
            }
            this.option2 = [];
            this.option1[0].fieldGroupValues.sort(function (a, b) {
                if (a.fieldKey == b.fieldKey) return parseFloat(b.after) - parseFloat(a.after);
                return a.fieldKey > b.fieldKey ? 1 : -1;
            });
            this.updateValue(this.finalValue);
        },
        showSearchText: function () {
            if (this.showSearch) {
                this.showSearch = false;
            } else {
                this.showSearch = true;
            }
            this.$nextTick(() => {
                this.$refs.refSearchInput.focus();
            });
        },
        searchArray: function () {
            let tempArray = [];

            for (let x = 0; x <= this.options.length - 1; x++) {
                let fieldGroupNames = this.options[x].fieldGroupNames;
                let fieldGroupValues = this.options[x].fieldGroupValues;

                fieldGroupValues = fieldGroupValues.filter((p) => {
                    if (this.option2.length > 0) {
                        let selectedOptions = [];
                        for (let x = 0; x <= this.option2.length - 1; x++) {
                            let fieldGroupNames2 = this.option2[x].fieldGroupNames;
                            let fieldGroupValues2 = this.option2[x].fieldGroupValues;
                            fieldGroupValues2 = fieldGroupValues2.filter((pp) => {
                                selectedOptions.push(pp.fieldKey);
                            });
                        }

                        if (!selectedOptions.includes(p.fieldKey)) {
                            return (
                                p.fieldKey.toLowerCase().includes(this.searchInput.toLowerCase()) ||
                                p.fieldValue.toLowerCase().includes(this.searchInput.toLowerCase())
                            );
                        }
                    } else {
                        return (
                            p.fieldKey.toLowerCase().includes(this.searchInput.toLowerCase()) ||
                            p.fieldValue.toLowerCase().includes(this.searchInput.toLowerCase())
                        );
                    }
                    // return foundAccount !== -1;
                });

                tempArray.push({
                    fieldGroupNames: fieldGroupNames,
                    fieldGroupValues: fieldGroupValues,
                });
            }
            this.option1 = tempArray;
        },
        init() {
            if (this.debug) console.log('chosen:init');
            this.$nextTick(function () {
                this.$el.value = this.value;
            });
            return this;
        },
        destroy() {
            return this;
        },
        updateValue(value) {
            this.$emit('input', value);
        },
        selectDiv: function (key) {
            let valueHolder = [];
            for (var x = 0; x <= this.option1.length - 1; x++) {
                let groupName = this.option1[x].fieldGroupNames;
                let groupValues = this.option1[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    let fieldKey = groupValues[y].fieldKey;
                    let fieldValue = groupValues[y].fieldValue;
                    if (fieldKey == key) {
                        let opt2Value = {
                            fieldGroupNames: groupName,
                            fieldGroupValues: [
                                {
                                    fieldKey: fieldKey,
                                    fieldValue: fieldValue,
                                    field_key: fieldKey,
                                    field_value: fieldValue,
                                    value: fieldKey,
                                    label: fieldValue,
                                },
                            ],
                        };
                        valueHolder = {
                            fieldKey: fieldKey,
                            fieldValue: fieldValue,
                            field_key: fieldKey,
                            field_value: fieldValue,
                            value: fieldKey,
                            label: fieldValue,
                        };
                        let flag = false;
                        for (let x2 = 0; x2 <= this.option2.length - 1; x2++) {
                            let groupName2 = this.option2[x2].fieldGroupNames;
                            let groupValues2 = this.option2[x2].fieldGroupValues;
                            if (groupName == groupName2) {
                                flag = true;
                            }
                            if (flag) {
                                this.option2[x2].fieldGroupValues.push(valueHolder);
                                x2 = this.option2.length;
                            }
                        }
                        if (!flag) {
                            this.option2.push(opt2Value);
                        }
                        this.option1[x].fieldGroupValues.splice(y, 1);
                        if (this.option1[x].fieldGroupValues.length <= 0) {
                            this.option1.splice(x, 1);
                        }
                        x = this.option1.length;
                        y = groupValues.length;
                    }
                }
            }

            this.option2[0].fieldGroupValues.sort(function (a, b) {
                if (a.fieldKey == b.fieldKey) return parseFloat(b.after) - parseFloat(a.after);
                return a.fieldKey > b.fieldKey ? 1 : -1;
            });
            this.finalValue.push(valueHolder);
            this.updateValue(this.finalValue);
        },
        deselectDiv: function (key) {
            let valueHolder = [];
            for (var x = 0; x <= this.option2.length - 1; x++) {
                let groupName = this.option2[x].fieldGroupNames;
                let groupValues = this.option2[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    let fieldKey = groupValues[y].fieldKey;
                    let fieldValue = groupValues[y].fieldValue;
                    if (fieldKey == key) {
                        let opt2Value = {
                            fieldGroupNames: groupName,
                            fieldGroupValues: [
                                {
                                    fieldKey: fieldKey,
                                    fieldValue: fieldValue,
                                    field_key: fieldKey,
                                    field_value: fieldValue,
                                    value: fieldKey,
                                    label: fieldValue,
                                },
                            ],
                        };
                        valueHolder = {
                            fieldKey: fieldKey,
                            fieldValue: fieldValue,
                            field_key: fieldKey,
                            field_value: fieldValue,
                            value: fieldKey,
                            label: fieldValue,
                        };

                        for (let a = 0; a <= this.finalValue.length - 1; a++) {
                            let fieldKeyFinal = this.finalValue[a].fieldKey;
                            if (fieldKey == fieldKeyFinal) {
                                this.finalValue.splice(a, 1);
                            }
                        }
                        let flag = false;
                        for (let x2 = 0; x2 <= this.option1.length - 1; x2++) {
                            let groupName2 = this.option1[x2].fieldGroupNames;
                            let groupValues2 = this.option1[x2].fieldGroupValues;
                            if (groupName == groupName2) {
                                flag = true;
                            }
                            if (flag) {
                                this.option1[x2].fieldGroupValues.push(valueHolder);
                                x2 = this.option1.length;
                            }
                        }
                        if (!flag) {
                            this.option1.push(opt2Value);
                        }
                        this.option2[x].fieldGroupValues.splice(y, 1);
                        if (this.option2[x].fieldGroupValues.length <= 0) {
                            this.option2.splice(x, 1);
                        }
                        x = this.option2.length;
                        y = groupValues.length;
                    }
                }
            }
            this.option1[0].fieldGroupValues.sort(function (a, b) {
                if (a.fieldKey == b.fieldKey) return parseFloat(b.after) - parseFloat(a.after);
                return a.fieldKey > b.fieldKey ? 1 : -1;
            });

            this.updateValue(this.finalValue);
        },
        countOptions: function () {
            let ctr = 0;
            for (let x = 0; x <= this.option1.length - 1; x++) {
                let groupValues = this.option1[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    ctr++;
                }
            }

            return ctr;
        },
    },
    watch: {
        value() {
            this.$nextTick(function () {});
        },
        options() {
            this.$nextTick(function () {
                this.destroy().init();
            });
        },
    },
};
</script>
