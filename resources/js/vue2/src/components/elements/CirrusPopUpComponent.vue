<template>
    <div>
        <v-dialog
            hide-overlay
            v-model="dialog_confirmation"
            max-width="400"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    <v-icon
                        v-show="icon_show"
                        :color="icon_color"
                        >{{ icon }}</v-icon
                    >
                    {{ title }}
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="
                            dialog_confirmation = false;
                            $emit('close');
                        "
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div
                        style="margin: 10px"
                        v-html="message"
                    ></div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        v-for="(btn, btn_index) in buttons_left"
                        :key="btn_index"
                        :color="'color' in btn ? btn.color : ''"
                        depressed
                        tile
                        small
                        @click="(response) => $emit('close', btn.value)"
                        >{{ btn.label }}</v-btn
                    >
                    <v-spacer />
                    <v-btn
                        v-for="(btn, btn_index) in buttons_right"
                        :key="btn_index"
                        :color="'color' in btn ? btn.color : ''"
                        depressed
                        tile
                        small
                        @click="(response) => $emit('close', btn.value)"
                        >{{ btn.label }}</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
export default {
    props: {
        title: { default: '' },
        message: { default: '' },
        icon_color: { default: 'warning' },
        icon: { default: 'warning' },
        icon_show: { default: false },
        buttons_left: { default: [] },
        buttons_right: { default: [{ label: 'close', value: 'close' }] },
    },
    data() {
        return {
            dialog_confirmation: true,
        };
    },
    mounted() {},
    methods: {},
    watch: {},
};
</script>
