<template>
    <div :id="elementID">
        <multiselect
            :class="customClass"
            :placeholder="placeholder"
            :deselectLabel="deselectLabel"
            :selectLabel="selectLabel"
            v-model="select_val"
            track-by="value"
            label="label"
            :options="options"
            @open="openList()"
            @input="changed()"
            :show-labels="hasEmpty"
            :allow-empty="hasEmpty"
            group-values="fieldGroupValues"
            group-label="fieldGroupNames"
            :selectedValue="selectedValue"
            :options-limit="optionsLimit"
            :clear-on-select="true"
        />
    </div>
</template>
<script>
export default {
    name: 'Cirrus8SingleSelect',
    props: {
        value: { default: null },
        options: { default: [] },
        optionsLimit: { default: 10000 },
        hasEmpty: { default: true },
        placeholder: { default: 'Select an Item' },
        deselectLabel: { default: 'Select to remove' },
        selectLabel: { default: '' },
        selectedValue: { default: [] },
        return: { default: 'value' },
        elementID: { default: 'selectgroup' + (Math.random() + 1).toString(36).substring(7) },
        displayDelay: { default: false },
        selectedScrollPos: { default: 0 },
        customClass: { default: '' },
    },
    data() {
        return {
            select_val: null,
        };
    },
    watch: {
        value(newVal, oldVal) {
            if (newVal != oldVal) {
                if (newVal && newVal != null) {
                    let find_val = this.options.filter((row) => {
                        return row.value == newVal;
                    });
                    if (find_val && find_val.length > 0) this.select_val = find_val[0];
                }
            }
        },
    },
    methods: {
        openList() {
            if (this.displayDelay) {
                let container = document.querySelector('#' + this.elementID + ' .multiselect__content-wrapper');
                let list = document.querySelector('#' + this.elementID + ' .multiselect__content');

                let preloader = document.createElement('div');
                preloader.setAttribute('id', 'Div1');
                preloader.style.paddingTop = '6px';
                preloader.style.paddingBottom = '6px';
                preloader.style.paddingLeft = '6px';
                preloader.style.paddingRight = '6px';
                preloader.style.position = 'absolute';
                preloader.style.zIndex = '99';
                preloader.textContent = 'Loading...';
                preloader.style.top = this.selectedScrollPos + 'px';

                container.prepend(preloader);

                list.style.visibility = 'hidden';
                setTimeout(() => document.querySelector('#' + this.elementID + ' #Div1').remove(), 50);
                setTimeout(() => (list.style.visibility = 'visible'), 50);
            }
        },
        changed() {
            if (this.return == 'all') {
                this.$emit('input', this.select_val);
            } else {
                if (this.select_val && this.select_val[this.return]) this.$emit('input', this.select_val[this.return]);
                else this.$emit('input', null);

                setTimeout(() => this.scroll_to_selected(), 30);
            }
        },
        forceChange(forceValue, options) {
            if (forceValue) {
                let found = null;
                for (var i = 0; i < options.length; i++) {
                    let opt = options[i];
                    if (opt.fieldGroupValues) {
                        for (var j = 0; j < opt.fieldGroupValues.length; j++) {
                            let row = opt.fieldGroupValues[j];
                            if (row.value == forceValue) {
                                found = row;
                                break;
                            }
                        }
                    }
                    if (found) break;
                }
                if (found) {
                    this.select_val = found;
                    this.changed();
                }
            }
        },
        get_selected() {
            if (this.selectedValue && this.selectedValue != null) {
                this.select_val = this.selectedValue;
            }
        },
        scroll_to_selected() {
            var selected = document.querySelector('#' + this.elementID + ' .multiselect__option--selected');

            if (selected) {
                var container = selected.closest('div.multiselect__content-wrapper');
                this.selectedScrollPos = selected.offsetTop;
                container.scrollTop = selected.offsetTop;
            }
        },
    },
    mounted() {
        this.get_selected();
        if (this.value && this.value != null) {
            let find_val = this.options.filter((row) => {
                return row.value == this.value;
            });
            if (find_val && find_val.length > 0) this.select_val = find_val[0];
        }
    },
};
</script>
