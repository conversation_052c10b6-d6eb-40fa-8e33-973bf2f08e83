<template>
    <div class="text-center">
        <v-bottom-sheet
            v-model="shortcut_sheet"
            inset
        >
            <template v-slot:activator="{ on }">
                <v-btn
                    color="secondary"
                    v-on="on"
                    fab
                    dark
                    small
                    bottom
                    left
                    fixed
                >
                    <v-icon>link</v-icon>
                </v-btn>
            </template>
            <v-list>
                <v-subheader>Shortcuts</v-subheader>
                <v-list-item
                    v-for="(shortcuts_data, shortcuts_index) in shortcuts"
                    :key="shortcuts_index"
                    @click="goToHref(shortcuts_data.shortcut_code)"
                >
                    <v-list-item-avatar>
                        <v-avatar
                            size="32px"
                            tile
                        >
                            <v-icon>{{ shortcuts_data.icon }}</v-icon>
                        </v-avatar>
                    </v-list-item-avatar>
                    <v-list-item-title>{{ shortcuts_data.title }}</v-list-item-title>
                </v-list-item>
            </v-list>
        </v-bottom-sheet>
    </div>
</template>
<script>
export default {
    props: {
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
    },
    data() {
        return {
            shortcuts: [
                { icon: 'business', title: 'Go to property', shortcut_code: 'property' },
                { icon: 'account_balance', title: 'Go to company', shortcut_code: 'company' },
                { icon: 'history', title: 'Lease Activity Logs', shortcut_code: 'lease_logs' },
                { icon: 'print', title: 'Print', shortcut_code: 'print' },
            ],
            shortcut_sheet: false,
        };
    },
    methods: {
        goToHref: function (parameter) {
            switch (parameter) {
                case 'property':
                    window.open(
                        '?module=properties&command=v2_manage_property_page&property_code=' + this.property_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'company':
                    //module=companies&command=company&companyID=7ELEVEN
                    window.open(
                        '?module=companies&command=company&companyID=' + this.$company_code,
                        '_blank', // <- This is what makes it open in a new window.
                    );
                    break;
                case 'lease_logs':
                    break;
                case 'print':
                    break;
            }
            if (this.template_tab === param1) {
                this.template_tab = param1;
                location.href = '#' + param2;
            } else {
                this.template_tab = param1;
                setTimeout(function () {
                    location.href = '#' + param2;
                }, 1000);
            }
        },
    },
};
</script>
