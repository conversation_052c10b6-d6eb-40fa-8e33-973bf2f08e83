<template>
    <select
        :value="value"
        v-on:input="updateValue($event.target.value)"
        :data-placeholder="placeholder"
        :class="class1 || {}"
    >
        <option
            v-for="(name, id) in options"
            :value="name.fieldKey"
            v-if="with<PERSON>ey"
        >
            {{ name.fieldKey }} - {{ name.fieldValue }}
        </option>
        <option
            v-for="(name, id) in options"
            :value="name.fieldKey"
            v-if="!withKey"
        >
            {{ name.fieldValue }}
        </option>
    </select>
</template>
<script>
export default {
    props: {
        value: { type: String, required: true, default: '' },
        options: { type: Array, required: true },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        debug: { type: Boolean, default: false },
        width: { type: String, default: '400px' },
        withKey: { type: Boolean, default: true },
    },
    mounted() {
        var my = this;
        $(this.$el).change(function () {
            if (this.debug) console.log('chosen:change', this.value);
            my.updateValue(this.value); // this == this.$el
        });
        //            this.selfThis = this;
        setTimeout(() => {
            this.init();
        }, 1);
        //            this.init();
        //            this.destroy().init();
    },
    methods: {
        init() {
            if (this.debug) console.log('chosen:init');
            $(this.$el).chosen({
                disable_search_threshold: 5,
                inherit_select_classes: true,
                no_results_text: '',
                search_contains: true,
                width: this.width,
            });
            this.$nextTick(function () {
                this.$el.value = this.value;
                $(this.$el).trigger('chosen:updated');
            });
            return this;
        },
        destroy() {
            if (this.debug) console.log('chosen:destroy');
            $(this.$el).chosen('destroy');
            return this;
        },
        updateValue(value) {
            this.$emit('input', value);
        },
    },
    watch: {
        value() {
            this.$nextTick(function () {
                if (this.debug) console.log('chosen:updated', this.value);
                $(this.$el).trigger('chosen:updated');
            });
        },
        options() {
            this.$nextTick(function () {
                if (this.debug) console.log('chosen:rebuild', this.options.length);
                this.destroy().init();
            });
        },
    },
};
</script>
