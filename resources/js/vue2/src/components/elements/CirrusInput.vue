<template>
    <div style="display: inline-block">
        <span v-if="inputFormat === ''">
            <v-text-field
                dense
                v-model="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                :class="custom_class"
                :placeholder="placeholder"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <!--            <input  type="text" :value="value" :size="size" :maxlength="maxlength" :disabled="disabled" v-if="edit_form" v-on:input="updateValue($event.target.value)">-->
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
            <v-chip
                v-if="error_msg.length > 0 && errorData.id === id"
                v-for="(errorData, index) in error_msg"
                :key="index"
                outlined
                color="error"
            >
                <v-icon left>error</v-icon>{{ errorData.message }}
            </v-chip>
        </span>
        <span v-if="inputFormat === 'search'">
            <v-text-field
                dense
                v-model="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                :class="custom_class"
                :placeholder="placeholder"
                prepend-inner-icon="search"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <!--            <input  type="text" :value="value" :size="size" :maxlength="maxlength" :disabled="disabled" v-if="edit_form" v-on:input="updateValue($event.target.value)">-->
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
            <v-chip
                v-if="error_msg.length > 0 && errorData.id === id"
                v-for="(errorData, index) in error_msg"
                :key="index"
                outlined
                color="error"
            >
                <v-icon left>error</v-icon>{{ errorData.message }}
            </v-chip>
        </span>
        <span v-if="inputFormat === 'dollar'">
            <v-text-field
                dense
                v-model="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                v-on:blur="fixDecimalFormat($event)"
                v-on:keypress="validateInputNumber($event)"
                class="cirrus-input-dollar"
                style="max-width: 100px !important"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{
                    accountingAmountFormat(numberWithCommas(roundTo(value, this.decimal_places))) | numWCurrency
                }}</span
            >
        </span>
        <span v-if="inputFormat === 'percentage'">
            <v-text-field
                dense
                v-model="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                v-on:blur="disable_fix_decimal ? true : fixDecimalFormat($event)"
                v-on:keypress="validateInputNumber($event)"
                class="cirrus-input-percentage"
                style="max-width: 80px !important"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
            <span class="form-input-text">%</span>
        </span>
        <span v-if="inputFormat === 'numberOnly'">
            <v-text-field
                dense
                v-model="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                v-on:blur="fixDecimalFormat($event)"
                v-on:keypress="validateInputNumber($event)"
                class="cirrus-input-dollar"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
        </span>
        <span v-if="inputFormat === 'wholeNumberOnly'">
            <v-text-field
                dense
                v-model="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                v-on:blur="fixIntFormat($event)"
                v-on:keypress="validateInputWholeNumber($event)"
                class="cirrus-input-dollar"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                >{{ value }}</span
            >
        </span>
        <span v-if="inputFormat === 'emailClickable'">
            <v-text-field
                dense
                v-model="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                :class="custom_class"
                :placeholder="placeholder"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                ><a :href="'mailto:' + value">{{ value }}</a></span
            >
            <v-chip
                v-if="error_msg.length > 0 && errorData.id === id"
                v-for="(errorData, index) in error_msg"
                :key="index"
                outlined
                color="error"
            >
                <v-icon left>error</v-icon>{{ errorData.message }}
            </v-chip>
        </span>
        <span v-if="inputFormat === 'phoneClickable'">
            <v-text-field
                dense
                v-model="cirrusInput"
                :disabled="disabled"
                v-if="edit_form"
                @input="updateValue()"
                :class="custom_class"
                :placeholder="placeholder"
                :maxlength="maxlength"
                :rules="rules"
                :tabindex="tabindex"
            ></v-text-field>
            <span
                class="form-input-text"
                v-if="!edit_form"
                ><a :href="'tel:' + value.replace(/[\s\/]/g, '')">{{ value }}</a></span
            >
            <v-chip
                v-if="error_msg.length > 0 && errorData.id === id"
                v-for="(errorData, index) in error_msg"
                :key="index"
                outlined
                color="error"
            >
                <v-icon left>error</v-icon>{{ errorData.message }}
            </v-chip>
        </span>
    </div>
</template>
<script>
import global_mixins from '../../plugins/mixins';

export default {
    props: {
        inputFormat: { type: String, default: '' },
        tabindex: { type: String, default: '0' },
        value: { default: '' },
        custom_class: { default: 'cirrus-input-form-textbox' },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        size: { type: String, default: '60' },
        maxlength: { type: String, default: '60' },
        id: { type: String, default: '60' },
        withKey: { type: Boolean, default: true },
        edit_form: { type: Boolean, default: false },
        disabled: { type: Boolean, default: false },
        tooltip: { type: String, default: '' },
        rules_length: { type: String, default: 60 },
        rules: [(v) => v.length <= this.rules_length || 'Max ' + this.rules_length + ' characters'],
        disable_fix_decimal: { type: Boolean, default: false },
        decimal_places: { type: Number, default: 2 },
    },
    data() {
        return {
            cirrusInput: null,
        };
    },
    mounted() {
        this.cirrusInput = this.value;
    },
    methods: {
        updateValue(value) {
            this.$emit('input', this.cirrusInput);
        },
        validateInputNumber: function (event) {
            // Fallback for older browsers (not commonly needed in modern environments)
            event = event || window.event;

            // Get the character code (code of the key pressed)
            let charCode = event.which || event.keyCode;

            // Define allowed keys and patterns
            const allowedKeys = [46, 8, 9, 27, 13, 110, 190]; // Delete, Backspace, Tab, Escape, Enter, Numpad '.', '.'
            const controlKeys = [65, 67, 88]; // Ctrl/Cmd + A, C, X
            const mathSymbols = [43, 45, 40, 41, 47, 42, 99, 118]; // +, -, (, ), /, *, c, v
            const navigationKeys = [35, 36, 37, 38, 39]; // Home, End, Left, Right, Up, Down

            // Allow specific keys
            if (
                allowedKeys.includes(charCode) || // Allowed keys from the list
                (controlKeys.includes(charCode) && (event.ctrlKey || event.metaKey)) || // Allow Ctrl/Cmd + A, C, X
                mathSymbols.includes(charCode) || // Math symbols
                navigationKeys.includes(charCode) // Navigation keys
            ) {
                // Handle Enter key (charCode 13) to round input
                if (charCode === 13) {
                    const numericValue = parseFloat(event.target.value); // Safely parse value as a number
                    if (!isNaN(numericValue)) {
                        event.target.value = this.roundTo(numericValue, this.decimal_places); // Round and update value
                    }
                }
                return; // Let the allowed key presses proceed
            }

            // Block invalid input (non-numeric values except '.')
            if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
                event.preventDefault(); // Prevent invalid key presses
            }
        },
        validateInputWholeNumber: function (event) {
            // Fallback for older browsers (rarely needed in modern environments)
            event = event || window.event;

            // Get the char code for the key pressed
            let charCode = event.which || event.keyCode;

            // Allowed keys
            const allowedKeys = [8, 9, 27, 13]; // Backspace, Tab, Escape, Enter
            const controlKeys = [65, 67, 88]; // Ctrl/Cmd + A, C, X (Select All, Copy, Cut)

            // Allow special cases
            if (
                allowedKeys.includes(charCode) || // Allow specific keys from allowedKeys
                (controlKeys.includes(charCode) && (event.ctrlKey || event.metaKey)) || // Allow Ctrl/Cmd + A, C, X
                charCode === 190 // Allow period key (may depend on specific context)
            ) {
                // Handle Enter key (13) to round the input value
                if (charCode === 13) {
                    const numericValue = parseFloat(event.target.value); // Safely convert input to a number
                    if (!isNaN(numericValue)) {
                        event.target.value = this.roundTo(numericValue, this.decimal_places); // Round and update
                    }
                }
                return; // Allow the event
            }

            // Prevent invalid key presses (anything outside the range of numbers '0-9')
            if (charCode < 48 || charCode > 57) {
                event.preventDefault(); // Block invalid input
            }
        },
        fixDecimalFormat: function (event) {
            if (event.target.value) {
                const numericValue = parseFloat(event.target.value); // Safely parse the number
                if (!isNaN(numericValue)) {
                    // Update event target value only if it parses successfully to a number
                    event.target.value = this.roundTo(numericValue, this.decimal_places);
                }
            }
        },
        fixIntFormat: function (event) {
            if (event.target.value) {
                const numericValue = parseFloat(event.target.value); // Parse the input as a number safely
                if (!isNaN(numericValue)) {
                    event.target.value = this.roundTo(Math.floor(numericValue), 0); // Apply Math.floor and ensure 0 decimal places
                }
            }
        },
        roundTo: function (n, digits) {
            if (digits === undefined) {
                digits = 0;
            }
            let multiplicator = Math.pow(10, digits);
            n = parseFloat((n * multiplicator).toFixed(11));
            let test = Math.round(n) / multiplicator;
            return test.toFixed(digits);
        },
    },
    watch: {
        cirrusInput: function () {
            this.$emit('input', this.value);
        },
        value(newVal, oldVal) {
            if (newVal != oldVal) {
                //  if (newVal && newVal != null) {
                this.cirrusInput = newVal;
                //  }
            }
        },
    },
    mixins: [global_mixins],
};
</script>
