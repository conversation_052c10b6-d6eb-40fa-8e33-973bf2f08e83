<template>
    <div>
        <div
            class="uix-multiselect ui-widget"
            style="width: 800px; height: 246px"
        >
            <div
                class="multiselect-selected-list"
                style="width: 400px; left: 400px; height: 247px"
            >
                <div class="ui-widget-header ui-corner-tr">
                    <button
                        v-on:click="deSelectAllOption()"
                        type="button"
                        class="uix-control-right ui-button ui-widget ui-state-default ui-corner-all ui-button-icon-only"
                        data-localekey="deselectAll"
                        title="Deselect All"
                        role="button"
                        aria-disabled="false"
                        style="display: inline-block"
                    >
                        <span class="ui-button-icon-primary ui-icon ui-icon-arrowthickstop-1-w"></span
                        ><span class="ui-button-text"></span>
                    </button>
                    <div class="header-text">{{ value.length }} options selected</div>
                </div>
                <div
                    class="uix-list-container ui-widget-content ui-droppable"
                    id="multiselect2_selListContent"
                    style="height: 204px"
                >
                    <div>
                        <div
                            class="multiselect-element-wrapper"
                            v-for="(group2, index2) in option2"
                            :key="index2"
                        >
                            <div style="display: block">
                                <div
                                    class="ui-widget-header ui-priority-secondary group-element group-element-collapsable"
                                    v-if="displayCount"
                                >
                                    <span
                                        :class="
                                            group2.show_toggle
                                                ? 'ui-icon collapse-handle ui-icon-triangle-1-s'
                                                : 'ui-icon collapse-handle ui-icon-triangle-1-e'
                                        "
                                        data-localekey="collapseGroup"
                                        title="Collapse Group"
                                        @click="showToggleSet2(index2)"
                                    ></span>
                                    <span class="label">
                                        {{ group2.fieldGroupNames }} ({{ group2.fieldGroupValues.length }})</span
                                    >
                                    <button
                                        v-on:click="deSelectGroupOption(index2)"
                                        type="button"
                                        class="uix-control-right ui-button ui-widget ui-state-default ui-corner-all ui-button-icon-only"
                                        data-localekey="deselectAll"
                                        title="Deselect All"
                                        role="button"
                                        aria-disabled="false"
                                        style="display: inline-block"
                                    >
                                        <span class="ui-button-icon-primary ui-icon ui-icon-arrowthickstop-1-w"></span
                                        ><span class="ui-button-text"></span>
                                    </button>
                                </div>
                            </div>
                            <div
                                style="display: block"
                                v-if="group2.show_toggle"
                            >
                                <div
                                    v-on:click="deselectDiv(option.fieldKey)"
                                    class="ui-state-default option-element"
                                    unselectable="on"
                                    v-for="option in group2.fieldGroupValues"
                                >
                                    <div>{{ displayKey ? option.fieldKey + ' - ' : '' }}{{ option.fieldValue }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="multiselect-available-list"
                style="width: 400px; left: 0px; height: 247px"
            >
                <div
                    id="mbAddAccountCode_multiSelectDiv"
                    class="ui-widget-header ui-corner-tl"
                >
                    <button
                        v-on:click="selectAllOption()"
                        type="button"
                        class="uix-control-right ui-button ui-widget ui-state-default ui-corner-all ui-button-icon-only"
                        data-localekey="selectAll"
                        title="Select All"
                        role="button"
                        aria-disabled="false"
                        style="display: inline-block"
                    >
                        <span class="ui-button-icon-primary ui-icon ui-icon-arrowthickstop-1-e"></span
                        ><span class="ui-button-text"></span>
                    </button>
                    <button
                        v-on:click="showSearchText()"
                        type="button"
                        class="uix-control-right ui-button ui-widget ui-state-default ui-corner-all ui-button-icon-only"
                        data-localekey="search"
                        title="Search Options"
                        role="button"
                        aria-disabled="false"
                    >
                        <span class="ui-button-icon-primary ui-icon ui-icon-search"></span
                        ><span class="ui-button-text"></span>
                    </button>
                    <input
                        v-on:keyup="searchArray()"
                        ref="refSearchInput"
                        placeholder="Search..."
                        v-model="searchInput"
                        type="text"
                        class="mutiSelectInput uix-search ui-widget-content ui-corner-left"
                        style="width: 363px; height: 24px; font-size: 100%"
                        v-show="showSearch"
                    />
                    <div class="header-text">{{ countOptions() }} options available</div>
                </div>
                <div
                    class="uix-list-container ui-widget-content ui-droppable"
                    id="multiselect2_avListContent2"
                    style="height: 204px"
                >
                    <div>
                        <div class="multiselect-element-wrapper">
                            <div></div>
                        </div>
                        <div
                            class="multiselect-element-wrapper"
                            v-for="(group1, index1) in option1"
                            :key="index1"
                        >
                            <div style="display: block">
                                <div
                                    class="ui-widget-header ui-priority-secondary group-element group-element-collapsable"
                                    v-if="displayCount"
                                >
                                    <span
                                        :class="
                                            group1.show_toggle
                                                ? 'ui-icon collapse-handle ui-icon-triangle-1-s'
                                                : 'ui-icon collapse-handle ui-icon-triangle-1-e'
                                        "
                                        data-localekey="collapseGroup"
                                        title="Collapse Group"
                                        @click="showToggleSet1(index1)"
                                    ></span>
                                    <span class="label"
                                        >{{ group1.fieldGroupNames }} ({{ group1.fieldGroupValues.length }})</span
                                    >
                                    <button
                                        v-on:click="selectGroupOption(index1)"
                                        type="button"
                                        class="uix-control-right ui-button ui-widget ui-state-default ui-corner-all ui-button-icon-only"
                                        data-localekey="selectAll"
                                        title="Select All"
                                        role="button"
                                        aria-disabled="false"
                                        style="display: inline-block"
                                    >
                                        <span class="ui-button-icon-primary ui-icon ui-icon-arrowthickstop-1-e"></span
                                        ><span class="ui-button-text"></span>
                                    </button>
                                </div>
                            </div>
                            <div
                                style="display: block"
                                v-if="group1.show_toggle"
                            >
                                <div
                                    v-on:click="selectDiv(option.fieldKey)"
                                    class="ui-state-default option-element"
                                    unselectable="on"
                                    v-for="option in group1.fieldGroupValues"
                                >
                                    <div>{{ displayKey ? option.fieldKey + ' - ' : '' }}{{ option.fieldValue }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        value: {
            type: Array,
            default: function () {
                return [];
            },
        },
        options: { type: Array },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        debug: { type: Boolean, default: false },
        width: { type: String, default: '400px' },
        withKey: { type: Boolean, default: true },
        displayCount: { type: Boolean, default: true },
    },
    data() {
        return {
            option_orig: [],
            option1: [],
            option2: [],
            finalValue: this.value,
            showSearch: false,
            searchInput: '',
            displayKey: this.withKey,
        };
    },
    mounted() {
        this.initComponent();
    },
    methods: {
        initComponent() {
            this.finalValue = this.value;
            this.option2 = [];
            this.option_orig = JSON.parse(JSON.stringify(this.options));
            this.option_orig = this.option_orig.map((item, index) => ({
                ...item,
                show_toggle: true,
                order: index + 1,
                fieldGroupValues: item.fieldGroupValues.map((subitem, subindex) => ({
                    ...subitem,
                    order: subindex + 1,
                })),
                field_group_values: item.fieldGroupValues.map((subitem, subindex) => ({
                    ...subitem,
                    order: subindex + 1,
                })),
            }));
            this.option1 = JSON.parse(JSON.stringify(this.option_orig));
            for (let a = 0; a <= this.value.length - 1; a++) {
                let key = this.value[a].fieldKey;
                let valueHolder = [];
                for (var x = 0; x <= this.option1.length - 1; x++) {
                    let groupName = this.option1[x].fieldGroupNames;
                    let show_toggle = this.option1[x].show_toggle;
                    let order = this.option1[x].order;
                    let groupValues = this.option1[x].fieldGroupValues;
                    for (let y = 0; y <= groupValues.length - 1; y++) {
                        let fieldKey = groupValues[y].fieldKey;
                        let fieldValue = groupValues[y].fieldValue;
                        let order2 = groupValues[y].order;
                        if (fieldKey == key) {
                            let opt2Value = {
                                fieldGroupNames: groupName,
                                show_toggle: show_toggle,
                                order: order,
                                fieldGroupValues: [
                                    {
                                        fieldKey: fieldKey,
                                        fieldValue: fieldValue,
                                        field_key: fieldKey,
                                        field_value: fieldValue,
                                        value: fieldKey,
                                        label: fieldValue,
                                        order: order2,
                                    },
                                ],
                            };
                            valueHolder = {
                                fieldKey: fieldKey,
                                fieldValue: fieldValue,
                                field_key: fieldKey,
                                field_value: fieldValue,
                                value: fieldKey,
                                label: fieldValue,
                                order: order2,
                            };
                            let flag = false;
                            for (let x2 = 0; x2 <= this.option2.length - 1; x2++) {
                                let groupName2 = this.option2[x2].fieldGroupNames;
                                let groupValues2 = this.option2[x2].fieldGroupValues;
                                if (groupName == groupName2) {
                                    flag = true;
                                }
                                if (flag) {
                                    this.option2[x2].fieldGroupValues.push(valueHolder);
                                    x2 = this.option2.length;
                                }
                            }
                            if (!flag) {
                                this.option2.push(opt2Value);
                            }
                            this.option1[x].fieldGroupValues.splice(y, 1);
                            if (this.option1[x].fieldGroupValues.length <= 0) {
                                this.option1.splice(x, 1);
                            }
                            x = this.option1.length;
                            y = groupValues.length;
                        }
                    }
                }
            }
        },
        selectAllOption() {
            this.option1.forEach((option) => {
                const groupName = option.fieldGroupNames;
                const show_toggle = option.show_toggle;
                const order = option.order;

                option.fieldGroupValues.forEach((groupValue) => {
                    const fieldKey = groupValue.fieldKey;
                    const fieldValue = groupValue.fieldValue;
                    const order2 = groupValue.order;

                    let valueHolder = {
                        fieldKey: fieldKey,
                        fieldValue: fieldValue,
                        field_key: fieldKey,
                        field_value: fieldValue,
                        value: fieldKey,
                        label: fieldValue,
                        order: order2,
                    };

                    this.finalValue.push(valueHolder);

                    let existingOption = this.option2.find((opt2) => opt2.fieldGroupNames === groupName);

                    if (existingOption) {
                        existingOption.fieldGroupValues.push(valueHolder);
                    } else {
                        this.option2.push({
                            fieldGroupNames: groupName,
                            show_toggle: show_toggle,
                            order: order,
                            fieldGroupValues: [valueHolder],
                        });
                    }
                });
            });

            this.option1 = [];

            this.option2.sort((a, b) => {
                if (a.order < b.order) return -1;
                if (a.order > b.order) return 1;
                return 0;
            });
            this.option2 = this.option2.map((item) => ({
                ...item,
                fieldGroupValues: item.fieldGroupValues.sort((a, b) => a.order - b.order),
            }));

            this.updateValue(this.finalValue);
        },
        deSelectAllOption: function () {
            this.option2.forEach((option) => {
                const groupName = option.fieldGroupNames;
                const show_toggle = option.show_toggle;
                const order = option.order;

                option.fieldGroupValues.forEach((groupValue) => {
                    const fieldKey = groupValue.fieldKey;
                    const fieldValue = groupValue.fieldValue;
                    const order2 = groupValue.order;

                    let valueHolder = {
                        fieldKey: fieldKey,
                        fieldValue: fieldValue,
                        field_key: fieldKey,
                        field_value: fieldValue,
                        value: fieldKey,
                        label: fieldValue,
                        order: order2,
                    };

                    this.finalValue = this.finalValue.filter((finalVal) => finalVal.fieldKey !== fieldKey);

                    let existingOption = this.option1.find((opt1) => opt1.fieldGroupNames === groupName);

                    if (existingOption) {
                        existingOption.fieldGroupValues.push(valueHolder);
                    } else {
                        this.option1.push({
                            fieldGroupNames: groupName,
                            show_toggle: show_toggle,
                            order: order,
                            fieldGroupValues: [valueHolder],
                        });
                    }
                });
            });

            this.option2 = [];

            this.option1.sort((a, b) => {
                if (a.order < b.order) return -1;
                if (a.order > b.order) return 1;
                return 0;
            });
            this.option1 = this.option1.map((item) => ({
                ...item,
                fieldGroupValues: item.fieldGroupValues.sort((a, b) => a.order - b.order),
            }));

            this.updateValue(this.finalValue);
        },
        showSearchText: function () {
            if (this.showSearch) {
                this.showSearch = false;
            } else {
                this.showSearch = true;
            }
            this.$nextTick(() => {
                this.$refs.refSearchInput.focus();
            });
        },
        getToggleValueByName: function (fieldGroupNames) {
            let data = this.option1.filter((m) => m.fieldGroupNames === fieldGroupNames);
            if (data.length > 1) return data[0].show_toggle;
            return true;
        },
        searchArray: function () {
            let tempArray = [];

            for (let x = 0; x <= this.option_orig.length - 1; x++) {
                let fieldGroupNames = this.option_orig[x].fieldGroupNames;
                let show_toggle = this.getToggleValueByName(fieldGroupNames);
                let order = this.option_orig[x].order;
                let fieldGroupValues = this.option_orig[x].fieldGroupValues;

                fieldGroupValues = fieldGroupValues.filter((p) => {
                    if (this.option2.length > 0) {
                        let selectedOptions = [];
                        for (let x = 0; x <= this.option2.length - 1; x++) {
                            let fieldGroupNames2 = this.option2[x].fieldGroupNames;
                            let fieldGroupValues2 = this.option2[x].fieldGroupValues;
                            fieldGroupValues2 = fieldGroupValues2.filter((pp) => {
                                selectedOptions.push(pp.fieldKey);
                            });
                        }

                        if (!selectedOptions.includes(p.fieldKey)) {
                            return (
                                p.fieldKey.toLowerCase().includes(this.searchInput.toLowerCase()) ||
                                p.fieldValue.toLowerCase().includes(this.searchInput.toLowerCase())
                            );
                        }
                    } else {
                        return (
                            p.fieldKey.toLowerCase().includes(this.searchInput.toLowerCase()) ||
                            p.fieldValue.toLowerCase().includes(this.searchInput.toLowerCase())
                        );
                    }
                    // return foundAccount !== -1;
                });

                tempArray.push({
                    fieldGroupNames: fieldGroupNames,
                    show_toggle: show_toggle,
                    order: order,
                    fieldGroupValues: fieldGroupValues,
                });
            }
            this.option1 = tempArray;
        },
        init() {
            if (this.debug) console.log('chosen:init');
            this.$nextTick(function () {
                this.$el.value = this.value;
            });
            return this;
        },
        destroy() {
            return this;
        },
        updateValue(value) {
            this.$emit('input', value);
        },
        selectDiv: function (key) {
            let valueHolder = [];
            for (var x = 0; x <= this.option1.length - 1; x++) {
                let groupName = this.option1[x].fieldGroupNames;
                let show_toggle = this.option1[x].show_toggle;
                let order = this.option1[x].order;
                let groupValues = this.option1[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    let fieldKey = groupValues[y].fieldKey;
                    let fieldValue = groupValues[y].fieldValue;
                    let order2 = groupValues[y].order;
                    if (fieldKey == key) {
                        let opt2Value = {
                            fieldGroupNames: groupName,
                            show_toggle: show_toggle,
                            order: order,
                            fieldGroupValues: [
                                {
                                    fieldKey: fieldKey,
                                    fieldValue: fieldValue,
                                    field_key: fieldKey,
                                    field_value: fieldValue,
                                    value: fieldKey,
                                    label: fieldValue,
                                    order: order2,
                                },
                            ],
                        };
                        valueHolder = {
                            fieldKey: fieldKey,
                            fieldValue: fieldValue,
                            field_key: fieldKey,
                            field_value: fieldValue,
                            value: fieldKey,
                            label: fieldValue,
                            order: order2,
                        };
                        let flag = false;
                        for (let x2 = 0; x2 <= this.option2.length - 1; x2++) {
                            let groupName2 = this.option2[x2].fieldGroupNames;
                            let groupValues2 = this.option2[x2].fieldGroupValues;
                            if (groupName == groupName2) {
                                flag = true;
                            }
                            if (flag) {
                                this.option2[x2].fieldGroupValues.push(valueHolder);
                                x2 = this.option2.length;
                            }
                        }
                        if (!flag) {
                            this.option2.push(opt2Value);
                        }
                        this.option1[x].fieldGroupValues.splice(y, 1);
                        if (this.option1[x].fieldGroupValues.length <= 0) {
                            this.option1.splice(x, 1);
                        }
                        x = this.option1.length;
                        y = groupValues.length;
                    }
                }
            }

            this.option2.sort((a, b) => {
                if (a.order < b.order) return -1;
                if (a.order > b.order) return 1;
                return 0;
            });
            this.option2 = this.option2.map((item) => ({
                ...item,
                fieldGroupValues: item.fieldGroupValues.sort((a, b) => a.order - b.order),
            }));
            this.finalValue.push(valueHolder);
            this.updateValue(this.finalValue);
        },
        deselectDiv: function (key) {
            let valueHolder = [];
            for (var x = 0; x <= this.option2.length - 1; x++) {
                let groupName = this.option2[x].fieldGroupNames;
                let show_toggle = this.option2[x].show_toggle;
                let order = this.option2[x].order;
                let groupValues = this.option2[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    let fieldKey = groupValues[y].fieldKey;
                    let fieldValue = groupValues[y].fieldValue;
                    let order2 = groupValues[y].order;
                    if (fieldKey == key) {
                        let opt2Value = {
                            fieldGroupNames: groupName,
                            show_toggle: show_toggle,
                            order: order,
                            fieldGroupValues: [
                                {
                                    fieldKey: fieldKey,
                                    fieldValue: fieldValue,
                                    field_key: fieldKey,
                                    field_value: fieldValue,
                                    value: fieldKey,
                                    label: fieldValue,
                                    order: order2,
                                },
                            ],
                        };
                        valueHolder = {
                            fieldKey: fieldKey,
                            fieldValue: fieldValue,
                            field_key: fieldKey,
                            field_value: fieldValue,
                            value: fieldKey,
                            label: fieldValue,
                            order: order2,
                        };

                        for (let a = 0; a <= this.finalValue.length - 1; a++) {
                            let fieldKeyFinal = this.finalValue[a].fieldKey;
                            if (fieldKey == fieldKeyFinal) {
                                this.finalValue.splice(a, 1);
                            }
                        }
                        let flag = false;
                        for (let x2 = 0; x2 <= this.option1.length - 1; x2++) {
                            let groupName2 = this.option1[x2].fieldGroupNames;
                            let groupValues2 = this.option1[x2].fieldGroupValues;
                            if (groupName == groupName2) {
                                flag = true;
                            }
                            if (flag) {
                                this.option1[x2].fieldGroupValues.push(valueHolder);
                                x2 = this.option1.length;
                            }
                        }
                        if (!flag) {
                            this.option1.push(opt2Value);
                        }
                        this.option2[x].fieldGroupValues.splice(y, 1);
                        if (this.option2[x].fieldGroupValues.length <= 0) {
                            this.option2.splice(x, 1);
                        }
                        x = this.option2.length;
                        y = groupValues.length;
                    }
                }
            }
            this.option1.sort((a, b) => {
                if (a.order < b.order) return -1;
                if (a.order > b.order) return 1;
                return 0;
            });
            this.option1 = this.option1.map((item) => ({
                ...item,
                fieldGroupValues: item.fieldGroupValues.sort((a, b) => a.order - b.order),
            }));

            this.updateValue(this.finalValue);
        },
        countOptions: function () {
            let ctr = 0;
            for (let x = 0; x <= this.option1.length - 1; x++) {
                let groupValues = this.option1[x].fieldGroupValues;
                for (let y = 0; y <= groupValues.length - 1; y++) {
                    ctr++;
                }
            }

            return ctr;
        },
        showToggleSet1: function (index) {
            this.option1[index].show_toggle = !this.option1[index].show_toggle;
        },
        showToggleSet2: function (index) {
            this.option2[index].show_toggle = !this.option2[index].show_toggle;
        },
        selectGroupOption(index) {
            const { fieldGroupNames, show_toggle, fieldGroupValues, order } = this.option1[index];

            const existingOption = this.option2.find((option) => option.fieldGroupNames === fieldGroupNames);

            if (existingOption) {
                existingOption.fieldGroupValues = [...existingOption.fieldGroupValues, ...fieldGroupValues];
            } else {
                this.option2.push({ fieldGroupNames, show_toggle, fieldGroupValues, order });
            }

            this.option2.sort((a, b) => {
                if (a.order < b.order) return -1;
                if (a.order > b.order) return 1;
                return 0;
            });
            this.option2 = this.option2.map((item) => ({
                ...item,
                fieldGroupValues: item.fieldGroupValues.sort((a, b) => a.order - b.order),
            }));

            this.option1.splice(index, 1);
            this.finalValue = [];
            this.option2.forEach((option) => {
                option.fieldGroupValues.forEach((groupValue) => {
                    const fieldKey = groupValue.fieldKey;
                    const fieldValue = groupValue.fieldValue;
                    const order = groupValue.order;
                    let valueHolder = {
                        fieldKey: fieldKey,
                        fieldValue: fieldValue,
                        field_key: fieldKey,
                        field_value: fieldValue,
                        value: fieldKey,
                        label: fieldValue,
                        order: order,
                    };
                    this.finalValue.push(valueHolder);
                });
                this.updateValue(this.finalValue);
            });
        },
        deSelectGroupOption(index) {
            const { fieldGroupNames, show_toggle, fieldGroupValues, order } = this.option2[index];

            const existingOption = this.option1.find((option) => option.fieldGroupNames === fieldGroupNames);

            if (existingOption) {
                existingOption.fieldGroupValues = [...existingOption.fieldGroupValues, ...fieldGroupValues];
            } else {
                this.option1.push({ fieldGroupNames, show_toggle, fieldGroupValues, order });
            }

            this.option1.sort((a, b) => {
                if (a.order < b.order) return -1;
                if (a.order > b.order) return 1;
                return 0;
            });
            this.option1 = this.option1.map((item) => ({
                ...item,
                fieldGroupValues: item.fieldGroupValues.sort((a, b) => a.order - b.order),
            }));

            this.option2.splice(index, 1);
            this.finalValue = [];
            this.option2.forEach((option) => {
                option.fieldGroupValues.forEach((groupValue) => {
                    const fieldKey = groupValue.fieldKey;
                    const fieldValue = groupValue.fieldValue;
                    const order = groupValue.order;
                    let valueHolder = {
                        fieldKey: fieldKey,
                        fieldValue: fieldValue,
                        field_key: fieldKey,
                        field_value: fieldValue,
                        value: fieldKey,
                        label: fieldValue,
                        order: order,
                    };
                    this.finalValue.push(valueHolder);
                });
                this.updateValue(this.finalValue);
            });
        },
    },
    watch: {
        value() {
            this.$nextTick(function () {});
        },
        options() {
            this.$nextTick(function () {
                this.destroy().init();
            });
            this.initComponent();
        },
    },
};
</script>
