<template>
    <span>
        <div v-if="edit_form">
            <div v-if="has_saved_file === false">
                <div id="app">
                    <div
                        class="p-12 bg-gray-100 upload-btn-wrapper"
                        @dragover="dragover"
                        @dragleave="dragleave"
                        @drop="drop"
                    >
                        <div v-if="!upload_version_2">
                            <input
                                type="file"
                                :id="id"
                                @change="fileSelected"
                                v-on:input="updateValue($event.target.value)"
                                ref="myFiles"
                                :accept="accept_type_list[accept_type]"
                            />
                            <label
                                :for="id"
                                class="block cursor-pointer upload-btn-label right v-btn v-btn--contained theme--light v-size--default primary v-btn--small"
                                style="margin-right: 0px"
                            >
                                <v-icon dark>cloud_upload</v-icon>&nbsp;
                                {{ file_upload_button_name }}
                            </label>
                            <label
                                v-if="file_upload_button_name !== 'Attachment'"
                                @click="removeAttachedFile()"
                                class="v-btn v-btn--flat v-btn--icon v-btn--small theme--light"
                                style="margin-left: 0px"
                            >
                                <v-icon color="red">close</v-icon>
                            </label>
                        </div>
                        <div v-if="upload_version_2">
                            <v-row class="form-row no-gutters">
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="9"
                                    xl="9"
                                >
                                    <div
                                        class="fileuploader-input-caption"
                                        style="width: 100%; height: 25px; padding-top: 4px"
                                    >
                                        <span v-if="file_upload_button_name === 'Attachment'"
                                            >Additional Attachment files to upload</span
                                        >
                                        <span v-if="file_upload_button_name !== 'Attachment'">{{
                                            file_upload_button_name
                                        }}</span>
                                        <label
                                            v-if="file_upload_button_name !== 'Attachment'"
                                            @click="removeAttachedFile()"
                                            class="v-btn v-btn--flat v-btn--icon v-btn--small theme--light"
                                            style="margin-left: 0px"
                                        >
                                            <v-icon color="red">close</v-icon>
                                        </label>
                                    </div>
                                </v-col>
                                <v-col
                                    cols="12"
                                    xs="12"
                                    sm="12"
                                    md="12"
                                    lg="3"
                                    xl="3"
                                >
                                    <div>
                                        <input
                                            type="file"
                                            :id="id"
                                            @change="fileSelected"
                                            v-on:input="updateValue($event.target.value)"
                                            ref="myFiles"
                                            :accept="accept_type_list[accept_type]"
                                        />
                                        <label
                                            :for="id"
                                            class="block cursor-pointer upload-btn-label right v-btn v-btn--contained theme--light v-size--default primary v-btn--small"
                                            style="margin-right: 0px"
                                        >
                                            <v-icon dark>cloud_upload</v-icon>&nbsp; Choose File
                                        </label>
                                    </div>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </div>
                <v-chip
                    v-if="error_msg.length > 0 && errorData.id === id"
                    v-for="(errorData, index) in error_msg"
                    :key="index"
                    outlined
                    color="error"
                >
                    <v-icon left>error</v-icon>{{ errorData.message }}
                </v-chip>
            </div>
        </div>
        <span v-if="!edit_form && has_saved_file === false">
            <span
                class="form-input-text"
                v-if="show_download_label"
                >No Attachment</span
            >
        </span>

        <span v-if="has_saved_file === true">
            <span class="form-input-text">
                <a
                    :href="returnValue()"
                    target="_blank"
                >
                    <img
                        v-if="accept_type === '*'"
                        :src="this.$assetDomain + 'assets/images/icons/flat_notes_green_20.png'"
                        alt="attachement"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'csv'"
                        :src="this.$assetDomain + 'assets/images/icons/csv.png'"
                        alt="csv"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'excel'"
                        :src="this.$assetDomain + 'assets/images/icons/xls.png'"
                        alt="excel"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'text'"
                        :src="this.$assetDomain + 'assets/images/icons/text.png'"
                        alt="text"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'image'"
                        :src="this.$assetDomain + 'assets/images/icons/img.png'"
                        alt="image"
                        class="icon"
                    />
                    <img
                        v-if="accept_type === 'pdf'"
                        :src="this.$assetDomain + 'assets/images/icons/pdf.png'"
                        alt="pdf"
                        class="icon"
                    />&nbsp;
                    <span v-if="show_download_label">Download</span>
                </a>
            </span>
        </span>
    </span>
</template>
<script>
import { BYTES_IN_MEGABYTES } from '../../constants';

export default {
    props: {
        value: {},
        placeholder: { type: String, default: '' },
        has_saved_file: { type: Boolean, default: false },
        show_download_label: { type: Boolean, default: true },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        upload_version_2: { type: Boolean, default: false },
        size: { type: String, default: '60' },
        max_length: { type: String, default: '60' },
        id: { type: String, default: '' },
        with_key: { type: Boolean, default: true },
        edit_form: { type: Boolean, default: false },
        accept_type: { type: String, default: '*' },
        size_limit: { type: Number, default: '0' },
        externalFile: File, // Dynamic external file as a prop
    },
    data() {
        return {
            cirrus_input: this.value,
            file_holder: null,
            file_upload_button_name: 'Attachment',
            href: 'download.php?fileID=' + this.value,
            accept_type_list: {
                csv: '.csv',
                excel: 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                text: 'text/plain',
                image: 'image/*',
                pdf: '.pdf',
                'pdf-image': 'image/jpeg,image/gif,image/png,application/pdf',
            },
            lc_has_saved_file: this.has_saved_file,
            filelist: [],
        };
    },
    methods: {
        returnValue() {
            return 'download.php?fileID=' + this.value;
        },
        updateValue(value) {
            this.$emit('input', value);
        },
        fileSelected: function () {
            this.file_holder = this.$refs.myFiles.files;

            this.$emit('input', this.file_holder);
            let fileList = this.$refs.myFiles.files;
            if (fileList.length === 1) {
                let fileDetails = fileList[0];
                let fileName = fileDetails.name;
                let fileType = fileDetails.type;
                let fileExtension = fileName.split('.');
                let fileNameFirstPart = fileExtension[0];
                let fileSize = fileDetails.size;
                let sizeLimit = this.size_limit * BYTES_IN_MEGABYTES;

                if (fileSize <= sizeLimit) {
                    fileType = fileType.split('/');
                    fileType = fileType[1];

                    fileExtension = fileExtension[fileExtension.length - 1];
                    let file_upload_button_name =
                        fileNameFirstPart.substr(0, 5) + '...' + fileNameFirstPart.substr(-3) + '.' + fileExtension;
                    if (this.upload_version_2)
                        file_upload_button_name =
                            fileNameFirstPart.substr(0, 20) +
                            '...' +
                            fileNameFirstPart.substr(-3) +
                            '.' +
                            fileExtension;
                    this.file_upload_button_name = file_upload_button_name;
                } else {
                    alert(
                        'Unable to upload file because the size exceeds the maximum allowed file size of ' +
                            this.size_limit +
                            'MB.',
                    );
                    this.removeAttachedFile();
                }
            }
        },
        removeAttachedFile: function () {
            this.file_upload_button_name = 'Attachment';
            this.file_holder = null;
            this.$refs.myFiles.value = null;
            this.$emit('input', this.file_holder);
        },
        onChange() {
            this.filelist = [...this.$refs.myFiles.files];
        },
        remove(i) {
            this.filelist.splice(i, 1);
        },
        dragover(event) {
            event.preventDefault();
            // Add some visual fluff to show the user can drop its files
            if (!event.currentTarget.classList.contains('bg-green-300')) {
                event.currentTarget.classList.remove('bg-gray-100');
                event.currentTarget.classList.add('bg-green-300');
            }
        },
        dragleave(event) {
            // Clean up
            event.currentTarget.classList.add('bg-gray-100');
            event.currentTarget.classList.remove('bg-green-300');
        },
        drop(event) {
            event.preventDefault();
            this.$refs.myFiles.files = event.dataTransfer.files;
            this.fileSelected(); // Trigger the onChange event manually
            // Clean up
            event.currentTarget.classList.add('bg-gray-100');
            event.currentTarget.classList.remove('bg-green-300');
        },
        setExternalFile(file) {
            if (file) {
                this.$refs.myFiles.files = file;
                this.fileSelected();
            }
        },
    },
    watch: {
        cirrus_input: function () {
            if (this.file_holder === null) {
                this.removeAttachedFile();
            }
            this.$emit('input', this.file_holder);
        },
        value: function () {
            if (this.value === null) {
                this.removeAttachedFile();
            }
        },
    },
    mounted() {
        if (this.externalFile) this.setExternalFile(this.externalFile);
    },
};
</script>
<style>
.upload-btn-label {
    background: #939496 !important;
    color: white !important;
    font-weight: bolder;
}

.upload-btn-wrapper input[type='file'] {
    display: none;
}
</style>
