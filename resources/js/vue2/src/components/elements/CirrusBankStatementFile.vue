<template>
    <div style="display: inline-block">
        <input
            type="file"
            name="file"
            :id="'file-' + $filters.date2Sql(date)"
            :ref="'file-' + $filters.date2Sql(date)"
            v-on:change="setUpload()"
            style="display: none"
        />
        <template v-if="btnLinks">
            <a
                href="#"
                @click.prevent="openImport()"
                v-if="has_download"
                >[Import File]</a
            >&nbsp;
            <a
                href="#"
                @click.prevent="download()"
                v-if="has_import"
                >[Download from Macquarie Bank]</a
            >&nbsp;
        </template>
        <template v-else>
            <v-btn
                tile
                small
                data-tooltip="Download TXN from Macquarie Bank"
                color="primary"
                @click.prevent="askDownload()"
                v-if="has_download"
            >
                <v-icon
                    left
                    dark
                    size="18"
                    >mdi-download</v-icon
                >
                Download
            </v-btn>
            &nbsp;
            <v-btn
                tile
                small
                data-tooltip="Import from a bank statement file"
                data-position="bottom right"
                color="primary"
                @click.prevent="openImport()"
                v-if="has_import"
            >
                <v-icon
                    left
                    dark
                    size="18"
                    >mdi-upload</v-icon
                >
                Import
            </v-btn>
            &nbsp;
        </template>

        <!-- IMPORT FILE MODAL -->
        <v-dialog
            v-model="import_mdl.show"
            persistent
            :max-width="import_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Import File
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="import_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="page-noty">
                        <div class="warning-box">
                            File formats you can import:
                            <ul>
                                <li
                                    v-for="(ft, index) in import_mdl.file_types"
                                    :key="index"
                                >
                                    {{ ft }}
                                </li>
                            </ul>
                            Or you can create your own bank statement file.
                            <a
                                href="#"
                                @click.prevent="downloadTemplate()"
                                >[Download our CSV Template]</a
                            >
                            <br />
                            Maximum of 1000 bank statement lines per import.
                        </div>
                    </div>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                class="form-label required"
                                >Select a File</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                class="form-input"
                            >
                                <v-btn
                                    color="primary"
                                    depressed
                                    tile
                                    small
                                    @click="browse()"
                                    ><v-icon
                                        left
                                        dark
                                        size="18"
                                        >mdi-folder</v-icon
                                    >
                                    Browse</v-btn
                                >&nbsp;
                                <strong>{{ import_mdl.file_name }}</strong>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                class="form-label"
                                >Has table headers</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                class="form-input"
                            >
                                <v-checkbox
                                    v-model="import_mdl.has_file_header"
                                    color="primary"
                                />
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="success"
                        depressed
                        tile
                        small
                        @click="handleImport()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-eye</v-icon
                        >Import Preview</v-btn
                    >
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="import_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- MATCH FILE MODAL -->
        <v-dialog
            v-model="match_mdl.show"
            persistent
            :max-width="match_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Import Options
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="match_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div
                        class="c8-padding-1"
                        v-if="match_mdl.view == 'fields'"
                    >
                        <v-row>
                            <v-col
                                xs="12"
                                sm="6"
                                style="padding-bottom: 0"
                            >
                                <div
                                    class="page-title c8-btm-space"
                                    style="padding-top: 4px"
                                >
                                    Imported Bank Statement Lines ({{ match_mdl.curr_line_key + 1 }}/{{
                                        match_mdl.lines.length
                                    }})
                                </div>
                                <v-checkbox
                                    v-model="match_mdl.one_amount"
                                    label="Has one amount for debit and credit"
                                    color="primary"
                                />
                            </v-col>
                            <v-col
                                xs="12"
                                sm="6"
                                align="right"
                                style="padding-bottom: 0; padding-top: 47px"
                            >
                                <v-btn
                                    depressed
                                    tile
                                    small
                                    :disabled="match_mdl.curr_line_key == 0"
                                    @click="match_mdl.curr_line_key -= 1"
                                    ><v-icon>mdi-chevron-left</v-icon> Prev</v-btn
                                >
                                <v-btn
                                    depressed
                                    tile
                                    small
                                    :disabled="match_mdl.curr_line_key >= match_mdl.lines.length - 1"
                                    @click="match_mdl.curr_line_key += 1"
                                    >Next <v-icon>mdi-chevron-right</v-icon></v-btn
                                >
                            </v-col>
                        </v-row>
                        <div
                            class="page-noty"
                            v-if="accounts_not_found.length > 0"
                        >
                            <div class="warning-box">
                                <div
                                    v-for="(no, nondex) in accounts_not_found"
                                    :key="'warning-acno' + nondex"
                                >
                                    &#8226; Bank statement lines has an account number <strong>{{ no }}</strong> found
                                    is not registered on bank accounts.
                                </div>
                            </div>
                        </div>
                        <v-row>
                            <v-col
                                xs="12"
                                sm="12"
                            >
                                <div class="page-list">
                                    <div class="c8-page-table">
                                        <table class="bordered">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th>Statement Line Data</th>
                                                    <th>Assign To</th>
                                                    <th>Format</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    v-for="(field, field_code) in match_mdl.fields"
                                                    :key="'curr-line-' + field_code"
                                                >
                                                    <td class="c8-page-table-row-header text-right">
                                                        {{ field.label }}
                                                        <span
                                                            v-if="field.required"
                                                            class="required"
                                                            >*</span
                                                        >
                                                    </td>
                                                    <td>
                                                        <div v-if="curr_line">
                                                            <span v-if="field.type == 'amount'">
                                                                <span
                                                                    v-if="
                                                                        match_mdl.one_amount &&
                                                                        (field_code == 'debit' ||
                                                                            field_code == 'credit')
                                                                    "
                                                                >
                                                                    {{
                                                                        drCrNum(
                                                                            field_code,
                                                                            curr_line[field.header_code],
                                                                        ) | num2AmtStr
                                                                    }}
                                                                </span>
                                                                <span v-else>{{
                                                                    cleanAmount(
                                                                        curr_line[field.header_code],
                                                                        field_code,
                                                                    ) | num2AmtStr
                                                                }}</span>
                                                            </span>
                                                            <span v-else-if="field.type == 'bool'">
                                                                <v-checkbox
                                                                    disabled
                                                                    v-model="curr_line[field.header_code]"
                                                                    color="primary"
                                                                />
                                                            </span>
                                                            <span v-else-if="field_code == 'trans_type'">
                                                                <!-- {{ getTransType(curr_line[field.header_code]) }} -->
                                                                <cirrus-single-select
                                                                    v-model="curr_line[field.header_code]"
                                                                    :options="trans_type_opts"
                                                                />
                                                            </span>
                                                            <span v-else>{{ curr_line[field.header_code] }}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <cirrus-single-select
                                                            v-model="field.header_code"
                                                            :options="headers_opts"
                                                            @input="tempFieldChange(field_code)"
                                                        />
                                                    </td>
                                                    <td>
                                                        <cirrus-single-select
                                                            v-if="field.type == 'date'"
                                                            v-model="field.format"
                                                            :options="match_mdl.date_formats"
                                                        />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                    <div
                        class="c8-padding-1"
                        v-else-if="match_mdl.view == 'balances'"
                    >
                        <div class="page-form">
                            <v-row
                                class="form-row"
                                v-if="match_mdl.last_closing_bal && match_mdl.last_closing_bal.balance"
                            >
                                <v-col
                                    xs="12"
                                    sm="4"
                                    class="form-label"
                                    >Last Closing Balance at
                                    <strong>{{ match_mdl.last_closing_bal.date | sql2Date }}</strong
                                    >:</v-col
                                >
                                <v-col
                                    xs="12"
                                    sm="8"
                                    class="form-input"
                                >
                                    <span class="form-input-text"
                                        ><strong>{{ match_mdl.last_closing_bal.balance | num2AmtStr }}</strong></span
                                    >
                                </v-col>
                            </v-row>
                            <template v-if="Object.keys(match_mdl.balances).length > 0">
                                <v-row
                                    class="form-row"
                                    style="max-height: 40px !important"
                                    v-for="(bal_amount, bal_date) in match_mdl.balances"
                                    :key="'row-' + bal_date"
                                >
                                    <v-col
                                        xs="12"
                                        sm="4"
                                        class="form-label"
                                        >File Closing Balance at {{ bal_date }}:</v-col
                                    >
                                    <v-col
                                        xs="12"
                                        sm="8"
                                        class="form-input"
                                    >
                                        <v-text-field
                                            dense
                                            v-model="match_mdl.balances[bal_date]"
                                            @blur="formatClosingBal(bal_date)"
                                            style="max-width: 300px"
                                        />
                                    </v-col>
                                </v-row>
                            </template>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />

                    <template v-if="match_mdl.view == 'fields'">
                        <v-btn
                            color="primary"
                            depressed
                            tile
                            small
                            @click="setMatchBalance()"
                            ><v-icon
                                left
                                dark
                                size="18"
                                >mdi-chevron-right</v-icon
                            >
                            Next</v-btn
                        >
                    </template>
                    <template v-else-if="match_mdl.view == 'balances'">
                        <v-btn
                            color="success"
                            depressed
                            tile
                            small
                            @click="saveImport()"
                            ><v-icon
                                left
                                dark
                                size="18"
                                >mdi-content-save</v-icon
                            >Import</v-btn
                        >
                        <v-btn
                            color="primary"
                            depressed
                            tile
                            small
                            @click="match_mdl.view = 'fields'"
                            ><v-icon
                                left
                                dark
                                size="18"
                                >mdi-chevron-left</v-icon
                            >
                            Back</v-btn
                        >
                    </template>
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="cancelImport()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- ASK DOWNLOAD  -->
        <v-dialog
            v-model="ask_dl_mdl.show"
            persistent
            :max-width="ask_dl_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Download TXN file direct from Macquarie Bank
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="ask_dl_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <p class="c8-padding-1">Do you want to download the bank statements for {{ date }}?</p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="success"
                        tile
                        small
                        @click.prevent="download()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-check</v-icon
                        >Download</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="ask_dl_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
import moment from 'moment';
export default {
    name: 'CirrusBankStatementFile',
    props: {
        date: { type: String, default: '' },
        fileBank: { type: String, default: {} },
        hasDownload: { type: Boolean, default: true },
        hasImport: { type: Boolean, default: true },
        btnLinks: { type: Boolean, default: false },
    },
    data() {
        return {
            has_download: false,
            has_import: false,
            accounts_not_found: [],
            bank: null,
            import_mdl: {
                show: false,
                width: 650,
                has_file_header: true,
                file_types: ['CSV', 'TXN'],
                file_name: '',
            },
            ask_dl_mdl: {
                show: false,
                width: 390,
            },
            trans_type_opts: [
                { label: 'Direct Deposit', value: 'dir' },
                { label: 'BPay', value: 'bpa' },
                { label: 'EFT', value: 'eft' },
                { label: 'Cheque', value: 'chq' },
            ],
            match_mdl: {
                width: 1024,
                show: false,
                headers: {},
                fields: {},
                lines: [],
                balances: {},
                last_closing_bal: {},
                one_amount: false,
                curr_line_key: 0,
                view: 'fields',
                date_formats: [
                    { label: 'Date/Month/Year', value: 'dmy' },
                    { label: 'Month/Date/Year', value: 'mdy' },
                    { label: 'Year/Month/Date', value: 'ymd' },
                ],
            },
        };
    },
    watch: {
        fileBank: function (newVal, oldVal) {
            // watch it
            this.bank = newVal;
            this.load();
        },
        hasDownload: function (newVal, oldVal) {
            // watch it
            this.has_download = newVal;
            this.load();
        },
        hasImport: function (newVal, oldVal) {
            // watch it
            this.has_import = newVal;
            console.log('import', this.has_import);
            this.load();
        },
    },
    computed: {
        curr_line() {
            let data = {};
            if (
                this.match_mdl.lines &&
                this.match_mdl.lines.length > 0 &&
                this.match_mdl.lines[this.match_mdl.curr_line_key]
            ) {
                data = this.match_mdl.lines[this.match_mdl.curr_line_key];
            }
            return data;
        },
        headers_opts() {
            let data = [];
            if (this.match_mdl.headers) {
                for (var hd_code in this.match_mdl.headers) {
                    data.push({ label: this.match_mdl.headers[hd_code].label, value: hd_code });
                }
            }
            return data;
        },
    },
    methods: {
        formatClosingBal(bal_date) {
            if (!this.match_mdl.balances[bal_date]) this.match_mdl.balances[bal_date] = null;
            else {
                let value = parseFloat(this.match_mdl.balances[bal_date].replace(/[^0-9\.\-]+/g, ''));
                if (this.match_mdl.balances[bal_date] != null && this.match_mdl.balances[bal_date] != '') {
                    this.match_mdl.balances[bal_date] = value
                        .toFixed(2)
                        .toString()
                        .replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1,');
                } else {
                    this.match_mdl.balances[bal_date] = null;
                }
            }
        },
        getTransType(type) {
            let trans_type = this.trans_type_opts.find((row) => {
                return row.value == type;
            });

            if (trans_type) return trans_type.label;
            else return '';
        },
        downloadTemplate() {
            this.$api
                .get('bank-statements/template/bank_statement_report_csv', this.req({ no_load: false }))
                .then((response) => {
                    this.printDownload(response.data.file, 'bank_statement_report_csv', 'csv');
                });
        },
        cleanAmount(amount_raw, field_code) {
            if (amount_raw) {
                return amount_raw.replace(/[^0-9\.-]+/g, '');
            } else {
                if (field_code == 'balance') return '';
                else return this.$filters.num2Amt(0);
            }
        },
        drCrNum(type, amount_raw) {
            let amount = this.cleanAmount(amount_raw);
            if (type == 'debit') {
                if (parseFloat(amount) > 0) return null;
                else return amount;
            } else if (type == 'credit') {
                if (parseFloat(amount) < 0) {
                    return null;
                } else return amount;
            }
        },
        tempFieldChange(field_code) {
            let curr_field = this.match_mdl.fields[field_code];
            let has_same = false;
            for (var fld in this.match_mdl.fields) {
                if (field_code != fld && this.match_mdl.fields[fld].header_code == curr_field.header_code) {
                    has_same = true;
                    break;
                }
            }
            if (has_same) {
                let find = this.headers_opts.find((row) => {
                    return row.value == curr_field.header_code;
                });
                this.$noty.error(find.label + ' is already assigned to another line data field');
                curr_field.header_code = null;
            } else {
                if (this.match_mdl.one_amount && (field_code == 'debit' || field_code == 'credit')) {
                    let field = this.match_mdl.fields[field_code];
                    if (field_code == 'debit') this.match_mdl.fields['credit'].header_code = field.header_code;
                    else this.match_mdl.fields['debit'].header_code = field.header_code;
                }
            }
        },
        setCurrLine(index) {
            this.match_mdl.curr_line_key = index;
        },
        saveImport() {
            let elem_date = this.$filters.date2Sql(this.date);
            let lines = this.match_mdl.lines.map((row) => {
                for (var fd in this.match_mdl.fields) {
                    let hd_code = this.match_mdl.fields[fd].header_code;
                    let type = this.match_mdl.fields[fd].type;
                    if (row[hd_code] && type == 'amount') {
                        let amt = parseFloat(row[hd_code].replace(/[^0-9\.-]+/g, ''));
                        if (fd == 'debit' && amt > 0) amt = amt * -1;
                        row[hd_code] = amt;
                    }
                }
                return row;
            });
            let imports = { lines: lines, fields: this.match_mdl.fields };
            if (Object.keys(this.match_mdl.balances).length > 0) {
                let format = this.match_mdl.fields['trans_date']['format'];
                let bl_obj = {};
                for (var bldate in this.match_mdl.balances) {
                    let uq = bldate;
                    let dt = null;
                    if (format == 'dmy') dt = moment(uq, 'DD/MM/YYYY').format('YYYY-MM-DD');
                    else if (format == 'mdy') dt = moment(uq, 'MM/DD/YYYY').format('YYYY-MM-DD');
                    else if (format == 'ymd') dt = moment(uq, 'YYYY/MM/DD').format('YYYY-MM-DD');
                    bl_obj[dt] = parseFloat(this.match_mdl.balances[bldate].replace(/[^0-9\.\-]+/g, ''));
                }
                imports['balances'] = bl_obj;
            }

            let request = {
                upload_date: this.date,
                file: this.$refs['file-' + elem_date].files[0],
                bank: this.bank,
                imports: JSON.stringify(imports),
            };

            this.$api
                .post('bank-statements/file/import', this.req(request), {
                    headers: { 'Content-Type': 'multipart/form-data' },
                })
                .then((response) => {
                    if (!response.data.error) {
                        this.$emit('import', true);
                        this.import_mdl.show = false;
                        this.match_mdl.show = false;
                    }
                    // let dl = false
                    // if(response.data.success && response.data.success == true){
                    // 	dl = true
                    // }
                    // this.$refs['file-'+elem_date].value = '';
                });
        },
        openImport() {
            let elem_date = this.$filters.date2Sql(this.date);
            this.$refs['file-' + elem_date].value = '';
            this.import_mdl.file_name = '';
            this.import_mdl.has_file_header = true;
            this.import_mdl.show = true;
            //---
            this.match_mdl.headers = {};
            this.match_mdl.fields = {};
            this.match_mdl.lines = [];
            this.match_mdl.one_amount = false;
            this.match_mdl.curr_line_key = 0;
            this.accounts_not_found = [];
        },
        cancelImport() {
            this.openImport();
            this.match_mdl.show = false;
        },
        browse() {
            let elem_date = this.$filters.date2Sql(this.date);
            let elem = this.$refs['file-' + elem_date];
            elem.click();
        },
        setUpload() {
            this.import_mdl.file_name = this.$refs['file-' + this.$filters.date2Sql(this.date)].files[0].name;
        },
        setMatchBalance() {
            this.match_mdl.view = 'balances';
            let header_code = this.match_mdl.fields['trans_date']['header_code'];
            let format = this.match_mdl.fields['trans_date']['format'];
            let balance_code = this.match_mdl.fields['balance']['header_code'];

            //==================================================================
            let dates = this.match_mdl.lines.map((row) => {
                let value = row[header_code];
                // let dt = null
                // if(format == 'dmy')
                // 	dt =  moment(value, "DD/MM/YYYY").format('YYYY-MM-DD')
                // else if(format == 'mdy')
                // 	dt =  moment(value, "MM/DD/YYYY").format('YYYY-MM-DD')
                // else if(format == 'ymd')
                // 	dt =  moment(value, "YYYY/MM/DD").format('YYYY-MM-DD')
                return value;
            });
            let unique_dates = dates.filter((item, i, ar) => ar.indexOf(item) === i);
            //==================================================================
            if (unique_dates.length > 0) {
                // GET LAST BALANCE PER DATE
                let date_balances = {};
                for (var i = 0; i < unique_dates.length; i++) {
                    let udt = unique_dates[i];
                    for (var l = 0; l < this.match_mdl.lines.length; l++) {
                        let ln = this.match_mdl.lines[l];

                        if (ln[header_code] == udt) {
                            date_balances[udt] = ln[balance_code];
                        }
                    }
                }

                this.match_mdl.balances = date_balances;
                if (Object.keys(this.match_mdl.balances).length > 0) {
                    for (var bldate in this.match_mdl.balances) {
                        this.formatClosingBal(bldate);
                    }
                }

                let unq = unique_dates.map((uq) => {
                    let dt = null;
                    if (format == 'dmy') dt = moment(uq, 'DD/MM/YYYY').format('YYYY-MM-DD');
                    else if (format == 'mdy') dt = moment(uq, 'MM/DD/YYYY').format('YYYY-MM-DD');
                    else if (format == 'ymd') dt = moment(uq, 'YYYY/MM/DD').format('YYYY-MM-DD');
                    return dt;
                });

                let request = {
                    bank: this.bank,
                    trans_dates: JSON.stringify(unq),
                };
                this.$api.post('bank-statements/balances', this.req(request)).then((response) => {
                    if (response.data.closings) {
                        let closings = response.data.closings;
                    }
                    if (response.data.last_closing) {
                        this.match_mdl.last_closing_bal = response.data.last_closing;
                    }
                });
            }
        },
        handleImport() {
            let elem_date = this.$filters.date2Sql(this.date);
            let request = {
                upload_date: this.date,
                file: this.$refs['file-' + elem_date].files[0],
                bank: this.bank,
                has_header: +this.import_mdl.has_file_header,
            };
            this.$api
                .post('bank-statements/file/read', this.req(request), {
                    headers: { 'Content-Type': 'multipart/form-data' },
                })
                .then((response) => {
                    if (!response.data.error) {
                        console.log(response.data);
                        if (response.data.preview) {
                            if (response.data.fields) {
                                this.match_mdl.fields = response.data.fields;
                            }
                            if (response.data.accounts_not_found && response.data.accounts_not_found.length > 0) {
                                this.accounts_not_found = response.data.accounts_not_found;
                            }
                            if (response.data.headers) {
                                let match_headers = response.data.headers;

                                let pos = [];
                                for (var hd in match_headers) {
                                    pos.push(match_headers[hd]['pos']);
                                }
                                let max_pos = Math.max.apply(Math, pos);

                                for (var fd in this.match_mdl.fields) {
                                    if (['isreconciled', 'istakeon', 'trans_type'].indexOf(fd) !== -1) {
                                        let found = false;
                                        for (var hd in match_headers) {
                                            if (match_headers[hd]['field'] == fd) {
                                                found = true;
                                                break;
                                            }
                                        }
                                        if (!found) {
                                            max_pos += 1;
                                            this.match_mdl.fields[fd].header_code = fd;
                                            match_headers[fd] = {
                                                field: fd,
                                                label: this.match_mdl.fields[fd].label,
                                                pos: max_pos,
                                            };
                                        }
                                    }
                                }
                                this.match_mdl.headers = match_headers;
                            }
                            if (response.data.lines) {
                                this.match_mdl.lines = response.data.lines.map((row) => {
                                    for (var hd in this.match_mdl.headers) {
                                        if (
                                            this.match_mdl.fields[hd] &&
                                            this.match_mdl.fields[hd]['type'] == 'amount' &&
                                            row[hd]
                                        ) {
                                            row[hd] = this.cleanAmount(row[hd]);
                                        }
                                        if (this.match_mdl.fields[hd] && this.match_mdl.fields[hd]['type'] == 'bool') {
                                            row[hd] = !!+row[hd];
                                        }
                                        if (this.match_mdl.headers[hd]['field'] == 'trans_type') {
                                            var row_trans_type = row[hd] ? row[hd] : 'dir';
                                            row[hd] = this.$filters.strtolower(row_trans_type);
                                        }
                                    }
                                    return row;
                                });
                                // console.log('lines',this.match_mdl.lines)
                                if (this.match_mdl.lines.length > 0) this.setCurrLine(0);
                            }

                            if (response.data.one_amount && response.data.one_amount == true) {
                                this.match_mdl.one_amount = true;
                                this.tempFieldChange('debit');
                            } else this.match_mdl.one_amount = false;

                            this.match_mdl.show = true;
                            this.match_mdl.view = 'fields';
                        } else {
                            this.import_mdl.show = false;
                            this.$emit('import', true);
                        }
                    }
                    // let dl = false
                    // if(response.data.success && response.data.success == true){
                    // 	dl = true
                    // }
                    // this.$refs['file-'+elem_date].value = '';
                });
        },
        askDownload() {
            this.ask_dl_mdl.show = true;
        },
        download() {
            let request = { bank: this.bank, from: this.date, to: this.date };
            this.$api.post('bank-statements/file/download', this.req(request)).then((response) => {
                let dl = false;
                if (response.data.success) {
                    dl = true;
                }
                this.$emit('download', dl);
            });
        },
        close() {
            this.$emit('close');
        },
        load() {
            // console.log(this.bank)
            // console.log('import',this.has_import)
            // console.log('download',this.has_download)
        },
    },
    mounted() {
        this.bank = this.fileBank;
        this.has_download = this.hasDownload;
        this.has_import = this.hasImport;
        this.load();
    },
};
</script>
