<template>
    <div style="display: inline-block">
        <input
            type="file"
            name="file"
            :id="'file-csv-upload'"
            :ref="'file-csv-upload'"
            v-on:change="setUpload()"
            style="display: none"
        />
        <v-btn
            color="primary"
            tile
            small
            @click="openImport()"
            data-tooltip="Import transasction(s) from a CSV File"
            data-position="bottom right"
        >
            <v-icon
                left
                dark
                size="18"
                >mdi-upload</v-icon
            >
            Import
        </v-btn>
        <!-- IMPORT FILE MODAL -->
        <v-dialog
            v-model="import_mdl.show"
            persistent
            :max-width="import_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Import File
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="import_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div
                        class="page-form"
                        style="padding-bottom: 1.5rem"
                    >
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                class="form-label required"
                                >Select a CSV File</v-col
                            >
                            <v-col
                                xs="12"
                                sm="9"
                                class="form-input"
                            >
                                <v-btn
                                    color="primary"
                                    tile
                                    small
                                    @click="browse()"
                                    ><v-icon
                                        left
                                        dark
                                        size="18"
                                        >mdi-folder</v-icon
                                    >
                                    Browse</v-btn
                                >&nbsp;
                                <strong>{{ import_mdl.file_name }}</strong>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-btn
                        color="warning"
                        tile
                        small
                        @click="downloadTemplate()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-download</v-icon
                        >Download CSV Template</v-btn
                    >
                    <v-spacer />
                    <v-btn
                        color="success"
                        tile
                        small
                        @click="handleImport()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-eye</v-icon
                        >Import Preview</v-btn
                    >
                    <v-btn
                        color="primary"
                        tile
                        small
                        @click="import_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >Close</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
        <!-- MATCH FILE MODAL -->
        <v-dialog
            v-model="match_mdl.show"
            persistent
            :max-width="match_mdl.width"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Import Options
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="match_mdl.show = false"
                        ><v-icon>mdi-close</v-icon></a
                    >
                </v-card-title>
                <v-card-text>
                    <div class="c8-padding-1">
                        <v-row>
                            <v-col
                                xs="12"
                                sm="6"
                                style="padding-bottom: 0"
                            >
                                <div
                                    class="page-title c8-btm-space"
                                    style="padding-top: 4px"
                                >
                                    Found Bank Statement Lines ({{ match_mdl.curr_line_key + 1 }}/{{
                                        match_mdl.lines.length
                                    }})
                                </div>
                            </v-col>
                            <v-col
                                xs="12"
                                sm="6"
                                align="right"
                                style="padding-bottom: 0"
                            >
                                <v-btn
                                    depressed
                                    tile
                                    small
                                    :disabled="match_mdl.curr_line_key == 0"
                                    @click="match_mdl.curr_line_key -= 1"
                                    ><v-icon>mdi-chevron-left</v-icon> Prev</v-btn
                                >
                                <v-btn
                                    depressed
                                    tile
                                    small
                                    :disabled="match_mdl.curr_line_key >= match_mdl.lines.length - 1"
                                    @click="match_mdl.curr_line_key += 1"
                                    >Next <v-icon>mdi-chevron-right</v-icon></v-btn
                                >
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col
                                xs="12"
                                sm="12"
                            >
                                <div class="page-list">
                                    <div class="c8-page-table">
                                        <table class="bordered">
                                            <thead>
                                                <tr>
                                                    <th style="width: 150px"></th>
                                                    <th>Line Value</th>
                                                    <!-- <th>Column</th> -->
                                                    <!-- <th>Line Value Format</th> -->
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr
                                                    v-for="(field, field_code) in match_mdl.fields"
                                                    :key="'curr-line-' + field_code"
                                                >
                                                    <td
                                                        class="c8-page-table-row-header text-right"
                                                        style="width: 150px"
                                                    >
                                                        {{ field.label }}
                                                        <span
                                                            v-if="field.required"
                                                            class="required"
                                                            >*</span
                                                        >
                                                    </td>
                                                    <td>
                                                        <div
                                                            v-if="
                                                                curr_line &&
                                                                typeof curr_line[field.header_code] != 'undefined'
                                                            "
                                                        >
                                                            <span v-if="field.type == 'amount'">
                                                                <span>{{
                                                                    curr_line[field.header_code] | num2AmtStr
                                                                }}</span>
                                                            </span>
                                                            <span v-else-if="field.type == 'bool'">
                                                                {{ curr_line[field.header_code] | yesNo }}
                                                            </span>
                                                            <span v-else>{{ curr_line[field.header_code] }}</span>
                                                        </div>
                                                    </td>
                                                    <!-- <td><cirrus-single-select v-model="field.header_code" :options="headers_opts" @input="tempFieldChange(field_code)"/></td> -->
                                                    <!-- <td><cirrus-single-select v-if="field.type == 'date'" v-model="field.format" :options="match_mdl.date_formats" /></td> -->
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="success"
                        depressed
                        tile
                        small
                        @click="saveImport()"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-check</v-icon
                        >
                        Save</v-btn
                    >
                    <v-btn
                        color="primary"
                        depressed
                        tile
                        small
                        @click="match_mdl.show = false"
                        ><v-icon
                            left
                            dark
                            size="18"
                            >mdi-close</v-icon
                        >
                        Cancel</v-btn
                    >
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
export default {
    name: 'CirrusBankStatementCsvUpload',
    props: {
        bank: { type: String, default: {} },
    },
    data() {
        return {
            import_mdl: {
                show: false,
                width: 650,
                has_file_header: true,
                file_types: ['CSV'],
                file_name: '',
            },
            match_mdl: {
                width: 650,
                show: false,
                headers: {},
                fields: {},
                lines: [],
                one_amount: false,
                curr_line_key: 0,
                date_formats: [
                    { label: 'Date/Month/Year', value: 'dmy' },
                    { label: 'Month/Date/Year', value: 'mdy' },
                    { label: 'Year/Month/Date', value: 'ymd' },
                ],
            },
        };
    },
    computed: {
        curr_line() {
            let data = {};
            if (
                this.match_mdl.lines &&
                this.match_mdl.lines.length > 0 &&
                this.match_mdl.lines[this.match_mdl.curr_line_key]
            ) {
                data = this.match_mdl.lines[this.match_mdl.curr_line_key];
            }
            return data;
        },
        headers_opts() {
            let data = [];
            if (this.match_mdl.headers) {
                for (var hd_code in this.match_mdl.headers) {
                    data.push({ label: this.match_mdl.headers[hd_code].label, value: hd_code });
                }
            }
            return data;
        },
    },
    methods: {
        drCrNum(type, amount) {
            if (type == 'debit') {
                if (parseFloat(amount) > 0) return null;
                else return amount;
            } else if (type == 'credit') {
                if (parseFloat(amount) < 0) {
                    return null;
                } else return amount;
            }
        },
        tempFieldChange(field_code) {
            if (this.match_mdl.one_amount && (field_code == 'debit' || field_code == 'credit')) {
                let field = this.match_mdl.fields[field_code];
                if (field_code == 'debit') this.match_mdl.fields['credit'].header_code = field.header_code;
                else this.match_mdl.fields['debit'].header_code = field.header_code;
            }
        },
        setCurrLine(index) {
            this.match_mdl.curr_line_key = index;
        },
        saveImport() {
            let elem_date = this.$filters.date2Sql(this.date);
            let request = {
                file: this.$refs['file-csv-upload'].files[0],
                bank_code: this.bank,
                imports: JSON.stringify({ lines: this.match_mdl.lines, fields: this.match_mdl.fields }),
            };
            this.$api
                .post('bank-statements/file/bank_statement_report_csv/import', this.req(request), {
                    headers: { 'Content-Type': 'multipart/form-data' },
                })
                .then((response) => {
                    if (!response.data.error) {
                        this.$emit('import', true);
                        this.import_mdl.show = false;
                        this.match_mdl.show = false;
                    }
                    // let dl = false
                    // if(response.data.success && response.data.success == true){
                    // 	dl = true
                    // }
                    // this.$refs['file-'+elem_date].value = '';
                });
        },
        handleImport() {
            let request = {
                // upload_date : this.date,
                file: this.$refs['file-csv-upload'].files[0],
                // bank 		: this.bank,
                // has_header 	: +this.import_mdl.has_file_header,
            };
            this.$api
                .post('bank-statements/file/bank_statement_report_csv/read', this.req(request), {
                    headers: { 'Content-Type': 'multipart/form-data' },
                })
                .then((response) => {
                    if (!response.data.error) {
                        console.log(response.data);
                        if (response.data.preview) {
                            if (response.data.headers) this.match_mdl.headers = response.data.headers;
                            if (response.data.lines) {
                                this.match_mdl.lines = response.data.lines;
                                this.setCurrLine(0);
                            }
                            if (response.data.fields) {
                                this.match_mdl.fields = response.data.fields;
                            }
                            if (response.data.one_amount && response.data.one_amount == true) {
                                this.match_mdl.one_amount = true;
                                this.tempFieldChange('debit');
                            } else this.match_mdl.one_amount = false;
                            this.match_mdl.show = true;
                        } else {
                            this.import_mdl.show = false;
                            this.$emit('import', true);
                        }
                    }
                });
        },
        downloadTemplate() {
            this.$api
                .get('bank-statements/template/bank_statement_report_csv', this.req({ no_load: false }))
                .then((response) => {
                    this.printDownload(response.data.file, 'bank_statement_report_csv', 'csv');
                });
        },
        openImport() {
            let elem_date = this.$filters.date2Sql(this.date);
            this.$refs['file-csv-upload'].value = '';
            this.import_mdl.file_name = '';
            this.import_mdl.has_file_header = true;
            this.import_mdl.show = true;
            //---
            // this.match_mdl.headers = {}
            // this.match_mdl.fields = {}
            // this.match_mdl.lines = []
            // this.match_mdl.one_amount = false
            // this.match_mdl.curr_line_key = 0
        },
        setUpload() {
            this.import_mdl.file_name = this.$refs['file-csv-upload'].files[0].name;
        },
        browse() {
            let elem = this.$refs['file-csv-upload'];
            elem.click();
        },
    },
    mounted() {},
};
</script>
