<template>
    <div>
        <v-btn-toggle
            :value="radioModel"
            mandatory
            v-on:input="updateValue($event.target.value)"
        >
            <v-btn
                small
                text
                :disabled="!edit_form"
                v-for="(optionData, index) in options"
                :key="index"
            >
                {{ optionData.fieldValue }}
            </v-btn>
        </v-btn-toggle>
    </div>
</template>
<script>
export default {
    props: {
        value: { type: String, default: '' },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        options: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        size: { type: String, default: '60' },
        id: { type: String, default: '60' },
        withKey: { type: Boolean, default: true },
        defaultValue: { type: Number, default: 0 },
        edit_form: { type: Boolean, default: false },
    },
    data() {
        return {
            radioModel: 0,
        };
    },
    mounted() {
        for (let x = 0; x <= this.options.length - 1; x++) {
            if (this.options[x].fieldKey === this.value) {
                this.radioModel = x;
            }
        }
    },
    methods: {
        updateValue(value) {
            console.log(value);
            this.$emit('input', value);
        },
    },
    watch: {
        value: function () {
            for (let x = 0; x <= this.options.length - 1; x++) {
                if (this.options[x].fieldKey === this.value) {
                    this.radioModel = x;
                }
            }
        },
    },
};
</script>
