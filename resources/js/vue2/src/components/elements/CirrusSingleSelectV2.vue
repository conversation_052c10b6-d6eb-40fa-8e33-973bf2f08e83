<template>
    <div :id="elementID">
        <multiselect
            :class="customClass"
            :placeholder="placeholder"
            :deselectLabel="deselectLabel"
            :selectLabel="selectLabel"
            v-model="select_val"
            :track-by="trackBy"
            :label="label"
            :options="options"
            @input="changed()"
            :show-labels="hasEmpty"
            :allow-empty="hasEmpty"
            :options-limit="optionsLimit"
            :custom-label="customLabel ?? undefined"
        />
    </div>
</template>
<script>
export default {
    name: 'Cirrus8SingleSelect',
    props: {
        value: { default: null },
        options: { default: [] },
        optionsLimit: { default: 10000 },
        hasEmpty: { default: true },
        placeholder: { default: 'Select an Item' },
        deselectLabel: { default: 'Select to remove' },
        selectLabel: { default: '' },
        return: { default: 'field_key' },
        trackBy: { default: 'field_key' },
        label: { default: 'field_value' },
        elementID: { default: 'select' + (Math.random() + 1).toString(36).substring(7) },
        customClass: { default: '' },
        customLabel: { default: null },
    },
    data() {
        return {
            select_val: null,
        };
    },
    watch: {
        value(newVal, oldVal) {
            if (newVal != oldVal) {
                if (newVal && newVal != null) {
                    let find_val = this.options.filter((row) => {
                        return eval('row.' + this.trackBy) == newVal;
                    });
                    if (find_val && find_val.length > 0) this.select_val = find_val[0];
                } else {
                    this.select_val = null;
                }
            }
        },
        options() {
            this.setDefaultWhenOptionsChange();
        },
    },
    methods: {
        changed() {
            if (this.return == 'all') {
                this.$emit('input', this.select_val);
            } else {
                if (this.select_val && this.select_val[this.return]) this.$emit('input', this.select_val[this.return]);
                else this.$emit('input', null);

                setTimeout(() => this.scroll_to_selected(), 40);
            }
        },
        scroll_to_selected() {
            var selected = document.querySelector('#' + this.elementID + ' .multiselect__option--selected');

            if (selected) {
                var container = selected.closest('div.multiselect__content-wrapper');
                container.scrollTop = selected.offsetTop;
            }
        },
        setDefaultWhenOptionsChange() {
            if (this.value && this.value != null) {
                let find_val = this.options.filter((row) => {
                    return eval('row.' + this.trackBy) == this.value;
                });
                if (find_val && find_val.length > 0) this.select_val = find_val[0];
            }
        },
    },
    mounted() {
        this.setDefaultWhenOptionsChange();
    },
};
</script>
