<template>
    <div>
        <div class="page-header">
            <div class="page-title">{{ title }}</div>
            <div
                class="page-subtitle pt-1"
                v-if="subtitle"
            >
                <b>{{ subtitle }}</b>
            </div>
        </div>
        <div
            class="page-noty"
            v-if="alerts && alerts.length > 0"
        >
            <div
                :class="alert.type + '-box'"
                v-for="(alert, index) in alerts"
                :key="index"
                v-html="alert.content"
            />
        </div>
    </div>
</template>
<script>
export default {
    name: 'Cirrus8PageHeader',
    props: {
        title: { type: String, default: '' },
        subtitle: { type: String, default: '' },
        alerts: { type: Array, default: () => [] },
    },
};
</script>
