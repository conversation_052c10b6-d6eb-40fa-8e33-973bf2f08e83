<template>
    <div class="c8-dp">
        <div v-show="edit_form">
            <input
                :tabindex="tabindex"
                class="c8-dp-input"
                :value="date_str"
                @blur="blurred"
                v-if="!iconOnly"
                v-on:keypress="validateInputDate($event)"
            />
            <div
                v-click-outside="closePicker"
                :id="id + '-dp-container'"
                style="display: inline-block"
                :data-blur-event="blurEvent"
            >
                <img
                    @click="openPicker"
                    :class="{ disabled: disabled }"
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAfdJREFUeNqckc9LVFEUx7/3vvfmMVpiNqsKU8QYArcFgbUIhShSGSYoEAnSTcv6CwJ30qrACtu0EByCVmFqi2nm5Y+FDjqOksoEzkYUlBDGmffjeu69k1FYTB44fDj38r3ne85ls6NPxZXddwA4JUN1ISgDzJ3pBRPD5wRuvqQ6+D89p4afH8OEQcX2PLD6vuKAVaGmjMYgteze2KZ4E7+AvKuvWRUuBOmbLaA/sQlulPcQpsNC2kEdudqadnDWBHZmHESO4fZ0GvXUpIY0JmnNsueqKQxaSt5JYXghAiuTIycNEJL4nUI0oDS3hA9P2lD2PPCS76v919XauHa9nchxN3YZp2v+zjuxNjXKge+BdbyaERMDV9H3dg02WQwZlVUyPeuflFl0fYw8jKLz9ax0EKjXToUZ4rdbYYc4um+1wrJ+sYto/qS6j2oHXgB+4PmqsG0DU+k8QjbHVEpzkmiRpfEv38EsAx+TmuOpjaMRjh6QHR50NMMwOe53asoaxKHuJjy6tI/nPU3oJ76It+gHXHJQ9PQIJnVMOPSv1DHhFDS/FsCI2ewiSr7AwuKSYiaTURqlvTg4KWSsr+aO5bcVzeXcimJ2WdcyGkmL84MT4qQhtSYr/khGnn264QaVP1Kf+C/qP7Y4Q7i8n5SnciP1OFnsHQowADLsOuq1c9H6AAAAAElFTkSuQmCC"
                />
                <datepicker
                    v-model="date"
                    :ref="id + '-dp'"
                    @input="updateDate(id)"
                ></datepicker>
            </div>
        </div>
        <div v-show="!edit_form">
            <span class="form-input-text">{{ value }}</span>
        </div>
    </div>
</template>
<script>
import Datepicker from 'vuejs-datepicker';
import moment from 'moment';

export default {
    name: 'Cirrus8PageHeader',
    components: {
        Datepicker,
    },
    props: {
        value: { type: String, default: null },
        tabindex: { type: String, default: '0' },
        id: { type: String, default: null },
        blurEvent: { type: String, default: null },
        iconOnly: { type: Boolean, default: false },
        doNotSet: { type: Boolean, default: false },
        disabled: { type: Boolean, default: false },
        edit_form: { type: Boolean, default: true },
    },
    data() {
        return {
            date: new Date(),
            date_str: '',
            opened: false,
        };
    },
    methods: {
        openPicker() {
            if (!this.disabled) {
                this.$refs[this.id + '-dp'].showCalendar();
                this.opened = true;
                let dp = document.querySelectorAll('#' + this.id + '-dp-container')[0];
                let pos = this.findPos(dp);

                if (!isNaN(pos.left) && window.innerHeight - parseFloat(pos.top) <= 350) {
                    dp.querySelectorAll('.vdp-datepicker__calendar')[0].style.top = parseFloat(pos.top) - 280 + 'px';
                } else {
                    dp.querySelectorAll('.vdp-datepicker__calendar')[0].style.top = pos.top + 15 + 'px';
                }

                if (!isNaN(pos.left) && window.innerWidth - parseFloat(pos.left) <= 350) {
                    dp.querySelectorAll('.vdp-datepicker__calendar')[0].style.left = parseFloat(pos.left) - 500 + 'px';
                } else {
                    dp.querySelectorAll('.vdp-datepicker__calendar')[0].style.left = pos.left + 5 + 'px';
                }
            }
        },
        findPos(element) {
            var top = 0,
                left = 0;
            do {
                top += element.offsetTop - element.scrollTop || 0;
                left += element.offsetLeft - element.scrollLeft || 0;
                element = element.offsetParent;
            } while (element);
            return {
                top: top,
                left: left,
            };
        },
        blurred(event) {
            this.updateDate(this.id, event.target.value);
        },
        changeDate(value) {
            this.updateDate(this.id, value);
        },
        updateDate(id, value) {
            let ref_name = id + '-dp';
            let val = this.date;

            if (typeof value != 'undefined') {
                if (value != '' && value != null) this.date = moment(value, 'DD/MM/YYYY').toDate();
                else this.date = null;
            }

            if (this.date) this.date_str = moment(Date.parse(this.date)).format('DD/MM/YYYY');
            else {
                this.date_str = null;
            }
            if (this.date_str === 'Invalid date') {
                this.date = moment().toDate();
                this.date_str = moment(Date.parse(this.date)).format('DD/MM/YYYY');
            }
            this.$emit('input', this.date_str);
            this.opened = false;

            let blurEvent = $('#' + ref_name + '-container').data('blur-event');
            if (blurEvent !== undefined) {
                switch (blurEvent) {
                    case 'quick-payment':
                        if (!this.date_str) {
                            this.$parent.runDate = null;
                        } else {
                            const [day, month, year] = this.date_str.split('/');
                            this.$parent.runDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                        }
                        break;
                }
            }
        },
        closePicker() {
            // console.log(this.opened)
            // if(this.opened ){
            //     console.log(this.opened = false)
            // }
            this.$refs[this.id + '-dp'].close();
            // console.log(this.$refs[this.id+'-dp'])
        },
        validateInputDate: function (event) {
            // let charKeyCode = e.which || e.keyCode;
            // evt = (event) ? event.keyCode : window.event.keyCode;

            event = event ? event : window.event;
            let charCode = event.which ? event.which : event.keyCode;
            //(charCode);

            if (
                $.inArray(charCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
                // Allow: Ctrl/cmd+A
                (charCode == 65 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: Ctrl/cmd+C
                (charCode == 67 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: Ctrl/cmd+X
                (charCode == 88 && (event.ctrlKey === true || event.metaKey === true)) ||
                // Allow: - /
                charCode == 45 ||
                charCode == 47 ||
                charCode == 99 ||
                charCode == 118 ||
                // Allow: home, end, left, right
                (charCode >= 38 && charCode <= 39)
            ) {
                // let it happen, don't do anything
                if (charCode == 13) {
                    // //(compo);
                    // compo = eval(compo);
                    // this.budgetExpensesArr[compo].value = eval(this.budgetExpensesArr[compo].value);
                    // $(compo).val(eval(e.target.defaultValue));
                    event.target.value = this.roundTo(eval(event.target.value), 2);
                }
                return;
            }

            if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
                event.preventDefault();
            }
        },
    },
    mounted() {
        if (!this.doNotSet && this.value !== '' && this.value != null) this.updateDate(this.id, this.value);
    },
    directives: {
        'click-outside': {
            bind: function (el, binding, vNode) {
                // Provided expression must evaluate to a function.
                if (typeof binding.value !== 'function') {
                    const compName = vNode.context.name;
                    let warn = `[Vue-click-outside:] provided expression '${binding.expression}' is not a function, but has to be`;
                    if (compName) {
                        warn += `Found in component '${compName}'`;
                    }

                    console.warn(warn);
                }
                // Define Handler and cache it on the element
                const bubble = binding.modifiers.bubble;
                const handler = (e) => {
                    if (bubble || (!el.contains(e.target) && el !== e.target)) {
                        binding.value(e);
                    }
                };
                el.__vueClickOutside__ = handler;
                // add Event Listeners
                document.addEventListener('click', handler);
            },
            unbind: function (el, binding) {
                // Remove Event Listeners
                document.removeEventListener('click', el.__vueClickOutside__);
                el.__vueClickOutside__ = null;
            },
        },
    },
    watch: {
        value(newVal, oldVal) {
            if (!this.doNotSet && this.value !== '' && this.value != null && newVal !== oldVal) {
                this.updateDate(this.id, this.value); // this is for changing the value after loading the component
            }
        },
    },
};
</script>
