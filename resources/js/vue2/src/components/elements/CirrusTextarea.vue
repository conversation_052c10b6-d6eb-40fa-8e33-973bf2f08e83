<template>
    <div>
        <textarea
            :tabindex="tabindex"
            :id="id"
            :value="value"
            :rows="rows"
            :cols="cols"
            :maxlength="maxlength"
            v-if="edit_form"
            v-on:input="updateValue($event.target.value)"
        ></textarea>
        <span
            class="form-input-text"
            v-if="!edit_form"
            >{{ value }}</span
        >
        <v-chip
            v-if="error_msg.length > 0 && errorData.id === id"
            v-for="(errorData, index) in error_msg"
            :key="index"
            outlined
            color="error"
        >
            <v-icon left>error</v-icon>{{ errorData.message }}
        </v-chip>
    </div>
</template>
<script>
export default {
    props: {
        value: { type: String, default: '' },
        tabindex: { type: String, default: '0' },
        placeholder: { type: String, default: '' },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        rows: { type: String, default: '60' },
        cols: { type: String, default: '60' },
        id: { type: String, default: '60' },
        maxlength: { type: String, default: '10000' },
        withKey: { type: Boolean, default: true },
        edit_form: { type: Boolean, default: false },
    },
    data() {
        return {
            cirrusInput: this.value,
        };
    },
    methods: {
        updateValue(value) {
            this.$emit('input', value);
        },
    },
    watch: {
        cirrusInput: function () {
            //console.log();
            this.$emit('input', this.value);
        },
    },
};
</script>
