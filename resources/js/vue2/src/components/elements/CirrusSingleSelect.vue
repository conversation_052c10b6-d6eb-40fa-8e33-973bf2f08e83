<template>
    <div :id="elementID">
        <multiselect
            :placeholder="placeholder"
            :deselectLabel="deselectLabel"
            :selectLabel="selectLabel"
            v-model="select_val"
            :track-by="trackBy"
            :label="label"
            :options="options"
            @input="changed()"
            :show-labels="hasEmpty"
            :allow-empty="hasEmpty"
            :options-limit="optionsLimit"
        />
    </div>
</template>
<script>
export default {
    name: 'Cirrus8SingleSelect',
    props: {
        value: { default: null },
        options: { default: [] },
        optionsLimit: { default: 10000 },
        hasEmpty: { default: true },
        placeholder: { default: 'Select an Item' },
        deselectLabel: { default: 'Select to remove' },
        selectLabel: { default: '' },
        return: { default: 'value' },
        trackBy: { default: 'value' },
        label: { default: 'label' },
        elementID: { default: 'select' + (Math.random() + 1).toString(36).substring(7) },
    },
    data() {
        return {
            select_val: null,
        };
    },
    watch: {
        value(newVal, oldVal) {
            if (newVal != oldVal) {
                if (newVal && newVal != null) {
                    let find_val = this.options.filter((row) => {
                        return row.value == newVal;
                    });
                    if (find_val && find_val.length > 0) this.select_val = find_val[0];
                } else {
                    this.select_val = null;
                }
            }
        },
    },
    methods: {
        changed() {
            if (this.return == 'all') {
                this.$emit('input', this.select_val);
            } else {
                if (this.select_val && this.select_val[this.return]) this.$emit('input', this.select_val[this.return]);
                else this.$emit('input', null);

                setTimeout(() => this.scroll_to_selected(), 40);
            }
        },
        scroll_to_selected() {
            var selected = document.querySelector('#' + this.elementID + ' .multiselect__option--selected');

            if (selected) {
                var container = selected.closest('div.multiselect__content-wrapper');
                container.scrollTop = selected.offsetTop;
            }
        },
    },
    mounted() {
        if (this.value && this.value != null) {
            let find_val = this.options.filter((row) => {
                return row.value == this.value;
            });
            if (find_val && find_val.length > 0) this.select_val = find_val[0];
        }
    },
};
</script>
