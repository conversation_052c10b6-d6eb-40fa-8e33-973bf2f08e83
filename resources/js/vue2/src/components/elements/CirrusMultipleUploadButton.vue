<template>
    <span>
        <div v-if="edit_form">
            <div v-if="lc_has_saved_file === false">
                <div class="upload-btn-wrapper">
                    <!--<v-btn :for="id" small color="#939496" dark>Attachment(s)</v-btn>-->
                    <label
                        :for="id"
                        class="upload-btn-label right v-btn v-btn--contained theme--light v-size--default primary v-btn--small"
                        style="margin-right: 0px"
                    >
                        <v-icon dark>cloud_upload</v-icon> &nbsp
                        {{ file_upload_button_name }}
                    </label>
                    <label
                        v-if="file_upload_button_name !== 'Attachment(s)'"
                        @click="removeAttachedFile()"
                        class="v-btn v-btn--flat v-btn--icon v-btn--small theme--light"
                        style="margin-left: 0px"
                    >
                        <v-icon color="red">close</v-icon>
                    </label>
                    <input
                        :id="id"
                        type="file"
                        v-on:change="fileSelected()"
                        ref="myFiles"
                        v-on:input="updateValue($event.target.value)"
                        :accept="accept_type_list[accept_type]"
                        multiple
                    />
                </div>
                <v-chip
                    v-if="error_msg.length > 0 && errorData.id === id"
                    v-for="(errorData, index) in error_msg"
                    :key="index"
                    outlined
                    color="error"
                >
                    <v-icon left>error</v-icon>{{ errorData.message }}
                </v-chip>
            </div>
        </div>
        <div v-if="!edit_form && lc_has_saved_file === false">No Attachment</div>

        <div
            v-if="lc_has_saved_file === true"
            v-for="(value_data, value_index) in value"
            :key="value_index"
        >
            <a :href="href(value_data)"
                ><img
                    :src="this.$assetDomain + 'assets/images/icons/pdf.png'"
                    alt="Adobe Logo"
                    class="icon"
                />&nbsp; Download</a
            >
        </div>
    </span>
</template>
<script>
export default {
    props: {
        value: {},
        placeholder: { type: String, default: '' },
        has_saved_file: { type: Boolean, default: false },
        class1: {
            type: Object,
            default: function () {
                return {};
            },
        },
        error_msg: {
            type: Array,
            default: function () {
                return [];
            },
        },
        debug: { type: Boolean, default: false },
        size: { type: String, default: '20971520' },
        max_length: { type: String, default: '60' },
        id: { type: String, default: '' },
        with_key: { type: Boolean, default: true },
        edit_form: { type: Boolean, default: false },
        accept_type: { type: String, default: '*' },
    },
    data() {
        return {
            cirrus_input: this.value,
            file_holder: null,
            file_upload_button_name: 'Attachment(s)',
            accept_type_list: {
                csv: '.csv',
                excel: 'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                text: 'text/plain',
                image: 'image/*',
                pdf: '.pdf',
            },
            lc_has_saved_file: this.has_saved_file,
        };
    },
    computed: {
        href: function (param) {
            return 'download.php?fileID=' + param;
        },
    },
    methods: {
        updateValue(value) {
            this.$emit('input', value);
        },
        fileSelected: function () {
            this.file_holder = this.$refs.myFiles.files;
            for (let x = 0; x <= this.$refs.myFiles.files.length - 1; x++) {
                let size_bytes = this.$refs.myFiles.files[x].size;
                if (size_bytes > this.size)
                    return alert(
                        'Unable to upload file because the size exceeds the maximum allowed file size of ' +
                            Math.ceil(eval(this.size) / (1024 * 1024)) +
                            'MB.',
                    );
            }
            this.$emit('input', this.file_holder);
            let fileList = this.$refs.myFiles.files;
            this.file_upload_button_name = fileList.length + ' Attachment(s)';
        },
        removeAttachedFile: function () {
            this.file_upload_button_name = 'Attachment(s)';
            this.file_holder = null;
            this.$refs.myFiles.value = null;
            this.$emit('input', this.file_holder);
        },
    },
    watch: {
        cirrus_input: function () {
            if (this.file_holder === null) {
                this.removeAttachedFile();
            }
            this.$emit('input', this.file_holder);
        },
        value: function () {
            if (this.value === null) {
                this.removeAttachedFile();
            }
        },
    },
};
</script>
<style>
.upload-btn-label {
    background: #939496 !important;
    color: white !important;
    font-weight: bolder;
}
.upload-btn-wrapper input[type='file'] {
    display: none;
}
</style>
