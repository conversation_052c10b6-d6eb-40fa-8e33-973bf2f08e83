<template>
    <li>
        <div
            class="node"
            :class="{
                selected: selected.includes(node.id),
                'drag-over': isDraggedOver,
            }"
            @click="selectNode($event)"
            @dragstart="onDragStart"
            @dragover.prevent="onDragOver"
            @dragenter="onDragEnter"
            @dragleave="onDragLeave"
            @drop="onDrop"
            draggable
            @dblclick.stop="toggleExpand"
        >
            <span
                class="d-flex align-items-center"
                :id="'tooltip_att' + node.id"
            >
                <span>
                    <span v-if="node.type === 'root'">
                        <v-icon>mdi-folder-home-outline</v-icon>
                    </span>
                    <span
                        v-else-if="node.type === 'directory'"
                        @click.stop="toggleExpand"
                    >
                        <v-badge
                            color="file-browser-badge grey"
                            bottom
                            overlap
                            bordered
                            :content="node.children ? node.children.length : 0"
                            :value="!!(node.children && node.children.length > 0)"
                        >
                            <v-icon :color="node.isArchived === notArchived ? '' : 'subPrimary'">{{
                                isExpanded ? 'mdi-folder-open' : 'mdi-folder'
                            }}</v-icon>
                        </v-badge>
                    </span>
                    <span v-else>
                        <v-icon :color="node.isArchived === notArchived ? '' : 'subPrimary'">
                            {{ getFileIcon }}
                        </v-icon>
                    </span>
                </span>

                <span v-if="node.fileType === 'root'"> {{ node.name }} </span>
                <span v-else-if="!node.fileType"> {{ node.name }} </span>
                <span v-else>
                    {{ node.itemNo }}
                    <a
                        v-if="node.leaseCode"
                        @click="goToShortcut(propertyModuleName, node.propertyCode)"
                        >[{{ node.propertyCode }}]</a
                    >:
                    <a
                        :href="node.fileType === 'file' ? returnValue(node) : node.externalUrl"
                        target="_blank"
                        v-if="!node.onS3"
                    >
                        {{ node.documentTitle }} -
                        {{ node.documentDescription }}
                    </a>
                    <a
                        target="_blank"
                        v-else
                        v-on:click="downloadS3File(node)"
                    >
                        {{ node.documentTitle }} -
                        {{ node.documentDescription }}
                    </a>

                    <v-chip
                        x-small
                        dense
                        v-if="node.fileType && node.documentPeriodDate"
                    >
                        Period Date: {{ node.documentPeriodDate }}
                    </v-chip>
                    <v-chip
                        x-small
                        dense
                        v-if="node.fileType && node.documentPeriodFrom"
                    >
                        Reporting Period: {{ node.documentPeriodFrom }} -
                        {{ node.documentPeriodTo }}
                    </v-chip>
                </span>
                <span
                    v-show="!node.fileType && node.folderModule !== null"
                    style="color: #777777; font-weight: bolder; padding-left: 3px; font-size: 8px"
                    >(protected)</span
                >
                <v-spacer></v-spacer>
                <span v-if="node.fileType && node.propertyCode">
                    <a
                        :data-tooltip="node.publishToOwner ? 'published to owner' : 'not published to owner'"
                        :data-position="edit_form ? 'top center' : 'left center'"
                    >
                        <v-icon
                            color="info"
                            small
                            :disabled="!(node.fileType && node.publishToOwner)"
                        >
                            mdi-office-building
                        </v-icon>
                    </a>
                </span>

                <span v-if="node.fileType && node.propertyCode && !node.leaseCode">
                    <a
                        :data-tooltip="
                            node.includeOwnerReport ? 'included to owner report' : 'excluded to owner report'
                        "
                        :data-position="edit_form ? 'top center' : 'left center'"
                    >
                        <v-icon
                            color="info"
                            small
                            :disabled="!(node.fileType && node.includeOwnerReport)"
                        >
                            mdi-chart-bar
                        </v-icon>
                    </a>
                </span>

                <span v-if="node.fileType && node.leaseCode">
                    <a
                        :data-tooltip="node.publishToTenant ? 'published to tenant' : 'not published to tenant'"
                        :data-position="edit_form ? 'top center' : 'left center'"
                    >
                        <v-icon
                            color="info"
                            small
                            :disabled="!(node.fileType && node.publishToTenant)"
                        >
                            mdi-chart-bar
                        </v-icon>
                    </a>
                </span>

                <span v-if="edit_form && node.type !== 'root'">
                    <a
                        data-tooltip="Rename folder name"
                        :data-position="'left center'"
                    >
                        <v-icon
                            color="primary"
                            small
                            v-if="!node.fileType && node.isArchived === notArchived && node.folderModule === null"
                            @click="openUpdateModal(node)"
                        >
                            fas fa-edit
                        </v-icon>
                    </a>
                    <a
                        data-tooltip="Edit Document"
                        :data-position="'left center'"
                    >
                        <v-icon
                            color="primary"
                            small
                            v-if="node.fileType"
                            @click="openUpdateDocumentModal(node)"
                        >
                            fas fa-edit
                        </v-icon>
                    </a>
                </span>

                <span v-if="node.fileType && node.type !== 'root'">
                    <v-icon
                        small
                        color="primary"
                        v-if="!node.fileType"
                        @click="downloadZipFolder(node.directoryId)"
                    >
                        mdi-download
                    </v-icon>
                    <a
                        data-tooltip="Download"
                        :data-position="'left center'"
                        :href="returnValue(node)"
                        v-if="node.fileType === 'file' && !node.onS3"
                    >
                        <v-icon
                            small
                            color="primary"
                        >
                            mdi-download
                        </v-icon>
                    </a>
                    <a
                        target="_blank"
                        data-tooltip="Download"
                        :data-position="'left center'"
                        v-if="node.fileType === 'file' && node.onS3"
                        v-on:click="downloadS3File(node)"
                    >
                        <v-icon
                            small
                            color="primary"
                        >
                            mdi-download
                        </v-icon>
                    </a>
                    <a
                        data-tooltip="Open Link"
                        :data-position="'left center'"
                        :href="node.externalUrl"
                        target="_blank"
                        v-if="node.fileType === 'link'"
                    >
                        <v-icon
                            color="primary"
                            small
                        >
                            mdi-link
                        </v-icon>
                    </a>
                </span>

                <span v-if="edit_form && node.type !== 'root'">
                    <a
                        data-tooltip="Delete Document"
                        :data-position="'left center'"
                    >
                        <v-icon
                            small
                            v-if="node.fileType && node.isArchived === notArchived"
                            color="red"
                            @click="deleteDocuments(node)"
                        >
                            mdi-close
                        </v-icon>
                    </a>
                    <a
                        data-tooltip="Delete folder"
                        :data-position="'left center'"
                    >
                        <v-icon
                            small
                            v-if="!node.fileType && node.isArchived === notArchived"
                            color="red"
                            @click="deleteFolder(node)"
                            :disabled="node.folderModule !== null"
                        >
                            mdi-close
                        </v-icon>
                    </a>
                    <a
                        data-tooltip="Remove from archive"
                        :data-position="'left center'"
                        v-if="!node.fileType && node.isArchived === isArchived && node.folderModule === null"
                        ><v-icon
                            color="subPrimary"
                            small
                            @click="archiveFolderById(node, notArchived)"
                            >{{ 'mdi-archive-remove' }}</v-icon
                        ></a
                    >
                    <a
                        data-tooltip="Remove from archive"
                        :data-position="'left center'"
                        v-if="node.fileType && node.isArchived === isArchived"
                        ><v-icon
                            color="subPrimary"
                            small
                            @click="archiveDocumentById(node, notArchived)"
                            >{{ 'mdi-archive-remove' }}</v-icon
                        ></a
                    >
                </span>
            </span>
        </div>
        <ul v-if="node.children && node.children.length > 0 && isExpanded">
            <file-node
                v-for="child in node.children"
                :key="child.id"
                :node="child"
                :selected="selected"
                :level="level + 1"
                :edit_form="edit_form"
                :is_live="is_live"
                :property_code="property_code"
                :lease_code="lease_code"
                :company_code="company_code"
                @update:selected="$emit('update:selected', $event)"
                @update:documentData="$emit('update:documentData', $event)"
                @update:directoryData="$emit('update:directoryData', $event)"
                @update:delDocumentData="$emit('update:delDocumentData', $event)"
                @update:delDirectoryData="$emit('update:delDirectoryData', $event)"
                @update:archiveDirectoryData="$emit('update:archiveDirectoryData', $event)"
                @update:archiveDocumentData="$emit('update:archiveDocumentData', $event)"
                @drag-event="$emit('drag-event', $event)"
            ></file-node>
        </ul>
    </li>
</template>

<script>
import Vue from 'vue';
import { COMPANY_MODULE_NAME, IS_ARCHIVED, LEASE_MODULE_NAME, NOT_ARCHIVED, PROPERTY_MODULE_NAME } from '../constants';

export default {
    name: 'FileNode',
    props: {
        node: {
            type: Object,
            required: true,
        },
        selected: {
            type: Array,
            required: true,
        },
        level: {
            type: Number,
            default: 0,
        },
        is_live: {
            type: Boolean,
            default: false,
        },
        edit_form: {
            type: Boolean,
            default: false,
        },
        property_code: {
            type: String,
            default: '',
        },
        lease_code: {
            type: String,
            default: '',
        },
        company_code: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            isExpanded: false, // State for folder expansion
            isDraggedOver: false, // State to track if current node is being dragged over
            files: {
                html: 'mdi-language-html5',
                js: 'mdi-nodejs',
                json: 'mdi-code-json',
                md: 'mdi-language-markdown',
                pdf: 'mdi-file-pdf-box',
                png: 'mdi-file-image',
                jpg: 'mdi-file-image',
                txt: 'mdi-file-document-outline',
                xls: 'mdi-file-excel',
                xlsx: 'mdi-file-excel',
                link: 'mdi-link-box-variant-outline',
                zip: 'mdi-folder-zip',
            },
            isArchived: IS_ARCHIVED,
            notArchived: NOT_ARCHIVED,
            propertyModuleName: PROPERTY_MODULE_NAME,
            leaseModuleName: LEASE_MODULE_NAME,
            companyModuleName: COMPANY_MODULE_NAME,
        };
    },
    methods: {
        /**
         * Toggle expand/collapse for folders.
         */
        toggleExpand() {
            this.isExpanded = !this.isExpanded;
        },

        /**
         * Handles selection of a node.
         * Single select by default, multi-select with Shift key.
         */
        selectNode(event) {
            if (event.shiftKey) {
                // Toggle the node in the selection
                if (this.selected.includes(this.node.id)) {
                    // If already selected, remove it
                    const updatedSelection = this.selected.filter((id) => id !== this.node.id);
                    this.$emit('update:selected', updatedSelection);
                } else {
                    // If not selected, add it
                    this.$emit('update:selected', Array.from(new Set([...this.selected, this.node.id])));
                }
            } else {
                // Single selection: toggle selection
                if (this.selected.includes(this.node.id)) {
                    // If already selected, deselect it
                    this.$emit('update:selected', []);
                } else {
                    // If not selected, select only this node
                    this.$emit('update:selected', [this.node.id]);
                }
            }
        },

        /**
         * Drag functionality for nodes.
         */
        onDragStart(event) {
            if (!this.edit_form) event.preventDefault();
            if (this.node.is_system === '1') event.preventDefault();
            if (this.node.type === 'root') event.preventDefault();
            // Pass all selected nodes into the drag event
            const selected_nodes = this.selected.includes(this.node.id)
                ? this.selected // Drag all selected nodes
                : [this.node.id]; // Drag only the current node if it's not selected

            event.dataTransfer.setData('text/plain', JSON.stringify(selected_nodes));
            event.dataTransfer.effectAllowed = 'move';

            // Optional: Add visual feedback for nodes being dragged
            event.target.classList.add('dragging');
        },
        onDragEnter(event) {
            this.isDraggedOver = true; // Enable highlight
        },
        onDragOver(event) {
            event.preventDefault(); // Allow drop
            this.isDraggedOver = true; // Enable highlight
        },
        onDragLeave(event) {
            this.isDraggedOver = false;
        },

        /**
         * Drop functionality for drag-and-drop into folders.
         */
        onDrop(event) {
            event.preventDefault();
            // Parse the dragged nodes (IDs)
            try {
                const source_node_ids = JSON.parse(event.dataTransfer.getData('text/plain'));
                this.isDraggedOver = false;
                this.$emit('drag-event', {
                    sourceNodes: source_node_ids, // Multiple nodes
                    targetNode: this.node, // Target folder
                });
            } catch (e) {
                // try {
                this.isDraggedOver = false;
                let noty = Vue.prototype.$noty;
                const droppedFiles = Array.from(event.dataTransfer.files);
                if (droppedFiles.length > 1) {
                    noty.error('Please drop only one file at a time!');
                    return;
                }
                // Filter dropped files to ensure only PDFs are allowed
                const validFiles = droppedFiles.filter((file) => this.isPdfFile(file));

                if (validFiles.length > 0) {
                    // Update file list
                    // this.files = [...this.files, ...validFiles];
                    this.$emit('drag-upload-event', {
                        fileNodes: droppedFiles[0],
                        targetNode: this.node, // Target folder
                    });
                } else {
                    noty.error('Only PDF file with size no more-than 20mb are allowed!');
                }
                // } catch (e) {
                //
                // }
            }
        },
        isPdfFile(file) {
            const maxFileSize = 5 * 1024 * 1024; // 5 MB
            return file.type === 'application/pdf' && file.size <= maxFileSize;
        },
        goToShortcut: function (parameter, code1 = '', code2 = '') {
            switch (parameter) {
                case this.propertyModuleName:
                    if (localStorage.getItem('user_type') !== 'A')
                        window.open(
                            '?module=properties&command=property_summary_page&property_code=' + code1,
                            '_blank',
                        );
                    else
                        window.open(
                            '?module=properties&command=v2_manage_property_page&property_code=' + code1,
                            '_blank',
                        );
                    break;
                case this.leaseModuleName:
                    if (localStorage.getItem('user_type') === 'A')
                        window.open(
                            '?module=leases&command=lease_page_v2&property_code=' + code1 + '&lease_code=' + code2,
                            '_blank',
                        );
                    else
                        window.open(
                            '?module=leases&command=lease_summary_page&property_code=' + code1 + '&lease_code=' + code2,
                            '_blank',
                        );
                    break;
                case this.companyModuleName:
                    window.open('?module=companies&command=company_v2&companyID=' + code1);
                    break;
            }
        },
        returnValue(node) {
            return 'download.php?fileID=' + node.filename;
        },
        openUpdateDocumentModal: function (item) {
            this.$emit('update:documentData', item);
        },
        openUpdateModal: function (item) {
            this.$emit('update:directoryData', item);
        },
        deleteDocuments: function (item) {
            this.$emit('update:delDocumentData', item);
        },
        deleteFolder: function (item) {
            this.$emit('update:delDirectoryData', item);
        },
        archiveFolderById: function (item, status) {
            this.$emit('update:archiveDirectoryData', [item, status]);
        },
        archiveDocumentById: function (item, status) {
            this.$emit('update:archiveDocumentData', [item, status]);
        },
        downloadS3File: function (item) {
            this.$emit('download:s3File', item);
        },
    },
    computed: {
        getFileIcon() {
            return this.files[this.node.fileExtension] ? this.files[this.node.fileExtension] : 'mdi-file-question';
        },
    },
};
</script>

<style scoped>
.node {
    padding: 3px;
    cursor: pointer;
}

.node.selected {
    background-color: #cce5ff;
    border: 1px #004085;
}

.node.drag-over {
    background: #ffeeba; /* Highlight color for valid drop targets */
    border: 1px dashed #ffc107;
}

.align-items-center {
    align-items: center;
}
</style>
