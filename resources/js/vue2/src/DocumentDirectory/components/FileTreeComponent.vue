<template>
    <div class="file-browser-tree">
        <ul class="ma-0 pa-0">
            <file-node
                v-for="(node, index) in folderTreeList"
                :key="node.id"
                :node="node"
                :selected="selectedNodes"
                :level="0"
                :edit_form="edit_form"
                :is_live="is_live"
                :property_code="property_code"
                :lease_code="lease_code"
                :company_code="company_code"
                @update:selected="updateSelected"
                @update:documentData="updateSelectedDocumentData"
                @update:directoryData="updateSelectedDirectoryData"
                @update:delDocumentData="deleteDocuments"
                @update:delDirectoryData="deleteFolder"
                @update:archiveDirectoryData="archiveDirectoryFromNode"
                @update:archiveDocumentData="archiveDocumentFromNode"
                @drag-event="handleDragEvent"
                @drag-upload-event="handleDragUploadEvent"
                @download:s3File="downloadS3File"
            ></file-node>
        </ul>

        <v-dialog
            v-model="showAddEditModal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitData()"
        >
            <v-card>
                <v-card-title class="headline">
                    Document Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="showAddEditModal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="errorServerMsg"
                        :errorMsg2="errorServerMsg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="documentsArr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        {{ documentsArr.index }}
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Path
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-model="addNewFolderPath"
                                        :options="folderList"
                                        :groupSelect="false"
                                        optionsLimit="10000"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select a folder"
                                        track-by="directoryId"
                                        label="folderPath"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Title:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="documentsArr.documentTitle"
                                        maxlength="50"
                                        :id="'key_id' + documentsArr.index"
                                        data-inverted=""
                                        :data-tooltip="'Document Title'"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Description:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="documentsArr.documentDescription"
                                        size=""
                                        :id="'key_id' + documentsArr.index"
                                        data-inverted=""
                                        :data-tooltip="'Document Description'"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >File:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <table>
                                        <tbody>
                                            <tr v-if="!isExternalLink(documentsArr.isExternal)">
                                                <td style="margin: 0; padding: 0; font-size: 12px">
                                                    <span v-if="!documentsArr.onS3">
                                                        <cirrus-single-upload-button2
                                                            :withLinkUploader="true"
                                                            :id="
                                                                getIdOfUploadButton(
                                                                    new Date().getTime() + Math.random(),
                                                                )
                                                            "
                                                            v-model="documentsArr.filename"
                                                            :has_saved_file="hasSavedFile(documentsArr.filenameOld)"
                                                            :edit_form="true"
                                                            accept_type="pdf"
                                                            :size_limit="20"
                                                        ></cirrus-single-upload-button2>
                                                    </span>
                                                    <span v-else>
                                                        <a
                                                            target="_blank"
                                                            v-on:click="downloadS3File(documentsArr)"
                                                        >
                                                            <img
                                                                :src="this.$assetDomain + 'assets/images/icons/pdf.png'"
                                                                alt="pdf"
                                                                class="icon"
                                                            />&nbsp; Download
                                                        </a>
                                                    </span>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!documentsArr.filename"
                                                >
                                                    <span>or</span>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!documentsArr.filename"
                                                >
                                                    <a
                                                        href="#"
                                                        @click="documentsArr.enableExternalLink = 1"
                                                    >
                                                        <v-icon
                                                            color="primary"
                                                            small
                                                        >
                                                            mdi-link
                                                        </v-icon>
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr v-if="isExternalLink(documentsArr.isExternal)">
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="
                                                        documentsArr.externalUrl === '' || documentsArr.index === 'New'
                                                    "
                                                >
                                                    <cirrus-input
                                                        custom_class="cirrus-input-table-textbox"
                                                        maxlength="255"
                                                        v-model="documentsArr.externalUrl"
                                                        size=""
                                                        :edit_form="true"
                                                        :error_msg="error_msg"
                                                    ></cirrus-input>
                                                    <v-btn
                                                        :edit_form="true"
                                                        x-small
                                                        class=""
                                                        @click="
                                                            documentsArr.enableExternalLink = 0;
                                                            documentsArr.externalUrl = '';
                                                        "
                                                        >Cancel
                                                    </v-btn>
                                                    <input
                                                        type="hidden"
                                                        v-model="documentsArr.enableExternalLink"
                                                    />
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0; font-size: 12px"
                                                    v-else
                                                >
                                                    <span class="form-input-text">
                                                        <a
                                                            :href="documentsArr.externalUrl"
                                                            target="_blank"
                                                        >
                                                            <v-icon
                                                                color="primary"
                                                                small
                                                            >
                                                                mdi-link
                                                            </v-icon>
                                                            Open Link
                                                        </a>
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="property_code && !lease_code && !company_code"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Publish to Owner:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <sui-checkbox
                                            v-model="documentsArr.publishToOwner"
                                            data-inverted=""
                                            :data-tooltip="edit_form ? 'Publish to Owner' : false"
                                        />
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="property_code && !lease_code && !company_code"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Include with Owner Reports:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <sui-checkbox
                                            v-model="documentsArr.includeOwnerReport"
                                            data-inverted=""
                                            :data-tooltip="edit_form ? 'Include with owner report' : false"
                                        />
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="property_code && lease_code && !company_code"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Publish to Tenant:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <sui-checkbox
                                            v-model="documentsArr.publishToTenant"
                                            data-inverted=""
                                            :data-tooltip="edit_form ? 'Publish to Tenant' : false"
                                        />
                                    </span>
                                </v-col>
                            </v-row>

                            <v-row
                                class="form-row"
                                v-if="documentsArr.index !== 'New'"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Archived:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <sui-checkbox v-model="documentsArr.archivedFile" />
                                    </span>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                ></v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        :loading="isBtnLoading"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="showNewModal"
            max-width="700"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Folder Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="
                            showNewModal = false;
                            errorServerMsg2 = [];
                        "
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <v-alert
                        v-if="systemMessageFlag"
                        type="warning"
                    >
                        Creation of subfolders beyond the second level is not permitted. Please ensure that the folder
                        hierarchy does not exceed two levels. For example, the structure
                        <strong>{{ rootDirectoryName }}/folder1/folder2</strong> is acceptable, but adding a
                        <strong>folder3</strong>
                        under
                        <strong>folder2</strong> is not allowed.
                    </v-alert>
                    <cirrus-server-error
                        :error_msg="errorServerMsg"
                        :errorMsg2="errorServerMsg2"
                    ></cirrus-server-error>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Path
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-model="addNewFolderPath"
                                        :options="folderList"
                                        :groupSelect="false"
                                        optionsLimit="10000"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select a folder"
                                        track-by="directoryId"
                                        label="folderPath"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="!systemMessageFlag"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Folder Name
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'40'"
                                        :maxlength="'40'"
                                        :id="'newFolderName'"
                                        v-model="newFolderName"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="!systemMessageFlag"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Archived
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <sui-checkbox v-model="newFolderArchived" />
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions v-if="!systemMessageFlag">
                    <v-spacer />
                    <v-btn
                        color="success"
                        depressed
                        small
                        @click="submitUpdateFolder()"
                        :loading="isBtnLoading"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        depressed
                        small
                        @click="
                            showNewModal = false;
                            errorServerMsg2 = [];
                        "
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import FileNode from './FileNode.vue';
import sortByParam from '../../plugins/sortByParam';
import global_mixins, { cirrusDialog } from '../../plugins/mixins';
import { bus } from '../../plugins/bus';
import Vue from 'vue';
import isEmpty from 'lodash/isEmpty';
import {
    ARCHIVE_LABEL,
    COMPANY_MODULE_NAME,
    DOCUMENT_SECTION_NAME,
    IS_ARCHIVED,
    IS_EXTERNAL,
    IS_PROPERTY_LEDGER,
    LEASE_MODULE_NAME,
    NOT_ARCHIVED,
    PROPERTY_MODULE_NAME,
    ROOT_DIRECTORY_NAME,
    UNARCHIVE_LABEL,
} from '../constants';
import { ENABLED } from '../../constants';

export default {
    name: 'FileTreeComponent',
    components: { FileNode },
    props: {
        is_live: {
            type: Boolean,
            default: false,
        },
        filter: {
            type: Object,
            default: {
                documentStatus: 'ACTIVE',
                searchDatatable: '',
                directoryId: null,
                filterDocType: 'ALL',
                filterPublishToOwner: false,
                filterIncludeOwnReport: false,
                filterPublishToTenant: false,
                filterSortBy: 0,
                filterSortType: 0,
            },
        },
        edit_form: {
            type: Boolean,
            default: false,
        },
        property_code: {
            type: String,
            default: '',
        },
        lease_code: {
            type: String,
            default: '',
        },
        company_code: {
            type: String,
            default: '',
        },
        version_id: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            folderTreeList: [],
            folderTreeOrigList: [],
            folderList: [],
            documentsArr: {},
            errorServerMsg: {},
            errorServerMsg2: [],
            addNewFolderPath: { directoryId: null, folderPath: ROOT_DIRECTORY_NAME },
            showAddEditModal: false,
            showNewModal: false,
            isBtnLoading: false,
            newFolderName: '',
            newFolderArchived: false,
            systemMessageFlag: false,
            selectedNodes: [],
            isArchived: IS_ARCHIVED,
            notArchived: NOT_ARCHIVED,
            isExternal: IS_EXTERNAL,
            isPropertyLedger: IS_PROPERTY_LEDGER,
            rootDirectoryName: ROOT_DIRECTORY_NAME,
            propertyModuleName: PROPERTY_MODULE_NAME,
            leaseModuleName: LEASE_MODULE_NAME,
            companyModuleName: COMPANY_MODULE_NAME,
            documentSectionName: DOCUMENT_SECTION_NAME,
            archiveLabel: ARCHIVE_LABEL,
            unarchiveLabel: UNARCHIVE_LABEL,
        };
    },
    methods: {
        loadModuleDocument: function () {
            let type = { module: this.propertyModuleName, section: this.documentSectionName };
            if (this.company_code) type = { module: this.companyModuleName, section: this.documentSectionName };
            if (this.lease_code) type = { module: this.leaseModuleName, section: this.documentSectionName };
            var formData = new FormData();
            formData.append('property_code', this.property_code);
            formData.append('lease_code', this.lease_code);
            formData.append('company_code', this.company_code);
            formData.append('folderModule', type.module);
            formData.append('section', type.section);
            formData.append('no_load', true);
            let apiUrl = 'unapproved/administration/document-folder/fetch/folder-with-file-list';
            if (this.is_live) apiUrl = 'administration/document-folder/fetch/folder-with-file-list';
            this.$api.post(apiUrl, formData).then((response) => {
                this.folderTreeOrigList = response.data.folderTreeList;
                this.folderTreeOrigList.push({
                    archivedFile: false,
                    archivedFileStatus: this.notArchived,
                    companyCode: '',
                    directoryId: '',
                    documentDescription: '',
                    documentFolderArchived: '',
                    documentFolderName: '',
                    documentFolderPath: '',
                    documentId: '',
                    documentPeriodDate: null,
                    documentPeriodDateRaw: null,
                    documentPeriodFrom: null,
                    documentPeriodFromRaw: null,
                    documentPeriodTo: null,
                    documentSeq: '9999999999',
                    documentTitle: this.rootDirectoryName,
                    documentType: '',
                    externalUrl: '',
                    fileExtension: '',
                    fileType: 'root',
                    filename: '',
                    filenameOld: '',
                    id: 'F',
                    includeOwnerReport: false,
                    isArchived: this.notArchived,
                    isExternal: this.isExternal,
                    itemNo: 'system',
                    leaseCode: '',
                    name: this.rootDirectoryName,
                    parentId: '',
                    propertyCode: '',
                    propertyIsLedger: this.isPropertyLedger,
                    publishToOwner: false,
                    publishToTenant: false,
                    selected: false,
                    sortOrder: 99999999,
                    type: 'root',
                });
                this.regenerateTreeViewBasedOnFilter();
                this.loadFolderList();
            });
        },
        removeArchivedNodes: function (data) {
            if (Array.isArray(data)) {
                data = data
                    .filter((item) => item.isArchived === this.notArchived)
                    .map((item) => {
                        if (item.children) {
                            item.children = this.removeArchivedNodes(item.children, this.notArchived);
                        }
                        return item;
                    });
            }
            return data;
        },
        recursiveFilter: function (archivedList, items) {
            return items.filter((item) => {
                if (item.isArchived === this.isArchived) {
                    archivedList.push(item);
                    return false;
                }

                if (Array.isArray(item.children)) {
                    item.children = this.recursiveFilter(archivedList, item.children);
                }

                return true;
            });
        },
        extractArchivedNodes: function (data) {
            const archivedList = [];
            data = this.recursiveFilter(archivedList, data);
            return { updatedData: data, archivedNodes: archivedList };
        },
        regenerateTreeViewBasedOnFilter: function () {
            let filteredData = this.objectClone(this.folderTreeOrigList);

            filteredData = filteredData.filter((item) => this.filterTreeViewBySearchBar(item));

            switch (this.filter.documentStatus) {
                case 'ACTIVE':
                    filteredData = this.removeArchivedNodes(filteredData);
                    break;
                case 'ARCHIVED':
                    filteredData = this.extractArchivedNodes(filteredData).archivedNodes;
                    break;
            }

            let filterDocType = this.filter.filterDocType;
            let filterPublishToOwner = this.filter.filterPublishToOwner;
            let filterIncludeOwnRpt = this.filter.filterIncludeOwnRpt;
            let filterPublishToTenant = this.filter.filterPublishToTenant;
            let filterSortBy = this.filter.filterSortBy;
            let filterSortType = this.filter.filterSortType === 0 ? 'asc' : 'desc';

            if (this.filter.directoryId)
                filteredData = filteredData.filter((item) =>
                    this.filterTreeViewByFolderId(item, this.filter.directoryId),
                );
            filteredData = filteredData.filter((item) => this.filterTreeViewByDocumentType(item, filterDocType));
            filteredData = filteredData.filter((item) =>
                this.filterTreeViewByPublishToOwner(item, filterPublishToOwner),
            );
            filteredData = filteredData.filter((item) =>
                this.filterTreeViewByIncludeOwnerReport(item, filterIncludeOwnRpt),
            );
            filteredData = filteredData.filter((item) =>
                this.filterTreeViewByPublishToTenant(item, filterPublishToTenant),
            );

            switch (filterSortBy) {
                case 0:
                    sortByParam(filteredData, 'sortOrder', filterSortType);
                    break;
                case 1:
                    sortByParam(filteredData, 'name', filterSortType);
                    break;
                case 2:
                    sortByParam(filteredData, 'documentDescription', filterSortType);
                    break;
                case 3:
                    sortByParam(filteredData, 'documentPeriodDateRaw', filterSortType);
                    break;
                case 4:
                    sortByParam(filteredData, 'documentPeriodFromRaw', filterSortType);
                    break;
            }

            this.folderTreeList = this.objectClone(filteredData);
        },
        filterTreeViewBySearchBar: function (item) {
            let searchText = this.filter.searchDatatable.toLowerCase();
            if (searchText === '') return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) => this.filterTreeViewBySearchBar(child));
                return item.children.length > 0;
            } else if (
                searchText &&
                typeof item.fileType === 'string' &&
                (item.documentTitle.toLowerCase().includes(searchText) ||
                    item.documentDescription.toLowerCase().includes(searchText) ||
                    item.itemNo.toLowerCase().includes(searchText))
            )
                return true;
            else return !!(searchText && item.name.toLowerCase().includes(searchText));
        },

        filterTreeViewByFolderId: function (item, directoryId) {
            if (directoryId === null) return true;
            if (directoryId && Number(item.directoryId) === Number(directoryId)) return true;
            else if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) => this.filterTreeViewByFolderId(child, directoryId));
                return item.children.length > 0 || Number(item.directoryId) === Number(directoryId);
            } else return false;
        },

        filterTreeViewByDocumentType: function (item, documentType) {
            if (documentType === 'ALL') return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) => this.filterTreeViewByDocumentType(child, documentType));
                return item.children.length > 0;
            } else if (
                item.type === 'file' ||
                !item.documentType ||
                (item.documentType && item.documentType === documentType)
            )
                return true;
            else return false;
        },

        filterTreeViewByPublishToOwner: function (item, filterPublishToOwner) {
            if (!filterPublishToOwner) return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) =>
                    this.filterTreeViewByPublishToOwner(child, filterPublishToOwner),
                );
                return item.children.length > 0;
            } else if (item.type === 'file' || (item.publishToOwner && item.publishToOwner === true)) return true;
            else return false;
        },

        filterTreeViewByIncludeOwnerReport: function (item, filterIncludeOwnRpt) {
            if (!filterIncludeOwnRpt) return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) =>
                    this.filterTreeViewByIncludeOwnerReport(child, filterIncludeOwnRpt),
                );
                return item.children.length > 0;
            } else if (item.type === 'file' || (item.includeOwnerReport && item.includeOwnerReport === true))
                return true;
            else return false;
        },

        filterTreeViewByPublishToTenant: function (item, filterPublishToTenant) {
            if (!filterPublishToTenant) return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) =>
                    this.filterTreeViewByPublishToTenant(child, filterPublishToTenant),
                );
                return item.children.length > 0;
            } else if (item.type === 'file' || (item.publishToTenant && item.publishToTenant === true)) return true;
            else return false;
        },
        /**
         * Updates selected nodes when a child sends selection events.
         */
        updateSelected(newSelection) {
            this.selectedNodes = newSelection;
        },
        updateSelectedDocumentData(item) {
            this.error_msg = [];
            this.errorServerMsg = {};
            this.errorServerMsg2 = [];
            this.showAddEditModal = true;
            this.addNewFolderPath = this.findObjectByProperty('directoryId', item.directoryId, this.folderList, {
                directoryId: null,
                folderPath: this.rootDirectoryName,
            });
            this.documentsArr = {
                index: item.itemNo,
                documentTitle: item.documentTitle,
                documentDescription: item.documentDescription,
                directoryId: item.directoryId,
                filename: item.filename,
                filenameOld: item.filenameOld,
                documentId: item.documentId,
                documentPeriodDate: item.documentPeriodDate,
                publishToOwner: item.publishToOwner,
                publishToTenant: item.publishToTenant,
                includeOwnerReport: item.includeOwnerReport,
                archivedFile: item.archivedFile,
                status: 'saved',
                isExternal: item.isExternal,
                enableExternalLink: Number(item.isExternal),
                externalUrl: item.externalUrl,
                folderType: item.directoryId ? 1 : 0,
                onS3: item.onS3,
                s3Filename: item.s3Filename,
                s3Filepath: item.s3Filepath,
            };
        },
        updateSelectedDirectoryData: function (item) {
            this.showNewModal = true;
            this.update_modal = true;
            this.newFolderName = item.name;
            this.newFolderArchived = item.isArchived === this.isArchived;
            this.updateDirectoryId = item.directoryId;
            this.parentId = item.parentId;
            this.select_item = item;
            this.errorServerMsg2 = [];
            this.addNewFolderPath = this.findObjectByProperty('directoryId', item.parentId, this.folderList, {
                directoryId: null,
                folderPath: this.rootDirectoryName,
            });
        },
        modalSubmitData: function () {
            this.isBtnLoading = true;
            let errorArr = [];
            let documentId = this.documentsArr.documentId;
            let documentTitle = this.documentsArr.documentTitle;
            let documentDescription = this.documentsArr.documentDescription;
            let documentPeriodDate = this.documentsArr.documentPeriodDate;
            let publishToOwner = this.documentsArr.publishToOwner;
            let publishToTenant = this.documentsArr.publishToTenant;
            let includeOwnerReport = this.documentsArr.includeOwnerReport;
            let archivedFile = this.documentsArr.archivedFile;
            let filename = this.documentsArr.filename;
            let filenameOld = this.documentsArr.filenameOld;
            let externalUrl = this.documentsArr.externalUrl;
            let enableExternalLink = this.documentsArr.enableExternalLink;
            let index = this.documentsArr.index;

            if (documentTitle === '') errorArr.push(['You have not entered a valid title for the document.']);
            if (documentDescription === '')
                errorArr.push(['You have not entered a valid description for the document.']);
            if (enableExternalLink === 0) {
                if (filename === null && index === 'New')
                    errorArr.push(['You have not entered a valid attachment for the document.']);
            } else {
                if (externalUrl === '' && index === 'New')
                    errorArr.push(['You have not entered a valid attachment for the document.']);
            }
            this.errorServerMsg2 = errorArr;
            if (this.errorServerMsg2.length === 0) {
                var formData = new FormData();
                formData.append('property_code', this.property_code);
                formData.append('lease_code', this.lease_code);
                formData.append('company_code', this.company_code);
                formData.append('version_id', this.version_id);
                formData.append('directory_id', this.addNewFolderPath.directoryId);
                formData.append('document_id', documentId);
                formData.append('document_title', documentTitle);
                formData.append('document_description', documentDescription);
                formData.append('document_period_date', documentPeriodDate);
                formData.append('publish_to_owner', publishToOwner);
                formData.append('publish_to_tenant', publishToTenant);
                formData.append('include_owner_report', includeOwnerReport);
                formData.append('archived_file', archivedFile);
                formData.append('external_url', externalUrl);
                formData.append('filename', filename);
                formData.append('no_load', true);
                if (filename !== filenameOld) formData.append('docs_file', this.documentsArr.filename[0]);

                let type = { module: this.propertyModuleName, section: this.documentSectionName };
                if (this.company_code) type = { module: this.companyModuleName, section: this.documentSectionName };
                if (this.lease_code) type = { module: this.leaseModuleName, section: this.documentSectionName };

                let apiUrl;
                switch (type.module) {
                    case this.propertyModuleName:
                        if (this.is_live) apiUrl = 'with-file-upload/property/update/document';
                        else apiUrl = 'with-file-upload/temp/property/update/document';
                        break;
                    case this.companyModuleName:
                        if (this.is_live) apiUrl = 'with-file-upload/company/update/single-document';
                        else apiUrl = 'with-file-upload/unapproved/company/update/single-document';
                        break;
                    case this.leaseModuleName:
                        if (this.is_live) apiUrl = 'with-file-upload/lease/update/document';
                        else apiUrl = 'with-file-upload/temp/lease/update/document';
                        break;
                }

                this.$api
                    .post(apiUrl, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.isBtnLoading = false;
                        this.showAddEditModal = false;
                        this.loadModuleDocument();
                    });
            }
        },
        submitUpdateFolder: function () {
            this.isBtnLoading = true;
            this.errorServerMsg2 = [];
            let newFolderName = this.newFolderName;
            let newFolderArchived = this.newFolderArchived;
            var formData = new FormData();
            formData.append('newFolderName', newFolderName);
            formData.append('newFolderArchived', newFolderArchived);
            formData.append('folderModule', this.form_type);
            formData.append('directoryId', this.updateDirectoryId);
            formData.append('parentId', this.addNewFolderPath.directoryId);
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/update/document-folder';

            this.$api
                .post(apiUrl, formData)
                .then((response) => {
                    this.loadModuleDocument();
                    this.showNewModal = false;
                    this.isBtnLoading = false;
                })
                .catch((error) => {
                    if (error.response && error.response.status === 422) {
                        const message = error.response.data.message;
                        this.errorServerMsg2.push([message]);
                        this.isBtnLoading = false;
                    }
                });
        },
        /**
         * Handle drag-and-drop events for moving files/folders.
         */
        handleDragEvent({ sourceNodes, targetNode }) {
            if (sourceNodes.length === 0) return;
            sourceNodes.forEach((id) => {
                let directoryId = targetNode.directoryId;
                var formData = new FormData();
                formData.append('draggedId', id);
                formData.append('droppedId', directoryId);
                formData.append('no_load', true);
                let apiUrl = 'unapproved/administration/document-folder/process/process-move-folder-or-file';
                if (this.is_live) apiUrl = 'administration/document-folder/process/process-move-folder-or-file';
                this.$api.post(apiUrl, formData).then(async (response) => {
                    this.loadModuleDocument();
                });
            });
        },
        /**
         * Handle drag-and-drop events for uploading file.
         */
        handleDragUploadEvent({ fileNodes, targetNode }) {
            bus.$emit('openNewDocumentModal', {
                directoryId: targetNode.directoryId,
                fileNodes: fileNodes,
            });
        },
        loadFolderList: function () {
            let type = { module: this.propertyModuleName, section: this.documentSectionName };
            if (this.company_code) type = { module: this.companyModuleName, section: this.documentSectionName };
            if (this.lease_code) type = { module: this.leaseModuleName, section: this.documentSectionName };
            var formData = new FormData();
            formData.append('property_code', this.property_code);
            formData.append('lease_code', this.lease_code);
            formData.append('company_code', this.company_code);
            formData.append('folderModule', type.module);
            formData.append('section', type.section);
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/fetch/folder-list';

            this.$api.post(apiUrl, formData).then((response) => {
                this.folderList = response.data.folderList;
                this.folderList.unshift({ directoryId: '', folderPath: this.rootDirectoryName });
            });
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadDocs_' + id;
        },
        async deleteDocuments(item) {
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                    { label: this.archiveLabel, value: 3 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            var formData = new FormData();
            formData.append('property_code', this.property_code);
            formData.append('lease_code', this.lease_code);
            formData.append('company_code', this.company_code);
            formData.append('version_id', this.version_id);
            formData.append('documentId', item.documentId);
            formData.append('document_id', item.documentId);
            formData.append('no_load', true);

            if (result === 1) {
                this.loading_setting = true;
                let type = { module: this.propertyModuleName, section: this.documentSectionName };
                if (this.company_code) type = { module: this.companyModuleName, section: this.documentSectionName };
                if (this.lease_code) type = { module: this.leaseModuleName, section: this.documentSectionName };

                let apiUrl;
                switch (type.module) {
                    case this.propertyModuleName:
                        apiUrl = 'temp/property/delete/document';
                        if (this.is_live) apiUrl = 'property/delete/document';
                        break;
                    case this.companyModuleName:
                        apiUrl = 'unapproved/company/delete/document';
                        if (this.is_live) apiUrl = 'company/delete/document';
                        break;
                    case this.leaseModuleName:
                        if (this.is_live) apiUrl = 'lease/delete/document';
                        else apiUrl = 'temp/lease/delete/document';
                        break;
                }

                this.$api.post(apiUrl, formData).then((response) => {
                    this.loadModuleDocument();
                });
            } else if (result === 3) {
                this.loading_setting = true;
                await this.archiveDocumentById(item, this.isArchived);
            }
        },
        async deleteFolder(item) {
            var formData = new FormData();
            formData.append('property_code', this.property_code);
            formData.append('lease_code', this.lease_code);
            formData.append('company_code', this.company_code);
            formData.append('version_id', this.version_id);
            formData.append('directoryId', item.directoryId);
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/fetch/check-file-in-folder-id';

            this.$api.post(apiUrl, formData).then(async (response) => {
                let dialog_prop = {};
                let apiUrl;
                if (response.data.file_check) {
                    dialog_prop = {
                        title: 'Warning',
                        message: 'Folder is being use by other file(s), do you want to archive instead?',
                        icon_show: true,
                        buttons_right: [
                            { label: this.archiveLabel, value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    apiUrl = 'administration/document-folder/process/archive-folder';
                    formData.append('archive_status', this.isArchived);
                } else {
                    dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                            {
                                label: this.archiveLabel,
                                value: 3,
                            },
                        ],
                    };
                    apiUrl = 'administration/document-folder/delete/delete-folder';
                }
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.$api.post(apiUrl, formData).then((response) => {
                        this.loadModuleDocument();
                    });
                } else if (result === 3) {
                    await this.archiveFolderById(item, this.isArchived);
                }
            });
        },
        async archiveFolderById(item, archive_status) {
            let button_lbl = this.archiveLabel;
            if (archive_status === this.notArchived) button_lbl = this.unarchiveLabel;
            var formData = new FormData();
            formData.append('property_code', this.property_code);
            formData.append('lease_code', this.lease_code);
            formData.append('company_code', this.company_code);
            formData.append('version_id', this.version_id);
            formData.append('directoryId', item.directoryId);
            formData.append('no_load', true);
            formData.append('archive_status', archive_status);
            let dialog_prop = {
                title: this.archiveLabel,
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: button_lbl, value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            let apiUrl = 'administration/document-folder/process/archive-folder';
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.$api.post(apiUrl, formData).then((response) => {
                    this.loadModuleDocument();
                });
            }
        },
        async archiveDocumentById(item, archive_status) {
            let button_lbl = this.archiveLabel;
            if (archive_status === this.notArchived) button_lbl = this.unarchiveLabel;
            var formData = new FormData();
            formData.append('property_code', this.property_code);
            formData.append('lease_code', this.lease_code);
            formData.append('company_code', this.company_code);
            formData.append('version_id', this.version_id);
            formData.append('documentId', item.documentId);
            formData.append('directoryId', item.directoryId);
            formData.append('no_load', true);
            formData.append('archive_status', archive_status);
            let dialog_prop = {
                title: this.archiveLabel,
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: button_lbl, value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            let apiUrl = 'unapproved/administration/document-folder/process/archive-file';
            if (this.is_live) apiUrl = 'administration/document-folder/process/archive-file';

            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.$api.post(apiUrl, formData).then((response) => {
                    this.loadModuleDocument();
                });
            }
        },
        archiveDirectoryFromNode: function (item) {
            this.archiveFolderById(item[0], item[1]);
        },
        archiveDocumentFromNode: function (item) {
            this.archiveDocumentById(item[0], item[1]);
        },
        downloadZipFolder(directoryId) {
            var formData = new FormData();
            formData.append('directoryId', directoryId);
            let apiUrl = 'unapproved/administration/document-folder/process/process-download-folder-zip';
            if (this.is_live) apiUrl = 'administration/document-folder/process/process-download-folder-zip';
            this.$api.post(apiUrl, formData).then(async (response) => {
                if (response.data !== '') {
                    let link = document.createElement('a');
                    link.href = 'download.php?fileID=' + response.data;
                    link.target = '_blank';
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
                } else {
                    let noty = Vue.prototype.$noty;
                    noty.error('No files in this folder');
                }
            });
        },
        loadModuleDocumentHandler(data) {
            this.loadModuleDocument();
        },
        regenerateTreeViewHandler(data) {
            this.filter = data;
            this.regenerateTreeViewBasedOnFilter();
        },
        hasSavedFile: function (value) {
            return !isEmpty(value);
        },
        isExternalLink: function (value) {
            return Number(value) === ENABLED.value;
        },
        downloadS3File(item) {
            const params = new FormData();
            params.append('s3path', item.s3Filepath);

            const api_url = 'documents/download-s3';
            this.$api
                .post(api_url, params)
                .then((response) => {
                    if (response.data.errorMessage) {
                        this.$noty.error(response.data.errorMessage);
                        return;
                    }

                    this.fileDL(response.data.s3File, item.s3Filename, '');
                })
                .catch((errorResponse) => {
                    this.$noty.error(
                        errorResponse.response.data.message ?? 'Something went wrong with the downloading of S3 file.',
                    );
                });
        },
    },
    watch: {
        property_code: function () {
            if (this.property_code !== '') {
                this.loadModuleDocument();
            }
        },
        lease_code: function () {
            if (this.lease_code !== '') {
                this.loadModuleDocument();
            }
        },
        company_code: function () {
            if (this.company_code !== '') {
                this.loadModuleDocument();
            }
        },
    },
    mounted() {
        this.loadModuleDocument();
    },
    created() {
        bus.$on('loadModuleDocumentSection', this.loadModuleDocumentHandler);
        bus.$on('regenerateTreeViewBasedOnFilter', this.regenerateTreeViewHandler);
    },
    beforeDestroy() {
        bus.$off('loadModuleDocumentSection', this.loadModuleDocumentHandler);
        bus.$off('regenerateTreeViewBasedOnFilter', this.regenerateTreeViewHandler);
    },
    mixins: [global_mixins],
};
</script>

<style>
.file-browser-tree ul {
    list-style: none;
    padding-left: 15px;
}
</style>
