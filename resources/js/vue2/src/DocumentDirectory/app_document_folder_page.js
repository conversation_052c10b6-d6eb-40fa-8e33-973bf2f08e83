import Vue from 'vue';
// VUETIFY
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../plugins/vuetify';
import '../plugins/global';
import '../plugins/request';
import SuiVue from 'semantic-ui-vue';
import Multiselect from 'vue-multiselect';
/**
 * Next, we will create a fresh Vue application instance and attach it to
 * the page. Then, you may begin adding components to this application
 * or customize the JavaScript scaffolding to fit your unique needs.
 */
import DocumentFolderPage from './templates/DocumentFolderPage.vue';
// import vSelect from 'vue-select'
Vue.use(SuiVue);

Vue.component('vue-modal', require('../components/elements/VueModal.vue').default);
Vue.component('cirrus-page-header', require('../components/elements/CirrusPageHeader.vue').default);
Vue.component('cirrus-text-area', require('../components/elements/CirrusTextarea.vue').default);
Vue.component('cirrus-date-picker', require('../components/elements/CirrusDatePicker.vue').default);
Vue.component('cirrus-icon-date-picker', require('../components/elements/CirrusIconDatePicker.vue').default);
Vue.component('cirrus-email-centralisation', require('../components/elements/CirrusEmailCentralisation.vue').default);
Vue.component('cirrus-input', require('../components/elements/CirrusInput.vue').default);
Vue.component('cirrus-single-upload-button', require('../components/elements/CirrusSingleUploadButton.vue').default);
Vue.component('cirrus-single-upload-button2', require('../components/elements/CirrusSingleUploadButtonV2.vue').default);
Vue.component('cirrus-radio-group', require('../components/elements/CirrusRadioGroup.vue').default);
Vue.component('cirrus-server-error', require('../components/elements/CirrusServerError.vue').default);
Vue.component('vue-dual-list-select', require('../components/elements/VueDualListSelect.vue').default);
Vue.component('cirrus-loader', require('../components/elements/CirrusLoader.vue').default);
Vue.component('cirrus-content-loader', require('../components/elements/CirrusContentLoader.vue').default);
Vue.component('cirrus-single-upload-button', require('../components/elements/CirrusSingleUploadButton.vue').default);

// CREATE VUE
new Vue({
    el: '#app',
    SuiVue,
    vuetify,
    components: {
        'main-template-component': DocumentFolderPage,
        multiselect: Multiselect,
    },
}).$mount('#app');
