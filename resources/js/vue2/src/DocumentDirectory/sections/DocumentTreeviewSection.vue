<style>
body.c8-dark #frame #container .document-section #status-form-toggle .v-btn--text .v-btn__content,
body.c8-dark #frame #container .document-section #type-form-toggle .v-btn--text .v-btn__content,
body.c8-dark #frame #container .document-section #sort-form-toggle .v-btn--text .v-btn__content,
body.c8-dark #frame #container .document-section #sort-type-form-toggle .v-btn--text .v-btn__content {
    color: #4e4e4e !important;
}

body.c8-dark #frame #container .document-section #document-treeview .v-treeview,
body.c8-dark #frame #container .document-section #document-treeview .v-icon {
    color: inherit !important;
}
</style>
<template>
    <div class="document-section">
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-switch
                    v-model="filterToggle"
                    inset
                    dense
                    color="#ff9507"
                    :label="`Filter`"
                    style="width: auto; padding-right: 10px"
                ></v-switch>
                <v-btn-toggle
                    id="status-form-toggle"
                    class="form-toggle"
                    v-model="documentListFilter"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Active
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        {{ archiveLabel }}
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        All
                    </v-btn>
                </v-btn-toggle>
                <cirrus-input
                    inputFormat="search"
                    v-model="search_content"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    icon
                    @click="openNewModal(null)"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    icon
                    @click="loadFolderTreeList()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <div
            v-if="filterToggle"
            style="border-radius: 0px !important"
        >
            <div class="page-form">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Folder Name
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            openDirection="bottom"
                            v-model="filterFolder"
                            :options="folderList"
                            :groupSelect="false"
                            optionsLimit="10000"
                            class="vue-select2 dropdown-left dropdown-400"
                            placeholder="Select a folder"
                            track-by="directoryId"
                            label="folderPath"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-btn
                            x-small
                            class="ma-1"
                            color="normal"
                            @click="filterFolder = { directoryId: null, folderPath: 'All' }"
                            >reset to all
                        </v-btn>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="module === propertyModuleName || module === leaseModuleName"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Document Type
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            id="type-form-toggle"
                            class="form-toggle"
                            v-model="filterDocumentType"
                            mandatory
                        >
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                All
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Document
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Inspection
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Sort By
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            id="sort-form-toggle"
                            class="form-toggle"
                            v-model="filterSortBy"
                            mandatory
                        >
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Sequence
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Title
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Description
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                                v-if="module === propertyModuleName"
                            >
                                Period Date
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                                v-if="module === propertyModuleName"
                            >
                                Reporting Date
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Sort Type
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            id="sort-type-form-toggle"
                            class="form-toggle"
                            v-model="filterSortType"
                            mandatory
                        >
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                ASC
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                DESC
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row
                    class="form-row"
                    v-if="module === propertyModuleName || module === leaseModuleName"
                >
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Other(s)
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-switch
                            v-model="filterPublishToOwner"
                            label="Publish to Owner"
                            color="secondary"
                            dense
                            style="width: auto; padding-right: 10px"
                        ></v-switch>
                        <v-switch
                            v-model="filterPublishToTenant"
                            label="Publish to Tenant"
                            color="secondary"
                            dense
                            style="width: auto; padding-right: 10px"
                            v-if="module === leaseModuleName"
                        ></v-switch>
                        <v-switch
                            v-model="filterIncludeOwnRpt"
                            label="Include to Owner Report"
                            color="secondary"
                            dense
                            style="width: auto; padding-right: 10px"
                            v-if="module === propertyModuleName"
                        ></v-switch>
                    </v-col>
                </v-row>
            </div>
            <br />
            <div class="text-right pa-2">
                <v-btn
                    color="primary"
                    x-small
                    right
                    @click="applyFilters()"
                    >Apply
                </v-btn>
                <v-btn
                    color="normal"
                    x-small
                    right
                    @click="resetFilters()"
                    >Reset
                </v-btn>
            </div>
        </div>
        <br v-if="filterToggle" />
        <v-card flat>
            <v-card-text>
                <v-treeview
                    id="document-treeview"
                    v-model="tree"
                    :open="initiallyOpen"
                    :items="folderTreeList"
                    activatable
                    hoverable
                    dense
                    item-key="name"
                    :search="search_content"
                    :active.sync="active"
                    :open.sync="open"
                    :key="refresh_index"
                >
                    <template v-slot:prepend="{ item, open }">
                        <v-icon
                            v-if="!item.fileType"
                            :color="item.isArchived === notArchived ? '' : 'subPrimary'"
                        >
                            {{ open ? 'mdi-folder-open' : 'mdi-folder' }}
                        </v-icon>
                        <v-icon v-else>
                            {{ files[item.fileExtension] ? files[item.fileExtension] : 'mdi-file-question' }}
                        </v-icon>
                    </template>
                    <template v-slot:label="{ item, open }">
                        <span
                            :id="item.id"
                            class="primary--text"
                        >
                            <span v-if="!item.fileType">
                                {{ item.name }}
                            </span>
                            <span v-else>
                                {{ item.itemNo }}
                                <a @click="goToShortcut(propertyModuleName, item.propertyCode)"
                                    >[{{ item.propertyCode }}]</a
                                >
                                <a
                                    v-if="item.leaseCode"
                                    @click="goToShortcut(leaseModuleName, item.propertyCode, item.leaseCode)"
                                    >[{{ item.lease_code }}]</a
                                >
                                <a
                                    v-if="item.companyCode"
                                    @click="goToShortcut(companyModuleName, item.companyCode)"
                                    >[{{ item.companyCode }}]</a
                                >
                                :
                                {{ item.documentTitle }} -
                                {{ item.documentDescription }}
                            </span>
                        </span>
                    </template>
                    <template v-slot:append="{ item, open }">
                        <div class="item-container">
                            <div
                                class="item-actions"
                                :id="'tooltip_att' + item.id"
                            >
                                <div>
                                    <a
                                        data-tooltip="Rename folder name"
                                        :data-position="'left center'"
                                    >
                                        <v-icon
                                            color="primary"
                                            small
                                            @click="openUpdateModal(item)"
                                        >
                                            fas fa-edit
                                        </v-icon>
                                    </a>
                                </div>

                                <div>
                                    <a
                                        data-tooltip="Delete folder"
                                        :data-position="'left center'"
                                        v-if="item.isArchived === notArchived"
                                    >
                                        <v-icon
                                            small
                                            color="red"
                                            @click="deleteFolder(item)"
                                        >
                                            mdi-close
                                        </v-icon>
                                    </a>
                                    <a
                                        data-tooltip="Remove from archive"
                                        :data-position="'left center'"
                                        v-if="item.isArchived === isArchived"
                                    >
                                        <v-icon
                                            color="subPrimary"
                                            small
                                            @click="archiveFolderById(item, notArchived)"
                                            >{{ 'mdi-archive-remove' }}
                                        </v-icon>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </template>
                </v-treeview>
            </v-card-text>
        </v-card>

        <v-dialog
            v-model="showNewModal"
            max-width="500"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Folder Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="
                            showNewModal = false;
                            errorServerMsg2 = [];
                        "
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="errorServerMsg"
                        :errorMsg2="errorServerMsg2"
                    ></cirrus-server-error>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                    >Path
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-model="addNewFolderPath"
                                        :options="folderList"
                                        :groupSelect="false"
                                        optionsLimit="10000"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select a folder"
                                        track-by="directoryId"
                                        label="folderPath"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label required"
                                    >Name
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'40'"
                                        :maxlength="'40'"
                                        :id="'newFolderName'"
                                        v-model="newFolderName"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="submitNewFolder()"
                        v-if="!showUpdateModal"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-check
                        </v-icon>
                        Submit
                    </v-btn>
                    <v-btn
                        color="primary"
                        depressed
                        small
                        @click="submitUpdateFolder()"
                        v-if="showUpdateModal"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-check
                        </v-icon>
                        Update
                    </v-btn>
                    <v-btn
                        depressed
                        small
                        @click="
                            showNewModal = false;
                            errorServerMsg2 = [];
                        "
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
<script>
import global_mixins, { cirrusDialog } from '../../plugins/mixins';
import { bus } from '../../plugins/bus';
import sortByParam from '../../plugins/sortByParam';
import {
    ARCHIVE_LABEL,
    COMPANY_MODULE_NAME,
    IS_ARCHIVED,
    LEASE_MODULE_NAME,
    NOT_ARCHIVED,
    PROPERTY_MODULE_NAME,
    ROOT_DIRECTORY_NAME,
    UNARCHIVE_LABEL,
} from '../constants';

const pause = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
export default {
    props: {
        module: { type: String, default: '' },
    },
    data() {
        return {
            loadingSetting: false,
            showNewModal: false,
            showUpdateModal: false,
            showFileModal: false,
            newFolderName: '',
            errorServerMsg: {},
            errorServerMsg2: [],
            folderList: [],
            folderTreeList: [],
            folderOrigTreeList: [],
            property_code: { field_key: '', field_value: 'Please select...' },
            lease_code: { field_key: '', field_value: 'Please select...' },
            company_code: { field_key: '', field_value: 'Please select...' },
            search_content: '',
            overview_loading_msg: 'Please wait...',
            initiallyOpen: ['public'],
            files: {
                html: 'mdi-language-html5',
                js: 'mdi-nodejs',
                json: 'mdi-code-json',
                md: 'mdi-language-markdown',
                pdf: 'mdi-file-pdf-box',
                png: 'mdi-file-image',
                jpg: 'mdi-file-image',
                txt: 'mdi-file-document-outline',
                xls: 'mdi-file-excel',
                xlsx: 'mdi-file-excel',
                link: 'mdi-link-box-variant-outline',
                zip: 'mdi-folder-zip',
            },
            tree: [],
            active: [],
            open: [],
            directoryId: null,
            parentId: null,
            update_directoryId: null,
            refresh_index: Math.random(),
            documentListFilter: 0,
            filterToggle: false,
            filterFolder: { directoryId: null, folderPath: 'All' },
            filterDocumentType: 0,
            filterSortBy: 0,
            filterSortType: 0,
            filterPublishToOwner: false,
            filterPublishToTenant: false,
            filterIncludeOwnRpt: false,
            addNewFolderPath: { directoryId: null, folderPath: ROOT_DIRECTORY_NAME },
            isArchived: IS_ARCHIVED,
            notArchived: NOT_ARCHIVED,
            rootDirectoryName: ROOT_DIRECTORY_NAME,
            propertyModuleName: PROPERTY_MODULE_NAME,
            leaseModuleName: LEASE_MODULE_NAME,
            companyModuleName: COMPANY_MODULE_NAME,
            archiveLabel: ARCHIVE_LABEL,
            unarchiveLabel: UNARCHIVE_LABEL,
        };
    },
    mounted() {
        this.loadFolderTreeList();
        this.loadFolderList();
    },
    computed: {},
    methods: {
        refreshData: function () {
            this.loadFolderTreeList();
            this.loadFolderList();
        },
        loadFolderList: function () {
            this.loadingSetting = true;
            var formData = new FormData();
            formData.append('folderModule', this.module);
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/fetch/folder-list';
            this.$api.post(apiUrl, formData).then((response) => {
                this.folderList = response.data.folderList;
                this.loadingSetting = false;
            });
        },
        loadFolderTreeList: function () {
            var formData = new FormData();
            formData.append('folderModule', this.module);
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/fetch/document-management-folder-list';
            this.$api.post(apiUrl, formData).then((response) => {
                this.folderOrigTreeList = response.data.folderTreeList;
                this.regenerateTreeViewBasedOnFilter();
            });
        },
        goToShortcut: function (parameter, code1 = '', code2 = '') {
            switch (parameter) {
                case this.propertyModuleName:
                    if (localStorage.getItem('user_type') !== 'A') {
                        window.open(
                            '?module=properties&command=property_summary_page&property_code=' + code1,
                            '_blank',
                        );
                    } else {
                        window.open(
                            '?module=properties&command=v2_manage_property_page&property_code=' + code1,
                            '_blank',
                        );
                    }

                    break;
                case this.leaseModuleName:
                    if (localStorage.getItem('user_type') === 'A') {
                        window.open(
                            '?module=leases&command=lease_page_v2&property_code=' + code1 + '&lease_code=' + code2,
                            '_blank',
                        );
                    } else {
                        window.open(
                            '?module=leases&command=lease_summary_page&property_code=' + code1 + '&lease_code=' + code2,
                            '_blank',
                        );
                    }
                    break;
                case this.companyModuleName:
                    window.open('?module=companies&command=company_v2&companyID=' + code1);

                    break;
            }
        },
        reset: function () {
            this.property_code = { field_key: '', field_value: 'Please select...' };
            this.lease_code = { field_key: '', field_value: 'Please select...' };
            this.company_code = { field_key: '', field_value: 'Please select...' };
        },
        async archiveFolderById(item, archiveStatus) {
            let buttonLabel = this.archiveLabel;
            if (archiveStatus === this.isArchived) buttonLabel = this.unarchiveLabel;
            var formData = new FormData();
            formData.append('document_id', this.document_id);
            formData.append('directoryId', item.directoryId);
            formData.append('no_load', true);
            formData.append('archiveStatus', archiveStatus);
            let dialog_prop = {
                title: this.archiveLabel,
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: buttonLabel, value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            let apiUrl = 'administration/document-folder/process/archive-folder';
            const result = await cirrusDialog(dialog_prop);
            if (result === 1) {
                this.$api.post(apiUrl, formData).then((response) => {
                    this.refreshData();
                });
            }
        },
        async deleteFolder(item) {
            var formData = new FormData();
            formData.append('document_id', this.document_id);
            formData.append('directoryId', item.directoryId);
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/fetch/check-file-in-folder-id';
            this.$api.post(apiUrl, formData).then(async (response) => {
                let dialog_prop = {};
                let apiUrl;
                if (response.data.file_check) {
                    dialog_prop = {
                        title: 'Warning',
                        message: 'Folder currently contains other file(s), do you want to archive instead?',
                        icon_show: true,
                        buttons_right: [
                            { label: this.archiveLabel, value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    apiUrl = 'administration/document-folder/process/archive-folder';
                    formData.append('archiveStatus', this.notArchived);
                } else {
                    dialog_prop = {
                        title: 'Warning',
                        message: 'Are you sure?',
                        icon_show: true,
                        buttons_right: [
                            { label: 'Yes', value: 1, color: 'primary' },
                            { label: 'No', value: 2 },
                        ],
                    };
                    apiUrl = 'administration/document-folder/delete/delete-folder';
                }
                const result = await cirrusDialog(dialog_prop);
                if (result === 1) {
                    this.loadingSetting = true;
                    this.$api.post(apiUrl, formData).then((response) => {
                        this.loadingSetting = false;
                        this.refreshData();
                        bus.$emit('loadPropertyDocumentSection', '');
                        bus.$emit('loadLeaseDocumentSection', '');
                        bus.$emit('loadLeaseDocumentSection', '');
                    });
                }
            });
        },
        submitNewFolder: function () {
            this.errorServerMsg2 = [];
            let newFolderName = this.newFolderName;
            this.loadingSetting = true;
            var formData = new FormData();
            formData.append('newFolderName', newFolderName);
            formData.append('folderModule', this.module);
            formData.append('parentId', this.addNewFolderPath.directoryId);
            formData.append('newFolderArchived', '0');
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/create/document-folder';
            this.$api
                .post(apiUrl, formData)
                .then((response) => {
                    this.loadFolderList();
                    this.loadFolderTreeList();
                    this.loadingSetting = false;
                    this.showNewModal = false;
                })
                .catch((error) => {
                    this.loadingSetting = false;
                    if (error.response && error.response.status === 422) {
                        const message = error.response.data.message;
                        this.errorServerMsg2.push([message]);
                        this.btn_loading = false;
                    }
                });
        },
        submitUpdateFolder: function () {
            this.errorServerMsg2 = [];
            let newFolderName = this.newFolderName;
            this.loadingSetting = true;
            var formData = new FormData();
            formData.append('newFolderName', newFolderName);
            formData.append('newFolderArchived', this.isArchived);
            formData.append('folderModule', this.module);
            formData.append('directoryId', this.update_directoryId);
            formData.append('parentId', this.addNewFolderPath.directoryId);

            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/update/document-folder';
            this.$api
                .post(apiUrl, formData)
                .then((response) => {
                    this.loadFolderList();
                    this.loadFolderTreeList();
                    this.loadingSetting = false;
                    this.showNewModal = false;
                })
                .catch((error) => {
                    if (error.response && error.response.status === 422) {
                        const message = error.response.data.message;
                        this.errorServerMsg2.push([message]);
                        this.loadingSetting = false;
                    }
                });
        },
        openNewModal: function (item) {
            this.showNewModal = true;
            this.showUpdateModal = false;
            this.newFolderName = '';
            this.errorServerMsg2 = [];
            this.addNewFolderPath = { directoryId: null, folderPath: this.rootDirectoryName };
            this.directoryId = item ? item.directoryId : null;
            this.parentId = item ? item.parentId : null;
        },
        openUpdateModal: function (item) {
            this.showNewModal = true;
            this.showUpdateModal = true;
            this.newFolderName = item.name;
            this.update_directoryId = item.directoryId;
            this.parentId = item.parentId;
            this.errorServerMsg2 = [];
            this.addNewFolderPath = this.findObjectByProperty('directoryId', this.parentId, this.folderList, {
                directoryId: null,
                folderPath: this.rootDirectoryName,
            });
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadDocs_' + id;
        },
        applyFilters: function () {
            this.regenerateTreeViewBasedOnFilter();
        },
        resetFilters: function () {
            this.filterFolder = { directoryId: null, folderPath: 'All' };
            this.filterDocumentType = 0;
            this.filterSortBy = 0;
            this.filterPublishToOwner = false;
            this.filterIncludeOwnRpt = false;
        },
        filterTreeView: function (item, directoryId, documentType, filterPublishToOwner, filterIncludeOwnRpt) {
            if (directoryId === null && documentType === 'ALL' && !filterPublishToOwner && !filterIncludeOwnRpt)
                return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) =>
                    this.filterTreeView(child, directoryId, documentType, filterPublishToOwner, filterIncludeOwnRpt),
                );
                return item.children.length > 0;
            } else if (
                (directoryId && eval(item.directoryId) === directoryId) ||
                !item.documentType ||
                (item.documentType && item.documentType === documentType) ||
                filterPublishToOwner ||
                filterIncludeOwnRpt
            )
                return true;
            else return false;
        },
        filterTreeViewByFolderId: function (item, directoryId) {
            if (directoryId === null) return true;
            if (directoryId && eval(item.directoryId) === directoryId) return true;
            else if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) => this.filterTreeViewByFolderId(child, directoryId));
                return item.children.length > 0;
            } else return false;
        },
        filterTreeViewByDocumentType: function (item, documentType) {
            if (documentType === 'ALL') return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) => this.filterTreeViewByDocumentType(child, documentType));
                return item.children.length > 0;
            } else if (
                item.type === 'file' ||
                !item.documentType ||
                (item.documentType && item.documentType === documentType)
            )
                return true;
            else return false;
        },
        filterTreeViewByPublishToOwner: function (item, filterPublishToOwner) {
            if (!filterPublishToOwner) return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) =>
                    this.filterTreeViewByPublishToOwner(child, filterPublishToOwner),
                );
                return item.children.length > 0;
            } else if (item.type === 'file' || (item.publish_to_owner && item.publish_to_owner === true)) return true;
            else return false;
        },
        filterTreeViewByIncludeOwnerReport: function (item, filterIncludeOwnRpt) {
            if (!filterIncludeOwnRpt) return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) =>
                    this.filterTreeViewByIncludeOwnerReport(child, filterIncludeOwnRpt),
                );
                return item.children.length > 0;
            } else if (item.type === 'file' || (item.include_owner_report && item.include_owner_report === true))
                return true;
            else return false;
        },
        filterTreeViewByPublishToTenant: function (item, filterPublishToTenant) {
            if (!filterPublishToTenant) return true;
            if (item.children && item.children.length > 0) {
                item.children = item.children.filter((child) =>
                    this.filterTreeViewByPublishToTenant(child, filterPublishToTenant),
                );
                return item.children.length > 0;
            } else if (item.type === 'file' || (item.publish_to_tenant && item.publish_to_tenant === true)) return true;
            else return false;
        },
        removeArchivedNodes: function (data) {
            if (Array.isArray(data)) {
                data = data
                    .filter((item) => item.isArchived === this.notArchived)
                    .map((item) => {
                        if (item.children) {
                            item.children = this.removeArchivedNodes(item.children);
                        }
                        return item;
                    });
            }
            return data;
        },
        recursiveFilter: function (archivedList, items) {
            return items.filter((item) => {
                if (item.isArchived === this.isArchived) {
                    archivedList.push(item);
                    return false;
                }

                if (Array.isArray(item.children)) {
                    item.children = this.recursiveFilter(archivedList, item.children);
                }

                return true;
            });
        },
        extractArchivedNodes: function (data) {
            const archivedList = [];
            data = this.recursiveFilter(archivedList, data);
            return { updatedData: data, archivedNodes: archivedList };
        },
        regenerateTreeViewBasedOnFilter: function () {
            let filteredData = this.objectClone(this.folderOrigTreeList);

            switch (this.documentListFilter) {
                case 0:
                    filteredData = this.removeArchivedNodes(filteredData);
                    break;
                case 1:
                    filteredData = this.extractArchivedNodes(filteredData).archivedNodes;
                    break;
            }
            let filterDocumentType = this.filterDocumentType;
            let documentType = 'ALL';
            switch (filterDocumentType) {
                case 1:
                    if (this.module === this.propertyModuleName) documentType = '15';
                    if (this.module === this.leaseModuleName) documentType = '10';
                    break;
                case 2:
                    if (this.module === this.propertyModuleName) documentType = '12';
                    if (this.module === this.leaseModuleName) documentType = '11';
                    break;
            }
            let filterPublishToOwner = this.filterPublishToOwner;
            let filterIncludeOwnRpt = this.filterIncludeOwnRpt;
            let filterPublishToTenant = this.filterPublishToTenant;
            let filterSortBy = this.filterSortBy;
            let filterSortType = this.filterSortType === 0 ? 'asc' : 'desc';
            if (this.filterFolder.directoryId)
                filteredData = filteredData.filter((item) => this.filterTreeViewByFolderId(item, directoryId));
            filteredData = filteredData.filter((item) => this.filterTreeViewByDocumentType(item, documentType));
            filteredData = filteredData.filter((item) =>
                this.filterTreeViewByPublishToOwner(item, filterPublishToOwner),
            );
            filteredData = filteredData.filter((item) =>
                this.filterTreeViewByIncludeOwnerReport(item, filterIncludeOwnRpt),
            );
            filteredData = filteredData.filter((item) =>
                this.filterTreeViewByPublishToTenant(item, filterPublishToTenant),
            );
            switch (filterSortBy) {
                case 0:
                    sortByParam(filteredData, 'sortOrder', filterSortType);
                    break;
                case 1:
                    sortByParam(filteredData, 'name', filterSortType);
                    break;
                case 2:
                    sortByParam(filteredData, 'documentDescription', filterSortType);
                    break;
                case 3:
                    sortByParam(filteredData, 'documentPeriodDateRaw', filterSortType);
                    break;
                case 4:
                    sortByParam(filteredData, 'documentPeriodFromRaw', filterSortType);
                    break;
            }
            this.folderTreeList = this.objectClone(filteredData);
        },
    },
    watch: {
        documentListFilter: function () {
            this.regenerateTreeViewBasedOnFilter();
        },
    },
    created() {},
    mixins: [global_mixins],
};
</script>

<style scoped>
.item-container {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.tooltip-content {
    font-size: 12px;
    color: #0087bf;
    border: 2px solid #0087bf;
    border-radius: 6px;
    padding: 8px;
}

.info-item {
    display: flex;
    margin-bottom: 4px;
}

.label {
    flex: 1;
    text-align: right;
    padding-right: 4px;
}

.separator {
    flex: 0;
}

.value {
    flex: 2;
    word-wrap: break-word;
}

.item-actions {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    align-items: center;
}

.fixed-width-16 {
    width: 16px;
}
</style>
