<template>
    <div
        class="document-section document-div"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Documents</h6>
                &nbsp
                <v-btn-toggle
                    v-if="sys_ver_control_list.isFolderSystemOn"
                    class="form-toggle"
                    v-model="doc_active_version"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                        @click="setActiveVersion(0)"
                    >
                        Version 1
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                        @click="setActiveVersion(1)"
                    >
                        Beta
                    </v-btn>
                </v-btn-toggle>
                <v-spacer></v-spacer>
                <v-switch
                    v-model="filterToggle"
                    inset
                    dense
                    color="#ff9507"
                    :label="`Filter`"
                    style="width: auto; padding-right: 10px"
                ></v-switch>
                <v-btn-toggle
                    id="status-form-toggle"
                    class="form-toggle"
                    v-model="documentListFilter"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Active
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Archive
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        All
                    </v-btn>
                </v-btn-toggle>
                <cirrus-input
                    inputFormat="search"
                    v-if="isEditable()"
                    v-model="searchDatatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="padding-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-show="!readonly && isEditable() && !pmro_read_only"
                    data-tooltip="Add Folder"
                    icon
                    @click="openNewFolderModal()"
                >
                    <v-icon>mdi-folder-plus</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!readonly && isEditable() && !pmro_read_only"
                    data-tooltip="Add Document"
                    icon
                    @click="openNewDocumentModal()"
                >
                    <v-icon>mdi-file-plus</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!readonly && isEditable() && !pmro_read_only"
                    data-tooltip="Edit"
                    v-if="!edit_form"
                    icon
                    @click="edit_form = true"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable() && !pmro_read_only"
                    data-tooltip="Read-only"
                    v-if="edit_form && !new_lease"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="!readonly && isEditable()"
                    icon
                    data-tooltip="Refresh"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    v-show="isLeaseFormLive()"
                    data-tooltip="View Log"
                    @click="showActivityModal()"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <div
            v-if="filterToggle"
            style="border-radius: 0px !important"
        >
            <div class="page-form">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Folder Name
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            openDirection="bottom"
                            v-model="filterFolder"
                            :options="folderList"
                            :groupSelect="false"
                            optionsLimit="10000"
                            class="vue-select2 dropdown-left dropdown-400"
                            placeholder="Select a folder"
                            track-by="directoryId"
                            label="folderPath"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-btn
                            x-small
                            class="ma-1"
                            color="normal"
                            @click="filterFolder = { directoryId: null, folderPath: 'All' }"
                            >reset to all
                        </v-btn>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Document Type
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            id="type-form-toggle"
                            class="form-toggle"
                            v-model="filterDocumentType"
                            mandatory
                        >
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                All
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Lease Document
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Lease Inspection
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Sort By
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            id="sort-form-toggle"
                            class="form-toggle"
                            v-model="filterSortBy"
                            mandatory
                        >
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Sequence
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Title
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Description
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Sort Type
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            id="sort-type-form-toggle"
                            class="form-toggle"
                            v-model="filterSortType"
                            mandatory
                        >
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                ASC
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                DESC
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Other(s)
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-switch
                            v-model="filterPublishToOwner"
                            label="Publish to Owner"
                            color="secondary"
                            dense
                            style="width: auto; padding-right: 10px"
                        ></v-switch>
                        <v-switch
                            v-model="filterPublishToTenant"
                            label="Publish to Tenant"
                            color="secondary"
                            dense
                            style="width: auto; padding-right: 10px"
                        ></v-switch>
                    </v-col>
                </v-row>
            </div>
            <br />
            <div class="text-right pa-2">
                <v-btn
                    color="primary"
                    x-small
                    right
                    @click="applyFilters()"
                    >Apply
                </v-btn>
                <v-btn
                    color="normal"
                    x-small
                    right
                    @click="resetFilters()"
                    >Reset
                </v-btn>
            </div>
        </div>
        <br v-if="filterToggle" />
        <cirrus-content-loader v-if="loadingSetting"></cirrus-content-loader>
        <cirrus-server-error
            :error_msg="errorServerMsg"
            :errorMsg2="errorServerMsg2"
        ></cirrus-server-error>
        <div
            class="page-form"
            v-if="!loadingSetting"
        >
            <file-tree-component
                :is_live="isLeaseFormLive()"
                :property_code="property_code"
                :lease_code="lease_code"
                :edit_form="edit_form"
                :version_id="version_id"
            />
        </div>

        <v-dialog
            v-model="showNewModal"
            max-width="700"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Folder Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="
                            showNewModal = false;
                            errorServerMsg2 = [];
                        "
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <v-alert
                        v-if="systemMessageFlag"
                        type="warning"
                    >
                        Creation of subfolders beyond the second level is not permitted. Please ensure that the folder
                        hierarchy does not exceed two levels. For example, the structure
                        <strong>{{ rootDirectoryName }}/folder1/folder2</strong> is acceptable, but adding a
                        <strong>folder3</strong>
                        under
                        <strong>folder2</strong> is not allowed.
                    </v-alert>
                    <cirrus-server-error
                        :error_msg="errorServerMsg"
                        :errorMsg2="errorServerMsg2"
                    ></cirrus-server-error>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Path
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-model="addNewFolderPath"
                                        :options="folderList"
                                        :groupSelect="false"
                                        optionsLimit="10000"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select a folder"
                                        track-by="directoryId"
                                        label="folderPath"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="!systemMessageFlag"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Folder Name
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'40'"
                                        :maxlength="'40'"
                                        :id="'newFolderName'"
                                        v-model="newFolderName"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="!systemMessageFlag && updateModal"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Archived
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <sui-checkbox v-model="newFolderArchived" />
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions v-if="!systemMessageFlag">
                    <v-spacer />
                    <v-btn
                        color="success"
                        depressed
                        small
                        @click="submitNewFolder()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        depressed
                        small
                        @click="
                            showNewModal = false;
                            errorServerMsg2 = [];
                        "
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="AddEditDeleteModal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitData()"
        >
            <v-card>
                <v-card-title class="headline">
                    Document Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AddEditDeleteModal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="errorServerMsg"
                        :errorMsg2="errorServerMsg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Lease add-->
                    <div :key="documentsArr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        {{ documentsArr.index }}
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Path
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-model="addNewFolderPath"
                                        :options="folderList"
                                        :groupSelect="false"
                                        optionsLimit="10000"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select a folder"
                                        track-by="directoryId"
                                        label="folderPath"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Title:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="documentsArr.documentTitle"
                                        maxlength="50"
                                        :id="'key_id' + documentsArr.index"
                                        data-inverted=""
                                        :data-tooltip="'Document Title'"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Description:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="documentsArr.documentDescription"
                                        size=""
                                        :id="'key_id' + documentsArr.index"
                                        data-inverted=""
                                        :data-tooltip="'Document Description'"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >File:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <table>
                                        <tbody>
                                            <tr v-if="documentsArr.enableExternalLink === 0">
                                                <td style="margin: 0; padding: 0; font-size: 12px">
                                                    <cirrus-single-upload-button2
                                                        :withLinkUploader="true"
                                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                                        v-model="documentsArr.filename"
                                                        :has_saved_file="
                                                            documentsArr.filenameOld !== '' &&
                                                            (typeof documentsArr.filenameOld === 'string' ||
                                                                documentsArr.filenameOld instanceof String)
                                                                ? true
                                                                : false
                                                        "
                                                        :edit_form="true"
                                                        accept_type="pdf"
                                                        :size_limit="20"
                                                        :externalFile="externalFile"
                                                    ></cirrus-single-upload-button2>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!documentsArr.filename"
                                                >
                                                    <span>or</span>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!documentsArr.filename"
                                                >
                                                    <a
                                                        href="#"
                                                        @click="documentsArr.enableExternalLink = 1"
                                                        ><img
                                                            :src="
                                                                assetDomain + 'assets/images/icons/link_icon_blue.png'
                                                            "
                                                            class="icon"
                                                            style="width: 19px"
                                                    /></a>
                                                </td>
                                            </tr>
                                            <tr v-if="documentsArr.enableExternalLink === 1">
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="
                                                        documentsArr.externalUrl === '' || documentsArr.index === 'New'
                                                    "
                                                >
                                                    <cirrus-input
                                                        custom_class="cirrus-input-table-textbox"
                                                        maxlength="255"
                                                        v-model="documentsArr.externalUrl"
                                                        size=""
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                    <v-btn
                                                        :edit_form="true"
                                                        x-small
                                                        class=""
                                                        @click="
                                                            documentsArr.enableExternalLink = 0;
                                                            documentsArr.externalUrl = '';
                                                        "
                                                        >Cancel
                                                    </v-btn>
                                                    <input
                                                        type="hidden"
                                                        v-model="documentsArr.enableExternalLink"
                                                    />
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0; font-size: 12px"
                                                    v-else
                                                >
                                                    <span class="form-input-text">
                                                        <a
                                                            :href="documentsArr.externalUrl"
                                                            target="_blank"
                                                            ><img
                                                                :src="
                                                                    assetDomain +
                                                                    'assets/images/icons/link_icon_blue.png'
                                                                "
                                                                class="icon"
                                                                style="width: 12px"
                                                            />Open Link</a
                                                        >
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Publish to Owner:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <sui-checkbox
                                            v-model="documentsArr.publishToOwner"
                                            data-inverted=""
                                            :data-tooltip="edit_form ? 'Publish to Owner' : false"
                                        />
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Publish to Tenant:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <sui-checkbox
                                            v-model="documentsArr.publishToTenant"
                                            data-inverted=""
                                            :data-tooltip="edit_form ? 'Publish to Tenant' : false"
                                        />
                                    </span>
                                </v-col>
                            </v-row>

                            <v-row
                                class="form-row"
                                v-if="documentsArr.index !== 'New'"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Archived:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <sui-checkbox v-model="documentsArr.archivedFile" />
                                    </span>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                ></v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="documentsArr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="showActivityLogModal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="showActivityLogModal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <lease-activity-log-component
                        v-if="showActivityLogModal"
                        :property_code="property_code"
                        :lease_code="lease_code"
                        :formSection="formSection"
                    ></lease-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="showActivityLogModal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import global_mixins from '../../plugins/mixins';
import { bus } from '../../plugins/bus';
import axios from 'axios';
import FileTreeComponent from '../components/FileTreeComponent.vue';
import {
    ARCHIVE_LABEL,
    COMPANY_MODULE_NAME,
    IS_ARCHIVED,
    LEASE_MODULE_NAME,
    NOT_ARCHIVED,
    PROPERTY_MODULE_NAME,
    ROOT_DIRECTORY_NAME,
    UNARCHIVE_LABEL,
} from '../constants';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        page_form_type: { type: String, default: '' },
        property_code: { type: String, default: '' },
        lease_code: { type: String, default: '' },
        version_id: { type: String, default: '' },
        read_only: { type: Boolean, default: false },
        summary: { type: Boolean, default: false },
        new_lease: { type: Boolean, default: false },
        forceLoad: { type: Boolean, default: false },
        viewTemp: { type: Boolean, default: false },
        edit_flag: { type: Boolean, default: false },
        pmro_read_only: { type: Boolean, default: false },
    },
    components: {
        'file-tree-component': FileTreeComponent,
    },
    data() {
        return {
            formType: LEASE_MODULE_NAME,
            formSection: 'LEASE_DOCUMENT',
            assetDomain: this.$assetDomain,
            errorServerMsg: {},
            errorServerMsg2: [],
            loadingSetting: true,
            edit_form: false,
            AddEditDeleteModal: false,
            documentsArr: [],
            readonly: this.read_only,
            showActivityLogModal: false,
            searchDatatable: '',
            directoryId: null,
            parentId: null,
            showNewModal: false,
            updateModal: false,
            showFileModal: false,
            newFolderName: '',
            newFolderArchived: false,
            documentListFilter: 0,
            folderList: [],
            filterToggle: false,
            folderPath: { directoryId: null, folderPath: ROOT_DIRECTORY_NAME },
            filterFolder: { directoryId: null, folderPath: 'All' },
            filterDocumentType: 0,
            filterSortBy: 0,
            filterSortType: 0,
            filterPublishToOwner: false,
            filterIncludeOwnRpt: false,
            filterPublishToTenant: false,
            addNewFolderPath: { directoryId: null, folderPath: ROOT_DIRECTORY_NAME },
            systemMessageFlag: false,
            externalFile: null,
            isArchived: IS_ARCHIVED,
            notArchived: NOT_ARCHIVED,
            rootDirectoryName: ROOT_DIRECTORY_NAME,
            propertyModuleName: PROPERTY_MODULE_NAME,
            leaseModuleName: LEASE_MODULE_NAME,
            companyModuleName: COMPANY_MODULE_NAME,
            archiveLabel: ARCHIVE_LABEL,
            unarchiveLabel: UNARCHIVE_LABEL,
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadingSetting = false;
        this.loadForm();
        if (this.edit_flag) this.edit_form = true;
        if (this.new_lease) this.edit_form = true;
        if (this.sys_ver_control_list.isFolderSystemOn) this.SET_DOC_ACTIVE_VERSION(1);
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_lease_form_read_only',
            'doc_active_version',
            'sys_ver_control_list',
        ]),
    },
    methods: {
        ...mapMutations(['SET_DOC_ACTIVE_VERSION']),
        isEditable: function () {
            if (this.new_lease) return true;
            else return !this.formSectionReadOnly(this.pm_lease_form_read_only, this.formType, this.formSection);
        },
        loadForm: function () {
            this.errorServerMsg = {};
            this.errorServerMsg2 = [];
            this.loadFolderList();
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadDocs_' + id;
        },
        openNewFolderModal: function () {
            this.showNewModal = true;
            this.addNewFolderPath = { directoryId: null, folderPath: this.rootDirectoryName };
            this.updateModal = false;
            this.newFolderName = '';
            this.newFolderArchived = false;
            this.errorServerMsg2 = [];
            this.directoryId = '';
            this.parentId = '';
        },
        submitNewFolder: function () {
            this.errorServerMsg2 = [];
            let newFolderName = this.newFolderName;
            let newFolderArchived = this.newFolderArchived;
            var form_data = new FormData();
            form_data.append('newFolderName', newFolderName);
            form_data.append('newFolderArchived', newFolderArchived);
            form_data.append('folderModule', this.formType);
            form_data.append('parentId', this.addNewFolderPath.directoryId);
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('no_load', true);
            let api_url = 'administration/document-folder/create/document-folder';
            this.$api
                .post(api_url, form_data)
                .then((response) => {
                    this.loadingSetting = false;
                    this.showNewModal = false;
                    this.updateModal = true;
                    this.loadFolderList();
                    this.loadForm();
                })
                .catch((error) => {
                    this.loadingSetting = false;
                    if (error.response && error.response.status === 422) {
                        const message = error.response.data.message;
                        this.errorServerMsg2.push([message]);
                        this.btn_loading = false;
                    }
                });
        },
        loadFolderList: function () {
            this.loadingSetting = true;
            var form_data = new FormData();
            form_data.append('folderModule', this.leaseModuleName);
            form_data.append('property_code', this.property_code);
            form_data.append('lease_code', this.lease_code);
            form_data.append('no_load', true);
            let api_url = 'administration/document-folder/fetch/folder-list';

            this.$api.post(api_url, form_data).then((response) => {
                this.folderList = response.data.folderList;
                const default_path = { directoryId: '', folderPath: this.rootDirectoryName };
                this.folderList.unshift(default_path);
                this.loadingSetting = false;
            });
        },
        showActivityModal: function () {
            this.showActivityLogModal = true;
        },
        openNewDocumentModal: function () {
            this.errorServerMsg = {};
            this.errorServerMsg2 = [];
            this.showFileModal = false;
            this.AddEditDeleteModal = true;
            this.addNewFolderPath = { directoryId: null, folderPath: this.rootDirectoryName };
            this.documentsArr = {
                index: 'New',
                documentTitle: '',
                documentDescription: '',
                filename: null,
                filenameOld: null,
                documentId: '',
                documentPeriodDate: null,
                publishToOwner: false,
                publishToTenant: false,
                includeOwnerReport: false,
                archivedFile: false,
                status: 'new',
                isExternal: 0,
                enableExternalLink: 0,
                externalUrl: '',
                folderType: 0,
            };
        },
        modalSubmitData: function () {
            let errorArr = [];
            let directoryId = this.documentsArr.directoryId;
            let documentId = this.documentsArr.documentId;
            let documentTitle = this.documentsArr.documentTitle;
            let documentDescription = this.documentsArr.documentDescription;
            let documentPeriodDate = this.documentsArr.documentPeriodDate;
            let publishToOwner = this.documentsArr.publishToOwner;
            let publishToTenant = this.documentsArr.publishToTenant;
            let archivedFile = this.documentsArr.archivedFile;
            let filename = this.documentsArr.filename;
            let filenameOld = this.documentsArr.filenameOld;
            let externalUrl = this.documentsArr.externalUrl;
            let enableExternalLink = this.documentsArr.enableExternalLink;
            let index = this.documentsArr.index;
            if (documentTitle === '') errorArr.push(['You have not entered a valid title for the document.']);
            if (documentDescription === '')
                errorArr.push(['You have not entered a valid description for the document.']);
            if (enableExternalLink === 0) {
                if (filename === null && index === 'New')
                    errorArr.push(['You have not entered a valid attachment for the document.']);
            } else {
                if (externalUrl === '' && index === 'New')
                    errorArr.push(['You have not entered a valid attachment for the document.']);
            }
            this.errorServerMsg2 = errorArr;
            if (this.errorServerMsg2.length === 0) {
                var form_data = new FormData();
                form_data.append('property_code', this.property_code);
                form_data.append('lease_code', this.lease_code);
                form_data.append('version_id', this.version_id);
                form_data.append('directory_id', this.addNewFolderPath.directoryId);
                form_data.append('document_id', documentId);
                form_data.append('document_title', documentTitle);
                form_data.append('document_description', documentDescription);
                form_data.append('document_period_date', documentPeriodDate);
                form_data.append('publish_to_owner', publishToOwner);
                form_data.append('publish_to_tenant', publishToTenant);
                form_data.append('archived_file', archivedFile);
                form_data.append('external_url', externalUrl);
                form_data.append('filename', filename);
                form_data.append('no_load', true);
                if (filename !== filenameOld) form_data.append('docs_file', this.documentsArr.filename[0]);
                let api_url;
                if (index === 'New') {
                    if (this.isLeaseFormLive()) api_url = 'with-file-upload/lease/create/document-v2';
                    else api_url = 'with-file-upload/temp/lease/create/document-v2';
                } else {
                    if (this.isLeaseFormLive()) api_url = 'with-file-upload/lease/update/document';
                    else api_url = 'with-file-upload/temp/lease/update/document';
                }
                this.$api
                    .post(api_url, form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.loadingSetting = false;
                        this.AddEditDeleteModal = false;
                        this.directoryId = null;
                        this.loadForm();
                    });
            }
        },
        applyFilters: function () {
            bus.$emit('regenerateTreeViewBasedOnFilter', this.filterHandler());
        },
        resetFilters: function () {
            this.filterFolder = { directoryId: null, folderPath: 'All' };
            this.filterDocumentType = 0;
            this.filterSortBy = 0;
            this.filterPublishToOwner = false;
            this.filterIncludeOwnRpt = false;
            this.filterPublishToTenant = false;
        },
        setActiveVersion: function (status) {
            this.SET_DOC_ACTIVE_VERSION(status);
        },
        filterHandler: function () {
            let documentListFilter = 'ACTIVE';
            let filterDocumentType = 'ALL';
            if (this.documentListFilter === 1) documentListFilter = 'ARCHIVED';
            if (this.documentListFilter === 2) documentListFilter = 'ALL';
            switch (this.filterDocumentType) {
                case 1:
                    filterDocumentType = '16';
                    break;
                case 2:
                    filterDocumentType = '15';
                    break;
                case 3:
                    filterDocumentType = '12';
                    break;
            }
            return {
                documentStatus: documentListFilter,
                searchDatatable: this.searchDatatable,
                directoryId: this.filterFolder.directoryId,
                filterDocumentType: filterDocumentType,
                filterPublishToOwner: this.filterPublishToOwner,
                filterIncludeOwnRpt: this.filterIncludeOwnRpt,
                filterPublishToTenant: this.filterPublishToTenant,
                filterSortBy: this.filterSortBy,
                filterSortType: this.filterSortType,
            };
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        documentListFilter: function () {
            bus.$emit('regenerateTreeViewBasedOnFilter', this.filterHandler());
        },
        searchDatatable: function () {
            bus.$emit('regenerateTreeViewBasedOnFilter', this.filterHandler());
        },
        filterFolder: function () {
            if (this.filterFolder === null) this.filterFolder = { directoryId: null, folderPath: 'All' };
        },
        addNewFolderPath: function () {
            if (this.addNewFolderPath === null)
                this.addNewFolderPath = { directoryId: null, folderPath: this.rootDirectoryName };
            else {
                if (this.addNewFolderPath.folderPath) {
                    this.systemMessageFlag = this.addNewFolderPath.folderPath.split('/').length >= 3;
                } else this.systemMessageFlag = false;
            }
        },
    },
    created() {
        bus.$on('openNewDocumentModal', (data) => {
            this.errorServerMsg = {};
            this.errorServerMsg2 = [];
            this.showFileModal = false;

            this.addNewFolderPath = this.findObjectByProperty('directoryId', data.directoryId, this.folderList, {
                directoryId: null,
                folderPath: this.rootDirectoryName,
            });
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(data.fileNodes);
            this.externalFile = dataTransfer.files;
            this.documentsArr = {
                index: 'New',
                documentTitle: '',
                documentDescription: '',
                filename: '',
                filenameOld: null,
                documentId: '',
                documentPeriodDate: null,
                publishToOwner: false,
                includeOwnerReport: false,
                archivedFile: false,
                status: 'new',
                isExternal: 0,
                enableExternalLink: 0,
                externalUrl: '',
                folderType: 0,
            };
            this.AddEditDeleteModal = true;
        });
    },
    mixins: [global_mixins],
};
</script>
<style scoped>
.item-container {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.tooltip-content {
    font-size: 12px;
    color: #0087bf;
    border: 2px solid #0087bf;
    border-radius: 6px;
    padding: 8px;
}

.info-item {
    display: flex;
    margin-bottom: 4px;
}

.label {
    flex: 1;
    text-align: right;
    padding-right: 4px;
}

.separator {
    flex: 0;
}

.value {
    flex: 2;
    word-wrap: break-word;
}

.item-actions {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    align-items: center;
}

.fixed-width-16 {
    width: 16px;
}
</style>
