<template>
    <div
        class="document-section document-div"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Documents</h6>
                &nbsp
                <v-btn-toggle
                    v-if="sys_ver_control_list.isFolderSystemOn"
                    class="form-toggle"
                    v-model="doc_active_version"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                        @click="setActiveVersion(0)"
                    >
                        Version 1
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                        @click="setActiveVersion(1)"
                    >
                        Beta
                    </v-btn>
                </v-btn-toggle>
                <v-spacer></v-spacer>
                <v-switch
                    v-model="filterToggle"
                    inset
                    dense
                    color="#ff9507"
                    :label="`Filter`"
                    style="width: auto; padding-right: 10px"
                ></v-switch>
                <v-btn-toggle
                    id="status-form-toggle"
                    class="form-toggle"
                    v-model="documentListFilter"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Active
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Archive
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        All
                    </v-btn>
                </v-btn-toggle>
                <cirrus-input
                    inputFormat="search"
                    v-if="action_parameter !== 'review'"
                    v-model="searchDatatable"
                    placeholder="Search"
                    :edit_form="true"
                    style="margin-right: 1em"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-if="!readOnly"
                    data-tooltip="Add Folder"
                    icon
                    @click="openNewFolderModal()"
                >
                    <v-icon>mdi-folder-plus</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="!readOnly"
                    data-tooltip="Add Document"
                    icon
                    @click="openNewDocumentModal()"
                >
                    <v-icon>mdi-file-plus</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="!edit_form && !readOnly"
                    v-show="action_parameter !== 'review' && action_parameter !== 'view'"
                    icon
                    @click="edit_form = true"
                    data-tooltip="Edit"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    data-tooltip="Done"
                    v-if="edit_form"
                    v-show="action_parameter !== 'review' && action_parameter !== 'view'"
                    class="v-step-revert-button"
                    icon
                    @click="
                        loadForm();
                        edit_form = false;
                    "
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    icon
                    data-tooltip="Refresh"
                    @click="loadForm()"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <div
            v-if="filterToggle"
            style="border-radius: 0px !important"
        >
            <div class="page-form">
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Folder Name
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <multiselect
                            openDirection="bottom"
                            v-model="filterFolder"
                            :options="folderList"
                            :groupSelect="false"
                            optionsLimit="10000"
                            class="vue-select2 dropdown-left dropdown-400"
                            placeholder="Select a folder"
                            track-by="directoryId"
                            label="folderPath"
                            :show-labels="false"
                            ><span slot="noResult">Oops! No elements found. Consider changing the search query.</span>
                        </multiselect>
                        <v-btn
                            x-small
                            class="ma-1"
                            color="normal"
                            @click="filterFolder = { directoryId: null, folderPath: 'All' }"
                            >reset to all
                        </v-btn>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Sort By
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            id="sort-form-toggle"
                            class="form-toggle"
                            v-model="filterSortBy"
                            mandatory
                        >
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Sequence
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Title
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                Description
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
                <v-row class="form-row">
                    <v-col
                        xs="12"
                        sm="2"
                        md="2"
                        class="form-label required"
                        >Sort Type
                    </v-col>
                    <v-col
                        xs="12"
                        sm="10"
                        md="10"
                        class="form-input"
                    >
                        <v-btn-toggle
                            id="sort-type-form-toggle"
                            class="form-toggle"
                            v-model="filterSortType"
                            mandatory
                        >
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                ASC
                            </v-btn>
                            <v-btn
                                x-small
                                depressed
                                text
                            >
                                DESC
                            </v-btn>
                        </v-btn-toggle>
                    </v-col>
                </v-row>
            </div>
            <br />
            <div class="text-right pa-2">
                <v-btn
                    color="primary"
                    x-small
                    right
                    @click="applyFilters()"
                    >Apply
                </v-btn>
                <v-btn
                    color="normal"
                    x-small
                    right
                    @click="resetFilters()"
                    >Reset
                </v-btn>
            </div>
        </div>
        <br v-if="filterToggle" />
        <cirrus-content-loader v-if="loadingSetting"></cirrus-content-loader>
        <cirrus-server-error
            :error_msg="errorServerMsg"
            :errorMsg2="errorServerMsg2"
        ></cirrus-server-error>
        <div
            class="page-form"
            v-if="!loadingSetting"
        >
            <file-tree-component
                :is_live="isLiveData()"
                :company_code="company_code"
                :edit_form="edit_form"
            />
        </div>

        <v-dialog
            v-model="showNewModal"
            max-width="700"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Folder Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="
                            showNewModal = false;
                            errorServerMsg2 = [];
                        "
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <v-alert
                        v-if="systemMessageFlag"
                        type="warning"
                    >
                        Creation of subfolders beyond the second level is not permitted. Please ensure that the folder
                        hierarchy does not exceed two levels. For example, the structure
                        <strong>{{ rootDirectoryName }}/folder1/folder2</strong> is acceptable, but adding a
                        <strong>folder3</strong>
                        under
                        <strong>folder2</strong> is not allowed.
                    </v-alert>
                    <cirrus-server-error
                        :error_msg="errorServerMsg"
                        :errorMsg2="errorServerMsg2"
                    ></cirrus-server-error>
                    <div>
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Path
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-model="addNewFolderPath"
                                        :options="folderList"
                                        :groupSelect="false"
                                        optionsLimit="10000"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select a folder"
                                        track-by="directoryId"
                                        label="folderPath"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="!systemMessageFlag"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Folder Name
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        :size="'40'"
                                        :maxlength="'40'"
                                        :id="'newFolderName'"
                                        v-model="newFolderName"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="!systemMessageFlag && updateModal"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Archived
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <sui-checkbox v-model="newFolderArchived" />
                                </v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions v-if="!systemMessageFlag">
                    <v-spacer />
                    <v-btn
                        color="success"
                        depressed
                        small
                        @click="submitNewFolder()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        depressed
                        small
                        @click="
                            showNewModal = false;
                            errorServerMsg2 = [];
                        "
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <v-dialog
            v-model="AddEditDeleteModal"
            max-width="1000"
            content-class="c8-page"
            @keydown.ctrl.enter="modalSubmitData()"
        >
            <v-card>
                <v-card-title class="headline">
                    Document Information
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="AddEditDeleteModal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <cirrus-server-error
                        :error_msg="errorServerMsg"
                        :errorMsg2="errorServerMsg2"
                    ></cirrus-server-error>
                    <v-alert
                        type="success"
                        dense
                        tile
                        text
                        v-if="success_flag"
                    >
                        Successfully Saved
                    </v-alert>
                    <!--Company add-->
                    <div :key="documentsArr.index">
                        <div class="page-form">
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >#
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        {{ documentsArr.index }}
                                    </span>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Path
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <multiselect
                                        openDirection="bottom"
                                        v-model="addNewFolderPath"
                                        :options="folderList"
                                        :groupSelect="false"
                                        optionsLimit="10000"
                                        class="vue-select2 dropdown-left dropdown-400"
                                        placeholder="Select a folder"
                                        track-by="directoryId"
                                        label="folderPath"
                                        ><span slot="noResult"
                                            >Oops! No elements found. Consider changing the search query.</span
                                        >
                                    </multiselect>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Title:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="documentsArr.documentTitle"
                                        maxlength="50"
                                        :id="'key_id' + documentsArr.index"
                                        data-inverted=""
                                        :data-tooltip="'Document Title'"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>
                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >Description:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-input
                                        custom_class="cirrus-input-table-textbox"
                                        v-model="documentsArr.documentDescription"
                                        size=""
                                        :id="'key_id' + documentsArr.index"
                                        data-inverted=""
                                        :data-tooltip="'Document Description'"
                                        :edit_form="true"
                                    ></cirrus-input>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label required"
                                    >File:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <table>
                                        <tbody>
                                            <tr v-if="documentsArr.enableExternalLink === 0">
                                                <td style="margin: 0; padding: 0; font-size: 12px">
                                                    <cirrus-single-upload-button2
                                                        :withLinkUploader="true"
                                                        :id="getIdOfUploadButton(new Date().getTime() + Math.random())"
                                                        v-model="documentsArr.filename"
                                                        :has_saved_file="
                                                            documentsArr.filenameOld !== '' &&
                                                            (typeof documentsArr.filenameOld === 'string' ||
                                                                documentsArr.filenameOld instanceof String)
                                                                ? true
                                                                : false
                                                        "
                                                        :edit_form="true"
                                                        accept_type="pdf"
                                                        :size_limit="20"
                                                        :externalFile="externalFile"
                                                    ></cirrus-single-upload-button2>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!documentsArr.filename"
                                                >
                                                    <span>or</span>
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="!documentsArr.filename"
                                                >
                                                    <a
                                                        href="#"
                                                        @click="documentsArr.enableExternalLink = 1"
                                                        ><img
                                                            :src="
                                                                assetDomain + 'assets/images/icons/link_icon_blue.png'
                                                            "
                                                            class="icon"
                                                            style="width: 19px"
                                                    /></a>
                                                </td>
                                            </tr>
                                            <tr v-if="documentsArr.enableExternalLink === 1">
                                                <td
                                                    style="margin: 0; padding: 0"
                                                    v-if="
                                                        documentsArr.externalUrl === '' || documentsArr.index === 'New'
                                                    "
                                                >
                                                    <cirrus-input
                                                        custom_class="cirrus-input-table-textbox"
                                                        maxlength="255"
                                                        v-model="documentsArr.externalUrl"
                                                        size=""
                                                        :edit_form="true"
                                                    ></cirrus-input>
                                                    <v-btn
                                                        :edit_form="true"
                                                        x-small
                                                        class=""
                                                        @click="
                                                            documentsArr.enableExternalLink = 0;
                                                            documentsArr.externalUrl = '';
                                                        "
                                                        >Cancel
                                                    </v-btn>
                                                    <input
                                                        type="hidden"
                                                        v-model="documentsArr.enableExternalLink"
                                                    />
                                                </td>
                                                <td
                                                    style="margin: 0; padding: 0; font-size: 12px"
                                                    v-else
                                                >
                                                    <span class="form-input-text">
                                                        <a
                                                            :href="documentsArr.externalUrl"
                                                            target="_blank"
                                                            ><img
                                                                :src="
                                                                    assetDomain +
                                                                    'assets/images/icons/link_icon_blue.png'
                                                                "
                                                                class="icon"
                                                                style="width: 12px"
                                                            />Open Link</a
                                                        >
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </v-col>
                            </v-row>
                            <v-row
                                class="form-row"
                                v-if="documentsArr.includeOwnerReport"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Period Date:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <cirrus-icon-date-picker
                                        :size="'40'"
                                        :id="'documentPeriodDate' + documentsArr.index"
                                        v-model="documentsArr.documentPeriodDate"
                                        :edit_form="true"
                                    ></cirrus-icon-date-picker>
                                </v-col>
                            </v-row>

                            <v-row
                                class="form-row"
                                v-if="documentsArr.index !== 'New'"
                            >
                                <v-col
                                    xs="12"
                                    sm="3"
                                    md="3"
                                    class="form-label"
                                    >Archived:
                                </v-col>
                                <v-col
                                    xs="12"
                                    sm="9"
                                    md="9"
                                    class="form-input"
                                >
                                    <span class="form-input-text">
                                        <sui-checkbox v-model="documentsArr.archivedFile" />
                                    </span>
                                </v-col>
                            </v-row>

                            <v-row class="form-row">
                                <v-col
                                    xs="12"
                                    sm="2"
                                    md="2"
                                    class="form-label"
                                ></v-col>
                                <v-col
                                    xs="12"
                                    sm="10"
                                    md="10"
                                    class="form-input"
                                ></v-col>
                            </v-row>
                        </div>
                    </div>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalSubmitData()"
                        data-tooltip="CTR + ENTER"
                        color="success"
                        depressed
                        small
                        :loading="submitBtnLoading"
                        :disabled="submitBtnLoading"
                        :dark="isDarkMode()"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >check
                        </v-icon>
                        Save
                    </v-btn>
                    <v-btn
                        class="v-step-save-2-button"
                        @click="modalAddData()"
                        v-if="documentsArr.index === 'New'"
                        data-tooltip="CTR + ENTER"
                        color="warning"
                        dark
                        depressed
                        small
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >clear_all
                        </v-icon>
                        Clear
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import global_mixins from '../../plugins/mixins';
import { bus } from '../../plugins/bus';
import axios from 'axios';
import FileTreeComponent from '../components/FileTreeComponent.vue';
import {
    ARCHIVE_LABEL,
    COMPANY_MODULE_NAME,
    DOCUMENT_TYPES,
    IS_ARCHIVED,
    LEASE_MODULE_NAME,
    NOT_ARCHIVED,
    PROPERTY_MODULE_NAME,
    ROOT_DIRECTORY_NAME,
    STATUS_FILTER,
    TYPE_FILTER,
    UNARCHIVE_LABEL,
} from '../constants';
import { isDarkMode } from '../../utils/sharedFunctions';

axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');

export default {
    props: {
        reload_components: { type: Boolean, default: false },
        company_code: { type: String, default: '' },
        forceLoad: { type: Boolean, default: false },
        is_temp: { type: Boolean, default: false },
        action_parameter: { type: String, default: '' },
    },
    components: {
        'file-tree-component': FileTreeComponent,
    },
    data() {
        return {
            assetDomain: this.$assetDomain,
            errorServerMsg: {},
            errorServerMsg2: [],
            loadingSetting: true,
            edit_form: false,
            AddEditDeleteModal: false,
            documentsArr: [],
            readonly: false,
            showActivityLogModal: false,
            searchDatatable: '',
            directoryId: null,
            parentId: null,
            showNewModal: false,
            updateModal: false,
            showFileModal: false,
            newFolderName: '',
            newFolderArchived: false,
            documentListFilter: 0,
            folderList: [],
            filterToggle: false,
            folderPath: { directoryId: null, folderPath: ROOT_DIRECTORY_NAME },
            filterFolder: { directoryId: null, folderPath: 'All' },
            filterDocumentType: 0,
            filterSortBy: 0,
            filterSortType: 0,
            filterPublishToOwner: false,
            filterIncludeOwnRpt: false,
            filterPublishToTenant: false,
            addNewFolderPath: { directoryId: null, folderPath: ROOT_DIRECTORY_NAME },
            systemMessageFlag: false,
            externalFile: null,
            isArchived: IS_ARCHIVED,
            notArchived: NOT_ARCHIVED,
            rootDirectoryName: ROOT_DIRECTORY_NAME,
            propertyModuleName: PROPERTY_MODULE_NAME,
            leaseModuleName: LEASE_MODULE_NAME,
            companyModuleName: COMPANY_MODULE_NAME,
            archiveLabel: ARCHIVE_LABEL,
            unarchiveLabel: UNARCHIVE_LABEL,
            submitBtnLoading: false,
        };
    },
    mounted() {
        axios.defaults.headers.common['X-CSRF-TOKEN'] = $('meta[name="csrf-token"]').attr('content');
        this.loadingSetting = false;
        this.loadForm();
        if (this.action_parameter === 'review') this.edit_form = true;
        if (this.sys_ver_control_list.isFolderSystemOn) this.SET_DOC_ACTIVE_VERSION(1);
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_property_form_read_only',
            'doc_active_version',
            'sys_ver_control_list',
        ]),
    },
    methods: {
        ...mapMutations(['SET_DOC_ACTIVE_VERSION']),
        isDarkMode,
        isLiveData: function () {
            return !this.is_temp;
        },
        loadForm: function () {
            this.errorServerMsg = {};
            this.errorServerMsg2 = [];
            this.loadFolderList();
        },
        getIdOfUploadButton: function (id) {
            return 'fileUploadDocs_' + id;
        },
        openNewFolderModal: function () {
            this.showNewModal = true;
            this.addNewFolderPath = { directoryId: null, folderPath: this.rootDirectoryName };
            this.updateModal = false;
            this.newFolderName = '';
            this.newFolderArchived = false;
            this.errorServerMsg2 = [];
            this.directoryId = '';
            this.parentId = '';
        },
        submitNewFolder: function () {
            this.errorServerMsg2 = [];
            let newFolderName = this.newFolderName;
            let newFolderArchived = this.newFolderArchived;
            var formData = new FormData();
            formData.append('newFolderName', newFolderName);
            formData.append('newFolderArchived', newFolderArchived);
            formData.append('folderModule', this.companyModuleName);
            formData.append('parentId', this.addNewFolderPath.directoryId);
            formData.append('company_code', this.company_code);
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/create/document-folder';
            this.$api
                .post(apiUrl, formData)
                .then((response) => {
                    this.loadingSetting = false;
                    this.showNewModal = false;
                    this.updateModal = true;
                    this.loadFolderList();
                    this.loadForm();
                })
                .catch((error) => {
                    this.loadingSetting = false;
                    if (error.response && error.response.status === 422) {
                        const message = error.response.data.message;
                        this.errorServerMsg2.push([message]);
                        this.btn_loading = false;
                    }
                });
        },
        loadFolderList: function () {
            this.loadingSetting = true;
            var formData = new FormData();
            formData.append('folderModule', this.companyModuleName);
            formData.append('company_code', this.company_code);
            formData.append('no_load', true);
            let apiUrl = 'administration/document-folder/fetch/folder-list';

            this.$api.post(apiUrl, formData).then((response) => {
                this.folderList = response.data.folderList;
                this.folderList.unshift({ directoryId: '', folderPath: this.rootDirectoryName });
                this.loadingSetting = false;
            });
        },
        openNewDocumentModal: function () {
            this.errorServerMsg = {};
            this.errorServerMsg2 = [];
            this.showFileModal = false;
            this.AddEditDeleteModal = true;
            this.addNewFolderPath = { directoryId: null, folderPath: this.rootDirectoryName };
            this.documentsArr = {
                index: 'New',
                documentTitle: '',
                documentDescription: '',
                filename: null,
                filenameOld: null,
                documentId: '',
                documentPeriodDate: null,
                publishToOwner: false,
                includeOwnerReport: false,
                archivedFile: false,
                status: 'new',
                isExternal: 0,
                enableExternalLink: 0,
                externalUrl: '',
                folderType: 0,
            };
        },
        modalSubmitData: function () {
            let errorArr = [];
            let directoryId = this.documentsArr.directoryId;
            let documentId = this.documentsArr.documentId;
            let documentTitle = this.documentsArr.documentTitle;
            let documentDescription = this.documentsArr.documentDescription;
            let documentPeriodDate = this.documentsArr.documentPeriodDate;
            let publishToOwner = this.documentsArr.publishToOwner;
            let includeOwnerReport = this.documentsArr.includeOwnerReport;
            let archivedFile = this.documentsArr.archivedFile;
            let filename = this.documentsArr.filename;
            let filenameOld = this.documentsArr.filenameOld;
            let externalUrl = this.documentsArr.externalUrl;
            let enableExternalLink = this.documentsArr.enableExternalLink;
            let index = this.documentsArr.index;
            if (documentTitle === '') errorArr.push(['You have not entered a valid title for the document.']);
            if (documentDescription === '')
                errorArr.push(['You have not entered a valid description for the document.']);
            if (enableExternalLink === 0) {
                if (filename === null && index === 'New')
                    errorArr.push(['You have not entered a valid attachment for the document.']);
            } else {
                if (externalUrl === '' && index === 'New')
                    errorArr.push(['You have not entered a valid attachment for the document.']);
            }
            this.errorServerMsg2 = errorArr;
            if (this.errorServerMsg2.length === 0) {
                this.submitBtnLoading = true;

                var formData = new FormData();
                formData.append('company_code', this.company_code);
                formData.append('directory_id', this.addNewFolderPath.directoryId);
                formData.append('document_id', documentId);
                formData.append('document_title', documentTitle);
                formData.append('document_description', documentDescription);
                formData.append('document_period_date', documentPeriodDate);
                formData.append('publish_to_owner', publishToOwner);
                formData.append('include_owner_report', includeOwnerReport);
                formData.append('archived_file', archivedFile);
                formData.append('external_url', externalUrl);
                formData.append('filename', filename);
                formData.append('no_load', true);
                if (filename !== filenameOld) formData.append('docs_file', this.documentsArr.filename[0]);

                const dataStatusSlug = this.is_temp ? 'unapproved/' : '';
                const actionSlug = index === 'New' ? 'create/' : 'update/';
                const apiUrl = `with-file-upload/${dataStatusSlug}company/${actionSlug}single-document`;

                this.$api
                    .post(apiUrl, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.loadingSetting = false;
                        this.AddEditDeleteModal = false;
                        this.directoryId = null;
                        this.loadForm();
                        this.submitBtnLoading = false;
                    });
            }
        },
        applyFilters: function () {
            bus.$emit('regenerateTreeViewBasedOnFilter', this.filterHandler());
        },
        resetFilters: function () {
            this.filterFolder = { directoryId: null, folderPath: 'All' };
            this.filterDocumentType = 0;
            this.filterSortBy = 0;
            this.filterPublishToOwner = false;
            this.filterIncludeOwnRpt = false;
            this.filterPublishToTenant = false;
        },
        setActiveVersion: function (status) {
            this.SET_DOC_ACTIVE_VERSION(status);
        },
        filterHandler: function () {
            let documentListFilter = 'ACTIVE';
            let filterDocumentType = 'ALL';
            if (this.documentListFilter === STATUS_FILTER.ARCHIVED) documentListFilter = 'ARCHIVED';
            if (this.documentListFilter === STATUS_FILTER.ALL) documentListFilter = 'ALL';
            switch (this.filterDocumentType) {
                case TYPE_FILTER.OWNER_REPORT:
                    filterDocumentType = DOCUMENT_TYPES.OWNER_REPORT;
                    break;
                case TYPE_FILTER.PROPERTY_DOC:
                    filterDocumentType = DOCUMENT_TYPES.PROPERTY_DOCUMENT;
                    break;
                case TYPE_FILTER.PROPERTY_INSPECTION:
                    filterDocumentType = DOCUMENT_TYPES.PROPERTY_INSPECTION;
                    break;
            }
            return {
                documentStatus: documentListFilter,
                searchDatatable: this.searchDatatable,
                directoryId: this.filterFolder.directoryId,
                filterDocumentType: filterDocumentType,
                filterPublishToOwner: this.filterPublishToOwner,
                filterIncludeOwnRpt: this.filterIncludeOwnRpt,
                filterPublishToTenant: this.filterPublishToTenant,
                filterSortBy: this.filterSortBy,
                filterSortType: this.filterSortType,
            };
        },
    },
    watch: {
        property_code: function () {
            this.loadForm();
        },
        documentListFilter: function () {
            bus.$emit('regenerateTreeViewBasedOnFilter', this.filterHandler());
        },
        searchDatatable: function () {
            bus.$emit('regenerateTreeViewBasedOnFilter', this.filterHandler());
        },
        filterFolder: function () {
            if (this.filterFolder === null) this.filterFolder = { directoryId: null, folderPath: 'All' };
        },
        addNewFolderPath: function () {
            if (this.addNewFolderPath === null)
                this.addNewFolderPath = { directoryId: null, folderPath: this.rootDirectoryName };
            else {
                if (this.addNewFolderPath.folderPath) {
                    this.systemMessageFlag = this.addNewFolderPath.folderPath.split('/').length >= 3;
                } else this.systemMessageFlag = false;
            }
        },
    },
    created() {
        bus.$on('openNewDocumentModal', (data) => {
            this.errorServerMsg = {};
            this.errorServerMsg2 = [];
            this.showFileModal = false;

            this.addNewFolderPath = this.findObjectByProperty('directoryId', data.directoryId, this.folderList, {
                directoryId: null,
                folderPath: this.rootDirectoryName,
            });
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(data.fileNodes);
            this.externalFile = dataTransfer.files;
            this.documentsArr = {
                index: 'New',
                documentTitle: '',
                documentDescription: '',
                filename: '',
                filenameOld: null,
                documentId: '',
                documentPeriodDate: null,
                publishToOwner: false,
                includeOwnerReport: false,
                archivedFile: false,
                status: 'new',
                isExternal: 0,
                enableExternalLink: 0,
                externalUrl: '',
                folderType: 0,
            };
            this.AddEditDeleteModal = true;
        });
    },
    mixins: [global_mixins],
};
</script>
<style scoped>
.item-container {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.tooltip-content {
    font-size: 12px;
    color: #0087bf;
    border: 2px solid #0087bf;
    border-radius: 6px;
    padding: 8px;
}

.info-item {
    display: flex;
    margin-bottom: 4px;
}

.label {
    flex: 1;
    text-align: right;
    padding-right: 4px;
}

.separator {
    flex: 0;
}

.value {
    flex: 2;
    word-wrap: break-word;
}

.item-actions {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    align-items: center;
}

.fixed-width-16 {
    width: 16px;
}
</style>
