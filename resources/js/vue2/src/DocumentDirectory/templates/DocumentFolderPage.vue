<style>
.file_type-description-textbox {
    min-width: 300px !important;
}

.documents-table {
    border: 1px solid rgba(34, 36, 38, 0.15);
}

td.text-start,
td.text-end {
    padding-top: 0.4em !important;
    padding-bottom: 0.4em !important;
}

.documents-table .option {
    font-size: 21px !important;
}

ul.v-pagination button {
    height: 30px !important;
    min-width: 30px !important;
    min-height: 30px !important;
}

.documents-table button.v-icon {
    font-size: 21px;
}

.fixed-width-16 {
    width: 16px !important;
}

.document-div .v-treeview-node .v-treeview-node__root {
    min-height: 30px !important;
}

td > [data-tooltip] {
    font-size: 12px !important;
}
</style>
<template>
    <div class="c8-page page-form document-div">
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header title="Manage Documents Folder" />
            </v-toolbar-title>
        </v-toolbar>
        <span class="caption"
            >This page allows you to manage your Documents Folders and create and edit default Document Folders which
            will automatically be set up against the relevant pages</span
        >
        <template>
            <v-card>
                <v-tabs
                    class="cirrus-tab-theme"
                    style="border-radius: 0px"
                >
                    <v-tab style="justify-content: left !important">
                        <v-icon> mdi-domain</v-icon>
                        Property
                    </v-tab>
                    <v-tab style="justify-content: left !important">
                        <v-icon> mdi-account-multiple</v-icon>
                        Lease
                    </v-tab>
                    <v-tab style="justify-content: left !important">
                        <v-icon> mdi-lan</v-icon>
                        Company
                    </v-tab>

                    <v-tab-item>
                        <document-treeview-component module="PROPERTY"></document-treeview-component>
                    </v-tab-item>
                    <v-tab-item>
                        <document-treeview-component module="LEASE"></document-treeview-component>
                    </v-tab-item>
                    <v-tab-item>
                        <document-treeview-component module="COMPANY"></document-treeview-component>
                    </v-tab-item>
                </v-tabs>
            </v-card>
        </template>
    </div>
</template>
<script>
import global_mixins from '../../plugins/mixins';
import DocumentTreeviewSection from '../sections/DocumentTreeviewSection.vue';

const pause = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
export default {
    data() {
        return {
            loading_setting: false,
        };
    },
    components: {
        'document-treeview-component': DocumentTreeviewSection,
    },
    mounted() {},
    computed: {},
    methods: {},
    watch: {},
    created() {},
    mixins: [global_mixins],
};
</script>
<style lang="scss" scoped>
.md-tabs + .md-tabs {
    margin-top: 24px;
}
</style>
<style lang="scss" scoped>
small {
    display: block;
}
</style>
<style>
.disabled_style {
    background-color: #f5f5f5 !important;
}
</style>
