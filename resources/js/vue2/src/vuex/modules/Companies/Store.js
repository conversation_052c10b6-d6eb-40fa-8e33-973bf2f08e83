import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);
// Vue.mixins(mixin);

export default new Vuex.Store({
    state: {
        current_db: localStorage.getItem('currentDB'),
        user_type: localStorage.getItem('user_type'),
        username: localStorage.getItem('un'),
        cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
        http_host: localStorage.getItem('http_host'),
        dd_default_value: {
            value: '',
            label: 'Please select ...',
            fieldKey: '',
            fieldValue: 'Please select ...',
            field_key: '',
            field_value: 'Please select ...',
        },
        dd_property_list: [],
        dd_debtor_list: [],
        dd_company_list: [],
        dd_sub_ledger_type_list: [],
        property_code: '',
        dd_country_list: [],
        sys_ver_control_list: { isFolderSystemOn: false, isMultiplePropertyLedger: false },
        sms_sending_setup: false,
        doc_active_version: 0,
    },
    getters: {},
    mutations: {
        SET_DD_PROPERTY_LIST(state, property_code) {
            state.dd_property_list = property_code;
        },
        SET_PUSH_DD_PROPERTY_LIST(state, property_code) {
            $.map(state.dd_property_list, function (val, i) {
                if (i == 0)
                    $.map(val, function (aVal, i) {
                        if (i == 'fieldGroupValues' || i == 'field_group_values') {
                            aVal.push(property_code);
                        }
                    });
            });
            //console.log(state.dd_property_list);
            //state.dd_property_list.push(property_code);
        },

        SET_PROPERTY_CODE(state, property_code) {
            state.property_code = property_code;
        },

        SET_DD_DEBTOR_LIST(state, debtor_code) {
            state.dd_debtor_list = debtor_code;
        },
        SET_DEBTOR_CODE(state, debtor_code) {
            state.debtor_code = debtor_code;
        },

        SET_DD_COMPANY_LIST(state, company_code) {
            state.dd_company_list = company_code;
        },
        SET_PUSH_DD_COMPANY_LIST(state, company_code) {
            $.map(state.dd_company_list, function (val, i) {
                if (i == 0)
                    $.map(val, function (aVal, i) {
                        if (i == 'fieldGroupValues' || i == 'field_group_values') {
                            aVal.push(company_code);
                        }
                    });
            });
        },
        SET_COMPANY_CODE(state, company_code) {
            state.company_code = company_code;
        },

        SET_DD_SUB_LEDGER_TYPE_LIST(state, sub_ledger_type) {
            state.dd_sub_ledger_type_list = sub_ledger_type;
        },
        SET_SUB_LEDGER_TYPE_CODE(state, sub_ledger_type) {
            state.sub_ledger_type = sub_ledger_type;
        },

        SET_DD_COUNTRY_LIST(state, country_list) {
            state.dd_country_list = country_list;
        },
        SET_DD_PARAM_SMS_SENDING_SETUP(state, sms_sending_setup) {
            state.sms_sending_setup = sms_sending_setup;
        },
        SET_SYS_VER_CONTROL(state, sys_ver_control_list) {
            state.sys_ver_control_list = sys_ver_control_list;
        },
        SET_DOC_ACTIVE_VERSION(state, doc_active_version) {
            state.doc_active_version = doc_active_version;
        },
    },
    actions: {
        fetchDebtorList(context) {
            Vue.prototype.$api.post('debtor-dropdown-list', {}).then((response) => {
                context.commit('SET_DD_DEBTOR_LIST', response.data);
            });
        },

        fetchCompanyList(context) {
            Vue.prototype.$api.post('company-group-dropdown-list', {}).then((response) => {
                context.commit('SET_DD_COMPANY_LIST', response.data.data);
            });
        },

        fetchCountryList(context) {
            Vue.prototype.$api.post('loadAPICountriesDropDownListRedis', {}).then((response) => {
                context.commit('SET_DD_COUNTRY_LIST', response.data.countryList);
            });
        },

        fetchParamSMSSendingSetup(context) {
            Vue.prototype.$api.post('ui/fetch/param-sms-sending-setup').then((response) => {
                context.commit('SET_DD_PARAM_SMS_SENDING_SETUP', response.data.param_value);
            });
        },
        fetchFormVersionControl(context) {
            Vue.prototype.$api.post('ui/fetch/system-form-version-control', {}).then((response) => {
                context.commit('SET_SYS_VER_CONTROL', response.data);
                if (response.data.is_folder_sys_on) context.commit('SET_DOC_ACTIVE_VERSION', 1);
            });
        },
    },
});
