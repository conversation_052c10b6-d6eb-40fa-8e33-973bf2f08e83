import Vue from 'vue';
import Vuex from 'vuex';
import { objectClone } from '../../../plugins/mixins';

Vue.use(Vuex);
// Vue.mixins(mixin);

export default new Vuex.Store({
    state: {
        current_db: localStorage.getItem('currentDB'),
        user_type: localStorage.getItem('user_type'),
        username: localStorage.getItem('un'),
        cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
        http_host: localStorage.getItem('http_host'),
        clientTimezone: localStorage.getItem('clientTimezone'),
        dd_property_list: [],
        dd_default_value: {
            value: '',
            label: 'Please select ...',
            fieldKey: '',
            fieldValue: 'Please select ...',
            field_key: '',
            field_value: 'Please select ...',
        },
    },
    getters: {},
    mutations: {
        SET_DD_PROPERTY_LIST(state, dd_property_list) {
            state.dd_property_list = dd_property_list;
        },
    },
    actions: {
        fetchPropertyList(context) {
            //this.$api.post('vue/loadAPIPropertyDropDownListRedis', {
            Vue.prototype.$api.post('vue/loadAPIPropertyPerPortfolioMgrDropDownListRedis', {}).then((response) => {
                context.commit('SET_DD_PROPERTY_LIST', response.data.data);
            });
        },
        fetchPropertyListByDefaultReport(context) {
            Vue.prototype.$api.post('vue/loadAPIPropertyPerDefReportTypeDropDownListRedis', {}).then((response) => {
                context.commit('SET_DD_PROPERTY_LIST', response.data.data);
            });
        },
    },
});
