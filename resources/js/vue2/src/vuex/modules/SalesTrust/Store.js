import Vue from 'vue';
import Vuex from 'vuex';
import { objectClone } from '../../../plugins/mixins';

Vue.use(Vuex);
// Vue.mixins(mixin);

export default new Vuex.Store({
    state: {
        current_db: localStorage.getItem('currentDB'),
        user_type: localStorage.getItem('user_type'),
        username: localStorage.getItem('un'),
        cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
        http_host: localStorage.getItem('http_host'),
        dd_default_value: {
            value: '',
            label: 'Please select ...',
            fieldKey: '',
            fieldValue: 'Please select ...',
            field_key: '',
            field_value: 'Please select ...',
        },
        dd_property_list: [],
        dd_debtor_list: [],
        dd_company_list: [],
        dd_sub_ledger_type_list: [],
        property_code: '',
        dd_country_list: [],
        dd_owner_company_list: [],
    },
    getters: {},
    mutations: {
        SET_DD_PROPERTY_LIST(state, property_code) {
            state.dd_property_list = property_code;
        },
        SET_PUSH_DD_PROPERTY_LIST(state, property_code) {
            $.map(state.dd_property_list, function (val, i) {
                if (i == 0)
                    $.map(val, function (aVal, i) {
                        if (i == 'fieldGroupValues' || i == 'field_group_values') {
                            aVal.push(property_code);
                        }
                    });
            });
            //console.log(state.dd_property_list);
            //state.dd_property_list.push(property_code);
        },

        SET_PROPERTY_CODE(state, property_code) {
            state.property_code = property_code;
        },

        SET_DD_DEBTOR_LIST(state, debtor_code) {
            state.dd_debtor_list = debtor_code;
        },
        SET_DEBTOR_CODE(state, debtor_code) {
            state.debtor_code = debtor_code;
        },

        SET_DD_COMPANY_LIST(state, company_code) {
            let count = Object.keys(company_code).length;
            let result = [];

            for (let key = 0; key <= count; key++) {
                if (company_code[key] != null) {
                    result.push(company_code[key]);
                }
            }

            state.dd_company_list = result;
        },
        SET_COMPANY_CODE(state, company_code) {
            state.company_code = company_code;
        },

        SET_DD_SUB_LEDGER_TYPE_LIST(state, sub_ledger_type) {
            let count = Object.keys(sub_ledger_type).length;
            let result = [];

            for (let key = 0; key <= count; key++) {
                if (sub_ledger_type[key] != null) {
                    result.push(sub_ledger_type[key]);
                }
            }

            state.dd_sub_ledger_type_list = result;
        },
        SET_SUB_LEDGER_TYPE_CODE(state, sub_ledger_type) {
            state.sub_ledger_type = sub_ledger_type;
        },

        SET_DD_COUNTRY_LIST(state, country_list) {
            state.dd_country_list = country_list;
        },
        SET_DD_OWNER_COMPANY_LIST(state, dd_owner_company_list) {
            state.dd_owner_company_list = dd_owner_company_list;
        },
    },
    actions: {
        fetchPropertyList(context) {
            Vue.prototype.$api.post('ledger-group-dropdown-list', {}).then((response) => {
                context.commit('SET_DD_PROPERTY_LIST', response.data.data);
            });
        },
        fetchDebtorList(context) {
            Vue.prototype.$api.post('debtor-dropdown-list', {}).then((response) => {
                context.commit('SET_DD_DEBTOR_LIST', response.data);
            });
        },

        fetchCompanyList(context) {
            Vue.prototype.$api.post('company-dropdown-list', {}).then((response) => {
                context.commit('SET_DD_COMPANY_LIST', response.data);
            });
        },

        fetchSubLedgerTypeList(context) {
            Vue.prototype.$api.post('ui/fetch/param-sub-ledger-type-list', {}).then((response) => {
                context.commit('SET_DD_SUB_LEDGER_TYPE_LIST', response.data);
            });
        },

        fetchCountryList(context) {
            Vue.prototype.$api.post('loadAPICountriesDropDownListRedis', {}).then((response) => {
                context.commit('SET_DD_COUNTRY_LIST', response.data.countryList);
            });
        },
        fetchOwnerCompanyList(context) {
            Vue.prototype.$api.post('owner-dropdown-list', {}).then((response) => {
                context.commit('SET_DD_OWNER_COMPANY_LIST', response.data);
            });
        },
    },
});
