import Vue from 'vue';
import Vuex from 'vuex';
import { objectClone } from '../../../plugins/mixins';

Vue.use(Vuex);
// Vue.mixins(mixin);

export default new Vuex.Store({
    state: {
        current_db: localStorage.getItem('currentDB'),
        user_type: localStorage.getItem('user_type'),
        username: localStorage.getItem('un'),
        cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
        http_host: localStorage.getItem('http_host'),
        dd_default_value: {
            value: '',
            label: 'Please select ...',
            fieldKey: '',
            fieldValue: 'Please select ...',
            field_key: '',
            field_value: 'Please select ...',
        },
        dd_property_list: [],
        property_code: '',
        dd_account_list: [],
        dd_account_group_list: [],
        dd_meter_type_list: [],
        dd_charges_type_list: [],
    },
    getters: {},
    mutations: {
        SET_DD_PROPERTY_LIST(state, property_code) {
            state.dd_property_list = property_code;
        },
        SET_PUSH_DD_PROPERTY_LIST(state, property_code) {
            $.map(state.dd_property_list, function (val, i) {
                if (i == 0)
                    $.map(val, function (aVal, i) {
                        if (i == 'fieldGroupValues' || i == 'field_group_values') {
                            aVal.push(property_code);
                        }
                    });
            });
        },

        SET_PROPERTY_CODE(state, property_code) {
            state.property_code = property_code;
        },

        SET_DD_METER_TYPE_LIST(state, meter_type) {
            state.dd_meter_type_list = meter_type;
        },

        SET_DD_CHARGES_TYPE_LIST(state, charges_type) {
            state.dd_charges_type_list = charges_type;
        },

        SET_DD_ACCOUNT_LIST(state, account_list) {
            state.dd_account_list = account_list;
        },

        SET_DD_ACCOUNT_GROUP_LIST(state, account_group_list) {
            state.dd_account_group_list = account_group_list;
        },
    },
    actions: {
        fetchPropertyList(context) {
            Vue.prototype.$api
                .post('loadAPIPropertyDropDownList', {
                    active_only: true,
                })
                .then((response) => {
                    context.commit('SET_DD_PROPERTY_LIST', response.data.data);
                });
        },

        fetchUtilityMeterTypeList(context) {
            Vue.prototype.$api.post('utility-meter/utility-meter-type-list', {}).then((response) => {
                context.commit('SET_DD_METER_TYPE_LIST', response.data);
            });
        },

        fetchUtilityChargesTypeList(context) {
            Vue.prototype.$api.post('utility-meter/utility-charges-type-list', {}).then((response) => {
                context.commit('SET_DD_CHARGES_TYPE_LIST', response.data);
            });
        },

        fetchAccountList(context) {
            Vue.prototype.$api.post('utility-meter/utility-account-list', {}).then((response) => {
                context.commit('SET_DD_ACCOUNT_LIST', response.data);
            });
        },

        fetchAccountGroupList(context) {
            Vue.prototype.$api.post('utility-meter/utility-account-group-list', {}).then((response) => {
                context.commit('SET_DD_ACCOUNT_GROUP_LIST', response.data.data);
            });
        },
    },
});
