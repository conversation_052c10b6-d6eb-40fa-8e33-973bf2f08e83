import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

export default new Vuex.Store({
    state: {
        current_db: localStorage.getItem('currentDB'),
        user_type: localStorage.getItem('user_type'),
        username: localStorage.getItem('un'),
        cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
        dd_default_value: {
            value: '',
            label: 'Please select ...',
            fieldKey: '',
            fieldValue: 'Please select ...',
            field_key: '',
            field_value: 'Please select ...',
        },
        property_code: '',
        property_details: {},
        lease_code: '',
        lease_details: {},
        lease_details_old: {},
        version_id: 1,
        tour_steps: [],
        dd_country_list: [],
        dd_account_income_grouped_list: [],
        dd_account_income_ungrouped_list: [],
        dd_bond_deposit_property_list: [],
        dd_param_division_list: [],
        dd_param_lease_type_list: [],
        dd_param_tenant_type_list: [],
        dd_retail_category_list: [],
        dd_retail_sub_category_list: [],
        dd_retail_fine_category_list: [],
    },
    getters: {
        getDDCountryStates: (state) => (country_code) => {
            let return_data = [];
            if (country_code !== '') {
                let get_filtered = state.dd_country_list.filter((m) => m.field_key === country_code);
                if (get_filtered.length > 0) {
                    return_data = get_filtered[0].state_list;
                }
            }
            return return_data;
        },
        getLeaseDetails: (state) => {
            return state.lease_details;
        },
    },
    mutations: {
        SET_LEASE_DETAILS: (state, lease_details) => {
            state.lease_details = lease_details;
        },
        SET_LEASE_OLD_DETAILS: (state, lease_details_old) => {
            state.lease_details_old = lease_details_old;
        },
        SET_PROPERTY_CODE(state, property_code) {
            state.property_code = property_code;
        },
        SET_LEASE_CODE(state, lease_code) {
            state.lease_code = lease_code;
        },
        SET_VERSION_ID(state, version_id) {
            state.version_id = version_id;
        },
        SET_TOUR_STEPS(state, tour_steps) {
            state.tour_steps = tour_steps;
        },
        SET_DD_COUNTRY_LIST(state, dd_country_list) {
            state.dd_country_list = dd_country_list;
        },
        SET_DD_ACCOUNT_GROUPED_LIST(state, dd_account_income_grouped_list) {
            state.dd_account_income_grouped_list = dd_account_income_grouped_list;
        },
        SET_DD_ACCOUNT_UNGROUPED_LIST(state, dd_account_income_ungrouped_list) {
            state.dd_account_income_ungrouped_list = dd_account_income_ungrouped_list;
        },
        SET_DD_BOND_DEP_PROPERTY_LIST(state, dd_bond_deposit_property_list) {
            state.dd_bond_deposit_property_list = dd_bond_deposit_property_list;
        },
        SET_DD_PARAM_DIVISION_LIST(state, dd_param_division_list) {
            state.dd_param_division_list = dd_param_division_list;
        },
        SET_DD_PARAM_LEASE_TYPE_LIST(state, dd_param_lease_type_list) {
            state.dd_param_lease_type_list = dd_param_lease_type_list;
        },
        SET_DD_PARAM_TENANT_TYPE_LIST(state, dd_param_tenant_type_list) {
            state.dd_param_tenant_type_list = dd_param_tenant_type_list;
        },
        SET_DD_RETAIL_CATEGORY_LIST(state, dd_retail_category_list) {
            state.dd_retail_category_list = dd_retail_category_list;
        },
        SET_DD_RETAIL_SUB_CATEGORY_LIST(state, dd_retail_sub_category_list) {
            state.dd_retail_sub_category_list = dd_retail_sub_category_list;
        },
        SET_DD_RETAIL_FINE_CATEGORY_LIST(state, dd_retail_fine_category_list) {
            state.dd_retail_fine_category_list = dd_retail_fine_category_list;
        },
    },
    actions: {
        fetchCountryList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/country-with-states-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_COUNTRY_LIST', response.data.country_list);
                });
        },
        fetchAccountIncomeGroupedList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/income-accounts-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_ACCOUNT_GROUPED_LIST', response.data.grouped);
                    context.commit('SET_DD_ACCOUNT_UNGROUPED_LIST', response.data.ungrouped);
                });
        },
        fetchBondPropertyList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/bond-property-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_BOND_DEP_PROPERTY_LIST', response.data);
                });
        },
        fetchParamDivisionList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-division-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_PARAM_DIVISION_LIST', response.data);
                });
        },
        fetchParamLeaseTypeList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-lease-type-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_PARAM_LEASE_TYPE_LIST', response.data);
                });
        },
        fetchParamTenantTypeList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-tenant-type-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_PARAM_TENANT_TYPE_LIST', response.data);
                });
        },
        fetchRetailCategoryList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/retail-category-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_RETAIL_CATEGORY_LIST', response.data);
                });
        },
        fetchRetailSubCategoryList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/retail-sub-category-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_RETAIL_SUB_CATEGORY_LIST', response.data);
                });
        },
        fetchRetailFineCategoryList(context) {
            axios
                .post(
                    localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/retail-fine-category-list',
                    {
                        current_db: localStorage.getItem('currentDB'),
                        user_type: localStorage.getItem('user_type'),
                        un: localStorage.getItem('un'),
                    },
                    { headers: { 'Content-Type': 'application/json' } },
                )
                .then((response) => {
                    context.commit('SET_DD_RETAIL_FINE_CATEGORY_LIST', response.data);
                });
        },
    },
});
