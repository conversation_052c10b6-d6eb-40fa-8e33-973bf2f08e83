import Vue from 'vue';
import Vuex from 'vuex';
import { objectClone } from '../../../plugins/mixins';

Vue.use(Vuex);
// Vue.mixins(mixin);

export default new Vuex.Store({
    state: {
        current_db: localStorage.getItem('currentDB'),
        user_type: localStorage.getItem('user_type'),
        username: localStorage.getItem('un'),
        cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
        http_host: localStorage.getItem('http_host'),
        dd_default_value: {
            value: '',
            label: 'Please select ...',
            fieldKey: '',
            fieldValue: 'Please select ...',
            field_key: '',
            field_value: 'Please select ...',
        },
        dd_category_list: [],
        dd_assignee_list: [],
        dd_property_list: [],
        dd_status_list: [],
        dd_company_list: [],
        dd_steps_list: [],
    },
    getters: {},
    mutations: {
        SET_DD_CATEGORY_LIST(state, category_list) {
            state.dd_category_list = category_list;
        },
        SET_DD_ASSIGNEE_LIST(state, assigned_list) {
            state.dd_assignee_list = assigned_list;
        },
        SET_DD_PROPERTY_LIST(state, property_list) {
            state.dd_property_list = property_list;
        },
        SET_DD_STATUS_LIST(state, status_list) {
            state.dd_status_list = status_list;
        },
        SET_DD_COMPANY_LIST(state, company_list) {
            state.dd_company_list = company_list;
        },
        SET_DD_STEPS_LIST(state, steps_list) {
            state.dd_steps_list = steps_list;
        },
    },
    actions: {
        fetchCategoryList(context) {
            Vue.prototype.$api.post('ui/fetch/param-task-category-list', {}).then((response) => {
                context.commit('SET_DD_CATEGORY_LIST', response.data);
            });
        },

        fetchAssigneeList(context) {
            Vue.prototype.$api.post('task-management/get-assignee', {}).then((response) => {
                context.commit('SET_DD_ASSIGNEE_LIST', response.data);
            });
        },

        fetchPropertyList(context) {
            Vue.prototype.$api.post('loadAPIPropertyDropDownList', {}).then((response) => {
                context.commit('SET_DD_PROPERTY_LIST', response.data.data);
            });
        },

        fetchStatusList(context) {
            Vue.prototype.$api.post('task-management/get-status', {}).then((response) => {
                context.commit('SET_DD_STATUS_LIST', response.data);
            });
        },

        fetchCompanyList(context) {
            Vue.prototype.$api
                .post('company-dropdown-list', {
                    show_active: 1,
                })
                .then((response) => {
                    context.commit('SET_DD_COMPANY_LIST', response.data);
                });
        },

        fetchStepsList(context) {
            Vue.prototype.$api.post('task-management/get-steps-option', {}).then((response) => {
                context.commit('SET_DD_STEPS_LIST', response.data);
            });
        },
    },
});
