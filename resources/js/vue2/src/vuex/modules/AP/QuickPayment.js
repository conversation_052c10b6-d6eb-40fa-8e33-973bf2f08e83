import Vue from 'vue';
import Vuex from 'vuex';
import { objectClone } from '../../../plugins/mixins';

Vue.use(Vuex);
// Vue.mixins(mixin);

export default new Vuex.Store({
    state: {
        cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
        currentDB: localStorage.getItem('currentDB'),
        user_type: localStorage.getItem('user_type'),
        un: localStorage.getItem('un'),
        http_host: localStorage.getItem('http_host'),
        dd_default_value: {
            value: '',
            label: 'Please select ...',
            fieldKey: '',
            fieldValue: 'Please select ...',
            field_key: '',
            field_value: 'Please select ...',
        },
    },
    getters: {},
    mutations: {},
    actions: {},
});
