import Vue from 'vue';
import Vuex from 'vuex';
import { objectClone } from '../../../plugins/mixins';
import { requestFromApi } from '../../../plugins/request';

Vue.use(Vuex);
// Vue.mixins(mixin);

export default new Vuex.Store({
    state: {
        current_db: localStorage.getItem('currentDB'),
        user_type: localStorage.getItem('user_type'),
        username: localStorage.getItem('un'),
        cirrus8_api_url: localStorage.getItem('cirrus8_api_url'),
        http_host: localStorage.getItem('http_host'),
        client_timezone: localStorage.getItem('clientTimezone'),
        dd_default_value: {
            value: '',
            label: 'Please select ...',
            fieldKey: '',
            fieldValue: 'Please select ...',
            field_key: '',
            field_value: 'Please select ...',
        },
        property_code: '',
        property_details: {},
        lease_code: '',
        lease_details: { unit_details: { lease_unit_code: '' } },
        lease_details_old: {},
        version_id: 1,
        tour_steps: [],
        pm_property_form_read_only: [],
        client_new_property_access: false,
        lease_profile: [],
        dd_company_list: [],
        dd_country_list: [],
        dd_account_grouped_list: [],
        dd_account_ungrouped_list: [],
        dd_account_income_grouped_list: [],
        dd_account_income_ungrouped_list: [],
        dd_account_expenses_grouped_list: [],
        dd_account_expenses_ungrouped_list: [],
        dd_bond_deposit_property_list: [],
        dd_param_division_list: [],
        dd_param_lease_type_list: [],
        dd_param_lease_tenant_type_list: [],
        dd_param_trust_accountant_list: [],
        dd_param_trust_accountant_default: {},
        dd_param_property_manager_list: [],
        dd_retail_category_list: [],
        dd_retail_sub_category_list: [],
        dd_retail_fine_category_list: [],
        sys_ver_control_list: { isFolderSystemOn: false, isMultiplePropertyLedger: false },
        auto_diarise: false,
        sms_sending_setup: false,
        email_cen_setup: false,
        server_date_today: '',
        doc_active_version: 0,
        property_ledger_list: [],
    },
    getters: {
        getDDCountryStates: (state) => (country_code) => {
            let return_data = [];
            if (country_code !== '') {
                let get_filtered = state.dd_country_list.filter((m) => m.field_key === country_code);
                if (get_filtered.length > 0) {
                    return_data = get_filtered[0].state_list;
                }
            }
            return return_data;
        },
        getLeaseDetails: (state) => {
            return state.lease_details;
        },
    },
    mutations: {
        SET_PROPERTY_CODE(state, property_code) {
            state.property_code = property_code;
        },
        SET_LEASE_CODE(state, lease_code) {
            state.lease_code = lease_code;
        },
        SET_VERSION_ID(state, version_id) {
            state.version_id = version_id;
        },
        SET_PM_PROPERTY_FORM_READ_ONLY(state, pm_property_form_read_only) {
            state.pm_property_form_read_only = pm_property_form_read_only;
        },
        SET_PM_NEW_PROPERTY_ACCESS(state, client_new_property_access) {
            state.client_new_property_access = client_new_property_access;
        },
        SET_DD_PARAM_LEASE_PROFILE(state, lease_profile) {
            state.lease_profile = lease_profile;
        },
        SET_TOUR_STEPS(state, tour_steps) {
            state.tour_steps = tour_steps;
        },
        SET_LEASE_MAIN_DETAILS: (state, lease_details) => {
            state.lease_details.main_form = objectClone(lease_details);
        },
        SET_LEASE_OLD_MAIN_DETAILS: (state, lease_details_old) => {
            state.lease_details_old.main_form = objectClone(lease_details_old);
        },
        SET_LEASE_UNIT_DETAILS: (state, lease_details) => {
            state.lease_details.unit_details = objectClone(lease_details);
            state.lease_details_old.unit_details = objectClone(lease_details);
        },
        SET_LEASE_CHARGE_DETAILS: (state, lease_details) => {
            state.lease_details.unit_charge_details = objectClone(lease_details);
            state.lease_details_old.unit_charge_details = objectClone(lease_details);
        },
        SET_LEASE_CONTACT_DETAILS: (state, lease_details) => {
            state.lease_details.contact_details = objectClone(lease_details);
        },
        SET_LEASE_OLD_CONTACT_DETAILS: (state, lease_details) => {
            state.lease_details_old.contact_details = objectClone(lease_details);
        },
        SET_LEASE_DIARY: (state, parameter) => {
            state.lease_details.diaries = objectClone(parameter);
            state.lease_details_old.diaries = objectClone(parameter);
        },
        SET_DD_COMPANY_LIST(state, dd_company_list) {
            state.dd_company_list = dd_company_list;
        },
        SET_DD_COUNTRY_LIST(state, dd_country_list) {
            state.dd_country_list = dd_country_list;
        },
        SET_DD_ACCOUNT_GROUPED_LIST(state, dd_account_grouped_list) {
            state.dd_account_grouped_list = dd_account_grouped_list;
        },
        SET_DD_ACCOUNT_UNGROUPED_LIST(state, dd_account_ungrouped_list) {
            state.dd_account_ungrouped_list = dd_account_ungrouped_list;
        },
        SET_DD_ACCOUNT_INV_GROUPED_LIST(state, dd_account_income_grouped_list) {
            state.dd_account_income_grouped_list = dd_account_income_grouped_list;
        },
        SET_DD_ACCOUNT_INV_UNGROUPED_LIST(state, dd_account_income_ungrouped_list) {
            state.dd_account_income_ungrouped_list = dd_account_income_ungrouped_list;
        },
        SET_DD_BOND_DEP_PROPERTY_LIST(state, dd_bond_deposit_property_list) {
            state.dd_bond_deposit_property_list = dd_bond_deposit_property_list;
        },
        SET_DD_PARAM_DIVISION_LIST(state, dd_param_division_list) {
            state.dd_param_division_list = dd_param_division_list;
        },
        SET_DD_PARAM_LEASE_TYPE_LIST(state, dd_param_lease_type_list) {
            state.dd_param_lease_type_list = dd_param_lease_type_list;
        },
        SET_DD_PARAM_TENANT_TYPE_LIST(state, dd_param_lease_tenant_type_list) {
            state.dd_param_lease_tenant_type_list = dd_param_lease_tenant_type_list;
        },
        SET_DD_PARAM_TA_LIST(state, dd_param_trust_accountant_list) {
            state.dd_param_trust_accountant_list = dd_param_trust_accountant_list.param_list;
            state.dd_param_trust_accountant_default = dd_param_trust_accountant_list.param_default;
        },
        SET_DD_PARAM_PM_LIST(state, dd_param_property_manager_list) {
            state.dd_param_property_manager_list = dd_param_property_manager_list;
        },
        SET_DD_RETAIL_CATEGORY_LIST(state, dd_retail_category_list) {
            state.dd_retail_category_list = dd_retail_category_list;
        },
        SET_DD_RETAIL_SUB_CATEGORY_LIST(state, dd_retail_sub_category_list) {
            state.dd_retail_sub_category_list = dd_retail_sub_category_list;
        },
        SET_DD_RETAIL_FINE_CATEGORY_LIST(state, dd_retail_fine_category_list) {
            state.dd_retail_fine_category_list = dd_retail_fine_category_list;
        },
        SET_DATE_TODAY(state, date_today_formatted) {
            state.server_date_today = date_today_formatted;
        },
        SET_AUTO_DIARISE(state, auto_diarise) {
            state.auto_diarise = auto_diarise;
        },
        SET_DD_PARAM_EMAIL_CEN_SETUP(state, lease_profile) {
            state.email_cen_setup = lease_profile;
        },
        SET_DD_PARAM_SMS_SENDING_SETUP(state, sms_sending_setup) {
            state.sms_sending_setup = sms_sending_setup;
        },
        SET_SYS_VER_CONTROL(state, sys_ver_control_list) {
            state.sys_ver_control_list = sys_ver_control_list;
        },
        SET_DOC_ACTIVE_VERSION(state, doc_active_version) {
            state.doc_active_version = doc_active_version;
        },
        SET_PROPERTY_LEDGER_LIST(state, list) {
            state.property_ledger_list = list;
        },
    },
    actions: {
        fetchLeaseDetails(context) {},
        fetchCompanyList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/dropDowns/companies')
                .then((response) => {
                    context.commit('SET_DD_COMPANY_LIST', response.data.data);
                });
        },
        fetchCountryList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/country-with-states-list')
                .then((response) => {
                    context.commit('SET_DD_COUNTRY_LIST', response.data.country_list);
                });
        },
        fetchAccountIncomeGroupedList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/income-accounts-list')
                .then((response) => {
                    context.commit('SET_DD_ACCOUNT_INV_GROUPED_LIST', response.data.grouped);
                    context.commit('SET_DD_ACCOUNT_INV_UNGROUPED_LIST', response.data.ungrouped);
                });
        },
        fetchAccountList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/accounts-list')
                .then((response) => {
                    context.commit('SET_DD_ACCOUNT_GROUPED_LIST', response.data.grouped);
                    context.commit('SET_DD_ACCOUNT_UNGROUPED_LIST', response.data.ungrouped);
                });
        },
        fetchBondPropertyList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/bond-property-list')
                .then((response) => {
                    context.commit('SET_DD_BOND_DEP_PROPERTY_LIST', response.data.param_list);
                });
        },
        fetchParamDivisionList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-division-list')
                .then((response) => {
                    context.commit('SET_DD_PARAM_DIVISION_LIST', response.data.param_list);
                });
        },
        fetchParamLeaseTypeList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-lease-type-list')
                .then((response) => {
                    context.commit('SET_DD_PARAM_LEASE_TYPE_LIST', response.data.param_list);
                });
        },
        fetchParamTenantTypeList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-tenant-type-list')
                .then((response) => {
                    context.commit('SET_DD_PARAM_TENANT_TYPE_LIST', response.data.param_list);
                });
        },
        fetchParamTAList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-ta-property-list')
                .then((response) => {
                    context.commit('SET_DD_PARAM_TA_LIST', response.data.param_list);
                });
        },
        fetchParamPMList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-pm-list')
                .then((response) => {
                    context.commit('SET_DD_PARAM_PM_LIST', response.data.param_list);
                });
        },
        fetchRetailCategoryList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/retail-category-list')
                .then((response) => {
                    context.commit('SET_DD_RETAIL_CATEGORY_LIST', response.data.param_list);
                });
        },
        fetchRetailSubCategoryList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/retail-sub-category-list')
                .then((response) => {
                    context.commit('SET_DD_RETAIL_SUB_CATEGORY_LIST', response.data.param_list);
                });
        },
        fetchRetailFineCategoryList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/retail-fine-category-list')
                .then((response) => {
                    context.commit('SET_DD_RETAIL_FINE_CATEGORY_LIST', response.data.param_list);
                });
        },
        fetchPMPropertyFormReadOnly(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/client-property-form-permission')
                .then((response) => {
                    if (localStorage.getItem('user_type') === 'A') {
                        context.commit('SET_PM_NEW_PROPERTY_ACCESS', true);
                    } else {
                        context.commit('SET_PM_PROPERTY_FORM_READ_ONLY', response.data.form_read_only_access_data);
                        context.commit('SET_PM_NEW_PROPERTY_ACCESS', response.data.client_new_property_access);
                    }
                });
        },
        fetchParameterDropdownList(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/client-lease-form-permission')
                .then((response) => {
                    context.commit('SET_PM_LEASE_FORM_READ_ONLY', response.data.form_read_only_access_data);
                });
        },
        fetchTAAutoDiarise(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/parameter/fetch/ta-property-auto-diarise')
                .then((response) => {
                    context.commit('SET_AUTO_DIARISE', response.data.auto_diarise);
                });
        },
        fetchPMAutoDiarise(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/parameter/fetch/pm-property-auto-diarise')
                .then((response) => {
                    context.commit('SET_AUTO_DIARISE', response.data.auto_diarise);
                });
        },
        fetchParamEmailCenSetup(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-email-centralisation-setup')
                .then((response) => {
                    context.commit('SET_DD_PARAM_EMAIL_CEN_SETUP', response.data.param_value);
                });
        },
        fetchParamSMSSendingSetup(context) {
            Vue.prototype.$api
                .post(localStorage.getItem('cirrus8_api_url') + 'api/ui/fetch/param-sms-sending-setup')
                .then((response) => {
                    context.commit('SET_DD_PARAM_SMS_SENDING_SETUP', response.data.param_value);
                });
        },
        fetchDateToday(context) {
            Vue.prototype.$api.post(localStorage.getItem('cirrus8_api_url') + 'api/get-date-today').then((response) => {
                context.commit('SET_DATE_TODAY', response.data.date_today_formatted);
            });
        },
        fetchFormVersionControl(context) {
            Vue.prototype.$api.post('ui/fetch/system-form-version-control', {}).then((response) => {
                context.commit('SET_SYS_VER_CONTROL', response.data);
                if (response.data.is_folder_sys_on) context.commit('SET_DOC_ACTIVE_VERSION', 1);
            });
        },
        fetchPropertyLedgerList(context) {
            requestFromApi.post('property/ledger/load', {}).then((response) => {
                let data = response.data;
                let list = data.map((item) => ({
                    field_key: item.propertyLedgerCode,
                    field_value: item.description,
                }));
                context.commit('SET_PROPERTY_LEDGER_LIST', list);
            });
        },
    },
});
