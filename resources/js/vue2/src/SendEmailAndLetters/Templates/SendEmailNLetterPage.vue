<template>
    <div class="c8-page page-form">
        <v-toolbar flat>
            <v-toolbar-title>
                <cirrus-page-header
                    :title="page_title"
                    :subtitle="page_subtitle"
                />
            </v-toolbar-title>
            <v-spacer></v-spacer>
        </v-toolbar>
        <v-expansion-panels v-if="success_email_list.length > 0">
            <v-expansion-panel class="email-success v-alert--text success--text">
                <v-expansion-panel-header>Successfully sent</v-expansion-panel-header>
                <v-expansion-panel-content>
                    <div
                        v-for="(data, index) in success_email_list"
                        :key="index"
                    >
                        {{ data }}
                    </div>
                </v-expansion-panel-content>
            </v-expansion-panel>
        </v-expansion-panels>
        <br />
        <cirrus-server-error :error_msg="error_server_msg"></cirrus-server-error>
        <cirrus-loader v-if="loading_page_setting"></cirrus-loader>
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Search Method
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="search_method"
                        :options="search_method_list"
                        ref="refSearchMethod"
                        trackBy="field_key"
                        label="field_value"
                        return="field_key"
                        placeholder="Please select..."
                    />
                </v-col>
            </v-row>
        </div>
        <property-owner-param-section
            v-if="search_method === letter_method_owner_key"
            :search_method="search_method"
            @update:mergeTag="updateMergeTag"
            @update:loading="updateLoading"
            @update:loadLetterContent="loadLetterContent"
            @update:loadPdfContent="loadPdfContent"
            @update:updateSubject="updateSubject"
            @update:updateCustomEmailList="updateCustomEmailList"
            @update:updateFromSection="updateFromSection"
            :country_default_settings="initialCountryDefaultSettings"
        />
        <property-owner-budget-param-section
            v-if="search_method === letter_method_owner_budget_key"
            :search_method="search_method"
            @update:mergeTag="updateMergeTag"
            @update:loading="updateLoading"
            @update:loadLetterContent="loadLetterContent"
            @update:loadPdfContent="loadPdfContent"
            @update:updateSubject="updateSubject"
            @update:updateCustomEmailList="updateCustomEmailList"
            @update:updateFromSection="updateFromSection"
            :country_default_settings="initialCountryDefaultSettings"
        />
        <owner-company-param-section
            v-if="search_method === letter_method_company_key"
            @update:mergeTag="updateMergeTag"
            @update:loading="updateLoading"
            @update:loadLetterContent="loadLetterContent"
            @update:loadPdfContent="loadPdfContent"
            @update:updateSubject="updateSubject"
            @update:updateCustomEmailList="updateCustomEmailList"
            @update:updateFromSection="updateFromSection"
            :country_default_settings="initialCountryDefaultSettings"
        />
        <supplier-param-section
            v-if="search_method === letter_method_supplier_key"
            @update:mergeTag="updateMergeTag"
            @update:loading="updateLoading"
            @update:loadLetterContent="loadLetterContent"
            @update:loadPdfContent="loadPdfContent"
            @update:updateSubject="updateSubject"
            @update:updateCustomEmailList="updateCustomEmailList"
            @update:updateFromSection="updateFromSection"
            :country_default_settings="initialCountryDefaultSettings"
        />
        <tenant-param-section
            v-if="search_method === letter_method_tenant_key"
            @update:mergeTag="updateMergeTag"
            @update:loading="updateLoading"
            @update:loadLetterContent="loadLetterContent"
            @update:loadPdfContent="loadPdfContent"
            @update:updateSubject="updateSubject"
            @update:updateCustomEmailList="updateCustomEmailList"
            @update:updateFromSection="updateFromSection"
            :country_default_settings="initialCountryDefaultSettings"
            :property_code="initialPropertyCode"
            :lease_code="initialLeaseCode"
        />
        <tenant-diary-param-section
            v-if="search_method === letter_method_tenant_diary_key"
            @update:mergeTag="updateMergeTag"
            @update:loading="updateLoading"
            @update:loadLetterContent="loadLetterContent"
            @update:loadPdfContent="loadPdfContent"
            @update:updateSubject="updateSubject"
            @update:updateCustomEmailList="updateCustomEmailList"
            @update:updateFromSection="updateFromSection"
            :country_default_settings="initialCountryDefaultSettings"
        />
        <budget-param-section
            v-if="search_method === letter_method_budget_key"
            @update:mergeTag="updateMergeTag"
            @update:loading="updateLoading"
            @update:loadLetterContent="loadLetterContent"
            @update:loadPdfContent="loadPdfContent"
            @update:updateSubject="updateSubject"
            @update:updateCustomEmailList="updateCustomEmailList"
            @update:updateFromSection="updateFromSection"
            :country_default_settings="initialCountryDefaultSettings"
        />

        <v-card
            v-show="search_method === letter_method_owner_budget_key"
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Attach Budget Report</h6>
                &nbsp&nbsp
                <v-btn-toggle
                    v-model="show_budget_report"
                    class="form-toggle"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Yes
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        No
                    </v-btn>
                </v-btn-toggle>
            </v-card-actions>
        </v-card>
        <div
            v-if="search_method === letter_method_owner_budget_key && show_budget_report === isOptionYes"
            class="page-form"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Report as at
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="budget_year"
                        :options="budget_year_list"
                        ref="refSearchMethod"
                        trackBy="field_key"
                        label="field_value"
                        return="field_key"
                        placeholder="Please select..."
                    />
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Report Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="budget_type"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                            value="1"
                        >
                            Cash Budget
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            value="2"
                        >
                            Cash Forecast
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            value="3"
                        >
                            Accruals Budget
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            value="4"
                        >
                            Accruals Forecast
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Report File
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="budget_report_file"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            Excel
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            PDF
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="budget_report_file === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Reports
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-checkbox
                        v-model="worksheet0"
                        label="Expenses Budget"
                        color="primary"
                        class="max-w-fit"
                    />
                    <v-checkbox
                        v-model="worksheet1"
                        label="Monthly Budget"
                        color="primary"
                        class="max-w-fit"
                    />
                    <v-checkbox
                        v-model="worksheet2"
                        :label="vo_label"
                        color="primary"
                        class="max-w-fit"
                    />
                    <v-checkbox
                        v-model="worksheet3"
                        label="Monthly Budget (per lease)"
                        color="primary"
                        class="max-w-fit"
                    />
                    <v-checkbox
                        v-model="worksheet4"
                        label="Breakdown Budget (per lease)"
                        color="primary"
                        class="max-w-fit"
                    />
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="budget_report_file === 0 && worksheet4"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Tenant Details
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="tenant_detail"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                            value="show_sqm"
                        >
                            Show <span v-html="area_unit"></span>
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            value="show_percentage"
                        >
                            Show Percentage
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            value="show_both"
                        >
                            Both
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="budget_report_file === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Use formula?
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="budget_use_formula"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                            value="Yes"
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            value="No"
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="budget_report_file === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Colour Scheme
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="colour_scheme"
                        :options="colour_scheme_list"
                        ref="refSearchMethod"
                        trackBy="field_key"
                        label="field_value"
                        return="field_key"
                        placeholder="Please select..."
                    />
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-show="budget_report_file === 1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                >
                    Group Expenses By
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="report_expenses_group"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                            value="None"
                        >
                            None
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            value="GL_GROUP"
                        >
                            Account Category
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                            value="SUB_CATEGORY"
                        >
                            Account Sub Category
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
        </div>

        <v-card
            v-show="search_method !== ''"
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Email</h6>
                &nbsp&nbsp
                <v-btn-toggle
                    v-model="show_email_editor"
                    class="form-toggle"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Yes
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        No
                    </v-btn>
                </v-btn-toggle>
            </v-card-actions>
        </v-card>
        <div
            v-show="search_method !== ''"
            class="page-form"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                >
                    Subject
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-input
                        v-model="subject"
                        placeholder="Subject"
                        :edit_form="true"
                    ></cirrus-input>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                >
                    CC me
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="ccme"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            x-small
                            tile
                            text
                            value="1"
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            x-small
                            tile
                            text
                            value="0"
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="custom_email_list.length > 1"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                >
                    Specify Send From?
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="specify_send_from"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            x-small
                            tile
                            text
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            x-small
                            tile
                            text
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="specify_send_from === isOptionYes"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                >
                    Send From (Email)
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="send_from_email"
                        :options="custom_email_list"
                        ref="refSearchMethod"
                        trackBy="field_key"
                        label="field_value"
                        return="field_key"
                        placeholder="Please select..."
                    />
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="specify_send_from === isOptionYes"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                >
                    From Name
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <span class="form-input-text">{{ send_from_name }}</span>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="specify_send_from === isOptionYes"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                >
                    Reply To (Email)
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <span class="form-input-text">{{ send_reply_email }}</span>
                </v-col>
            </v-row>

            <v-row
                v-show="show_email_editor === isOptionYes"
                class="form-row"
            >
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                >
                    <textarea
                        v-model="email_editor"
                        id="email_editor"
                    ></textarea>
                    <!-- Editor textarea -->
                </v-col>
            </v-row>
        </div>

        <v-card
            v-show="search_method !== ''"
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <v-btn-toggle
                    v-model="attachment_type"
                    class="form-toggle"
                    mandatory
                >
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        None
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        PDF
                    </v-btn>
                    <v-btn
                        x-small
                        tile
                        text
                    >
                        Attachment
                    </v-btn>
                </v-btn-toggle>
            </v-card-actions>
        </v-card>
        <div
            v-show="search_method !== ''"
            class="page-form"
        >
            <v-row
                v-show="attachment_type === isPdfAttachment"
                class="form-row"
            >
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    v-show="useDocxFile === isValueForYes"
                >
                    <v-row class="form-row">
                        <v-col
                            xs="12"
                            sm="2"
                            md="2"
                            class="form-label required"
                        >
                            PDF Content
                        </v-col>
                        <v-col
                            xs="12"
                            sm="10"
                            md="10"
                            class="form-input"
                        >
                            <span class="form-input-text">
                                <a
                                    target="_blank"
                                    :href="'download.php?fileID=' + letterFilenameEn"
                                >
                                    <v-icon small>mdi-microsoft-word</v-icon>download
                                </a>
                            </span>
                        </v-col>
                    </v-row>
                </v-col>
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    v-show="useDocxFile === isValueForNo"
                >
                    <textarea
                        v-model="pdf_editor"
                        id="pdf_editor"
                    ></textarea>
                    <!-- Editor textarea -->
                </v-col>
            </v-row>
            <v-row
                v-show="attachment_type === isExternalAttachment"
                class="form-row"
            >
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                >
                    <cirrus-multiple-upload-button
                        :id="Math.random()"
                        v-model="additional_attachments"
                        accept-type="pdf"
                        :size-in-bytes="upload_limit"
                    ></cirrus-multiple-upload-button>
                </v-col>
            </v-row>
        </div>
        <div
            v-show="search_method !== ''"
            class="page-form"
        >
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="12"
                    md="12"
                    class="form-input text-right"
                >
                    <v-checkbox
                        v-show="useDocxFile === isValueForNo"
                        v-model="use_letter_head"
                        label="include letterhead"
                        ripple="false"
                        dense
                        style="width: auto; margin-right: 15px"
                    ></v-checkbox>
                    <v-checkbox
                        v-show="search_method !== letter_method_budget_key && useDocxFile === isValueForYes"
                        v-model="download_docx_file"
                        label="Download Docx File"
                        ripple="false"
                        dense
                        style="width: auto; margin-right: 15px"
                    ></v-checkbox>
                    <v-btn
                        color="normal"
                        depressed
                        small
                        @click="processSendEmail()"
                    >
                        Send Email
                    </v-btn>
                    <v-btn
                        color="normal"
                        depressed
                        small
                        @click="processDownloadAll()"
                    >
                        Download All
                    </v-btn>
                    <v-btn
                        color="normal"
                        depressed
                        small
                        @click="processDownloadAllWithoutEmail()"
                    >
                        Download All w/o email
                    </v-btn>
                </v-col>
            </v-row>
        </div>
        <cirrus-server-error :error_msg="error_server_msg"></cirrus-server-error>
    </div>
</template>
<script>
import SendEmailLetterPropertyOwnerSection from '../Sections/SendEmailLetterPropertyOwnerSection.vue';
import SendEmailLetterOwnerCompanySection from '../Sections/SendEmailLetterOwnerCompanySection.vue';
import SendEmailLetterSupplierSection from '../Sections/SendEmailLetterSupplierSection.vue';
import SendEmailLetterTenantSection from '../Sections/SendEmailLetterTenantSection.vue';
import SendEmailLetterTenantDiarySection from '../Sections/SendEmailLetterTenantDiarySection.vue';
import SendEmailLetterBudgetSection from '../Sections/SendEmailLetterBudgetSection.vue';
import axios from 'axios';
import global_mixins from '../../plugins/mixins';
import isEmpty from 'lodash/isEmpty';
import {
    ATTACHMENT_TYPE,
    COLOR_SCHEME_LIST,
    LETTER_METHOD_BUDGET_KEY,
    LETTER_METHOD_BUDGET_LABEL,
    LETTER_METHOD_COMPANY_KEY,
    LETTER_METHOD_COMPANY_LABEL,
    LETTER_METHOD_OWNER_BUDGET_KEY,
    LETTER_METHOD_OWNER_BUDGET_LABEL,
    LETTER_METHOD_OWNER_KEY,
    LETTER_METHOD_OWNER_LABEL,
    LETTER_METHOD_SUPPLIER_KEY,
    LETTER_METHOD_SUPPLIER_LABEL,
    LETTER_METHOD_TENANT_DIARY_KEY,
    LETTER_METHOD_TENANT_DIARY_LABEL,
    LETTER_METHOD_TENANT_KEY,
    LETTER_METHOD_TENANT_LABEL,
    SEND_EMAIL_N_LETTER_TITLE,
} from '../constants';
import { DISABLED, EIGHT_MB_IN_BYTES_LIMIT, ENABLED, YES_NO_OPTION } from '../../constants';
import imageUploadHandler from '../../plugins/imageUploader';

export default {
    props: {
        initialSearchMethod: String,
        initialPropertyCode: String,
        initialLeaseCode: String,
        initialCountryDefaultSettings: String,
    },
    mixins: [global_mixins],
    components: {
        'property-owner-param-section': SendEmailLetterPropertyOwnerSection,
        'property-owner-budget-param-section': SendEmailLetterPropertyOwnerSection,
        'owner-company-param-section': SendEmailLetterOwnerCompanySection,
        'supplier-param-section': SendEmailLetterSupplierSection,
        'tenant-param-section': SendEmailLetterTenantSection,
        'tenant-diary-param-section': SendEmailLetterTenantDiarySection,
        'budget-param-section': SendEmailLetterBudgetSection,
    },
    data() {
        return {
            page_title: SEND_EMAIL_N_LETTER_TITLE,
            upload_limit: EIGHT_MB_IN_BYTES_LIMIT,
            page_subtitle: '',
            search_method: '',
            search_method_list: [
                {
                    field_key: LETTER_METHOD_OWNER_KEY,
                    field_value: LETTER_METHOD_OWNER_LABEL,
                },
                {
                    field_key: LETTER_METHOD_OWNER_BUDGET_KEY,
                    field_value: LETTER_METHOD_OWNER_BUDGET_LABEL,
                },
                {
                    field_key: LETTER_METHOD_COMPANY_KEY,
                    field_value: LETTER_METHOD_COMPANY_LABEL,
                },
                {
                    field_key: LETTER_METHOD_SUPPLIER_KEY,
                    field_value: LETTER_METHOD_SUPPLIER_LABEL,
                },
                {
                    field_key: LETTER_METHOD_TENANT_KEY,
                    field_value: LETTER_METHOD_TENANT_LABEL,
                },
                {
                    field_key: LETTER_METHOD_TENANT_DIARY_KEY,
                    field_value: LETTER_METHOD_TENANT_DIARY_LABEL,
                },
                {
                    field_key: LETTER_METHOD_BUDGET_KEY,
                    field_value: LETTER_METHOD_BUDGET_LABEL,
                },
            ],
            letter_method_owner_key: LETTER_METHOD_OWNER_KEY,
            letter_method_owner_budget_key: LETTER_METHOD_OWNER_BUDGET_KEY,
            letter_method_company_key: LETTER_METHOD_COMPANY_KEY,
            letter_method_supplier_key: LETTER_METHOD_SUPPLIER_KEY,
            letter_method_tenant_key: LETTER_METHOD_TENANT_KEY,
            letter_method_tenant_diary_key: LETTER_METHOD_TENANT_DIARY_KEY,
            letter_method_budget_key: LETTER_METHOD_BUDGET_KEY,
            letter_method_owner_label: LETTER_METHOD_OWNER_LABEL,
            letter_method_owner_budget_label: LETTER_METHOD_OWNER_BUDGET_LABEL,
            letter_method_company_label: LETTER_METHOD_COMPANY_LABEL,
            specify_send_from: YES_NO_OPTION.yes,
            isPdfAttachment: ATTACHMENT_TYPE.pdf,
            isExternalAttachment: ATTACHMENT_TYPE.attachment,
            isOptionYes: YES_NO_OPTION.yes,
            isValueForYes: ENABLED.value,
            isValueForNo: DISABLED.value,
            isLabelForYes: ENABLED.label,
            isLabelForNo: DISABLED.label,
            show_email_editor: YES_NO_OPTION.no,
            show_budget_report: YES_NO_OPTION.no,
            attachment_type: ATTACHMENT_TYPE.none,
            letter_template: '',
            contact_type: [],
            property_manager: [],
            error_server_msg: [],
            property: [],
            owner: [],
            supplier: [],
            tenant: [],
            budget_use_formula: DISABLED.label,
            send_contact_type: DISABLED.label,
            report_expenses_group: 'None',
            custom_email_list: [],
            additional_attachments: null,
            subject: '',
            loading_page_setting: false,
            use_letter_head: true,
            download_docx_file: false,
            mergeTag: '',
            email_editor: '',
            pdf_editor: '',
            budget_type: '1',
            budget_report_file: 0,
            budget_year: '',
            show_annual_property_budget: ENABLED.label,
            ccme: DISABLED.value,
            tenant_detail: 'show_sqm',
            send_from_email: '',
            send_from_name: '',
            send_reply_email: '',
            success_email_list: [],
            budget_year_list: [],
            useDocxFile: DISABLED.value,
            letterFilename: null,
            letterFilenameEn: null,
            colour_scheme: 'Bluish Cyan',
            colour_scheme_list: COLOR_SCHEME_LIST,
            worksheet0: false,
            worksheet1: true,
            worksheet2: true,
            worksheet3: true,
            worksheet4: false,
            area_unit: 'm&sup2;',
            vo_label: 'Variable Outgoings',
            letter_id: null,
            diary_type: null,
            from_date: null,
            to_date: null,
        };
    },
    methods: {
        initializeEmailEditor() {
            if (window.tinymce) {
                const base64_img_handler = (blobInfo) =>
                    new Promise((resolve) => {
                        resolve('data:image/png;base64,' + blobInfo.base64());
                    });
                // Initialize TinyMCE editor
                tinymce.init({
                    selector: '#email_editor', // Select the text area
                    plugins:
                        'ai advcode a11ychecker autocorrect autolink emoticons image inlinecss link linkchecker lists mergetags powerpaste tinymcespellchecker help table',
                    toolbar:
                        'undo redo | styles fontfamily fontsizeinput | bold italic underline forecolor backcolor | table | link image emoticons | align | mergetags  | spellcheckdialog a11ycheck | aidialog aishortcuts | code removeformat',
                    toolbar_sticky: false,
                    branding: false,
                    ai_request: (request, respondWith) => {
                        const openAiOptions = {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                Authorization: 'Bearer ' + this.$openAILicenseCode,
                            },
                            body: JSON.stringify({
                                model: 'gpt-3.5-turbo',
                                temperature: 0.7,
                                max_tokens: 800,
                                messages: [{ role: 'user', content: request.prompt }],
                            }),
                        };
                        respondWith.string((signal) =>
                            window
                                .fetch('https://api.openai.com/v1/chat/completions', { signal, ...openAiOptions })
                                .then(async (response) => {
                                    if (response) {
                                        const data = await response.json();
                                        if (data.error) {
                                            throw new Error(`${data.error.type}: ${data.error.message}`);
                                        } else if (response.ok) {
                                            // Extract the response content from the data returned by the API
                                            return data?.choices[0]?.message?.content?.trim();
                                        }
                                    } else {
                                        throw new Error('Failed to communicate with the ChatGPT API');
                                    }
                                }),
                        );
                    },
                    menubar: false,
                    height: 400,
                    editable_root: true,
                    images_upload_handler: imageUploadHandler('api/home/<USER>'),
                    automatic_uploads: true,
                    images_upload_credentials: true,
                    editable_class: 'tiny-editable',
                    elementpath: false,
                    visual: false,
                    link_target_list: false,
                    link_list: [
                        { title: 'Features', value: 'https://www.tiny.cloud/tinymce/features/' },
                        { title: 'Docs', value: 'https://www.tiny.cloud/docs/tinymce/latest/' },
                        { title: 'Pricing', value: 'https://www.tiny.cloud/pricing/' },
                    ],
                    object_resizing: false,
                    formats: {
                        h1: { block: 'h1', styles: { fontSize: '24px', color: '#335dff' } },
                        h2: { block: 'h2', styles: { fontSize: '20px' } },
                        largetext: { block: 'p', styles: { fontSize: '20px' } },
                        calltoaction: {
                            selector: 'a',
                            styles: {
                                backgroundColor: '#335dff',
                                padding: '12px 16px',
                                color: '#ffffff',
                                borderRadius: '4px',
                                textDecoration: 'none',
                                display: 'inline-block',
                            },
                        },
                    },
                    style_formats: [
                        { title: 'Paragraph', format: 'p' },
                        { title: 'Heading 1', format: 'h1' },
                        { title: 'Heading 2', format: 'h2' },
                        { title: 'Large text', format: 'largetext' },
                        { title: 'Button styles' },
                        { title: 'Call-to-action', format: 'calltoaction' },
                    ],

                    images_file_types: 'jpeg,jpg,png,gif',
                    spellchecker_ignore_list: ['i.e', 'Mailchimp', 'CSS-inlined'],
                    mergetags_list: this.mergeTag,
                    mergetags_prefix: '%',
                    mergetags_suffix: '%',

                    content_style: `
                        body {
                          background-color: #ffffff;
                        }

                        /* Edit area functional css */
                        .tiny-editable {
                          position: relative;
                        }
                        .tiny-editable:hover:not(:focus),
                        .tiny-editable:focus {
                          outline: 3px solid #b4d7ff;
                          outline-offset: 4px;
                        }

                        /* Create an edit placeholder */
                        .tiny-editable:empty::before,
                        .tiny-editable:has(> br[data-mce-bogus]:first-child)::before {
                          content: "Write here...";
                          position: absolute;
                          top: 0;
                          left: 0;
                          color: #999;
                        }
                      `,
                    // Sync TinyMCE content to `email_editor` on editor events
                    setup: (editor) => {
                        editor.on('init', () => {
                            editor.setContent(this.email_editor); // Set initial content from Vue data
                        });
                        editor.on('change keyup', () => {
                            this.email_editor = editor.getContent(); // Sync TinyMCE content to Vue data
                        });
                    },
                });
            }
        },
        initializePDFEditor() {
            if (window.tinymce) {
                const base64_img_handler = (blobInfo) =>
                    new Promise((resolve) => {
                        resolve('data:image/png;base64,' + blobInfo.base64());
                    });
                // Initialize TinyMCE editor
                tinymce.init({
                    selector: '#pdf_editor', // Select the text area
                    plugins:
                        'ai advcode a11ychecker autocorrect autolink emoticons image inlinecss link linkchecker lists mergetags powerpaste tinymcespellchecker help table',
                    toolbar:
                        'undo redo | styles fontfamily fontsizeinput | bold italic underline forecolor backcolor | link image emoticons | align | mergetags  | spellcheckdialog a11ycheck | aidialog aishortcuts | code removeformat',
                    toolbar_sticky: false,
                    branding: false,
                    ai_request: (request, respondWith) => {
                        const openAiOptions = {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                Authorization: 'Bearer ' + this.$openAILicenseCode,
                            },
                            body: JSON.stringify({
                                model: 'gpt-3.5-turbo',
                                temperature: 0.7,
                                max_tokens: 800,
                                messages: [{ role: 'user', content: request.prompt }],
                            }),
                        };
                        respondWith.string((signal) =>
                            window
                                .fetch('https://api.openai.com/v1/chat/completions', { signal, ...openAiOptions })
                                .then(async (response) => {
                                    if (response) {
                                        const data = await response.json();
                                        if (data.error) {
                                            throw new Error(`${data.error.type}: ${data.error.message}`);
                                        } else if (response.ok) {
                                            // Extract the response content from the data returned by the API
                                            return data?.choices[0]?.message?.content?.trim();
                                        }
                                    } else {
                                        throw new Error('Failed to communicate with the ChatGPT API');
                                    }
                                }),
                        );
                    },
                    menubar: 'edit insert view format tools',
                    height: 400,
                    editable_root: true,
                    images_upload_handler: base64_img_handler,
                    editable_class: 'tiny-editable',
                    elementpath: false,
                    visual: false,
                    link_target_list: false,
                    link_list: [
                        { title: 'Features', value: 'https://www.tiny.cloud/tinymce/features/' },
                        { title: 'Docs', value: 'https://www.tiny.cloud/docs/tinymce/latest/' },
                        { title: 'Pricing', value: 'https://www.tiny.cloud/pricing/' },
                    ],
                    object_resizing: false,
                    formats: {
                        h1: { block: 'h1', styles: { fontSize: '24px', color: '#335dff' } },
                        h2: { block: 'h2', styles: { fontSize: '20px' } },
                        largetext: { block: 'p', styles: { fontSize: '20px' } },
                        calltoaction: {
                            selector: 'a',
                            styles: {
                                backgroundColor: '#335dff',
                                padding: '12px 16px',
                                color: '#ffffff',
                                borderRadius: '4px',
                                textDecoration: 'none',
                                display: 'inline-block',
                            },
                        },
                    },
                    style_formats: [
                        { title: 'Paragraph', format: 'p' },
                        { title: 'Heading 1', format: 'h1' },
                        { title: 'Heading 2', format: 'h2' },
                        { title: 'Large text', format: 'largetext' },
                        { title: 'Button styles' },
                        { title: 'Call-to-action', format: 'calltoaction' },
                    ],

                    images_file_types: 'jpeg,jpg,png,gif',
                    spellchecker_ignore_list: ['i.e', 'Mailchimp', 'CSS-inlined'],
                    mergetags_list: this.mergeTag,
                    mergetags_prefix: '%',
                    mergetags_suffix: '%',

                    content_style: `
                        body {
                          background-color: #ffffff;
                        }

                        /* Edit area functional css */
                        .tiny-editable {
                          position: relative;
                        }
                        .tiny-editable:hover:not(:focus),
                        .tiny-editable:focus {
                          outline: 3px solid #b4d7ff;
                          outline-offset: 4px;
                        }

                        /* Create an edit placeholder */
                        .tiny-editable:empty::before,
                        .tiny-editable:has(> br[data-mce-bogus]:first-child)::before {
                          content: "Write here...";
                          position: absolute;
                          top: 0;
                          left: 0;
                          color: #999;
                        }
                      `,
                    // Sync TinyMCE content to `email_editor` on editor events
                    setup: (editor) => {
                        editor.on('init', () => {
                            editor.setContent(this.pdf_editor); // Set initial content from Vue data
                        });
                        editor.on('change keyup', () => {
                            this.pdf_editor = editor.getContent(); // Sync TinyMCE content to Vue data
                        });
                    },
                });
            }
        },
        async processSendEmail() {
            this.success_email_list = [];
            this.error_server_msg = [];
            this.loading_page_setting = true;
            let form_data = this.generateFormData(1);
            form_data.set('submit', '1');
            if (this.search_method === this.letter_method_budget_key) {
                this.success_email_list = [];
                let tenant = this.tenant;
                for (let index = 0; index < tenant.length; index++) {
                    let tenant_code = tenant[index].field_key;
                    form_data.set('tenantID', tenant_code);
                    await axios
                        .post('?module=administration&command=letter&action=sendEmail&sc=with-file-upload', form_data, {
                            headers: {
                                'Content-Type': 'multipart/form-data',
                            },
                        })
                        .then((response) => {
                            this.extractResponseData(response.data);
                            this.loading_page_setting = false;
                        });
                }
            } else {
                await axios
                    .post('?module=administration&command=letter&action=sendEmail&sc=with-file-upload', form_data, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                        },
                    })
                    .then((response) => {
                        this.extractResponseData(response.data);
                        this.loading_page_setting = false;
                    });
            }
        },
        async processDownloadAll() {
            this.error_server_msg = [];
            this.loading_page_setting = true;
            let form_data = this.generateFormData(0);
            form_data.set('submit', '1');
            await axios
                .post('?module=administration&command=letter&action=dlAll&sc=with-file-upload', form_data)
                .then((response) => {
                    this.extractResponseData(response.data);
                    this.loading_page_setting = false;
                });
        },
        async processDownloadAllWithoutEmail() {
            this.error_server_msg = [];
            this.loading_page_setting = true;
            let form_data = this.generateFormData(0);
            form_data.set('submit', '1');
            await axios
                .post('?module=administration&command=letter&action=dlAllNoEmail&sc=with-file-upload', form_data)
                .then((response) => {
                    this.extractResponseData(response.data);
                    this.loading_page_setting = false;
                });
        },
        extractResponseData: function (response) {
            if (response?.validationErrors) {
                Object.entries(response.validationErrors).forEach(([key, value]) => {
                    this.error_server_msg.push([value]);
                });
            } else {
                this.additional_attachments = null;
                const regex = /document\.location\.href\s*=\s*['"]([^'"]+)['"]/;
                const match = response.match(regex);

                if (match && match[1]) {
                    window.location.href = match[1];
                } else {
                    (response ?? [])
                        .split(';')
                        .filter((email) => !isEmpty(email.trim()))
                        .forEach((email) => {
                            this.success_email_list.push(email.trim());
                        });
                }
            }
        },
        generateFormData: function (type = 0) {
            let owner = this.extractArrayValue(this.owner, type);
            let property = this.extractArrayValue(this.property, type);
            let contact_type = this.extractArrayValue(this.contact_type, type);
            let property_manager = this.extractArrayValue(this.property_manager, type);
            let supplier = this.extractArrayValue(this.supplier, type);
            let tenant = this.extractArrayValue(this.tenant, type);
            if (this.search_method === this.letter_method_company_key) supplier = owner;
            let subject = this.subject;
            let use_letter_head = this.use_letter_head ? ENABLED.value : DISABLED.value;
            let download_docx_file = this.download_docx_file ? ENABLED.value : DISABLED.value;
            let use_email = this.show_email_editor === YES_NO_OPTION.yes ? ENABLED.value : DISABLED.value;
            let use_pdf = this.attachment_type === ATTACHMENT_TYPE.pdf ? ENABLED.value : DISABLED.value;
            let use_attachment = this.attachment_type === ATTACHMENT_TYPE.attachment ? ENABLED.value : DISABLED.value;
            let ccme = this.ccme;
            let budget_type = this.budget_type;
            let budget_year = this.budget_year;
            let show_annual_property_budget = this.show_annual_property_budget;
            let tenant_detail = this.tenant_detail;
            var form_data = new FormData();
            form_data.append('searchMethod', this.search_method);
            form_data.append('ownerID', owner);
            form_data.append('supplierID', supplier);
            form_data.append('propertyID', property);
            form_data.append('showContactTypes', this.send_contact_type);
            form_data.append('contactTypeID', contact_type);
            form_data.append('tenantID', tenant);
            form_data.append('emailSubject', subject);
            form_data.append('ccme', ccme);
            form_data.append('letterContent', this.email_editor);
            form_data.append('pdfContent', this.pdf_editor);
            form_data.append('useLetterhead', use_letter_head);
            form_data.append('downloadDocx', download_docx_file);
            if (this.letterFilename) form_data.append('letterFilename', this.letterFilename);
            form_data.append('useEMAIL', use_email);
            form_data.append('usePDF', use_pdf);
            form_data.append('useAttachment', use_attachment);
            form_data.append('showAnnualPropertyBudget', show_annual_property_budget);
            form_data.append('budget_type', budget_type);
            form_data.append('budgetYear', budget_year);
            form_data.append('tenant_detail', tenant_detail);
            form_data.append('attach_budget_report', this.show_budget_report === YES_NO_OPTION.yes ? 'Yes' : 'No');
            form_data.append('budget_report_file', this.budget_report_file === 0 ? 'xlsx' : 'pdf');
            form_data.append('worksheet0', this.worksheet0 ? 'Yes' : 'No');
            form_data.append('worksheet1', this.worksheet1 ? 'Yes' : 'No');
            form_data.append('worksheet2', this.worksheet2 ? 'Yes' : 'No');
            form_data.append('worksheet3', this.worksheet3 ? 'Yes' : 'No');
            form_data.append('worksheet4', this.worksheet4 ? 'Yes' : 'No');
            form_data.append('budget_use_formula', this.budget_use_formula);
            form_data.append('colour_scheme', this.colour_scheme);
            form_data.append('report_expenses_group', this.report_expenses_group);
            form_data.append('emailSpecified', this.specify_send_from === YES_NO_OPTION.yes ? '1' : '0');
            form_data.append('sendFrom', this.send_from_email);
            form_data.append('fromName', this.send_from_name);
            form_data.append('replyTo', this.send_reply_email);
            form_data.append('letterID', this.letter_id);
            form_data.append('from_api', 1);
            form_data.append('no_load', true);
            form_data.append('diary_type', this.diary_type);
            form_data.append('fromDate', this.from_date);
            form_data.append('toDate', this.to_date);
            Object.values(this.additional_attachments ?? {}).forEach((attachment) => {
                form_data.append('file[]', attachment);
            });
            return form_data;
        },
        destroyEditor(editor_id) {
            if (!window.tinymce) return;
            if (tinymce.get(editor_id)) {
                // tinymce.get(editor_id).remove();
                tinymce.get(editor_id).setContent('');
            }
        },
        updateMergeTag: function (data) {
            this.mergeTag = data;
            if (this.show_email_editor === YES_NO_OPTION.yes && this.mergeTag) {
                this.initializeEmailEditor();
            }
            if (this.attachment_type === ATTACHMENT_TYPE.pdf && this.mergeTag) {
                this.initializePDFEditor();
            }
        },
        updateLoading: function (state) {
            this.loading_page_setting = state;
        },
        loadLetterContent: function (data) {
            this.show_email_editor = YES_NO_OPTION.yes;
            this.specify_send_from = YES_NO_OPTION.yes;
            this.email_editor = data;
            const editor = tinymce.get('email_editor'); // Get the TinyMCE editor instance
            if (editor) {
                editor.setContent(data); // Update the TinyMCE editor content
            }
        },
        loadPdfContent: function (data) {
            this.attachment_type = ATTACHMENT_TYPE.pdf;
            this.specify_send_from = YES_NO_OPTION.yes;
            this.pdf_editor = data;
            const editor = tinymce.get('pdf_editor'); // Get the TinyMCE editor instance
            if (editor) {
                editor.setContent(data); // Update the TinyMCE editor content
            }
        },
        updateSubject: function (data) {
            this.subject = data;
        },
        updateCustomEmailList: function (data) {
            this.send_from_email = data.clientSendFrom;
            this.send_from_name = data.clientFromName;
            this.send_reply_email = data.clientReplyTo;
            this.custom_email_list = data.customEmailList.map((email) => ({
                field_key: email.send_from,
                field_value: email.send_from,
                send_from: email.send_from,
                reply_to: email.reply_to,
                from_name: email.from_name,
            }));
        },
        updateFromSection: function (data) {
            this.property = data?.property;
            this.owner = data?.owner;
            this.supplier = data?.supplier;
            this.tenant = data?.tenant;
            this.budget_type = data?.budget_type;
            this.budget_year = data?.budget_year;
            this.show_annual_property_budget = data?.show_annual_property_budget;
            this.tenant_detail = data?.tenant_detail;
            this.send_contact_type = data?.send_contact_type;
            this.contact_type = data?.contact_type;
            this.useDocxFile = Number(data?.useDocxFile ?? 0);
            this.letterFilename = data?.letterFilename;
            this.letterFilenameEn = data?.letterFilenameEn;
            this.budget_year_list = data?.budget_year_list;
            this.letter_id = data?.letter_id;
            this.diary_type = data?.diary_type;
            this.from_date = data?.from_date;
            this.to_date = data?.to_date;
        },
        extractArrayValue: function (data, type = 0) {
            if (!data) return '';
            const data_array = data.map((item) => item.field_key || item.fieldKey);
            return type === 0 ? data_array.join('::') : data_array.join(',');
        },
    },
    mounted() {
        let country_default_settings = JSON.parse(atob(this.initialCountryDefaultSettings));
        this.area_unit = country_default_settings.area_unit;
        this.vo_label = this.properCase(country_default_settings.variable_outgoings);
        if (!window.tinymce) {
            const script = document.createElement('script');
            script.src = 'https://cdn.tiny.cloud/1/' + this.$tinymceLicenseCode + '/tinymce/7/tinymce.min.js';
            script.referrerPolicy = 'origin';
            document.head.appendChild(script);
        }
        if (this.initialSearchMethod) {
            const value = this.returnValueFromList(this.initialSearchMethod, this.search_method_list);
            if (!isEmpty(value)) this.search_method = value;
        }
    },
    beforeDestroy() {
        this.destroyEditor('email_editor');
        this.destroyEditor('pdf_editor');
    },
    watch: {
        search_method: function () {
            if (this.search_method) {
                this.destroyEditor('email_editor');
                this.destroyEditor('pdf_editor');
                this.show_email_editor = YES_NO_OPTION.no;
                this.additional_attachments = null;
                this.success_email_list = [];
                this.attachment_type = ATTACHMENT_TYPE.none;
            }
        },
        show_email_editor: function () {
            this.success_email_list = [];
            if (this.show_email_editor === YES_NO_OPTION.yes && this.mergeTag) {
                this.initializeEmailEditor();
            }
        },
        attachment_type: function () {
            this.success_email_list = [];
            if (this.attachment_type === ATTACHMENT_TYPE.pdf && this.mergeTag) {
                this.initializePDFEditor();
            }
        },
        send_from_email: function () {
            this.success_email_list = [];
            if (this.send_from_email) {
                let data = this.getValueInList(this.send_from_email, this.custom_email_list);
                this.send_from_name = data.from_name;
                this.send_reply_email = data.reply_to;
            }
        },
    },
};
</script>
