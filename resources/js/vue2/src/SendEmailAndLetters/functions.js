/**
 * Get options for active properties based on the given property list
 *
 * @param {Array} propertyList - The list of properties to filter and map
 * @return {Array} - An array of objects with fieldKey and fieldValue properties for active properties
 */
export function getActivePropertiesOptions(propertyList) {
    return propertyList
        .filter((m) => m.propertyStatus === 0)
        .map((prop) => ({
            fieldKey: prop.propertyID,
            fieldValue: prop.propertyName,
        }));
}

/**
 * Get options for inactive properties from a list of properties
 *
 * @param {Array} propertyList - The list of properties to filter from
 * @return {Array} - An array of options for inactive properties with fieldKey and fieldValue
 */
export function getInActivePropertiesOptions(propertyList) {
    return propertyList
        .filter((m) => m.propertyStatus === 1)
        .map((prop) => ({
            fieldKey: prop.propertyID,
            fieldValue: prop.propertyName,
        }));
}
