export const LETTER_METHOD_OWNER_KEY = 'Owner';
export const LETTER_METHOD_OWNER_LABEL = 'Property Owner';
export const LETTER_METHOD_OWNER_BUDGET_KEY = 'Owner Budget';
export const LETTER_METHOD_OWNER_BUDGET_LABEL = 'Property Owner Budget';
export const LETTER_METHOD_COMPANY_KEY = 'Owner Company';
export const LETTER_METHOD_COMPANY_LABEL = 'Owner Company';
export const LETTER_METHOD_SUPPLIER_KEY = 'Supplier';
export const LETTER_METHOD_SUPPLIER_LABEL = 'Supplier';
export const LETTER_METHOD_TENANT_KEY = 'Tenant';
export const LETTER_METHOD_TENANT_LABEL = 'Tenant';
export const LETTER_METHOD_TENANT_DIARY_KEY = 'Tenant Diary';
export const LETTER_METHOD_TENANT_DIARY_LABEL = 'Tenant Diary';
export const LETTER_METHOD_BUDGET_KEY = 'Budget';
export const LETTER_METHOD_BUDGET_LABEL = 'Budget';
export const PLACEHOLDER_DATES = '%day% | %monthNumber% | %monthName% | %year% | %priorMonth% | %priorYear%';
export const PLACEHOLDER_PROPERTY_MANAGER =
    '%propertyManagerID% | %propertyManagerName% | %propertyManagerTitle% | %propertyManagerEmail% | %propertyManagerMobileNumber%';
export const PLACEHOLDER_PROPERTY_MANAGER_WITHOUT_ID =
    '%propertyManagerName% | %propertyManagerTitle% | %propertyManagerEmail% | %propertyManagerMobileNumber%';
export const PLACEHOLDER_PROPERTY =
    '%propertyName% | %propertyStreet% | %propertyCity% | %propertyState% | %propertyPostCode% | %propertyDescription%';
export const PLACEHOLDER_MISCELLANEOUS =
    '%ownerName% | %ownerCode% | %ownerStreet% | %ownerSuburb% | %ownerPostCode% | %ownerState% | %propertyCode% | %contactSalutation% | %totalRemittanceForLastMonth% | %ownerRemitLast% | %totalOwnerRemittanceForLast3months% | %totalRemittanceForLast3Months% | %propertyContactSalutation%';
export const PLACEHOLDER_BUDGET =
    '%newVO% | %newVOSQM% | %newVOpm% | %currentVO% | %currentVOSQM% | %currentVOpm% | %changeVOpercent% | %newDR% | %newDRSQM% | %newTotal% | %newTotalSQM%';
export const SEND_EMAIL_N_LETTER_TITLE = 'Send Email & Letters';

export const COLOR_SCHEME_LIST = [
    { field_key: 'Bluish Cyan', field_value: 'Bluish Cyan' },
    { field_key: 'Medium Blue', field_value: 'Medium Blue' },
    { field_key: 'Indigo', field_value: 'Indigo' },
    { field_key: 'Magenta', field_value: 'Magenta' },
    { field_key: 'Pinkish Red', field_value: 'Pinkish Red' },
    { field_key: 'Red-Orange', field_value: 'Red-Orange' },
    { field_key: 'Orange', field_value: 'Orange' },
    { field_key: 'Yellow-Orange', field_value: 'Yellow-Orange' },
    { field_key: 'Yellow', field_value: 'Yellow' },
    { field_key: 'Greenish-Yellow', field_value: 'Greenish-Yellow' },
    { field_key: 'Yellow-Green', field_value: 'Yellow-Green' },
    { field_key: 'Blue-Green', field_value: 'Blue-Green' },
    { field_key: 'Navy Blue', field_value: 'Navy Blue' },
];
export const ATTACHMENT_TYPE = Object.freeze({
    none: 0,
    pdf: 1,
    attachment: 2,
});
