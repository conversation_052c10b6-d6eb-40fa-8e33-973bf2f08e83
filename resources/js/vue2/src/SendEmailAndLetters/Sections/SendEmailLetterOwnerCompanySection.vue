<template>
    <div class="c8-page page-form">
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Send by Contact Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="send_contact_type"
                        class="form-toggle"
                        @change="handleToggleChange"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="send_contact_type === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Contact Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        :withKey="false"
                        v-model="contact_type"
                        v-bind:options="contact_type_list"
                        :displayCount="false"
                    ></vue-dual-list-select>
                    &nbsp;<v-btn
                        small
                        color="normal"
                        @click="loadFrameworkParameters(0)"
                        >Select Contact Type/s
                    </v-btn>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Owner
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="owner"
                        v-bind:options="owner_list"
                        :displayCount="false"
                    ></vue-dual-list-select>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Letter Template
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="letter_template"
                        :options="letter_template_list"
                        ref="refSearchMethod"
                        trackBy="field_key"
                        label="field_value"
                        return="field_key"
                        placeholder="Please select..."
                    />
                </v-col>
            </v-row>
        </div>
        <v-card
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Placeholders can be used in e-mail and document content</h6>
                &nbsp&nbsp
            </v-card-actions>
        </v-card>
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Date
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    %day% | %month% | %monthName% | %year%
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Owner
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    %ownerCode% | %ownerName% | %ownerStreet% | %ownerCity% | %ownerState% | %ownerPostCode% |
                    %contactSalutation%
                </v-col>
            </v-row>
        </div>
    </div>
</template>
<script>
import global_mixins from '../../plugins/mixins';
import axios from 'axios';

export default {
    mixins: [global_mixins],
    props: {
        search_method: { type: String, default: 'Owner Company' },
        country_default_settings: { type: String, default: '' },
    },
    data() {
        return {
            letter_template: '',
            contact_type: [],
            property_manager: [],
            property: [],
            owner: [],
            use_letter_head: 0,
            mergeTag: [],
            show_inactive_properties: 1,
            show_current_tenant: 0,
            send_contact_type: 1,
            letter_template_list: [],
            contact_type_list: [],
            property_manager_list: [],
            property_list: [],
            owner_list: [],
            property_manager_label: '',
            useDocxFile: '0',
            letterFilename: null,
            letterFilenameEn: null,
        };
    },
    methods: {
        async loadFrameworkParameters(type = 0) {
            this.$emit('update:loading', true);
            let url = '?module=administration&command=letter';
            if (type === 1) url = '?module=administration&command=letter&action=changeMethod';
            if (type === 2) url = '?module=administration&command=letter&resetTenants=1';
            const contact_type_value = this.contact_type.map((item) => item.fieldKey);
            const property_manager_value = this.property_manager.map((item) => item.fieldKey);
            const property_value = this.property.map((item) => item.fieldKey);

            this.loading_page_setting = true;
            var form_data = new FormData();
            if (contact_type_value.length > 0) form_data.append('contactTypeID', contact_type_value.join('::'));
            if (property_manager_value.length > 0)
                form_data.append('propertyManagerID', property_manager_value.join('::'));
            if (property_value.length > 0) form_data.append('propertyID', property_value.join('::'));
            if (this.letter_template) form_data.append('letterID', this.letter_template);
            form_data.append('searchMethod', this.search_method);
            form_data.append('showContactTypes', this.send_contact_type === 1 ? 'No' : 'Yes');
            form_data.append('showCurrentOnly', this.show_current_tenant === 0 ? 'No' : 'Yes');
            form_data.append('showActiveOnly', this.show_inactive_properties === 1 ? 'No' : 'Yes');
            form_data.append('useLetterhead', this.use_letter_head === 0 ? '1' : '0');
            form_data.append('from_api', 1);
            form_data.append('no_load', true);
            await axios.post(url, form_data).then((response) => {
                this.extractResponseData(response.data);
                this.loading_page_setting = false;
            });
        },
        extractResponseData(data) {
            this.useDocxFile = data?.useDocxFile;
            this.letterFilename = data?.letterFilename;
            this.letterFilenameEn = data?.letterFilenameEn;
            this.letter_template_list = [];
            this.property_manager_list = [];
            this.property_list = [];
            this.contact_type_list = [];
            this.owner_list = [];
            let contactTypeList = data?.contactTypeList ?? [];
            let ownerList = this.ensureParameterIsAnArray(data?.companyList);
            let templateList = this.ensureParameterIsAnArray(data?.templateList);

            this.$emit('update:updateSubject', data?.emailSubject);

            let temp_value = [];

            for (const i in ownerList) {
                temp_value.push({
                    fieldKey: ownerList[i].companyID,
                    fieldValue: ownerList[i].companyNameWithEmail,
                });
            }
            if (temp_value.length > 0)
                this.owner_list.push({
                    fieldGroupNames: '',
                    fieldGroupValues: temp_value,
                });

            for (const i in templateList) {
                this.letter_template_list.push({
                    field_key: templateList[i].letterTemplateID,
                    field_value: templateList[i].letterTemplateName,
                });
            }

            temp_value = [];
            for (const key in contactTypeList) {
                temp_value.push({ fieldKey: key, fieldValue: contactTypeList[key] });
            }
            if (temp_value.length > 0)
                this.contact_type_list.push({
                    fieldGroupNames: '',
                    fieldGroupValues: temp_value,
                });
            if (data?.letterContent !== undefined) this.$emit('update:loadLetterContent', data.letterContent);
            if (data?.pdfContent !== undefined) this.$emit('update:loadPdfContent', data.pdfContent);

            this.$emit('update:mergeTag', JSON.parse(data.mergeTag));
            if (data?.customEmailList !== undefined) {
                let custom_email_list = {
                    clientFromName: data.clientFromName,
                    clientReplyTo: data.clientReplyTo,
                    clientSendFrom: data.clientSendFrom,
                    customEmailList: data.customEmailList,
                };
                this.$emit('update:updateCustomEmailList', custom_email_list);
            }
            this.updateParentData();
            this.$emit('update:loading', false);
        },
        handleToggleChange: function () {
            this.loadFrameworkParameters(2);
        },
        updateParentData: function () {
            let data = {
                property: this.property,
                owner: this.owner,
                send_contact_type: this.send_contact_type === 0 ? 'Yes' : 'No',
                contact_type: this.contact_type,
                useDocxFile: this.useDocxFile,
                letterFilename: this.letterFilename,
                letterFilenameEn: this.letterFilenameEn,
                letter_id: this.letter_template,
            };
            this.$emit('update:updateFromSection', data);
        },
    },
    watch: {
        letter_template: function () {
            if (this.letter_template) {
                this.loadFrameworkParameters();
            }
        },
        property: function () {
            this.updateParentData();
        },
        owner: function () {
            this.updateParentData();
        },
    },
    created() {
        this.loadFrameworkParameters(1);
    },
    mounted() {
        let country_default_settings = JSON.parse(atob(this.country_default_settings));
        this.property_manager_label = this.ucwords(country_default_settings.property_manager);
    },
};
</script>
