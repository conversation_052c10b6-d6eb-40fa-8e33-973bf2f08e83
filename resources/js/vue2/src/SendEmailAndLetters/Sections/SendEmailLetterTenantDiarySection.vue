<template>
    <div class="c8-page page-form">
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Send by Contact Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="send_contact_type"
                        class="form-toggle"
                        @change="handleToggleChange"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row
                class="form-row"
                v-if="send_contact_type === 0"
            >
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Contact Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        :withKey="false"
                        v-model="contact_type"
                        v-bind:options="contact_type_list"
                        :displayCount="false"
                    ></vue-dual-list-select>
                    &nbsp;<v-btn
                        small
                        color="normal"
                        @click="loadFrameworkParameters(0, 'reset-prop')"
                        >Select Contact Type/s
                    </v-btn>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >{{ property_manager_label }}
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="property_manager"
                        v-bind:options="property_manager_list"
                        :displayCount="false"
                    ></vue-dual-list-select>
                    &nbsp;<v-btn
                        small
                        color="normal"
                        @click="loadFrameworkParameters(0, 'reset-prop')"
                        >Select {{ property_manager_label }}/s
                    </v-btn>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Show Inactive Properties?
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="show_inactive_properties"
                        @change="handleToggleChange"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            Yes
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            No
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Property
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="property"
                        v-bind:options="property_list"
                    ></vue-dual-list-select>
                    &nbsp;<v-btn
                        small
                        color="normal"
                        @click="loadFrameworkParameters(0)"
                        >Select Property
                    </v-btn>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Diary Type
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="diary_type"
                        :options="diary_type_list"
                        ref="refSearchMethod"
                        trackBy="field_key"
                        label="field_value"
                        return="field_key"
                        placeholder="Please select..."
                    />
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Diary Date
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-icon-date-picker
                        :size="'40'"
                        :id="'from_date' + String(Math.random()).replace('.', '')"
                        v-model="from_date"
                        :edit_form="true"
                    ></cirrus-icon-date-picker>
                    &nbsp; to &nbsp;
                    <cirrus-icon-date-picker
                        :size="'40'"
                        :id="'to_date' + String(Math.random()).replace('.', '')"
                        v-model="to_date"
                        :edit_form="true"
                    ></cirrus-icon-date-picker>
                    &nbsp;<v-btn
                        small
                        color="normal"
                        @click="loadFrameworkParameters(0)"
                        >Refresh
                    </v-btn>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >With Status
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <v-btn-toggle
                        v-model="with_status"
                        @change="handleToggleChange"
                        class="form-toggle"
                        mandatory
                    >
                        <v-btn
                            small
                            tile
                            text
                        >
                            All Diary Entries
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            Open Diary Entries
                        </v-btn>
                        <v-btn
                            small
                            tile
                            text
                        >
                            Closed Diary Entries
                        </v-btn>
                    </v-btn-toggle>
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label required"
                    >Tenant
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <vue-dual-list-select
                        v-model="tenant"
                        :withKey="false"
                        v-bind:options="tenant_list"
                    ></vue-dual-list-select>
                </v-col>
            </v-row>

            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Letter Template
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input"
                >
                    <cirrus-single-select-v2
                        v-model="letter_template"
                        :options="letter_template_list"
                        ref="refSearchMethod"
                        trackBy="field_key"
                        label="field_value"
                        return="field_key"
                        placeholder="Please select..."
                    />
                </v-col>
            </v-row>
        </div>
        <v-card
            class="section-toolbar"
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Placeholders can be used in e-mail and document content</h6>
                &nbsp&nbsp
            </v-card-actions>
        </v-card>
        <div class="page-form">
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Date
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    %day% | %month% | %monthName% | %year%
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >{{ property_manager_label }}
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    {{ placeholder_property_man }}
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Property
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    %propertyName% | %propertyStreet% | %propertyCity% | %propertyState% | %propertyPostCode% |
                    %propertyCode% | %principalOwner% | %principalOwnerABN%
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Lease
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    %leaseName% | %leaseDescription% | %leaseStreet% | %leaseCity% | %leaseState% | %leasePostCode% |
                    %leaseExpiry% | %nextTermCommencement% | %CRN% | %leaseCommencement% | %leaseOption% | %leaseCode% |
                    %rentPM% | %rentAnnual%
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Miscellaneous
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    %unitDescription% | %tenantName% | %leaseContactSalutation% | %nextRentReviewDate% |
                    %nextScheduledInspectionDate% | %nextInsuranceExpiryDate% | %nextInsuranceExpiryType% |
                    %nextInsuranceExpiryNote%
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Mail
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    %mailingName% | %mailingAddress% | %mailingCity% | %mailingState% | %mailingPostCode% |
                    %mailingCountry%
                </v-col>
            </v-row>
            <v-row class="form-row">
                <v-col
                    xs="12"
                    sm="2"
                    md="2"
                    class="form-label"
                    >Description
                </v-col>
                <v-col
                    xs="12"
                    sm="10"
                    md="10"
                    class="form-input pt-3"
                >
                    %diaryDescription%
                </v-col>
            </v-row>
        </div>
    </div>
</template>
<script>
import global_mixins from '../../plugins/mixins';
import axios from 'axios';
import { PLACEHOLDER_PROPERTY_MANAGER_WITHOUT_ID } from '../constants';
import { getActivePropertiesOptions, getInActivePropertiesOptions } from '../functions';

export default {
    mixins: [global_mixins],
    props: {
        search_method: { type: String, default: 'Tenant Diary' },
        country_default_settings: { type: String, default: '' },
    },
    data() {
        const now = new Date();

        // Calculate the first day of this month and next month
        const firstDayCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const firstDayNextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);

        // Reusable date formatting function
        const formatDate = (date) => {
            const day = String(date.getDate()).padStart(2, '0'); // e.g., "01"
            const month = String(date.getMonth() + 1).padStart(2, '0'); // e.g., "02"
            const year = date.getFullYear(); // Full year
            return `${day}/${month}/${year}`; // Construct format dd/mm/yyyy
        };
        return {
            letter_template: '',
            contact_type: [],
            property_manager: [],
            property: [],
            tenant: [],
            use_letter_head: 0,
            mergeTag: [],
            show_inactive_properties: 1,
            show_current_tenant: 0,
            send_contact_type: 1,
            letter_template_list: [],
            contact_type_list: [],
            property_manager_list: [],
            property_list: [],
            tenant_list: [],
            diary_type_list: [],
            diary_type: '',
            with_status: 0,
            from_date: formatDate(firstDayCurrentMonth),
            to_date: formatDate(firstDayNextMonth),
            property_manager_label: '',
            useDocxFile: '0',
            letterFilename: null,
            letterFilenameEn: null,
            placeholder_property_man: PLACEHOLDER_PROPERTY_MANAGER_WITHOUT_ID,
        };
    },
    methods: {
        async loadFrameworkParameters(type = 0, btn = '') {
            this.$emit('update:loading', true);
            let url = '?module=administration&command=letter';
            if (type === 1) url = '?module=administration&command=letter&action=changeMethod';
            if (type === 2) url = '?module=administration&command=letter&resetTenants=1';
            if (btn === 'reset-prop') this.property = [];
            const contact_type_value = this.contact_type.map((item) => item.fieldKey);
            const property_manager_value = this.property_manager.map((item) => item.fieldKey);
            const property_value = this.property.map((item) => item.fieldKey);

            this.loading_page_setting = true;
            var form_data = new FormData();
            if (contact_type_value.length > 0) form_data.append('contactTypeID', contact_type_value.join('::'));
            if (property_manager_value.length > 0)
                form_data.append('propertyManagerID', property_manager_value.join('::'));
            if (property_value.length > 0) form_data.append('propertyID', property_value.join('::'));
            if (this.letter_template) form_data.append('letterID', this.letter_template);
            if (this.diary_type) form_data.append('diary_type', this.diary_type);
            let status = 'A';
            if (this.with_status === 1) status = 'O';
            if (this.with_status === 2) status = 'C';
            form_data.append('fromDate', this.from_date);
            form_data.append('toDate', this.to_date);
            form_data.append('searchMethod', this.search_method);
            form_data.append('showContactTypes', this.send_contact_type === 1 ? 'No' : 'Yes');
            form_data.append('showCurrentOnly', this.show_current_tenant === 1 ? 'No' : 'Yes');
            form_data.append('showActiveOnly', this.show_inactive_properties === 1 ? 'No' : 'Yes');
            form_data.append('useLetterhead', this.use_letter_head === 0 ? '1' : '0');
            form_data.append('status', status);
            form_data.append('from_api', 1);
            form_data.append('no_load', true);
            await axios.post(url, form_data).then((response) => {
                this.extractResponseData(response.data);
                this.loading_page_setting = false;
            });
        },
        extractResponseData(data) {
            this.useDocxFile = data?.useDocxFile;
            this.letterFilename = data?.letterFilename;
            this.letterFilenameEn = data?.letterFilenameEn;
            this.letter_template_list = [];
            this.property_manager_list = [];
            this.property_list = [];
            this.contact_type_list = [];
            this.tenant_list = [];
            this.diary_type_list = [];
            let propertyManagerList = this.ensureParameterIsAnArray(data?.propertyManagerList);
            let propertyList = this.ensureParameterIsAnArray(data?.propertyList);
            let contactTypeList = data?.contactTypeList ?? [];
            let tenantList = this.ensureParameterIsAnArray(data?.tenantList);
            let templateList = this.ensureParameterIsAnArray(data?.templateList);
            let diary_type_list = this.ensureParameterIsAnArray(data?.diary_type_list);
            this.$emit('update:updateSubject', data?.emailSubject);

            let temp_value = [];
            for (const i in propertyManagerList) {
                temp_value.push({
                    fieldKey: propertyManagerList[i].managerID,
                    fieldValue: propertyManagerList[i].managerName,
                });
            }
            if (temp_value.length > 0)
                this.property_manager_list.push({
                    fieldGroupNames: '',
                    fieldGroupValues: temp_value,
                });
            temp_value = [];
            let active_tenants = tenantList.filter((m) => m.leaseStatus === 'C');
            for (const i in active_tenants) {
                temp_value.push({
                    fieldKey: active_tenants[i].prop_leaseID,
                    fieldValue: active_tenants[i].leaseNameWithEmail,
                });
            }
            if (temp_value.length > 0)
                this.tenant_list.push({
                    fieldGroupNames: 'Current',
                    fieldGroupValues: temp_value,
                });

            let other_tenants = tenantList.filter((m) => m.leaseStatus !== 'C');
            temp_value = [];
            for (const i in other_tenants) {
                temp_value.push({
                    fieldKey: other_tenants[i].prop_leaseID,
                    fieldValue: other_tenants[i].leaseNameWithEmail,
                });
            }
            if (temp_value.length > 0)
                this.tenant_list.push({
                    fieldGroupNames: 'Vacated',
                    fieldGroupValues: temp_value,
                });

            temp_value = [];
            temp_value = getActivePropertiesOptions(propertyList);
            if (temp_value.length > 0)
                this.property_list.push({
                    fieldGroupNames: 'Active',
                    fieldGroupValues: temp_value,
                });

            temp_value = [];
            temp_value = getInActivePropertiesOptions(propertyList);
            if (temp_value.length > 0)
                this.property_list.push({
                    fieldGroupNames: 'Inactive',
                    fieldGroupValues: temp_value,
                });

            for (const i in templateList) {
                this.letter_template_list.push({
                    field_key: templateList[i].letterTemplateID,
                    field_value: templateList[i].letterTemplateName,
                });
            }

            temp_value = [];
            for (const key in contactTypeList) {
                temp_value.push({ fieldKey: key, fieldValue: contactTypeList[key] });
            }
            if (temp_value.length > 0)
                this.contact_type_list.push({
                    fieldGroupNames: '',
                    fieldGroupValues: temp_value,
                });

            for (const i in diary_type_list) {
                this.diary_type_list.push({
                    field_key: diary_type_list[i].parameterID,
                    field_value: diary_type_list[i].parameterDescription,
                });
            }

            if (data?.letterContent !== undefined) this.$emit('update:loadLetterContent', data.letterContent);
            if (data?.pdfContent !== undefined) this.$emit('update:loadPdfContent', data.pdfContent);

            this.$emit('update:mergeTag', JSON.parse(data.mergeTag));
            if (data?.customEmailList !== undefined) {
                let custom_email_list = {
                    clientFromName: data.clientFromName,
                    clientReplyTo: data.clientReplyTo,
                    clientSendFrom: data.clientSendFrom,
                    customEmailList: data.customEmailList,
                };
                this.$emit('update:updateCustomEmailList', custom_email_list);
            }
            this.updateParentData();
            this.$emit('update:loading', false);
        },
        handleToggleChange: function () {
            this.loadFrameworkParameters(2);
        },
        updateParentData: function () {
            let data = {
                property: this.property,
                tenant: this.tenant,
                send_contact_type: this.send_contact_type === 0 ? 'Yes' : 'No',
                contact_type: this.contact_type,
                useDocxFile: this.useDocxFile,
                letterFilename: this.letterFilename,
                letterFilenameEn: this.letterFilenameEn,
                letter_id: this.letter_template,
                diary_type: this.diary_type,
                from_date: this.from_date,
                to_date: this.to_date,
            };
            this.$emit('update:updateFromSection', data);
        },
    },
    watch: {
        letter_template: function () {
            if (this.letter_template) {
                this.loadFrameworkParameters();
            }
        },
        property: function () {
            if (this.property.length === 0) this.tenant = [];
            this.updateParentData();
        },
        tenant: function () {
            this.updateParentData();
        },
    },
    created() {
        this.loadFrameworkParameters(1);
    },
    mounted() {
        let country_default_settings = JSON.parse(atob(this.country_default_settings));
        this.property_manager_label = this.ucwords(country_default_settings.property_manager);
    },
};
</script>
