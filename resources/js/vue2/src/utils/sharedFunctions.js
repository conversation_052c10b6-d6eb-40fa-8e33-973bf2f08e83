import { DARK_MODE_CLASS, EMAIL_ADDRESS_BOOK_TYPES } from '../constants';
import isEmpty from 'lodash/isEmpty';

/**
 * Collect the address book settings based on the company user types
 *
 * @param {array} settingsList contains all the email centralisation settings
 * @param {object} companyTypes contains the company types settings (debtor, owner, supplier)
 *
 * @returns {array} filtered email address book settings
 */
export function collectAddressBookSettings(settingsList = [], companyTypes) {
    return settingsList.filter((setting) => {
        return (
            (setting.main_code === EMAIL_ADDRESS_BOOK_TYPES.OWNER && companyTypes.owner) ||
            (setting.main_code === EMAIL_ADDRESS_BOOK_TYPES.TENANT && companyTypes.debtor) ||
            (setting.main_code === EMAIL_ADDRESS_BOOK_TYPES.SUPPLIER && companyTypes.supplier)
        );
    });
}

/**
 * Checks if the page is currently in dark mode by verifying
 * if the body element contains the specified dark mode class.
 *
 * @return {boolean} if the body has the dark mode class or not
 */
export function isDarkMode() {
    const pageBody = document.body;
    return pageBody.classList.contains(DARK_MODE_CLASS);
}

/**
 * Formats a given text string by inserting a specified delimiter at a specified frequency.
 *
 * @param {string} text - The input text to be formatted.
 * @param {string} [delimiter=''] - The delimiter to insert into the text. Defaults to an empty string.
 * @param {number} [frequency=3] - The frequency, in number of characters, at which the delimiter is inserted. Defaults to 3.
 *
 * @return {string} The formatted text with the delimiter inserted at the specified frequency.
 */
export function formatWithDelimiter(text, delimiter = '-', frequency = 3) {
    if (isEmpty(text)) return '';

    if (text.length <= frequency) return text;

    const output = [...text]
        .map((char, index) => (index > 0 && (text.length - index) % frequency === 0 ? delimiter : '') + char)
        .join('');

    return output;
}

/**
 * Transforms the provided validation response data into a flattened and reformatted array structure.
 *
 * @param {Object} validationResponse - Object containing validation errors, where each key holds an array of error details.
 * @return {Array} A reformatted array where each error item is encapsulated in its own array.
 */
export function formatValidationResponseData(validationResponse) {
    return Object.values(validationResponse?.errors ?? [])
        .flat()
        .map((errorItem) => [errorItem]);
}

/**
 * Formats an identifier as a fixed-length 6-character string, left-padded with zeros.
 *
 * Behavior:
 * - The input is coerced to a string.
 * - If the result is shorter than 6 characters, zeros are added on the left.
 * - If the result is longer than 6 characters, only the rightmost 6 characters are kept.
 * - For null/undefined, this returns "000000".
 *
 * Examples:
 * - displayIdCode(123) => "000123"
 * - displayIdCode("45") => "000045"
 * - displayIdCode("1234567") => "234567"
 *
 * @param {number|string|null|undefined} id The identifier to format.
 * @returns {string} A 6-character zero-padded string.
 */
export function displayIdCode(id) {
    const str = String(id ?? '');
    return `${'0'.repeat(6)}${str}`.slice(-6);
}
