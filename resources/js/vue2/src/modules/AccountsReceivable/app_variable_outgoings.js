import Vue from 'vue';
// VUETIFY
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../../plugins/vuetify';
import '../../plugins/global';
import '../../plugins/request';
import store from '../../vuex/modules/leases/store';
import SuiVue from 'semantic-ui-vue';
import Multiselect from 'vue-multiselect';
// import vSelect from 'vue-select'
window.axios = require('axios');
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
window.Vue = require('vue');

window.axios.defaults.headers.common = {
    Authorization: 'Bearer ' + localStorage.getItem('Authentication'),
    'Content-Type': 'application/x-www-form-urlencoded',
};

Vue.use(SuiVue);
/**
 * Next, we will create a fresh Vue application instance and attach it to
 * the page. Then, you may begin adding components to this application
 * or customize the JavaScript scaffolding to fit your unique needs.
 */
import VariableOutgoings from '../../components/modules/AccountsReceivable/Templates/VariableOutgoingsTemplate.vue';
// import VariableOutgoings from '../../components/modules/Leases/Templates/LeaseFormTemplate.vue';
Vue.component('vue-modal', require('../../components/elements/VueModal.vue').default);
Vue.component('cirrus-page-header', require('../../components/elements/CirrusPageHeader.vue').default);
Vue.component('cirrus-lease-shortcut-component', require('../../components/elements/CirrusLeaseShortcuts.vue').default);
Vue.component('cirrus-text-area', require('../../components/elements/CirrusTextarea.vue').default);
Vue.component('cirrus-date-picker', require('../../components/elements/CirrusDatePicker.vue').default);
Vue.component('cirrus-icon-date-picker', require('../../components/elements/CirrusIconDatePicker.vue').default);
Vue.component(
    'cirrus-email-centralisation',
    require('../../components/elements/CirrusEmailCentralisation.vue').default,
);
Vue.component('cirrus-input', require('../../components/elements/CirrusInput.vue').default);
Vue.component('cirrus-single-upload-button', require('../../components/elements/CirrusSingleUploadButton.vue').default);
Vue.component(
    'cirrus-single-upload-button2',
    require('../../components/elements/CirrusSingleUploadButtonV2.vue').default,
);
Vue.component('cirrus-radio-group', require('../../components/elements/CirrusRadioGroup.vue').default);
Vue.component('cirrus-server-error', require('../../components/elements/CirrusServerError.vue').default);
Vue.component('vue-dual-list-select', require('../../components/elements/VueDualListSelect.vue').default);
Vue.component('cirrus-loader', require('../../components/elements/CirrusLoader.vue').default);
Vue.component('cirrus-content-loader', require('../../components/elements/CirrusContentLoader.vue').default);
Vue.component(
    'cirrus-multiple-upload-button',
    require('../../components/elements/CirrusMultipleUploadButton.vue').default,
);

// CREATE VUE
new Vue({
    el: '#app',
    SuiVue,
    vuetify,
    store,
    components: {
        'main-template-component': VariableOutgoings,
        multiselect: Multiselect,
    },
}).$mount('#app');
