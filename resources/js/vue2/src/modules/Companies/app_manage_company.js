/**
 * First we will load all of this project's JavaScript dependencies which
 * includes Vue and other libraries. It is a great starting point when
 * building robust, powerful web applications using Vue and Laravel.
 */

import Vue from 'vue';

//require('../../bootstrap');
window.Vue = require('vue');
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../../plugins/vuetify';
import '../../plugins/global';
import '../../plugins/request';
import store from '../../vuex/modules/Companies/Store';

/**
 * Next, we will create a fresh Vue application instance and attach it to
 * the page. Then, you may begin adding components to this application
 * or customize the JavaScript scaffolding to fit your unique needs.
 */

Vue.component(
    'main-template-component',
    require('../../components/modules/Companies/templates/ManageCompanyFormTemplate.vue').default,
);

Vue.component('vue-modal', require('../../components/elements/VueModal.vue').default);

Vue.component('cirrus-text-area', require('../../components/elements/CirrusTextarea.vue').default);
Vue.component('cirrus-date-picker', require('../../components/elements/CirrusDatePicker.vue').default);
Vue.component('cirrus-input', require('../../components/elements/CirrusInput.vue').default);
Vue.component('cirrus-single-upload-button', require('../../components/elements/CirrusSingleUploadButton.vue').default);
Vue.component(
    'cirrus-single-upload-button2',
    require('../../components/elements/CirrusSingleUploadButtonV2.vue').default,
);
Vue.component('cirrus-radio-group', require('../../components/elements/CirrusRadioGroup.vue').default);
Vue.component('cirrus-server-error', require('../../components/elements/CirrusServerError.vue').default);
Vue.component('vue-dual-list-select', require('../../components/elements/VueDualListSelect.vue').default);
Vue.component('cirrus-loader', require('../../components/elements/CirrusLoader.vue').default);
Vue.component('cirrus-content-loader', require('../../components/elements/CirrusContentLoader.vue').default);
Vue.component('cirrus-single-select', require('../../components/elements/CirrusSingleSelect.vue').default);
Vue.component('cirrus-single-select-group', require('../../components/elements/CirrusSingleSelectGroup.vue').default);
Vue.component('cirrus-icon-date-picker', require('../../components/elements/CirrusIconDatePicker.vue').default);
Vue.component(
    'cirrus-email-centralisation',
    require('../../components/elements/CirrusEmailCentralisation.vue').default,
);
Vue.component('cirrus-single-select-v2', require('../../components/elements/CirrusSingleSelectV2.vue').default);
// Vue.mixin(mixins);
new Vue({
    vuetify,
    store,
}).$mount('#app');
const Swal = require('sweetalert2');
function errorResponseHandler(error) {
    // check for errorHandle config
    if (error.config.hasOwnProperty('errorHandle') && error.config.errorHandle === false) {
        return Promise.reject(error);
    }

    // if has response show the error
    if (error.response) {
        const toast = Swal.mixin({
            toast: true,
            position: 'bottom-end',
            showConfirmButton: false,
            timer: 60000,
        });

        toast({
            type: 'error',
            title: 'Request failed, please try again',
        });
    }
}
