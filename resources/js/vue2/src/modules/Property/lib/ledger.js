import { requestFrom<PERSON>pi } from '../../../plugins/request';
import isEmpty from 'lodash/isEmpty';

/**
 * Property Ledger API client utilities.
 *
 * Functions in this module post to backend endpoints via `requestFrom<PERSON>pi`
 * and return relevant slices of `response.data`.
 */

/**
 * Shape of the payload used to create/update a Property Ledger.
 * @typedef {Object} PropertyLedgerPayload
 * @property {string} [id] Optional. If provided and not empty, will be sent for updates.
 * @property {string} propertyLedgerCode
 * @property {string} description
 * @property {string} [ledgerType]
 * @property {string} [taxCode]
 * @property {boolean} isUsePrincipalOwnersEftDetails
 * @property {boolean} isPropertyLedgerStatusActive
 * @property {boolean} isAllowedOwnerPayments
 * @property {string|null} [defaultNoteForWithheldAmount]
 * @property {number|null} [defaultAmountToWithhold]
 * @property {string} [bankCode]
 */

/**
 * Validation error bag returned by the API on HTTP 422.
 * Keys are field names, values are arrays of error messages.
 * @typedef {Object.<string, string[]>} ValidationErrors
 */

/**
 * Fetches grouped account list for dropdowns.
 * POST: loadAllAccountDropdown
 * @returns {Promise<unknown>} Resolves with `response.data.grouped`.
 */

export async function fetchAllAccountList() {
    const response = await requestFromApi.post('loadAllAccountDropdown');
    return response.data.grouped;
}

/**
 * Fetches property bank account list.
 * POST: property/fetch/property-details-lists
 * @returns {Promise<unknown>} Resolves with `response.data.bank_account_list`.
 */

export async function fetchBankAccountList() {
    const response = await requestFromApi.post('property/fetch/property-details-lists');
    return response.data.bank_account_list;
}

/**
 * Fetches VAT/tax status list.
 * POST: ui/fetch/tax-rate-list
 * @returns {Promise<unknown>} Resolves with `response.data.param_list`.
 */

export async function fetchVATStatusList() {
    const response = await requestFromApi.post('ui/fetch/tax-rate-list');
    return response.data.param_list;
}

/**
 * Builds multipart form data for creating/updating a property ledger.
 * Omits `id` if it is empty/falsy.
 * @param {string} propertyCode
 * @param {PropertyLedgerPayload} object
 * @returns {FormData}
 */

function createLedgerFormData(propertyCode, object) {
    let formData = new FormData();
    formData.append('propertyCode', propertyCode);
    if (!isEmpty(object.id)) {
        formData.append('id', object.id);
    }
    formData.append('propertyLedgerCode', object.propertyLedgerCode);
    formData.append('description', object.description);
    formData.append('ledgerType', object.ledgerType ?? '');
    formData.append('taxCode', object.taxCode ?? '');
    formData.append('isUsePrincipalOwnersEftDetails', object.isUsePrincipalOwnersEftDetails);
    formData.append('isPropertyLedgerStatusActive', object.isPropertyLedgerStatusActive);
    formData.append('isAllowedOwnerPayments', object.isAllowedOwnerPayments);
    formData.append('defaultNoteForWithheldAmount', object.defaultNoteForWithheldAmount);
    formData.append('defaultAmountToWithhold', object.defaultAmountToWithhold);
    formData.append('bankCode', object.bankCode ?? '');
    return formData;
}

/**
 * Helper to POST FormData and return `response.data`.
 * Converts HTTP 422 responses into a thrown `ValidationErrors` object.
 * @template T
 * @param {string} url
 * @param {FormData} formData
 * @returns {Promise<T>}
 * @throws {ValidationErrors} When the server responds with HTTP 422.
 * @throws {any} On other network or server errors.
 */

async function handleApiRequest(url, formData) {
    try {
        const response = await requestFromApi.post(url, formData);
        return response.data;
    } catch (error) {
        if (error.response && error.response.status === 422) {
            throw error.response.data.errors;
        }
        throw error;
    }
}

/**
 * Loads property ledger details for a given property.
 * POST: property/ledger/load
 * @param {string} propertyCode
 * @returns {Promise<unknown>} Resolves with `response.data`.
 */

export async function fetchPropertyLedger(propertyCode) {
    let formData = new FormData();
    formData.append('propertyCode', propertyCode);
    return handleApiRequest('property/ledger/load', formData);
}

/**
 * Creates a new property ledger.
 * POST: property/ledger/save
 * @param {string} propertyCode
 * @param {PropertyLedgerPayload} object
 * @returns {Promise<unknown>} Resolves with `response.data`.
 * @throws {ValidationErrors} When validation fails (HTTP 422).
 */

export async function savePropertyLedger(propertyCode, object) {
    const formData = createLedgerFormData(propertyCode, object);
    return handleApiRequest('property/ledger/save', formData);
}

/**
 * Updates an existing property ledger.
 * POST: property/ledger/update
 * @param {string} propertyCode
 * @param {PropertyLedgerPayload} object Should include a non-empty `id`.
 * @returns {Promise<unknown>} Resolves with `response.data`.
 * @throws {ValidationErrors} When validation fails (HTTP 422).
 */
export async function updatePropertyLedger(propertyCode, object) {
    const formData = createLedgerFormData(propertyCode, object);
    return handleApiRequest('property/ledger/update', formData);
}

/**
 * Deletes a property ledger by identifier.
 * POST: property/ledger/delete
 * @param {string|number} id
 * @returns {Promise<unknown>} Resolves with `response.data`.
 */
export async function deletePropertyLedger(id) {
    const formData = new FormData();
    formData.append('id', id);
    return handleApiRequest('property/ledger/delete', formData);
}
