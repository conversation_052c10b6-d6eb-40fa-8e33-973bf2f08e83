import { requestFromApi } from '../../../plugins/request';

async function handleApiRequest(url, formData, isReturningResponse = false) {
    try {
        const response = await requestFromApi.post(url, formData);
        if (isReturningResponse) return response;
        return response.data;
    } catch (error) {
        if (error.response && error.response.status === 422) {
            throw error.response.data.errors;
        }
        throw error;
    }
}

export async function fetchFeesAndChargesData(formData) {
    return handleApiRequest('property/fees-and-charges/load', formData, true);
}

export async function saveSundryData(formData) {
    return handleApiRequest('property/sundry-charge/store', formData);
}

export async function updateSundryData(formData) {
    return handleApiRequest('property/sundry-charge/update', formData);
}
