import { request<PERSON>rom<PERSON><PERSON> } from '../../../plugins/request';
/**
 * Management Fee API client utilities.
 *
 * Functions in this module post FormData to backend endpoints via `requestFrom<PERSON>pi`
 * and return `response.data`. Validation errors from the server (HTTP 422)
 * are normalized and thrown as a field-error bag.
 */

/**
 * Validation error bag returned by the API on HTTP 422.
 * Keys are field names, values are arrays of error messages.
 * @typedef {Object.<string, string[]>} ValidationErrors
 */

/**
 * Helper to POST FormData and return `response.data`.
 * Converts HTTP 422 responses into a thrown `ValidationErrors` object.
 * @template T
 * @param {string} url Backend relative URL.
 * @param {FormData} formData Payload to send.
 * @returns {Promise<T>}
 * @throws {ValidationErrors} When the server responds with HTTP 422.
 * @throws {any} On other network or server errors.
 */

async function handleApiRequest(url, formData) {
    try {
        const response = await requestFromApi.post(url, formData);
        return response.data;
    } catch (error) {
        if (error.response && error.response.status === 422) {
            throw error.response.data.errors;
        }
        throw error;
    }
}

/**
 * Fetches management fee data for a property.
 * POST: property/fetch/management-fees
 * @param {FormData} formData Should include the identifiers required by the endpoint.
 * @returns {Promise<any>} Resolves with the response payload containing management fee data.
 * @throws {ValidationErrors|any}
 */

export async function fetchManagementFeeData(formData) {
    return handleApiRequest('property/fetch/management-fees', formData);
}

/**
 * Creates or updates management fee details for a property.
 * POST: property/store/management-fees
 * @param {FormData} formData Must include the fields expected by the backend for saving.
 * @returns {Promise<any>} Resolves with the response payload after save.
 * @throws {ValidationErrors|any}
 */

export async function saveManagementFeeData(formData) {
    return handleApiRequest('property/store/management-fees', formData);
}

/**
 * Updates existing management fee details for a property.
 * POST: property/update/management-fees
 * @param {FormData} formData Must include identifiers (e.g., propertyCode, id) and fields to update.
 * @returns {Promise<any>} Resolves with the response payload after update.
 * @throws {ValidationErrors|any}
 */
export async function updateManagementFeeData(formData) {
    return handleApiRequest('property/update/management-fees', formData);
}
