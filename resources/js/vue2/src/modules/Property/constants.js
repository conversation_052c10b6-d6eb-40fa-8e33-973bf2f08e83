import PropertyMainForm from '../../components/modules/properties/Forms/PropertyMainForm.vue';
import PropertyDiaryForm from '../../components/modules/properties/Forms/PropertyDiaryForm.vue';
import PropertyCalendarForm from '../../components/modules/properties/Forms/PropertyCalendarForm.vue';
import PropertyContactForm from '../../components/modules/properties/Forms/PropertyContactForm.vue';
import PropertyFloorNUnitForm from '../../components/modules/properties/Forms/PropertyFloorNUnitForm.vue';
import PropertyInspectionForm from '../../components/modules/properties/Forms/PropertyInspectionForm.vue';
import PropertyInsuranceForm from '../../components/modules/properties/Forms/PropertyInsuranceForm.vue';
import PropertyNoteForm from '../../components/modules/properties/Forms/PropertyNoteForm.vue';
import PropertyFeesNChargesForm from '../../components/modules/properties/Forms/PropertyFeesNChargesForm.vue';
import PropertyTakeOnBalanceForm from '../../components/modules/properties/Forms/PropertyTakeOnBalanceForm.vue';
import PropertyManagementFeeForm from '../../components/modules/properties/Forms/PropertyManagementFeeForm.vue';
import PropertyManagementAgreementForm from '../../components/modules/properties/Forms/PropertyManagementAgreementForm.vue';
import PropertyManagementDetailForm from '../../components/modules/properties/Forms/PropertyManagementDetailForm.vue';
import PropertyParkingBaysForm from '../../components/modules/properties/Forms/PropertyParkingBaysForm.vue';
import PropertyFundForm from '../../components/modules/properties/Forms/PropertyFundForm.vue';
import PropertyKeysForm from '../../components/modules/properties/Forms/PropertyKeysForm.vue';
import PropertyRecoverableSplitForm from '../../components/modules/properties/Forms/PropertyRecoverableSplitForm.vue';
import PropertyBudgetSection from '../../components/modules/properties/Forms/PropertyBudgetSection.vue';
import PropertyDocumentFormV2 from '../../DocumentDirectory/sections/PropertyDocumentFormV2.vue';
import PropertyCommunicationHistoryForm from '../../components/modules/properties/Forms/PropertyCommunicationHistoryForm.vue';
import PropertyBankBalancesSection from '../../components/modules/properties/sections/PropertyBankBalancesSection.vue';
import PropertyLedgerSection from '../../modules/Property/sections/PropertyLedgerSection.vue';

export const PROPERTY_TAB_KEYS = Object.freeze({
    OVERVIEW: 'overview-tab',
    MANAGEMENT: 'management-tab',
    LEDGER: 'ledger-tab',
    FLOORS_AND_UNITS: 'floors-and-unit-tab',
    DIARY: 'diary-tab',
    CALENDAR: 'calendar-tab',
    NOTES_AND_ADDITIONAL: 'notes-and-additional-tab',
    COMMUNICATION_AND_DOCUMENT: 'communication-and-document-tab',
});

export const PROPERTY_SUMMARY_TABS = [
    {
        name: 'Overview',
        value: PROPERTY_TAB_KEYS.OVERVIEW,
        icon: 'business',
        components: {
            PropertyBankBalancesSection: PropertyBankBalancesSection,
            PropertyMainForm: PropertyMainForm,
            PropertyContactForm: PropertyContactForm,
        },
    },
    {
        name: 'Ledger',
        value: PROPERTY_TAB_KEYS.LEDGER,
        icon: 'monetization_on',
        disabled: false,
        components: {
            PropertyLedgerSection: PropertyLedgerSection,
        },
    },
    {
        name: 'Management',
        value: PROPERTY_TAB_KEYS.MANAGEMENT,
        icon: 'monetization_on',
        disabled: false,
        components: {
            PropertyManagementDetailForm: PropertyManagementDetailForm,
            PropertyManagementAgreementForm: PropertyManagementAgreementForm,
            PropertyManagementFeeForm: PropertyManagementFeeForm,
            PropertyFeesNChargesForm: PropertyFeesNChargesForm,
            PropertyRecoverableSplitForm: PropertyRecoverableSplitForm,
            PropertyFundForm: PropertyFundForm,
        },
    },
    {
        name: 'floor(s) and Unit(s)',
        value: PROPERTY_TAB_KEYS.FLOORS_AND_UNITS,
        icon: 'business',
        disabled: false,
        components: {
            PropertyFloorNUnitForm: PropertyFloorNUnitForm,
            PropertyParkingBaysForm: PropertyParkingBaysForm,
        },
    },
    {
        name: 'Diary',
        value: PROPERTY_TAB_KEYS.DIARY,
        icon: 'date_range',
        disabled: false,
        components: {
            PropertyDiaryForm: PropertyDiaryForm,
        },
    },
    {
        name: 'Calendar',
        value: PROPERTY_TAB_KEYS.CALENDAR,
        icon: 'calendar_today',
        disabled: false,
        components: {
            PropertyCalendarForm: PropertyCalendarForm,
        },
    },
    {
        name: 'Notes and Additional Details',
        value: PROPERTY_TAB_KEYS.NOTES_AND_ADDITIONAL,
        icon: 'notes',
        disabled: false,
        components: {
            PropertyKeysForm: PropertyKeysForm,
            PropertyNoteForm: PropertyNoteForm,
            PropertyInspectionForm: PropertyInspectionForm,
            PropertyInsuranceForm: PropertyInsuranceForm,
            PropertyBudgetSection: PropertyBudgetSection,
            PropertyTakeOnBalanceForm: PropertyTakeOnBalanceForm,
        },
    },
    {
        name: 'Communication and Document Details',
        value: PROPERTY_TAB_KEYS.COMMUNICATION_AND_DOCUMENT,
        icon: 'attachment',
        disabled: false,
        components: {
            PropertyDocumentFormV2: PropertyDocumentFormV2,
            PropertyCommunicationHistoryForm: PropertyCommunicationHistoryForm,
        },
    },
];
