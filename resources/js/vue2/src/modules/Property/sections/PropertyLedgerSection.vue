<script>
import { bus } from '../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../plugins/mixins';
import { mapState } from 'vuex';
import PropertyLedgerDetailModal from './PropertyLedgerDetailModal.vue';
import { deletePropertyLedger, fetchPropertyLedger } from '../lib/ledger';
import { LEDGER_DETAIL_DEFAULT } from '../enums';
import { displayIdCode } from '../../../utils/sharedFunctions';

export default {
    props: {
        propertyCode: { type: String, default: '' },
        newProperty: { type: Boolean, default: false },
        isInactive: { type: Boolean, default: false },
    },
    mixins: [global_mixins],
    components: {
        'ledger-detail-modal': PropertyLedgerDetailModal,
    },
    created() {
        bus.$on('loadPropertyLedgerSection', (data) => {
            this.loadForm();
        });
        bus.$on('setInactiveStatus', (data) => {
            if (data) {
                this.isEditMode = false;
            }
        });
    },
    mounted() {
        this.edit_form = this.new_property;
        this.loadForm();
    },
    data() {
        return {
            formType: 'PROPERTY',
            formSection: 'PROPERTY_LEDGER',
            headers: [
                { text: '#', value: 'index', sortable: false, width: '40px' },
                { text: 'ID', value: 'itemNo', sortable: false, width: '50px' },
                { text: 'Property Ledger Code', value: 'propertyLedgerCode' },
                { text: 'Property Ledger Name', value: 'description' },
                { text: 'Bank Account', value: 'bankDescription' },
                { text: '', value: 'action1', align: 'end', sortable: false, width: '78px' },
            ],
            searchInDatatable: '',
            isEditMode: false,
            showActivityLogModal: false,
            page: 1,
            page_count: 0,
            items_per_page: 20,
            ledgerList: [],
            ledgerDetail: {},
            showLedgerDetailModal: false,
            isLoading: false,
        };
    },
    computed: {
        ...mapState([
            'current_db',
            'user_type',
            'username',
            'cirrus8_api_url',
            'pm_property_form_read_only',
            'client_timezone',
        ]),
        isEditMode() {
            return this.isEditMode;
        },
    },
    methods: {
        displayIdCode,
        isEditable() {
            if (!this.new_property) {
                return !this.formSectionReadOnly(
                    this.pm_property_form_read_only,
                    this.formType,
                    this.formSection,
                    this.isInactive,
                );
            }
            return this.new_property;
        },
        doubleClickForm() {
            this.isEditMode = !this.formSectionReadOnly(
                this.pm_property_form_read_only,
                this.form_type,
                this.form_section,
                this.is_inactive,
            );
        },
        async loadForm() {
            this.isEditMode = false;
            this.isLoading = true;
            try {
                this.ledgerList = await fetchPropertyLedger(this.propertyCode);
            } catch (errors) {
                if (typeof errors === 'object') {
                    console.log([Object.values(errors).flat()]);
                } else {
                    console.log(['An unexpected error occurred']);
                }
            }
            this.isLoading = false;
        },
        showAddModal() {
            this.ledgerDetail = LEDGER_DETAIL_DEFAULT;
            this.showLedgerDetailModal = true;
        },
        async deleteData(id) {
            let dialogProp = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialogProp);
            if (result !== 1) return;
            await deletePropertyLedger(id);
            await this.loadForm();
        },
        openDetailModal(id) {
            const foundLedger = this.ledgerList.find((item) => item.id === id);
            if (foundLedger) {
                this.ledgerDetail = foundLedger;
                this.showLedgerDetailModal = true;
            }
        },
    },
    watch: {
        propertyCode: function () {
            this.loadForm();
        },
    },
};
</script>
<template>
    <div
        v-on:dblclick="doubleClickForm"
        style="max-width: 100%"
    >
        <v-card
            class="section-toolbar"
            dark
            color="titleHeader"
            text
            tile
        >
            <v-card-actions>
                <h6 class="title font-weight-black">Ledger</h6>
                <v-spacer></v-spacer>
                <cirrus-input
                    inputFormat="search"
                    v-model="searchInDatatable"
                    placeholder="Search"
                    :edit_form="true"
                    class="pr-1"
                ></cirrus-input>
                <v-btn
                    x-small
                    v-if="isEditable"
                    icon
                    @click="showAddModal()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-if="!isEditMode"
                    icon
                    @click="isEditMode = true"
                    v-show="isEditable"
                >
                    <v-icon>edit</v-icon>
                </v-btn>
                <v-btn
                    x-small
                    v-show="isEditable"
                    v-if="isEditMode && !newProperty"
                    icon
                    @click="loadForm()"
                >
                    <v-icon color="red">undo</v-icon>
                </v-btn>

                <v-btn
                    x-small
                    icon
                    @click="loadForm"
                    v-if="isEditable"
                >
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn
                    icon
                    x-small
                    @click="showActivityLogModal = true"
                >
                    <v-icon>history</v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
        <cirrus-content-loader v-if="isLoading"></cirrus-content-loader>
        <v-data-table
            class="c8-datatable-custom"
            dense
            v-if="!isLoading"
            item-key="id"
            :headers="headers"
            :items="ledgerList"
            :items-per-page="items_per_page"
            hide-default-footer
            :page.sync="page"
            :total-visible="20"
            @page-count="page_count = $event"
            :search="searchInDatatable"
        >
            <template v-slot:item.index="{ item, index }">
                {{ index + 1 }}
            </template>
            <template v-slot:item.itemNo="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ displayIdCode(item.id) }}</span>
                </div>
            </template>
            <template v-slot:item.bankDescription="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.bankCode }} - {{ item.bank.accountName }}</span>
                </div>
            </template>
            <template v-slot:item.propertyLedgerDescription="{ item }">
                <div class="form-row no-border-line">
                    <span class="form-input-text">{{ item.propertyLedgerCode }} - {{ item.description }}</span>
                </div>
            </template>

            <template v-slot:item.action1="{ item }">
                <v-icon
                    small
                    @click="openDetailModal(item.id)"
                    v-if="isEditMode"
                    >fas fa-edit
                </v-icon>
            </template>
        </v-data-table>

        <ledger-detail-modal
            :isModalOpen.sync="showLedgerDetailModal"
            :propertyCode.sync="propertyCode"
            :ledgerDetail.sync="ledgerDetail"
            :ledgerList.sync="ledgerList"
        ></ledger-detail-modal>

        <v-dialog
            v-model="showActivityLogModal"
            max-width="1000"
            content-class="c8-page"
        >
            <v-card>
                <v-card-title class="headline">
                    Activity Log
                    <a
                        href="#"
                        class="dialog-close"
                        @click.prevent="showActivityLogModal = false"
                    >
                        <v-icon>mdi-close</v-icon>
                    </a>
                </v-card-title>
                <v-card-text>
                    <property-activity-log-component
                        v-if="showActivityLogModal"
                        :property_code="propertyCode"
                        :form_section="formSection"
                    ></property-activity-log-component>
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn
                        depressed
                        small
                        @click="showActivityLogModal = false"
                    >
                        <v-icon
                            left
                            dark
                            size="18"
                            >mdi-close
                        </v-icon>
                        Close
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>
