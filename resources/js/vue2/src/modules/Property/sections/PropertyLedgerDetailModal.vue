<script>
import {
    deletePropertyLedger,
    fetchBankAccountList,
    fetchVATStatusList,
    savePropertyLedger,
    updatePropertyLedger,
} from '../lib/ledger';
import { LEDGER_DETAIL_DEFAULT, LEDGER_TYPE_LIST, NEW_LABEL } from '../enums';
import isEmpty from 'lodash/isEmpty';
import { bus } from '../../../plugins/bus';
import global_mixins, { cirrusDialog } from '../../../plugins/mixins';
import { displayIdCode } from '../../../utils/sharedFunctions';

export default {
    props: {
        propertyCode: { type: String },
        ledgerDetail: {
            type: Object,
            default: { ...LEDGER_DETAIL_DEFAULT },
        },
        isModalOpen: { type: Boolean },
        ledgerList: { type: Array, default: [] },
    },
    mixins: [global_mixins],
    data() {
        return {
            showSuccess: false,
            errorServerMessage: [],
            bankAccountList: [],
            ledgerVATStatusList: [],
            ledgerTypeList: LEDGER_TYPE_LIST,
            ledgerDetailObject: this.ledgerDetail,
        };
    },
    watch: {
        isModalOpen(val) {
            if (!val) {
                this.$emit('update:isModalOpen', false);
            }
            this.ledgerDetailObject = this.objectClone(this.ledgerDetail);
        },
    },
    methods: {
        displayIdCode,
        async loadBankAccountList() {
            try {
                this.bankAccountList = await fetchBankAccountList();
            } catch (error) {
                console.error('Error loading bank account list:', error);
            }
        },
        async loadVATStatusList() {
            try {
                this.ledgerVATStatusList = await fetchVATStatusList();
            } catch (error) {
                console.error('Error loading VAT list:', error);
            }
        },
        nameWithDash({ field_key, field_value }) {
            if (isEmpty(`${field_key}`)) return `${field_value}`;
            return `${field_key} — ${field_value}`;
        },
        closeModal() {
            this.$emit('update:isModalOpen', false);
        },
        modalPrevData: function () {
            this.errorServerMessage = [];
            let currentIndex = this.ledgerList.indexOf(this.ledgerDetailObject);
            if (currentIndex === -1) {
                this.ledgerDetailObject = this.ledgerList[0];
                return;
            }
            currentIndex = Math.max(0, currentIndex - 1);
            this.ledgerDetailObject = this.ledgerList[currentIndex];
        },
        modalNextData: function () {
            this.errorServerMessage = [];
            let currentIndex = this.ledgerList.indexOf(this.ledgerDetailObject);
            if (currentIndex === -1) {
                this.ledgerDetailObject = this.ledgerList[0];
                return;
            }
            currentIndex = Math.max(this.ledgerList.length - 1, currentIndex + 1);
            this.ledgerDetailObject = this.ledgerList[currentIndex];
        },
        modalAddData() {
            this.errorServerMessage = [];
            this.ledgerDetailObject = { ...LEDGER_DETAIL_DEFAULT };
        },
        async modalSubmitData() {
            this.errorServerMessage = [];
            try {
                let data;
                if (isEmpty(this.ledgerDetailObject.id)) {
                    data = await savePropertyLedger(this.propertyCode, this.ledgerDetailObject);
                } else {
                    data = await updatePropertyLedger(this.propertyCode, this.ledgerDetailObject);
                }

                if (data) {
                    bus.$emit('loadPropertyLedgerSection');
                }
            } catch (errors) {
                if (typeof errors === 'object') {
                    this.errorServerMessage = [Object.values(errors).flat()];
                } else {
                    this.errorServerMessage = ['An unexpected error occurred'];
                }
            }
        },
        async deleteData() {
            this.errorServerMessage = [];
            let dialog_prop = {
                title: 'Warning',
                message: 'Are you sure?',
                icon_show: true,
                buttons_right: [
                    { label: 'Yes', value: 1, color: 'primary' },
                    { label: 'No', value: 2 },
                ],
            };
            const result = await cirrusDialog(dialog_prop);
            if (result !== 1) return;
            await deletePropertyLedger(this.ledgerDetailObject.id);
            bus.$emit('loadPropertyLedgerSection');
        },
    },
    computed: {
        showIndexNumber() {
            return this.displayIdCode(this.ledgerDetailObject.id) ?? NEW_LABEL;
        },
        isNew() {
            return this.showIndexNumber === NEW_LABEL;
        },
        hasLedgerListData() {
            return this.ledgerList.length > 0;
        },
    },
    mounted() {
        this.loadBankAccountList();
        this.loadVATStatusList();
    },
    created() {},
};
</script>
<template>
    <v-dialog
        :value="isModalOpen"
        @input="$emit('update:isModalOpen', $event)"
        max-width="1000"
        content-class="c8-page"
        @keydown.ctrl.left="modalPrevData"
        @keydown.ctrl.right="modalNextData"
        @keydown.ctrl.shift.enter="modalAddData"
        @keydown.ctrl.enter="modalSubmitData"
        @keydown.ctrl.delete="deleteData()"
    >
        <v-card>
            <v-card-title class="headline">
                Property Ledger Information
                <a
                    href="#"
                    class="dialog-close"
                    @click.prevent="closeModal"
                >
                    <v-icon>mdi-close</v-icon>
                </a>
            </v-card-title>
            <v-card-text>
                <cirrus-server-error :errorMsg2="errorServerMessage"></cirrus-server-error>
                <v-alert
                    type="success"
                    dense
                    tile
                    text
                    v-if="showSuccess"
                >
                    Successfully Saved
                </v-alert>
                <div>
                    <div class="page-form">
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >ID
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <span class="form-input-text">{{ showIndexNumber }}</span>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Property Ledger ID
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="ledgerDetailObject.propertyLedgerCode"
                                    :edit_form="true"
                                    :rules_length="50"
                                    :maxlength="50"
                                    :size="50"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Property Ledger Name
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="ledgerDetailObject.description"
                                    :edit_form="true"
                                    :rules_length="50"
                                    :maxlength="50"
                                    :size="50"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Property Ledger Type
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select-v2
                                    v-model="ledgerDetailObject.ledgerType"
                                    :options="ledgerTypeList"
                                    ref="refPropertyLedgerType"
                                    trackBy="field_key"
                                    label="field_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Bank Account
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select-v2
                                    v-model="ledgerDetailObject.bankCode"
                                    :options="bankAccountList"
                                    ref="refPropertyType"
                                    trackBy="field_key"
                                    :customLabel="nameWithDash"
                                    label="field_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Default amount to withold from owner
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="ledgerDetailObject.defaultAmountToWithhold"
                                    :edit_form="true"
                                    :rules_length="16"
                                    :maxlength="16"
                                    :size="16"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Default note for witheld amount
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-input
                                    v-model="ledgerDetailObject.defaultNoteForWithheldAmount"
                                    :edit_form="true"
                                    :rules_length="16"
                                    :maxlength="16"
                                    :size="16"
                                ></cirrus-input>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Use principal owner's eft details on tenant invoices
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input d-flex align-center"
                            >
                                <v-checkbox
                                    v-model="ledgerDetailObject.isUsePrincipalOwnersEftDetails"
                                    :ripple="false"
                                ></v-checkbox>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                                >Allow owner payment
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input d-flex align-center"
                            >
                                <v-checkbox
                                    v-model="ledgerDetailObject.isAllowedOwnerPayments"
                                    :ripple="false"
                                ></v-checkbox>
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Property Ledger VAT status
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                                <cirrus-single-select-v2
                                    v-model="ledgerDetailObject.taxCode"
                                    :options="ledgerVATStatusList"
                                    ref="refPropertyLedgerVATStatus"
                                    trackBy="field_key"
                                    label="field_value"
                                    return="field_key"
                                    placeholder="Please select"
                                />
                            </v-col>
                        </v-row>
                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label required"
                                >Property Ledger status
                            </v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input d-flex align-center"
                            >
                                <v-btn-toggle
                                    class="form-toggle"
                                    mandatory
                                    v-model="ledgerDetailObject.isPropertyLedgerStatusActive"
                                >
                                    <v-btn
                                        x-small
                                        tile
                                        text
                                        :value="true"
                                    >
                                        Active
                                    </v-btn>
                                    <v-btn
                                        x-small
                                        tile
                                        text
                                        :value="false"
                                    >
                                        Inactive
                                    </v-btn>
                                </v-btn-toggle>
                            </v-col>
                        </v-row>

                        <v-row class="form-row">
                            <v-col
                                xs="12"
                                sm="3"
                                md="3"
                                class="form-label"
                            ></v-col>
                            <v-col
                                xs="12"
                                sm="9"
                                md="9"
                                class="form-input"
                            >
                            </v-col>
                        </v-row>
                    </div>
                </div>
            </v-card-text>
            <v-card-actions>
                <v-btn
                    v-if="!isNew && hasLedgerListData"
                    class="v-step-save-2-button"
                    @click="modalPrevData()"
                    data-tooltip="CTR + LEFT"
                    color="primary"
                    dark
                    depressed
                    small
                >
                    <v-icon
                        left
                        dark
                        size="18"
                        >skip_previous
                    </v-icon>
                    Previous
                </v-btn>
                <v-spacer />
                <v-btn
                    v-if="!isNew && hasLedgerListData"
                    class="v-step-save-2-button"
                    @click="modalAddData()"
                    data-tooltip="CTR + SHIFT + ENTER"
                    color="primary"
                    dark
                    depressed
                    small
                >
                    <v-icon
                        left
                        dark
                        size="18"
                        >add
                    </v-icon>
                    Add New
                </v-btn>
                <v-btn
                    class="v-step-save-2-button"
                    @click="modalSubmitData()"
                    data-tooltip="CTR + ENTER"
                    color="success"
                    dark
                    depressed
                    small
                >
                    <v-icon
                        left
                        dark
                        size="18"
                        >check
                    </v-icon>
                    Save
                </v-btn>
                <v-btn
                    class="v-step-save-2-button"
                    @click="modalAddData()"
                    color="warning"
                    dark
                    depressed
                    small
                >
                    <v-icon
                        left
                        dark
                        size="18"
                        >clear_all
                    </v-icon>
                    Clear
                </v-btn>
                <v-btn
                    v-if="!isNew && hasLedgerListData"
                    class="v-step-save-2-button"
                    @click="modalNextData"
                    data-tooltip="CTR + RIGHT"
                    color="primary"
                    dark
                    depressed
                    small
                >
                    Next
                    <v-icon
                        left
                        dark
                        size="18"
                        >skip_next
                    </v-icon>
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
