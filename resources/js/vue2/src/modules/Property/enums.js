export const SHORTCUT_LIST = [
    { icon: 'table_chart', title: 'Property Abstract', shortcut_code: 'property_abstract' },
    { icon: 'account_balance', title: 'Tenancy Schedule', shortcut_code: 'tenant_schedule' },
    {
        icon: 'account_balance',
        title: 'Cash Book (current month)',
        shortcut_code: 'cash_book_current_month',
    },
    { icon: 'account_balance', title: 'Cash Book (all dates)', shortcut_code: 'cash_book_all_date' },
    {
        icon: 'receipt',
        title: 'Unpaid Invoices and Property Balances',
        shortcut_code: 'unpaid_invoice_n_balances',
    },
    { icon: 'email', title: 'Email Address Book', shortcut_code: 'email_address_book' },
    { icon: 'message', title: 'Send SMS', shortcut_code: 'sms_send' },
    { icon: 'print', title: 'Print', shortcut_code: 'print' },
];

export const LEDGER_TYPE_LIST = [
    { field_key: 'RENT', field_value: 'Rent' },
    { field_key: 'SERVICE CHARGE', field_value: 'Service Charge' },
    { field_key: 'RECOVERABLE', field_value: 'Recoverable' },
    { field_key: 'OTHER', field_value: 'Other' },
];
export const LEDGER_DETAIL_DEFAULT = Object.freeze({
    index: null,
    itemNo: null,
    id: null,
    propertyLedgerCode: '',
    description: '',
    ledgerType: '',
    bankCode: '',
    defaultAmountToWithhold: '',
    defaultNoteForWithheldAmount: '',
    taxCode: '',
    isUsePrincipalOwnersEftDetails: false,
    isAllowedOwnerPayments: false,
    isPropertyLedgerStatusActive: true,
});
export const NEW_LABEL = 'New';
export const REPORTING_GST_BASIS = Object.freeze({
    CASH: 'C',
    ACCRUAL: 'A',
});
export const REPORTING_GST_PERIOD = Object.freeze({
    MONTHLY: 'M',
    QUARTERLY: 'Q',
});
