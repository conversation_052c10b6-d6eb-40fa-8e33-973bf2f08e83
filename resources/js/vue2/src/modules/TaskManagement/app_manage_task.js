import Vue from 'vue';
// VUETIFY
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../../plugins/vuetify';
import '../../plugins/global';
import '../../plugins/request';

/**
 * Next, we will create a fresh Vue application instance and attach it to
 * the page. Then, you may begin adding components to this application
 * or customize the JavaScript scaffolding to fit your unique needs.
 */

Vue.component(
    'manage-task-type-component',
    require('../../components/modules/TaskManagement/templates/ManageTaskFormType.vue').default,
);
Vue.component(
    'manage-task-template-component',
    require('../../components/modules/TaskManagement/templates/ManageTaskFormTemplate.vue').default,
);
Vue.component('vue-modal', require('../../components/elements/VueModal.vue').default);
Vue.component('cirrus-single-select', require('../../components/elements/CirrusSingleSelect.vue').default);
Vue.component('cirrus-server-error', require('../../components/elements/CirrusServerError.vue').default);
Vue.component('cirrus-single-upload-button', require('../../components/elements/CirrusSingleUploadButton.vue').default);
Vue.component('cirrus-input', require('../../components/elements/CirrusInput.vue').default);
Vue.component('cirrus-single-upload-button', require('../../components/elements/CirrusSingleUploadButton.vue').default);
Vue.component('cirrus-content-loader', require('../../components/elements/CirrusContentLoader.vue').default);
import store from '../../vuex/modules/TaskManagement/ManageTask';

new Vue({
    vuetify,
    store,
}).$mount('#app');
