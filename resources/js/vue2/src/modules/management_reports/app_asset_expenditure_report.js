import Vue from 'vue';
// VUETIFY
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../../plugins/vuetify';
import '../../plugins/global';
import '../../plugins/request';
// PAGE
import AssetExpenditureReportPage from '../../components/modules/ManagementReports/AssetExpenditureReportPage.vue';
// CREATE VUE
new Vue({
    el: '#app',
    vuetify,
    components: {
        'main-template-component': AssetExpenditureReportPage,
    },
}).$mount('#app');
