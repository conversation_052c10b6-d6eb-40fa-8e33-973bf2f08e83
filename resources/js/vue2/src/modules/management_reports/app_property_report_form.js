import Vue from 'vue';
// VUETIFY
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../../plugins/vuetify';
import '../../plugins/global';
import '../../plugins/request';

/**
 * Next, we will create a fresh Vue application instance and attach it to
 * the page. Then, you may begin adding components to this application
 * or customize the JavaScript scaffolding to fit your unique needs.
 */

/** Vue.component('property-report-form-template-component', require('./components/modules/management_reports/templates/property_report_form_template.vue').default); */
Vue.component(
    'main-template-component',
    require('../../components/modules/ManagementReports/templates/PropertyReportFormTemplate.vue').default,
);

Vue.component('vue-dual-list-select', require('../../components/elements/VueDualListSelect.vue').default);
Vue.component('cirrus-server-error', require('../../components/elements/CirrusServerError.vue').default);
Vue.component('cirrus-loader', require('../../components/elements/CirrusLoader.vue').default);
Vue.component('cirrus-content-loader', require('../../components/elements/CirrusContentLoader.vue').default);
Vue.component('cirrus-date-picker', require('../../components/elements/CirrusDatePicker.vue').default);
Vue.component('cirrus-icon-date-picker', require('../../components/elements/CirrusIconDatePicker.vue').default);

import store from '../../vuex/modules/ManagementReports/Store';

new Vue({
    vuetify,
    store,
}).$mount('#app');
