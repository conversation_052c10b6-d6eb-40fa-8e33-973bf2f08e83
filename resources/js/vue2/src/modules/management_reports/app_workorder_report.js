import Vue from 'vue';
// VUETIFY
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../../plugins/vuetify';
import '../../plugins/global';
import '../../plugins/request';
// PAGE
import WorkorderReportPage from '../../components/modules/ManagementReports/WorkorderReportPage.vue';
// CREATE VUE
new Vue({
    el: '#app',
    vuetify,
    components: {
        'main-template-component': WorkorderReportPage,
    },
}).$mount('#app');
