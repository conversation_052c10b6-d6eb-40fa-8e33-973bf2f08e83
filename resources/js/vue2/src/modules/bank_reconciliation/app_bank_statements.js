import Vue from 'vue';
// VUETIFY
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../../plugins/vuetify';
import '../../plugins/global';
import '../../plugins/request';
import store from '../../vuex/modules/BankReconciliation/Store';
// PAGE
import BankStatementsPage from '../../components/modules/BankReconciliation/BankStatementsPage.vue';
// CREATE VUE
new Vue({
    el: '#app',
    vuetify,
    store,
    components: {
        'main-template-component': BankStatementsPage,
    },
}).$mount('#app');
