/**
 * First we will load all of this project's JavaScript dependencies which
 * includes Vue and other libraries. It is a great starting point when
 * building robust, powerful web applications using Vue and Laravel.
 */

import Vue from 'vue';
// VUETIFY
import 'vuetify/dist/vuetify.min.css'; // Ensure you are using css-loader
import 'vuetify/lib/util/colors';
import vuetify from '../../plugins/vuetify';
import '../../plugins/global';
import '../../plugins/request';
import SuiVue from 'semantic-ui-vue';
import Multiselect from 'vue-multiselect';
// import vSelect from 'vue-select'
Vue.use(SuiVue);

/**
 * Next, we will create a fresh Vue application instance and attach it to
 * the page. Then, you may begin adding components to this application
 * or customize the JavaScript scaffolding to fit your unique needs.
 */

Vue.component(
    'qp-template-component',
    require('../../components/modules/AP/templates/QuickPaymentFormTemplate.vue').default,
);

Vue.component('vue-modal', require('../../components/elements/VueModal.vue').default);

Vue.component('cirrus-text-area', require('../../components/elements/CirrusTextarea.vue').default);
Vue.component('cirrus-date-picker', require('../../components/elements/CirrusDatePicker.vue').default);
Vue.component('cirrus-input', require('../../components/elements/CirrusInput.vue').default);
Vue.component('cirrus-single-upload-button', require('../../components/elements/CirrusSingleUploadButton.vue').default);
Vue.component('cirrus-radio-group', require('../../components/elements/CirrusRadioGroup.vue').default);
Vue.component('cirrus-server-error', require('../../components/elements/CirrusServerError.vue').default);
Vue.component('vue-dual-list-select', require('../../components/elements/VueDualListSelectV1.vue').default);
Vue.component('cirrus-loader', require('../../components/elements/CirrusLoader.vue').default);
Vue.component('cirrus-content-loader', require('../../components/elements/CirrusContentLoader.vue').default);
Vue.component('cirrus-single-select', require('../../components/elements/CirrusSingleSelect.vue').default);
Vue.component('cirrus-icon-date-picker', require('../../components/elements/CirrusIconDatePicker.vue').default);

import store from '../../vuex/modules/AP/QuickPayment';
import '@mdi/font/css/materialdesignicons.css';
// import 'material-design-icons-iconfont/dist/material-design-icons.css';
import '../../plugins/global';
// Vue.mixin(mixins);
new Vue({
    vuetify,
    store,
}).$mount('#app');
