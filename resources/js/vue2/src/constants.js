export const LEASE_TERM_LOG_IS_ENABLED = Number(process.env.MIX_CONFIG_IS_LEASE_TERM_LOG_ENABLED) === 1;
export const SIX_MB_IN_BYTES_LIMIT = '6291456';
export const EIGHT_MB_IN_BYTES_LIMIT = '8388608';
export const MIX_CONFIG_ASSET_DOMAIN = process.env.MIX_CONFIG_ASSET_DOMAIN;

export const ENABLED = Object.freeze({
    value: 1,
    label: 'Yes',
});

export const DISABLED = Object.freeze({
    value: 0,
    label: 'No',
});

export const YES_NO_OPTION = Object.freeze({
    yes: 0,
    no: 1,
});

export const DIALOG_BTN_YES = 1;
export const DIALOG_BTN_NO = 2;
export const DIALOG_BUTTONS = [
    { label: 'Yes', value: DIALOG_BTN_YES, color: 'primary' },
    { label: 'No', value: DIALOG_BTN_NO },
];

export const USER_TYPES = Object.freeze({
    TRUST_ACCOUNTANT: 'A',
    PM_READ_ONLY: 'PMRO',
});

export const EMAIL_ADDRESS_BOOK_TYPES = Object.freeze({
    OWNER: 'OWN_GEN',
    TENANT: 'TEN_GEN',
    SUPPLIER: 'SUP_GEN',
});

export const BYTES_IN_MEGABYTES = 1048576;

export const DARK_MODE_CLASS = 'c8-dark';
