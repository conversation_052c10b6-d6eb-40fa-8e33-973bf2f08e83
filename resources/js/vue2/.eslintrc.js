const { resolve } = require('path');

module.exports = {
    parserOptions: {
        project: resolve(__dirname, './tsconfig.json'),
    },
    extends: [
        '@cirrus8/eslint-config',
        // See https://eslint.vuejs.org/rules/#available-rules (look for Vuejs 2 ones)
        'plugin:vue/vue2-essential', // Priority A: Essential (Error Prevention)
        'plugin:vue/vue2-strongly-recommended', // Priority B: Strongly Recommended (Improving Readability)
        'plugin:vue/vue2-recommended', // Priority C: Recommended (Minimizing Arbitrary Choices and Cognitive Overhead)
    ],
    ignorePatterns: ['node_modules', 'dist'],
};
