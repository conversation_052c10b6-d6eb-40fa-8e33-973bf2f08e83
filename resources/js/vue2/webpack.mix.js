let mix = require('laravel-mix');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const webpack = require('webpack'); // Required to use Webpack plugins
const path = require('path'); // Required for resolving absolute paths
const fs = require('fs');
/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining Webpack build steps for your
 | Laravel application. We’re compiling JS files and SCSS files, bundling them
 | for the application.
 |`
 */
mix.webpackConfig({
    ignoreWarnings: [/sass-loader/],
    plugins: [
        new NodePolyfillPlugin(),
        // Define Vue feature flags for better production handling
        new webpack.DefinePlugin({
            'process.env': JSON.stringify(process.env),
            __VUE_PROD_DEVTOOLS__: false, // Disable Vue devtools in production
            __VUE_OPTIONS_API__: true, // Enable Options API (if you're using it)
            __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false, // Disable hydration mismatch info
        }),
        // Provide process and Buffer polyfills
        new webpack.ProvidePlugin({
            process: 'process/browser',
            Buffer: ['buffer', 'Buffer'],
        }),
    ],
    resolve: {
        fallback: {
            fs: false,
        },
    },
});

// Grouping all `.js` files for a cleaner configuration
const vue2_js_files = [
    'src/modules/leases/app_lease_manage_form.js',
    'src/modules/leases/app_lease_new_form.js',
    'src/modules/leases/app_lease_summary_form.js',
    'src/modules/leases/app_lease_approval_form.js',
    'src/modules/leases/app_lease_temp_form.js',
    'src/modules/leases/app_lease_print_page.js',
    'src/app_ocr.js',
    'src/modules/management_reports/app_property_report_form.js',
    'src/modules/management_reports/app_workorder_report.js',
    'src/modules/management_reports/app_asset_expenditure_report.js',
    'src/modules/properties/app_property_budget.js',
    'src/modules/AccountsReceivable/app_variable_outgoings.js',
    'src/modules/bank_reconciliation/app_auto_bank_recon.js',
    'src/modules/bank_reconciliation/app_bank_statements.js',
    'src/modules/Companies/app_manage_company.js',
    'src/modules/SalesTrust/app_manage_ledger.js',
    'src/modules/SalesTrust/app_manage_ledger2.js',
    'src/modules/properties/app_property_manage_form.js',
    'src/modules/properties/app_property_new_form.js',
    'src/modules/properties/app_property_summary_form.js',
    'src/modules/properties/app_property_approval_form.js',
    'src/modules/properties/app_property_temp_form.js',
    'src/modules/properties/app_property_print_page.js',
    'src/modules/properties/app_property_bulk_update_form.js',
    'src/modules/AP/app_quick_payment.js',
    'src/modules/leases/app_manage_rent_review.js',
    'src/modules/TaskManagement/app_manage_task.js',
    'src/modules/home/<USER>',
    'src/modules/leases/app_multiple_lease_rent_review.js',
    'src/modules/Retail/retail_maintenance_form.js',
    'src/modules/Utilities/app_utilities.js',
    'src/modules/MainApp/cirrus_main.js',
    'src/modules/UserAccount/app_user_account.js',
    'src/modules/Administration/app_contact_report_page.js',
    'src/modules/Administration/app_manage_email_queue_page.js',
    'src/DocumentDirectory/app_document_folder_page.js',
    'src/SendEmailAndLetters/app_send_email_letters_page.js',
];

// Grouping all `.scss` files for a cleaner configuration
const sassFiles = [
    'sass/_variables.scss',
    'sass/ocr.scss',
    'sass/propertyBudget.scss',
    'sass/app.scss',
    'sass/quick_payment.scss',
    'sass/page.scss',
    'sass/compact.scss',
    'sass/lease.scss',
    'sass/lease_vue.scss',
    'sass/property.scss',
    'sass/manage_email_queue.scss',
    'sass/communication_history.scss',
    'sass/send_email_letters.scss',
];

// Compile all `.js` files and output them to `public/vuejs/js`
vue2_js_files.forEach((file) => {
    mix.ts(file, '../../../public/vuejs/js').vue({ version: 2 }).setPublicPath('../../../public').version();
});
// Compile all `.scss` files and output them to `public/vuejs/css`
sassFiles.forEach((file) => {
    mix.sass(file, '../../../public/vuejs/css')
        .setPublicPath('../../../public')
        .options({
            postCss: [require('@tailwindcss/postcss')],
        })
        .version();
});

mix.after(() => {
    const manifestPath = '../../../public/mix-manifest.json'; // Original Manifest
    const vue2ManifestPath = '../../../public/vue2-mix-manifest.json'; // Vue 2 Manifest
    const vue3ManifestPath = '../../../public/vue3-mix-manifest.json'; // Vue 3 Manifest

    // Copy default mix manifest to Vue 2 manifest file
    if (fs.existsSync(manifestPath)) {
        fs.copyFileSync(manifestPath, vue2ManifestPath);
        console.log('Vue 2 Manifest saved to:', vue2ManifestPath);
    }

    // Combine Vue 2 and Vue 3 manifests
    if (fs.existsSync(vue2ManifestPath) && fs.existsSync(vue3ManifestPath)) {
        // Read the contents of both manifest files
        const vue2Manifest = JSON.parse(fs.readFileSync(vue2ManifestPath, 'utf-8'));
        const vue3Manifest = JSON.parse(fs.readFileSync(vue3ManifestPath, 'utf-8'));

        // Merge both manifests into a single object
        const combinedManifest = { ...vue2Manifest, ...vue3Manifest };

        // Write the merged manifest back to `mix-manifest.json`
        fs.writeFileSync(manifestPath, JSON.stringify(combinedManifest, null, 4));
        console.log('Combined manifest saved to:', manifestPath);
    }
});
