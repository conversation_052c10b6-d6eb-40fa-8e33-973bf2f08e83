@import '_variables';

.v-menu__content {
    position: fixed !important;
    font-weight: normal !important;
    font-size: 11px !important;
    z-index: 999999 !important;
    color: inherit !important;
}

.v-dialog .multiselect__content-wrapper {
    position: fixed;
    max-width: inherit;
}

.v-dialog {
    .c8-datatable-custom-footer {
        .multiselect__content-wrapper {
            position: absolute;
            max-width: inherit;
        }
    }
}

.v-dialog__content {
    background-color: transparent !important;
}

[data-tooltip][data-position='left center']:hover:before {
    margin-left: -9px !important;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    background-color: rgba(0, 0, 0, 1);
    text-decoration: none;
}

.c8-page .c8-datatable-custom .v-data-table-header th {
    background: #eaeaea !important;
}

.data-grid td.required {
    width: 16.28px;
}

#lease-suburb > div > div > div > .v-select__slot {
    width: 100%;
    border: 1px solid #ececec !important;
    min-height: 30px;
    padding: 0px;
    //background-color: #FFFFFF !important;
}

#lease-postcode > div > div > div > .v-select__slot {
    width: 100%;
    border: 1px solid #ececec !important;
    min-height: 30px;
    padding: 0px;
    //background-color: #FFFFFF !important;
}

#lease-suburb-mail > div > div > div > .v-select__slot {
    width: 100%;
    border: 1px solid #ececec !important;
    min-height: 30px;
    padding: 0px;
    //background-color: #FFFFFF !important;
}

#lease-postcode-mail > div > div > div > .v-select__slot {
    width: 100%;
    border: 1px solid #ececec !important;
    min-height: 30px;
    padding: 0px;
    //background-color: #FFFFFF !important;
}

.abn-detail-container {
    height: 30px;
    margin-top: -30px;
    margin-left: 302px;
}

.abn-result-table {
    max-width: 390px;
    width: 100%;
    border: 1px solid #ececec;
}

.abn-result-table th,
#abn-result-table td {
    text-align: left;
    border-bottom: 1px solid #ececec !important;
}

.abn-message {
    color: crimson;
    font-weight: bold;
    min-height: 30px;
    padding-top: 2px;
}

.copy-value-btn {
    margin-left: 5px;
    max-width: 30px;
    min-width: 20px;
}

.abn-detail-container {
    height: 30px;
    margin-top: -30px;
    margin-left: 302px;
}

.loading-info-text {
    position: relative;
    top: 1px;
}

.noteTextArea textarea {
    font-size: 14px;
}

pre {
    font-size: $c8-font-size;
    font-family: 'Segoe UI', Helvetica, Arial, Tahoma, sans-serif;
}

body.c8-dark #frame #container #leasePageDiv .c8-page .page-form .form-row .form-subheader {
    background: var(--dark-ligther-color) !important;
}

body.c8-dark .v-data-table .v-data-table__wrapper table thead tr th {
    background: var(--dark-ligther-color) !important;
}

body.c8-dark .section-toolbar {
    background: var(--dark-ligther-color) !important;
}

body #frame #container #leasePageDiv .theme--light.v-data-table thead tr:last-child th {
    color: #888;
}

body.c8-dark #frame #container #leasePageDiv .theme--light.v-data-table thead tr:last-child th {
    color: #ffffff !important;
}

.lease-inactive-row {
    background: #f7f7f7;
    color: #b2b2b2;
}

.inactive-checkbox {
    label {
        color: white !important;
    }
}

.file_type-description-textbox {
    min-width: 300px !important;
}

.documents-table {
    border: 1px solid rgba(34, 36, 38, 0.15);
}

td.text-start,
td.text-end {
    padding-top: 0.4em !important;
    padding-bottom: 0.4em !important;
}

.documents-table .option {
    font-size: 21px !important;
}

ul.v-pagination button {
    height: 30px !important;
    min-width: 30px !important;
    min-height: 30px !important;
}

.documents-table button.v-icon {
    font-size: 21px;
}

.fixed-width-16 {
    width: 16px !important;
}

.document-div .v-treeview-node .v-treeview-node__root {
    min-height: 30px !important;
}

td > [data-tooltip] {
    font-size: 12px !important;
}

.v-expansion-panel-content > .v-expansion-panel-content__wrap {
    padding: 0px;
}

body.c8-dark #frame #container .document-section #status-form-toggle .v-btn--text .v-btn__content,
body.c8-dark #frame #container .document-section #type-form-toggle .v-btn--text .v-btn__content,
body.c8-dark #frame #container .document-section #sort-form-toggle .v-btn--text .v-btn__content,
body.c8-dark #frame #container .document-section #sort-type-form-toggle .v-btn--text .v-btn__content {
    color: #4e4e4e !important;
}

.system-row {
    color: $c8-primary;
    background: $c8-highlight-primary;
}

.c8-dark .system-row {
    color: $c8-primary !important;
    background: $c8-highlight-primary !important;
}
