@import '_variables';

.container * {
    font-size: 11px !important;
}

//.center {
//  text-align: center !important;
//}
.inputAmount {
    text-align: right !important;
    width: 7em;
    padding-left: 1em !important;
}

.inputAmount2 {
    text-align: right !important;
    width: 6em;
    padding-left: 1em !important;
}

.ui.icon.input > i.icon {
    text-align: left !important;
    width: 15px !important;
}

.warpperB {
    font-size: 10px !important;
}

.fieldDescriptionAmount {
    width: 5em !important;
}

.fieldDescriptionPercentage {
    width: 4em !important;
}

.tableActionButton > .ui-button-text {
    padding: 0 !important;
}

#mbPropertyID {
    width: 400px;
}

#mbPropertyMgmtFeeAccount {
    width: 400px;
}

#mbPropertyMgmtFeeAccountRangeAddFrom {
    width: 400px;
}

#mbPropertyMgmtFeeAccountRangeAddTo {
    width: 400px;
}

#mbAccountListVOIncome {
    width: 400px;
}

#mbAccountListVOIncomeAdj {
    width: 400px;
}

#mbSplitType {
    width: 400px;
}

.dropdownNgRepeat {
    width: 400px;
}

#mbPropertyMgmtFeeMethod {
    width: 230px;
}

#mbFinancialYear {
    width: 302px;
}

#mbFinancialYearCopy {
    width: 302px;
}

#mbActualsYear {
    width: 162px;
}

.btnSelectedBudget {
    border: 1px solid #e79400 !important;
    background: #ff9507 50% 50% repeat-x !important;
    font-weight: normal !important;
    color: #fff !important;
}

#managementBudgetBtn {
    /*display: none; !* Hidden by default *!*/
    position: fixed; /* Fixed/sticky position */
    bottom: 200px; /* Place the button at the bottom of the page */
    right: 30px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background: #3489a1; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 15px; /* Some padding */
    border-radius: 50%; /* Rounded corners */
    width: 20px;
}

#settingBudgetBtn {
    /*display: none; !* Hidden by default *!*/
    position: fixed; /* Fixed/sticky position */
    bottom: 140px; /* Place the button at the bottom of the page */
    right: 30px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background: #3489a1; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 5px; /* Some padding */
    border-radius: 50%; /* Rounded corners */
    width: 40px;
}

#addBudgetBtn {
    /*display: none; !* Hidden by default *!*/
    position: fixed; /* Fixed/sticky position */
    bottom: 80px; /* Place the button at the bottom of the page */
    right: 30px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background: #3489a1; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 15px; /* Some padding */
    border-radius: 50%; /* Rounded corners */
    width: 20px;
}

#saveBudgetBtn {
    /*display: none; !* Hidden by default *!*/
    position: fixed; /* Fixed/sticky position */
    bottom: 20px; /* Place the button at the bottom of the page */
    right: 210px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background: #3489a1; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 15px; /* Some padding */
    border-radius: 50%; /* Rounded corners */
    width: 20px;
}

#deleteBudgetBtn {
    /*display: none; !* Hidden by default *!*/
    position: fixed; /* Fixed/sticky position */
    bottom: 20px; /* Place the button at the bottom of the page */
    right: 150px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background: #3489a1; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 15px; /* Some padding */
    border-radius: 50%; /* Rounded corners */
    width: 20px;
}

.lockBudgetBtn {
    /*display: none; !* Hidden by default *!*/
    position: fixed; /* Fixed/sticky position */
    bottom: 20px; /* Place the button at the bottom of the page */
    right: 90px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background: #3489a1; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 15px; /* Some padding */
    border-radius: 50%; /* Rounded corners */
    width: 20px;
}

#exportToExcelIcon {
    /*display: none; !* Hidden by default *!*/
    position: fixed; /* Fixed/sticky position */
    bottom: 20px; /* Place the button at the bottom of the page */
    left: 30px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background: #3489a1; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 15px; /* Some padding */
    border-radius: 50%; /* Rounded corners */
    /*width: 40px;*/
}

#activityLogsDiv {
    left: 30px !important;
    bottom: 1200px !important;
}

#activityLogs {
    /*display: none; !* Hidden by default *!*/
    position: fixed; /* Fixed/sticky position */
    bottom: 90px; /* Place the button at the bottom of the page */
    left: 30px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background: #3489a1; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 15px; /* Some padding */
    border-radius: 50%; /* Rounded corners */
    /*width: 40px;*/
}

#mbPropertyMgmtFeeAccountList_chosen {
    width: 300px !important;
}

.modal > .header2 {
    background-color: #3489a1 !important;
    color: white !important;
}

#mbAddAccountCode {
    width: 300px !important;
}

.modalFixTop {
    margin-top: 10% !important;
    top: 10% !important;
}

#mbAddLeaseToAccountCode {
    width: 300px !important;
}

#expensesUnitBreakdownModal.modal {
    /*margin-top: -27% !important;*/
    /*min-height: 900px !important;*/
    /*margin-top: 10% !important;*/
    /*top: 10% !important;*/
}

.description {
    padding: 1em 2.6em 1em 2.6em;
}

.leaseAmountCell {
    padding-right: 2px !important;
}

.overview-leaseAmountCell {
    padding-right: 8px !important;
}

.leaseAmountCell:hover > div > span {
    /*padding-left: .5rem;*/
    top: -1rem;
    right: 0;
    display: block;
    -webkit-transition: all 0.125s ease-in-out;
    transition: all 0.2s ease-in-out;
}

input:focus ~ span {
    /*padding-left: .5rem;*/
    top: -1.5rem;
    right: 0;
    display: block;
    -webkit-transition: all 0.125s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.floating-field {
    /*float: right;*/
    /*width: 33.3333%;*/
    /*margin: 2rem 0 1rem;*/
    position: relative;
}

.floating-field span {
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    -webkit-transition: all 0.25s ease-in-out(0.2, 0, 0.03, 1);
    transition: all 0.25s ease-in-out;
}

.floating-field.wide {
    width: 100%;
}

.amountFieldLease input:focus ~ span {
    -webkit-transition: all 0.125s ease-in-out;
    transition: all 0.125s ease-in-out;
}

.accountIDHoverButtons {
    width: 100px !important;
}

.firstTd {
    width: 60px !important;
    padding: 0px !important;
    text-align: right !important;
}

.headerTr {
    background: #3489a1 !important;
    color: white !important;
}

.item.disabled {
    display: none !important;
}

.ui.input > a {
    position: absolute;
    z-index: 1;
    left: -9px;
    top: 6px;
}

.red {
    background-color: transparent !important;
    border-color: transparent !important;
}

.red.highlighted {
    background-color: #00baf2 !important;
    border-color: transparent !important;
    color: #fff;
}

.redBgColor {
    background-color: #f44336 !important;
    color: white !important;
}

.multiselect__tags {
    border-radius: 0px !important;
    //padding: 1px 2px 0px 0px !important;
    min-height: 0px !important;
    font-size: 11px !important;
    //height: 20px;
    //padding-top: 2px;
}

.subHeaderSubTotal {
    color: #36708d !important;
    background: #ffbe76 !important;
}

.rightAlign {
    text-align: right !important;
}

.v-stepper__wrapper {
    min-height: 100% !important;
    padding-bottom: 350px !important;
}

.budget-main-tables {
    td {
        padding: 3px 3px !important;
    }

    thead {
        tr:first-child > th:first-child {
            border-radius: 0px !important;
        }

        th {
            padding: 3px 3px !important;
        }
    }
}

.v-menu__content {
    position: fixed !important;
    font-weight: normal !important;
    font-size: 11px !important;
    z-index: 999999 !important;
    color: inherit !important;
}

.v-dialog .multiselect__content-wrapper {
    position: fixed;
    max-width: inherit;
}

.v-dialog {
    .c8-datatable-custom-footer {
        .multiselect__content-wrapper {
            position: absolute;
            max-width: inherit;
        }
    }
}

.v-dialog__content {
    background-color: transparent !important;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    background-color: rgba(0, 0, 0, 1);
    text-decoration: none;
}

.variance_col {
    background-color: #eaeaea !important;
}

.budget-toolbar {
    padding-right: 0px !important;

    .v-card__actions {
        padding-right: 0px !important;

        .v-input:not(.v-textarea) input {
            color: black !important;
        }

        div {
            padding-right: 0px !important;
        }
    }
}

.expenses-datatable {
    div {
        table {
            td:nth-child(8) {
                border-left: 1px solid #eaeaea;
            }

            td:nth-child(11) {
                border-right: 1px solid #eaeaea;
            }
        }
    }
}

.income-datatable {
    input {
        border: 1px solid #f6f6f6 !important;
    }
}

.amount-format {
    padding-right: 8px !important;
}

.page-header {
    margin-bottom: 0px !important;
    padding-right: 10px !important;

    .page-title {
        margin-bottom: 0px !important;
    }
}

.v-data-table-header {
    tr {
        th {
            background: #3489a1 !important;
            color: white !important;
        }
    }
}

.budget-main-tables {
    input {
        border: 1px solid #f6f6f6 !important;
    }
}

.v-stepper__wrapper {
    .menu {
        .v-chip {
            margin: 11px 4px;
        }
    }
}

.v-stepper__header {
    height: 50px !important;
}

.v-stepper__step {
    padding: 14px 24px 14px 24px !important;
}

.v-stepper__content {
    padding: 0px 10px 16px !important;
}

.section-toolbar {
    .v-input:not(.v-textarea) {
        max-width: 300px;
        margin-top: 0px;
        min-width: initial;
        width: 100%;

        &.v-text-field > .v-input__control > .v-input__slot:before {
            border: 0 !important;
        }

        &.v-text-field > .v-input__control > .v-input__slot:after {
            border: 0 !important;
        }

        .v-text-field__details {
            min-height: initial;

            & .v-message,
            & .v-counter {
                padding: 0.3rem 0;
            }
        }

        input {
            display: inline-block;
            border: 1px solid #ececec !important;
            min-height: 30px;
            padding: 3px 10px;
            color: #ffffff !important;
            //background-color: #FFFFFF !important;
        }

        &.text-right input {
            text-align: right;
        }
    }

    * {
        -webkit-transition: none !important;
        -moz-transition: none !important;
        -o-transition: none !important;
        transition: none !important;
    }
}

table.budget-actions {
    float: right;
    margin-right: 4px;

    button.v-btn {
        height: 27px;
        width: 19px;
    }
}

.budget-actions-template {
    padding-right: 0px !important;
}

[data-tooltip][data-position='left center']:hover:before {
    margin-left: -9px !important;
}

.label-chips {
    margin-top: 11px;
    margin-left: 5px;
}

.budget-radius-0 {
    border-radius: 0 !important;
}

pre {
    font-size: $c8-font-size;
    font-family: 'Segoe UI', Helvetica, Arial, Tahoma, sans-serif;
}

/* PROPERTY SUMMARY BETA */
body.c8-dark #frame #container #propertyBudgetDiv .ui.selectable.table tbody tr:hover,
body.c8-dark #frame #container #propertyBudgetDiv .ui.table tbody tr td.selectable:hover {
    background: var(--dark-ligther-color) !important;
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .c8-page .page-form .form-row .form-subheader {
    background: var(--dark-ligther-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .c8-page .page-form .form-row {
    background: var(--dark-primary-color) !important;
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .noteTextArea textarea,
body.c8-dark #frame #container #propertyBudgetDiv .noteTextAreaBgColor,
body.c8-dark #frame #container #propertyBudgetDiv .new-comment-modal-container .v-input__slot {
    /*background: var(--dark-primary-color) !important;*/
    background: rgba(247, 233, 141, 0.2) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .new-comment-modal-container textarea,
body.c8-dark .ui-widget-content #diaryCommentDiv textarea,
body.c8-dark #leaseInspection #upComingTableInspection input[type='image'] {
    border: none !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .ui.checkbox label {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .c8-page .page-form .form-row .form-text-button {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .theme--light.v-icon,
body.c8-dark #frame #container #propertyBudgetDiv .multiselect__placeholder,
body.c8-dark
    #frame
    #container
    #propertyBudgetDiv
    .theme--light.v-date-picker-header
    .v-date-picker-header__value:not(.v-date-picker-header__value--disabled)
    button:not(:hover):not(:focus),
body.c8-dark #frame #container #propertyBudgetDiv .v-btn--outlined .v-btn__content .v-icon,
body.c8-dark #frame #container #propertyBudgetDiv .v-btn--round .v-btn__content .v-icon,
body.c8-dark #frame #container #propertyBudgetDiv .theme--light.v-date-picker-table .v-date-picker-table--date__week,
body.c8-dark #frame #container #propertyBudgetDiv .theme--light.v-date-picker-table th,
body.c8-dark #frame #container #propertyBudgetDiv .v-application .primary--text {
    color: var(--primary-text-color);
}

body.c8-dark #frame #container #propertyBudgetDiv .v-btn__content {
    background: transparent;
}

body.c8-dark #frame #container #propertyBudgetDiv .c8-page .page-form .form-row .v-input:not(.v-textarea) input {
    background: transparent !important;
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .v-input--selection-controls__ripple {
    background: transparent;
}

body.c8-dark #frame #container #propertyBudgetDiv .v-card {
    box-shadow: none;
}

body.c8-dark #frame #container #propertyBudgetDiv .v-card .v-card__title input {
    /* border: 1px solid !important; */
    /* border-color: var(--primary-text-color); */
}

body.c8-dark #frame #container #propertyBudgetDiv .v-card header.v-toolbar .v-toolbar__content {
    background: var(--dark-tertiary-color);
}

body.c8-dark #frame #container #propertyBudgetDiv .v-card header.v-toolbar .v-toolbar__content div,
body.c8-dark #frame #container #propertyBudgetDiv .v-data-table__checkbox,
body.c8-dark #frame #container #propertyBudgetDiv .v-card .v-data-table__wrapper .v-data-table-header .columnheader div,
body.c8-dark
    #frame
    #container
    #propertyBudgetDiv
    .v-card
    .v-data-table__wrapper
    .v-data-table-header
    .columnheader
    table
    tr {
    background: transparent;
}

body.c8-dark #frame #container #propertyBudgetDiv .theme--light.v-data-table thead tr:last-child th {
    background: var(--dark-secondary-color);
    color: var(--primary-text-color);
}

body.c8-dark #frame #container #propertyBudgetDiv .v-card__actions,
body.c8-dark #frame #container #propertyBudgetDiv .v-card .v-card__actions {
    background: var(--dark-primary-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .v-list-item--link:before {
    background-color: var(--dark-ligthest-color);
}

body.c8-dark #frame #container #propertyBudgetDiv .v-dialog__content,
body.c8-dark #frame #container #propertyBudgetDiv .v-overlay {
    background: transparent !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .c8-page .v-input:not(.v-textarea) input {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .theme--light.v-label {
    color: var(--primary-text-color) !important;
}

body.c8-dark
    #frame
    #container
    #propertyBudgetDiv
    .theme--light.v-text-field
    > .v-input__control
    > .v-input__slot:before {
    border-color: var(--primary-text-color);
}

body.c8-dark #frame #container #propertyBudgetDiv .v-application .grey--text.text--darken-1 {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .v-alert,
body.c8-dark #frame #container #propertyBudgetDiv .v-alert .v-alert__wrapper,
body.c8-dark #frame #container #propertyBudgetDiv .v-alert .v-alert__wrapper .v-alert__content,
body.c8-dark #frame #container #propertyBudgetDiv .v-alert .v-alert__wrapper .v-alert__content div {
    background: var(--light-primary-color) !important;
    color: var(--light-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .v-alert .v-alert__wrapper i.v-icon.v-alert__icon.red--text {
    color: black !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .v-alert .v-alert__wrapper .v-alert__content div .v-divider {
    border: none !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .v-btn.back-btn {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #propertyBudgetDiv td .tree-vertical-line,
body.c8-dark #frame #container #propertyBudgetDiv td .tree-horizontal-line {
    z-index: 0;
    background-color: transparent !important;
}

.comment-icon {
    color: grey;
    caret-color: grey;
}

.comment-icon-has-content {
    color: $c8-has-comment !important;
    caret-color: $c8-has-comment !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .comment-icon-has-content {
    color: $c8-has-comment !important;
    caret-color: $c8-has-comment !important;
}

.income-unsaved-bg {
    background: $c8-unsaved-bg !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .income-unsaved-bg {
    background: $c8-unsaved-bg-dark !important;
}

body.c8-dark #frame #container #propertyBudgetDiv .variance_col {
    background-color: $c8-variance-col !important;

    div {
        background-color: $c8-variance-col !important;
    }
}
