@layer theme, base, components, utilities;

@import 'tailwindcss/theme.css' layer(theme);
@import 'tailwindcss/utilities.css' layer(utilities);

@import '_variables';
@import 'vue-multiselect/dist/vue-multiselect.min.css';

@theme {
    --color-primary: #00baf2;
    --color-secondary: #3489a1;
    --color-error: #b71c1c;
    --color-warning: #ffb812;
    --color-success: #2ab27b;
}

.max-w-fit {
    max-width: fit-content !important;
}
#menuIconShowSideBarMenu {
    box-sizing: initial !important;
}
//.vue-select2 {
//  z-index: 9999999 !important;
//}
.dropdown-button {
    padding-top: 0.4em !important;
    padding-bottom: 0.47em !important;
    border-bottom-left-radius: 0px !important;
    border-top-left-radius: 0px !important;
}
.dropdown-left {
    float: left !important;
}
.dropdown-300 {
    width: 300px !important;
    max-width: 300px !important;
    .multiselect__content-wrapper {
        width: 300px !important;
        max-width: 300px !important;
    }
}
.dropdown-400 {
    width: 400px !important;
    max-width: 400px !important;
    .multiselect__content-wrapper {
        width: 400px !important;
        max-width: 400px !important;
    }
}
.dropdown-800 {
    width: 800px !important;
    max-width: 800px !important;
    .multiselect__content-wrapper {
        width: 800px !important;
        max-width: 800px !important;
    }
}
.dropdown-200 {
    width: 200px !important;
    max-width: 200px !important;
    .multiselect__content-wrapper {
        width: 200px !important;
        max-width: 200px !important;
    }
}
.dropdown-150 {
    width: 150px !important;
    max-width: 150px !important;
    .multiselect__content-wrapper {
        width: 150px !important;
        max-width: 150px !important;
    }
}
.dropdown-auto {
    width: auto !important;
}

.dropDown {
    background: none !important;
}
.ui.selection.dropdown {
    padding: 0px !important;
    border: none !important;
    min-height: 0px !important;
}
.ui.selection.dropdown > .search.icon,
.ui.selection.dropdown > .delete.icon,
.ui.selection.dropdown > .dropdown.icon {
    padding: 0px !important;
    margin: 0px !important;
    top: 0.585714em !important;
    height: 24px !important;
}
.v-select .dropdown-toggle .clear {
    display: none !important;
}
.v-select input[type='search'],
.v-select input[type='search']:focus {
    height: 23px !important;
}
.v-select .open-indicator {
    bottom: 0px !important;
}
.v-select .dropdown-toggle {
    border-radius: 0px !important;
}
.v-select .selected-tag {
    border: 0px !important;
    margin: 0px !important;
    padding-top: 0px !important;
    height: 23px !important;
}

.multiselect .dropdown-toggle .clear {
    display: none !important;
}
.multiselect input[type='search'],
.multiselect input[type='search']:focus {
    height: 23px !important;
}
.multiselect .open-indicator {
    bottom: 0px !important;
}
.multiselect .dropdown-toggle {
    border-radius: 0px !important;
}
.multiselect .selected-tag {
    border: 0px !important;
    margin: 0px !important;
    padding-top: 0px !important;
    height: 23px !important;
}
.multiselect__tags {
    border-radius: 0px !important;
    padding: 0px 2px 0px 0px;
    min-height: 0px !important;
}

.item > .multiselect > .multiselect__tags {
    border-radius: 0px !important;
    padding: 4px 2px 5px 0px !important;
    min-height: 0px !important;
}
.multiselect__single {
    margin-bottom: 1px !important;
}
.multiselect__select {
    height: 24px !important;
    z-index: 999 !important;
    padding: 12px 8px !important;
}
.multiselect__input,
.multiselect__single {
    //width: 92% !important;
    margin-bottom: 1px !important;
    //border: 1px solid #f6f6f6 !important;
    //border-top: 1px solid #f6f6f6 !important;
    //border-bottom: 1px solid #f6f6f6 !important;
    //border-left: 1px solid #f6f6f6 !important;
    border-right: 0px !important;
    border-radius: 0px !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
}
.multiselect {
    min-height: 0px !important;
}
.multiselect__option--highlight {
    background: #00baf2 !important;
}
.multiselect__option--selected.multiselect__option--highlight {
    background: #00baf2 !important;
}
.multiselect__option--highlight:after {
    background: #00baf2 !important;
}
.multiselect__option--selected.multiselect__option--highlight {
    background: #00baf2 !important;
}
.uix-multiselect .option-element,
.dragged-element {
    padding-top: 5px !important;
    padding-bottom: 5px !important;
}
.modal-container {
    width: fit-content !important;
}
.uix-multiselect .ui-widget-header {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
}
.option-element:hover {
    background: #ff9507 !important;
}

.multiselect__tags {
    border-radius: 0px !important;
    //padding: 1px 2px 0px 0px !important;
    min-height: 0px !important;
    font-size: 11px !important;
}
.multiselect__single {
    font-size: 11px !important;
}

.multiselect--active {
    z-index: 999999 !important;
}
.md-theme-default a:not(.md-button) {
    color: inherit;
}
.dropDown {
    background: none !important;
}
input[type='text'],
input[type='password'] {
    border: none !important;
}
.dropdown-toggle {
    padding: 0 !important;
}
.v-select input[type='search'],
.v-select input[type='search']:focus {
    margin: 0 !important;
}
.multiselect__placeholder {
    margin: 0px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    padding-left: 5 !important;
    padding-right: 0 !important;
}
.multiselect__content {
    font-size: 11px !important;
}
// .container *:not(.v-label):not(.v-card__title):not(.v-card__subtitle):not(.page-title):not(.page-subtitle) {
//   font-size: 11px !important;
// }
//.ui.input > input {
//  padding-left: 10px !important;
//}
.ui[class*='left icon'].input > input {
    //width: 80px;
    padding-left: 2.67142857em !important;
}
///* Left Icon Input */
//.ui[class*="left icon"].input > i.icon {
//  right: auto;
//  left: 1px;
//  border-radius: 0.28571429rem 0em 0em 0.28571429rem;
//}
//.ui[class*="left icon"].input > i.circular.icon {
//  right: auto;
//  left: 0.5em;
//}
//.ui[class*="left icon"].input > input {
//  padding-left: 1.67142857em !important;
//  padding-right: .5em !important;
//}
.md-steppers-wrapper {
    height: auto !important;
}
li.item a {
    color: gray !important;
}
li.active a {
    color: #3489a1 !important;
}
.v-stepper__wrapper {
    min-height: 100%;
}
.v-application .title {
    font-size: 11px !important;
    line-height: 25px !important;
}
.theme--light.application {
    background: none !important;
}

.ui.unselectable.table tbody tr:hover,
.ui.table tbody tr td.unselectable:hover {
    background: none !important;
    color: rgba(0, 0, 0, 0.95) !important;
}
.left {
    float: none !important;
}
.v-btn--active {
    border: 1px solid #e79400 !important;
    background-color: #ff9507 !important;
    font-weight: normal !important;
    color: #fff !important;
}
.v-item-group > button {
    border: 1px solid #d0d0d0;
    background-color: #e1e1e1;
    font-weight: normal;
    color: #8d8d8d;
}
.v-select .dropdown-toggle {
    border: 1px solid #e8e8e8 !important;
}
.multiselect,
.multiselect__input,
.multiselect__single {
    font-size: inherit !important;
}
.v-btn-toggle > .v-btn {
    //min-height: $c8-field-min-height !important;
    //height: $c8-field-min-height !important;
}

.ui.checkbox > label {
    font-size: 11px !important;
}
.menuable__content__active {
    //top: 2em !important;
    //left: 0px !important;
}
.v-date-picker-table {
    height: auto !important;
}
.v-messages .theme--light {
    min-height: 0px !important;
}
.v-menu__activator > .v-text-field {
    padding-top: 0px !important;
    margin-top: 0px !important;
}
.v-messages {
    min-height: 0px !important;
}
.v-input__slot {
    margin-bottom: 0px !important;
}
.v-text-field__slot {
    width: 78px;
}
.vue-data-grid {
    margin: 0px !important;
}
.vue-data-grid th {
    background: #eaeaea !important;
    color: #888 !important;
}
.vue.data.grid .fieldDescription {
    padding: 0px !important;
}
.vue-data-grid td {
    margin: 0px;
    padding: 3px 6px !important;
    font-size: 11px;
    //line-height: 25px;
    border-bottom: 1px solid #ececec;
}
.vue-data-grid th {
    margin: 0px;
    padding: 3px 6px !important;
    font-size: 11px;
    line-height: 25px;
    border-bottom: 1px solid #ececec;
}
#menuSidebarContent {
    width: 17rem !important;
}
.sidebarStyle > .item.active {
    background: rgba(0, 0, 0, 0.05) !important;
    font-weight: bolder !important;
}
.sidebarStyle > .item {
    font:
        11px 'Segoe UI',
        Helvetica,
        Arial,
        Tahoma,
        sans-serif !important;
    font-size: 1.2em !important;
    //font-weight: bolder !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    line-height: 1.3 !important;
    position: relative !important;
    vertical-align: middle !important;
    line-height: 1 !important;
    text-decoration: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-box-flex: 0 !important;
    -ms-flex: 0 0 auto !important;
    flex: 0 0 auto !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    background: none !important;
    padding: 0.92857143em 1.14285714em !important;
    text-transform: none !important;
    color: rgba(0, 0, 0, 0.87) !important;
    /*font-weight: normal;*/
    -webkit-transition:
        background 0.1s ease,
        color 0.1s ease,
        -webkit-box-shadow 0.1s ease !important;
    transition:
        background 0.1s ease,
        color 0.1s ease,
        -webkit-box-shadow 0.1s ease !important;
    transition:
        background 0.1s ease,
        box-shadow 0.1s ease,
        color 0.1s ease !important;
    transition:
        background 0.1s ease,
        box-shadow 0.1s ease,
        color 0.1s ease,
        -webkit-box-shadow 0.1s ease !important;
}
.side-by-side {
    font-weight: normal !important;
}
.dblist {
    font-weight: normal !important;
}
#menuIconShowSideBarMenu > .item {
    font:
        11px 'Segoe UI',
        Helvetica,
        Arial,
        Tahoma,
        sans-serif !important;
    font-size: 1.1em !important;
    font-weight: bolder !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    line-height: 1.3 !important;
    /*position: relative !important;*/
    vertical-align: middle !important;
    line-height: 1 !important;
    text-decoration: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-box-flex: 0 !important;
    -ms-flex: 0 0 auto !important;
    flex: 0 0 auto !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    background: none !important;
    padding: 0.92857143em 1.14285714em !important;
    text-transform: none !important;
    /*font-weight: normal;*/
    -webkit-transition:
        background 0.1s ease,
        color 0.1s ease,
        -webkit-box-shadow 0.1s ease !important;
    transition:
        background 0.1s ease,
        color 0.1s ease,
        -webkit-box-shadow 0.1s ease !important;
    transition:
        background 0.1s ease,
        box-shadow 0.1s ease,
        color 0.1s ease !important;
    transition:
        background 0.1s ease,
        box-shadow 0.1s ease,
        color 0.1s ease,
        -webkit-box-shadow 0.1s ease !important;
}
#menuIconShowSideBarMenu > .item:not(#menuIconShowSideBarMenu2) {
    /*position: relative !important;*/
}
.inputAmount {
    text-align: right !important;
    width: 7em;
    padding-left: 1em !important;
}
.v-btn--small {
    height: 24px !important;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}
.v-chip.v-chip--outline.theme--light.red.red--text > .v-chip__content {
    height: 14px !important;
}
.v-chip.v-chip--outline.theme--light.red.red--text > .v-chip__content > i {
    font-size: 14px !important;
}
.v-application ol,
.v-application ul {
    padding-left: 0px !important;
}
.data-grid > tbody > .row {
    display: table-row !important;
    background: #fff !important;
}
.row {
    background: none;
}
.date-picker-component > .v-menu__content.theme--light.menuable__content__active {
    top: auto !important;
    left: auto !important;
    min-width: auto !important;
}
.date-picker-input-field > .v-input__control > .v-text-field__details {
    min-height: 0px !important;
}
.date-picker-input-field.v-text-field {
    padding-top: 0px !important;
    margin-top: 0px !important;
}
.theme--light.v-application {
    background: #ffffff;
    color: rgba(0, 0, 0, 0.87);
}
.titleHeader {
    color: white !important;
    .v-btn--round {
        .v-btn__content {
            .v-icon {
                color: white;
            }
        }
    }
}
.successHeader {
    color: white !important;
    .v-btn--round {
        .v-btn__content {
            .v-icon {
                color: white;
            }
        }
    }
}
.ui.table td.center {
    text-align: center !important;
}
.ui.table th.center {
    text-align: center !important;
}

.data-grid-version-2 {
    background: transparent;
    margin: 0px;
    padding: 0px;
    width: 100%;
}
.data-grid-version-2 td {
    margin: 0px;
    padding: 6px;
    font-size: 11px;
    line-height: 25px;
    border-bottom: 0px solid #ececec;
}
.data-grid-version-2 th {
    margin: 0px !important;
    padding: 6px !important;
    font-size: 11px !important;
    line-height: 25px !important;
    border-bottom: 0px solid #ececec !important;
}
.data-grid-version-2 tr {
}
.data-grid-version-2 td.data {
    white-space: nowrap;
}
.data-grid-version-2 .subTitle {
    background: #3489a1;
    color: white;
}
.data-grid-version-2 .subTitle td {
    font-size: 12px;
    font-weight: bold;
}
.data-grid-version-2 .subTitle a {
    color: white;
}

.data-grid-version-2 .info {
    color: #36708d !important;
    background: #acd7ec;
}
.data-grid-version-2 .info td {
    border-top: 1px solid #91d5ff;
    border-bottom: 1px solid #91d5ff;
}
.data-grid-version-2 .info a {
    color: black;
}

.data-grid-version-2 .info2 {
    color: #aaa;
    background: white;
}
.data-grid-version-2 .info2 td {
    border-bottom: 1px solid #ececec;
    border-top: 1px solid #6bc9ff;
}
.data-grid-version-2 .info2 a {
    color: white;
}

.data-grid-version-2 .subHeader {
    color: #36708d;
    background: #acd7ec;
}
.data-grid-version-2 .subHeader td {
    border-top: 1px solid #91d5ff;
    border-bottom: 1px solid #91d5ff;
}
.data-grid-version-2 .warningTitle {
    font-size: 13px;
    background: crimson;
    color: white;
}
.data-grid-version-2 .fieldDescription {
    background: #eaeaea;
    color: #888;
}
.data-grid-version-2 .fieldDescription td {
}
.data-grid-version-2 .fieldTitle {
}
.data-grid-version-2 .title {
    width: 150px;
    text-align: right;
    color: #666;
}
.data-grid-version-2 .item {
}
.data-grid-version-2 td.accept {
    color: green;
}
.data-grid-version-2 td.reject {
    color: crimson;
}
.data-grid-version-2 tr.draft td {
    color: white;
    font-weight: bold;
}
.data-grid-version-2 tr.draft a {
    color: white !important;
}
.data-grid-version-2 tr.rejected td {
    color: white;
    font-weight: bold;
}
.data-grid-version-2 tr.rejected a {
    color: white !important;
}
.data-grid-version-2 tr.approved td {
    color: white;
    font-weight: bold;
}
.data-grid-version-2 tr.finalised td {
    color: white;
    font-weight: bold;
}
.data-grid-version-2 tr.finalised a {
    color: white !important;
}
.data-grid-version-2 td.action {
    width: 1% !important;
    white-space: nowrap;
    align: right;
}
.data-grid-version-2 .menubar {
    background: #99d3ee;
}
.data-grid-version-2 .menubar a {
    font-size: 1.2em;
    border-bottom: none;
    margin-left: 1em;
}
.data-grid-version-2 .notice {
    background: #b7e0ff;
}
.data-grid-version-2 .footer {
    text-align: right; /*background:url(../images/form_footer.gif)#dcdcdc;*/
}
.data-grid-version-2 .footer td {
    border-top: 1px solid #6bc9ff;
}
.data-grid-version-2 .light {
}
.data-grid-version-2 .light td {
    color: #888;
}
.data-grid-version-2 .highlight {
    background: #e6f3f9;
}
.data-grid-version-2 .highlight td {
    font-weight: bold;
}
.data-grid-version-2 .highlightnormal {
    background: #e6f3f9;
}
.data-grid-version-2 a {
    color: #0089b3;
    cursor: pointer;
}
.data-grid-version-2 img {
    border: none !important;
}
.data-grid-version-2 a:hover {
    color: #1f79b8;
}
.data-grid-version-2 td.short {
    width: 50px;
}
.data-grid-version-2 td.unprocessed {
    color: orange;
    font-weight: bold;
}
.data-grid-version-2 .warning {
    background: #c41e3a;
    border: 1px solid DarkRed;
    color: white;
    padding: 15px;
    margin: 0;
    width: 100%;
}
.data-grid-version-2 .warning td {
    border: 2px solid #c20022;
    background: #fee5e9;
    color: #f1002b;
    padding: 10px;
}
.data-grid-version-2 .problem td {
    border-top: 1px solid #ff6b90;
}
.data-grid-version-2 .historical {
    background: #f7f7f7;
    color: #b2b2b2;
}
.data-grid-version-2 .historical a {
    color: #b2b2b2;
}
.data-grid-version-2 .quickEntry2 {
    color: #1167a4;
    font-weight: bold;
    background: #daeaf2;
    border-bottom: 1px solid #b9d6e8;
}
.data-grid-version-2 .subMenu {
    font-size: 11px;
    background: #666;
    color: #ffd012;
}
.data-grid-version-2 .submenu td {
    border-top: 1px solid #39444c;
    border-bottom: 1px solid #39444c;
}
.data-grid-version-2 .subMenu a {
    display: inline;
    margin: 0em 1em;
    color: white;
    text-decoration: underline;
}
.data-grid-version-2 .subMenu a:hover {
    color: #ffd012;
}
.data-grid-version-2 td.required {
    width: 1%;
    color: crimson;
    font-weight: bold;
    valign: middle;
}

.v-step {
    z-index: 99999999 !important;
}

.date-picker-input-field > .v-input__control > .v-input__slot > div.v-menu__content {
    max-width: none !important;
}

[id^='input-'] {
    background: none !important;
}

.dropdown-toggle {
    background-color: white !important;
}

.noteTextArea textarea {
    background: linear-gradient(#f9efaf, #f7e98d) !important;
    background: -o-linear-gradient(#f9efaf, #f7e98d) !important;
    background: -ms-linear-gradient(#f9efaf, #f7e98d) !important;
    background: -moz-linear-gradient(#f9efaf, #f7e98d) !important;
    background: -webkit-linear-gradient(#f9efaf, #f7e98d) !important;
    padding: 12px;
}
.noteTextArea {
    .v-input__slot {
        background: linear-gradient(#f9efaf, #f7e98d) !important;
        background: -o-linear-gradient(#f9efaf, #f7e98d) !important;
        background: -ms-linear-gradient(#f9efaf, #f7e98d) !important;
        background: -moz-linear-gradient(#f9efaf, #f7e98d) !important;
        background: -webkit-linear-gradient(#f9efaf, #f7e98d) !important;
        padding: 12px;
    }
}
.noteTextAreaBgColor {
    background: linear-gradient(#f9efaf, #f7e98d) !important;
    background: -o-linear-gradient(#f9efaf, #f7e98d) !important;
    background: -ms-linear-gradient(#f9efaf, #f7e98d) !important;
    background: -moz-linear-gradient(#f9efaf, #f7e98d) !important;
    background: -webkit-linear-gradient(#f9efaf, #f7e98d) !important;
}
.noteTextArea {
    //padding: 6px !important;
    width: 100%;
    font-size: 11px !important;
}
.rotate90 {
    transform: rotate(90deg);
}
.cirrus-input-percentage {
    max-width: 80px !important;
    min-width: 80px !important;
    .v-text-field__slot {
        input {
            text-align: right;
        }
    }
}
.cirrus-input-dollar {
    max-width: 100px !important;
    min-width: 100px !important;
    .v-text-field__slot {
        input {
            text-align: right;
        }
    }
}
.cirrus-input-table-textbox {
    max-width: 150px !important;
    min-width: 150px !important;
    .v-text-field__slot {
        input {
            text-align: left;
        }
    }
}
.cirrus-input-form-textbox {
    max-width: 300px !important;
    min-width: 300px !important;
    .v-text-field__slot {
        input {
            text-align: left;
        }
    }
}
#goTopBtn {
    width: 50px !important;
}
.data-grid-no-line td {
    border-top: none !important;
    border-bottom: none !important;
}
.table-raw td {
    padding: 0 !important;
    border-top: 0 !important;
    border-bottom: 0 !important;
}
.v-alert--prominent .v-alert__icon {
    height: 30px !important;
    min-width: 30px !important;
}
.v-alert--prominent .v-alert__icon.v-icon {
    font-size: 19px;
}
.print-break-page {
    page-break-after: always;
}
.v-tab {
    font-size: 11px;
}
.v-tabs--icons-and-text > .v-tabs-bar {
    height: 49px;
}
.data-grid-dense td {
    padding: 3px 6px;
}
.v-item-group.v-btn-toggle .v-btn.v-btn + .v-btn {
    margin-left: 0px;
}

.section-toolbar:not(.subHeader) {
    .v-card__actions {
        padding-left: 6px;
        padding-right: 6px;
        padding-top: 3px;
        padding-bottom: 3px;
        .v-input:not(.v-textarea) {
            max-width: $c8-input-max-width;
            margin-top: 0px;
            min-width: initial;
            width: 100%;
            &.v-text-field > .v-input__control > .v-input__slot:before {
                border: 0 !important;
            }
            &.v-text-field > .v-input__control > .v-input__slot:after {
                border: 0 !important;
            }
            .v-text-field__details {
                min-height: initial;
                & .v-message,
                & .v-counter {
                    padding: 0.3rem 0;
                }
            }
            input {
                display: inline-block;
                border: 1px solid $c8-border-color !important;
                min-height: 30px;
                padding: 3px 10px;
                color: #ffffff !important;
                //background-color: #FFFFFF !important;
            }
            &.text-right input {
                text-align: right;
            }
        }
        * {
            -webkit-transition: none !important;
            -moz-transition: none !important;
            -o-transition: none !important;
            transition: none !important;
        }
    }
}
.section-toolbar.subHeader {
    .v-card__actions {
        padding-left: 6px;
        padding-right: 6px;
        padding-top: 3px;
        padding-bottom: 3px;
        .v-input:not(.v-textarea) {
            max-width: $c8-input-max-width;
            margin-top: 0px;
            min-width: initial;
            width: 100%;
            &.v-text-field > .v-input__control > .v-input__slot:before {
                border: 0 !important;
            }
            &.v-text-field > .v-input__control > .v-input__slot:after {
                border: 0 !important;
            }
            .v-text-field__details {
                min-height: initial;
                & .v-message,
                & .v-counter {
                    padding: 0.3rem 0;
                }
            }
            input {
                display: inline-block;
                border: 1px solid $c8-border-color !important;
                min-height: 30px;
                padding: 3px 10px;
                color: black !important;
                //background-color: #FFFFFF !important;
            }
            &.text-right input {
                text-align: right;
            }
        }
        * {
            -webkit-transition: none !important;
            -moz-transition: none !important;
            -o-transition: none !important;
            transition: none !important;
        }
    }
}

.cirrus-tab-theme {
    .v-tabs-bar {
        background-color: #006581 !important;
        color: #ffffff !important;
    }
    .v-tab {
        color: #ffffff !important;
        .v-icon {
            color: #ffffff !important;
        }
    }
}

.data-table-mini-action {
    width: 56px !important;
    padding: 0 !important;
}
.v-data-table__expanded__content td {
    padding-right: 0 !important;
}
.dropdown-menu {
    z-index: 99999999999 !important;
}

.no-border-line {
    border-top: 0px !important;
    border-bottom: 0px !important;
}

.highlight {
    background: #e6f3f9;
}

.dashboard.v-tabs--icons-and-text > .v-tabs-bar {
    height: 32px;
}

.dashboard.v-tabs--icons-and-text > .v-tabs-bar .v-tab > :first-child {
    margin-bottom: 0px;
}

@media only screen and (min-width: 960px) {
    .dashboard #right_panel {
        padding-left: 50px;
    }
}

.dashboard a:hover .v-icon {
    display: -webkit-inline-box;
}

.dashboard .v-tabs-bar .theme--dark.v-icon,
.dashboard .v-tabs-bar a {
    text-decoration: none;
    color: gray !important;
}
.dashboard .v-tabs-bar .theme--dark.v-icon,
.dashboard .v-tabs-bar a span.fa-text {
    font-family: 'Lato', 'Helvetica Neue', Arial, Helvetica, sans-serif;
}

.dashboard.theme--dark.v-tabs > .v-tabs-bar {
    background-color: white !important;
}

.dashboard .v-tabs-bar .v-tab--active,
.dashboard .v-tab--active .theme--dark.v-icon {
    background-color: #e9e9e9;
    color: black !important;
}
.c8-dark .dashboard .v-tabs-bar .theme--dark.v-icon,
.c8-dark .dashboard .v-tabs-bar a {
    color: white !important;
}
.c8-dark .dashboard .v-tabs-bar .v-tab--active,
.dashboard .v-tab--active .theme--dark.v-icon {
    background-color: transparent;
}

.cirrus8-loader-vue-js {
    opacity: 0.9;
    left: 0px;
    top: 0 px;
    color: white;
    background: white;
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 99999 !important;
}

.v-tab.v-tab--active {
    font-weight: bolder;
    font-size: 12px;
}
.c8-page {
    .v-tab {
        font-family:
            Lato,
            Helvetica Neue,
            Arial,
            Helvetica,
            sans-serif;
        text-decoration: none;
    }
}

.c8-info-alert {
    .v-alert__content {
        font-size: 11px;
    }
}
.v-data-table tbody tr.v-data-table__expanded__content {
    box-shadow: none !important;
}
.tree-horizontal-line {
    width: 35px;
    height: 47px;
    border-bottom: 2px solid #bfbfbf;
    position: absolute;
}
.tree-vertical-line {
    width: 47px;
    height: 47px;
    border-left: 2px solid #bfbfbf;
    position: absolute;
}
