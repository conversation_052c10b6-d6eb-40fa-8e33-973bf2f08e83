@import '_variables';
.c8-page {
    font-size: $c8-font-size;
    font-family: 'Segoe UI', Helvetica, Arial, Tahoma, sans-serif;
    padding: 0;
    .v-btn-toggle {
        border-radius: 0;
    }
    &.v-dialog {
        border-radius: 0;
        .v-card {
            border-radius: 0;
            font-size: $c8-font-size;
            .v-card__text {
                padding: 0;
                font-size: $c8-font-size !important;
            }
            .v-card__title.headline {
                position: relative;
                background-color: $c8-secondary;
                font-size: $c8-font-size + 2px !important;
                color: #ffffff;
                padding: 0.5rem 1rem;
                margin-bottom: 0.5rem;
                .dialog-close {
                    position: absolute;
                    top: 0.8rem;
                    right: 1rem;
                    i {
                        font-size: $c8-font-size + 7px !important;
                        color: #ffffff;
                    }
                }
            }
        }
    }
    .multiselect {
        min-height: 30px !important;
        .multiselect__select {
            top: 5px;
            right: -4px;
            z-index: 2 !important;
        }
        .multiselect__tags {
            border: 1px solid $c8-border-color !important;
            min-height: 30px !important;
            padding-left: 5px !important;

            .multiselect__placeholder {
                padding-top: 6px !important;
            }
            .multiselect__single {
                color: $c8-font-color !important;
                padding-top: 5px !important;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: calc(100% - 30px);
            }
        }
        .multiselect__content {
            width: 100%;
            .multiselect__element {
                .multiselect__option {
                    white-space: initial;
                    .option__desc {
                        .option__title {
                            white-space: initial;
                        }
                    }
                }
            }
        }
        .multiselect__content-wrapper {
            //max-height: 230px !important; //use prop maxHeight or optionHeight
        }
        &.multiselect--active {
            .multiselect__select {
                top: 0;
                height: 30px !important;
            }
            .multiselect__tags {
                input.multiselect__input {
                    padding-left: 5px !important;
                    margin-bottom: 0 !important;
                    height: 26px !important;
                }
            }
        }
        &.multiselect--disabled {
            .multiselect__select {
                background: none;
            }
        }
        &.error {
            .multiselect__tags {
                border: 1px solid #f44336 !important;
            }
        }
    }
    .v-input--selection-controls {
        padding: 0;
        margin: 0;
        margin-top: 3px;
    }
    .v-input--selection-controls__input {
        background-color: transparent !important;
        margin-right: 0px;
    }
    .v-input--selection-controls.v-input .v-label {
        font-size: $c8-font-size;
    }
    .col {
        padding: 5px;
    }
    .v-input:not(.v-textarea) {
        display: inline-block;
        min-width: $c8-input-max-width;
        input {
            box-shadow: none !important;
            border: 0 !important;
            color: $c8-font-color !important;
        }
        &.v-input--checkbox {
            min-width: initial;
        }
        &.v-input--is-disabled input {
            cursor: not-allowed;
            color: $c8-disabled-color !important;
        }
        &.v-input--is-disabled:not(.v-input--is-readonly) {
            cursor: not-allowed;
            pointer-events: auto;
            color: $c8-disabled-color !important;
        }
    }
    input {
        display: inline-block;
        border: 1px solid $c8-border-color;
        min-height: 30px;
        padding: 5px 10px;
        background-color: #ffffff;
    }
    .c8-dp {
        display: inline-block;
        max-width: $c8-input-max-width;
        .c8-dp-input {
            display: inline-block;
            border: 1px solid $c8-border-color;
            color: $c8-font-color !important;
            min-height: 30px;
            padding: 5px 10px;
            width: calc(#{$c8-input-max-width} - 223px);
            background-color: #ffffff;
        }
        img {
            cursor: pointer;
            display: inline-block;
            margin-left: 5px;
            &.disabled {
                cursor: not-allowed;
            }
        }
        .vdp-datepicker input {
            display: none;
        }
        .vdp-datepicker__calendar {
            position: fixed;
            // left: 5px;
        }
        .vdp-datepicker__calendar .cell.selected {
            background: $c8-primary;
            color: #ffffff;
        }
        .vdp-datepicker__calendar .cell:not(.blank):not(.disabled).day:hover {
            border: 1px solid $c8-primary;
        }
        .vdp-datepicker__calendar {
            .day__month_btn {
                font-size: $c8-font-size + 1px;
                text-transform: uppercase;
            }
            .cell.day-header {
                font-size: $c8-font-size + 1px;
                color: $c8-table-th-text-color;
                background-color: $c8-table-th-color;
                text-transform: uppercase;
            }
        }
    }
    .form-title-row {
        color: #ffffff;
        font-size: calc(#{$c8-font-size} + 1px);
        font-weight: bold;
        background-color: $c8-secondary;
        padding: 0.7rem 0.5rem;
    }
    .page-header {
        padding: 0;
        margin-bottom: 10px;
        .page-title {
            font-size: calc(#{$c8-font-size} + 18px) !important;
            color: $c8-title;
            margin-bottom: 20px;
        }
        .page-subtitle {
            font-size: calc(#{$c8-font-size}) !important;
            margin-top: -29px;
            color: #7f8c8d;
            text-transform: uppercase;
        }
    }
    .page-title {
        font-size: calc(#{$c8-font-size} + 4px) !important;
        color: $c8-title;
    }
    .page-form {
        .form-row {
            color: $c8-font-color !important;
            margin: 0;
            border-bottom: 1px solid $c8-border-color;
            .form-toggle {
                button {
                    color: #4e4e4e !important;
                    border: 1px solid #d0d0d0 !important;
                    background-color: #e1e1e1 !important;
                    padding: 0.4em 1em !important;
                    font-size: 11px !important;
                    font-family: 'Segoe UI', Helvetica, Arial, Tahoma, sans-serif;
                    text-transform: none !important;
                    letter-spacing: 0px !important;
                }
                button.v-btn--active {
                    border: 1px solid #ff9507 !important;
                    background-color: #ff9507 !important;
                    font-weight: normal !important;
                    color: #fff !important;
                }
                button:first-child {
                    border-top-left-radius: 3px;
                    border-bottom-left-radius: 3px;
                }
                button:last-child {
                    border-top-right-radius: 3px;
                    border-bottom-right-radius: 3px;
                }
            }
            .multiselect {
                max-width: $c8-input-max-width;
            }
            .v-input:not(.v-textarea) {
                max-width: $c8-input-max-width;
                margin-top: 0px;
                padding-top: 0px;
                min-width: initial;
                width: 100%;
                &.v-text-field > .v-input__control > .v-input__slot:before {
                    border: 0 !important;
                }
                &.v-text-field > .v-input__control > .v-input__slot:after {
                    border: 0 !important;
                }
                .v-text-field__details {
                    min-height: initial;
                    & .v-message,
                    & .v-counter {
                        padding: 0.3rem 0;
                    }
                }
                input {
                    display: inline-block;
                    border: 1px solid $c8-border-color !important;
                    min-height: 30px;
                    padding: 3px 10px;
                    background-color: #ffffff;
                }
                &.text-right input {
                    text-align: right;
                }
            }
            * {
                -webkit-transition: none !important;
                -moz-transition: none !important;
                -o-transition: none !important;
                transition: none !important;
            }
            .form-subheader {
                background: $c8-table-th-color !important;
            }
            .form-label {
                padding-top: 12px;
                text-align: right;
                max-width: 200px;
                &:after {
                    display: inline-block;
                    content: '\00a0\00a0';
                    padding: 0 10px;
                }
                &.required:after {
                    display: inline-block;
                    content: '*';
                    color: $c8-error;
                    padding: 0 10px;
                }
            }
            .form-label.small-10 {
                font-size: 10px;
            }
            .form-input {
                .v-input.primary--text {
                    color: $c8-font-color !important;
                    caret-color: $c8-font-color !important;
                }
                .form-input-text {
                    display: inline-block;
                    padding-top: 6px;
                }
                &.text-only {
                    display: inline-block;
                    padding-top: 12px;
                }
            }
            .form-input--no-pad-top {
                .v-input.primary--text {
                    color: $c8-font-color !important;
                    caret-color: $c8-font-color !important;
                }
                .form-input-text {
                    display: inline-block;
                    padding-top: 0px;
                }
                &.text-only {
                    display: inline-block;
                    padding-top: 12px;
                }
            }
            .form-text-button {
                margin-bottom: -6px;
                height: $c8-field-min-height !important;
                margin-left: -3px !important;
            }
        }
        .form-title-row {
            color: #ffffff;
            font-size: calc(#{$c8-font-size} + 1px);
            font-weight: bold;
            background-color: $c8-secondary;
            padding: 0.7rem 0.5rem;
        }
        .form-helper-text {
            display: inline-block;
            font-style: italic;
            margin: 0 0.5rem;
        }
        .v-chip:not(.v-size--x-small) {
            border-radius: 0px !important;
            height: $c8-field-min-height;
            font-size: $c8-font-size;
            margin: 0 5px;
            color: $c8-font-color;
            &.text-warning {
                background: rgba($c8-warning, 0.4) !important;
                border: 2px solid $c8-warning;
            }
            &.text-error {
                background: rgba($c8-error, 0.4) !important;
                border: 2px solid $c8-error;
            }
            &.text-primary {
                background: rgba($c8-primary, 0.4) !important;
                border: 2px solid $c8-primary;
            }
        }
    }
    .page-form.no-line .form-row {
        border: 0px;
    }
    .v-dialog .page-list,
    .page-list {
        .c8-float-header {
            position: fixed;
            top: 0px;
            display: none;
            z-index: 1;
            background-color: transparent;
            tr.c8-page-table-row-header td {
                background-color: $c8-table-th-color;
                color: $c8-table-th-text-color !important;
                border-color: $c8-border-color;
                font-size: $c8-font-size !important;
                font-weight: 400 !important;
                text-align: left;
                padding: $c8-table-th-pd;
                border: 0;
                border-bottom: 1px solid $c8-border-color;
                height: $c8-field-min-height + 8px;
                &.inverted {
                    background-color: $c8-table-th-bg-dark;
                    border: 0;
                    color: #ffffff;
                    td {
                        border: 0;
                        color: #ffffff;
                    }
                }
            }
        }
        .v-data-table {
            .v-data-table-header th {
                font-size: $c8-font-size + 1px;
                background-color: $c8-table-th-color;
                color: $c8-table-th-text-color !important;
                border-color: $c8-border-color;
                font-weight: 400 !important;
                padding: $c8-table-th-pd;
            }
            tbody tr td {
                font-size: $c8-font-size !important;
                padding: 10px 10px;
                border-color: $c8-border-color !important;
            }
            .v-select__slot .v-select__selections input {
                box-shadow: none !important;
                padding: 0;
            }
        }
        .v-select.v-text-field input {
            box-shadow: none !important;
            padding: 0;
        }
        .c8-page-table {
            position: relative;
            .c8-page-table-header,
            table {
                width: 100%;
                font-size: $c8-font-size;
                color: $c8-font-color;
                border: 0;
                border-collapse: collapse;
                thead th,
                tbody tr.c8-page-table-row-header,
                tbody tr td.c8-page-table-row-header {
                    background-color: $c8-table-th-color;
                    color: $c8-table-th-text-color !important;
                    border-color: $c8-border-color;
                    font-size: $c8-font-size + 1px;
                    font-weight: 400 !important;
                    text-align: left;
                    padding: $c8-table-th-pd;
                    border: 0;
                    border-bottom: 1px solid $c8-border-color;
                    height: $c8-field-min-height + 8px;
                    &.inverted {
                        background-color: $c8-table-th-bg-dark;
                        border: 0;
                        color: #ffffff;
                        td {
                            border: 0;
                            color: #ffffff;
                        }
                    }
                }
                tbody td {
                    border-color: $c8-border-color !important;
                    font-size: $c8-font-size !important;
                    padding: $c8-table-td-pd;
                    border: 0;
                    border-bottom: 1px solid $c8-secondary;
                    height: $c8-field-min-height + 8px;
                    .v-input--selection-controls {
                        padding: 0;
                        margin: 0;
                    }
                    .v-input--selection-controls__input {
                        background-color: transparent !important;
                        margin-right: 0px;
                    }
                }
                tr.c8-page-table-header td {
                    border: 0;
                    padding: $c8-table-th-pd;
                    font-size: $c8-font-size + 1px !important;
                }
                tr.c8-page-table-row-header td {
                    border: 0;
                    padding: $c8-table-th-pd;
                }
                tr.c8-page-table-calendar-sub-row td {
                    border: 1;
                    padding: $c8-table-td-calendar-pd;
                }
                tr.c8-page-table-header {
                    background-color: $c8-secondary;
                    font-size: $c8-font-size + 1px;
                    color: #ffffff;
                    font-weight: 600;
                    padding: $c8-table-td-pd;
                }
                tr.c8-page-table-sub-header {
                    background-color: #acd7ec;
                    font-size: $c8-font-size + 1px;
                    color: #36708d;
                    padding: $c8-table-td-pd;
                }
                tr.c8-page-table-row-cursor td {
                    cursor: pointer;
                }
                tr.c8-page-table-footer td {
                    font-weight: 500;
                    font-size: $c8-font-size;
                    background-color: rgba($c8-primary, 0.1);
                }
                tbody tr.bg-primary td,
                tbody tr td.bg-primary {
                    background-color: rgba($c8-primary, 0.1);
                    border-bottom-color: $c8-primary !important;
                }
                tbody tr.bg-success td,
                tbody tr td.bg-success {
                    background-color: rgba($c8-success, 0.1);
                    border-bottom-color: $c8-success !important;
                }
                tbody tr.bg-warning td,
                tbody tr td.bg-warning {
                    background-color: rgba($brand-warning, 0.1);
                    border-bottom-color: $brand-warning !important;
                }
                tbody tr.bg-error td,
                tbody tr td.bg-error {
                    background-color: rgba($brand-danger, 0.1);
                    border-bottom-color: $brand-danger !important;
                }
                thead th.sliced {
                    border-left: 2px solid $c8-secondary !important;
                }
                tbody td.sliced {
                    border-left: 2px solid $c8-secondary !important;
                }
                thead th.sliced-grey {
                    border-left: 1px solid $c8-table-th-color !important;
                }
                tbody td.sliced-grey {
                    border-left: 1px solid $c8-table-th-color !important;
                }
            }
            table.bordered {
                border: 1px solid $c8-table-th-color !important;
            }
        }
    }
    .page-noty {
        div > a {
            color: $c8-font-color;
            text-decoration: none;
        }
        .warning-box {
            background: rgba($c8-warning, 0.4);
            border: 2px solid $c8-warning;
            padding: $c8-alert-pd;
            color: $c8-font-color;
            position: relative;
            margin: 1rem 0;
        }
        .primary-box {
            background: rgba($c8-primary, 0.4);
            border: 2px solid $c8-primary;
            padding: $c8-alert-pd;
            color: $c8-font-color;
            position: relative;
            margin: 1rem 0;
        }
        .error-box {
            background: rgba($c8-error, 0.4);
            border: 2px solid $c8-error;
            padding: $c8-alert-pd;
            color: $c8-font-color;
            position: relative;
            margin: 1rem 0;
        }
        .success-box {
            background: rgba($c8-success, 0.4);
            border: 2px solid $c8-success;
            padding: $c8-alert-pd;
            color: $c8-font-color;
            position: relative;
            margin: 1rem 0;
        }
    }
    &.v-dialog {
        color: $c8-font-color;
        p {
            color: $c8-font-color;
        }
        .page-noty {
            margin: 1rem 0.5rem;
            a {
                color: $c8-font-color;
                text-decoration: none;
            }
        }
    }
    // CLASSES
    .c8-btm-space {
        margin-bottom: 1rem !important;
    }
    .c8-top-space {
        margin-top: 1rem !important;
    }
    .c8-no-padding {
        padding: 0 !important;
    }
    .c8-padding-1 {
        padding: $c8-dflt-pd !important;
    }
    .c8-padding-hlf {
        padding: $c8-dflt-hlf !important;
    }
    .c8-on-left {
        float: left !important;
    }
    .c8-on-right {
        float: right !important;
    }
    .c8-weight-bold {
        font-weight: 600;
    }
    .c8-weight-light {
        font-weight: 400;
    }
    .c8-color-primary {
        color: $c8-primary;
    }
    .c8-color-warning {
        color: $c8-warning;
    }
    .c8-color-error {
        color: $c8-error;
    }
    .c8-color-success {
        color: $c8-success;
    }
    .c8-full-width {
        width: 100%;
    }
    .con-abs-br {
        position: fixed;
        bottom: 1.5rem;
        right: 1.5rem;
    }
    [data-tooltip]:after {
        color: #ffffff;
        background-color: rgba(0, 0, 0, 1);
        font-size: 1.2em !important;
        border: none !important;
        padding: 7px 8px !important;
        border-radius: 4px !important;
        z-index: 99;
    }
    [data-tooltip]:before {
        opacity: 1 !important;
        border-radius: 1px !important;
    }
    .c8-datatable-custom-footer {
        margin-left: auto;
        td {
            text-wrap: avoid;
            font-size: 0.75rem;
        }
    }
    .c8-datatable-custom {
        .v-data-table-header {
            th {
                background: #eaeaea;
            }
        }
        td {
            font-size: 11px !important;
        }
    }
    .c8-btn {
        color: $c8-btn-color;
        border: 1px solid $c8-btn-border;
        background-color: $c8-btn-bg !important;
        text-transform: none;
        font-weight: normal;
        font-size: calc(#{$c8-font-size} + 1px);
    }
}
// ALERT CUSTOM STYLE
#noty_layout__topRight {
    top: 90px;
    min-width: $c8-alert-min-width;
    max-width: $c8-alert-max-width;
    .noty_theme__mint.noty_bar {
        font-size: $c8-font-size + 1px;
        border: 0;
        border-radius: 0;
        margin: 5px 0;
        .noty_close_button {
            background-color: transparent;
        }
    }
    .noty_theme__mint.noty_type__success {
        background-color: $c8-success;
        box-shadow:
            0 0 0 1px rgba($c8-success, 1) inset,
            0 0 0 0 transparent;
        .noty_progressbar {
            background-color: #ffffff;
            height: 5px;
            opacity: 0.6;
        }
    }
    .noty_theme__mint.noty_type__error {
        background-color: $c8-error;
        box-shadow:
            0 0 0 1px rgba($c8-error, 1) inset,
            0 0 0 0 transparent;
        .noty_progressbar {
            background-color: #ffffff;
            height: 5px;
            opacity: 0.6;
        }
    }
    .noty_theme__mint.noty_type__warning {
        background-color: $c8-warning;
        box-shadow:
            0 0 0 1px rgba($c8-warning, 1) inset,
            0 0 0 0 transparent;
        .noty_progressbar {
            background-color: #ffffff;
            height: 5px;
            opacity: 0.6;
        }
    }
    .noty_theme__mint.noty_type__info {
        background-color: $c8-primary;
        box-shadow:
            0 0 0 1px rgba($c8-primary, 1) inset,
            0 0 0 0 transparent;
        .noty_progressbar {
            background-color: #ffffff;
            height: 5px;
            opacity: 0.6;
        }
    }
}
.c8-page input[type='radio'] {
    min-height: 0px;
}
