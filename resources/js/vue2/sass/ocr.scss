#main_container {
    height: auto !important;
    overflow-y: visible !important;
}

.comparison-dialog {
    overflow-y: visible !important;
}

#header a {
    height: auto !important;
}

#menuIconShowSideBarMenu2 {
    > .item {
        font-weight: normal !important;

        #menu-help {
            height: auto !important;
        }
    }
}

#submenuV2 {
    display: none;
}

#content {
    margin-left: 0 !important;
}

#header {
    #topmenu {
        ul.menu-left {
            li {
                a#logout {
                    cursor: pointer !important;
                }
            }
        }
    }
}

#ocrPageContainer {
    #app.c-vue {
        .v-application {
            .v-snack--top {
                top: 130px;
                z-index: 100;

                .v-icon.pointer {
                    position: absolute;
                    top: 4px;
                    right: 4px;
                }
            }
            .v-tooltip__content {
                background: rgba(97, 97, 97, 1);
                opacity: 1 !important;
            }
            .divider {
                padding: 0 5px;
            }
            .pointer {
                cursor: pointer;
            }
            .c-btn {
                text-transform: none;
                color: #8d8d8d;
                height: 24px;
                padding: 0 12px;
                border-radius: 2px;
                letter-spacing: normal;
                font-size: 12px;
                font-weight: 400;

                &:hover {
                    border: 1px solid #acacac;
                    background: #b3b3b3;
                    font-weight: normal;
                    color: #ffffff;
                }

                i.mdi-spin {
                    font-size: 18px;
                }
            }
            .c-label {
                color: white;
                padding: 5px;
                font-size: 12px;
                border-radius: 5px !important;
                margin: 0 !important;
                height: 26px;

                &.danger {
                    background: #f44336;
                }

                &.success {
                    background: #4caf50;
                }
            }
            .modal-container {
                padding: 3px;
                width: 100% !important;

                .v-card__title {
                    background: #3489a1 !important;
                    color: white;
                    font-size: 26px;
                    font-weight: 700;
                }
                .v-card__text {
                    padding: 16px;
                    h5 {
                        color: #757575;
                        font-size: 12px;
                    }
                    table {
                        width: 100%;
                        font-size: 11px;
                        border: none;
                        border-spacing: 0;
                        border-collapse: separate;
                        overflow: auto;

                        tr.header {
                            background: #dedede;
                            color: #757575;
                            > td {
                                padding: 8px;
                                height: 32px;
                                font-size: 11px;
                            }
                        }

                        tbody {
                            > tr > td {
                                padding: 8px;
                                height: 25px;
                            }
                        }

                        &.previous-invoices-table {
                            tr.header {
                                td {
                                    &:nth-child(3) {
                                        width: 80px;
                                    }
                                    // &:nth-child(6) {
                                    //     width: 150px;
                                    // }
                                    &:nth-child(10) {
                                        width: 80px;
                                    }
                                    &:last-child {
                                        width: 75px;
                                    }
                                }
                            }

                            tbody {
                                tr {
                                    &:hover {
                                        background: #e0ffff;
                                    }
                                }
                            }
                        }
                    }

                    .ar-container {
                        // padding-bottom: 50px;
                        min-height: 500px;

                        table.table-ar {
                            font-size: 12px;
                            padding-bottom: 20px;

                            tr {
                                td {
                                    padding: 10px 5px;
                                    border-bottom: none !important;

                                    &.form-label {
                                        padding: 10px 25px 10px 5px;
                                        color: #666666;
                                        text-align: right;
                                        width: 170px;

                                        + td {
                                            div.inner {
                                                width: 300px;
                                                position: relative;
                                            }
                                        }
                                    }

                                    input.v-input {
                                        width: 100%;
                                        height: 28px;
                                        border: 1px solid #aaaaaa !important;
                                        box-shadow: none !important;

                                        &[disabled] {
                                            background: #ececec !important;
                                        }
                                    }

                                    textarea.v-textarea {
                                        background: none;
                                        height: 30px;
                                        min-width: 100%;
                                        border: 1px solid rgb(170, 170, 170) !important;
                                        box-shadow: none !important;
                                        padding-bottom: 15px;
                                    }

                                    p.character-counter {
                                        position: absolute;
                                        bottom: 5px;
                                        left: 10px;
                                        font-size: 10px;
                                        color: #8d8d8d;
                                        margin-bottom: 0;
                                    }
                                }
                            }

                            &.pm-view {
                                tr {
                                    td {
                                        &.form-label {
                                            width: 180px;
                                        }
                                        &.c-datepicker {
                                            .has-error {
                                                .date-picker-input-field {
                                                    .v-input__control {
                                                        .v-input__slot {
                                                            border: 1px solid #f44336 !important;

                                                            &:before {
                                                                border-color: #f44336 !important;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        &.has-checkbox {
                                            input {
                                                vertical-align: text-bottom;
                                            }
                                            strong {
                                                margin-left: 5px;
                                            }
                                        }
                                        .c-multiselect.error {
                                            border: 1px solid #f44336 !important;

                                            .multiselect__tags {
                                                border: none !important;
                                            }
                                        }
                                        textarea.error {
                                            border: 1px solid #f44336 !important;
                                        }
                                        .error-message {
                                            margin-top: 5px;
                                            color: #f44336;
                                            font-size: 11px;
                                        }
                                    }
                                }
                            }
                        }

                        .tooltip {
                            .tooltiptext {
                                padding: 5px !important;
                                /* background: rgba(171, 205, 239, 0.6) */ /* Blue background with 60% opacity */
                            }

                            .mgmt-fee-tooltip {
                                cursor: pointer;
                            }
                        }

                        table.table-ar-lease {
                            padding: 20px 0;
                            border-top: 1px solid #6bc9ff;
                            border-bottom: 1px solid #6bc9ff;

                            thead {
                                font-weight: bold;
                                background: #eaeaea;
                                line-height: 25px;

                                tr {
                                    > td {
                                        padding: 6px 10px;

                                        &.apply-header {
                                            width: 52px;

                                            .tooltip {
                                                i {
                                                    font-size: 20px;
                                                    cursor: pointer;
                                                }

                                                .vue-tooltiptext {
                                                    top: -30%;
                                                    margin-left: 15px;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            tbody {
                                tr {
                                    height: 40px;
                                    > td {
                                        padding: 6px 10px;

                                        &.apply-group {
                                            width: 52px;
                                        }
                                        &.lease-code {
                                            width: 44px;

                                            .lease-success {
                                                background: none !important;
                                                border: none !important;
                                                color: #4caf50;
                                            }

                                            .tooltip {
                                                border: none !important;

                                                .tooltiptext {
                                                    background: #616161;
                                                    opacity: 1 !important;
                                                    color: #fff;
                                                    border-radius: 4px;
                                                    font-size: 14px;
                                                    line-height: 22px;
                                                    display: inline-block;
                                                    padding: 5px 16px;
                                                    position: absolute;
                                                    text-transform: none;
                                                    width: 210px;
                                                    pointer-events: none;
                                                    box-shadow: none !important;
                                                    border: none !important;
                                                    z-index: 2;
                                                    top: -5px;
                                                    left: 105%;
                                                    bottom: -3px;
                                                    margin-left: 0;
                                                }

                                                .mgmt-fee-tooltip {
                                                    cursor: pointer;
                                                }
                                            }
                                        }
                                        &.amount-group {
                                            width: 75px;
                                        }
                                        input.v-input {
                                            width: 100%;
                                            border: 1px solid #aaaaaa !important;
                                            box-shadow: none !important;

                                            &[disabled] {
                                                background: #ececec !important;
                                            }
                                        }
                                        &.remove-group {
                                            text-align: right;

                                            .remove-rows {
                                                color: #c50c0c;
                                                cursor: pointer;
                                            }
                                        }
                                    }
                                }
                            }
                            tfoot {
                                font-weight: bold;
                                background: #e6f3f9;
                                line-height: 25px;

                                tr {
                                    > td {
                                        padding: 6px 10px;
                                    }
                                }
                            }
                        }
                        .v-btn-toggle {
                            .v-btn {
                                border-radius: 0 !important;
                                font-size: 11px;
                                padding: 4px 11px;
                                border: none !important;
                                height: 26px !important;
                                text-transform: none;
                                letter-spacing: normal;
                            }
                        }
                        .date-picker-component {
                            position: relative;
                        }
                        .date-picker-input-field {
                            width: 100% !important;

                            .v-input__control {
                                .v-input__slot {
                                    border: 1px solid #aaaaaa !important;
                                    padding-left: 5px;

                                    &:before {
                                        border-color: #aaaaaa !important;
                                    }
                                    .v-text-field__slot {
                                        input {
                                            border: none !important;
                                            box-shadow: none !important;
                                        }
                                    }

                                    div.v-menu__content {
                                        min-width: 195px !important;
                                        width: 195px !important;
                                        right: 0 !important;
                                        left: unset !important;
                                    }
                                }
                            }
                        }
                        .c-multiselect {
                            min-height: 28px !important;

                            .multiselect__select {
                                top: 5px;
                                right: -4px;
                                z-index: 2 !important;
                            }
                            .multiselect__tags {
                                border: 1px solid #aaaaaa !important;
                                min-height: 28px !important;
                                padding-left: 5px !important;

                                .multiselect__placeholder {
                                    padding-top: 7px !important;
                                    padding-left: 4px !important;
                                }
                                .multiselect__single {
                                    padding-top: 4px !important;
                                    overflow: hidden;
                                    white-space: nowrap;
                                    text-overflow: ellipsis;
                                    width: calc(100% - 30px);
                                }
                            }
                            .multiselect__content {
                                width: 100%;
                                .multiselect__element {
                                    .multiselect__option {
                                        white-space: initial;
                                        .option__desc {
                                            .option__title {
                                                white-space: initial;
                                            }
                                        }
                                    }
                                }
                            }
                            .multiselect__content-wrapper {
                                max-height: 200px !important;
                            }
                            &.multiselect--active {
                                .multiselect__select {
                                    top: 0;
                                    height: 28px !important;
                                }
                                .multiselect__tags {
                                    input.multiselect__input {
                                        padding-left: 5px !important;
                                        margin-bottom: 0 !important;
                                        height: 26px !important;
                                    }
                                }
                            }
                            &.multiselect--disabled {
                                .multiselect__select {
                                    background: none;
                                }
                            }
                        }
                    }

                    .supplier-recon-table {
                        .v-data-table__wrapper {
                            .v-data-table-header {
                                tr {
                                    th {
                                        &:first-child {
                                            width: 107px;
                                        }
                                        &:nth-child(3) {
                                            width: 92px;
                                        }
                                        &:nth-child(7) {
                                            width: 108px;
                                        }
                                    }
                                }
                            }
                            tbody {
                                tr {
                                    td {
                                        &:nth-child(9),
                                        &:nth-child(10),
                                        &:nth-child(11) {
                                            text-align: right !important;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .v-data-table.lease-variable-outgoing-notes {
                        .v-data-table__wrapper {
                            table {
                                tbody {
                                    tr > td {
                                        &:nth-child(4) {
                                            width: 100px;
                                        }
                                        &:last-child {
                                            width: 150px;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .v-data-table.account-code-budget {
                        .v-data-table__wrapper {
                            table {
                                tbody {
                                    tr > td {
                                        &:nth-child(5),
                                        &:nth-child(6),
                                        &:nth-child(7),
                                        &:nth-child(8),
                                        &:nth-child(9),
                                        &:nth-child(10) {
                                            text-align: right !important;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    &#workOrdersLookupContainer {
                        min-height: 300px;

                        #workOrdersLookupHeader {
                            -webkit-box-align: center;
                            align-items: center;
                            display: -webkit-box;
                            display: flex;
                            flex-wrap: wrap;
                            font-size: 12px;
                            margin-bottom: 10px;

                            span {
                                padding-right: 10px;
                            }

                            .dropdown-200 {
                                width: 300px !important;
                            }

                            .v-input.search {
                                padding: 0;
                                margin: 0;
                            }

                            #workOrderSearch {
                                box-shadow: none !important;
                                border: none !important;
                            }
                        }

                        #workOrdersLookupDataTableContainer {
                            table tbody > tr > td {
                                &:first-child {
                                    width: 70px;
                                }

                                &:nth-child(2) {
                                    width: 150px;
                                }

                                &:nth-child(3) {
                                    width: 125px;
                                }

                                &:nth-child(6) {
                                    text-align: right !important;
                                }

                                &:nth-child(8) {
                                    width: 229px;

                                    span {
                                        line-height: 23px;
                                        word-break: break-all;
                                    }
                                }
                            }
                        }
                    }

                    #workOrderDetailContainer {
                        font-family:
                            Roboto,
                            -apple-system,
                            Helvetica Neue,
                            Helvetica,
                            Arial,
                            sans-serif;

                        .text-color-body {
                            color: #666;
                        }

                        .text-color-gray {
                            color: #999;
                        }

                        #workOrderHeader {
                            .header-inner {
                                padding: 0 12px 24px;
                                position: relative;

                                .header-text {
                                    font-size: 20px;
                                    font-weight: 500;
                                    line-height: 1.12;
                                    letter-spacing: 0.02em;
                                    margin-bottom: 8px;
                                }

                                #workorderFMLinks {
                                    margin: 0 0 5px;

                                    span.spacer {
                                        margin: 0 5px;
                                    }
                                }
                            }

                            #workorderHeaderFloat {
                                position: absolute;
                                top: 16px;
                                right: 24px;

                                span.label {
                                    color: #ffffff;
                                    text-transform: uppercase;
                                    padding: 5px;
                                    margin-left: 5px;

                                    &.neutral {
                                        background: #e0e1e2 !important;
                                    }

                                    &.positive {
                                        background: #21ba45 !important;
                                    }

                                    &.negative {
                                        background: #db2828 !important;
                                    }

                                    &.info {
                                        background: #31ccec !important;
                                    }

                                    &.warning {
                                        background: #f2c037 !important;
                                    }
                                }
                            }
                        }

                        .text-medium {
                            font-size: 13px;
                            font-weight: 500;
                            margin-bottom: 5px;
                            line-height: 1.2;
                        }

                        .text-small {
                            font-size: 12px;
                            font-weight: 400;
                            margin-bottom: 5px;
                            line-height: 1.2;
                        }

                        .text-smaller {
                            font-size: 11px;
                            line-height: 1.2;
                            font-weight: 400;
                        }

                        .no-pad {
                            padding: 0;
                        }

                        #workOrderSubHeader {
                            .content-holder {
                                .col {
                                    padding: 0 12px;
                                }

                                .content-inner {
                                    padding: 8px 12px;

                                    .subcontent-category {
                                        font-weight: 500;
                                        margin-bottom: 5px;
                                        line-height: 1.2;
                                    }

                                    .content-category {
                                        margin-bottom: 5px;
                                    }

                                    .content-subheader {
                                        font-size: 16px;
                                        font-weight: 500;
                                        margin-bottom: 5px;
                                    }

                                    .horizontal {
                                        font-size: 12px;
                                        line-height: 1.4;

                                        .label {
                                            float: left;
                                            width: 25%;
                                            clear: left;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                            white-space: nowrap;
                                        }

                                        .value {
                                            margin-left: 30%;
                                        }
                                    }

                                    .notes-holder {
                                        margin-top: 5px;
                                        line-height: 1.2;

                                        p {
                                            margin: 0;

                                            &.label {
                                                font-weight: 500;
                                            }

                                            &.value {
                                                white-space: pre-line;
                                                margin-top: 3px;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        #workOrderDataTableContainer {
                            padding-top: 16px;

                            table tbody > tr > td {
                                &:last-child {
                                    text-align: right !important;
                                }
                            }
                        }

                        #workOrderFooter {
                            margin-top: -6px;

                            .comment {
                                border: 1px solid #ddd;
                                margin: 6px 12px;
                                padding: 12px;
                                min-height: 125px;

                                .comment-inner {
                                    padding: 6px 12px;

                                    .comment-label {
                                        margin-bottom: 5px;
                                    }

                                    .comment-content {
                                        white-space: pre-line;
                                    }
                                }

                                .horizontal {
                                    font-size: 15px;
                                    line-height: 1.4;

                                    .label {
                                        width: 50%;
                                        text-align: left;
                                        font-weight: 400;
                                        margin-bottom: 5px;

                                        float: left;
                                        clear: left;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                        white-space: nowrap;
                                    }

                                    .amount {
                                        margin-left: 50%;
                                        margin-bottom: 5px;
                                    }

                                    .total {
                                        font-weight: 600;
                                        font-size: 17px;
                                    }
                                }
                            }

                            .no-pad-top {
                                padding-top: 0;
                            }

                            #additionalDetailsContainer {
                                padding: 0 6px 0 12px;

                                .comment {
                                    margin: 6px 3px 3px 12px;
                                }
                            }

                            #computedTotalContainer {
                                padding: 0 12px 0 6px;

                                .comment {
                                    margin: 6px 6px 3px 3px;
                                }
                            }

                            #buildingNotesContainer {
                                padding: 0 6px 6px 12px;

                                .comment {
                                    margin: 3px 3px 6px 12px;
                                }
                            }
                        }

                        #workOrderMisc {
                            margin-top: -12px;

                            .v-tabs {
                                .v-tabs-bar {
                                    background: #006480;
                                    color: #fff !important;

                                    .v-tab:not(.v-tab--active) {
                                        color: #fff !important;
                                        opacity: 0.7;
                                    }
                                }
                            }

                            #commentContainer {
                                max-width: 1000px;
                                margin: 0 auto;
                                padding: 12px 0 0;

                                #commentList {
                                    border: 1px solid #e0e0e0;
                                    padding: 8px 0;

                                    .comment {
                                        position: relative;
                                        display: flex;
                                        -webkit-box-align: center;
                                        align-items: center;
                                        font-size: 12px;
                                        text-align: left;
                                        padding: 8px 16px;
                                        min-height: 40px;
                                        border-bottom: 1px solid #e0e0e0;

                                        &:last-child {
                                            border-bottom: none;
                                        }

                                        .comment-item {
                                            -webkit-box-flex: 1;
                                            flex: 1 1 auto;
                                            min-width: 0;

                                            .comment-label {
                                                font-size: 13px;
                                                margin-bottom: 16px;
                                                white-space: pre-line;
                                                line-height: 1.2;
                                            }

                                            .comment-details {
                                                color: #757575;
                                                font-size: 90%;
                                                margin-top: 4px;
                                                line-height: 1.3;

                                                span {
                                                    font-weight: 500;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .v-card__actions {
                    button {
                        background: #e1e1e1 !important;
                        color: #8d8d8d !important;
                        letter-spacing: normal;
                        box-shadow: none;
                        text-transform: none;

                        &.error {
                            background-color: #db2828 !important;
                            color: #ffffff !important;
                        }

                        &.success {
                            background-color: #4caf50 !important;
                            color: #ffffff !important;
                        }

                        &.warning {
                            background-color: #fb8c00 !important;
                            color: #ffffff !important;
                        }

                        &.default {
                            background-color: #00baf2 !important;
                            color: #ffffff !important;
                        }
                    }
                }

                .v-data-table__wrapper {
                    td {
                        font-size: 11px !important;

                        &.text-start {
                            span.label {
                                color: #ffffff;
                                text-transform: uppercase;
                                padding: 5px;

                                &.neutral {
                                    background: #e0e1e2;
                                }

                                &.positive {
                                    background: #21ba45;
                                }

                                &.negative {
                                    background: #db2828;
                                }

                                &.info {
                                    background: #31ccec;
                                }

                                &.warning {
                                    background: #f2c037;
                                }
                            }
                        }
                    }
                    .v-data-table-header {
                        tr {
                            background: #dedede;
                            color: #757575;

                            th {
                                padding: 8px;
                                height: 32px;
                                font-size: 11px;
                            }
                        }
                    }
                }

                .v-data-footer {
                    .v-data-footer__select {
                        display: none;
                    }
                }

                &.amount-lookup {
                    h4 {
                        margin-bottom: 15px;
                    }
                    table {
                        font-size: 12px;
                        color: #666666;
                        text-align: right;

                        tr {
                            td {
                                padding: 10px 5px;
                                border-bottom: none !important;

                                &.form-label {
                                    width: 120px;
                                    text-align: right;
                                    padding-right: 10px;
                                }

                                input.v-input {
                                    width: 100%;
                                    height: 28px;
                                    border: 1px solid #aaaaaa !important;
                                    box-shadow: none !important;
                                }
                            }
                        }
                    }
                    .c-multiselect {
                        min-height: 28px !important;

                        .multiselect__select {
                            top: 5px;
                            right: -4px;
                            z-index: 2 !important;
                        }
                        .multiselect__tags {
                            border: 1px solid #aaaaaa !important;
                            min-height: 28px !important;
                            padding-left: 5px !important;

                            .multiselect__placeholder {
                                padding-top: 6px !important;
                            }
                            .multiselect__single {
                                padding-top: 5px !important;
                                overflow: hidden;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                width: calc(100% - 30px);
                            }
                        }
                        .multiselect__content {
                            width: 100%;
                            .multiselect__element {
                                .multiselect__option {
                                    white-space: initial;
                                    .option__desc {
                                        .option__title {
                                            white-space: initial;
                                        }
                                    }
                                }
                            }
                        }
                        .multiselect__content-wrapper {
                            max-height: 200px !important;
                        }
                        &.multiselect--active {
                            .multiselect__select {
                                top: 0;
                                height: 28px !important;
                            }
                            .multiselect__tags {
                                input.multiselect__input {
                                    padding-left: 3px !important;
                                    margin-bottom: 0 !important;
                                    height: 26px !important;
                                }
                            }
                        }
                        &.multiselect--disabled {
                            .multiselect__select {
                                background: none;
                            }
                        }
                    }
                    .search-button-container {
                        padding: 20px 0 80px;
                    }
                    .search-results-container {
                        padding-left: 40px;
                        .v-input.search {
                            margin: 0 0 8px;
                            padding: 0;

                            .v-input__control {
                                .v-input__slot {
                                    .v-text-field__slot {
                                        .v-label {
                                            z-index: 1;
                                            font-size: 12px;
                                            top: 3px;
                                        }
                                        input {
                                            box-shadow: none !important;
                                            border: none !important;
                                            padding: 0 !important;
                                        }
                                    }
                                }
                            }
                        }
                        .v-data-table.property-code-data-table {
                            .v-data-table__wrapper {
                                table {
                                    tbody > tr {
                                        &:nth-of-type(even) {
                                            background-color: rgba(0, 0, 0, 0.03);
                                        }
                                        td {
                                            font-size: 12px !important;
                                        }
                                    }
                                }
                            }
                            .v-data-footer__select {
                                display: flex;

                                .v-input {
                                    .v-input__control {
                                        .v-input__slot {
                                            .v-select__slot {
                                                input {
                                                    display: none;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                &.property-code-lookup {
                    h4 {
                        margin-bottom: 15px;
                    }
                    table {
                        font-size: 12px;
                        color: #666666;
                        text-align: right;

                        tr {
                            td {
                                padding: 10px 5px;
                                border-bottom: none !important;

                                &.form-label {
                                    width: 120px;
                                    text-align: right;
                                    padding-right: 10px;
                                }

                                input.v-input {
                                    width: 100%;
                                    height: 28px;
                                    border: 1px solid #aaaaaa !important;
                                    box-shadow: none !important;
                                }
                            }
                        }
                    }
                    .c-multiselect {
                        min-height: 28px !important;

                        .multiselect__select {
                            top: 5px;
                            right: -4px;
                            z-index: 2 !important;
                        }
                        .multiselect__tags {
                            border: 1px solid #aaaaaa !important;
                            min-height: 28px !important;
                            padding-left: 5px !important;

                            .multiselect__placeholder {
                                padding-top: 6px !important;
                            }
                            .multiselect__single {
                                padding-top: 5px !important;
                                overflow: hidden;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                width: calc(100% - 30px);
                            }
                        }
                        .multiselect__content {
                            width: 100%;
                            .multiselect__element {
                                .multiselect__option {
                                    white-space: initial;
                                    .option__desc {
                                        .option__title {
                                            white-space: initial;
                                        }
                                    }
                                }
                            }
                        }
                        .multiselect__content-wrapper {
                            max-height: 200px !important;
                        }
                        &.multiselect--active {
                            .multiselect__select {
                                top: 0;
                                height: 28px !important;
                            }
                            .multiselect__tags {
                                input.multiselect__input {
                                    padding-left: 3px !important;
                                    margin-bottom: 0 !important;
                                    height: 26px !important;
                                }
                            }
                        }
                        &.multiselect--disabled {
                            .multiselect__select {
                                background: none;
                            }
                        }
                    }
                    .search-button-container {
                        padding: 20px 0 80px;
                    }
                    .search-results-container {
                        padding-left: 40px;
                        .v-input.search {
                            margin: 0 0 8px;
                            padding: 0;

                            .v-input__control {
                                .v-input__slot {
                                    .v-text-field__slot {
                                        .v-label {
                                            z-index: 1;
                                            font-size: 12px;
                                            top: 3px;
                                        }
                                        input {
                                            box-shadow: none !important;
                                            border: none !important;
                                            padding: 0 !important;
                                        }
                                    }
                                }
                            }
                        }
                        .v-data-table.property-code-data-table {
                            .v-data-table__wrapper {
                                table {
                                    tbody > tr {
                                        &:nth-of-type(even) {
                                            background-color: rgba(0, 0, 0, 0.03);
                                        }
                                        td {
                                            font-size: 12px !important;
                                        }
                                    }
                                }
                            }
                            .v-data-footer__select {
                                display: flex;

                                .v-input {
                                    .v-input__control {
                                        .v-input__slot {
                                            .v-select__slot {
                                                input {
                                                    display: none;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .v-dialog__content {
                // align-items: flex-start;
                // margin-top: 10vh;
            }
            .v-page-loader {
                padding-top: 15%;
                padding-bottom: 15%;
                text-align: center;

                img {
                    width: 250px;
                }

                .v-loader {
                    position: relative;
                    vertical-align: middle;
                    margin: 0em;
                    left: 0em;
                    top: 0em;
                    -webkit-transform: none;
                    transform: none;
                    display: inline-block;

                    &::before {
                        position: absolute;
                        content: '';
                        top: 0%;
                        left: 50%;
                        // width: 100%;
                        // height: 100%;
                        border-radius: 500rem;
                        border: 0.2em solid rgba(0, 0, 0, 0.1);

                        width: 2.28571429rem;
                        height: 2.28571429rem;
                        margin: 0em 0em 0em -1.14285714rem;
                    }

                    &::after {
                        position: absolute;
                        content: '';
                        top: 0%;
                        left: 50%;
                        width: 2.28571429rem;
                        height: 2.28571429rem;
                        margin: 0em 0em 0em -1.14285714rem;
                        -webkit-animation: loader-ui 0.6s linear;
                        animation: loader-ui 0.6s linear;
                        -webkit-animation-iteration-count: infinite;
                        animation-iteration-count: infinite;
                        border-radius: 500rem;
                        border-color: #767676 transparent transparent;
                        border-style: solid;
                        border-width: 0.2em;
                        box-shadow: 0px 0px 0px 1px transparent;
                    }

                    @-webkit-keyframes loader-ui {
                        from {
                            -webkit-transform: rotate(0deg);
                            transform: rotate(0deg);
                        }

                        to {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }
                    }

                    @keyframes loader-ui {
                        from {
                            -webkit-transform: rotate(0deg);
                            transform: rotate(0deg);
                        }

                        to {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }
                    }
                }
            }
            .accent {
                background-color: #00baf2 !important;
                border-color: #00baf2 !important;
            }
            .accent--text {
                color: #00baf2 !important;
                caret-color: #00baf2 !important;
            }
            .v-picker--date {
                width: 195px !important;

                .v-picker__body {
                    width: 100% !important;

                    .v-date-picker-header {
                        padding: 2.5px 5px;

                        .v-btn--fab.v-size--default {
                            height: 16px;
                            width: 16px;

                            .v-icon {
                                height: 16px;
                                font-size: 16px;
                                width: 16px;
                            }
                        }

                        .accent--text {
                            button {
                                font-size: 12px;
                                padding: 2px 0;
                            }
                        }

                        .v-date-picker-header__value {
                            pointer-events: none;
                        }
                    }
                    .v-date-picker-table {
                        padding: 0 2.5px;

                        table {
                            margin: 0 0 5px;

                            thead {
                                background: none !important;
                                line-height: normal !important;

                                tr {
                                    height: 24.8px !important;

                                    th {
                                        font-size: 10px;
                                        padding: 7px 3px;
                                    }
                                }
                            }

                            tbody {
                                tr {
                                    height: 16.8px !important;

                                    td {
                                        width: 26px !important;
                                        height: 17px !important;
                                        padding: 0 !important;
                                        border: none !important;

                                        button {
                                            padding: 2px;
                                            width: 16px;
                                            height: 16px;

                                            .v-btn__content {
                                                font-size: 10px;
                                                line-height: 12px;
                                                letter-spacing: normal;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .vertical-top {
                vertical-align: top;
            }
            .tooltip {
                border: none !important;

                &:hover {
                    .vue-tooltiptext {
                        visibility: visible;
                        opacity: 1;
                    }
                }

                .vue-tooltiptext {
                    visibility: hidden;
                    position: absolute;
                    width: 120px;
                    background-color: #555;
                    color: #fff;
                    text-align: center;
                    padding: 5px 16px;
                    border-radius: 6px;
                    z-index: 1;
                    opacity: 0;
                    transition: opacity 0.6s;
                    border-radius: 4px;
                    font-size: 14px;
                    line-height: 22px;
                    font-weight: normal;
                    z-index: 20;

                    &.sm {
                        width: 100px;

                        &.bottom {
                            margin-left: -50px;
                        }
                    }

                    &.md {
                        width: 180px;

                        &.bottom {
                            margin-left: -90px;
                        }
                    }

                    &.lg {
                        width: 240px;

                        &.bottom {
                            margin-left: -120px;

                            &.bottom-left {
                                margin-left: -40px;
                                top: 140%;

                                &.property-notes-tooltip {
                                    margin-left: -105px;
                                }
                            }
                        }
                    }

                    &.xl {
                        width: 260px;

                        &.bottom {
                            margin-left: -130px;

                            &.bottom-left {
                                margin-left: -75px;
                                top: 140%;
                            }
                        }
                    }

                    &.bottom {
                        top: 153%;
                        left: 50%;
                        margin-left: -60px;
                    }
                }
            }
        }
        .ocr-pages {
            .subheader-container {
                font-size: 12px;
                padding: 0 16px;
            }
            .v-data-table__wrapper {
                .v-data-table-header {
                    background: #45a7c3;

                    tr > th {
                        text-align: center !important;
                        color: #ffffff;

                        &.sortable {
                            .v-data-table-header__icon {
                                color: white;
                                margin-top: -5px;
                            }
                        }
                    }
                }
                tbody {
                    tr > td {
                        font-size: 11px;
                        text-align: center !important;

                        &.dt_tbl_row_orangered {
                            //background: rgba(128, 0, 0, 0.5);
                            background: #800001;
                            color: #ffffff;
                            //// background: rgba(255, 69, 0, 0.3);
                        }
                        &.dt_tbl_row_lightsalmon {
                            background: #ff4500;
                            color: #ffffff;
                            //background: rgba(255, 69, 0, 0.3);
                            //// background: rgba(255, 160, 122, 0.3);
                        }
                        &.dt_tbl_row_yellow {
                            //background: rgba(255, 255, 0, 0.3);
                            background: #ffff00;
                        }
                        &.dt_tbl_row_greenyellow {
                            background: #228b22;
                            color: #ffffff;
                            //background: rgba(34, 139, 34, 0.3);
                        }
                        &.dt_tbl_row_default {
                            background: rgba(228, 241, 254, 1);
                        }

                        &.icon-start {
                            span.icon {
                                i {
                                    font-size: 18px !important;
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                    tr.v-data-table__empty-wrapper {
                        td {
                            font-size: 12px;
                            color: #000000;
                        }
                    }
                }
            }
            .v-data-footer {
                .v-data-footer__select {
                    .v-select {
                        // min-width: 50px;
                        .v-input__control {
                            .v-input__slot {
                                .v-select__slot {
                                    .v-select__selections {
                                        input {
                                            box-shadow: none !important;
                                            border: none !important;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .v-input.search {
                padding-top: 0;
                margin-top: 0;
                flex: 0.05 auto;
                .v-input__control {
                    .v-input__slot {
                        .v-text-field__slot {
                            label.v-label {
                                z-index: 1;
                                font-size: 12px;
                            }
                            input {
                                box-shadow: none !important;
                                border: none !important;
                                padding: 0 !important;
                            }
                        }
                    }
                }
            }
            .dropdown.v-select {
                ul.dropdown-menu {
                    li {
                        padding: 0 5px;
                    }

                    .active a {
                        background: none;
                        color: #000000 !important;
                        font-weight: normal;
                    }

                    .highlight a {
                        background: #00baf2;
                        color: #ffffff !important;
                    }
                }

                &[name='portfolioManagerSelect'] {
                    width: 250 !important;
                }
            }
            .c-multiselect.dropdown-250 {
                width: 250px !important;
            }
            .date-picker-component {
                .v-menu__content {
                    transform: none;
                    transition: none;
                    // top: 40px !important;
                }
            }

            .date-picker-input-field {
                width: 300px !important;
                border: 1px solid #aaaaaa;
                border-bottom: none;

                > .v-input__control > .v-input__slot > div.v-menu__content {
                    min-width: 195px !important;
                    width: 195px !important;
                    right: 0 !important;
                    left: unset !important;
                }
            }

            &.ocr-dashboard {
                .v-card__title#testDatabaseList {
                    margin-top: 60px;
                    color: #ff0000;
                }
                .v-data-table__wrapper {
                    .v-data-table-header {
                        tr > th {
                            &:first-child {
                                width: 25%;
                            }
                            &:nth-child(2) {
                                background: rgb(128, 0, 0.6);
                                // background: #ff4500;
                            }
                            &:nth-child(3) {
                                background: #ff4500;
                                // background: #ffa07a;
                            }
                            &:nth-child(4) {
                                background: #ffff00;
                                color: #a9a9a9;

                                .v-data-table-header__icon {
                                    color: #a9a9a9;
                                }
                            }
                            &:nth-child(5) {
                                background: #228b22;
                            }
                        }
                    }
                }
            }
            &.ocr-list {
                .subheader-container {
                    justify-content: space-between;

                    div:first-child {
                        line-height: 24px;
                    }
                }

                #statusSelectContainer {
                    font-size: 12px;
                    padding-top: 30px;

                    span {
                        padding-right: 10px;

                        &:nth-of-type(2) {
                            padding-left: 40px;
                        }
                    }

                    #auditTrailDatePickersContainer {
                        display: flex;
                        margin-top: 5px;

                        span:nth-of-type(1) {
                            width: 91.77;
                        }

                        span:nth-of-type(2) {
                            width: 137.88;
                        }

                        #auditTrailDateFrom,
                        #auditTrailDateTo {
                            position: relative;
                        }
                    }

                    /* For date range in search filter */
                    #aiListDatePickersContainer {
                        display: flex;
                        margin-top: 5px;

                        span {
                            position: relative;
                            top: -3px;
                        }

                        span:nth-of-type(1) {
                            width: 71.77;
                        }

                        span:nth-of-type(2) {
                            //width: 110.88;
                            width: 94.88;
                        }

                        button.range-search-btn {
                            margin-left: 10px;
                        }

                        .date-picker-input-field {
                            width: 250px !important;

                            .v-input__control {
                                .v-input__slot {
                                    .v-text-field__slot {
                                        input {
                                            border: none !important;
                                            box-shadow: none !important;
                                            padding-left: 5px;
                                        }
                                    }
                                }
                            }
                        }

                        .ai-list-date-picker {
                            max-width: 150px;

                            .v-input {
                                position: relative;
                                top: 1px;
                                border: 1px solid #e8e8e8;
                            }

                            .v-input__slot {
                                max-height: 23px;
                            }

                            .v-input__slot:before {
                                border: none;
                            }

                            .v-input__append-inner {
                                margin-top: 0px !important;
                            }

                            button.v-icon {
                                font-size: 18px;
                            }
                        }

                        #pmDateRangeFrom,
                        #pmDateRangeTo {
                            position: relative;
                        }
                    }

                    #aiOperatorListDatePickersContainer {
                        display: flex;
                        margin-top: 5px;
                        padding-left: 10px;

                        span {
                            position: relative;
                            top: -3px;
                        }

                        span:nth-of-type(1) {
                            width: 71.77;
                        }

                        span:nth-of-type(2) {
                            //width: 110.88;
                            width: 94.88;
                        }

                        button.range-search-btn {
                            margin-left: 10px;
                        }

                        .date-picker-input-field {
                            width: 250px !important;

                            .v-input__control {
                                .v-input__slot {
                                    .v-text-field__slot {
                                        input {
                                            border: none !important;
                                            box-shadow: none !important;
                                            padding-left: 5px;
                                        }
                                    }
                                }
                            }
                        }

                        .ai-list-date-picker {
                            max-width: 150px;

                            .v-input {
                                position: relative;
                                top: 1px;
                                border: 1px solid #e8e8e8;
                            }

                            .v-input__slot {
                                max-height: 23px;
                            }

                            .v-input__slot:before {
                                border: none;
                            }

                            .v-input__append-inner {
                                margin-top: 0px !important;
                            }

                            button.v-icon {
                                font-size: 18px;
                            }
                        }

                        #aiOpDateRangeFrom,
                        #aiOpDateRangeTo {
                            position: relative;
                        }
                    }
                }
            }
            &.ocr-entry {
                .c-multiselect {
                    min-height: 28px !important;

                    .multiselect__select {
                        top: 5px;
                        right: -4px;
                        z-index: 2 !important;
                    }
                    .multiselect__tags {
                        border: 1px solid #aaaaaa !important;
                        min-height: 28px !important;
                        padding-left: 5px !important;

                        .multiselect__placeholder {
                            padding-top: 6px !important;
                        }
                        .multiselect__single {
                            padding-top: 5px !important;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            width: calc(100% - 30px);
                        }
                    }
                    .multiselect__content {
                        width: 100%;
                        .multiselect__element {
                            .multiselect__option {
                                white-space: initial;
                                .option__desc {
                                    .option__title {
                                        white-space: initial;
                                    }
                                }
                            }
                        }
                    }
                    .multiselect__content-wrapper {
                        max-height: 200px !important;
                        bottom: unset !important;
                    }
                    &.multiselect--active {
                        .multiselect__select {
                            top: 0;
                            height: 28px !important;
                        }
                        .multiselect__tags {
                            input.multiselect__input {
                                padding-left: 5px !important;
                                margin-bottom: 0 !important;
                                height: 26px !important;
                            }
                        }
                    }
                    &.multiselect--disabled {
                        .multiselect__select {
                            background: none;
                        }
                    }

                    &.error {
                        .multiselect__tags {
                            border: 1px solid #f44336 !important;
                        }
                    }
                }
                input.c-input {
                    width: 100%;
                    height: 28px;
                    border: 1px solid #aaaaaa !important;
                    box-shadow: none !important;

                    &[disabled] {
                        background: #ececec !important;
                    }

                    &.error {
                        border: 1px solid #f44336 !important;
                        background: #fff !important;
                    }
                }
                .v-input.date-picker-input-field {
                    .v-text-field__slot {
                        input {
                            border: none !important;
                            box-shadow: none !important;
                            padding: 8px;
                        }
                    }
                }
                .v-card__title {
                    .c-label {
                        margin-left: 10px !important;
                    }
                }
                #entryContainer {
                    padding: 30px 16px 0;

                    .flexed {
                        justify-content: space-between;
                    }
                    .row {
                        &.row-flexed {
                            display: flex !important;
                        }
                    }

                    .grid {
                        display: grid;
                        grid-column-gap: 10px;
                        grid-template-columns: 480px 1fr;
                    }

                    #entryUpperSection {
                        margin-bottom: 20px;

                        #entryDetailsContainer {
                            width: 480px;
                        }

                        .inner {
                            box-shadow: none;
                            border: none;
                        }
                        #invoiceDetailsSection {
                            background: #f8ffff;
                            border: 1px solid #a9d5de;
                            border-radius: 0;
                            box-shadow: none;
                            padding: 12px 16px;
                            margin-bottom: 5px;
                            max-width: 450px;

                            .sub-head {
                                padding: 0 10px;

                                p {
                                    font-size: 11px;
                                    margin-bottom: 15px;
                                }
                            }

                            table {
                                font-size: 12px;
                                width: 100%;

                                tr {
                                    td {
                                        padding: 0 10px;

                                        &.form-label {
                                            min-width: 125px;
                                            vertical-align: top;
                                        }
                                        .form-item {
                                            padding-bottom: 6px;
                                            font-weight: bold;
                                            line-height: 14px;
                                            min-height: 0px;
                                        }
                                        .spacer {
                                            min-height: 20px;
                                        }
                                        &.highlight-container {
                                            padding: 0 0 10px 0;

                                            .flexed {
                                                display: flex;

                                                .highlight {
                                                    display: inline-block;
                                                    width: calc(49.5% - 5px);
                                                    background: #f8f8f9;
                                                    border: 1px solid #22242638;
                                                    padding: 12px 16px;

                                                    table {
                                                        width: 100%;

                                                        tr {
                                                            td {
                                                                padding: 0;

                                                                &.form-label {
                                                                    min-width: 75px !important;
                                                                    padding: 0px;
                                                                }

                                                                .form-item {
                                                                    max-width: 80px;
                                                                    word-wrap: break-word;
                                                                    min-height: 0;
                                                                }

                                                                .spacer {
                                                                    min-height: 56px;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }

                                                .highlight:first-child {
                                                    margin-right: 10px;
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            .blue {
                                color: #00008b !important;
                                background: none !important;
                                font-weight: bold;
                            }
                        }
                        #entryFormSection {
                            #entryFormTable {
                                font-size: 12px;

                                > tr > td {
                                    padding: 10px 5px;
                                    border: none;
                                    position: relative;

                                    .v-alert {
                                        font-size: 11px;
                                        margin-bottom: 0;
                                        padding: 8px;
                                        max-width: 300px;

                                        .v-alert__wrapper {
                                            i {
                                                display: none;
                                            }
                                            .v-alert__content {
                                                color: #721c24 !important;
                                                font-weight: bold;

                                                button {
                                                    font-weight: normal;
                                                    color: #337ab7 !important;
                                                    text-decoration: underline;
                                                    padding: 0;
                                                    text-transform: none;
                                                    font-size: 11px;
                                                    transition: none !important;
                                                    letter-spacing: normal;

                                                    &:before {
                                                        background-color: none !important;
                                                    }

                                                    &:hover:before {
                                                        background-color: transparent !important;
                                                    }
                                                }
                                            }
                                        }

                                        &.error {
                                            background: #f8d7da !important;
                                            border: 1px solid #f5c6cb !important;

                                            .v-alert__wrapper {
                                                .v-alert__content {
                                                    color: #721c24 !important;
                                                }
                                            }
                                        }

                                        &.warning {
                                            background: #fff3cd !important;
                                            border: 1px solid #ffeeba !important;
                                            line-height: 28px;
                                            min-height: 46px;

                                            .v-alert__wrapper {
                                                .v-alert__content {
                                                    color: #856404 !important;

                                                    span {
                                                        font-weight: normal !important;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    .queued-file {
                                        padding: 5px;
                                    }
                                    &.has-border {
                                        border-bottom: 1px solid #ececec;
                                    }

                                    input.has-checkbox {
                                        vertical-align: text-top;
                                    }

                                    &.c-datepicker {
                                        .has-error {
                                            background-color: none !important;
                                            border: 1px solid #f44336 !important;

                                            .date-picker-input-field {
                                                border: none !important;
                                            }
                                        }
                                    }

                                    .btn-icons {
                                        vertical-align: top;
                                        background: #757171;
                                        color: white !important;
                                        border-radius: 50%;
                                        padding: 4px;
                                        cursor: pointer;
                                        border: none !important;

                                        i {
                                            color: white !important;
                                            font-size: 18px;
                                        }

                                        &:disabled {
                                            background: #e0dddd;
                                        }
                                    }
                                }
                                td.form-label {
                                    color: #666666;
                                    text-align: right;
                                    padding: 10px 25px 10px 5px;
                                }
                                td.pdf-upload {
                                    position: relative;

                                    .tile {
                                        position: relative;
                                        background-color: #f5f7fa;
                                        align-self: center;
                                        text-align: center;
                                        padding: 40px 10px;
                                        color: #848ea1;
                                        font-size: 12px;
                                        cursor: pointer;

                                        &.loading {
                                            padding-top: 30px;
                                        }
                                    }
                                    .remove-file {
                                        text-align: center;
                                        padding: 10px;

                                        a {
                                            color: #c50c0c;
                                        }
                                    }

                                    #aiUploadFile {
                                        position: absolute;
                                        margin: 0;
                                        padding: 0;
                                        width: 300px;
                                        height: 94px;
                                        outline: none;
                                        opacity: 0;
                                        display: block;
                                        top: 10px;
                                        left: 5px;
                                        cursor: pointer;
                                    }
                                }

                                td#supplierSelect {
                                    .c-multiselect {
                                        width: 300px;
                                    }
                                }

                                tr#abnDetails {
                                    table.abn-table {
                                        font-size: 12px;
                                        width: 300px;
                                        margin-bottom: -10px;

                                        tr {
                                            &:first-child {
                                                td.form-label {
                                                    padding-top: 0;
                                                }
                                                td {
                                                    padding-top: 0;
                                                }
                                            }
                                        }

                                        td.form-label {
                                            text-align: left;
                                            padding-left: 0;
                                            width: 90px;
                                        }
                                    }
                                }
                            }
                        }
                        #entryPDFSection {
                            // height: 500px;
                            height: 100%;
                        }
                    }
                    #entryLowerSection {
                        border-top: 1px solid #ececec;
                        padding: 20px 0;
                        overflow: auto;

                        table {
                            width: 100%;
                            font-size: 11px;
                            border: none;
                            border-spacing: 0;
                            border-collapse: separate;

                            thead {
                                font-weight: bold;
                                background: #eaeaea;
                                line-height: 25px;
                                > tr > td {
                                    padding: 6px 10px;

                                    &.hold-ap-header {
                                        line-height: normal;
                                        height: 62px;
                                    }
                                    .header-button {
                                        margin-left: 10px;

                                        i {
                                            font-size: 20px;
                                        }
                                    }
                                    .btn-icons {
                                        vertical-align: top;
                                        background: #757171;
                                        color: white !important;
                                        border-radius: 50%;
                                        padding: 4px;
                                        cursor: pointer;
                                        border: none !important;

                                        i {
                                            color: white !important;
                                            font-size: 18px;
                                        }
                                    }
                                }
                            }
                            tbody {
                                tr {
                                    height: 40px;
                                    &.footer {
                                        background: #e6f3f9;
                                        > td {
                                            border-bottom: none;
                                            &.extra-padding {
                                                padding: 12px;
                                            }

                                            table.total-amount-table {
                                                width: 138px;
                                                font-weight: bold;

                                                tr {
                                                    height: auto;

                                                    td {
                                                        padding: 0;
                                                        border-bottom: none;

                                                        &.amount-label {
                                                            width: 60px;
                                                        }
                                                    }
                                                }
                                            }

                                            span.amount-label {
                                                width: 90px;
                                                display: inline-block;
                                            }

                                            &.notes-container {
                                                position: relative;

                                                textarea.c-textarea {
                                                    height: 61px;
                                                    width: 277px;
                                                    top: 7px;
                                                    left: 11px;
                                                    position: absolute !important;
                                                    border: 1px solid #aaaaaa !important;
                                                    box-shadow: none !important;
                                                    background: white !important;
                                                    resize: none;
                                                }
                                            }

                                            input[type='checkbox'] {
                                                vertical-align: middle;
                                            }
                                        }
                                    }
                                    > td {
                                        padding: 6px 10px;
                                        vertical-align: top;
                                        border-bottom: 1px solid #ececec;

                                        .buttons-container {
                                            vertical-align: top;
                                        }
                                        .btn-icons {
                                            vertical-align: top;
                                            background: #757171;
                                            color: white !important;
                                            border-radius: 50%;
                                            padding: 4px;
                                            cursor: pointer;
                                            border: none !important;

                                            &:disabled {
                                                background: #e0dddd;
                                            }
                                            &.success {
                                                background: #4caf50 !important;
                                            }
                                            &.danger {
                                                background: #f44336 !important;
                                            }
                                            i {
                                                color: white !important;
                                                font-size: 18px;
                                            }
                                        }
                                        &.counter-group {
                                            width: 40px;

                                            span {
                                                line-height: 28px;
                                            }
                                        }
                                        &.property-group {
                                            width: 360px; //AI

                                            @media only screen and (max-width: 1836px) {
                                                width: 271px;
                                            }

                                            .property-select {
                                                padding-bottom: 5px;
                                                vertical-align: top;

                                                .c-multiselect {
                                                    width: 250px;

                                                    &.unit-select {
                                                        margin-top: 5px;
                                                    }
                                                }
                                            }
                                            .error-message {
                                                margin-top: 5px;
                                                color: #f44336;
                                                font-size: 11px;
                                            }
                                        }
                                        &.type-group {
                                            width: 120px;

                                            .v-btn-toggle > .v-btn.v-btn {
                                                border-radius: 0 !important;
                                                font-size: 11px;
                                                padding: 4px 11px;
                                                border: none !important;
                                                height: 26px !important;
                                            }
                                        }
                                        &.account-group {
                                            width: 280px; //AI

                                            @media only screen and (max-width: 1744px) {
                                                width: 220px;
                                            }

                                            .account-select {
                                                padding-bottom: 5px;
                                                vertical-align: top;

                                                .c-multiselect {
                                                    width: 200px;
                                                }
                                            }

                                            .text-only-warning {
                                                color: orange;
                                                padding-top: 5px;

                                                span {
                                                    font-weight: bold;
                                                    text-transform: uppercase;
                                                }
                                            }
                                        }
                                        &.description-group {
                                            width: 335px;
                                            .textarea-container {
                                                position: relative;

                                                textarea.c-textarea {
                                                    background: none;
                                                    border: 1px solid #aaaaaa !important;
                                                    box-shadow: none !important;
                                                    height: 28px;
                                                    min-width: 315px;
                                                    margin-bottom: 5px;
                                                    // padding-bottom: 10px !important;

                                                    &.error {
                                                        border: 1px solid #f44336 !important;
                                                        background: #fff !important;
                                                    }
                                                }
                                                p.character-counter {
                                                    position: absolute;
                                                    bottom: 10px;
                                                    left: 10px;
                                                    font-size: 10px;
                                                    color: #8d8d8d;
                                                    margin-bottom: 0;
                                                }
                                            }
                                        }
                                        &.date-group {
                                            width: 180px;
                                            @media only screen and (max-width: 1825px) {
                                                min-width: 138px;
                                            }

                                            .form-group {
                                                padding-bottom: 5px;
                                                @media only screen and (max-width: 1825px) {
                                                    min-width: 140px;
                                                }

                                                .form-label {
                                                    min-width: 50px;
                                                    padding: 0 15px 0 0;
                                                    vertical-align: top;
                                                    line-height: 28px;
                                                    display: inline-block;

                                                    @media only screen and (max-width: 1825px) {
                                                        min-width: 30px;
                                                        padding: 0;
                                                    }

                                                    & + div {
                                                        display: inline-block;

                                                        .position-relative {
                                                            position: relative;
                                                        }
                                                    }
                                                }
                                                input {
                                                    background: none;
                                                    border: none !important;
                                                    box-shadow: none !important;
                                                }
                                                .date-picker-input-field {
                                                    width: 105px !important;
                                                }
                                                .has-error {
                                                    border: 1px solid #f44336 !important;

                                                    .date-picker-input-field {
                                                        border: none !important;
                                                    }
                                                }
                                                .error-message {
                                                    margin-top: 5px;
                                                    color: #f44336;
                                                    font-size: 11px;
                                                }
                                            }
                                        }
                                        &.tax-group {
                                            width: 140px;
                                            .c-multiselect {
                                                width: 120px;
                                            }
                                            .owner-tax-status {
                                                padding-top: 5px;
                                                font-size: 11px;
                                                color: #79b7e7;
                                            }
                                        }
                                        &.amount-group {
                                            width: 173px;

                                            .form-group {
                                                padding-bottom: 5px;

                                                @media only screen and (max-width: 1825px) {
                                                    width: 138px;
                                                }

                                                .form-label {
                                                    min-width: 50px;
                                                    padding-right: 15px;
                                                    display: inline-block;

                                                    @media only screen and (max-width: 1825px) {
                                                        min-width: 35px;
                                                        padding: 0;
                                                    }
                                                }
                                                input {
                                                    width: 100px;
                                                    text-align: right;
                                                    background: none;
                                                    border: 1px solid #aaaaaa !important;
                                                    box-shadow: none !important;
                                                    height: 28px;

                                                    &[disabled] {
                                                        background: #ececec !important;
                                                    }

                                                    &.error {
                                                        border: 1px solid #f44336 !important;
                                                        background: #fff !important;
                                                    }
                                                }

                                                .error-message {
                                                    margin-top: 5px;
                                                    color: #f44336;
                                                    font-size: 11px;
                                                }
                                            }
                                        }
                                        &.hol-ap-group {
                                            text-align: center;
                                            width: 60px;

                                            input {
                                                margin-top: 6px;
                                            }
                                        }
                                        &.ar-group {
                                            width: 45px;

                                            .v-input--selection-controls__input {
                                                color: #ff9507 !important;
                                                caret-color: #ff9507 !important;
                                                margin-right: 0 !important;

                                                i.mdi-checkbox-marked {
                                                    color: #ff9507 !important;
                                                    caret-color: #ff9507 !important;
                                                }
                                            }
                                        }
                                        &.remove-group {
                                            // width: 60px;
                                            text-align: right;

                                            .remove-rows {
                                                color: #c50c0c;
                                                cursor: pointer;
                                            }
                                        }
                                        .v-input--selection-controls {
                                            margin-top: 0;
                                            padding-top: 0;
                                        }
                                    }

                                    &.footer-min {
                                        height: 20px;
                                        td.extra-padding {
                                            padding: 6px 12px !important;
                                        }
                                    }
                                }
                            }

                            .v-date-picker-table--date td {
                                // padding: 0 !important;
                                // border-bottom: none !important;
                                // vertical-align: middle !important;
                            }

                            .v-select .dropdown-toggle {
                                border: 1px solid #aaaaaa !important;
                            }

                            #newEntryBtn {
                                font-size: 11px;
                                line-height: 22px;

                                i {
                                    font-size: 15px;
                                    margin-top: -3px;
                                }
                            }
                        }
                    }
                    #interimInvoiceOptions {
                        padding: 12px;

                        input {
                            vertical-align: text-bottom;
                            margin-right: 5px;
                        }

                        .spacer {
                            margin-left: 20px;
                        }
                    }
                    #navigationButtonsContainer {
                        padding: 12px 0;
                        border-top: 1px solid #6bc9ff;

                        .c-btn {
                            font-size: 11px;
                            padding-top: 3px;

                            i {
                                font-size: 18px;
                                margin-top: -3px;
                            }

                            &:hover {
                                i {
                                    color: #ffffff;
                                    transition: none !important;
                                }
                            }
                        }
                    }
                }
                #interimContainer {
                    .interim {
                        font-size: 12px;
                        padding: 10px;
                        width: 545px;
                        margin: 10px auto;
                        border-radius: 0;

                        @media only screen and (max-width: 959px) {
                            width: 90%;
                        }
                        &.interim-success {
                            background: #d1ecf1;
                            b {
                                display: block;
                                padding-top: 10px;

                                a {
                                    display: inline-flex;
                                    padding-bottom: 10px;
                                    margin-right: 10px;
                                    min-width: 75px;
                                    font-size: 11px;

                                    img {
                                        margin-top: -3px;
                                        padding-right: 5px;
                                    }

                                    span {
                                        color: #337ab7;
                                    }
                                }

                                a + span {
                                    display: none;
                                }
                            }
                        }
                        &.interim-error {
                            background: #fff3cd;
                        }
                    }
                }
            }
            &.ocr-audit {
                .d-flex {
                    justify-content: space-between;

                    .flexed {
                        span.c-label {
                            margin-left: 10px !important;
                            background: #00baf2;
                        }
                        .right-flexed {
                            text-align: right;
                            padding: 16px 16px 8px;

                            @media only screen and (max-width: 959px) {
                                text-align: left;
                            }
                        }
                    }
                }
                #auditEntryContainer {
                    padding: 52px 16px 16px;

                    .audit-item {
                        font-size: 11px;

                        .form-label {
                            font-weight: bold;
                            padding-right: 15px;
                            vertical-align: top;
                        }

                        .form-item {
                            padding-bottom: 15px;
                        }

                        textarea {
                            min-width: 300px;
                            min-height: 520px;
                            border: none !important;
                            box-shadow: none !important;
                            background: none !important;
                        }
                    }
                }
            }
        }
    }
}

@keyframes placeHolderShimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

.animated-background {
    animation-duration: 1s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: placeHolderShimmer;
    animation-timing-function: linear;
    background: #f6f7f8;
    background: linear-gradient(to right, #eeeeee 8%, #dddddd 18%, #eeeeee 33%);
    background-size: 800px 104px;
    // height: 96px;
    position: relative;
}

.skeleton-header-title {
    width: 350px;
    height: 32px;
}

.skeleton-header-subtitle {
    width: 350px;
    height: 18px;
}

.skeleton-highlight-sm {
    width: 450px;
    height: 395px;
}

.skeleton-highlight-lg {
    height: 700px;
}

.skeleton-upper-table-label {
    width: 120px;
    height: 18px;
    margin: 13px 0;
}

.skeleton-upper-table-value {
    width: 310px;
    height: 18px;
    margin: 13px 0 13px 14px;
}

.skeleton-lower-table-header {
    width: 100%;
    height: 37px;
}

.skeleton-lower-table-value {
    height: 18px;
    margin: 13px;

    &.corner-left,
    &.middle {
        margin-left: 0;
    }

    &.corner-right {
        margin-right: 0;
    }
}
