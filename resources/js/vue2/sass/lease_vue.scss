body.c8-dark #frame #container #leasePageDiv {
    nav {
        ul.v-pagination {
            button.v-pagination__navigation {
                .theme--light.v-icon {
                    color: rgba(0, 0, 0, 0.54) !important;
                }
            }
        }
    }

    .v-alert {
        .theme--dark.v-icon {
            color: #000;
        }
    }

    .v-icon.titleHeader {
        background: none !important;
    }

    .v-dialog {
        .v-data-table {
            .v-btn {
                color: #fff;
            }
        }
    }

    .ui.modal {
        .v-btn {
            color: #fff;
        }

        .dropdown.vue-select2 {
            .open-indicator:before {
                border-color: #fff;
            }

            .vs__selected-options {
                .selected-tag {
                    color: #fff;
                }

                input.form-control,
                input[type='search'] {
                    color: #fff;
                }
            }

            .dropdown-menu {
                background: #282828;
            }
        }
    }
}
