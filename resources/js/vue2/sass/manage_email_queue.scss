.v-menu__content {
    position: fixed !important;
    font-weight: normal !important;
    font-size: 11px !important;
    z-index: 999999 !important;
    color: inherit !important;
}
.v-dialog .multiselect__content-wrapper {
    position: fixed;
    max-width: inherit;
}
.v-dialog {
    .c8-datatable-custom-footer {
        .multiselect__content-wrapper {
            position: absolute;
            max-width: inherit;
        }
    }
}
.v-dialog__content {
    background-color: transparent !important;
}
[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    background-color: rgba(0, 0, 0, 1);
    text-decoration: none;
}
.c8-page .c8-datatable-custom .v-data-table-header th {
    background: #eaeaea !important;
}
.data-grid td.required {
    width: 16.28px;
}
.abn-detail-container {
    height: 30px;
    margin-top: -30px;
    margin-left: 302px;
}

.abn-result-table {
    max-width: 390px;
    width: 100%;
    border: 1px solid #ececec;
}

.abn-result-table th,
#abn-result-table td {
    text-align: left;
    border-bottom: 1px solid #ececec !important;
}

.abn-message {
    color: crimson;
    font-weight: bold;
    min-height: 30px;
    padding-top: 2px;
}

.copy-value-btn {
    margin-left: 5px;
    max-width: 30px;
    min-width: 20px;
}

.abn-detail-container {
    height: 30px;
    margin-top: -30px;
    margin-left: 302px;
}

.loading-info-text {
    position: relative;
    top: 1px;
}
.noteTextArea textarea {
    font-size: 14px;
}

body.c8-dark #frame #container #propertyPageDiv .c8-page .page-form .form-row .form-subheader {
    background: var(--dark-ligther-color) !important;
}

body.c8-dark .v-data-table .v-data-table__wrapper table thead tr th {
    background: var(--dark-ligther-color) !important;
}
body.c8-dark .section-toolbar {
    background: var(--dark-ligther-color) !important;
}

body #frame #container #propertyPageDiv .theme--light.v-data-table thead tr:last-child th {
    color: #888;
}
body.c8-dark #frame #container #propertyPageDiv .theme--light.v-data-table thead tr:last-child th {
    color: #ffffff;
}

.c8-page .multiselect .multiselect__tags .multiselect__placeholder {
    padding-top: 3px !important;
}

.view-email-modal {
    font-size: 11px;

    .recipient-alert {
        margin-top: -7px;
        margin-bottom: 0px;
        border-left-color: #89bf1d;
        background-color: #f7fbf0;
        .recipent-email {
            color: #47630f;
        }
        .recipient-status {
            color: #9faf80;
        }
    }
    .cc-alert {
        margin-top: -7px;
        margin-bottom: 0px;
        border-left-color: #5cc3cc;
        background-color: #eff9fa;
        .cc-email {
            color: #2e8c94;
        }
        .cc-status {
            color: #8fc3c7;
        }
    }
    .failed-alert {
        margin-top: -7px;
        margin-bottom: 0px;
    }
    .info-alert {
        margin-top: -7px;
        margin-bottom: 0px;
    }
    .attachment-chip {
        margin: 1px !important;
        height: 22px !important;
    }
    div {
        font-size: 11px;
    }
    table {
        width: 984px;
        font-size: 11px;
        .alert-td {
            padding-bottom: 12px;
            padding-top: 12px;
            .row {
                margin-left: 0;
            }
        }
    }
}
.modal-iframe {
    width: -webkit-fill-available;
    height: -webkit-fill-available;
    border: none;
    background-color: white;
}
