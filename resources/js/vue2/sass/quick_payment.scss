.v-menu__content {
    position: fixed !important;
}

.v-tooltip__content {
    position: fixed !important;
}
.txt-pay,
.txt-green a {
    color: #1ebc30 !important;
}

.txt-ignore,
.txt-gray {
    color: #aaaaaa !important;
}

.txt-ignore,
txt-gray a {
    color: #aaaaaa !important;
}

.txt-orange a {
    color: #f2711c !important;
}

.txt-orange {
    color: #f2711c !important;
}

.txt-bold {
    font-weight: bold;
}

.txt-not-paid {
    color: crimson;
    font-weight: bold;
}

.txt-reserve {
    color: #00baf2 !important;
    font-weight: bold;
}

.txt-info a {
    color: #006581 !important;
    font-weight: bold;
}

.c8-page .v-dialog .page-list .v-data-table .v-data-table-header th,
.c8-page .page-list .v-data-table .v-data-table-header th {
    padding: unset !important;
}

.c8-page .v-dialog .page-list .v-data-table tbody tr td,
.c8-page .page-list .v-data-table tbody tr td {
    padding: unset !important;
}
div.v-item-group.theme--light.v-btn-toggle {
    border-radius: 4px !important;
}
.no-text-transform {
    text-transform: none !important;
}
#quick-payment-div {
    .uix-multiselect .ui-widget-header {
        padding: 2px 4px !important;

        .header-text {
            line-height: 25px;
        }

        .uix-control-right,
        .uix-control-left {
            width: 25px;
            height: 24px;
        }

        input[type='text'].uix-search {
            margin-top: 1.5px;
            height: 22px !important;
            min-height: 22px !important;
            line-height: 22px !important;
        }
    }
}

.group-total-row {
    color: #ffffff;
    font-weight: bold;
}
