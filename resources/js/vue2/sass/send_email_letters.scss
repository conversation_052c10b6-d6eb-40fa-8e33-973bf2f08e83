/* LETTER BETA */
body.c8-dark #frame #container #send-email-and-letters-div .ui.selectable.table tbody tr:hover,
body.c8-dark #frame #container #send-email-and-letters-div .ui.table tbody tr td.selectable:hover {
    background: var(--dark-ligther-color) !important;
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .c8-page .page-form .form-row .form-subheader {
    background: var(--dark-ligther-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .c8-page .page-form .form-row {
    background: var(--dark-primary-color) !important;
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .noteTextArea textarea,
body.c8-dark #frame #container #send-email-and-letters-div .noteTextAreaBgColor {
    background: var(--dark-primary-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .ui.checkbox label {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .c8-page .page-form .form-row .form-text-button {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .theme--light.v-icon,
body.c8-dark #frame #container #send-email-and-letters-div .multiselect__placeholder,
body.c8-dark
    #frame
    #container
    #send-email-and-letters-div
    .theme--light.v-date-picker-header
    .v-date-picker-header__value:not(.v-date-picker-header__value--disabled)
    button:not(:hover):not(:focus),
body.c8-dark #frame #container #send-email-and-letters-div .v-btn--outlined .v-btn__content .v-icon,
body.c8-dark #frame #container #send-email-and-letters-div .v-btn--round .v-btn__content .v-icon,
body.c8-dark
    #frame
    #container
    #send-email-and-letters-div
    .theme--light.v-date-picker-table
    .v-date-picker-table--date__week,
body.c8-dark #frame #container #send-email-and-letters-div .theme--light.v-date-picker-table th,
body.c8-dark #frame #container #send-email-and-letters-div .v-application .primary--text {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-btn__content {
    background: transparent;
}

body.c8-dark
    #frame
    #container
    #send-email-and-letters-div
    .c8-page
    .page-form
    .form-row
    .v-input:not(.v-textarea)
    input {
    background: transparent !important;
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-input--selection-controls__ripple {
    background: transparent;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-card {
    box-shadow: none;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-card .v-card__title input {
    /* border: 1px solid !important; */
    /* border-color: var(--primary-text-color); */
}

body.c8-dark #frame #container #send-email-and-letters-div .v-card header.v-toolbar .v-toolbar__content {
    background: var(--dark-tertiary-color);
}

body.c8-dark #frame #container #send-email-and-letters-div .v-card header.v-toolbar .v-toolbar__content div,
body.c8-dark #frame #container #send-email-and-letters-div .v-data-table__checkbox,
body.c8-dark
    #frame
    #container
    #send-email-and-letters-div
    .v-card
    .v-data-table__wrapper
    .v-data-table-header
    .columnheader
    div,
body.c8-dark
    #frame
    #container
    #send-email-and-letters-div
    .v-card
    .v-data-table__wrapper
    .v-data-table-header
    .columnheader
    table
    tr {
    background: transparent;
}

body.c8-dark #frame #container #send-email-and-letters-div .theme--light.v-data-table thead tr:last-child th {
    background: var(--dark-secondary-color);
    color: var(--primary-text-color);
}

body.c8-dark #frame #container #send-email-and-letters-div .v-card__actions,
body.c8-dark #frame #container #send-email-and-letters-div .v-card .v-card__actions {
    background: var(--dark-primary-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-list-item--link:before {
    background-color: var(--dark-ligthest-color);
}

body.c8-dark #frame #container #send-email-and-letters-div .v-dialog__content,
body.c8-dark #frame #container #send-email-and-letters-div .v-overlay {
    background: transparent !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .c8-page .v-input:not(.v-textarea) input {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .theme--light.v-label {
    color: var(--primary-text-color) !important;
}

body.c8-dark
    #frame
    #container
    #send-email-and-letters-div
    .theme--light.v-text-field
    > .v-input__control
    > .v-input__slot:before {
    border-color: var(--primary-text-color);
}

body.c8-dark #frame #container #send-email-and-letters-div .v-application .grey--text.text--darken-1 {
    color: var(--primary-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-alert,
body.c8-dark #frame #container #send-email-and-letters-div .v-alert .v-alert__wrapper,
body.c8-dark #frame #container #send-email-and-letters-div .v-alert .v-alert__wrapper .v-alert__content,
body.c8-dark #frame #container #send-email-and-letters-div .v-alert .v-alert__wrapper .v-alert__content div {
    background: var(--light-primary-color) !important;
    color: var(--light-text-color) !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-alert .v-alert__wrapper i.v-icon.v-alert__icon.red--text {
    color: black !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-alert .v-alert__wrapper .v-alert__content div .v-divider {
    border: none !important;
}

body.c8-dark #frame #container #send-email-and-letters-div .v-btn.back-btn {
    color: var(--primary-text-color) !important;
}

.c8-dark .tox-tinymce div {
    background-color: white !important;
}
.c8-dark .tox-tinymce input[type='text'] {
    background-color: black !important;
    color: black !important;
    div {
        color: black !important;
    }
}
