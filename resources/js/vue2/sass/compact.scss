.multiselect__option {
    padding: 6px;
    min-height: 20px;
    line-height: 16px;
}
.multiselect__option:after {
    padding: 6px;
    min-height: 20px;
    line-height: 16px;
}
.v-data-table__wrapper {
    td {
        font-size: 11px !important;
    }
}
.c8-page {
    .c8-dp {
        .c8-dp-input {
            font-size: 11px;
        }
    }
}

.form-expansion-header {
    font-size: 12px;
    min-height: 36px;
}
.v-expansion-panel--active > .v-expansion-panel-header {
    min-height: 36px;
}

.pdf-upload {
    position: relative;

    .tile {
        background-color: #f5f7fa;
        align-self: center;
        text-align: center;
        padding: 40px 10px;
        color: #848ea1;
        font-size: 12px;
        cursor: pointer;

        &.loading {
            padding-top: 30px;
        }
    }
    .remove-file {
        text-align: center;
        padding: 10px;

        a {
            color: #c50c0c;
        }
    }
}
.treeview-sub-label {
    color: #777777;
    font-weight: bolder;
    text-transform: uppercase;
    font-size: 6px;
}
.pre-content {
    white-space: pre-wrap; /* Since CSS 2.1 */
    white-space: -moz-pre-wrap; /* Mozilla, since 1999 */
    white-space: -pre-wrap; /* Opera 4-6 */
    white-space: -o-pre-wrap; /* Opera 7 */
    word-wrap: break-word; /* Internet Explorer 5.5+ */
}
.float-end {
    float: right !important;
}
.treeview-sub-label {
    color: #777777;
    font-weight: bolder;
    text-transform: uppercase;
    font-size: 6px;
}
.file-browser-badge {
    font-size: 9px !important;
    height: 13px !important;
    min-width: 13px !important;
    padding: 3px 3px !important;
}
//.pdf-upload {
//  position: relative;
//
//  .tile {
//    position: relative;
//    background-color: #F5F7FA;
//    align-self: center;
//    text-align: center;
//    padding: 40px 10px;
//    color: #848EA1;
//    font-size: 12px;
//    cursor: pointer;
//
//    &.loading {
//      padding-top: 30px;
//    }
//  }
//  .remove-file {
//    text-align: center;
//    padding: 10px;
//
//    a {
//      color: #C50C0C;
//    }
//  }
//
//  #aiUploadFile {
//    position: absolute;
//    margin: 0;
//    padding: 0;
//    width: 300px;
//    height: 94px;
//    outline: none;
//    opacity: 0;
//    display: block;
//    top: 10px;
//    left: 5px;
//    cursor: pointer;
//  }
//}
