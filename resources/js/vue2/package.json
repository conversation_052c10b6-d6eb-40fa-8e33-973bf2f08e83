{"name": "@cirrus8/api-vue2", "version": "1.3.0-alpha.16", "private": true, "scripts": {"dev": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "build": "mix --production", "test": "npm run lint", "lint": "eslint --ext .js,.ts,.vue src", "style": "prettier . --check"}, "devDependencies": {"@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@cirrus8/eslint-config": "workspace:*", "@mdi/font": "^3.9.97", "@tailwindcss/postcss": "^4.1.5", "cross-env": "^5.2.1", "eslint": "~8.57.0", "jquery": "^3.5.1", "laravel-mix": "^6.0.49", "lodash": "^4.17.21", "node-polyfill-webpack-plugin": "^4.1.0", "popper.js": "^1.16.1", "resolve-url-loader": "^5.0.0", "sass": "^1.83.1", "sass-loader": "^13.3.3", "ts-loader": "^9.5.2", "vue": "^2.7.16", "vue-loader": "^15.11.1", "webpack": "^5.97.1", "webpack-cli": "^4.10.0"}, "dependencies": {"@bugsnag/browser-performance": "^2.13.0", "@ckeditor/ckeditor5-vue": "^1.0.1", "@jamescoyle/vue-icon": "^0.1.2", "@jemicle17/ckeditor5-custom-build-cirrus8": "git+https://github.com/jemicle17/ckeditor5-custom-build-cirrus8.git", "@mdi/js": "^7.4.47", "axios": "^1.7.9", "dayjs": "^1.11.13", "material-design-icons-iconfont": "^4.0.5", "moment": "^2.27.0", "portal-vue": "^2.1.7", "semantic-ui-vue": "^0.8.1", "sweetalert2": "^9.17.1", "tailwindcss": "^4.0.0", "tooltipster": "^4.2.8", "vue-content-loading": "^1.6.0", "vue-html-to-paper": "^1.3.1", "vue-multiselect": "^2.1.9", "vue-router": "^3.5.3", "vue-select": "^2.6.4", "vue-select2": "^0.2.6", "vue-sweetalert2": "^1.6.4", "vuedraggable": "^2.24.3", "vuejs-datepicker": "^1.6.2", "vuejs-noty": "^0.1.3", "vuetify": "^2.3.6", "vuex": "^3.5.1", "xlsx": "^0.17.0"}}