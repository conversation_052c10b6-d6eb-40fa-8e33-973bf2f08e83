{"AU": {"status": 1, "country_code": "AU", "business_label": "ABN", "business_length": 11, "display_state": true, "display_bsb": true, "post_code_length": 4, "post_code_min_length": 4, "bank_account_length": 9, "bsb_label": "BSB", "bsb_length": 6, "bsb_format": {"delimiter": "-", "delimiter_frequency": 3}, "tax_label": "GST", "business_prefix": null, "currency_name": "australian dollar", "currency_code": "AUD", "currency_symbol": "$", "simple_currency_name": "dollar", "area_unit": "m&sup2;", "area_unit_string": "SQM", "area_unit_full_string": "square meter", "per_area_unit": "psm", "post_code_format": ["9999"], "vo": "vo", "variable": "variable", "variable_outgoings": "variable outgoings", "trust_account": "trust account", "trust_bank_account": "trust bank account", "property_manager": "property manager", "portfolio_manager": "portfolio manager", "trust_accountant": "trust accountant", "suburb": "suburb", "strata": "strata", "mobile_settings": {"mobile_country_code": "+61", "mobile_number_length": 9, "mobile_prefixes": "4", "number_format": "XXXX XXX XXX, XXXXXXXXXX"}}, "NZ": {"status": 1, "country_code": "NZ", "business_label": "GST No.", "business_length": 9, "display_state": false, "display_bsb": false, "post_code_length": 4, "post_code_min_length": 4, "bank_account_length": 16, "bsb_label": null, "bsb_length": 0, "bsb_format": {"delimiter": "&nbsp; ", "delimiter_frequency": 0}, "tax_label": "GST", "business_prefix": null, "currency_name": "new zealand dollar", "currency_code": "NZ", "currency_symbol": "$", "simple_currency_name": "dollar", "area_unit": "m&sup2;", "area_unit_string": "SQM", "area_unit_full_string": "square meter", "per_area_unit": "psm", "post_code_format": ["9999"], "vo": "vo", "variable": "variable", "variable_outgoings": "variable outgoings", "trust_account": "trust account", "trust_bank_account": "trust bank account", "property_manager": "property manager", "portfolio_manager": "portfolio manager", "trust_accountant": "trust accountant", "suburb": "suburb", "strata": "strata", "mobile_settings": {"mobile_country_code": "+64", "mobile_number_length": 8, "mobile_prefixes": "2,3,21,22,27", "number_format": "XXXX XXX XXX, XXXXXXXXXX"}}, "CA": {"status": 1, "country_code": "CA", "business_label": "ABN", "business_length": 11, "display_state": true, "display_bsb": true, "post_code_length": 0, "post_code_min_length": 0, "bank_account_length": 9, "bsb_label": "BSB", "bsb_length": 6, "bsb_format": {"delimiter": "-", "delimiter_frequency": 3}, "tax_label": "GST", "business_prefix": null, "currency_name": "canadian dollar", "currency_code": "CAD", "currency_symbol": "$", "simple_currency_name": "dollar", "area_unit": "m&sup2;", "area_unit_string": "SQM", "area_unit_full_string": "square meter", "per_area_unit": "psm", "post_code_format": ["A9A 9A9", "A9A-9A9"], "vo": "vo", "variable": "variable", "variable_outgoings": "variable outgoings", "trust_account": "trust account", "trust_bank_account": "trust bank account", "property_manager": "property manager", "portfolio_manager": "portfolio manager", "trust_accountant": "trust accountant", "suburb": "suburb", "strata": "strata", "mobile_settings": {"mobile_country_code": "+1", "mobile_number_length": 10, "mobile_prefixes": null, "number_format": "XXX XXX XXXX, XXXXXXXXXX"}}, "GB": {"status": 1, "country_code": "GB", "business_label": "VAT Number", "business_length": 9, "display_state": false, "display_bsb": true, "post_code_length": 8, "post_code_min_length": 6, "bank_account_length": 8, "bsb_label": "Sort Code", "bsb_length": 6, "bsb_format": {"delimiter": "-", "delimiter_frequency": 2}, "tax_label": "VAT", "business_prefix": "GB", "currency_name": "british pound", "currency_code": "GBP", "currency_symbol": "£", "simple_currency_name": "pound", "area_unit": "ft&sup2;", "area_unit_string": "SQFT", "area_unit_full_string": "square foot", "per_area_unit": "psft", "post_code_format": ["AA9A 9AA", "A9A 9AA", "A9 9AA", "A99 9AA", "AA9 9AA", "AA99 9AA"], "vo": "sc", "variable": "service", "variable_outgoings": "service charge", "trust_account": "client account", "trust_bank_account": "client bank account", "property_manager": "surveyor", "portfolio_manager": "surveyor", "trust_accountant": "client accountant", "suburb": "town", "strata": "block management", "mobile_settings": {"mobile_country_code": "+44", "mobile_number_length": 10, "mobile_prefixes": "7", "number_format": "XXXX XXXXXX, XXXXXXXXXX"}}}