const { resolve } = require('path');
const { globals } = require('globals');

module.exports = {
    // https://eslint.org/docs/user-guide/configuring#configuration-cascading-and-hierarchy
    root: true,

    parser: 'vue-eslint-parser',
    parserOptions: {
        extraFileExtensions: ['.vue'],
        parser: '@typescript-eslint/parser',
        project: resolve(__dirname, './tsconfig.json'),
        tsconfigRootDir: __dirname,
        ecmaVersion: 2024,
        sourceType: 'module',
    },

    env: {
        browser: true,
        es2024: true,
        node: true,
    },

    // Rules order is important, please avoid shuffling them
    extends: [
        // Base ESLint recommended rules
        'eslint:recommended',
        // https://github.com/typescript-eslint/typescript-eslint/tree/master/packages/eslint-plugin#usage
        'plugin:@typescript-eslint/recommended',
        'plugin:jsdoc/recommended',
        'plugin:prettier/recommended',
        'prettier',
    ],

    plugins: [
        // required to apply rules which need type information
        '@typescript-eslint',
        // https://eslint.vuejs.org/user-guide/#why-doesn-t-it-work-on-vue-file
        // required to lint *.vue files
        'vue',
        'prettier',
    ],

    globals: {
        ga: 'readonly', // Google Analytics
        $: 'readonly', // jquery from framework
        ...globals,
    },

    // add your custom rules here
    rules: {
        'prefer-const': 'error',
        'prefer-template': 'error',
        'prefer-promise-reject-errors': 'off',

        // this rule, if on, would require explicit return type on the `render` function
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/consistent-type-imports': ['error', { prefer: 'type-imports' }],

        // in plain CommonJS modules, you can't use `import foo = require('foo')` to pass this rule, so it has to be disabled
        // '@typescript-eslint/no-var-requires': 'off',

        'vue/component-definition-name-casing': ['warn', 'kebab-case'],
        'vue/multi-word-component-names': 'off',
        'vue/prop-name-casing': 'off',
        'vue/no-use-v-if-with-v-for': 'off',
        'vue/no-v-text-v-html-on-component': [
            'error',
            {
                ignoreElementNamespaces: true,
            },
        ],

        // allow debugger during development only
        'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    },
};
