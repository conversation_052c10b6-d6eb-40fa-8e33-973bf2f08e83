{"name": "@cirrus8/eslint-config", "version": "1.3.0-alpha.16", "main": "index.js", "license": "proprietary", "private": true, "dependencies": {"@eslint/js": "^9.22.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jsdoc": "^50.6.11", "eslint-plugin-prettier": "^5.3.1", "eslint-plugin-vue": "^10.1.0", "globals": "^16.0.0", "prettier": "^3.5.3", "typescript": "^5.8.2", "vue-eslint-parser": "^10.1.3"}, "peerDependencies": {"eslint": "^8.0.0", "prettier": "^3.0.0"}}