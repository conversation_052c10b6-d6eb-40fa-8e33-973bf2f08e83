<html>
<table class="table">
    <thead>
    <tr>
        {{--            <th colspan="7">System and PM Data Comparison (Client: {{ $report_db }}) Month: {{ $report_month }} Year: {{ $report_year }}</th>--}}
        <th colspan="10">System and PM Data Comparison ( Client: {{ $report_db }} )
            for {{ $fmt_report_month  }} {{ $report_year }}</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        {{--        <td colspan="10" align="left" style="font-weight: bold; background-color: #45A7C3; color: #ffffff;">HEADER INFO</td>--}}
        <td colspan="10" align="left" style="font-weight: bold; background-color: #45A7C3; color: #ffffff;">HEADER
            INFO
        </td>
    </tr>
    <tr>
        <td style="font-weight: bold; background-color: #EEEEEE;">Batch ID</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">Received At</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Invoice Number</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Invoice Number</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Invoice Date</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Invoice Date</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Supplier Code</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Supplier Code</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI BPAY CRN</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM BPAY CRN</td>
    </tr>
    @foreach ($get_data as $get_val)
        <tr>
            <td>{{ $get_val->PM_fc_batch_id }}</td>
            <td>{{ $get_val->PM_fmt_created_at }}</td>
            <td>{{ $get_val->AI_invoice_number }}</td>
            <td>{{ $get_val->PM_invoice_number }}</td>
            <td>{{ $get_val->AI_invoice_date }}</td>
            <td>{{ $get_val->PM_invoice_date }}</td>
            <td>{{ $get_val->AI_supplier_code }}</td>
            <td>{{ $get_val->PM_supplier_code }}</td>
            <td>{{ $get_val->AI_bpay_crn }}</td>
            <td>{{ $get_val->PM_bpay_crn }}</td>
        </tr>
    @endforeach
</table>

{{--        @foreach ($client_list as $client)--}}
{{--            <tr>--}}
{{--                <td>{{ $client->database_id }}</td>--}}
{{--                <td>{{ $client->database_name }}</td>--}}
{{--                <td>{{ $client->database_description }}</td>--}}
{{--                <td>{{ $client->logo_url }}</td>--}}
{{--                <td>{{ $client->active }}</td>--}}
{{--                <td>{{ $client->development }}</td>--}}
{{--                <td>{{ $client->use_ai }}</td>--}}
{{--            </tr>--}}
{{--        @endforeach--}}
<table class="table">
    {{--    <tr>--}}
    {{--        <td colspan="7">&nbsp;</td>--}}
    {{--    </tr>--}}
    <thead>
    <tr>
        <th colspan="24" align="left" style="font-weight: bold; background-color: #45A7C3; color: #ffffff;">LINE INFO
        </th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td style="font-weight: bold; background-color: #EEEEEE;">Batch ID</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">Received At</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Line No.</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Line No.</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Property Code</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Property Code</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Account Code</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Account Code</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI From Date</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM From Date</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI To Date</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM To Date</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Due Date</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Due Date</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Description</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Description</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Gross Amount</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Gross Amount</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI {{ $tax_label }} Amount</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM {{ $tax_label }} Amount</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Net Amount</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Net Amount</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">AI Transaction Type</td>
        <td style="font-weight: bold; background-color: #EEEEEE;">PM Transaction Type</td>
    </tr>
    @foreach ($get_line_data as $get_line_val)
        <tr>
            {{--            <td>{{ $get_line_val->AI_fc_batch_id }}</td>--}}
            <td>{{ $get_line_val->PM_fc_batch_id }}</td>
            <td>{{ $get_line_val->PM_fmt_created_at }}</td>
            <td>{{ $get_line_val->AI_line_no }}</td>
            <td>{{ $get_line_val->PM_line_no }}</td>
            <td>{{ $get_line_val->AI_property_code }}</td>
            <td>{{ $get_line_val->PM_property_code }}</td>
            <td>{{ $get_line_val->AI_account_code }}</td>
            <td>{{ $get_line_val->PM_account_code }}</td>
            <td>{{ $get_line_val->AI_from_date }}</td>
            <td>{{ $get_line_val->PM_from_date }}</td>
            <td>{{ $get_line_val->AI_to_date }}</td>
            <td>{{ $get_line_val->PM_to_date }}</td>
            <td>{{ $get_line_val->AI_due_date }}</td>
            <td>{{ $get_line_val->PM_due_date }}</td>
            <td>{{ $get_line_val->AI_description }}</td>
            <td>{{ $get_line_val->PM_description }}</td>
            <td>{{ $get_line_val->AI_gross_amount }}</td>
            <td>{{ $get_line_val->PM_gross_amount }}</td>
            <td>{{ $get_line_val->AI_tax_amount }}</td>
            <td>{{ $get_line_val->PM_tax_amount }}</td>
            <td>{{ $get_line_val->AI_net_amount }}</td>
            <td>{{ $get_line_val->PM_net_amount }}</td>
            <td>{{ $get_line_val->AI_trans_type }}</td>
            <td>{{ $get_line_val->PM_trans_type }}</td>
        </tr>
    @endforeach

    </tbody>
</table>

</html>