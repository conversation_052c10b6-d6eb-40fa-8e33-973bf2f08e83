<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<style>
    @page {
        margin: 2px;
    }

    body {
        font-family: Helvetica-Bold, Arial, sans-serif !important;
        font-size: 9px !important;
        margin: 10px;
        margin-bottom: 50px !important;
    }

    header {
        position: fixed;
        top: 0px;
        left: 0px;
        right: 0px;
        padding: 10px
    }

    footer {
        position: fixed;
        bottom: 0px;
        left: 0px;
        right: 0px;
        height: 50px;

        /** Extra personal styles **/
        text-align: center;
        line-height: 35px;
    }

    .page-break {
        page-break-after: always;
    }

    .report-title {
        font-size: 12px !important;
        color: #004472 !important;
    }

    .report-footer td {
        font-size: 6px !important;
        font-family: Helvetica, Arial, sans-serif !important;
        width: 33%;
    }

    #pdf-table .subHeader {
        color: white;
        background: #004472;
        font-size: 9px !important;
    }

    .subHeader th {
        padding-top: 4px !important;
        padding-bottom: 2px !important;
        font-family: Helvetica-Bold, <PERSON>l, sans-serif !important;
    }

    #pdf-table td, #pdf-table th {
        border-bottom: 1px solid #ececec;
        padding: 1px;
    }

    .center {
        text-align: center;
    }

    .right {
        text-align: right;
    }

    .sub-total-row td {
        background: #e2e2e2 !important;
    }

    .title {
        background: #f0f0f0 !important;
    }

    .sub-total-row td {
        border: none !important;
    }

    .header-logo {
        height: auto;
    }
</style>
<body>
<footer>
    <table width="100%" cellpadding="0" cellspacing="0">
        <tr class="report-footer">
            <td class="center">&nbsp;</td>
            <td class="center">Powered by Cirrus8 Software</td>
            <td class="center">cirrus8\{{ $date }}\{{ $doc_type }}</td>
        </tr>
    </table>
</footer>

<main>
    <table id="pdf-table" width="100%" cellpadding="0" cellspacing="0">
        <thead>
        <tr>
            <th valign="top">
                <div class="report-title">INCOME & EXPENDITURE BUDGET</div>
                <div class="report-title">EXPLANATORY NOTES</div>
                <div><strong>for year ending {{ $day }} {{ $month_name }} {{ $financial_year }}</strong></div>
                <div>for {{ $property_name }} ({{ $property_code }})</div>
            </th>
            <th class="right" valign="top">
                @if($logo_height==0)
                    <img class="header-logo" src="{!! $company_header_logo_path !!}" alt="Logo"
                         width="{{ $logo_width }}px">
                @else
                    <img src="{!! $company_header_logo_path !!}" alt="Logo" width="{{ $logo_width }}px"
                         height="{{ $logo_height }}px">
                @endif
            </th>
        </tr>
        </thead>
        @if($income_comment_page_flag)
            <tbody>
            <tr>
                <td colspan="2" class="title"><strong>INCOME</strong></td>
            </tr>
            </tbody>
            @foreach($income_budget_arr as $row_data)
                @if($row_data["comment_flag"])
                    <tbody>
                    <tr>
                        <td colspan="2"><strong>{{ $row_data['account_code'] }}
                                - {{ $row_data['account_name'] }}</strong></td>
                    </tr>
                    </tbody>
                    @foreach($row_data["lease_list"] as $row_lease_data)
                        @if($row_lease_data['comment']!="")
                            <tbody>
                            <tr>
                                <td colspan="2">
                                    {{ $row_lease_data['unit_code'] }} - {{ $row_lease_data['lease_name'] }}
                                    <br/>
                                    {{ $row_lease_data['comment'] }}
                                </td>
                            </tr>
                            </tbody>
                        @endif
                    @endforeach
                @endif
            @endforeach
        @endif
        @if($expenses_comment_page_flag)
            <tbody>
            <tr>
                <td colspan="2">&nbsp;</td>
            </tr>
            <tr>
                <td colspan="2" class="title"><strong>Expenses</strong></td>
            </tr>
            </tbody>
            @foreach($expenses_budget_arr as $row_data)
                @if($row_data["comment_flag"])
                    <tbody>
                    <tr>
                        <td colspan="2"><strong>{{ $row_data['account_sub_category_description'] }}</strong></td>
                    </tr>
                    </tbody>
                    @foreach($row_data["account_list"] as $row_account_data)
                        @if($row_account_data['comment']!="")
                            <tbody>
                            <tr>
                                <td>{{ $row_account_data['account_code'] }}
                                    - {{ $row_account_data['account_name'] }}</td>
                                <td>{{ $row_account_data['comment'] }}</td>
                            </tr>
                            </tbody>
                        @endif

                    @endforeach
                @endif
            @endforeach
        @endif

    </table>
</main>

</body>


