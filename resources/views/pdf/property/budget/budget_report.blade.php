<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<style>
    @page {
        margin: 2px;
    }

    body {
        font-family: Helvetica-Bold, Arial, sans-serif !important;
        font-size: 9px !important;
        margin: 10px;
        margin-bottom: 50px !important;
    }

    header {
        position: fixed;
        top: 0px;
        left: 0px;
        right: 0px;
        padding: 10px
    }

    footer {
        position: fixed;
        bottom: 0px;
        left: 0px;
        right: 0px;
        height: 50px;

        /** Extra personal styles **/
        text-align: center;
        line-height: 35px;
    }

    .page-break {
        page-break-after: always;
    }

    .report-title {
        font-size: 12px !important;
        color: #004472 !important;
    }

    .report-footer td {
        font-size: 6px !important;
        font-family: Helvetica, Arial, sans-serif !important;
        width: 33%;
    }

    #pdf-table .subHeader {
        color: white;
        background: #004472;
        font-size: 9px !important;
    }

    .subHeader th {
        padding-top: 4px !important;
        padding-bottom: 2px !important;
        font-family: Helvetica-Bold, <PERSON>l, sans-serif !important;
    }

    #pdf-table td, #pdf-table th {
        border-bottom: 1px solid #ececec;
        padding: 1px;
    }

    .center {
        text-align: center;
    }

    .right {
        text-align: right;
    }

    .sub-total-row td {
        background: #e2e2e2 !important;
    }

    .title {
        background: #f0f0f0 !important;
    }

    .sub-total-row td {
        border-bottom: 1px solid #ececec !important;
    }

    .header-logo {
        height: auto;
    }

    .page-number {
        position: absolute;
        right: 10px;
    }

    .page-number:before {
        content: "Page " counter(page);
    }
</style>
<body>
<footer>
    <table width="100%" cellpadding="0" cellspacing="0">
        <tr class="report-footer">
            <td class="center">&nbsp;</td>
            <td class="center">Powered by Cirrus8 Software</td>
            <td class="center">
                <span>cirrus8\{{ $date }}\{{ $doc_type }}</span>
                <span class="page-number"></span>
            </td>
        </tr>
    </table>

</footer>

<main>
    <table id="pdf-table" width="100%" cellpadding="0" cellspacing="0">
        <thead>
        <tr>
            <th colspan="6" valign="top">
                <div class="report-title">INCOME & EXPENDITURE BUDGET</div>
                <div><strong>for year ending {{ $day }} {{ $month_name }} {{ $financial_year }}</strong></div>
                <div>for {{ $property_name }} ({{ $property_code }})</div>
                <br/>
                <br/>
            </th>
            <th colspan="10" class="right" valign="top">
                @if($logo_height==0)
                    <img class="header-logo" src="{!! $company_header_logo_path !!}" alt="Logo"
                         width="{{ $logo_width }}px">
                @else
                    <img src="{!! $company_header_logo_path !!}" alt="Logo" width="{{ $logo_width }}px"
                         height="{{ $logo_height }}px">
                @endif
            </th>
        </tr>
        <tr class="subHeader">
            <th class="center">Unit</th>
            <th class="center">Code</th>
            <th>Description</th>
            @for($period=1; $period <= 12; $period++)
                <th class="center">{{ $period_label[$period] }}</th>
            @endfor
            <th class="center">Total</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="16" class="title"><strong>INCOME</strong></td>
        </tr>
        </tbody>
        @foreach($income_budget_arr as $row_data)
            <tbody>
            <tr>
                <td colspan="3"><strong>{{ $row_data['account_name'] }}</strong></td>
                <td class="right" colspan="13"></td>
            </tr>
            </tbody>
            @foreach($row_data["lease_list"] as $row_lease_data)
                <tbody>
                <tr>
                    <td class="center">{{ $row_lease_data['unit_code'] }}</td>
                    <td class="center">{{ $row_data['account_code'] }}</td>
                    <td>{{ $row_lease_data['lease_name'] == '' ? 'VACANT' : $row_lease_data['lease_name'] }}</td>
                    @for($period=1; $period <= 12; $period++)
                        <td class="right">{{ $row_lease_data["lease_period"][$period]["value"] }}</td>
                    @endfor
                    <td class="right"><strong>{{ $row_lease_data["lease_total"]  }}</strong></td>
                </tr>
                </tbody>
            @endforeach
            <tbody>
            <tr class="sub-total-row">
                <td></td>
                <td></td>
                <td><strong>Subtotal</strong></td>
                @for($period=1; $period <= 12; $period++)
                    <td class="right"><strong>{{ $row_data['total_period_'.$period] }}</strong></td>
                @endfor
                <td class="right"><strong>{{ $row_data['total_period'] }}</strong></td>
            </tr>
            </tbody>
        @endforeach
        <tbody>
        <tr class="sub-total-row">
            <td class="center"></td>
            <td class="center"></td>
            <td><strong>Total Income</strong></td>
            @for($period=1; $period <= 12; $period++)
                <td class="right"><strong>{{ eval('echo $total_income_period_'.$period.';') }}</strong></td>
            @endfor
            <td class="right"><strong>{{ $total_income_period }}</strong></td>
        </tr>
        </tbody>
        <tbody>
        <tr>
            <td colspan="16">&nbsp;</td>
        </tr>
        <tr>
            <td colspan="16" class="title"><strong>EXPENSES</strong></td>
        </tr>
        </tbody>
        @if($report_expenses_group === "None")
            @foreach($expenses_budget_arr as $row_data)
                @foreach($row_data["account_list"] as $row_account_data)
                    <tbody>
                    <tr>
                        <td class="center"></td>
                        <td class="center">{{ $row_account_data['account_code'] }}</td>
                        <td>{{ $row_account_data['account_name'] }}</td>
                        @for($period=1; $period <= 12; $period++)
                            <td class="right">{{ $row_account_data["period_list"][$period]["value"] }}</td>
                        @endfor
                        <td class="right"><strong>{{ $row_account_data['account_total'] }}</strong></td>
                    </tr>
                    </tbody>
                @endforeach
            @endforeach
        @else
            @foreach($expenses_budget_arr as $row_data)
                <tbody>
                <tr>
                    <td colspan="3">
                        <strong>{{ str_replace("V/o","V/O",ucwords(strtolower($row_data['account_sub_category_description']))) }}</strong>
                    </td>
                    <td class="right" colspan="13"></td>
                </tr>
                </tbody>
                @foreach($row_data["account_list"] as $row_account_data)
                    <tbody>
                    <tr>
                        <td class="center"></td>
                        <td class="center">{{ $row_account_data['account_code'] }}</td>
                        <td>{{ $row_account_data['account_name'] }}</td>
                        @for($period=1; $period <= 12; $period++)
                            <td class="right">{{ $row_account_data["period_list"][$period]["value"] }}</td>
                        @endfor
                        <td class="right"><strong>{{ $row_account_data['account_total'] }}</strong></td>
                    </tr>
                    </tbody>
                @endforeach
                <tbody>
                <tr class="sub-total-row">
                    <td></td>
                    <td></td>
                    <td><strong>Subtotal</strong></td>
                    @for($period=1; $period <= 12; $period++)
                        <td class="right"><strong>{{ $row_data['total_period_'.$period] }}</strong></td>
                    @endfor
                    <td class="right"><strong>{{ $row_data['total_period'] }}</strong></td>
                </tr>
                </tbody>
            @endforeach
        @endif

        <tbody>
        <tr class="sub-total-row">
            <td class="center"></td>
            <td class="center"></td>
            <td><strong>Total Expenses</strong></td>
            @for($period=1; $period <= 12; $period++)
                <td class="right"><strong>{{ eval('echo $total_expenses_period_'.$period.';') }}</strong></td>
            @endfor
            <td class="right"><strong>{{ $total_expenses_period }}</strong></td>
        </tr>
        </tbody>
        <tbody>
        <tr class="sub-total-row">
            <td class="center"></td>
            <td class="center"></td>
            <td><strong>Net Income</strong></td>
            @for($period=1; $period <= 12; $period++)
                <td class="right">
                    <strong>{{ eval('echo number_format(str_replace(",","",$total_income_period_'.$period.') - str_replace(",","",$total_expenses_period_'.$period.'), 2, ".", ",");') }}</strong>
                </td>
            @endfor
            <td class="right">
                <strong>{{ number_format(str_replace(",","",$total_income_period) - str_replace(",","",$total_expenses_period), 2, '.', ',') }}</strong>
            </td>
        </tr>
        </tbody>

    </table>
</main>
</body>


