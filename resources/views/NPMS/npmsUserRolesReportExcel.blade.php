<html>
@if(isset($with_header_view) && $with_header_view)
    <table class="table">
        <thead>
        @if(isset($title) && $title)
            <tr>
                <th colspan="5">{{$title}}</th>
            </tr>
        @endif
        @foreach ($rows as $details)
            @php
                $cols_num = 3;
                $row_ctr = 0;
            @endphp
            @if(isset($header_columns))
                @foreach ($header_columns as $col)
                    @if(!isset($col['full']) || !$col['full'])
                        @if($row_ctr % $cols_num == 0 )
                            <tr>
                                @endif
                                @php
                                    $row_ctr++;
                                @endphp
                                <td>
                                    @if(!isset($col['nolabel']) || !$col['nolabel'])
                                        <span class="row-title">{{$col['label']}}:</span>
                                    @endif
                                    @if(isset($details['header'][$col['field']]) )
                                        <span class="row-subtitle">{{$details['header'][$col['field']]}}</span>
                                    @endif
                                </td>
                                @if($row_ctr % $cols_num == 0 )
                            </tr>
                        @endif
                    @endif
                @endforeach

                @foreach ($header_columns as $col)
                    @if(isset($col['full']) && $col['full'])
                        <tr>
                            <td colspan="{{count($header_columns)}}">
                                @if(!isset($col['nolabel']) || !$col['nolabel'])
                                    <span class="row-title">{{$col['label']}}:</span>
                                @endif
                                @if(isset($details['header'][$col['field']]) )
                                    <span class="row-subtitle">{{$details['header'][$col['field']]}}</span>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach
            @endif
            @if(isset($header_sub_table) && count($header_sub_table) > 0)
                @foreach ($header_sub_table as $values)
                    <tr>
                        @foreach ($values as $val)
                            <td>{{$val}}</td>
                        @endforeach
                    </tr>
                @endforeach
            @endif
            @if(isset($columns))
                <tr>
                    @foreach ($columns as $col)
                        <th>{{ $col['label'] }}</th>
                    @endforeach
                </tr>
            @endif
        @endforeach
        </thead>
        <tbody>
        @foreach ($rows as $details)
            @foreach ($details['lines'] as $row)
                <tr>
                    @foreach ($columns as $col)
                        @if(isset($row[$col['field']]))
                            <td>{{ $row[$col['field']] }}</td>
                        @else
                            <td></td>
                        @endif

                    @endforeach
                </tr>
            @endforeach
        @endforeach
        </tbody>
    </table>
@else
    <table class="table">
        @if(isset($columns))
            <thead>
            <tr>
                @foreach ($columns as $col)
                    <th>{{ $col['label'] }}</th>
                @endforeach
            </tr>
            </thead>
        @endif
        @if(isset($columns) && isset($grouped))
            <tbody>
            @foreach ($grouped as $grp)
                <tr>
                    <td colspan="{{count($columns)}}">{{ $grp['title'] }}</td>
                </tr>
                @foreach ($grp['rows'] as $row)
                    <tr>
                        @foreach ($columns as $col)
                            @if(isset($row[$col['field']]))
                                <td>{{ $row[$col['field']] }}</td>
                            @endif
                        @endforeach
                    </tr>
                @endforeach
            @endforeach
            </tbody>
        @elseif(isset($columns) && isset($rows))
            <tbody>
            @foreach ($rows as $row)
                <tr>
                    @foreach ($columns as $col)
                        @if(isset($row[$col['field']]))
                            <td>{{ $row[$col['field']] }}</td>
                        @endif
                    @endforeach
                </tr>
            @endforeach
            </tbody>
        @endif
    </table>
@endif
</html>