<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->

    <link rel="shortcut icon" type="image/png" href="{{asset('favicon.ico')}}"/>
    <title>{{ config('app.name', 'Laravel') }}</title>
    <!-- Styles -->
    {{--    <link href="{{ asset('css/app.css') }}" rel="stylesheet">--}}
    <link href="{{ url (mix('/vuejs/css/app.css')) }}" rel="stylesheet">
    <link href="{{ asset('semantic-ui/semantic.css') }}" rel="stylesheet">
    <link href="{{ asset('multiselect-master/css/jquery.uix.multiselect.css') }}" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900|Material+Icons' rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@3.x/css/materialdesignicons.min.css" rel="stylesheet">
    <!-- Scripts -->

    {{--    <script src="{{ asset('js/app.js') }}" defer></script>--}}

</head>
<body>
<div id="app">
    <v-app>
        <main class="">
            @yield('content')
        </main>
    </v-app>
</div>
<script src="{{ url (mix('/vuejs/js/app.js')) }}" type="text/javascript"></script>
</body>

</html>
