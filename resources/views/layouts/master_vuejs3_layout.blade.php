<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->

    <link rel="shortcut icon" type="image/png" href="{{asset('favicon.ico')}}"/>
    <title>{{ config('app.name', 'Cirrus8') }}</title>
    <!-- Styles -->
    <link href="{{ url (mix('/vuejs/css/cirrus-main-style.css')) }}" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900|Material+Icons' rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@6.x/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.0.13/css/all.css" rel="stylesheet">
    <!-- Scripts -->
    @if(isset($main_css) AND $main_css != '')
        @if(is_array($main_css))
            @for($index=0; $index <= sizeof($main_css)-1; $index++)
                <link href="{{ url (mix('/vuejs/css/'.$main_css[$index].'.css')) }}" rel="stylesheet">
            @endfor
        @else
            <link href="{{ url (mix('/vuejs/css/'.$main_css.'.css')) }}" rel="stylesheet">
        @endif

    @endif
</head>
<body>
<div id="app">
    <v-app>
        <main class="">
            @yield('content')
        </main>
    </v-app>
</div>
<script src="{{ url (mix('/vuejs/js/'.$main_js.'.js')) }}" type="text/javascript"></script>
</body>

</html>
