Hello,<br/>
<br/>
The transaction submitted for lease <strong>{{ $leaseID }}</strong> on property
<strong>{{ $propertyID }}</strong> has been approved.<br/>
<br/>
<ul>
    @if ((isset($notes)) && (count($notes) > 0))
    Notes: <br>
    @foreach ($notes as $reason)
    <li>{{ $reason }}</li>
    @endforeach
    @endif
</ul>
<br/>
<b>Charge / Rent Review</b>
<hr/>
<table class="data-grid" cellspacing="0" cellpadding="3" border="0">
    <tr class="">
        <td class="title">Property</td>
        <td><b>{{ $propertyID }}</b> - {{ $propertyName }}</td>
    </tr>
    <tr class="">
        <td class="title">Lease</td>
        <td><b>{{ $leaseID }}</b> - {{ $leaseName }}</td>
    </tr>
    <tr class="">
        <td class="title">Owner</td>
        <td><b>{{ $ownerName }}</b> ({{ $countryDefault['business_label'] }}
            : @if($clientCountry == 'AU') {{ ($ownerABN) }} @else
            {{ $countryDefault['business_prefix'].$ownerABN }} @endif
            )
        </td>
    </tr>
    <tr class="">
        <td class="title">Invoice Number</td>
        <td><b>{{ $invoiceNumber }}</b> (If an invoice number doesnt exist, no interim invoice was generated)
        </td>
    </tr>
    <tr class="">
        <td class="title">Created by</td>
        <td><b>{{ $createUser }}</b> on <b>{{ $createDate }}</b></td>
    </tr>
</table>
<hr/>
<br/>
<table class="data-grid" cellspacing="0" cellpadding="3" border="0">
    <tr class="fieldDescription">
        <th>Account</th>
        <th>Transaction Date</th>
        <th>Description</th>
        <th>Net Amount</th>
        <th>Tax Amount</th>
        <th>Gross Amount</th>
    </tr>
    @if ($chargeList->count() > 0)
    @foreach ($chargeList as $key => $charge)
    <tr class="">
        <td>{{ $charge['accountID'] }}</td>
        <td>{{ $charge['transactionDate'] }}</td>
        <td>{{ $charge['description'] }}</td>
        <td>{{ $countryHelper->toMoney($charge['netAmount']) }}</td>
        <td>{{ $countryHelper->toMoney($charge['taxAmount']) }}</td>
        <td>{{ $countryHelper->toMoney($charge['transactionAmount']) }}</td>
    </tr>
    @endforeach
    @endif
</table><br/><br/>
Thank You,<br/>
<font color="#00BAF2">cirrus8</font><br/>
<br/>