<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=7;FF=3;OtherUA=4"/>
    <title>Email</title>
    <style type="text/css"><!--
        body {
            text-align: center;
            background-color: #fafafa;
            font-family: tahoma;
            font-size: 11px;
            line-height: 18px;
        }

        a {
            font-size: 13px;
            text-decoration: none;
            border-bottom: 1px dotted silver;
            color: silver;
        }

        a:hover {
            color: #2b3137;
        }

        table.main {
            text-align: left;
            width: 600px;
            margin: 0 auto;
        }

        table, td {
            text-align: left;
        }

        td.title {
            font-weight: bold;
            background: #3489A1;
            color: white;
            font-size: 14px;
        }

        td.content {
            background: white;
            color: #6a7077;
        }

        td.disclaimer {
            border-top: 1px solid #ececec;
            color: #999;
            line-height: 12px;
            font-size: 10px;
        }

        h1 {
            font-size: 1.2em;
            font-weight: normal;
            border-bottom: 1px solid #ececec;
            margin: 1em 0em;
        }

        h1 b {
            color: #00589e
        }

        h2 {
            font-size: 1.1em;
            color: gray;
            font-weight: normal;
        }

        div.border {
            padding: 2px;
            width: 600px;
            background: #171d22;
            border: 1px solid #3c4349;
        }

        --></style>
</head>
<body>
<br/><br/>
<center>
    <img width="30%" src="{{$client_logo}}" alt="" id=""/>
</center>
<br/><br/>
<table class="main" cellpadding="10" cellspacing="0" border="0">
    <tr>
        <td class="content">
            Hello,<br/>
            <br/>
            The Property <strong>{{ $propertyID }}</strong> has been processed and added to the system. Any notes
            relevant shown below.<br/>
            <br/>
            @if(isset($comment))
                <ul>
                    <li>{{$comment}}</li>
                </ul>
            @endif
            <br/>
            If you would like to view the lease and are currently logged in, please <a
                    href="{{ url( env('HTTPHOST').'/framework/index.php?module=leases&command=lease_page_v2&property_code='.$propertyID) }}">click
                here</a>
            <br/><br/>
            Thank You,<br/>
            <font color="#00BAF2">cirrus8</font><br/>
            <br/>
        </td>
    </tr>
    @if(View::exists($email_signature))
        @component($email_signature)
        @endcomponent
    @endif
</table>
</body>

</html>