<html>
<table class="table">
    @if(isset($theaders))
        @foreach ($theaders as $thds)
            <tr class="table-headers">
                @foreach ($thds as $th)
                    <td colspan="{{isset($th['colspan']) ? $th['colspan'] : 1}}"
                        class="{{isset($th['align']) ? 'text-'.$th['align'] : 'text-left' }}">{!! $th['text'] !!}</td>
                @endforeach
            </tr>
        @endforeach
    @endif
    @if(isset($columns))
        <thead>
        <tr>
            @foreach ($columns as $col)
                <th>{{ $col['label'] }}</th>
            @endforeach
        </tr>
        </thead>
    @endif
    @if(isset($columns) && isset($grouped))
        <tbody>
        @foreach ($grouped as $grp)
            <tr>
                <td colspan="{{count($columns)}}">{{ $grp['title'] }}</td>
            </tr>
            @foreach ($grp['rows'] as $row)
                <tr>
                    @foreach ($columns as $col)
                        @if(isset($row[$col['field']]))
                            <td>{{ $row[$col['field']] }}</td>
                        @endif
                    @endforeach
                </tr>
            @endforeach
        @endforeach
        </tbody>
    @elseif(isset($columns) && isset($rows))
        <tbody>
        @foreach ($rows as $row)
            <tr>
                @foreach ($columns as $col)
                    @if(isset($row[$col['field']]))
                        <td>{{ $row[$col['field']] }}</td>
                    @endif
                @endforeach
            </tr>
        @endforeach
        </tbody>
    @endif
    @if(isset($thfooters))
        @foreach ($thfooters as $thds)
            <tr class="table-footers">
                @foreach ($thds as $th)
                    <td colspan="{{isset($th['colspan']) ? $th['colspan'] : 1}}"
                        class="{{isset($th['align']) ? 'text-'.$th['align'] : 'text-left' }}">{!! $th['text'] !!}</td>
                @endforeach
            </tr>
        @endforeach
    @endif
</table>
</html>