<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <link rel="stylesheet" href="{{ public_path('css/c8-report.css') }}">
    <style>
        @page {
            margin: 150px 5px 40px 5px;
        }

        header {
            position: fixed;
            top: -150px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 150px;
        }

        footer {
            position: fixed;
            bottom: -40px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 50px;
        }
    </style>
</head>
<body>
<header>
    <div class="header">
        @if(isset($header))
            @if(isset($header['title']))
                <div class="title">{!! $header['title'] !!}</div>
            @endif
            @if(isset($header['subtitle']))
                <div class="subtitle">{!! $header['subtitle'] !!}</div>
            @endif
            @if(isset($header['mintitle']))
                <div class="mintitle">{!! $header['mintitle'] !!}</div>
            @endif
            @if(isset($header['textlines']) && is_array($header['textlines']))
                @foreach ($header['textlines'] as $tl)
                    <div class="subtitle-light">{!! $tl !!}</div>
                @endforeach
            @endif
        @endif
        @if(isset($logo))
            <div class="logo"><img src="{{$logo}}"/></div>
        @endif
    </div>
</header>
<footer>
    <div class="footer">
        <table class="copyright">
            <tr>
                <td class="text-left">&nbsp;</td>
                <td class="text-center">Powered by Cirrus8 Software</td>
                <td class="text-right">{!! $footer['reference'] !!}</td>
            </tr>
        </table>
    </div>
</footer>
<main>
    <div class="body">
        <table class="table-list">
            @if(isset($theaders))
                @foreach ($theaders as $thds)
                    <tr class="table-headers">
                        @foreach ($thds as $th)
                            <td colspan="{{isset($th['colspan']) ? $th['colspan'] : 1}}"
                                class="{{isset($th['align']) ? 'text-'.$th['align'] : 'text-left' }}">{!! $th['text'] !!}</td>
                        @endforeach
                    </tr>
                @endforeach
            @endif
            @if(isset($columns))
                <thead>
                <tr>
                    @foreach ($columns as $col)
                        <th class="{{isset($col['align']) ? 'text-'.$col['align'] : 'text-left' }}">{!! $col['label'] !!}</th>
                    @endforeach
                </tr>
                </thead>
            @endif
            @if(isset($columns) && isset($grouped))
                <tbody>
                @foreach ($grouped as $grp)
                    <tr class="row-header">
                        <td colspan="{{count($columns)}}">{!! $grp['title'] !!}</td>
                    </tr>
                    @foreach ($grp['rows'] as $row)
                        <tr>
                            @foreach ($columns as $col)
                                @if(isset($row[$col['field']]))
                                    <td class="{{isset($col['align']) ? 'text-'.$col['align'] : 'text-left' }}">{!! $row[$col['field']] !!}</td>
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                @endforeach
                </tbody>
            @elseif(isset($columns) && isset($rows))
                <tbody>
                @foreach ($rows as $row)
                    <tr>
                        @foreach ($columns as $col)
                            @if(isset($row[$col['field']]))
                                <td class="{{isset($col['align']) ? 'text-'.$col['align'] : 'text-left' }}">{!! $row[$col['field']] !!}</td>
                            @endif
                        @endforeach
                    </tr>
                @endforeach
                </tbody>
            @endif
            @if(isset($thfooters))
                @foreach ($thfooters as $thds)
                    <tr class="table-footers">
                        @foreach ($thds as $th)
                            <td colspan="{{isset($th['colspan']) ? $th['colspan'] : 1}}"
                                class="{{isset($th['align']) ? 'text-'.$th['align'] : 'text-left' }}">{!! $th['text'] !!}</td>
                        @endforeach
                    </tr>
                @endforeach
            @endif
        </table>
    </div>
</main>
</body>
</html>
