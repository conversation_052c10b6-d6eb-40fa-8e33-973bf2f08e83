<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <link rel="stylesheet" href="{{ public_path('css/FM/report.css') }}">
    <style>
        @page {
            margin: 150px 20px 50px 20px;
        }

        header {
            position: fixed;
            top: -150px;
            left: 0px;
            right: 0px;
            background-color: transparent;
            height: 150px;
        }

        footer {
            position: fixed;
            bottom: -50px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 50px;
        }

        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
<header>
    <div class="header">
        @if(isset($header))
            @if(isset($header['title']))
                <div class="title">{!! $header['title'] !!}</div>
            @endif
            @if(isset($header['subtitle']))
                <div class="subtitle heighter">{!! $header['subtitle'] !!}</div>
            @endif
            @if(isset($header['mintitle']))
                <div class="mintitle">{!! $header['mintitle'] !!}</div>
            @endif
        @endif
        @if(isset($logo))
            <div class="logo"><img src="{{$logo}}"/></div>
        @endif
    </div>
</header>
<footer>
    <div class="footer">
        <table class="copyright">
            <tr>
                <td class="text-left">&nbsp;</td>
                <td class="text-center">Powered by Cirrus8 Software</td>
                <td class="text-right">{!! $footer['reference'] !!}</td>
            </tr>
        </table>
    </div>
</footer>
<main>
    <div class="body">
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 80;
                $y = $pdf->get_height() - 24;
                //$text = "Page {PAGE_NUM} of {PAGE_COUNT}";
                $text = "Page {PAGE_NUM}";
                $pdf->page_text($x, $y, $text, null, 8, array(0.333,0.333,0.333));
            }
        </script>
        @if(isset($rows))
            @php
                $max_rows = count($rows);
                $ctr = 1;
                $max_rows_per_page = 30;
            @endphp
            @foreach ($rows as $property_code => $details)
                @php
                    $rows_per_page = 0;
                @endphp

                <table class="table-list" cellspacing="0">
                    @if( $rows_per_page % $max_rows_per_page == 0 )
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}<br/>
                </div>
                <table class="table-list">
                    @endif
                    <thead>
                    <tr>
                        <th style="width:50px;text-align:left;">Code</th>
                        <th style="width:150px;text-align:left;">Description</th>
                        <th style="width:100px;text-align:left;">Type</th>
                        <th style="width:30px;text-align:left;">Acquire Date</th>
                        <th style="width:30px;text-align:left;">Install Date</th>
                        <th style="width:30px;text-align:right;">Life Span (days)</th>
                        <th style="width:30px;text-align:left;">Replace Date</th>
                        <th style="width:30px;text-align:right;">Replace Amount</th>
                        <th style="width:80px;text-align:left;">Make</th>
                        <th style="width:80px;text-align:left;">Model</th>
                        <th style="width:80px;text-align:left;">Serial No.</th>
                        <th style="width:50px;text-align:left;">Status</th>
                    </tr>
                    </thead>
                    @php $rows_per_page++; @endphp
                    @foreach ($details['lines'] as $ln)
                        @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                            <tr>
                                <td class="sub-line-header" colspan="12">{!! $ln['description'] !!}</td>
                            </tr>
                        @else
                            <tr>
                                <td style="width:50px;text-align:left;">{{$ln['code']}}</td>
                                <td style="width:150px;text-align:left;">{{$ln['description']}}</td>
                                <td style="width:100px;text-align:left;">{{$ln['type']}}</td>
                                <td style="width:30px;text-align:left;">{{$ln['acquired_date']}}</td>
                                <td style="width:30px;text-align:left;">{{$ln['install_date']}}</td>
                                <td style="width:30px;text-align:right;">{{$ln['life_span_days']}}</td>
                                <td style="width:30px;text-align:left;">{{$ln['replace_date']}}</td>
                                <td style="width:30px;text-align:right;">{{$ln['replace_value']}}</td>
                                <td style="width:80px;text-align:left;">{{$ln['make']}}</td>
                                <td style="width:80px;text-align:left;">{{$ln['model']}}</td>
                                <td style="width:80px;text-align:left;">{{$ln['serial_no']}}</td>
                                <td style="width:50px;text-align:left;">{{$ln['status']}}</td>
                            </tr>
                        @endif
                        @php $rows_per_page++; @endphp
                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endforeach
                </table>
                @if( $ctr < $max_rows)
                    <div class="page-break"></div>
                @endif

                @php
                    $ctr++;
                @endphp

            @endforeach
        @endif
    </div>
</main>
</body>
</html>
