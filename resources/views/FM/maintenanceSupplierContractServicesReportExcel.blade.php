<html>
<table class="table">
    @foreach ($rows as $supplier_code => $details)
        <tr class="line-header">
            <td colspan="6">{{$details['supplier_desc']}}</td>
        </tr>
        <tr class="line-header">
            <td colspan="6">{{$details['address']}}</td>
        </tr>
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
        @if( isset($details['lines']) && count($details['lines']) > 0 )
            <tr class="line-header">
                <td colspan="6">Service Contracts</td>
            </tr>
            <tr>
                <th>Contract #</th>
                <th>Property</th>
                <th>Service</th>
                <th>Description</th>
                <th>Date Period</th>
                <th>Status</th>
            </tr>
            @foreach ($details['lines'] as $ln)
                @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                    <tr class="line-subheader">
                        <td colspan="6">{!! $ln['description'] !!}</td>
                    </tr>
                @else
                    <tr>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['contract_no']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['property_code']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['service']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['description']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['from_date']}}
                            - {{$ln['to_date']}} </td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{!! $ln['status'] !!}</td>
                    </tr>

                    @if( isset($ln['lines']) && count($ln['lines']) > 0 )
                        <tr class="subcolumn-header">
                            <th>Workorder #</th>
                            <th colspan="2">Description</th>
                            <th colspan="2">Status</th>
                            <th>Order Date</th>
                        </tr>
                        @foreach ($ln['lines'] as $conln)
                            <tr>
                                <td>{{$conln['ref_no']}}</td>
                                <td colspan="2">{{$conln['description']}}</td>
                                <td colspan="2">{{$conln['status']}}</td>
                                <td>{{$conln['ordered_at']}}</td>
                            </tr>
                        @endforeach
                    @elseif(isset($ln['no_lines']) && $ln['no_lines'])
                        <tr class="line-subheader">
                            <td colspan="6" class="text-center">No transactions found</td>
                        </tr>
                    @endif
                    <tr class="spacer">
                        <td colspan="6"></td>
                    </tr>
                    <tr class="spacer">
                        <td colspan="6"></td>
                    </tr>
                @endif
            @endforeach
        @endif
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
    @endforeach
</table>
</html>