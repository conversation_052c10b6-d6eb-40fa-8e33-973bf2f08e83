<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Cirrus8</title>
    <style>
        /* -------------------------------------
            INLINED WITH htmlemail.io/inline
        ------------------------------------- */
        /* -------------------------------------
            RESPONSIVE AND MOBILE FRIENDLY STYLES
        ------------------------------------- */
        @media only screen and (max-width: 620px) {
            table[class=body] h1 {
                font-size: 28px !important;
                margin-bottom: 10px !important;
            }

            table[class=body] p,
            table[class=body] ul,
            table[class=body] ol,
            table[class=body] td,
            table[class=body] span,
            table[class=body] a {
                font-size: 16px !important;
            }

            table[class=body] .wrapper,
            table[class=body] .article {
                padding: 10px !important;
            }

            table[class=body] .content {
                padding: 0 !important;
            }

            table[class=body] .container {
                padding: 0 !important;
                width: 100% !important;
            }

            table[class=body] .main {
                border-left-width: 0 !important;
                border-radius: 0 !important;
                border-right-width: 0 !important;
            }

            table[class=body] .btn table {
                width: 100% !important;
            }

            table[class=body] .btn a {
                width: 100% !important;
            }

            table[class=body] .img-responsive {
                height: auto !important;
                max-width: 100% !important;
                width: auto !important;
            }
        }

        /* -------------------------------------
            PRESERVE THESE STYLES IN THE HEAD
        ------------------------------------- */
        @media all {
            .ExternalClass {
                width: 100%;
            }

            .ExternalClass,
            .ExternalClass p,
            .ExternalClass span,
            .ExternalClass font,
            .ExternalClass td,
            .ExternalClass div {
                line-height: 100%;
            }

            .apple-link a {
                color: inherit !important;
                font-family: inherit !important;
                font-size: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
                text-decoration: none !important;
            }

            .btn-primary table td:hover {
                background-color: rgba(0, 186, 242, 0.1) !important;
            }

            .btn-primary a:hover {
                background-color: rgba(0, 186, 242, 0.1) !important;
                border-color: #00BAF2 !important;
            }
        }
    </style>
</head>
<body class=""
      style="background-color: #EEF5F9; color:#666666; font-family: sans-serif; -webkit-font-smoothing: antialiased; font-size: 14px; line-height: 1.4; margin: 0; padding: 0; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;">
<table border="0" cellpadding="0" cellspacing="0" class="body"
       style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background-color: #EEF5F9;">
    <tr>
        <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;">&nbsp;</td>
        <td class="container"
            style="font-family: sans-serif; font-size: 14px; vertical-align: top; display: block; Margin: 0 auto; max-width: 580px; padding: 10px; width: 580px;">
            <div class="content"
                 style="box-sizing: border-box; display: block; Margin: 0 auto; max-width: 580px; padding: 10px;">

                <!-- START CENTERED WHITE CONTAINER -->
                <table class="main"
                       style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background: #ffffff; border-radius: 3px;">
                    <tr>
                        <td style="background-color: #00BAF2;padding: 10px 15px;text-align: center;color: #ffffff;font-size:26px;font-weight:600"
                            colspan="100%">
                            <span>cirrus8</span>
                        </td>
                    </tr>
                    <!-- START MAIN CONTENT AREA -->
                    <tr>
                        <td class="wrapper"
                            style="font-family: sans-serif; font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 20px;">
                            <table border="0" cellpadding="0" cellspacing="0"
                                   style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;">
                                <tr>
                                    <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;">
                                        <table border="0" cellpadding="0" cellspacing="0" class="body"
                                               style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;">
                                            <tr>
                                                <td valign="top">
                                                    @if(isset($content->main->titles))
                                                        @foreach($content->main->titles as $cont)
                                                            <div><b>{!! $cont !!}</b></div>
                                                        @endforeach
                                                    @endif
                                                    <br/>
                                                    @if(isset($content->main->subtitles))
                                                        <div>
                                                            <table border="0" cellpadding="0" cellspacing="0"
                                                                   class="body"
                                                                   style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;font-size:11px;">
                                                                @foreach($content->main->subtitles as $cont)
                                                                    <tr>
                                                                        @if(!is_array($cont))
                                                                            <td>{!! $cont !!}</td>
                                                                        @else
                                                                            @foreach($cont as $con => $tent)
                                                                                <td style="max-width:60px;">{!! $con !!}</td>
                                                                                <td>{!! $tent !!}</td>
                                                                @endforeach
                                                                @endif
                                                                @endforeach
                                                            </table>
                                                        </div>
                                                    @endif
                                                </td>
                                                @if(isset($content->main->logo))
                                                    <td>
                                                        <div style="text-align: right;">
                                                            <img src="{{$content->main->logo}}" width="150">
                                                        </div>
                                                    </td>
                                                @endif
                                            </tr>
                                        </table>
                                        <br/>
                                        <br/>
                                        @if(isset($content->main->blocks))
                                            @foreach($content->main->blocks as $details)
                                                <table border="0" cellpadding="0" cellspacing="0" class="body"
                                                       style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;border: 1px solid #DDDDDD;font-size:12px;">
                                                    <tr>
                                                        <td style="padding:10px;" valign="top">
                                                            @foreach($details as $det)
                                                                @foreach($det as $type => $cont)
                                                                    @if($type == 'text')
                                                                        <div>
                                                                            <div style="white-space: pre-line;font-family: sans-serif; font-size: 12px; font-weight: normal; margin: 0;">{!! $cont !!}</div>
                                                                        </div>
                                                                    @elseif($type == 'subtext')
                                                                        <div style="font-size:10px;color:#AAAAAA;">{!! $cont !!}</div>
                                                                    @elseif($type == 'button' && isset($cont['label']) && isset($cont['value']) )
                                                                        <div style="text-align: center;margin:10px 0px;">
                                                                            <a id="_buttomTemp" target="_blank"
                                                                               href="{{$cont['value']}}"
                                                                               style="display: inline-block;font-decoration:none; color: #00BAF2; background-color: #ffffff; border: solid 1px #00BAF2; border-radius: 5px; box-sizing: border-box; cursor: pointer; text-decoration: none; font-size: 14px; font-weight: bold; margin: 0; padding: 10px 20px; text-transform: capitalize; border-color: #00BAF2;">
                                                                                {!! $cont['label'] !!}
                                                                            </a>
                                                                        </div>
                                                                    @elseif($type == 'preview' && isset($cont['url']) && isset($cont['type']))
                                                                        @if(in_array($cont['type'],['jpg','gif','jpeg','png']))
                                                                            <div style="display:inline-block;margin:5px;">
                                                                                <a href="{{$cont['url']}}"
                                                                                   target="_blank"
                                                                                   style="display:inline-block;text-align:center;">
                                                                                    <img src="{{$cont['url']}}"
                                                                                         height="100">
                                                                                    <!-- <div>{{$cont['name']}}</div> -->
                                                                                </a>
                                                                            </div>
                                                                        @else
                                                                            <div style="display:inline-block;margin:5px;">
                                                                                <a href="{{$cont['url']}}"
                                                                                   target="_blank"
                                                                                   style="display:inline-block;text-align:center;">
                                                                                    <img src="{{URL::asset('/images/icons/file-solid.png')}}"
                                                                                         height="100">
                                                                                    <!-- <div>{{$cont['name']}}</div> -->
                                                                                </a>
                                                                            </div>
                                                                        @endif
                                                                    @elseif($type == 'table')
                                                                        <table border="0" cellpadding="0"
                                                                               cellspacing="0" class="body"
                                                                               style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;font-size:11px;padding:10px 0px;">
                                                                            @foreach($cont as $rows)
                                                                                @foreach($rows as $label => $value)
                                                                                    <tr>
                                                                                        <td style="padding:3px 10px;text-align:right;width:100px;"
                                                                                            valign="top">{!! $label !!}</td>
                                                                                        <td style="padding:3px 10px;"
                                                                                            valign="top">
                                                                                            @if(is_array($value))
                                                                                                @foreach($value as $val)
                                                                                                    {!! $val !!}<br/>
                                                                                                @endforeach
                                                                                            @else
                                                                                                {!! $value !!}
                                                                                            @endif
                                                                                        </td>
                                                                                    </tr>
                                                                                @endforeach
                                                                            @endforeach
                                                                        </table>
                                                                    @elseif($type == 'columns')
                                                                        <table border="0" cellpadding="0"
                                                                               cellspacing="0" class="body"
                                                                               style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;font-size:11px;padding:10px 0px;">
                                                                            <tr>
                                                                                @foreach($cont as $key => $rows)
                                                                                    <td style="padding:10px;"
                                                                                        valign="top">
                                                                                        @foreach($rows as $row_details)
                                                                                            @foreach($row_details as $row_type => $value)
                                                                                                @if($row_type == 'subtext')
                                                                                                    <span style="font-size:10px;color:#AAAAAA;">{!! $value !!}</span>
                                                                                                    <br/>
                                                                                                @elseif($row_type == 'text')
                                                                                                    <span>{!! $value !!}</span>
                                                                                                    <br/>
                                                                                                @endif
                                                                                            @endforeach
                                                                                        @endforeach
                                                                                    </td>
                                                                                @endforeach
                                                                            </tr>
                                                                        </table>
                                                                    @elseif($type == 'lines')
                                                                        <table border="0" cellpadding="0"
                                                                               cellspacing="0" class="body"
                                                                               style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;font-size:11px;padding:10px 0px;">
                                                                            @foreach($cont as $key => $rows)
                                                                                @if($key != 0)
                                                                                    <tr>
                                                                                        <td colspan="2"
                                                                                            style="padding:5px 0px 10px 0px;border-top:1px solid #DDDDDD"></td>
                                                                                    </tr>
                                                                                @endif
                                                                                @foreach($rows as $label => $value)
                                                                                    <tr>
                                                                                        <td style="padding:3px 10px;text-align:right;width:100px;"
                                                                                            valign="top">{!! $label !!}</td>
                                                                                        <td style="padding:3px 10px;"
                                                                                            valign="top">
                                                                                            @if(is_array($value))
                                                                                                @foreach($value as $val)
                                                                                                    {!! $val !!}<br/>
                                                                                                @endforeach
                                                                                            @else
                                                                                                {!! $value !!}
                                                                                            @endif
                                                                                        </td>
                                                                                    </tr>
                                                                                @endforeach

                                                                            @endforeach
                                                                        </table>
                                                                    @endif
                                                                @endforeach
                                                            @endforeach
                                                        </td>
                                                    </tr>
                                                </table>
                                                <br/>
                                            @endforeach
                                        @endif

                                        @if(isset($content->footer))
                                            @foreach($content->footer as $text)
                                                <p style="white-space: pre-line;font-family: sans-serif; font-size: 12px; font-weight: normal; margin: 0;margin-bottom:10px;">
                                                    {!! $text !!}
                                                </p>
                                            @endforeach
                                            <br/>
                                            <br/>
                                        @endif

                                        <table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary"
                                               style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; box-sizing: border-box;">
                                            <tbody>
                                            <tr>
                                                <td align="left"
                                                    style="background-color:#f1f1f1;font-family: sans-serif; font-size: 14px; vertical-align: top; padding: 10px;">
                                                    <p style="font-family: sans-serif; font-size: 10px; font-weight: normal; margin: 0;">
                                                        Disclaimer:
                                                        <br>
                                                        This email and any files transmitted with it are confidential
                                                        and intended solely for the use of the individual or entity to
                                                        whom they are addressed. If you have received this email in
                                                        error, please notify your property management / facility
                                                        management contact. Finally, the recipient should check this
                                                        email and any attachments for the presence of viruses. Cirrus8
                                                        accepts no responsibility for any damage caused by any virus
                                                        transmitted by this email.
                                                    </p>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- END MAIN CONTENT AREA -->
                </table>

                <!-- START FOOTER -->
                <div class="footer" style="clear: both; Margin-top: 10px; text-align: center; width: 100%;">
                    <table border="0" cellpadding="0" cellspacing="0"
                           style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;">
                        <tr>
                            <td class="content-block"
                                style="font-family: sans-serif; vertical-align: top; padding-bottom: 10px; padding-top: 10px; font-size: 12px; color: #999999; text-align: center;">
                                <span class="apple-link" style="color: #999999; font-size: 12px; text-align: center;">Copyright © {{$content->copyright}} cirrus8 (cir8 Pty Ltd)</span>
                                <br> <a href="https://cirrus8.com.au"
                                        style="text-decoration: underline; color: #999999; font-size: 12px; text-align: center;">https://cirrus8.com.au</a>.
                            </td>
                        </tr>
                    </table>
                </div>
                <!-- END FOOTER -->

                <!-- END CENTERED WHITE CONTAINER -->
            </div>
        </td>
        <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;">&nbsp;</td>
    </tr>
</table>
</body>

</html>
