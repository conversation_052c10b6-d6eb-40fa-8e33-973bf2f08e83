<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <link rel="stylesheet" href="{{ public_path('css/FM/report.css') }}">
    <style>
        @page {
            margin: 150px 5px 50px 5px;
        }

        header {
            position: fixed;
            top: -150px;
            left: 0px;
            right: 0px;
            background-color: transparent;
            height: 150px;
        }

        footer {
            position: fixed;
            bottom: -50px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 50px;
        }

        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body class="cirrus8">
<header>
    <div class="header">
        @if(isset($header))
            @if(isset($header['title']))
                <div class="title">{!! $header['title'] !!}</div>
            @endif
            @if(isset($header['subtitle']))
                <div class="subtitle heighter">{!! $header['subtitle'] !!}</div>
            @endif
            @if(isset($header['mintitle']))
                <div class="mintitle">{!! $header['mintitle'] !!}</div>
            @endif
        @endif
        @if(isset($logo))
            <div class="logo"><img src="{{$logo}}"/></div>
        @endif
    </div>
</header>
<footer>
    <div class="footer">
        <table class="copyright">
            <tr>
                <td class="text-left">&nbsp;</td>
                <td class="text-center">Powered by Cirrus8 Software</td>
                <td class="text-right">{!! $footer['reference'] !!}</td>
            </tr>
        </table>
    </div>
</footer>
<main>
    <div class="body">
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 80;
                $y = $pdf->get_height() - 24;
                //$text = "Page {PAGE_NUM} of {PAGE_COUNT}";
                $text = "Page {PAGE_NUM}";
                $pdf->page_text($x, $y, $text, null, 8, array(0.333,0.333,0.333));
            }
        </script>
        @php
            $max_rows = count($rows);
            $ctr = 1;
            $max_rows_per_page = 29;
        @endphp
        @foreach ($rows as $property_code => $property)
            @php
                $rows_per_page = 0;
            @endphp
            <table class="table-list">
                <tbody>
                @if( $rows_per_page % $max_rows_per_page == 0 )
                    <tr>
                        <td colspan="9">
                            <div class="per-page-header-title">
                                <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                            </div>
                        </td>
                    </tr>
                @endif
                <tr class="row-title">
                    <td>Account Name</td>
                    <td>Payee</td>
                    <td>Invoice #</td>
                    <td>Workorder #</td>
                    <td>Asset Code</td>
                    <td>Asset Name</td>
                    <td style="text-align:right">Net {{ $currency_symbol }}</td>
                    <td style="text-align:right">{{ $tax_label }} {{
                  $currency_symbol }}</td>
                    <td style="text-align:right">Gross {{ $currency_symbol }}  </td>
                </tr>
                @php $rows_per_page++; @endphp
                @if( isset($property['operating_exp']['line_count']) && $property['operating_exp']['line_count'] > 0 )
                    @foreach ($property['operating_exp']['rows'] as $group_code => $group)
                        @if( isset($group['subgroups']) && count($group['subgroups']) > 0 )
                            <tr class="row-subtitle">
                                <td colspan="9"><strong>{{$group['title']}}</strong></td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                            @foreach ($group['subgroups'] as $subgroup_code => $subgroup)
                                <tr class="row-total">
                                    <td colspan="9"><strong>{{$subgroup['title']}}</strong></td>
                                </tr>
                                @php $rows_per_page++; @endphp
                                @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                    <tr>
                                        <td colspan="9">
                                            <div class="per-page-header-title">
                                                <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                            </div>
                                        </td>
                                    </tr>
                                @endif
                                @foreach ($subgroup['rows'] as $index => $row)
                                    <tr>
                                        <td>{{$row['account_desc']}}</td>
                                        <td>{{$row['payee']}}</td>
                                        <td>{{$row['invoice_no']}}</td>
                                        <td>{{$row['workorder_no']}}</td>
                                        <td>{{$row['asset_code']}}</td>
                                        <td>{{$row['asset_name']}}</td>
                                        <td style="text-align:right">{{number_format($row['net'],2)}}</td>
                                        <td style="text-align:right">{{number_format($row['gst'],2)}}</td>
                                        <td style="text-align:right">{{number_format($row['gross'],2)}}  </td>
                                    </tr>
                                    @php $rows_per_page++; @endphp
                                    @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                        <tr>
                                            <td colspan="9">
                                                <div class="per-page-header-title">
                                                    <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                                <tr class="row-total">
                                    <td colspan="6"><strong>Total {{$subgroup['title']}}</strong></td>
                                    <td style="text-align:right">
                                        <strong>{{number_format($subgroup['totals']['net'],2)}}</strong></td>
                                    <td style="text-align:right">
                                        <strong>{{number_format($subgroup['totals']['gst'],2)}}</strong></td>
                                    <td style="text-align:right">
                                        <strong>{{number_format($subgroup['totals']['gross'],2)}}</strong></td>
                                </tr>
                                @php $rows_per_page++; @endphp
                                @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                    <tr>
                                        <td colspan="9">
                                            <div class="per-page-header-title">
                                                <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                            </div>
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                            <tr class="row-total">
                                <td colspan="6"><strong>Total {{$group['title']}}</strong></td>
                                <td style="text-align:right">
                                    <strong>{{number_format($group['totals']['net'],2)}}</strong></td>
                                <td style="text-align:right">
                                    <strong>{{number_format($group['totals']['gst'],2)}}</strong></td>
                                <td style="text-align:right">
                                    <strong>{{number_format($group['totals']['gross'],2)}}</strong></td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        @endif
                    @endforeach
                    <tr class="row-total">
                        <td colspan="6"><strong>Total Operating Expenditure</strong></td>
                        <td style="text-align:right">
                            <strong>{{number_format($property['operating_exp']['totals']['net'],2)}}</strong></td>
                        <td style="text-align:right">
                            <strong>{{number_format($property['operating_exp']['totals']['gst'],2)}}</strong></td>
                        <td style="text-align:right">
                            <strong>{{number_format($property['operating_exp']['totals']['gross'],2)}}  </strong></td>
                    </tr>
                    @php $rows_per_page++; @endphp
                    @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                        <tr>
                            <td colspan="9">
                                <div class="per-page-header-title">
                                    <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                </div>
                            </td>
                        </tr>
                    @endif
                @endif
                @if( isset($property['others_exp']['line_count']) && $property['others_exp']['line_count'] > 0 )
                    @foreach ($property['others_exp']['rows'] as $group_code => $group)
                        @if( isset($group['subgroups']) && count($group['subgroups']) > 0 )
                            <tr class="row-subtitle">
                                <td colspan="9"><strong>{{$group['title']}}</strong></td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                            @foreach ($group['subgroups'] as $subgroup_code => $subgroup)
                                <tr class="row-total">
                                    <td colspan="9"><strong>{{$subgroup['title']}}</strong></td>
                                </tr>
                                @php $rows_per_page++; @endphp
                                @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                    <tr>
                                        <td colspan="9">
                                            <div class="per-page-header-title">
                                                <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                            </div>
                                        </td>
                                    </tr>
                                @endif
                                @foreach ($subgroup['rows'] as $index => $row)
                                    <tr>
                                        <td>{{$row['account_desc']}}</td>
                                        <td>{{$row['payee']}}</td>
                                        <td>{{$row['invoice_no']}}</td>
                                        <td>{{$row['workorder_no']}}</td>
                                        <td>{{$row['asset_code']}}</td>
                                        <td>{{$row['asset_name']}}</td>
                                        <td style="text-align:right">{{number_format($row['net'],2)}}</td>
                                        <td style="text-align:right">{{number_format($row['gst'],2)}}</td>
                                        <td style="text-align:right">{{number_format($row['gross'],2)}}  </td>
                                    </tr>
                                    @php $rows_per_page++; @endphp
                                    @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                        <tr>
                                            <td colspan="9">
                                                <div class="per-page-header-title">
                                                    <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                                <tr class="row-total">
                                    <td colspan="6"><strong>Total {{$subgroup['title']}}</strong></td>
                                    <td style="text-align:right">
                                        <strong>{{number_format($subgroup['totals']['net'],2)}}</strong></td>
                                    <td style="text-align:right">
                                        <strong>{{number_format($subgroup['totals']['gst'],2)}}</strong></td>
                                    <td style="text-align:right">
                                        <strong>{{number_format($subgroup['totals']['gross'],2)}}</strong></td>
                                </tr>
                                @php $rows_per_page++; @endphp
                                @if( $rows_per_page % $max_rows_per_page == 0  && $ctr !== 1 )
                                    <tr>
                                        <td colspan="9">
                                            <div class="per-page-header-title">
                                                <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                            </div>
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                        @endif
                    @endforeach
                @endif
                @if( (isset($property['operating_exp']['line_count']) && $property['operating_exp']['line_count'] > 0) || (isset($property['others_exp']['line_count']) && $property['operating_exp']['line_count'] > 0)  )
                    <tr class="row-total">
                        <td colspan="6"><strong>Total Expenditure</strong></td>
                        <td style="text-align:right"><strong>{{number_format($property['totals']['net'],2)}}</strong>
                        </td>
                        <td style="text-align:right"><strong>{{number_format($property['totals']['gst'],2)}}</strong>
                        </td>
                        <td style="text-align:right"><strong>{{number_format($property['totals']['gross'],2)}}</strong>
                        </td>
                    </tr>
                    @php $rows_per_page++; @endphp
                    @if( $rows_per_page % $max_rows_per_page == 0 && $ctr !== 1 )
                        <tr>
                            <td colspan="9">
                                <div class="per-page-header-title">
                                    <strong>{{$property['details']['name'] ." (".$property['details']['code'].")"}}</strong>
                                </div>
                            </td>
                        </tr>
                    @endif
                @endif
                </tbody>
            </table>
            @if($ctr < $max_rows)
                <div class="page-break"></div>
            @endif
            @php
                $ctr++;
            @endphp
        @endforeach
    </div>
</main>
</body>
</html>
