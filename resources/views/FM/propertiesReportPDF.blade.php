<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <link rel="stylesheet" href="{{ public_path('css/FM/report.css') }}">
    <style>
        @page {
            margin: 150px 20px 50px 20px;
        }

        header {
            position: fixed;
            top: -150px;
            left: 0px;
            right: 0px;
            background-color: transparent;
            height: 150px;
        }

        footer {
            position: fixed;
            bottom: -50px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 50px;
        }

        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
<header>
    <div class="header">
        @if(isset($header))
            @if(isset($header['title']))
                <div class="title">{!! $header['title'] !!}</div>
            @endif
            @if(isset($header['subtitle']))
                <div class="subtitle heighter">{!! $header['subtitle'] !!}</div>
            @endif
            @if(isset($header['mintitle']))
                <div class="mintitle">{!! $header['mintitle'] !!}</div>
            @endif
        @endif
        @if(isset($logo))
            <div class="logo"><img src="{{$logo}}"/></div>
        @endif
    </div>
</header>
<footer>
    <div class="footer">
        <table class="copyright">
            <tr>
                <td class="text-left">&nbsp;</td>
                <td class="text-center">Powered by Cirrus8 Software</td>
                <td class="text-right">{!! $footer['reference'] !!}</td>
            </tr>
        </table>
    </div>
</footer>
<main>
    <div class="body">
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 80;
                $y = $pdf->get_height() - 24;
                //$text = "Page {PAGE_NUM} of {PAGE_COUNT}";
                $text = "Page {PAGE_NUM}";
                $pdf->page_text($x, $y, $text, null, 8, array(0.333,0.333,0.333));
            }
        </script>
        @if(isset($rows))
            @php
                $max_rows = count($rows);
                $ctr = 1;
                $max_rows_per_page = 28;
            @endphp
            @foreach ($rows as $property_code => $details)
                @php
                    $rows_per_page = 0;
                @endphp

                <table class="table-list" cellspacing="0">
                    @if( $rows_per_page % $max_rows_per_page == 0 )
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}<br/>
                </div>
                <table class="table-list">
                    @endif

                    <tr class="line-title">
                        <td colspan="6">Preferred Contractors</td>
                    </tr>
                    @php $rows_per_page++; @endphp
                    @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif

                    <tr class="thead-line">
                        <td colspan="1" style="width:200px;text-align:left;">Contractor</td>
                        <td colspan="1" class="text-left">Primary Contact Name</td>
                        <td colspan="1" class="text-left">Primary Contact Email</td>
                        <td colspan="1" class="text-left">Primary Contact Mobile</td>
                        <td colspan="2" class="text-left">Primary Contact Phone</td>
                    </tr>

                    @php $rows_per_page++; @endphp
                    @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif

                    @if(isset($details['suppliers']) && count($details['suppliers']) > 0)
                        @foreach ($details['suppliers'] as $sp)
                            <tr>
                                <td colspan="1" style="width:200px;text-align:left;">{{$sp['code']}}
                                    - {{$sp['name']}}</td>
                                <td colspan="1" class="text-left">{{$sp['contact_name']}}</td>
                                <td colspan="1" class="text-left">{{$sp['contact_email']}}</td>
                                <td colspan="1" class="text-left">{{$sp['contact_mobile']}}</td>
                                <td colspan="2" class="text-left">{{$sp['contact_phone']}}</td>
                            </tr>

                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endforeach
                    @else
                        <tr>
                            <td colspan="6">None</td>
                        </tr>
                        @php $rows_per_page++; @endphp
                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endif

                    <tr class="line-title">
                        <td colspan="6">Repeating Workorders</td>
                    </tr>
                    @php $rows_per_page++; @endphp
                    @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif



                    @if(isset($details['repeatings']) && count($details['repeatings']) > 0)
                        <thead>
                        <tr>
                            <th style="width:200px;text-align:left;">#</th>
                            <th style="width:100px;text-align:left;">Contractor</th>
                            <th style="width:80px;text-align:left;">Tenant</th>
                            <th style="width:80px;text-align:left;">Repeat Every</th>
                            <th style="width:80px;text-align:left;">Date Period</th>
                            <th style="width:80px;text-align:left;">Next Date</th>
                        </tr>
                        </thead>
                        @php $rows_per_page++; @endphp
                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif

                    @foreach ($details['repeatings'] as $rp)
                        <tr>
                            <td style="width:200px;text-align:left;">{{$rp['ref_no']}}</td>
                            <td style="width:100px;text-align:left;">{{$rp['supplier_desc']}}</td>
                            <td style="width:80px;text-align:left;">{{$rp['tenant_desc']}}</td>
                            <td style="width:80px;text-align:left;">
                                {{$rp['repeat_every_num']}} {{$rp['type'] . "(s)"}}
                            </td>
                            <td style="width:80px;text-align:left;">
                                {{$rp['date_period']}}
                            </td>
                            <td style="width:80px;text-align:left;">{{$rp['next_ordered_at']}}</td>
                        </tr>
                        @php $rows_per_page++; @endphp
                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif

                    @endforeach
                    @else
                        <tr>
                            <td colspan="6">None</td>
                        </tr>
                        @php $rows_per_page++; @endphp
                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endif


                    <tr class="line-title">
                        <td colspan="6">Maintenance</td>
                    </tr>
                    @php $rows_per_page++; @endphp
                    @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    <thead>
                    <tr>
                        <th style="width:200px;text-align:left;">Description</th>
                        <th style="width:100px;text-align:left;">Service</th>
                        <th style="width:80px;text-align:left;">Last Maintenance Date</th>
                        <th style="width:80px;text-align:left;">Next Maintenance Date</th>
                        <th style="width:30px;text-align:left;">Days Overdue</th>
                        <th style="width:30px;text-align:left;">Status</th>
                    </tr>
                    </thead>
                    @php $rows_per_page++; @endphp
                    @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif


                    @foreach ($details['lines'] as $ln)
                        @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                            @if( isset($ln['cell-class']) && $ln['cell-class'] == 'row-title' )
                                <tr>
                                    <td colspan="6" class="sub-line-header">{!! $ln['description'] !!}</td>
                                </tr>
                            @else
                                <tr>
                                    <td colspan="6">{!! $ln['description'] !!}</td>
                                </tr>
                            @endif
                        @else
                            <tr>
                                <td style="width:200px;text-align:left;">{{$ln['description']}}</td>
                                <td style="width:100px;text-align:left;">{{$ln['service_desc']}}</td>
                                <td style="width:80px;text-align:left;">{{$ln['last_maintenance_date']}}</td>
                                <td style="width:80px;text-align:left;">{{$ln['next_maintenance_date']}}</td>
                                <td style="width:30px;text-align:left;">{{$ln['overdue_days']}}</td>
                                <td style="width:30px;text-align:left;">{!! $ln['status'] !!}</td>
                            </tr>
                        @endif
                        @php $rows_per_page++; @endphp
                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endforeach


                    @if(isset($details['notes']) && count($details['notes']) > 0)
                        <tr class="line-title">
                            <td colspan="6" class="text-left">Building Notes:</td>
                        </tr>
                        @php $rows_per_page++; @endphp
                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @foreach ($details['notes'] as $nt)
                        <tr>
                            <td colspan="6" class="text-left">{{$nt}}</td>
                        </tr>
                        @php $rows_per_page++; @endphp
                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endforeach
                    @endif

                </table>
                @if( $ctr < $max_rows)
                    <div class="page-break"></div>
                @endif

                @php
                    $ctr++;
                @endphp

            @endforeach
        @endif
    </div>
</main>
</body>
</html>
