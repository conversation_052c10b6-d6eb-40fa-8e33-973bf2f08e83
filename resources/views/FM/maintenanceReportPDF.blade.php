<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <link rel="stylesheet" href="{{ public_path('css/FM/report.css') }}">
    <style>
        @page {
            margin: 150px 20px 50px 20px;
        }

        header {
            position: fixed;
            top: -150px;
            left: 0px;
            right: 0px;
            background-color: transparent;
            height: 150px;
        }

        footer {
            position: fixed;
            bottom: -50px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 50px;
        }

        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
<header>
    <div class="header">
        @if(isset($header))
            @if(isset($header['title']))
                <div class="title">{!! $header['title'] !!}</div>
            @endif
            @if(isset($header['subtitle']))
                <div class="subtitle heighter">{!! $header['subtitle'] !!}</div>
            @endif
            @if(isset($header['mintitle']))
                <div class="mintitle">{!! $header['mintitle'] !!}</div>
            @endif
        @endif
        @if(isset($logo))
            <div class="logo"><img src="{{$logo}}"/></div>
        @endif
    </div>
</header>
<footer>
    <div class="footer">
        <table class="copyright">
            <tr>
                <td class="text-left">&nbsp;</td>
                <td class="text-center">Powered by Cirrus8 Software</td>
                <td class="text-right">{!! $footer['reference'] !!}</td>
            </tr>
        </table>
    </div>
</footer>
<main>
    <div class="body">
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 80;
                $y = $pdf->get_height() - 24;
                //$text = "Page {PAGE_NUM} of {PAGE_COUNT}";
                $text = "Page {PAGE_NUM}";
                $pdf->page_text($x, $y, $text, null, 8, array(0.333,0.333,0.333));
            }
        </script>
        @if(isset($rows))
            @php
                $max_rows = count($rows);
                $ctr = 1;
                $max_rows_per_page = 10;
            @endphp
            @foreach ($rows as $property_code => $details)
                @php
                    $rows_per_page = 0;
                @endphp

                <table class="table-list" cellspacing="0">
                    @if( $rows_per_page % $max_rows_per_page == 0 )
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}<br/>
                    @if( isset($details['overall']) )
                        Overall Passed: {{$details['overall']}}<br/>
                        Overall Passed Compliance: {{$details['man_overall']}}
                    @endif
                </div>
                <table class="table-list">
                    @endif

                    @if( isset($details['lines']) && count($details['lines']) > 0 )
                        <tr>
                            <td class="sub-line-header" colspan="8">Maintenance Services</td>
                        </tr>
                        <tr class="thead-line">
                            <td style="text-align:left;" colspan="2">Description</td>
                            <td style="text-align:left;">Service</td>
                            <td style="text-align:left;">Last Maintenance Date</td>
                            <td style="text-align:left;">Next Maintenance Date</td>
                            <td style="text-align:left;">Days Overdue</td>
                            <td style="text-align:center;">Status</td>
                        </tr>
                        @php $rows_per_page++; @endphp
                        @foreach ($details['lines'] as $ln)
                            @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                                <tr>
                                    <td class="sub-line-header" colspan="8">{!! $ln['description'] !!}</td>
                                </tr>
                            @else
                                <tr>
                                    <td style="text-align:left;" colspan="2">{{$ln['description']}}</td>
                                    <td style="text-align:left;">{{$ln['service_desc']}}</td>
                                    <td style="text-align:left;">{{$ln['last_maintenance_date']}}</td>
                                    <td style="text-align:left;">{{$ln['next_maintenance_date']}}</td>
                                    <td style="text-align:left;">{{$ln['overdue_days']}}</td>
                                    <td style="text-align:center;">{!! $ln['status'] !!}</td>
                                </tr>
                            @endif
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endforeach
                    @endif

                    @if( isset($details['doc_lines']) && count($details['doc_lines']) > 0 )
                        <tr>
                            <td class="sub-line-header" colspan="8">Maintenance Documents</td>
                        </tr>
                        <tr class="thead-line">
                            <td style="text-align:left;" colspan="3">Name</td>
                            <td style="text-align:left;">Title</td>
                            <td style="text-align:left;">Description</td>
                            <td style="text-align:left;">Required</td>
                            <td style="text-align:left;">Expiration Date</td>
                            <td style="text-align:left;">Status</td>
                        </tr>

                        @php $rows_per_page++; @endphp
                        @foreach ($details['doc_lines'] as $ln)
                            @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                                <tr>
                                    <td class="sub-line-header" colspan="8">{!! $ln['description'] !!}</td>
                                </tr>
                            @else
                                <tr>
                                    <td style="text-align:left;" colspan="3">{{$ln['name']}}</td>
                                    <td style="text-align:left;">{{$ln['title']}}</td>
                                    <td style="text-align:left;">{{$ln['description']}}</td>
                                    <td style="text-align:left;">{{$ln['is_required']}}</td>
                                    <td style="text-align:left;">{{$ln['expire_date']}}</td>
                                    <td style="text-align:left;">{!! $ln['status'] !!}</td>
                                </tr>
                            @endif
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endforeach
                    @endif

                    @if( isset($details['con_lines']) && count($details['con_lines']) > 0 )
                        <tr>
                            <td class="sub-line-header" colspan="8">Service Contracts</td>
                        </tr>
                        <tr class="thead-line">
                            <td style="text-align:left;">Service Contract Type</td>
                            <td style="text-align:left;">Template</td>
                            <td style="text-align:left;">Description</td>
                            <td style="text-align:left;">Contract #</td>
                            <td style="text-align:left;">Contractor</td>
                            <td style="text-align:left;">Service</td>
                            <td style="text-align:left;">Date Period</td>
                            <td style="text-align:center;">Status</td>
                        </tr>

                        @php $rows_per_page++; @endphp
                        @foreach ($details['con_lines'] as $ln)
                            @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                                <tr>
                                    <td class="sub-line-header" colspan="8">{!! $ln['description'] !!}</td>
                                </tr>
                            @else
                                <tr>
                                    <td style="text-align:left;">{{$ln['service_contract_type_desc']}}</td>
                                    <td style="text-align:left;">{{$ln['maintenance_template_desc']}}</td>
                                    <td style="text-align:left;">{{$ln['description']}}</td>
                                    <td style="text-align:left;">{{$ln['contract_no']}}</td>
                                    <td style="text-align:left;">{{$ln['supplier_code']}}</td>
                                    <td style="text-align:left;">{{$ln['service']}}</td>
                                    <td style="text-align:left;">{{$ln['from_date']}} - {{$ln['to_date']}}</td>
                                    <td style="text-align:center;">{!! $ln['status'] !!}</td>
                                </tr>
                                @if( isset($ln['lines']) && count($ln['lines']) > 0 )

                                    <tr class="row-header">
                                        <td style="text-align:left;">Workorder #</td>
                                        <td style="text-align:left;" colspan="4">Description</td>
                                        <td style="text-align:left;">Status</td>
                                        <td style="text-align:left;">Order Date</td>
                                        <td style="text-align:right;">Amount</td>
                                    </tr>

                                    @foreach ($ln['lines'] as $conln)
                                        <tr>
                                            <td style="text-align:left;">{{$conln['ref_no']}}</td>
                                            <td style="text-align:left;" colspan="4">{{$conln['description']}}</td>
                                            <td style="text-align:left;">{{$conln['status']}}</td>
                                            <td style="text-align:left;">{{$conln['ordered_at']}}</td>
                                            <td style="text-align:right;">{{$conln['total_amount']}}</td>
                                        </tr>
                                        @php $rows_per_page++; @endphp
                                        @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endforeach
                    @elseif(isset($ln['no_lines']) && $ln['no_lines'])
                        <tr class="line-subheader">
                            <td colspan="8" class="text-center">No transactions found</td>
                        </tr>
                    @endif
                    @endif
                    @php $rows_per_page++; @endphp
                    @if( $rows_per_page % $max_rows_per_page == 0)
                </table>
                <div class="per-page-header-title">{{$details['property_desc']}}</div>
                <div class="per-page-header-details">
                    {{$details['owner_desc']}}<br/>
                    {{$details['address']}}
                </div>
                <table class="table-list">
                    @php
                        $rows_per_page = 0;
                    @endphp
                    @endif
                    @endforeach
                    @endif

                </table>
                @if( $ctr < $max_rows)
                    <div class="page-break"></div>
                @endif

                @php
                    $ctr++;
                @endphp

            @endforeach
        @endif
    </div>
</main>
</body>
</html>
