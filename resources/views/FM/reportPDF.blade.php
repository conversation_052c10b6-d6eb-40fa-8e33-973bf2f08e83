<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <link rel="stylesheet" href="{{ public_path('css/FM/report.css') }}">
    <style>
        @page {
            margin: 150px 20px 50px 20px;
        }

        header {
            position: fixed;
            top: -150px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 150px;
        }

        footer {
            position: fixed;
            bottom: -50px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 50px;
        }
    </style>
</head>
<body>
<header>
    <div class="header">
        @if(isset($header))
            @if(isset($header['title']))
                <div class="title">{!! $header['title'] !!}</div>
            @endif
            @if(isset($header['subtitle']))
                <div class="subtitle">{!! $header['subtitle'] !!}</div>
            @endif
            @if(isset($header['mintitle']))
                <div class="mintitle">{!! $header['mintitle'] !!}</div>
            @endif
        @endif

        <table class="title-table">
            <tr>
                @if(isset($with_header_view) && $with_header_view)
                    <td valign="top" class="title-table-fields">
                        @foreach ($rows as $details)
                            @php
                                $cols_num = 3;
                                $row_ctr = 0;
                            @endphp
                            @if(isset($header_columns))
                                <table style="margin-bottom:10px;">
                                    <tbody>
                                    @foreach ($header_columns as $col)
                                        @if(!isset($col['full']) || !$col['full'])
                                            @if($row_ctr % $cols_num == 0 )
                                                <tr>
                                                    @endif
                                                    @php
                                                        $row_ctr++;
                                                    @endphp
                                                    <td>
                                                        @if(isset($details['header'][$col['field']]) )
                                                            <span class="row-title">{!! $col['label'] !!}:</span> <br/>
                                                            <span class="row-subtitle">{!! $details['header'][$col['field']] !!}</span>
                                                        @endif
                                                    </td>
                                                    @if($row_ctr % $cols_num == 0 )
                                                </tr>
                                            @endif
                                        @endif
                                    @endforeach

                                    @foreach ($header_columns as $col)
                                        @if(isset($col['full']) && $col['full'])
                                            <tr>
                                                <td colspan="{{$cols_num}}" style="white-space:pre-line;">
                                                    @if(!isset($col['nolabel']) || !$col['nolabel'])
                                                        <span class="row-title {{isset($col['title_class']) ? $col['title_class'] : ''}}">{!! $col['label'] !!}</span>
                                                        <br/>
                                                    @endif

                                                    @if(isset($details['header'][$col['field']]) )
                                                        <span class="row-subtitle {{isset($col['subtitle_class']) ? $col['subtitle_class'] : ''}}">{!! $details['header'][$col['field']] !!}</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach
                                    </tbody>
                                </table>
                            @endif
                        @endforeach
                    </td>
                @endif
                @if(isset($header_sub_table) && count($header_sub_table) > 0)
                    <td valign="top">
                        <table class="header-table">
                            <tbody>
                            @foreach ($header_sub_table as $values)
                                <tr>
                                    @foreach ($values as $val)
                                        <td>{{$val}}</td>
                                    @endforeach
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </td>
                @endif
            </tr>
        </table>


        @if(isset($logo))
            <div class="logo"><img src="{{$logo}}"/></div>
        @endif
    </div>
</header>
<footer>
    <div class="footer">
        <table class="copyright">
            <tr>
                <td class="text-left">&nbsp;</td>
                <td class="text-right">Powered by Cirrus8 Software</td>
                <td class="text-right">{!! $footer['reference'] !!}</td>
            </tr>
        </table>
    </div>
</footer>
<main>
    <div class="body">
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 80;
                $y = $pdf->get_height() - 24;
                //$text = "Page {PAGE_NUM} of {PAGE_COUNT}";
                $text = "Page {PAGE_NUM}";
                $pdf->page_text($x, $y, $text, null, 8, array(0.333,0.333,0.333));
            }
        </script>
        @if(isset($with_header_view) && $with_header_view)
            @foreach ($rows as $details)
                @php
                    $cols_num = 3;
                    $row_ctr = 0;
                @endphp
                @if(isset($header_columns))
                    @foreach ($header_columns as $col)
                        @if(!isset($col['full']) || !$col['full'])
                            @php
                                $row_ctr++;
                            @endphp
                        @endif
                    @endforeach

                    @foreach ($header_columns as $col)
                        @if(isset($col['full']) && $col['full'])
                            @php
                                $row_ctr++;
                            @endphp
                        @endif
                    @endforeach
                @endif
                @php
                    $space = $row_ctr * 8;
                @endphp
                        <!-- style="margin-top:{{$space}}px;" -->
                <table class="table-list">
                    @if(isset($columns))
                        <thead>
                        <tr>
                            @foreach ($columns as $col)
                                <th class="{{isset($col['align']) ? 'text-'.$col['align'] : 'text-left' }}">{!! $col['label'] !!}</th>
                            @endforeach
                        </tr>
                        </thead>
                    @endif
                    <tbody>
                    @foreach ($details['lines'] as $row)
                        <tr>
                            @foreach ($columns as $col)
                                @if(isset($row[$col['field']]))
                                    @if( isset($row['line-full-span']) && $row['line-full-span'] )
                                        <td class="{{ (isset($col['align']) ? 'text-'.$col['align'] : 'text-left') .' '. (isset($row['cell-class']) ? $row['cell-class'] : '') }}"
                                            colspan="{{count($columns)}}">
                                            @if(is_array($row[$col['field']]))
                                                @foreach ($row[$col['field']] as $fldtxt)
                                                    <div style="margin-bottom:5px">{{$fldtxt}}</div>
                                                @endforeach
                                            @else
                                                {{ $row[$col['field']] }}
                                            @endif
                                        </td>
                                    @else
                                        <td class="{{ (isset($col['align']) ? 'text-'.$col['align'] : 'text-left') .' '. (isset($row['cell-class']) ? $row['cell-class'] : '') }}">
                                            @if(is_array($row[$col['field']]))
                                                @foreach ($row[$col['field']] as $fldtxt)
                                                    <div style="margin-bottom:5px">{{$fldtxt}}</div>
                                                @endforeach
                                            @else
                                                {{ $row[$col['field']] }}
                                            @endif
                                        </td>
                                    @endif
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            @endforeach
        @else
            <table class="table-list">
                @if(isset($columns))
                    <thead>
                    <tr>
                        @foreach ($columns as $col)
                            <th class="{{isset($col['align']) ? 'text-'.$col['align'] : 'text-left' }}">{!! $col['label'] !!}</th>
                        @endforeach
                    </tr>
                    </thead>
                @endif
                @if(isset($columns) && isset($grouped))
                    <tbody>
                    @foreach ($grouped as $grp)
                        <tr class="row-header">
                            <td colspan="{{count($columns)}}">{!! $grp['title'] !!}</td>
                        </tr>
                        @foreach ($grp['rows'] as $row)
                            <tr>
                                @foreach ($columns as $col)
                                    @if(isset($row[$col['field']]))
                                        <td class="{{ (isset($col['align']) ? 'text-'.$col['align'] : 'text-left')  .' '. (isset($row['cell-class']) ? $row['cell-class'] : '') }}">
                                            @if(is_array($row[$col['field']]))
                                                @foreach ($row[$col['field']] as $fldtxt)
                                                    <div style="margin-bottom:5px">{{$fldtxt}}</div>
                                                @endforeach
                                            @else
                                                {{ $row[$col['field']] }}
                                            @endif
                                        </td>
                                    @endif
                                @endforeach
                            </tr>
                        @endforeach
                    @endforeach
                    </tbody>
                @elseif(isset($columns) && isset($rows))
                    <tbody>
                    @foreach ($rows as $row)
                        <tr>
                            @foreach ($columns as $col)
                                @if(isset($row[$col['field']]))
                                    <td class="{{ (isset($col['align']) ? 'text-'.$col['align'] : 'text-left') .' '. (isset($row['cell-class']) ? $row['cell-class'] : '') }}">
                                        @if(is_array($row[$col['field']]))
                                            @foreach ($row[$col['field']] as $fldtxt)
                                                <div style="margin-bottom:5px">{{$fldtxt}}</div>
                                            @endforeach
                                        @else
                                            {{ $row[$col['field']] }}
                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    @endforeach
                    </tbody>
                @endif
            </table>
        @endif
    </div>
</main>
</body>
</html>
