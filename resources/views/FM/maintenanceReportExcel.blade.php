<html>
<table class="table">
    @foreach ($rows as $property_code => $details)
        <tr class="line-header">
            <td colspan="8">{{$details['property_desc']}}</td>
        </tr>
        <tr class="line-header">
            <td colspan="8">{{$details['owner_desc']}}</td>
        </tr>
        <tr class="line-header">
            <td colspan="8">{{$details['address']}}</td>
        </tr>
        @if( isset($details['overall']) )
            <tr class="line-header">
                <td colspan="8">Overall Passed: {{$details['overall']}}</td>
            </tr>
        @endif
        @if( isset($details['man_overall']) )
            <tr class="line-header">
                <td colspan="8">Overall Passed Compliance: {{$details['man_overall']}}</td>
            </tr>
        @endif
        <tr class="spacer">
            <td colspan="8"></td>
        </tr>
        @if( isset($details['lines']) && count($details['lines']) > 0 )
            <tr class="line-header">
                <td colspan="8">Services</td>
            </tr>
            <tr>
                <th colspan="2">Description</th>
                <th>Service</th>
                <th>Last Maintenance Date</th>
                <th>Next Maintenance Date</th>
                <th>Days Overdue</th>
                <th>Status</th>
            </tr>
            @foreach ($details['lines'] as $ln)
                @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                    <tr class="line-subheader">
                        <td colspan="8">{!! $ln['description'] !!}</td>
                    </tr>
                @else
                    <tr>
                        <td colspan="2">{{$ln['description']}}</td>
                        <td>{{$ln['service_desc']}}</td>
                        <td>{{$ln['last_maintenance_date']}}</td>
                        <td>{{$ln['next_maintenance_date']}}</td>
                        <td>{{$ln['overdue_days']}}</td>
                        <td>{!! $ln['status'] !!}</td>
                    </tr>
                @endif
            @endforeach
            <tr class="spacer">
                <td colspan="8"></td>
            </tr>
            <tr class="spacer">
                <td colspan="8"></td>
            </tr>
        @endif
        @if( isset($details['doc_lines']) && count($details['doc_lines']) > 0 )
            <tr class="line-header">
                <td colspan="8">Documents</td>
            </tr>
            <tr>
                <th colspan="2">Name</th>
                <th>Title</th>
                <th>Description</th>
                <th>Required</th>
                <th>Expiration Date</th>
                <th>Status</th>
            </tr>
            @foreach ($details['doc_lines'] as $ln)
                @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                    <tr class="line-subheader">
                        <td colspan="8">{!! $ln['description'] !!}</td>
                    </tr>
                @else
                    <tr>
                        <td colspan="2">{{$ln['name']}}</td>
                        <td>{{$ln['title']}}</td>
                        <td>{{$ln['description']}}</td>
                        <td>{{$ln['is_required']}}</td>
                        <td>{{$ln['expire_date']}}</td>
                        <td>{!! $ln['status'] !!}</td>
                    </tr>
                @endif
            @endforeach
            <tr class="spacer">
                <td colspan="6"></td>
            </tr>
            <tr class="spacer">
                <td colspan="8"></td>
            </tr>
        @endif
        @if( isset($details['con_lines']) && count($details['con_lines']) > 0 )
            <tr class="line-header">
                <td colspan="8">Service Contracts</td>
            </tr>
            <tr>
                <th>Service Contract Type</th>
                <th>Template</th>
                <th>Description</th>
                <th>Contract #</th>
                <th>Contractor</th>
                <th>Service</th>
                <th>Date Period</th>
                <th>Status</th>
            </tr>
            @foreach ($details['con_lines'] as $ln)
                @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                    <tr class="line-subheader">
                        <td colspan="8">{!! $ln['description'] !!}</td>
                    </tr>
                @else
                    <tr>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['service_contract_type_desc']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['maintenance_template_desc']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['description']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['contract_no']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['supplier_code']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['service']}}</td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{{$ln['from_date']}}
                            - {{$ln['to_date']}} </td>
                        <td {!! (isset($ln['cell-class']) ? ("class='".$ln['cell-class']."'") : '') !!} >{!! $ln['status'] !!}</td>
                    </tr>

                    @if( isset($ln['lines']) && count($ln['lines']) > 0 )
                        <tr class="subcolumn-header">
                            <th>Workorder #</th>
                            <th colspan="4">Description</th>
                            <th>Status</th>
                            <th>Order Date</th>
                            <th>Amount</th>
                        </tr>
                        @foreach ($ln['lines'] as $conln)
                            <tr {!! (isset($conln['cell-class']) ? ("class='".$conln['cell-class']."'") : '') !!} >
                                <td>{{$conln['ref_no']}}</td>
                                <td colspan="4">{{$conln['description']}}</td>
                                <td>{{$conln['status']}}</td>
                                <td>{{$conln['ordered_at']}}</td>
                                <td>{{$conln['total_amount']}}</td>
                            </tr>
                        @endforeach
                        <tr class="spacer">
                            <td colspan="8"></td>
                        </tr>
                        <tr class="spacer">
                            <td colspan="8"></td>
                        </tr>
                        <tr class="spacer">
                            <td colspan="8"></td>
                        </tr>
                    @elseif(isset($ln['no_lines']) && $ln['no_lines'])
                        <tr class="line-subheader">
                            <td colspan="8" class="text-center">No transactions found</td>
                        </tr>
                        <tr class="spacer">
                            <td colspan="8"></td>
                        </tr>
                        <tr class="spacer">
                            <td colspan="8"></td>
                        </tr>
                    @endif

                @endif
            @endforeach
        @endif
        <tr class="spacer">
            <td colspan="8"></td>
        </tr>
        <tr class="spacer">
            <td colspan="8"></td>
        </tr>
        <tr class="spacer">
            <td colspan="8"></td>
        </tr>
        <tr class="spacer">
            <td colspan="8"></td>
        </tr>
    @endforeach
</table>
</html>