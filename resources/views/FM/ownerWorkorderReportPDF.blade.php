<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <link rel="stylesheet" href="{{ public_path('css/FM/report.css') }}">
    <style>
        @page {
            margin: 150px 5px 50px 5px;
        }

        header {
            position: fixed;
            top: -150px;
            left: 0px;
            right: 0px;
            background-color: transparent;
            height: 150px;
        }

        footer {
            position: fixed;
            bottom: -50px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 50px;
        }

        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body class="cirrus8">
<header>
    <div class="header">
        @if(isset($header))
            @if(isset($header['title']))
                <div class="title">{!! $header['title'] !!}</div>
            @endif
            @if(isset($header['subtitle']))
                <div class="subtitle heighter">{!! $header['subtitle'] !!}</div>
            @endif
            @if(isset($header['mintitle']))
                <div class="mintitle">{!! $header['mintitle'] !!}</div>
            @endif
        @endif
        @if(isset($logo))
            <div class="logo"><img src="{{$logo}}"/></div>
        @endif
    </div>
</header>
<footer>
    <div class="footer">
        <table class="copyright">
            <tr>
                <td class="text-left">&nbsp;</td>
                <td class="text-center">Powered by Cirrus8 Software</td>
                <td class="text-right">{!! $footer['reference'] !!}</td>
            </tr>
        </table>
    </div>
</footer>
<main>
    <div class="body">
        <script type="text/php">
            if (isset($pdf)) {
                $x = $pdf->get_width() - 80;
                $y = $pdf->get_height() - 24;
                //$text = "Page {PAGE_NUM} of {PAGE_COUNT}";
                $text = "Page {PAGE_NUM}";
                $pdf->page_text($x, $y, $text, null, 8, array(0.333,0.333,0.333));
            }
        </script>
        @if(isset($rows))
            @php
                $max_rows = count($rows);
                $ctr = 1;
                $max_rows_per_page = 31;
            @endphp
            @foreach ($rows as $property_code => $property)
                @php
                    $rows_per_page = 0;
                @endphp
                @if( isset($header['owner_workorder_type']) && $header['owner_workorder_type'] == 'complete' && isset($property['complete']) && empty($property['complete']) )
                    <table class="table-list">
                        <tr>
                            <td colspan="9">
                                <div class="per-page-header-title">
                                    <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="9">
                                <div class="per-page-center-title">
                                    <div class="title">Completed Workorders</div>
                                    <div class="subtitle">{{$header['from_date']}} to {{$header['to_date']}}</div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="9" style="text-align:center;font-size:16px;">No completed workorders found.
                            </td>
                        </tr>
                    </table>
                @endif
                @if(isset($property['complete']) && !empty($property['complete']) )
                    <table class="table-list">
                        @if( $rows_per_page % $max_rows_per_page == 0 )
                            <tr>
                                <td colspan="9">
                                    <div class="per-page-header-title">
                                        <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="9">
                                    <div class="per-page-center-title">
                                        <div class="title">Completed Workorders</div>
                                        <div class="subtitle">{{$header['from_date']}} to {{$header['to_date']}}</div>
                                    </div>
                                </td>
                            </tr>
                        @endif
                        @foreach ($property['complete'] as $ref_no => $workorder)
                            <tr class="row-title">
                                <td>Workorder #</td>
                                <td>Contractor Code</td>
                                <td>Contractor Name</td>
                                <td>Completion Date</td>
                                <td>Tenant Code</td>
                                <td colspan="4">Tenant Name</td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0 )
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-center-title">
                                            <div class="title">Completed Workorders</div>
                                            <div class="subtitle">{{$header['from_date']}}
                                                to {{$header['to_date']}}</div>
                                        </div>
                                    </td>
                                </tr>
                                @php
                                    $rows_per_page = 0;
                                @endphp
                            @endif
                            <tr>
                                <td>{{$workorder['ref_no']}}</td>
                                <td>{{$workorder['supplier_code']}}</td>
                                <td>{{$workorder['supplier_name']}}</td>
                                <td>{{$workorder['complete_date']}}</td>
                                <td>{{$workorder['tenant_code']}}</td>
                                <td colspan="4">{{$workorder['tenant_name']}}</td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0)
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-center-title">
                                            <div class="title">Completed Workorders</div>
                                            <div class="subtitle">{{$header['from_date']}}
                                                to {{$header['to_date']}}</div>
                                        </div>
                                    </td>
                                </tr>
                                @php
                                    $rows_per_page = 0;
                                @endphp
                            @endif
                            <tr class="row-header">
                                <td>Line #</td>
                                <td>Service Category</td>
                                <td>Service</td>
                                <td colspan="3">Description</td>
                                <td>Required By Date</td>
                                <td>Account Code</td>
                                <td style="text-align:right;">Amount {{$currency_symbol}}</td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0 )
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-center-title">
                                            <div class="title">Completed Workorders</div>
                                            <div class="subtitle">{{$header['from_date']}}
                                                to {{$header['to_date']}}</div>
                                        </div>
                                    </td>
                                </tr>
                                @php
                                    $rows_per_page = 0;
                                @endphp
                            @endif
                            @foreach ($workorder['lines'] as $index => $line)
                                <tr>
                                    <td>{{$index + 1}}</td>
                                    <td>{{$line['service_category']}}</td>
                                    <td>{{$line['service_name']}}</td>
                                    <td colspan="3">{{ substr($line['description'],0,70).(strlen($line['description']) >= 70 ? "..." : "") }}</td>
                                    <td>{{$line['require_date']}}</td>
                                    <td>{{$line['account_desc']}}</td>
                                    <td style="text-align:right;">{{number_format($line['amount'],2)}}</td>
                                </tr>
                                @php $rows_per_page++; @endphp
                                @if( $rows_per_page % $max_rows_per_page == 0 )
                                    <tr>
                                        <td colspan="9">
                                            <div class="per-page-header-title">
                                                <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="9">
                                            <div class="per-page-center-title">
                                                <div class="title">Completed Workorders</div>
                                                <div class="subtitle">{{$header['from_date']}}
                                                    to {{$header['to_date']}}</div>
                                            </div>
                                        </td>
                                    </tr>
                                    @php
                                        $rows_per_page = 0;
                                    @endphp
                                @endif
                            @endforeach
                        @endforeach
                    </table>
                    @if(isset($property['open']) && !empty($property['open']) )
                        <div class="page-break"></div>
                        @php
                            $rows_per_page = 0;
                        @endphp
                    @endif
                @endif

                @if(isset($header['owner_workorder_type']) && $header['owner_workorder_type'] == 'open' && isset($property['open']) && empty($property['open']) )
                    <table class="table-list">
                        <tr>
                            <td colspan="9">
                                <div class="per-page-header-title">
                                    <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="9">
                                <div class="per-page-center-title">
                                    <div class="title">Open Workorders</div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="9" style="text-align:center;font-size:16px;">No open workorders found.</td>
                        </tr>
                    </table>
                @endif
                @if(isset($property['open']) && !empty($property['open']) )
                    <table class="table-list">
                        @if( $rows_per_page % $max_rows_per_page == 0 )
                            <tr>
                                <td colspan="9">
                                    <div class="per-page-header-title">
                                        <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="9">
                                    <div class="per-page-center-title">
                                        <div class="title">Open Workorders</div>
                                    </div>
                                </td>
                            </tr>
                        @endif
                        @foreach ($property['open'] as $ref_no => $workorder)
                            <tr class="row-title">
                                <td>Workorder #</td>
                                <td>Contractor Code</td>
                                <td>Contractor Name</td>
                                <td>Order Date</td>
                                <td>Tenant Code</td>
                                <td colspan="4">Tenant Name</td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0)
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-center-title">
                                            <div class="title">Open Workorders</div>
                                        </div>
                                    </td>
                                </tr>
                                @php
                                    $rows_per_page = 0;
                                @endphp
                            @endif
                            <tr>
                                <td>{{$workorder['ref_no']}}</td>
                                <td>{{$workorder['supplier_code']}}</td>
                                <td>{{$workorder['supplier_name']}}</td>
                                <td>{{$workorder['order_date']}}</td>
                                <td>{{$workorder['tenant_code']}}</td>
                                <td colspan="4">{{$workorder['tenant_name']}}</td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0)
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-center-title">
                                            <div class="title">Open Workorders</div>
                                        </div>
                                    </td>
                                </tr>
                                @php
                                    $rows_per_page = 0;
                                @endphp
                            @endif
                            <tr class="row-header">
                                <td>Line #</td>
                                <td>Service Category</td>
                                <td>Service</td>
                                <td colspan="3">Description</td>
                                <td>Required By Date</td>
                                <td>Account Code</td>
                                <td style="text-align:right;">Amount {{$currency_symbol}}</td>
                            </tr>
                            @php $rows_per_page++; @endphp
                            @if( $rows_per_page % $max_rows_per_page == 0 )
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-header-title">
                                            <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="9">
                                        <div class="per-page-center-title">
                                            <div class="title">Open Workorders</div>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                            @foreach ($workorder['lines'] as $index => $line)
                                <tr>
                                    <td>{{$index + 1}}</td>
                                    <td>{{$line['service_category']}}</td>
                                    <td>{{$line['service_name']}}</td>
                                    <td colspan="3">{{ substr($line['description'],0,70).(strlen($line['description']) >= 70 ? "..." : "") }}</td>
                                    <td>{{$line['require_date']}}</td>
                                    <td>{{$line['account_desc']}}</td>
                                    <td style="text-align:right;">{{number_format($line['amount'],2)}}</td>
                                </tr>
                                @php $rows_per_page++; @endphp
                                @if( $rows_per_page % $max_rows_per_page == 0 )
                                    <tr>
                                        <td colspan="9">
                                            <div class="per-page-header-title">
                                                <strong>{{$property['property_name'] ." (".$property['property_code'].")"}}</strong>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="9">
                                            <div class="per-page-center-title">
                                                <div class="title">Open Workorders</div>
                                            </div>
                                        </td>
                                    </tr>
                                    @php
                                        $rows_per_page = 0;
                                    @endphp
                                @endif
                            @endforeach
                        @endforeach
                    </table>
                @endif

                @if( $ctr < $max_rows)
                    <div class="page-break"></div>
                @endif

                @php
                    $ctr++;
                @endphp

            @endforeach
        @endif
    </div>
</main>
</body>
</html>
