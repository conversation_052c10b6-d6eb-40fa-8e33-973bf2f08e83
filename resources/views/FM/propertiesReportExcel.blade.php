<html>
<table class="table">
    @foreach ($rows as $property_code => $details)
        <tr class="line-header">
            <td colspan="6">{{$details['property_desc']}}</td>
        </tr>
        <tr class="line-header">
            <td colspan="6">{{$details['owner_desc']}}</td>
        </tr>
        <tr class="line-header">
            <td colspan="6">{{$details['address']}}</td>
        </tr>
        <tr class="line-title">
            <td colspan="6">Preferred Contractors</td>
        </tr>
        <tr>
            <th colspan="2" class="text-left">Contractor</th>
            <th colspan="1" class="text-left">Primary Contact Name</th>
            <th colspan="1" class="text-left">Primary Contact Email</th>
            <th colspan="1" class="text-left">Primary Contact Mobile</th>
            <th colspan="1" class="text-left">Primary Contact Phone</th>
        </tr>
        @if(isset($details['suppliers']) && count($details['suppliers']) > 0)
            @foreach ($details['suppliers'] as $sp)
                <tr>
                    <td colspan="2" class="text-left">{{$sp['code']}} - {{$sp['name']}}</td>
                    <td colspan="1" class="text-left">{{$sp['contact_name']}}</td>
                    <td colspan="1" class="text-left">{{$sp['contact_email']}}</td>
                    <td colspan="1" class="text-left">{{$sp['contact_mobile']}}</td>
                    <td colspan="1" class="text-left">{{$sp['contact_phone']}}</td>
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="6">None</td>
            </tr>
        @endif
        <tr class="line-title">
            <td colspan="6">Repeating Workorders</td>
        </tr>
        @if(isset($details['repeatings']) && count($details['repeatings']) > 0)
            <tr>
                <th class="text-left">#</th>
                <th class="text-left">Contractor</th>
                <th class="text-left">Tenant</th>
                <th class="text-left">Repeat Every</th>
                <th class="text-left">Date Period</th>
                <th class="text-left">Next Date</th>
            </tr>
            @foreach ($details['repeatings'] as $rp)
                <tr>
                    <td class="text-left">{{$rp['ref_no']}}</td>
                    <td class="text-left">{{$rp['supplier_desc']}}</td>
                    <td class="text-left">{{$rp['tenant_desc']}}</td>
                    <td class="text-left">
                        {{$rp['repeat_every_num']}} {{$rp['type'] . "(s)"}}
                    </td>
                    <td class="text-left">
                        {{$rp['date_period']}}
                    </td>
                    <td class="text-left">{{$rp['next_ordered_at']}}</td>
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="6">None</td>
            </tr>
        @endif
        <tr class="line-title">
            <td colspan="6">Maintenance</td>
        </tr>
        <tr>
            <th class="text-left">Description</th>
            <th class="text-left">Service</th>
            <th class="text-left">Last Maintenance Date</th>
            <th class="text-left">Next Maintenance Date</th>
            <th class="text-right">Days Overdue</th>
            <th class="text-center">Status</th>
        </tr>
        @foreach ($details['lines'] as $ln)
            @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                @if( isset($ln['cell-class']) && $ln['cell-class'] == 'row-title' )
                    <tr class="line-subheader">
                        <td colspan="6">{!! $ln['description'] !!}</td>
                    </tr>
                @else
                    <tr>
                        <td colspan="6">{!! $ln['description'] !!}</td>
                    </tr>
                @endif
            @else
                <tr>
                    <td class="text-left">{{$ln['description']}}</td>
                    <td class="text-left">{{$ln['service_desc']}}</td>
                    <td class="text-left">{{$ln['last_maintenance_date']}}</td>
                    <td class="text-left">{{$ln['next_maintenance_date']}}</td>
                    <td class="text-right">{{$ln['overdue_days']}}</td>
                    <td class="text-center">{!! $ln['status'] !!}</td>
                </tr>
            @endif
        @endforeach
        @if(isset($details['notes']) && count($details['notes']) > 0)
            <tr class="line-title">
                <td colspan="6" class="text-left">Building Notes:</td>
            </tr>
            @foreach ($details['notes'] as $nt)
                <tr>
                    <td colspan="6" class="text-left">{{$nt}}</td>
                </tr>
            @endforeach
        @endif
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
    @endforeach
</table>
</html>