<html>
<table class="table">
    @foreach ($rows as $property_code => $property)
        <tr>
            <th colspan="7">{{$property['property_name'] ." (".$property['property_code'].")"}}</th>
        </tr>
        @if( isset($header['owner_workorder_type']) && $header['owner_workorder_type'] == 'complete' && isset($property['complete']) && empty($property['complete']) )
            <tr>
                <td colspan="7">No completed workorders found.</td>
            </tr>
        @endif
        @if(isset($property['complete']) && !empty($property['complete']) )
            <tr>
                <th colspan="7">Completed workorders
                    for {{ $header['from_date'] ? $header['from_date'] : '' }} {{ $header['to_date'] ? ' - '.$header['to_date'] : '' }}</th>
            </tr>
            @foreach ($property['complete'] as $ref_no => $workorder)
                <tr>
                    <th>Workorder #</th>
                    <th>Contractor Code</th>
                    <th>Contractor Name</th>
                    <th>Completion Date</th>
                    <th>Tenant Code</th>
                    <th>Tenant Name</th>
                </tr>
                <tr>
                    <td>{{$workorder['ref_no']}}</td>
                    <td>{{$workorder['supplier_code']}}</td>
                    <td>{{$workorder['supplier_name']}}</td>
                    <td>{{$workorder['complete_date']}}</td>
                    <td>{{$workorder['tenant_code']}}</td>
                    <td>{{$workorder['tenant_name']}}</td>
                </tr>
                <tr>
                    <td>Line #</td>
                    <td>Service Category</td>
                    <td>Service</td>
                    <td>Description</td>
                    <td>Required By Date</td>
                    <td>Account Code</td>
                    <td style="text-align:right;">Amount {{$currency_symbol}}</td>
                </tr>
                @foreach ($workorder['lines'] as $index => $line)
                    <tr>
                        <td>{{$index + 1}}</td>
                        <td>{{$line['service_category']}}</td>
                        <td>{{$line['service_name']}}</td>
                        <td>{{ substr($line['description'],0,50).(strlen($line['description']) >= 50 ? "..." : "") }}</td>
                        <td>{{$line['require_date']}}</td>
                        <td>{{$line['account_desc']}}</td>
                        <td>{{$line['amount']}}</td>
                    </tr>
                @endforeach
            @endforeach
        @endif
        @if( isset($header['owner_workorder_type']) && $header['owner_workorder_type'] == 'open' && isset($property['open']) && empty($property['open']) )
            <tr>
                <td colspan="7">No open workoders found.</td>
            </tr>
        @endif
        @if(isset($property['open']) && !empty($property['open']) )
            <tr>
                <th colspan="7">Open workorders
                    for {{ $header['from_date'] ? $header['from_date'] : '' }} {{ $header['to_date'] ? ' - '.$header['to_date'] : '' }}</th>
            </tr>
            @foreach ($property['open'] as $ref_no => $workorder)
                <tr>
                    <th>Workorder #</th>
                    <th>Contractor Code</th>
                    <th>Contractor Name</th>
                    <th>Order Date</th>
                    <th>Tenant Code</th>
                    <th>Tenant Name</th>
                </tr>
                <tr>
                    <td>{{$workorder['ref_no']}}</td>
                    <td>{{$workorder['supplier_code']}}</td>
                    <td>{{$workorder['supplier_name']}}</td>
                    <td>{{$workorder['order_date']}}</td>
                    <td>{{$workorder['tenant_code']}}</td>
                    <td>{{$workorder['tenant_name']}}</td>
                </tr>
                <tr>
                    <td>Line #</td>
                    <td>Service Category</td>
                    <td>Service</td>
                    <td>Description</td>
                    <td>Required By Date</td>
                    <td>Account Code</td>
                    <td style="text-align:right;">Amount {{$currency_symbol}}</td>
                </tr>
                @foreach ($workorder['lines'] as $index => $line)
                    <tr>
                        <td>{{$index + 1}}</td>
                        <td>{{$line['service_category']}}</td>
                        <td>{{$line['service_name']}}</td>
                        <td>{{ substr($line['description'],0,50).(strlen($line['description']) >= 50 ? "..." : "") }}</td>
                        <td>{{$line['require_date']}}</td>
                        <td>{{$line['account_desc']}}</td>
                        <td>{{$line['amount']}}</td>
                    </tr>
                @endforeach
            @endforeach
        @endif
    @endforeach
</table>
</html>