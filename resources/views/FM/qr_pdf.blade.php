<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <link rel="stylesheet" href="{{ public_path('css/FM/qr.css') }}">
    <style>
        @page {
            margin: 150px 20px 50px 20px;
        }

        header {
            position: fixed;
            top: -150px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 150px;
        }

        footer {
            position: fixed;
            bottom: -50px;
            left: 0px;
            right: 0px;
            background-color: white;
            height: 50px;
        }
    </style>
</head>
<body>
<header>
    <div class="header">
        @if(isset($logo))
            <div class="logo"><img src="{{$logo}}"/></div>
        @endif
    </div>
</header>
<footer>
    <div class="footer">
        <table class="copyright">
            <tr>
                <td class="text-left">&nbsp;</td>
                <td class="text-center">Powered by Cirrus8 Software</td>
                @if(isset($footer['reference']))
                    <td class="text-right">{!! $footer['reference'] !!}</td>
                @endif
            </tr>
        </table>
    </div>
</footer>
<main>
    <div class="body">
        <div class="content">
            @if(isset($contents['title']) && is_array($contents['title']))
                @foreach($contents['title'] as $hdtxt)
                    <div class="title-txt">{!! nl2br($hdtxt) !!}</div>
                @endforeach
            @endif
            @if(isset($contents['header']) && is_array($contents['header']))
                @foreach($contents['header'] as $hdtxt)
                    <div class="head-txt">{!! nl2br($hdtxt) !!}</div>
                @endforeach
            @endif
            @if(isset($qrimage))
                <div class="qrimage">
                    <img src="{{$qrimage}}"/>
                </div>
            @endif
            @if(isset($contents['footer']) && is_array($contents['footer']))
                @foreach($contents['footer'] as $hdtxt)
                    <div class="foot-txt">{!! nl2br($hdtxt) !!}</div>
                @endforeach
            @endif
        </div>
    </div>
</main>
</body>
</html>
