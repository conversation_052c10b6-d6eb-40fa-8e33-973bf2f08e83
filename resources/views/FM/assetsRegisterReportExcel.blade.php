<html>
<table class="table">
    @foreach ($rows as $property_code => $details)
        <tr class="line-header">
            <td colspan="12">{{$details['property_desc']}}</td>
        </tr>
        <tr class="line-header">
            <td colspan="12">{{$details['owner_desc']}}</td>
        </tr>
        <tr class="line-header">
            <td colspan="12">{{$details['address']}}</td>
        </tr>
        <tr>
            <th class="text-left">Code</th>
            <th class="text-left">Description</th>
            <th class="text-left">Type</th>
            <th class="text-left">Acquire Date</th>
            <th class="text-left">Install Date</th>
            <th class="text-left">Life Span (days)</th>
            <th class="text-left">Replace Date</th>
            <th class="text-right">Replace Amount</th>
            <th class="text-left">Make</th>
            <th class="text-left">Model</th>
            <th class="text-left">Serial No.</th>
            <th class="text-left">Status</th>
        </tr>
        @foreach ($details['lines'] as $ln)
            @if( isset($ln['line-full-span']) && $ln['line-full-span'] )
                <tr class="line-subheader">
                    <td colspan="12">{!! $ln['description'] !!}</td>
                </tr>
            @else
                <tr>
                    <td class="text-left">{{$ln['code']}}</td>
                    <td class="text-left">{{$ln['description']}}</td>
                    <td class="text-left">{{$ln['type']}}</td>
                    <td class="text-left">{{$ln['acquired_date']}}</td>
                    <td class="text-left">{{$ln['install_date']}}</td>
                    <td class="text-left">{{$ln['life_span_days']}}</td>
                    <td class="text-left">{{$ln['replace_date']}}</td>
                    <td class="text-right">{{$ln['replace_value']}}</td>
                    <td class="text-left">{{$ln['make']}}</td>
                    <td class="text-left">{{$ln['model']}}</td>
                    <td class="text-left">{{$ln['serial_no']}}</td>
                    <td class="text-left">{{$ln['status']}}</td>
                </tr>
            @endif
        @endforeach
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
        <tr class="spacer">
            <td colspan="6"></td>
        </tr>
    @endforeach
</table>
</html>