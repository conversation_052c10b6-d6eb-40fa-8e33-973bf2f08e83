<html>
<table class="table">
    @foreach ($rows as $property_code => $property)
        <thead>
        <tr>
            <th>Account Name</th>
            <th>Payee</th>
            <th>Invoice #</th>
            <th>Workorder #</th>
            <th>Asset Code</th>
            <th>Asset Name</th>
            <th>Net {{ $currency_symbol }}</th>
            <th>{{ $tax_label }} {{ $currency_symbol }}</th>
            <th>Gross {{ $currency_symbol }}</th>
        </tr>
        </thead>
        <thead>
        <tr>
            <th colspan="9">{{$property_code}}</th>
        </tr>
        </thead>
        <tbody>
        @if( isset($property['operating_exp']['line_count']) && $property['operating_exp']['line_count'] > 0 )
            @foreach ($property['operating_exp']['rows'] as $group_code => $group)
                @if( isset($group['subgroups']) && count($group['subgroups']) > 0 )
                    <tr>
                        <td colspan="9">{{$group['title']}}</td>
                    </tr>
                    @foreach ($group['subgroups'] as $subgroup_code => $subgroup)
                        <tr>
                            <td colspan="9">{{$subgroup['title']}}</td>
                        </tr>
                        @foreach ($subgroup['rows'] as $index => $row)
                            <tr>
                                <td>{{$row['account_desc']}}</td>
                                <td>{{$row['payee']}}</td>
                                <td>{{$row['invoice_no']}}</td>
                                <td>{{$row['workorder_no']}}</td>
                                <td>{{$row['asset_code']}}</td>
                                <td>{{$row['asset_name']}}</td>
                                <td>{{number_format($row['net'],2)}}</td>
                                <td>{{number_format($row['gst'],2)}}</td>
                                <td>{{number_format($row['gross'],2)}}</td>
                            </tr>
                        @endforeach
                        <tr>
                            <td colspan="6">Total {{$subgroup['title']}}</td>
                            <td>{{number_format($subgroup['totals']['net'],2)}}</td>
                            <td>{{number_format($subgroup['totals']['gst'],2)}}</td>
                            <td>{{number_format($subgroup['totals']['gross'],2)}}</td>
                        </tr>
                    @endforeach
                    <tr>
                        <td colspan="6">Total {{$group['title']}}</td>
                        <td>{{number_format($group['totals']['net'],2)}}</td>
                        <td>{{number_format($group['totals']['gst'],2)}}</td>
                        <td>{{number_format($group['totals']['gross'],2)}}</td>
                    </tr>
                @endif
            @endforeach
            <tr>
                <td colspan="6">Total Operating Expenditure</td>
                <td>{{number_format($property['operating_exp']['totals']['net'],2)}}</td>
                <td>{{number_format($property['operating_exp']['totals']['gst'],2)}}</td>
                <td>{{number_format($property['operating_exp']['totals']['gross'],2)}}</td>
            </tr>
        @endif
        @if( isset($property['others_exp']['line_count']) && $property['others_exp']['line_count'] > 0 )
            @foreach ($property['others_exp']['rows'] as $group_code => $group)
                @if( isset($group['subgroups']) && count($group['subgroups']) > 0 )
                    <tr>
                        <td colspan="9">{{$group['title']}}</td>
                    </tr>
                    @foreach ($group['subgroups'] as $subgroup_code => $subgroup)
                        <tr>
                            <td colspan="9" class="subtitle"><strong>{{$subgroup['title']}}</strong></td>
                        </tr>
                        @foreach ($subgroup['rows'] as $index => $row)
                            <tr>
                                <td>{{$row['account_desc']}}</td>
                                <td>{{$row['payee']}}</td>
                                <td>{{$row['invoice_no']}}</td>
                                <td>{{$row['workorder_no']}}</td>
                                <td>{{$row['asset_code']}}</td>
                                <td>{{$row['asset_name']}}</td>
                                <td>{{number_format($row['net'],2)}}</td>
                                <td>{{number_format($row['gst'],2)}}</td>
                                <td>{{number_format($row['gross'],2)}}</td>
                            </tr>
                        @endforeach
                        <tr>
                            <td colspan="6">Total {{$subgroup['title']}}</td>
                            <td>{{number_format($subgroup['totals']['net'],2)}}</td>
                            <td>{{number_format($subgroup['totals']['gst'],2)}}</td>
                            <td>{{number_format($subgroup['totals']['gross'],2)}}</td>
                        </tr>
                    @endforeach
                @endif
            @endforeach
        @endif
        @if( (isset($property['operating_exp']['line_count']) && $property['operating_exp']['line_count'] > 0) || (isset($property['others_exp']['line_count']) && $property['operating_exp']['line_count'] > 0)  )
            <tr>
                <td colspan="6">Total Expenditure</td>
                <td>{{number_format($property['totals']['net'],2)}}</td>
                <td>{{number_format($property['totals']['gst'],2)}}</td>
                <td>{{number_format($property['totals']['gross'],2)}}</td>
            </tr>
        @endif
        </tbody>
    @endforeach
</table>
</html>