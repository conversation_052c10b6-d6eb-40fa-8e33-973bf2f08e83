<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<style>
    .page-break {
        page-break-after: always;
    }

    /*@font-face {*/
    /*    font-family: 'Helvetica';*/
    /*    font-weight: normal;*/
    /*    font-style: normal;*/
    /*    font-variant: normal;*/
    /*    src: url("font url");*/
    /*}*/
    body {
        font-family: Arial, sans-serif !important;
        font-size: 13px !important;
    }

    .header-logo {
        position: absolute;
        right: 0px;
    }
</style>
<body>
@if($company_header_logo!='')
    {{--        <img class="header-logo" src="{!! env('APP_CLIENT_LOGO').'/'.$company_header_logo !!}" alt="Logo" height="{{ env('LOGO_HEIGHT') }}px" width="{{ env('LOGO_WIDTH') }}px">--}}
@endif
@if($email_attachment_type=='0' || $email_attachment_type=='1')
    @if($page_1_template_body!="")
        {!! $page_1_template_body !!}
        <div class="page-break"></div>
    @endif
@endif

@if($email_attachment_type=='0' || $email_attachment_type=='2')

    <table style="width: 100%;font-weight: bolder;">
        <tr>
            <td>Property Code: {{ $property_code }}</td>
        </tr>
        <tr>
            <td>Property Name: {{ $property_name }}</td>
        </tr>
        <tr>
            <td>{{ $variable_outgoings }} Reconciliation for period {{ $from_date }} - {{ $to_date }} </td>
        </tr>
        <tr>
            <td>Statement of Operating Expenses</td>
        </tr>
    </table>
    <br/>
    <br/>
    @if($include_vo_flag)
        <table style="width: 100%;">
            <tr>
                <td colspan="3"><strong>EXPENDITURE</strong></td>
            </tr>
            @foreach($page_2_account_group_content as $row_data)
                <tr>
                    <td></td>
                    <td>{{ $row_data['account_group_desc'] }}</td>
                    <td style="text-align: right;">
                        @if($row_data['account_group_amount']<0)
                            ({{ number_format(abs($row_data['account_group_amount']), 2) }})
                        @else
                            {{ number_format($row_data['account_group_amount'], 2) }}
                        @endif
                    </td>
                </tr>
            @endforeach
            <tr>
                <td></td>
                <td></td>
                <td style="border-top: 1px solid black;text-align: right;">
                    @if($page_2_account_group_total<0)
                        ({{ number_format(abs($page_2_account_group_total),2) }})
                    @else
                        {{ number_format($page_2_account_group_total,2) }}
                    @endif
                </td>
            </tr>
            <tr>
                <td colspan="3"></td>
            </tr>
            <tr>
                <td colspan="3">Allocation to Lessee</td>
            </tr>
            <tr>
                <td colspan="3">Tenant: {{ $lease_name }}</td>
            </tr>
            <tr>
                <td colspan="3">Allocation of total operating expenses</td>
            </tr>
            <tr>
                <td colspan="3">for the period ended {{ $from_date }} - {{ $to_date }}</td>
            </tr>
            <tr>
                <td></td>
                <td>under terms of their lease</td>
                <td style="text-align: right;">

                    @if($page_2_total_operating_expense<0)
                        ({{ number_format(abs($page_2_under_term_amount), 2) }})
                    @else
                        {{ number_format($page_2_under_term_amount, 2) }}
                    @endif
                </td>
            </tr>
            <tr>
                <td></td>
                <td>LESS: Total operating expenses charged against tenant</td>
                <td style="text-align: right;">
                    @if($page_2_total_operating_expense<0)
                        ({{ number_format(abs($page_2_total_operating_expense), 2) }})
                    @else
                        {{ number_format($page_2_total_operating_expense, 2) }}
                    @endif
                </td>
            </tr>
            <tr>
                <td></td>
                <td><strong>UNDER/(OVER) RECOVERY FOR THE PERIOD</strong></td>
                <td style="border-top: 1px solid black;font-weight: bolder;text-align: right;">
                    @if($page_2_adjustment_total<0)
                        ({{ number_format(abs($page_2_adjustment_total), 2) }})
                    @else
                        {{ number_format($page_2_adjustment_total, 2) }}
                    @endif
                </td>
            </tr>
            <tr>
                <td colspan="3"></td>
            </tr>
        </table>
        <br/>
        <br/>
    @endif
    @if($include_dr_flag)
        @if(sizeof($page_2_dr_account_content)>0)
            <table style="width: 100%;">
                <tr>
                    <td colspan="3"><strong>DIRECTLY RECOVERABLE</strong></td>
                </tr>
                @foreach($page_2_dr_account_content as $row_data)
                    <tr>
                        <td></td>
                        <td>{{ $row_data['account_name'] }}</td>
                        <td style="text-align: right;">

                            @if($row_data['under_term_amount']<0)
                                ({{ number_format(abs($row_data['under_term_amount']),2) }})
                            @else
                                {{ number_format($row_data['under_term_amount'],2) }}
                            @endif
                        </td>
                    </tr>
                @endforeach
                <tr>
                    <td></td>
                    <td></td>
                    <td style="border-top: 1px solid black;text-align: right;">

                        @if($page_2_dr_total<0)
                            ({{ number_format(abs($page_2_dr_total), 2) }})
                        @else
                            {{ number_format($page_2_dr_total, 2) }}
                        @endif
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td>LESS: Directly recoverable expenses charged against tenant</td>
                    <td style="text-align: right;">
                        @if($page_2_dr_total_operating<0)
                            ({{ number_format(abs($page_2_dr_total_operating), 2) }})
                        @else
                            {{ number_format($page_2_dr_total_operating, 2) }}
                        @endif

                    </td>
                </tr>
                <tr>
                    <td colspan="3"></td>
                </tr>
                <tr>
                    <td></td>
                    <td><strong>UNDER/(OVER) RECOVERY FOR THE PERIOD</strong></td>
                    <td style="border-top: 1px solid black;font-weight: bolder;text-align: right;">
                        @if($page_2_dr_net_amount<0)
                            ({{ number_format(abs($page_2_dr_net_amount), 2) }})
                        @else
                            {{ number_format($page_2_dr_net_amount, 2) }}
                        @endif
                    </td>
                </tr>
            </table>
        @endif
    @endif
@endif
</body>


