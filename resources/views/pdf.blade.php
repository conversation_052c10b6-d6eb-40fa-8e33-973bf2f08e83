<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="charset=utf-8"/>
    <title></title>
    <style type="text/css">
        @page {
            margin: 0px;
        }

        html,
        body {
            height: 842px;
        }

        body {
            min-height: 842px;
        }

        body {
            font-family: 'Helvetica';
            font-size: 11px;
            color: #333333;
            line-height: 1.25;
            padding: 20px;
            height: 842px;
            background-color: #FFFFFF;
        }

        table {
            border-spacing: 0;
            border-collapse: collapse;
        }

        .padd-0,
        td.padd-0 {
            padding: 0 !important;
        }

        .wrapper {
            width: 100%;
            border-spacing: 0;
            border-collapse: collapse;
        }

        h4,
        h5 {
            margin: 0;
            font-weight: 400;
            font-size: 1em;
        }

        h6,
        .title-min {
            margin: 0;
            font-weight: 400;
            font-size: 0.8em;
        }

        .header td {
            vertical-align: top;
        }

        .sub-header td {
            width: 50%;
            padding-top: 10px;
            text-align: left;
            padding-bottom: 10px;
        }

        .light-header td {
            text-align: left;
            padding: 10px 15px;
            background-color: #EEF5F9;
            font-size: 1.25em !important;
            font-weight: bold;
            text-transform: uppercase;
        }

        .detail-header {
            width: 100%;
            border-spacing: 0;
            border-collapse: collapse;
        }

        .detail-header td {
            padding-top: 5px;
            width: 33.33%;
            border: 0;
        }

        .header td:last-child,
        .light-header td:last-child,
        .sub-header td:last-child {
            text-align: right;
        }

        .detail-header td {
            padding-right: 5px;
        }

        .detail-header td:last-child {
            padding-left: 5px;
            padding-right: 0;
        }

        .title {
            font-size: 1.25em !important;
            font-weight: bold;
        }

        .italize {
            font-weight: 400 !important;
            font-style: italic;
        }

        .title-md {
            font-size: 1.1em !important;
            font-weight: bold;
        }

        .title-lg {
            font-size: 2em !important;
            font-weight: bold;
        }

        .title-sm {
            font-weight: bold;
        }

        .col2-details {
            width: 250px;
        }

        .col2-details tr td {
            padding: 0;
            padding-bottom: 3px;
            width: 105px;
        }

        /*.col2-details tr td:first-child{
          font-weight: bold;
        }*/
        .col2-details tr td:last-child {
            text-align: left;
            width: 105px !important;
            padding-left: 25px;
        }

        .col1-details {
            border-spacing: 0;
            border-collapse: collapse;
        }

        .col1-details tr td {
            width: 100%;
            padding: 5px 5px !important;
        }

        .detail-header .col1-details {
            width: 100%;
        }

        .detail-header .col2-details {
            width: auto;
        }

        .detail-header .col1-details tr:last-child td {
            height: 70px;
            vertical-align: top;
        }

        .detail-header .col2-details tr td:first-child {
            width: 105px !important;
        }

        .detail-header .col2-details tr td:last-child {
            padding-left: 5px !important;
            word-wrap: break-word;
            overflow-wrap: break-word;
            width: 100%;
        }

        .detail-header .col2-details tr td.padd-0:last-child {
            padding-left: 0px !important;
            word-wrap: break-word;
            overflow-wrap: break-word;
            width: 100%;
        }

        .main-lines .heighter {
            /*height: 400px;*/
            vertical-align: top;
        }

        .lines,
        .footer,
        .lines-summary {
            width: 100%;
            border-spacing: 0;
            border-collapse: collapse;
        }

        .lines-summary {
            margin-top: 5px;
            margin-left: -1px;
        }

        .footer {
            font-size: 0.8em;
        }

        .footer td:first-child {
            width: 70%;
        }

        .footer td:last-child {
            width: 30%;
        }

        .footer table {
            width: 100%;
        }

        .lines-summary td {
            width: 50%;
            vertical-align: top;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .lines-summary tbody tr td:first-child {
            padding-right: 5px;
        }

        .lines-summary tbody tr td:last-child {
            padding-left: 5px;
        }

        .lines tr td {
            padding: 5px 8px;
            border: 0;
        }

        .lines tr td.num {
            width: 20px;
            text-align: center;
        }

        .lines tr td.amount {
            text-align: right;
            width: 100px;
        }

        .lines tr td.date {
            width: 100px;
        }

        .lines thead tr td {
            background-color: #EEF5F9;
            border-bottom: 1px solid #DDDDDD;
            font-weight: bold;
            font-size: 1em;
            padding: 8px 8px;
        }

        .lines tbody tr:nth-child(even) td {
            background-color: #F1F1F1;
        }

        .lines-container {
            /*height: 350px;*/
            border: 1px solid #DDDDDD;
            margin-top: 3px;
        }

        .lines-summary .col1-details {
            width: 100%;
        }

        .lines-summary td:last-child .col1-details {
            margin-right: -2px;
        }

        .lines-summary .totals {
            float: right;
            width: 300px;
            font-size: 1.3em;
            border-spacing: 0;
            border-collapse: collapse;
        }

        .lines-summary .totals tr td {
            text-align: right;
            width: auto;
            padding: 5px 8px 5px 12px !important;
            font-weight: bold;
        }

        .bordered tr td {
            border: 1px solid #DDDDDD;
        }

        .no-border tr td {
            border: 0px !important;
        }

        .no-padding tr td {
            padding: 0px !important;
        }

        .normal tr td {
            height: auto !important;
            padding: 0px !important;
            text-align: left !important;
        }

        .valign-top {
            vertical-align: top;
        }

        .space-top {
            padding-top: 10px;
        }

        .space-top-x2 {
            padding-top: 20px;
        }

        .space-right {
            padding-right: 5px;
        }

        .space-left {
            padding-left: 5px;
        }

        .go-left {
            float: left;
        }

        .go-right {
            float: right;
        }

        .to-left {
            margin-left: auto;
        }

        .text-left {
            text-align: left !important;
        }

        .text-right {
            text-align: right !important;
        }

        .text-center {
            text-align: center !important;
        }

        .full-width {
            width: 100%;
        }

        .indent-left {
            padding-left: 20px;
        }

        .copyright {
            height: 25px;
            width: 65px;
            padding: 5px 10px 4px 10px;
            margin-top: 5px;
        }

        .copyright-text {
            vertical-align: bottom;
        }

        .copyright img {
            float: left;
            height: 100%;
            width: 100%;
            margin-left: 50px;
            margin-top: -26px;
        }

        .wrapper.foot {
            /*display: none;*/
            position: fixed;
            bottom: 230px;
            left: 25px;
            width: 100%;
            padding-right: 45px;
        }

        .go-bold {
            font-weight: bold;
        }
    </style>
</head>
<body>
<table class="wrapper">
    @if(isset($header) && count($header) > 0)
        <tr class="header">
            <td>
                @if(isset($pageTitle) && count($pageTitle) > 0)
                    @if(isset($pageTitle['text']))
                        <h5 class="{{isset($pageTitle['class']) ? $pageTitle['class'] : '' }}">{!! $pageTitle['text'] !!}</h5>
                    @endif
                    @if(isset($pageTitle['space']))
                        @for ($i = 0; $i < $pageTitle['space']; $i++)
                            <br/>
                        @endfor
                    @endif
                @endif
                @foreach ($header as $lines)
                    @if(isset($lines['text']))
                        <h5 class="{{isset($lines['class']) ? $lines['class'] : '' }}">{!! $lines['text'] !!}</h5>
                    @endif
                    @if(isset($lines['table']))
                        <table class="{{isset($lines['class']) ? $lines['class'] : '' }}">
                            @foreach ($lines['table'] as $rows)
                                <tr>
                                    @foreach ($rows as $data)
                                        <td>{!! $data !!}</td>
                                    @endforeach
                                </tr>
                            @endforeach
                        </table>
                    @endif
                    @if(isset($lines['space']))
                        @for ($i = 0; $i < $lines['space']; $i++)
                            <br/>
                        @endfor
                    @endif
                @endforeach
            </td>
            <td>
                @if(isset($logo))
                    <img src="{{$logo}}" style="max-width:270px;max-height:150px;"/>
                @endif
            </td>
        </tr>
    @endif
    @if( (isset($subHeaderLeft) && count($subHeaderLeft) > 0) || (isset($subHeaderRight) && count($subHeaderRight) > 0) )
        <tr class="sub-header">
            <td>
                @if( (isset($subHeaderLeft) && count($subHeaderLeft) > 0))
                    @foreach ($subHeaderLeft as $lines)
                        @if(isset($lines['text']))
                            <h5 class="{{isset($lines['class']) ? $lines['class'] : '' }}">{!! $lines['text'] !!}</h5>
                        @endif
                        @if(isset($lines['table']))
                            <table class="{{isset($lines['class']) ? $lines['class'] : '' }}">
                                @foreach ($lines['table'] as $rows)
                                    <tr>
                                        @foreach ($rows as $data)
                                            <td>{!! $data !!}</td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            </table>
                        @endif
                        @if(isset($lines['space']))
                            @for ($i = 0; $i < $lines['space']; $i++)
                                <br/>
                            @endfor
                        @endif
                    @endforeach
                @endif
            </td>
            <td>
                @if( (isset($subHeaderRight) && count($subHeaderRight) > 0))
                    @foreach ($subHeaderRight as $lines)
                        @if(isset($lines['text']))
                            <h5 class="{{isset($lines['class']) ? $lines['class'] : '' }}">{!! $lines['text'] !!}</h5>
                        @endif
                        @if(isset($lines['table']))
                            <table class="{{isset($lines['class']) ? $lines['class'] : '' }}">
                                @foreach ($lines['table'] as $rows)
                                    <tr>
                                        @foreach ($rows as $data)
                                            <td>{!! $data !!}</td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            </table>
                        @endif
                        @if(isset($lines['space']))
                            @for ($i = 0; $i < $lines['space']; $i++)
                                <br/>
                            @endfor
                        @endif
                    @endforeach
                @endif
            </td>
        </tr>
    @endif
    @if(isset($boxHeader) && count($boxHeader) > 0)
        <tr>
            <td colspan="2">
                <table class="detail-header">
                    <tr>
                        @foreach ($boxHeader as $box)
                            <td>
                                <table class="col1-details bordered">
                                    <tr>
                                        <td>{!! $box['title'] !!}</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            @foreach ($box['lines'] as $lines)
                                                @if(isset($lines['text']))
                                                    <h5 class="{{isset($lines['class']) ? $lines['class'] : '' }}">{!! $lines['text'] !!}</h5>
                                                @endif
                                                @if(isset($lines['table']))
                                                    <table style="table-layout: fixed; width: 100%"
                                                           class="{{isset($lines['class']) ? $lines['class'] : '' }}">
                                                        @foreach ($lines['table'] as $rows)
                                                            <tr>
                                                                <td style="word-break:break-word">@foreach ($rows as $data)
                                                                        {!! $data !!}
                                                                    @endforeach</td>
                                                            </tr>
                                                        @endforeach
                                                    </table>
                                                @endif
                                                @if(isset($lines['space']))
                                                    @for ($i = 0; $i < $lines['space']; $i++)
                                                        <br/>
                                                    @endfor
                                                @endif
                                            @endforeach
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        @endforeach
                    </tr>
                </table>
            </td>
        </tr>
    @endif
    @if(isset($details) && count($details) > 0)
        <tr class="main-lines">
            <td colspan="2" class="heighter">
                <div class="lines-container">
                    <table class="lines">
                        <thead>
                        <tr>
                            @foreach ($details['columns'] as $col)
                                <td class="{{isset($col['class']) ? $col['class'] : '' }}"
                                    colspan="{{isset($col['colspan']) ? $col['colspan'] : '1' }}">{!! $col['text'] !!}</td>
                            @endforeach
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($details['rows'] as $row)
                            <tr>
                                @foreach ($row as $cell)
                                    <td class="{{isset($cell['class']) ? $cell['class'] : '' }}"
                                        colspan="{{isset($cell['colspan']) ? $cell['colspan'] : '1' }}">
                                        @if(isset($cell['text']))
                                            {!! $cell['text'] !!}
                                        @endif
                                    </td>
                                @endforeach
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                <table class="lines-summary">
                    <tr>
                        <td style="width:90%">
                            @if(isset($details['remarks']) && $details['remarks'] != '')
                                <table class="col1-details no-border">
                                    <tr>
                                        <td class="title-sm">Additional Details:</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            {!! nl2br($details['remarks']) !!}
                                        </td>
                                    </tr>
                                </table>
                            @endif
                            @if(isset($details['others']) && $details['others'] != '')
                                <table class="col1-details no-border">
                                    <tr>
                                        @if(isset($details['others_label']) && $details['others_label'] != '')
                                            <td class="title-sm">{!! $details['others_label'] !!}</td>
                                        @else
                                            <td class="title-sm">Other Details:</td>
                                        @endif
                                    </tr>
                                    <tr>
                                        <td>
                                            {!! nl2br($details['others']) !!}
                                        </td>
                                    </tr>
                                </table>
                            @endif
                        </td>
                        <td>
                            <table class="col2-details totals">
                                @foreach ($details['totals'] as $row)
                                    <tr>
                                        @foreach ($row as $cell)
                                            <td>{!! $cell !!}</td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    @endif
</table>
<table class="wrapper foot">
    <tr class="footer">
        <td class="valign-top space-top space-right">
            <table class="col1-details bordered">
                <tr>
                    <td class="title-md">Terms &amp; Conditions:</td>
                </tr>
                <tr>
                    <td style="height: 150px;text-align: justify" class="valign-top">
                        @if(isset($terms) && $terms != '')
                            {!! nl2br($terms) !!}
                        @endif
                    </td>
                </tr>
            </table>
        </td>
        <td class="valign-top space-top space-left">
            <table class="col1-details bordered to-left full-width">
                <tr>
                    <td class="title-md">Contact & Payment Information:</td>
                </tr>
                <tr>
                    <td style="height: 150px;" class="valign-top">

                        <!-- <table class="no-border no-padding full-width">
                          <tr> -->
                        @if(isset($footerInfoLeft) && count($footerInfoLeft) > 0)
                            <!-- <td class="valign-top"> -->
                            @foreach ($footerInfoLeft as $lines)
                                @if(isset($lines['text']))
                                    <h5 class="{{isset($lines['class']) ? $lines['class'] : '' }}">{!! $lines['text'] !!}</h5>
                                @endif
                                @if(isset($lines['table']))
                                    <table class="{{isset($lines['class']) ? $lines['class'] : '' }}">
                                        @foreach ($lines['table'] as $rows)
                                            <tr>
                                                @foreach ($rows as $data)
                                                    <td>{!! $data !!}</td>
                                                @endforeach
                                            </tr>
                                        @endforeach
                                    </table>
                                @endif
                                @if(isset($lines['space']))
                                    @for ($i = 0; $i < $lines['space']; $i++)
                                        <br/>
                                    @endfor
                                @endif
                            @endforeach
                            <!-- </td> -->
                        @endif
                        @if(isset($footerInfoRight) && count($footerInfoRight) > 0)
                            <!-- <td class="valign-top indent-left"> -->
                            @foreach ($footerInfoRight as $lines)
                                @if(isset($lines['text']))
                                    <h5 class="{{isset($lines['class']) ? $lines['class'] : '' }}">{!! $lines['text'] !!}</h5>
                                @endif
                                @if(isset($lines['table']))
                                    <table class="{{isset($lines['class']) ? $lines['class'] : '' }}">
                                        @foreach ($lines['table'] as $rows)
                                            <tr>
                                                @foreach ($rows as $data)
                                                    <td>{!! $data !!}</td>
                                                @endforeach
                                            </tr>
                                        @endforeach
                                    </table>
                                @endif
                                @if(isset($lines['space']))
                                    @for ($i = 0; $i < $lines['space']; $i++)
                                        <br/>
                                    @endfor
                                @endif
                            @endforeach
                            <!-- </td> -->
                        @endif
                        <!-- </tr>
              </table> -->

                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr class="footer second">
        <td class="valign-top space-top space-right">
            <table>
                <tr>
                    <td>
                        <span class="copyright-text">&nbsp;</span>
                    </td>
                    <td class="text-center" style="width:250px">
                        <span class="copyright-text">Powered by Cirrus8 Software</span>
                    </td>
                </tr>
            </table>
            <!-- <div class="copyright">
          <img src="{{asset('images/logo.png')}}">
        </div> -->
        </td>
        <td class="valign-top space-top space-left text-right">
            @if(isset($footerRef) && $footerRef != '')
                {!! $footerRef !!}
            @endif
            <!-- <br/>
        @if(isset($pageOf) && $pageOf != '')
                {!! $pageOf !!}
            @endif -->
        </td>
    </tr>
</table>
</body>
</html>
