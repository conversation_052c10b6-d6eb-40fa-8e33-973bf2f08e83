{"name": "cirrus8/c8-api", "description": "Main API for Cirrus8", "license": "proprietary", "type": "project", "require": {"php": "^8.3", "ext-zip": "*", "ext-redis": "*", "ext-openssl": "*", "bacon/bacon-qr-code": "~1.0", "barryvdh/laravel-dompdf": "^3.0.0", "bugsnag/bugsnag-laravel": "^2.29.0", "coconutcraig/laravel-postmark": "^3.0", "endroid/qrcode": "^1.7", "intervention/image": "^2.4", "jaspersoft/rest-client": "^2.0", "laravel/framework": "^11.0", "laravel/horizon": "^5.33", "laravel/nightwatch": "^1.7", "laravel/passport": "^12.0", "laravel/ui": "^4.0", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1.35", "madnest/madzipper": "^1.1", "microsoft/azure-storage-blob": "^1.5", "microsoft/azure-storage-file": "^1.2.5", "microsoft/azure-storage-queue": "^1.3.4", "microsoft/azure-storage-table": "^1.1.6", "nesbot/carbon": "^2.31", "php-open-source-saver/jwt-auth": "^2.3", "phpoffice/phpspreadsheet": "^1.18", "pragmarx/google2fa-laravel": "^1.4.0", "predis/predis": "^3.0", "setasign/fpdf": "^1.8", "setasign/fpdi": "^2.2", "spatie/geocoder": "^3.11.0", "spatie/laravel-csp": "^2.8", "spatie/laravel-data": "^4.15", "stevebauman/location": "^7.2", "wildbit/postmark-php": "^4.0"}, "require-dev": {"filp/whoops": "~2.0", "fzaninotto/faker": "~1.4", "jetbrains/phpstorm-attributes": "^1.2", "larastan/larastan": "^3.0", "lcobucci/jwt": "^4.0.0", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "php-cs-fixer/shim": "^3.75", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpunit/phpunit": "^12.1", "spatie/laravel-ignition": "^2.0", "spatie/laravel-ray": "^1.40", "spatie/phpunit-snapshot-assertions": "^5.1"}, "autoload": {"classmap": ["database/seeds", "database/factories", "phpdocx/Classes/Phpdocx"], "psr-4": {"App\\": "app/"}, "files": ["app/Helper/bcmath.php", "app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"], "baseline": ["Composer\\Config::disableProcessTimeout", "./vendor/bin/phpstan analyse --generate-baseline"], "stan-with-ignored": ["Composer\\Config::disableProcessTimeout", "./vendor/bin/phpstan analyse --configuration=./phpstan-with-ignored.neon"], "stan": ["Composer\\Config::disableProcessTimeout", "./vendor/bin/phpstan analyse --memory-limit=2G"], "test": ["Composer\\Config::disableProcessTimeout", "phpunit -d memory_limit=256M --color=always"], "lint": ["Composer\\Config::disableProcessTimeout", "vendor/bin/php-cs-fixer fix --verbose --allow-risky=yes"], "check-style": "vendor/bin/php-cs-fixer check --verbose --allow-risky=yes"}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"kylekatarnls/update-helper": true}}}