# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### PHP/Laravel Commands

- **Test**: `composer test` or `phpunit -d memory_limit=256M --color=always`
- **Lint/Style**: `composer lint` (fixes) or `composer check-style` (checks only)
- **Static Analysis**: `composer stan` or `composer stan-with-ignored` (includes ignored issues)
- **Generate PHPStan Baseline**: `composer baseline`

### Frontend Commands (PNPM workspace)

- **Development**: `pnpm dev` - starts development servers for Vue apps
- **Watch**: `pnpm watch` - watches for changes and rebuilds
- **Build**: `pnpm build` - production build
- **Style**: `pnpm style` - runs Prettier formatting

### Laravel Artisan Commands

- **Serve**: `php artisan serve` - local development server
- **Clear caches**: `php artisan config:clear && php artisan route:clear && php artisan view:clear`
- **Optimize**: `php artisan optimize`

## Architecture Overview

This is a **Laravel 11** property management API using **Domain-Driven Design (DDD)** principles with a multi-tenant architecture supporting different client applications.

### Backend Structure (PHP 8.3+)

- **`app/Model/`**: Legacy Eloquent models organized by domain (AP, AR, FM, GL, etc.)
- **`app/Http/Controllers/`**: API controllers with helper classes
- **`app/Services/`**: Business logic services (email, SMS, external APIs)
- **`app/Helper/`**: Utility classes and validation rules
- **`routes/`**: API routes organized by application (admin.php, fm.php, sso.php, tenant.php)

### Frontend Structure (Vue 2 & Vue 3)

- **`resources/js/vue2/`**: Main Vue 2 application with Vuetify
- **`resources/js/vue3/`**: Newer Vue 3 components
- Both use **modular architecture** with domain-specific components in `modules/`

### Key Domains

- **FM (Facilities Management)**: Work orders, maintenance, suppliers
- **AP (Accounts Payable)**: Invoice processing, OCR, payments
- **AR (Accounts Receivable)**: Receipts, tenant billing
- **GL (General Ledger)**: Chart of accounts, journal entries
- **Properties**: Property management, leases, contacts
- **NPMS**: User management and access control

## Technology Stack

### Backend

- **Laravel 11** with **Passport** authentication
- **Spatie packages**: laravel-data, laravel-query-builder, laravel-package-tools
- **Microsoft SQL Server** primary database
- **Redis** for caching and queues
- **Horizon** for queue management
- **PHPStan** for static analysis with **PHP-CS-Fixer** for code style

### Frontend

- **Vue 2** (main app) with **Vuetify** UI framework
- **Vue 3** (newer components)
- **Vuex** for state management
- **Webpack** via Laravel Mix
- **PNPM** workspace with multiple packages

### External Integrations

- **Postmark** for email delivery
- **Azure Storage** for file storage
- **Google Services** (Maps, 2FA)
- **JasperReports** for PDF generation

## Development Guidelines

### Code Standards (.cursorrules)

- **PHP 8.3+** features preferred
- **Domain-Driven Design** approach for new features
- **camelCase** for PHP variables/methods, **PascalCase** for classes
- **Early returns** instead of nested if/else
- **Pure functions** where possible (no side effects)
- **Type safety** - always add and check types
- Functions should be **10-20 lines max**

### Database

- **Multiple databases**: Main API, FM, NPMS modules
- **Legacy schema** with extensive stored procedures
- Use **Eloquent models** in `app/Model/` organized by domain

### Authentication & Authorization

- **OAuth 2.0** with Laravel Passport
- **Multi-tenant** with company-based access control
- **Role-based permissions** via middleware

## Common Patterns

### API Routes

Routes are organized by application type:

- `/api/admin/*` - Admin functionality
- `/api/fm/*` - Facilities Management
- `/api/sso/*` - Single Sign-On
- `/api/tenant/*` - Tenant portal

### Vue Component Structure

```
modules/[Domain]/
├── forms/          # Form components
├── templates/      # Page templates
└── sections/       # Reusable sections
```

### Helper Classes

Extensive helper classes in `app/Helper/` for:

- Date/number formatting
- Email processing
- PDF generation
- Payment processing

## Testing

- **PHPUnit** configuration in `phpunit.xml`
- Tests should be in `app/[Domain]/Tests/` following DDD structure
- Helper function tests in `app/Helper/Tests/`

## Key Configuration Files

- **Multi-environment** config in `config/` (c8admin.php, c8fm.php, etc.)
- **CORS** configuration for API access
- **Database** connections for multiple tenant databases
- **OAuth** keys in `storage/` directory
