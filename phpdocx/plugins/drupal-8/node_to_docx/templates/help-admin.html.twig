<p>
This module is used to export a node as DOCX using phpdocx. This module allows you to generate DOCX documents of any node:
<p>
<strong>DOCX creation (www.domain.com/node/[node]/convert-to-docx)</strong>
</p>
<p>
where nid is the node id of a particular node to render.
</p>
<p>
To use this module, <u>you need to download thee phpdocx library</u> from <a href="http://www.phpdocx.com" title="phpdocx download" target="_blank">www.phpdocx.com</a> and uncompress it to the libraries or module folders:
</br>
<blockquote>/<em>yoursite/libraries/phpdocx</em>
</br>
or
</br>
<em>/yoursite/modules/custom/node_to_docx/phpdocx</em>
</blockquote>
</p>
<p>
The conventions used for the templates to render the node are:
</p>
<ul>
	<li>node__node_to_docx</li>
	<li>node__article__node_to_docx</li>
	<li>node__1__node_to_docx</li>
</ul>
<p>
if you have any question please do not hesitate to contact us and send an email with your question or comment to <a href="mailto:<EMAIL>"><EMAIL></a>
</p>