=== PHPdocX 9.5 ===
https://www.phpdocx.com/

====Where I can find documentation?====
The phpdocx site has a comprehensive documentation and resources.
Check this list if you need guidance in your first steps or if you want help or just to learn more.

1. Read the Tutorial to learn about installation and how to create your first documents and templates. 

https://www.phpdocx.com/documentation/introduction/tutorial

2. See the Introduction to phpdocx for a larger review of the  library.

It includes the Tutorial above mentioned, an introduction to the PDF conversion plugin and information 
about Digital Signatures, HTML to word conversion, protection and encryption of Word documents with PHP 
and generation of texts that read from Right to Left.

https://www.phpdocx.com/documentation/introduction

3. Check the Practical phpdocx site to learn about functional application: creating documents, 
working with templates, adding content, headers and footers, pictures, charts, HTML and CSS,
merging and so on.
   
https://www.phpdocx.com/documentation/practical
   
4. The conversion plugin is a feature available for Advanced and Premium licenses. 
It allows to convert a docx to PDF while keeping the contents and looks. Here's all you need to know about it:

https://www.phpdocx.com/documentation/conversion-plugin

5. Dive in each and every method with this complete list that includes description, parameters, 
values and examples of php code and its resulting word documents.

https://www.phpdocx.com/documentation/api-documentation

6. Other Questions? Problems?

- Read our FAQs: https://www.phpdocx.com/faqs

- Visit the forum: https://www.phpdocx.com/en/forum/default/category/1

- Reach us through our form: https://www.phpdocx.com/contact

- Write <NAME_EMAIL>

PHPDocX is developed by 2mdc (http://www.2mdc.com).