/**
 * DOMPDF - PHP5 HTML to PDF renderer
 *
 * File: $RCSfile: html.css,v $
 * Created on: 2004-08-04
 *
 * Copyright (c) 2004 - <PERSON><PERSON> <PERSON> <<EMAIL>>
 *               1998 - Netscape Communications Corporation
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library in the file LICENSE.LGPL; if not, write to the
 * Free Software Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA
 * 02111-1307 USA
 *
 * Originally from mozilla (http://www.mozilla.org).  Original code was
 * licensed under the NPL, with the option of release under the GPL or LGPL.
 * This file is hence released under the LGPL.
 *
 * The Original Code is mozilla.org code.
 *
 * The Initial Developer of the Original Code is 
 * Netscape Communications Corporation.
 * Portions created by the Initial Developer are Copyright (C) 1998
 * the Initial Developer. All Rights Reserved.
 *
 * Contributor(s):
 *      Blake Ross <<EMAIL>>
 *
 * The latest version of DOMPDF might be available at:
 * http://www.dompdf.com/
 *
 * @link http://www.dompdf.com/
 * @copyright 2004 Benj Carson
 * <AUTHOR> Carson <<EMAIL>>
 * @package dompdf
 * @version 0.3
 */

/* $Id: html.css,v 1.4 2006-04-05 20:09:00 benjcarson Exp $ */

@page {
  margin: 1.2cm;
}

html { 
  display: -dompdf-page;
  counter-increment: page;
}

/* blocks */

div, map, dt, isindex {
  display: block;
}

body {
  page-break-before: avoid;
  display: block;
}

p, dl, multicol {
  display: block;
  margin: 1em 0;
}

dd {
  display: block;
  margin-left: 40px;
}

blockquote {
  display: block;
  margin: 1em 40px;
}

address {
  display: block;
  font-style: italic;
}

center {
  display: block;
  text-align: center;
}

blockquote[type=cite] {
  display: block;
  margin: 1em 0px;
  padding-left: 1em;
  border-left: solid;
  border-color: blue;
  border-width: thin;
}

h1, h2, h3, h4, h5, h6 {
  display: block;
  font-weight: bold;
}

h1 {
  font-size: 2em;
  margin: .67em 0;
}

h2 {
  font-size: 1.5em;
  margin: .83em 0;
}

h3 {
  font-size: 1.17em;
  margin: 1em 0;
}

h4 {
  margin: 1.33em 0;
}

h5 {
  font-size: 0.83em;
  margin: 1.67em 0;
}

h6 {
  font-size: 0.67em;
  margin: 2.33em 0;
}

listing {
  display: block;
  font-family: fixed;
  font-size: medium;
  white-space: pre;
  margin: 1em 0;
}

plaintext, xmp, pre {
  display: block;
  font-family: fixed;
  white-space: pre;
  margin: 1em 0;
}

/* tables */

table {
  display: table;
  border-spacing: 2px;
  border-collapse: separate;
  margin-top: 0;
  margin-bottom: 0;
  text-indent: 0;
  text-align: left; /* quirk */
}

table[border] {
  border-style: outset;
  border-color: gray;
}

/* This won't work (???) */
/*
table[border] td,
table[border] th {
  border: 1pt solid grey;
}*/

/* make sure backgrounds are inherited in tables  -- see bug 4510 */
td, th, tr {
  background: inherit;
}
   
/* caption inherits from table not table-outer */  
caption {
  display: table-caption;
  text-align: center;
}

tr {
  display: table-row;
  vertical-align: inherit;
}

col {
  display: table-column;
}

colgroup {
  display: table-column-group;
}

tbody {
  display: table-row-group;
  vertical-align: middle;
}

thead {
  display: table-header-group;
  vertical-align: middle;
}

tfoot {
  display: table-footer-group;
  vertical-align: middle;
}

/* To simulate tbody auto-insertion */
table > tr {
  vertical-align: middle;
}

td { 
  display: table-cell;
  vertical-align: inherit;
  text-align: inherit; 
  padding: 1px;
}

th {
  display: table-cell;
  vertical-align: inherit;
  font-weight: bold;
  padding: 1px;
  text-align: center;
}

/* inlines */
q {
  quotes: '"' '"' "'" "'"; /* FIXME only the first level is used */
}

q:before {
  content: open-quote;
}

q:after {
  content: close-quote;
}

a:link {
  color: #00c;
  text-decoration: underline;
}

b, strong {
  font-weight: bolder;
}

i, cite, em, var, dfn {
  font-style: italic;
}

tt, code, kbd, samp {
  font-family: fixed;
}

u, ins {
  text-decoration: underline;
}

s, strike, del {
  text-decoration: line-through;
}

blink {
  text-decoration: blink;
}

big {
  font-size: larger;
}

small {
  font-size: smaller;
}

sub {
  vertical-align: sub;
  font-size: smaller;
  line-height: normal;
}

sup {
  vertical-align: super;
  font-size: smaller;
  line-height: normal;
}

nobr {
  white-space: nowrap;
}

/* lists */

ul, menu, dir {
  display: block;
  list-style-type: disc;
  margin: 1em 0;
  padding-left: 40px;
}

ol {
  display: block;
  list-style-type: decimal;
  margin: 1em 0;
  padding-left: 40px;
}

li {
  display: list-item;
}

/* nested lists have no top/bottom margins */
ul ul,   ul ol,   ul dir,   ul menu,   ul dl,
ol ul,   ol ol,   ol dir,   ol menu,   ol dl,
dir ul,  dir ol,  dir dir,  dir menu,  dir dl,
menu ul, menu ol, menu dir, menu menu, menu dl,
dl ul,   dl ol,   dl dir,   dl menu,   dl dl {
  margin-top: 0;
  margin-bottom: 0;
}

/* 2 deep unordered lists use a circle */
ol ul,   ul ul,   menu ul,   dir ul,
ol menu, ul menu, menu menu, dir menu,
ol dir,  ul dir,  menu dir,  dir dir {
  list-style-type: circle;
}

/* 3 deep (or more) unordered lists use a square */
ol ol ul,     ol ul ul,     ol menu ul,     ol dir ul,
ol ol menu,   ol ul menu,   ol menu menu,   ol dir menu,
ol ol dir,    ol ul dir,    ol menu dir,    ol dir dir,
ul ol ul,     ul ul ul,     ul menu ul,     ul dir ul,
ul ol menu,   ul ul menu,   ul menu menu,   ul dir menu,
ul ol dir,    ul ul dir,    ul menu dir,    ul dir dir,
menu ol ul,   menu ul ul,   menu menu ul,   menu dir ul,
menu ol menu, menu ul menu, menu menu menu, menu dir menu,
menu ol dir,  menu ul dir,  menu menu dir,  menu dir dir,
dir ol ul,    dir ul ul,    dir menu ul,    dir dir ul,
dir ol menu,  dir ul menu,  dir menu menu,  dir dir menu,
dir ol dir,   dir ul dir,   dir menu dir,   dir dir dir {
  list-style-type: square;
}

/* forms */

/*
input[type=text] {
	border: 1pt solid black;
	background-color: white;
}

input[type=text]:before {
  content: attr(value);
}*/

fieldset {
  display: block;
  margin-left: 2px;
  margin-right: 2px;
  padding: 0.35em 0.625em 0.75em;
  border: 1pt groove #666;
}

fieldset > legend {
  padding-left: 2px;
  padding-right: 2px;
  min-width: 1em;
  max-width: none;
  height: auto;
  min-height: 0;
  max-height: none;
  white-space: nowrap;
  margin-top: -1.0em;
  background: white;
}

legend {
  display: inline-block;
}


/* leafs */

hr {
  display: block;
  height: 2px;
  border: 1px inset;
  margin: 0.5em auto 0.5em auto;
}

iframe {
  border: 2px inset;
}

noframes {
  display: none;
}

br { 
  display: -dompdf-br;
}

img, img_generated {
  display: -dompdf-image;
}

dompdf_generated {
  display: inline;
}

/* hidden elements */
area, base, basefont, head, meta, script, style, title,
noembed, noscript, param {
  display: none;
}