APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=homestead
DB_USERNAME=homestead
DB_PASSWORD=secret

BROADCAST_DRIVER=log
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=sync

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=

LOG_CHANNEL=stack
LOG_LEVEL=info
LOG_STACK=single

BUGSNAG_API_KEY=abc123
NIGHTWATCH_TOKEN=abc123

HORIZON_BASIC_AUTH_USERNAME=<EMAIL>
HORIZON_BASIC_AUTH_PASSWORD=abc123

POSTMARK_TEST_MODE=true
POSTMARK_TEST_CLIENT=
POSTMARK_TEST_SENDER=<EMAIL>
POSTMARK_TEST_RECIPIENT=<EMAIL>
