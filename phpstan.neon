rules:
    - <PERSON><PERSON>\Ray\PHPStan\RemainingRayCallRule
includes:
    - phpstan-baseline.neon
    - vendor/larastan/larastan/extension.neon
    - vendor/nesbot/carbon/extension.neon
    - vendor/phpstan/phpstan-mockery/extension.neon
    - vendor/phpstan/phpstan/conf/bleedingEdge.neon
parameters:
    level: 8
    reportUnmatchedIgnoredErrors: false
    treatPhpDocTypesAsCertain: false
    excludePaths:
        - database/migrations/support/*
    paths:
        - app
        - resources/views
        - config
        - routes
        - database/migrations
    tmpDir: .phpstan.cache
