{"private": true, "version": "1.3.0-alpha.16", "scripts": {"dev": "pnpm --filter \"@cirrus8/api-vue*\" run dev", "watch": "pnpm --filter \"@cirrus8/api-vue*\" run watch", "watch-poll": "pnpm --filter \"@cirrus8/api-vue*\" run watch-poll", "hot": "pnpm --filter \"@cirrus8/api-vue*\" run hot", "build": "pnpm --filter \"@cirrus8/api-vue*\" run build", "test": "pnpm --filter \"@cirrus8/api-vue*\" test", "style": "pnpm --filter \"@cirrus8/api-vue*\" style", "prepare": "husky", "release": "commit-and-tag-version", "commit": "commit"}, "engines": {"node": ">=22.14.0"}, "lint-staged": {"**/*": ["prettier --write --ignore-unknown"]}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"scope-case": [2, "always", ["lower-case", "kebab-case"]], "scope-min-length": [2, "always", 2], "scope-empty": [2, "never"]}}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@commitlint/prompt-cli": "^19.8.0", "@types/jquery": "^3.5.32", "@types/node": "^22.15.3", "commit-and-tag-version": "^12.5.1", "conventional-changelog-conventionalcommits": "^9.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.5.3", "typescript": "^5.8.2"}}